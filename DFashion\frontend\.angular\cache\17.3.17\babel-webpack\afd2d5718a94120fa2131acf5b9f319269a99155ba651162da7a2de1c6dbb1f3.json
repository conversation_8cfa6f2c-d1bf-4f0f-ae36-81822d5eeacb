{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, H as Host } from './index-a1a47f01.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nconst avatarIosCss = \":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:48px;height:48px}\";\nconst IonAvatarIosStyle0 = avatarIosCss;\nconst avatarMdCss = \":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:64px;height:64px}\";\nconst IonAvatarMdStyle0 = avatarMdCss;\nconst Avatar = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n  }\n  render() {\n    return h(Host, {\n      key: 'f6014b524497bb18ae919ba6f6928407310d6870',\n      class: getIonMode(this)\n    }, h(\"slot\", {\n      key: '192ff4a8e10c0b0a4a2ed795ff2675afa8b23449'\n    }));\n  }\n};\nAvatar.style = {\n  ios: IonAvatarIosStyle0,\n  md: IonAvatarMdStyle0\n};\nconst badgeIosCss = \":host{--background:var(--ion-color-primary, #3880ff);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:0.8125rem;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{border-radius:10px;font-size:max(13px, 0.8125rem)}\";\nconst IonBadgeIosStyle0 = badgeIosCss;\nconst badgeMdCss = \":host{--background:var(--ion-color-primary, #3880ff);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:0.8125rem;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{--padding-top:3px;--padding-end:4px;--padding-bottom:4px;--padding-start:4px;border-radius:4px}\";\nconst IonBadgeMdStyle0 = badgeMdCss;\nconst Badge = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.color = undefined;\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '22d41ceefb76f40dfbf739fd71483f1272a45858',\n      class: createColorClasses(this.color, {\n        [mode]: true\n      })\n    }, h(\"slot\", {\n      key: 'e7e65463bac5903971a8f9f6be55515f42b81a83'\n    }));\n  }\n};\nBadge.style = {\n  ios: IonBadgeIosStyle0,\n  md: IonBadgeMdStyle0\n};\nconst thumbnailCss = \":host{--size:48px;--border-radius:0;border-radius:var(--border-radius);display:block;width:var(--size);height:var(--size)}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}\";\nconst IonThumbnailStyle0 = thumbnailCss;\nconst Thumbnail = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n  }\n  render() {\n    return h(Host, {\n      key: 'd2667635930e4c0896805f452357e7dc9086bc72',\n      class: getIonMode(this)\n    }, h(\"slot\", {\n      key: '66eb1487f3da4da2ef71b812a8d0f0fe884c7d81'\n    }));\n  }\n};\nThumbnail.style = IonThumbnailStyle0;\nexport { Avatar as ion_avatar, Badge as ion_badge, Thumbnail as ion_thumbnail };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}