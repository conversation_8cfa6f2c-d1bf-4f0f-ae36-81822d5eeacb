{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { TrendingProductsComponent } from '../trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../new-arrivals/new-arrivals.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/product.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@ionic/angular\";\nconst _c0 = a0 => [\"/category\", a0];\nfunction SidebarComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelement(1, \"img\", 11);\n    i0.ɵɵelementStart(2, \"div\", 12)(3, \"h5\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_8_Template_button_click_7_listener() {\n      const user_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.followUser(user_r2.id));\n    });\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", user_r2.avatar, i0.ɵɵsanitizeUrl)(\"alt\", user_r2.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r2.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r2.followedBy);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", user_r2.isFollowing ? \"Following\" : \"Follow\", \" \");\n  }\n}\nfunction SidebarComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵelement(1, \"img\", 11);\n    i0.ɵɵelementStart(2, \"div\", 15)(3, \"h5\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 16)(8, \"span\", 17);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 18);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_13_Template_button_click_12_listener() {\n      const influencer_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.followInfluencer(influencer_r5.id));\n    });\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const influencer_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", influencer_r5.avatar, i0.ɵɵsanitizeUrl)(\"alt\", influencer_r5.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(influencer_r5.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.formatFollowerCount(influencer_r5.followersCount), \" followers\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", influencer_r5.postsCount, \" posts\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", influencer_r5.engagement, \"% engagement\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", influencer_r5.isFollowing ? \"Following\" : \"Follow\", \" \");\n  }\n}\nfunction SidebarComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵelement(1, \"img\", 11);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c0, category_r6.slug));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", category_r6.image, i0.ɵɵsanitizeUrl)(\"alt\", category_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r6.name);\n  }\n}\nexport class SidebarComponent {\n  constructor(productService, router) {\n    this.productService = productService;\n    this.router = router;\n    this.suggestedUsers = [];\n    this.trendingProducts = [];\n    this.topInfluencers = [];\n    this.categories = [];\n  }\n  ngOnInit() {\n    this.loadSuggestedUsers();\n    this.loadTrendingProducts();\n    this.loadTopInfluencers();\n    this.loadCategories();\n  }\n  loadSuggestedUsers() {\n    // Mock data for suggested users\n    this.suggestedUsers = [{\n      id: '1',\n      username: 'fashionista_maya',\n      fullName: 'Maya Chen',\n      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',\n      followedBy: 'Followed by 12 others',\n      isFollowing: false\n    }, {\n      id: '2',\n      username: 'style_guru_alex',\n      fullName: 'Alex Rodriguez',\n      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',\n      followedBy: 'Followed by 8 others',\n      isFollowing: false\n    }];\n  }\n  loadTrendingProducts() {\n    this.trendingProducts = [];\n  }\n  loadTopInfluencers() {\n    // Mock data for top influencers\n    this.topInfluencers = [{\n      id: '1',\n      username: 'fashion_queen',\n      fullName: 'Priya Sharma',\n      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',\n      followersCount: 25000,\n      postsCount: 156,\n      engagement: 8.5,\n      isFollowing: false\n    }, {\n      id: '2',\n      username: 'style_maven',\n      fullName: 'Kavya Reddy',\n      avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150',\n      followersCount: 18000,\n      postsCount: 89,\n      engagement: 12.3,\n      isFollowing: true\n    }];\n  }\n  loadCategories() {\n    // Mock data for categories\n    this.categories = [{\n      id: '1',\n      name: 'Women',\n      slug: 'women',\n      image: 'https://images.unsplash.com/photo-1483985988355-763728e1935b?w=400'\n    }, {\n      id: '2',\n      name: 'Men',\n      slug: 'men',\n      image: 'https://images.unsplash.com/photo-1516257984-b1b4d707412e?w=400'\n    }, {\n      id: '3',\n      name: 'Accessories',\n      slug: 'accessories',\n      image: 'https://images.unsplash.com/photo-1506629905607-d405b7a30db9?w=400'\n    }, {\n      id: '4',\n      name: 'Footwear',\n      slug: 'footwear',\n      image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400'\n    }];\n  }\n  formatFollowerCount(count) {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'k';\n    }\n    return count.toString();\n  }\n  followUser(userId) {\n    const user = this.suggestedUsers.find(u => u.id === userId);\n    if (user) {\n      user.isFollowing = !user.isFollowing;\n    }\n  }\n  followInfluencer(influencerId) {\n    const influencer = this.topInfluencers.find(i => i.id === influencerId);\n    if (influencer) {\n      influencer.isFollowing = !influencer.isFollowing;\n    }\n  }\n  quickBuy(productId) {\n    console.log('Quick buy product:', productId);\n    // TODO: Implement quick buy functionality\n  }\n  browseCategory(categorySlug) {\n    console.log('Browse category:', categorySlug);\n    this.router.navigate(['/category', categorySlug]);\n  }\n  static {\n    this.ɵfac = function SidebarComponent_Factory(t) {\n      return new (t || SidebarComponent)(i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SidebarComponent,\n      selectors: [[\"app-sidebar\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 19,\n      vars: 3,\n      consts: [[1, \"sidebar\"], [1, \"suggestions\"], [1, \"suggestion-list\"], [\"class\", \"suggestion-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"influencers\"], [1, \"influencer-list\"], [\"class\", \"influencer-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"categories\"], [1, \"category-list\"], [\"class\", \"category-item\", \"routerLinkActive\", \"active\", \"tabindex\", \"0\", 3, \"routerLink\", 4, \"ngFor\", \"ngForOf\"], [1, \"suggestion-item\"], [3, \"src\", \"alt\"], [1, \"suggestion-info\"], [1, \"follow-btn\", 3, \"click\"], [1, \"influencer-item\"], [1, \"influencer-info\"], [1, \"influencer-stats\"], [1, \"posts-count\"], [1, \"engagement\"], [\"routerLinkActive\", \"active\", \"tabindex\", \"0\", 1, \"category-item\", 3, \"routerLink\"]],\n      template: function SidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"aside\", 0);\n          i0.ɵɵelement(1, \"app-trending-products\")(2, \"app-featured-brands\")(3, \"app-new-arrivals\");\n          i0.ɵɵelementStart(4, \"div\", 1)(5, \"h3\");\n          i0.ɵɵtext(6, \"Suggested for you\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 2);\n          i0.ɵɵtemplate(8, SidebarComponent_div_8_Template, 9, 5, \"div\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 4)(10, \"h3\");\n          i0.ɵɵtext(11, \"Top Fashion Influencers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 5);\n          i0.ɵɵtemplate(13, SidebarComponent_div_13_Template, 14, 7, \"div\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 7)(15, \"h3\");\n          i0.ɵɵtext(16, \"Shop by Category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 8);\n          i0.ɵɵtemplate(18, SidebarComponent_div_18_Template, 4, 6, \"div\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.suggestedUsers);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.topInfluencers);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, RouterModule, i2.RouterLink, i2.RouterLinkActive, IonicModule, i4.RouterLinkDelegate, TrendingProductsComponent, FeaturedBrandsComponent, NewArrivalsComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.sidebar[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 80px;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n  max-height: calc(100vh - 100px);\\n  overflow-y: auto;\\n  padding-right: 8px;\\n}\\n\\napp-trending-products[_ngcontent-%COMP%], app-featured-brands[_ngcontent-%COMP%], app-new-arrivals[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: block;\\n  margin-bottom: 24px;\\n}\\n\\n.suggestions[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: #fff;\\n  border-radius: 12px;\\n  margin-bottom: 24px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  border: 1px solid #e0e0e0;\\n}\\n.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px;\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  transform: translateY(-1px);\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 12px;\\n  color: #666;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%] {\\n  padding: 6px 12px;\\n  background: #007bff;\\n  color: white;\\n  border: none;\\n  border-radius: 6px;\\n  font-size: 12px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: background 0.2s ease;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]:hover {\\n  background: #0056b3;\\n}\\n\\n.categories[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n  box-shadow: 0 8px 32px rgba(0, 210, 211, 0.2);\\n}\\n.categories[_ngcontent-%COMP%]   .categories-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.categories[_ngcontent-%COMP%]   .categories-slider-container[_ngcontent-%COMP%]   .categories-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 100%;\\n  pointer-events: none;\\n}\\n.categories[_ngcontent-%COMP%]   .categories-slider-container[_ngcontent-%COMP%]   .categories-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-prev[_ngcontent-%COMP%], .categories[_ngcontent-%COMP%]   .categories-slider-container[_ngcontent-%COMP%]   .categories-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-next[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 40px;\\n  height: 40px;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  border: none;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 18px;\\n  font-weight: bold;\\n  cursor: pointer;\\n  pointer-events: all;\\n  transition: all 0.3s ease;\\n  z-index: 10;\\n}\\n.categories[_ngcontent-%COMP%]   .categories-slider-container[_ngcontent-%COMP%]   .categories-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-prev[_ngcontent-%COMP%]:hover, .categories[_ngcontent-%COMP%]   .categories-slider-container[_ngcontent-%COMP%]   .categories-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-next[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.9);\\n  transform: scale(1.1);\\n}\\n.categories[_ngcontent-%COMP%]   .categories-slider-container[_ngcontent-%COMP%]   .categories-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-prev[_ngcontent-%COMP%] {\\n  left: -20px;\\n}\\n.categories[_ngcontent-%COMP%]   .categories-slider-container[_ngcontent-%COMP%]   .categories-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-next[_ngcontent-%COMP%] {\\n  right: -20px;\\n}\\n.categories[_ngcontent-%COMP%]   .categories-slider-container[_ngcontent-%COMP%]   .categories-slider[_ngcontent-%COMP%]   .owl-dots[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.trending[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  padding: 16px;\\n}\\n\\n.influencers[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencers-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencers-slider-container[_ngcontent-%COMP%]   .influencers-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 100%;\\n  pointer-events: none;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencers-slider-container[_ngcontent-%COMP%]   .influencers-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-prev[_ngcontent-%COMP%], .influencers[_ngcontent-%COMP%]   .influencers-slider-container[_ngcontent-%COMP%]   .influencers-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-next[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 40px;\\n  height: 40px;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  border: none;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 18px;\\n  font-weight: bold;\\n  cursor: pointer;\\n  pointer-events: all;\\n  transition: all 0.3s ease;\\n  z-index: 10;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencers-slider-container[_ngcontent-%COMP%]   .influencers-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-prev[_ngcontent-%COMP%]:hover, .influencers[_ngcontent-%COMP%]   .influencers-slider-container[_ngcontent-%COMP%]   .influencers-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-next[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.9);\\n  transform: scale(1.1);\\n}\\n.influencers[_ngcontent-%COMP%]   .influencers-slider-container[_ngcontent-%COMP%]   .influencers-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-prev[_ngcontent-%COMP%] {\\n  left: -20px;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencers-slider-container[_ngcontent-%COMP%]   .influencers-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-next[_ngcontent-%COMP%] {\\n  right: -20px;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencers-slider-container[_ngcontent-%COMP%]   .influencers-slider[_ngcontent-%COMP%]   .owl-dots[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDCA1\\\";\\n  font-size: 28px;\\n}\\n\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDECD\\uFE0F\\\";\\n  font-size: 28px;\\n}\\n\\n.trending[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin-bottom: 16px;\\n  color: #8e8e8e;\\n}\\n\\n.influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDC51\\\";\\n  font-size: 28px;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 16px;\\n  margin-bottom: 16px;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 16px;\\n  margin-bottom: 16px;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);\\n}\\n\\n.influencer-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid rgba(255, 255, 255, 0.3);\\n  transition: transform 0.3s ease;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.suggestion-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.suggestion-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 4px;\\n}\\n\\n.suggestion-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 8px 16px;\\n  border-radius: 8px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n  transform: translateY(-2px);\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  padding: 16px;\\n  margin-bottom: 16px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.suggestions[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  background: rgba(255, 255, 255, 0.2);\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  border-radius: 6px;\\n  margin-bottom: 8px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.suggestions[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.suggestions[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: rgba(255, 255, 255, 0.4);\\n  margin-bottom: 16px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 8px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.influencer-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 16px;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid rgba(255, 255, 255, 0.3);\\n  transition: transform 0.3s ease;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.influencer-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.influencer-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 4px;\\n}\\n\\n.influencer-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.7);\\n  margin-bottom: 8px;\\n}\\n\\n.influencer-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n  margin-bottom: 12px;\\n}\\n\\n.influencer-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: rgba(255, 255, 255, 0.6);\\n  background: rgba(255, 255, 255, 0.1);\\n  padding: 4px 8px;\\n  border-radius: 8px;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  -webkit-backdrop-filter: blur(5px);\\n          backdrop-filter: blur(5px);\\n}\\n\\n.follow-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 8px 16px;\\n  border-radius: 8px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  align-self: flex-start;\\n}\\n\\n.follow-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n  transform: translateY(-2px);\\n}\\n\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  padding: 16px;\\n  margin-bottom: 16px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  background: rgba(255, 255, 255, 0.2);\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  border-radius: 6px;\\n  margin-bottom: 8px;\\n}\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0%, 100% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n.influencers[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.influencers[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: rgba(255, 255, 255, 0.4);\\n  margin-bottom: 16px;\\n}\\n.influencers[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 8px;\\n}\\n.influencers[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.trending-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n  padding-bottom: 16px;\\n  border-bottom: 1px solid #f1f5f9;\\n}\\n\\n.trending-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n  margin-bottom: 0;\\n  padding-bottom: 0;\\n}\\n\\n.trending-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 8px;\\n  object-fit: cover;\\n}\\n\\n.trending-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.trending-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin-bottom: 4px;\\n}\\n\\n.trending-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: var(--primary-color);\\n  margin-bottom: 8px;\\n}\\n\\n.original-price[_ngcontent-%COMP%] {\\n  text-decoration: line-through;\\n  color: #8e8e8e;\\n  font-weight: 400;\\n  margin-left: 4px;\\n}\\n\\n.trending-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n\\n.trending-badge[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 600;\\n  background: #fef3c7;\\n  color: #92400e;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n\\n.views[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #8e8e8e;\\n}\\n\\n.quick-buy-btn[_ngcontent-%COMP%] {\\n  background: #f1f5f9;\\n  color: #64748b;\\n  border: 1px solid #e2e8f0;\\n  padding: 6px 12px;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n  align-self: flex-start;\\n}\\n\\n.quick-buy-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-color);\\n  color: #fff;\\n  border-color: var(--primary-color);\\n}\\n\\n.categories[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1f2937;\\n  margin-bottom: 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDECD\\uFE0F\\\";\\n  font-size: 18px;\\n}\\n\\n.category-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 16px;\\n}\\n\\n.category-item[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 20px 16px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-align: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  text-decoration: none;\\n  color: white;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.category-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);\\n}\\n\\n.category-item.active[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  border-color: rgba(255, 255, 255, 0.4);\\n  transform: translateY(-4px);\\n}\\n\\n.category-item.active[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n.category-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  margin-bottom: 12px;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  transition: transform 0.3s ease;\\n}\\n\\n.category-item[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.category-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: white;\\n  text-align: center;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.category-item[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.category-item[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n\\n@media (max-width: 1024px) {\\n  .sidebar[_ngcontent-%COMP%] {\\n    order: -1;\\n    position: static;\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n    gap: 20px;\\n    max-height: none;\\n    overflow-y: visible;\\n  }\\n  app-view-add-stories[_ngcontent-%COMP%], app-trending-products[_ngcontent-%COMP%], app-featured-brands[_ngcontent-%COMP%], app-new-arrivals[_ngcontent-%COMP%] {\\n    margin-bottom: 16px;\\n  }\\n  .suggestions[_ngcontent-%COMP%], .influencers[_ngcontent-%COMP%], .categories[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .suggestion-item[_ngcontent-%COMP%], .influencer-item[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .suggestion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n  }\\n  .category-grid[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n  .category-item[_ngcontent-%COMP%] {\\n    padding: 16px 12px;\\n  }\\n  .category-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .sidebar[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  app-view-add-stories[_ngcontent-%COMP%], app-trending-products[_ngcontent-%COMP%], app-featured-brands[_ngcontent-%COMP%], app-new-arrivals[_ngcontent-%COMP%] {\\n    margin-bottom: 12px;\\n  }\\n  .suggestions[_ngcontent-%COMP%], .influencers[_ngcontent-%COMP%], .categories[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .suggestion-item[_ngcontent-%COMP%], .influencer-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n    padding: 16px;\\n  }\\n  .suggestion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n    margin-bottom: 12px;\\n  }\\n  .influencer-stats[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    justify-content: center;\\n    gap: 8px;\\n  }\\n  .category-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 12px;\\n  }\\n  .category-item[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    text-align: left;\\n    padding: 12px;\\n    gap: 12px;\\n  }\\n  .category-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n    margin-bottom: 0;\\n  }\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "IonicModule", "TrendingProductsComponent", "FeaturedBrandsComponent", "NewArrivalsComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "SidebarComponent_div_8_Template_button_click_7_listener", "user_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "followUser", "id", "ɵɵadvance", "ɵɵproperty", "avatar", "ɵɵsanitizeUrl", "fullName", "ɵɵtextInterpolate", "username", "<PERSON><PERSON><PERSON>", "ɵɵtextInterpolate1", "isFollowing", "SidebarComponent_div_13_Template_button_click_12_listener", "influencer_r5", "_r4", "followInfluencer", "formatFollowerCount", "followersCount", "postsCount", "engagement", "ɵɵpureFunction1", "_c0", "category_r6", "slug", "image", "name", "SidebarComponent", "constructor", "productService", "router", "suggestedUsers", "trendingProducts", "topInfluencers", "categories", "ngOnInit", "loadSuggestedUsers", "loadTrendingProducts", "loadTopInfluencers", "loadCategories", "count", "toFixed", "toString", "userId", "user", "find", "u", "influencerId", "influencer", "i", "quickBuy", "productId", "console", "log", "browseCategory", "categorySlug", "navigate", "ɵɵdirectiveInject", "i1", "ProductService", "i2", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SidebarComponent_Template", "rf", "ctx", "ɵɵtemplate", "SidebarComponent_div_8_Template", "SidebarComponent_div_13_Template", "SidebarComponent_div_18_Template", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RouterLink", "RouterLinkActive", "i4", "RouterLinkDelegate", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\sidebar\\sidebar.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\sidebar\\sidebar.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\n\nimport { ProductService } from '../../../../core/services/product.service';\nimport { Product } from '../../../../core/models/product.model';\nimport { TrendingProductsComponent } from '../trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../new-arrivals/new-arrivals.component';\n\n@Component({\n  selector: 'app-sidebar',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    IonicModule,\n    TrendingProductsComponent,\n    FeaturedBrandsComponent,\n    NewArrivalsComponent\n  ],\n  templateUrl: './sidebar.component.html',\n  styleUrls: ['./sidebar.component.scss']\n})\nexport class SidebarComponent implements OnInit {\n  suggestedUsers: any[] = [];\n  trendingProducts: Product[] = [];\n  topInfluencers: any[] = [];\n  categories: any[] = [];\n\n  constructor(\n    private productService: ProductService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadSuggestedUsers();\n    this.loadTrendingProducts();\n    this.loadTopInfluencers();\n    this.loadCategories();\n  }\n\n  loadSuggestedUsers() {\n    // Mock data for suggested users\n    this.suggestedUsers = [\n      {\n        id: '1',\n        username: 'fashionista_maya',\n        fullName: 'Maya Chen',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',\n        followedBy: 'Followed by 12 others',\n        isFollowing: false\n      },\n      {\n        id: '2',\n        username: 'style_guru_alex',\n        fullName: 'Alex Rodriguez',\n        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',\n        followedBy: 'Followed by 8 others',\n        isFollowing: false\n      }\n    ];\n  }\n\n  loadTrendingProducts() {\n    this.trendingProducts = [];\n  }\n\n  loadTopInfluencers() {\n    // Mock data for top influencers\n    this.topInfluencers = [\n      {\n        id: '1',\n        username: 'fashion_queen',\n        fullName: 'Priya Sharma',\n        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',\n        followersCount: 25000,\n        postsCount: 156,\n        engagement: 8.5,\n        isFollowing: false\n      },\n      {\n        id: '2',\n        username: 'style_maven',\n        fullName: 'Kavya Reddy',\n        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150',\n        followersCount: 18000,\n        postsCount: 89,\n        engagement: 12.3,\n        isFollowing: true\n      }\n    ];\n  }\n\n  loadCategories() {\n    // Mock data for categories\n    this.categories = [\n      {\n        id: '1',\n        name: 'Women',\n        slug: 'women',\n        image: 'https://images.unsplash.com/photo-1483985988355-763728e1935b?w=400'\n      },\n      {\n        id: '2',\n        name: 'Men',\n        slug: 'men',\n        image: 'https://images.unsplash.com/photo-1516257984-b1b4d707412e?w=400'\n      },\n      {\n        id: '3',\n        name: 'Accessories',\n        slug: 'accessories',\n        image: 'https://images.unsplash.com/photo-1506629905607-d405b7a30db9?w=400'\n      },\n      {\n        id: '4',\n        name: 'Footwear',\n        slug: 'footwear',\n        image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400'\n      }\n    ];\n  }\n\n  formatFollowerCount(count: number): string {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'k';\n    }\n    return count.toString();\n  }\n\n  followUser(userId: string) {\n    const user = this.suggestedUsers.find(u => u.id === userId);\n    if (user) {\n      user.isFollowing = !user.isFollowing;\n    }\n  }\n\n  followInfluencer(influencerId: string) {\n    const influencer = this.topInfluencers.find(i => i.id === influencerId);\n    if (influencer) {\n      influencer.isFollowing = !influencer.isFollowing;\n    }\n  }\n\n  quickBuy(productId: string) {\n    console.log('Quick buy product:', productId);\n    // TODO: Implement quick buy functionality\n  }\n\n  browseCategory(categorySlug: string) {\n    console.log('Browse category:', categorySlug);\n    this.router.navigate(['/category', categorySlug]);\n  }\n}\n", "<aside class=\"sidebar\">\n  <!-- Trending Products Section -->\n  <app-trending-products></app-trending-products>\n\n  <!-- Featured Brands Section -->\n  <app-featured-brands></app-featured-brands>\n\n  <!-- New Arrivals Section -->\n  <app-new-arrivals></app-new-arrivals>\n\n  <!-- Suggested for you -->\n  <div class=\"suggestions\">\n    <h3>Suggested for you</h3>\n    <div class=\"suggestion-list\">\n      <div class=\"suggestion-item\" *ngFor=\"let user of suggestedUsers\">\n        <img [src]=\"user.avatar\" [alt]=\"user.fullName\">\n        <div class=\"suggestion-info\">\n          <h5>{{ user.username }}</h5>\n          <p>{{ user.followedBy }}</p>\n        </div>\n        <button class=\"follow-btn\" (click)=\"followUser(user.id)\">\n          {{ user.isFollowing ? 'Following' : 'Follow' }}\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Top Fashion Influencers -->\n  <div class=\"influencers\">\n    <h3>Top Fashion Influencers</h3>\n    <div class=\"influencer-list\">\n      <div class=\"influencer-item\" *ngFor=\"let influencer of topInfluencers\">\n        <img [src]=\"influencer.avatar\" [alt]=\"influencer.fullName\">\n        <div class=\"influencer-info\">\n          <h5>{{ influencer.username }}</h5>\n          <p>{{ formatFollowerCount(influencer.followersCount) }} followers</p>\n          <div class=\"influencer-stats\">\n            <span class=\"posts-count\">{{ influencer.postsCount }} posts</span>\n            <span class=\"engagement\">{{ influencer.engagement }}% engagement</span>\n          </div>\n          <button class=\"follow-btn\" (click)=\"followInfluencer(influencer.id)\">\n            {{ influencer.isFollowing ? 'Following' : 'Follow' }}\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Shop by Category -->\n  <div class=\"categories\">\n    <h3>Shop by Category</h3>\n    <div class=\"category-list\">\n      <div\n        class=\"category-item\"\n        *ngFor=\"let category of categories\"\n        [routerLink]=\"['/category', category.slug]\"\n        routerLinkActive=\"active\"\n        tabindex=\"0\"\n      >\n        <img [src]=\"category.image\" [alt]=\"category.name\">\n        <span>{{ category.name }}</span>\n      </div>\n    </div>\n  </div>\n</aside>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,WAAW,QAAQ,gBAAgB;AAI5C,SAASC,yBAAyB,QAAQ,kDAAkD;AAC5F,SAASC,uBAAuB,QAAQ,8CAA8C;AACtF,SAASC,oBAAoB,QAAQ,wCAAwC;;;;;;;;;;ICKvEC,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAE,SAAA,cAA+C;IAE7CF,EADF,CAAAC,cAAA,cAA6B,SACvB;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5BJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAqB;IAC1BH,EAD0B,CAAAI,YAAA,EAAI,EACxB;IACNJ,EAAA,CAAAC,cAAA,iBAAyD;IAA9BD,EAAA,CAAAK,UAAA,mBAAAC,wDAAA;MAAA,MAAAC,OAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,UAAA,CAAAP,OAAA,CAAAQ,EAAA,CAAmB;IAAA,EAAC;IACtDf,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;;;;IARCJ,EAAA,CAAAgB,SAAA,EAAmB;IAAChB,EAApB,CAAAiB,UAAA,QAAAV,OAAA,CAAAW,MAAA,EAAAlB,EAAA,CAAAmB,aAAA,CAAmB,QAAAZ,OAAA,CAAAa,QAAA,CAAsB;IAExCpB,EAAA,CAAAgB,SAAA,GAAmB;IAAnBhB,EAAA,CAAAqB,iBAAA,CAAAd,OAAA,CAAAe,QAAA,CAAmB;IACpBtB,EAAA,CAAAgB,SAAA,GAAqB;IAArBhB,EAAA,CAAAqB,iBAAA,CAAAd,OAAA,CAAAgB,UAAA,CAAqB;IAGxBvB,EAAA,CAAAgB,SAAA,GACF;IADEhB,EAAA,CAAAwB,kBAAA,MAAAjB,OAAA,CAAAkB,WAAA,+BACF;;;;;;IASFzB,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAE,SAAA,cAA2D;IAEzDF,EADF,CAAAC,cAAA,cAA6B,SACvB;IAAAD,EAAA,CAAAG,MAAA,GAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAA8D;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEnEJ,EADF,CAAAC,cAAA,cAA8B,eACF;IAAAD,EAAA,CAAAG,MAAA,GAAiC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAClEJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,IAAuC;IAClEH,EADkE,CAAAI,YAAA,EAAO,EACnE;IACNJ,EAAA,CAAAC,cAAA,kBAAqE;IAA1CD,EAAA,CAAAK,UAAA,mBAAAqB,0DAAA;MAAA,MAAAC,aAAA,GAAA3B,EAAA,CAAAQ,aAAA,CAAAoB,GAAA,EAAAlB,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAkB,gBAAA,CAAAF,aAAA,CAAAZ,EAAA,CAA+B;IAAA,EAAC;IAClEf,EAAA,CAAAG,MAAA,IACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;;;;;IAZCJ,EAAA,CAAAgB,SAAA,EAAyB;IAAChB,EAA1B,CAAAiB,UAAA,QAAAU,aAAA,CAAAT,MAAA,EAAAlB,EAAA,CAAAmB,aAAA,CAAyB,QAAAQ,aAAA,CAAAP,QAAA,CAA4B;IAEpDpB,EAAA,CAAAgB,SAAA,GAAyB;IAAzBhB,EAAA,CAAAqB,iBAAA,CAAAM,aAAA,CAAAL,QAAA,CAAyB;IAC1BtB,EAAA,CAAAgB,SAAA,GAA8D;IAA9DhB,EAAA,CAAAwB,kBAAA,KAAAb,MAAA,CAAAmB,mBAAA,CAAAH,aAAA,CAAAI,cAAA,gBAA8D;IAErC/B,EAAA,CAAAgB,SAAA,GAAiC;IAAjChB,EAAA,CAAAwB,kBAAA,KAAAG,aAAA,CAAAK,UAAA,WAAiC;IAClChC,EAAA,CAAAgB,SAAA,GAAuC;IAAvChB,EAAA,CAAAwB,kBAAA,KAAAG,aAAA,CAAAM,UAAA,iBAAuC;IAGhEjC,EAAA,CAAAgB,SAAA,GACF;IADEhB,EAAA,CAAAwB,kBAAA,MAAAG,aAAA,CAAAF,WAAA,+BACF;;;;;IAUJzB,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAE,SAAA,cAAkD;IAClDF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAC3BH,EAD2B,CAAAI,YAAA,EAAO,EAC5B;;;;IANJJ,EAAA,CAAAiB,UAAA,eAAAjB,EAAA,CAAAkC,eAAA,IAAAC,GAAA,EAAAC,WAAA,CAAAC,IAAA,EAA2C;IAItCrC,EAAA,CAAAgB,SAAA,EAAsB;IAAChB,EAAvB,CAAAiB,UAAA,QAAAmB,WAAA,CAAAE,KAAA,EAAAtC,EAAA,CAAAmB,aAAA,CAAsB,QAAAiB,WAAA,CAAAG,IAAA,CAAsB;IAC3CvC,EAAA,CAAAgB,SAAA,GAAmB;IAAnBhB,EAAA,CAAAqB,iBAAA,CAAAe,WAAA,CAAAG,IAAA,CAAmB;;;ADnCjC,OAAM,MAAOC,gBAAgB;EAM3BC,YACUC,cAA8B,EAC9BC,MAAc;IADd,KAAAD,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IAPhB,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,gBAAgB,GAAc,EAAE;IAChC,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,UAAU,GAAU,EAAE;EAKnB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAH,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACL,cAAc,GAAG,CACpB;MACE7B,EAAE,EAAE,GAAG;MACPO,QAAQ,EAAE,kBAAkB;MAC5BF,QAAQ,EAAE,WAAW;MACrBF,MAAM,EAAE,oEAAoE;MAC5EK,UAAU,EAAE,uBAAuB;MACnCE,WAAW,EAAE;KACd,EACD;MACEV,EAAE,EAAE,GAAG;MACPO,QAAQ,EAAE,iBAAiB;MAC3BF,QAAQ,EAAE,gBAAgB;MAC1BF,MAAM,EAAE,oEAAoE;MAC5EK,UAAU,EAAE,sBAAsB;MAClCE,WAAW,EAAE;KACd,CACF;EACH;EAEAyB,oBAAoBA,CAAA;IAClB,IAAI,CAACL,gBAAgB,GAAG,EAAE;EAC5B;EAEAM,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACL,cAAc,GAAG,CACpB;MACE/B,EAAE,EAAE,GAAG;MACPO,QAAQ,EAAE,eAAe;MACzBF,QAAQ,EAAE,cAAc;MACxBF,MAAM,EAAE,oEAAoE;MAC5Ea,cAAc,EAAE,KAAK;MACrBC,UAAU,EAAE,GAAG;MACfC,UAAU,EAAE,GAAG;MACfR,WAAW,EAAE;KACd,EACD;MACEV,EAAE,EAAE,GAAG;MACPO,QAAQ,EAAE,aAAa;MACvBF,QAAQ,EAAE,aAAa;MACvBF,MAAM,EAAE,iEAAiE;MACzEa,cAAc,EAAE,KAAK;MACrBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,IAAI;MAChBR,WAAW,EAAE;KACd,CACF;EACH;EAEA2B,cAAcA,CAAA;IACZ;IACA,IAAI,CAACL,UAAU,GAAG,CAChB;MACEhC,EAAE,EAAE,GAAG;MACPwB,IAAI,EAAE,OAAO;MACbF,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;KACR,EACD;MACEvB,EAAE,EAAE,GAAG;MACPwB,IAAI,EAAE,KAAK;MACXF,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE;KACR,EACD;MACEvB,EAAE,EAAE,GAAG;MACPwB,IAAI,EAAE,aAAa;MACnBF,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE;KACR,EACD;MACEvB,EAAE,EAAE,GAAG;MACPwB,IAAI,EAAE,UAAU;MAChBF,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE;KACR,CACF;EACH;EAEAR,mBAAmBA,CAACuB,KAAa;IAC/B,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEAzC,UAAUA,CAAC0C,MAAc;IACvB,MAAMC,IAAI,GAAG,IAAI,CAACb,cAAc,CAACc,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5C,EAAE,KAAKyC,MAAM,CAAC;IAC3D,IAAIC,IAAI,EAAE;MACRA,IAAI,CAAChC,WAAW,GAAG,CAACgC,IAAI,CAAChC,WAAW;;EAExC;EAEAI,gBAAgBA,CAAC+B,YAAoB;IACnC,MAAMC,UAAU,GAAG,IAAI,CAACf,cAAc,CAACY,IAAI,CAACI,CAAC,IAAIA,CAAC,CAAC/C,EAAE,KAAK6C,YAAY,CAAC;IACvE,IAAIC,UAAU,EAAE;MACdA,UAAU,CAACpC,WAAW,GAAG,CAACoC,UAAU,CAACpC,WAAW;;EAEpD;EAEAsC,QAAQA,CAACC,SAAiB;IACxBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,SAAS,CAAC;IAC5C;EACF;EAEAG,cAAcA,CAACC,YAAoB;IACjCH,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEE,YAAY,CAAC;IAC7C,IAAI,CAACzB,MAAM,CAAC0B,QAAQ,CAAC,CAAC,WAAW,EAAED,YAAY,CAAC,CAAC;EACnD;;;uBAjIW5B,gBAAgB,EAAAxC,EAAA,CAAAsE,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAxE,EAAA,CAAAsE,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAhBlC,gBAAgB;MAAAmC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA7E,EAAA,CAAA8E,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCzB7BpF,EAAA,CAAAC,cAAA,eAAuB;UAQrBD,EANA,CAAAE,SAAA,4BAA+C,0BAGJ,uBAGN;UAInCF,EADF,CAAAC,cAAA,aAAyB,SACnB;UAAAD,EAAA,CAAAG,MAAA,wBAAiB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC1BJ,EAAA,CAAAC,cAAA,aAA6B;UAC3BD,EAAA,CAAAsF,UAAA,IAAAC,+BAAA,iBAAiE;UAWrEvF,EADE,CAAAI,YAAA,EAAM,EACF;UAIJJ,EADF,CAAAC,cAAA,aAAyB,UACnB;UAAAD,EAAA,CAAAG,MAAA,+BAAuB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChCJ,EAAA,CAAAC,cAAA,cAA6B;UAC3BD,EAAA,CAAAsF,UAAA,KAAAE,gCAAA,kBAAuE;UAe3ExF,EADE,CAAAI,YAAA,EAAM,EACF;UAIJJ,EADF,CAAAC,cAAA,cAAwB,UAClB;UAAAD,EAAA,CAAAG,MAAA,wBAAgB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACzBJ,EAAA,CAAAC,cAAA,cAA2B;UACzBD,EAAA,CAAAsF,UAAA,KAAAG,gCAAA,iBAMC;UAMPzF,EAFI,CAAAI,YAAA,EAAM,EACF,EACA;;;UAlD4CJ,EAAA,CAAAgB,SAAA,GAAiB;UAAjBhB,EAAA,CAAAiB,UAAA,YAAAoE,GAAA,CAAAzC,cAAA,CAAiB;UAiBX5C,EAAA,CAAAgB,SAAA,GAAiB;UAAjBhB,EAAA,CAAAiB,UAAA,YAAAoE,GAAA,CAAAvC,cAAA,CAAiB;UAuB9C9C,EAAA,CAAAgB,SAAA,GAAa;UAAbhB,EAAA,CAAAiB,UAAA,YAAAoE,GAAA,CAAAtC,UAAA,CAAa;;;qBDvCtCrD,YAAY,EAAAgG,EAAA,CAAAC,OAAA,EACZhG,YAAY,EAAA8E,EAAA,CAAAmB,UAAA,EAAAnB,EAAA,CAAAoB,gBAAA,EACZjG,WAAW,EAAAkG,EAAA,CAAAC,kBAAA,EACXlG,yBAAyB,EACzBC,uBAAuB,EACvBC,oBAAoB;MAAAiG,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}