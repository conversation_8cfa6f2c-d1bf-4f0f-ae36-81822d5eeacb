{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { TrendingProductsComponent } from '../trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../new-arrivals/new-arrivals.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/product.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@ionic/angular\";\nconst _c0 = a0 => [\"/category\", a0];\nfunction SidebarComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelement(1, \"img\", 11);\n    i0.ɵɵelementStart(2, \"div\", 12)(3, \"h5\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_8_Template_button_click_7_listener() {\n      const user_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.followUser(user_r2.id));\n    });\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", user_r2.avatar, i0.ɵɵsanitizeUrl)(\"alt\", user_r2.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r2.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r2.followedBy);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", user_r2.isFollowing ? \"Following\" : \"Follow\", \" \");\n  }\n}\nfunction SidebarComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵelement(1, \"img\", 11);\n    i0.ɵɵelementStart(2, \"div\", 15)(3, \"h5\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 16)(8, \"span\", 17);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 18);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_13_Template_button_click_12_listener() {\n      const influencer_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.followInfluencer(influencer_r5.id));\n    });\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const influencer_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", influencer_r5.avatar, i0.ɵɵsanitizeUrl)(\"alt\", influencer_r5.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(influencer_r5.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.formatFollowerCount(influencer_r5.followersCount), \" followers\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", influencer_r5.postsCount, \" posts\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", influencer_r5.engagement, \"% engagement\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", influencer_r5.isFollowing ? \"Following\" : \"Follow\", \" \");\n  }\n}\nfunction SidebarComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵelement(1, \"img\", 11);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c0, category_r6.slug));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", category_r6.image, i0.ɵɵsanitizeUrl)(\"alt\", category_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r6.name);\n  }\n}\nexport class SidebarComponent {\n  constructor(productService, router) {\n    this.productService = productService;\n    this.router = router;\n    this.suggestedUsers = [];\n    this.trendingProducts = [];\n    this.topInfluencers = [];\n    this.categories = [];\n  }\n  ngOnInit() {\n    this.loadSuggestedUsers();\n    this.loadTrendingProducts();\n    this.loadTopInfluencers();\n    this.loadCategories();\n  }\n  loadSuggestedUsers() {\n    // Mock data for suggested users\n    this.suggestedUsers = [{\n      id: '1',\n      username: 'fashionista_maya',\n      fullName: 'Maya Chen',\n      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',\n      followedBy: 'Followed by 12 others',\n      isFollowing: false\n    }, {\n      id: '2',\n      username: 'style_guru_alex',\n      fullName: 'Alex Rodriguez',\n      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',\n      followedBy: 'Followed by 8 others',\n      isFollowing: false\n    }];\n  }\n  loadTrendingProducts() {\n    this.trendingProducts = [];\n  }\n  loadTopInfluencers() {\n    // Mock data for top influencers\n    this.topInfluencers = [{\n      id: '1',\n      username: 'fashion_queen',\n      fullName: 'Priya Sharma',\n      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',\n      followersCount: 25000,\n      postsCount: 156,\n      engagement: 8.5,\n      isFollowing: false\n    }, {\n      id: '2',\n      username: 'style_maven',\n      fullName: 'Kavya Reddy',\n      avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150',\n      followersCount: 18000,\n      postsCount: 89,\n      engagement: 12.3,\n      isFollowing: true\n    }];\n  }\n  loadCategories() {\n    // Mock data for categories\n    this.categories = [{\n      id: '1',\n      name: 'Women',\n      slug: 'women',\n      image: 'https://images.unsplash.com/photo-1483985988355-763728e1935b?w=400'\n    }, {\n      id: '2',\n      name: 'Men',\n      slug: 'men',\n      image: 'https://images.unsplash.com/photo-1516257984-b1b4d707412e?w=400'\n    }, {\n      id: '3',\n      name: 'Accessories',\n      slug: 'accessories',\n      image: 'https://images.unsplash.com/photo-1506629905607-d405b7a30db9?w=400'\n    }, {\n      id: '4',\n      name: 'Footwear',\n      slug: 'footwear',\n      image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400'\n    }];\n  }\n  formatFollowerCount(count) {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'k';\n    }\n    return count.toString();\n  }\n  followUser(userId) {\n    const user = this.suggestedUsers.find(u => u.id === userId);\n    if (user) {\n      user.isFollowing = !user.isFollowing;\n    }\n  }\n  followInfluencer(influencerId) {\n    const influencer = this.topInfluencers.find(i => i.id === influencerId);\n    if (influencer) {\n      influencer.isFollowing = !influencer.isFollowing;\n    }\n  }\n  quickBuy(productId) {\n    console.log('Quick buy product:', productId);\n    // TODO: Implement quick buy functionality\n  }\n  browseCategory(categorySlug) {\n    console.log('Browse category:', categorySlug);\n    this.router.navigate(['/category', categorySlug]);\n  }\n  static {\n    this.ɵfac = function SidebarComponent_Factory(t) {\n      return new (t || SidebarComponent)(i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SidebarComponent,\n      selectors: [[\"app-sidebar\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 19,\n      vars: 3,\n      consts: [[1, \"sidebar\"], [1, \"suggestions\"], [1, \"suggestion-list\"], [\"class\", \"suggestion-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"influencers\"], [1, \"influencer-list\"], [\"class\", \"influencer-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"categories\"], [1, \"category-list\"], [\"class\", \"category-item\", \"routerLinkActive\", \"active\", \"tabindex\", \"0\", 3, \"routerLink\", 4, \"ngFor\", \"ngForOf\"], [1, \"suggestion-item\"], [3, \"src\", \"alt\"], [1, \"suggestion-info\"], [1, \"follow-btn\", 3, \"click\"], [1, \"influencer-item\"], [1, \"influencer-info\"], [1, \"influencer-stats\"], [1, \"posts-count\"], [1, \"engagement\"], [\"routerLinkActive\", \"active\", \"tabindex\", \"0\", 1, \"category-item\", 3, \"routerLink\"]],\n      template: function SidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"aside\", 0);\n          i0.ɵɵelement(1, \"app-trending-products\")(2, \"app-featured-brands\")(3, \"app-new-arrivals\");\n          i0.ɵɵelementStart(4, \"div\", 1)(5, \"h3\");\n          i0.ɵɵtext(6, \"Suggested for you\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 2);\n          i0.ɵɵtemplate(8, SidebarComponent_div_8_Template, 9, 5, \"div\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 4)(10, \"h3\");\n          i0.ɵɵtext(11, \"Top Fashion Influencers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 5);\n          i0.ɵɵtemplate(13, SidebarComponent_div_13_Template, 14, 7, \"div\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 7)(15, \"h3\");\n          i0.ɵɵtext(16, \"Shop by Category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 8);\n          i0.ɵɵtemplate(18, SidebarComponent_div_18_Template, 4, 6, \"div\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.suggestedUsers);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.topInfluencers);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, RouterModule, i2.RouterLink, i2.RouterLinkActive, IonicModule, i4.RouterLinkDelegate, TrendingProductsComponent, FeaturedBrandsComponent, NewArrivalsComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.sidebar[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 80px;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n  max-height: calc(100vh - 100px);\\n  overflow-y: auto;\\n  padding-right: 8px;\\n}\\n\\napp-trending-products[_ngcontent-%COMP%], app-featured-brands[_ngcontent-%COMP%], app-new-arrivals[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: block;\\n  margin-bottom: 24px;\\n}\\n\\n.suggestions[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #6c5ce7;\\n}\\n.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  margin: 0;\\n}\\n.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);\\n  color: white;\\n  border: none;\\n  padding: 12px 20px;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 15px rgba(108, 92, 231, 0.3);\\n}\\n.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(108, 92, 231, 0.4);\\n}\\n.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDCA1\\\";\\n  font-size: 28px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  padding: 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid #f8f9fa;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1a1a1a;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 12px;\\n  color: #666;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);\\n  color: white;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 8px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 15px rgba(108, 92, 231, 0.3);\\n}\\n\\n.influencers[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n}\\n.influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDC51\\\";\\n  font-size: 28px;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  padding: 20px;\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 16px;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 70px;\\n  height: 70px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid #f8f9fa;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0 0 6px 0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1a1a1a;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  font-size: 14px;\\n  color: #666;\\n  font-weight: 500;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 6px;\\n  margin-bottom: 16px;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  background: #f8f9fa;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  font-weight: 500;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);\\n  color: white;\\n  border: none;\\n  padding: 10px 20px;\\n  border-radius: 8px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 15px rgba(232, 67, 147, 0.3);\\n}\\n\\n.categories[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n}\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDECD\\uFE0F\\\";\\n  font-size: 28px;\\n}\\n.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(2, 1fr);\\n  gap: 16px;\\n}\\n.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  padding: 20px 16px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-align: center;\\n  text-decoration: none;\\n  color: inherit;\\n}\\n.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\\n}\\n.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 70px;\\n  height: 70px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  margin-bottom: 12px;\\n  border: 3px solid #f8f9fa;\\n  transition: transform 0.3s ease;\\n}\\n.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #1a1a1a;\\n  text-align: center;\\n}\\n\\n@media (max-width: 768px) {\\n  .suggestions[_ngcontent-%COMP%], .influencers[_ngcontent-%COMP%], .categories[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before, .influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before, .categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n    font-size: 24px;\\n  }\\n  .suggestion-item[_ngcontent-%COMP%], .influencer-item[_ngcontent-%COMP%] {\\n    padding: 12px;\\n    gap: 12px;\\n  }\\n  .suggestion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n  }\\n  .suggestion-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .influencer-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .category-list[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 12px;\\n  }\\n  .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    text-align: left;\\n    padding: 12px;\\n    gap: 12px;\\n  }\\n  .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n    margin-bottom: 0;\\n  }\\n  .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "IonicModule", "TrendingProductsComponent", "FeaturedBrandsComponent", "NewArrivalsComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "SidebarComponent_div_8_Template_button_click_7_listener", "user_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "followUser", "id", "ɵɵadvance", "ɵɵproperty", "avatar", "ɵɵsanitizeUrl", "fullName", "ɵɵtextInterpolate", "username", "<PERSON><PERSON><PERSON>", "ɵɵtextInterpolate1", "isFollowing", "SidebarComponent_div_13_Template_button_click_12_listener", "influencer_r5", "_r4", "followInfluencer", "formatFollowerCount", "followersCount", "postsCount", "engagement", "ɵɵpureFunction1", "_c0", "category_r6", "slug", "image", "name", "SidebarComponent", "constructor", "productService", "router", "suggestedUsers", "trendingProducts", "topInfluencers", "categories", "ngOnInit", "loadSuggestedUsers", "loadTrendingProducts", "loadTopInfluencers", "loadCategories", "count", "toFixed", "toString", "userId", "user", "find", "u", "influencerId", "influencer", "i", "quickBuy", "productId", "console", "log", "browseCategory", "categorySlug", "navigate", "ɵɵdirectiveInject", "i1", "ProductService", "i2", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SidebarComponent_Template", "rf", "ctx", "ɵɵtemplate", "SidebarComponent_div_8_Template", "SidebarComponent_div_13_Template", "SidebarComponent_div_18_Template", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RouterLink", "RouterLinkActive", "i4", "RouterLinkDelegate", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\sidebar\\sidebar.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\sidebar\\sidebar.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\n\nimport { ProductService } from '../../../../core/services/product.service';\nimport { Product } from '../../../../core/models/product.model';\nimport { TrendingProductsComponent } from '../trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../new-arrivals/new-arrivals.component';\n\n@Component({\n  selector: 'app-sidebar',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    IonicModule,\n    TrendingProductsComponent,\n    FeaturedBrandsComponent,\n    NewArrivalsComponent\n  ],\n  templateUrl: './sidebar.component.html',\n  styleUrls: ['./sidebar.component.scss']\n})\nexport class SidebarComponent implements OnInit {\n  suggestedUsers: any[] = [];\n  trendingProducts: Product[] = [];\n  topInfluencers: any[] = [];\n  categories: any[] = [];\n\n  constructor(\n    private productService: ProductService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadSuggestedUsers();\n    this.loadTrendingProducts();\n    this.loadTopInfluencers();\n    this.loadCategories();\n  }\n\n  loadSuggestedUsers() {\n    // Mock data for suggested users\n    this.suggestedUsers = [\n      {\n        id: '1',\n        username: 'fashionista_maya',\n        fullName: 'Maya Chen',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',\n        followedBy: 'Followed by 12 others',\n        isFollowing: false\n      },\n      {\n        id: '2',\n        username: 'style_guru_alex',\n        fullName: 'Alex Rodriguez',\n        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',\n        followedBy: 'Followed by 8 others',\n        isFollowing: false\n      }\n    ];\n  }\n\n  loadTrendingProducts() {\n    this.trendingProducts = [];\n  }\n\n  loadTopInfluencers() {\n    // Mock data for top influencers\n    this.topInfluencers = [\n      {\n        id: '1',\n        username: 'fashion_queen',\n        fullName: 'Priya Sharma',\n        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',\n        followersCount: 25000,\n        postsCount: 156,\n        engagement: 8.5,\n        isFollowing: false\n      },\n      {\n        id: '2',\n        username: 'style_maven',\n        fullName: 'Kavya Reddy',\n        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150',\n        followersCount: 18000,\n        postsCount: 89,\n        engagement: 12.3,\n        isFollowing: true\n      }\n    ];\n  }\n\n  loadCategories() {\n    // Mock data for categories\n    this.categories = [\n      {\n        id: '1',\n        name: 'Women',\n        slug: 'women',\n        image: 'https://images.unsplash.com/photo-1483985988355-763728e1935b?w=400'\n      },\n      {\n        id: '2',\n        name: 'Men',\n        slug: 'men',\n        image: 'https://images.unsplash.com/photo-1516257984-b1b4d707412e?w=400'\n      },\n      {\n        id: '3',\n        name: 'Accessories',\n        slug: 'accessories',\n        image: 'https://images.unsplash.com/photo-1506629905607-d405b7a30db9?w=400'\n      },\n      {\n        id: '4',\n        name: 'Footwear',\n        slug: 'footwear',\n        image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400'\n      }\n    ];\n  }\n\n  formatFollowerCount(count: number): string {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'k';\n    }\n    return count.toString();\n  }\n\n  followUser(userId: string) {\n    const user = this.suggestedUsers.find(u => u.id === userId);\n    if (user) {\n      user.isFollowing = !user.isFollowing;\n    }\n  }\n\n  followInfluencer(influencerId: string) {\n    const influencer = this.topInfluencers.find(i => i.id === influencerId);\n    if (influencer) {\n      influencer.isFollowing = !influencer.isFollowing;\n    }\n  }\n\n  quickBuy(productId: string) {\n    console.log('Quick buy product:', productId);\n    // TODO: Implement quick buy functionality\n  }\n\n  browseCategory(categorySlug: string) {\n    console.log('Browse category:', categorySlug);\n    this.router.navigate(['/category', categorySlug]);\n  }\n}\n", "<aside class=\"sidebar\">\n  <!-- Trending Products Section -->\n  <app-trending-products></app-trending-products>\n\n  <!-- Featured Brands Section -->\n  <app-featured-brands></app-featured-brands>\n\n  <!-- New Arrivals Section -->\n  <app-new-arrivals></app-new-arrivals>\n\n  <!-- Suggested for you -->\n  <div class=\"suggestions\">\n    <h3>Suggested for you</h3>\n    <div class=\"suggestion-list\">\n      <div class=\"suggestion-item\" *ngFor=\"let user of suggestedUsers\">\n        <img [src]=\"user.avatar\" [alt]=\"user.fullName\">\n        <div class=\"suggestion-info\">\n          <h5>{{ user.username }}</h5>\n          <p>{{ user.followedBy }}</p>\n        </div>\n        <button class=\"follow-btn\" (click)=\"followUser(user.id)\">\n          {{ user.isFollowing ? 'Following' : 'Follow' }}\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Top Fashion Influencers -->\n  <div class=\"influencers\">\n    <h3>Top Fashion Influencers</h3>\n    <div class=\"influencer-list\">\n      <div class=\"influencer-item\" *ngFor=\"let influencer of topInfluencers\">\n        <img [src]=\"influencer.avatar\" [alt]=\"influencer.fullName\">\n        <div class=\"influencer-info\">\n          <h5>{{ influencer.username }}</h5>\n          <p>{{ formatFollowerCount(influencer.followersCount) }} followers</p>\n          <div class=\"influencer-stats\">\n            <span class=\"posts-count\">{{ influencer.postsCount }} posts</span>\n            <span class=\"engagement\">{{ influencer.engagement }}% engagement</span>\n          </div>\n          <button class=\"follow-btn\" (click)=\"followInfluencer(influencer.id)\">\n            {{ influencer.isFollowing ? 'Following' : 'Follow' }}\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Shop by Category -->\n  <div class=\"categories\">\n    <h3>Shop by Category</h3>\n    <div class=\"category-list\">\n      <div\n        class=\"category-item\"\n        *ngFor=\"let category of categories\"\n        [routerLink]=\"['/category', category.slug]\"\n        routerLinkActive=\"active\"\n        tabindex=\"0\"\n      >\n        <img [src]=\"category.image\" [alt]=\"category.name\">\n        <span>{{ category.name }}</span>\n      </div>\n    </div>\n  </div>\n</aside>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,WAAW,QAAQ,gBAAgB;AAI5C,SAASC,yBAAyB,QAAQ,kDAAkD;AAC5F,SAASC,uBAAuB,QAAQ,8CAA8C;AACtF,SAASC,oBAAoB,QAAQ,wCAAwC;;;;;;;;;;ICKvEC,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAE,SAAA,cAA+C;IAE7CF,EADF,CAAAC,cAAA,cAA6B,SACvB;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5BJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAqB;IAC1BH,EAD0B,CAAAI,YAAA,EAAI,EACxB;IACNJ,EAAA,CAAAC,cAAA,iBAAyD;IAA9BD,EAAA,CAAAK,UAAA,mBAAAC,wDAAA;MAAA,MAAAC,OAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,UAAA,CAAAP,OAAA,CAAAQ,EAAA,CAAmB;IAAA,EAAC;IACtDf,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;;;;IARCJ,EAAA,CAAAgB,SAAA,EAAmB;IAAChB,EAApB,CAAAiB,UAAA,QAAAV,OAAA,CAAAW,MAAA,EAAAlB,EAAA,CAAAmB,aAAA,CAAmB,QAAAZ,OAAA,CAAAa,QAAA,CAAsB;IAExCpB,EAAA,CAAAgB,SAAA,GAAmB;IAAnBhB,EAAA,CAAAqB,iBAAA,CAAAd,OAAA,CAAAe,QAAA,CAAmB;IACpBtB,EAAA,CAAAgB,SAAA,GAAqB;IAArBhB,EAAA,CAAAqB,iBAAA,CAAAd,OAAA,CAAAgB,UAAA,CAAqB;IAGxBvB,EAAA,CAAAgB,SAAA,GACF;IADEhB,EAAA,CAAAwB,kBAAA,MAAAjB,OAAA,CAAAkB,WAAA,+BACF;;;;;;IASFzB,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAE,SAAA,cAA2D;IAEzDF,EADF,CAAAC,cAAA,cAA6B,SACvB;IAAAD,EAAA,CAAAG,MAAA,GAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAA8D;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEnEJ,EADF,CAAAC,cAAA,cAA8B,eACF;IAAAD,EAAA,CAAAG,MAAA,GAAiC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAClEJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,IAAuC;IAClEH,EADkE,CAAAI,YAAA,EAAO,EACnE;IACNJ,EAAA,CAAAC,cAAA,kBAAqE;IAA1CD,EAAA,CAAAK,UAAA,mBAAAqB,0DAAA;MAAA,MAAAC,aAAA,GAAA3B,EAAA,CAAAQ,aAAA,CAAAoB,GAAA,EAAAlB,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAkB,gBAAA,CAAAF,aAAA,CAAAZ,EAAA,CAA+B;IAAA,EAAC;IAClEf,EAAA,CAAAG,MAAA,IACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;;;;;IAZCJ,EAAA,CAAAgB,SAAA,EAAyB;IAAChB,EAA1B,CAAAiB,UAAA,QAAAU,aAAA,CAAAT,MAAA,EAAAlB,EAAA,CAAAmB,aAAA,CAAyB,QAAAQ,aAAA,CAAAP,QAAA,CAA4B;IAEpDpB,EAAA,CAAAgB,SAAA,GAAyB;IAAzBhB,EAAA,CAAAqB,iBAAA,CAAAM,aAAA,CAAAL,QAAA,CAAyB;IAC1BtB,EAAA,CAAAgB,SAAA,GAA8D;IAA9DhB,EAAA,CAAAwB,kBAAA,KAAAb,MAAA,CAAAmB,mBAAA,CAAAH,aAAA,CAAAI,cAAA,gBAA8D;IAErC/B,EAAA,CAAAgB,SAAA,GAAiC;IAAjChB,EAAA,CAAAwB,kBAAA,KAAAG,aAAA,CAAAK,UAAA,WAAiC;IAClChC,EAAA,CAAAgB,SAAA,GAAuC;IAAvChB,EAAA,CAAAwB,kBAAA,KAAAG,aAAA,CAAAM,UAAA,iBAAuC;IAGhEjC,EAAA,CAAAgB,SAAA,GACF;IADEhB,EAAA,CAAAwB,kBAAA,MAAAG,aAAA,CAAAF,WAAA,+BACF;;;;;IAUJzB,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAE,SAAA,cAAkD;IAClDF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAC3BH,EAD2B,CAAAI,YAAA,EAAO,EAC5B;;;;IANJJ,EAAA,CAAAiB,UAAA,eAAAjB,EAAA,CAAAkC,eAAA,IAAAC,GAAA,EAAAC,WAAA,CAAAC,IAAA,EAA2C;IAItCrC,EAAA,CAAAgB,SAAA,EAAsB;IAAChB,EAAvB,CAAAiB,UAAA,QAAAmB,WAAA,CAAAE,KAAA,EAAAtC,EAAA,CAAAmB,aAAA,CAAsB,QAAAiB,WAAA,CAAAG,IAAA,CAAsB;IAC3CvC,EAAA,CAAAgB,SAAA,GAAmB;IAAnBhB,EAAA,CAAAqB,iBAAA,CAAAe,WAAA,CAAAG,IAAA,CAAmB;;;ADnCjC,OAAM,MAAOC,gBAAgB;EAM3BC,YACUC,cAA8B,EAC9BC,MAAc;IADd,KAAAD,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IAPhB,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,gBAAgB,GAAc,EAAE;IAChC,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,UAAU,GAAU,EAAE;EAKnB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAH,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACL,cAAc,GAAG,CACpB;MACE7B,EAAE,EAAE,GAAG;MACPO,QAAQ,EAAE,kBAAkB;MAC5BF,QAAQ,EAAE,WAAW;MACrBF,MAAM,EAAE,oEAAoE;MAC5EK,UAAU,EAAE,uBAAuB;MACnCE,WAAW,EAAE;KACd,EACD;MACEV,EAAE,EAAE,GAAG;MACPO,QAAQ,EAAE,iBAAiB;MAC3BF,QAAQ,EAAE,gBAAgB;MAC1BF,MAAM,EAAE,oEAAoE;MAC5EK,UAAU,EAAE,sBAAsB;MAClCE,WAAW,EAAE;KACd,CACF;EACH;EAEAyB,oBAAoBA,CAAA;IAClB,IAAI,CAACL,gBAAgB,GAAG,EAAE;EAC5B;EAEAM,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACL,cAAc,GAAG,CACpB;MACE/B,EAAE,EAAE,GAAG;MACPO,QAAQ,EAAE,eAAe;MACzBF,QAAQ,EAAE,cAAc;MACxBF,MAAM,EAAE,oEAAoE;MAC5Ea,cAAc,EAAE,KAAK;MACrBC,UAAU,EAAE,GAAG;MACfC,UAAU,EAAE,GAAG;MACfR,WAAW,EAAE;KACd,EACD;MACEV,EAAE,EAAE,GAAG;MACPO,QAAQ,EAAE,aAAa;MACvBF,QAAQ,EAAE,aAAa;MACvBF,MAAM,EAAE,iEAAiE;MACzEa,cAAc,EAAE,KAAK;MACrBC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,IAAI;MAChBR,WAAW,EAAE;KACd,CACF;EACH;EAEA2B,cAAcA,CAAA;IACZ;IACA,IAAI,CAACL,UAAU,GAAG,CAChB;MACEhC,EAAE,EAAE,GAAG;MACPwB,IAAI,EAAE,OAAO;MACbF,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;KACR,EACD;MACEvB,EAAE,EAAE,GAAG;MACPwB,IAAI,EAAE,KAAK;MACXF,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE;KACR,EACD;MACEvB,EAAE,EAAE,GAAG;MACPwB,IAAI,EAAE,aAAa;MACnBF,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE;KACR,EACD;MACEvB,EAAE,EAAE,GAAG;MACPwB,IAAI,EAAE,UAAU;MAChBF,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE;KACR,CACF;EACH;EAEAR,mBAAmBA,CAACuB,KAAa;IAC/B,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEAzC,UAAUA,CAAC0C,MAAc;IACvB,MAAMC,IAAI,GAAG,IAAI,CAACb,cAAc,CAACc,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5C,EAAE,KAAKyC,MAAM,CAAC;IAC3D,IAAIC,IAAI,EAAE;MACRA,IAAI,CAAChC,WAAW,GAAG,CAACgC,IAAI,CAAChC,WAAW;;EAExC;EAEAI,gBAAgBA,CAAC+B,YAAoB;IACnC,MAAMC,UAAU,GAAG,IAAI,CAACf,cAAc,CAACY,IAAI,CAACI,CAAC,IAAIA,CAAC,CAAC/C,EAAE,KAAK6C,YAAY,CAAC;IACvE,IAAIC,UAAU,EAAE;MACdA,UAAU,CAACpC,WAAW,GAAG,CAACoC,UAAU,CAACpC,WAAW;;EAEpD;EAEAsC,QAAQA,CAACC,SAAiB;IACxBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,SAAS,CAAC;IAC5C;EACF;EAEAG,cAAcA,CAACC,YAAoB;IACjCH,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEE,YAAY,CAAC;IAC7C,IAAI,CAACzB,MAAM,CAAC0B,QAAQ,CAAC,CAAC,WAAW,EAAED,YAAY,CAAC,CAAC;EACnD;;;uBAjIW5B,gBAAgB,EAAAxC,EAAA,CAAAsE,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAxE,EAAA,CAAAsE,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAhBlC,gBAAgB;MAAAmC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA7E,EAAA,CAAA8E,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCzB7BpF,EAAA,CAAAC,cAAA,eAAuB;UAQrBD,EANA,CAAAE,SAAA,4BAA+C,0BAGJ,uBAGN;UAInCF,EADF,CAAAC,cAAA,aAAyB,SACnB;UAAAD,EAAA,CAAAG,MAAA,wBAAiB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC1BJ,EAAA,CAAAC,cAAA,aAA6B;UAC3BD,EAAA,CAAAsF,UAAA,IAAAC,+BAAA,iBAAiE;UAWrEvF,EADE,CAAAI,YAAA,EAAM,EACF;UAIJJ,EADF,CAAAC,cAAA,aAAyB,UACnB;UAAAD,EAAA,CAAAG,MAAA,+BAAuB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChCJ,EAAA,CAAAC,cAAA,cAA6B;UAC3BD,EAAA,CAAAsF,UAAA,KAAAE,gCAAA,kBAAuE;UAe3ExF,EADE,CAAAI,YAAA,EAAM,EACF;UAIJJ,EADF,CAAAC,cAAA,cAAwB,UAClB;UAAAD,EAAA,CAAAG,MAAA,wBAAgB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACzBJ,EAAA,CAAAC,cAAA,cAA2B;UACzBD,EAAA,CAAAsF,UAAA,KAAAG,gCAAA,iBAMC;UAMPzF,EAFI,CAAAI,YAAA,EAAM,EACF,EACA;;;UAlD4CJ,EAAA,CAAAgB,SAAA,GAAiB;UAAjBhB,EAAA,CAAAiB,UAAA,YAAAoE,GAAA,CAAAzC,cAAA,CAAiB;UAiBX5C,EAAA,CAAAgB,SAAA,GAAiB;UAAjBhB,EAAA,CAAAiB,UAAA,YAAAoE,GAAA,CAAAvC,cAAA,CAAiB;UAuB9C9C,EAAA,CAAAgB,SAAA,GAAa;UAAbhB,EAAA,CAAAiB,UAAA,YAAAoE,GAAA,CAAAtC,UAAA,CAAa;;;qBDvCtCrD,YAAY,EAAAgG,EAAA,CAAAC,OAAA,EACZhG,YAAY,EAAA8E,EAAA,CAAAmB,UAAA,EAAAnB,EAAA,CAAAoB,gBAAA,EACZjG,WAAW,EAAAkG,EAAA,CAAAC,kBAAA,EACXlG,yBAAyB,EACzBC,uBAAuB,EACvBC,oBAAoB;MAAAiG,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}