{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nlet ShopByCategoryComponent = class ShopByCategoryComponent {\n  constructor(router) {\n    this.router = router;\n    this.categories = [];\n    this.isLoading = true;\n    this.error = null;\n    this.subscription = new Subscription();\n    // Slider properties\n    this.currentSlide = 0;\n    this.slideOffset = 0;\n    this.cardWidth = 200; // Width of each category card including margin\n    this.visibleCards = 4; // Number of cards visible at once\n    this.maxSlide = 0;\n    this.autoSlideDelay = 4500; // 4.5 seconds for categories\n    this.isAutoSliding = true;\n    this.isPaused = false;\n  }\n  ngOnInit() {\n    this.loadCategories();\n    this.updateResponsiveSettings();\n    this.setupResizeListener();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n  loadCategories() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        // Mock data for shop categories\n        _this.categories = [{\n          id: '1',\n          name: 'Women\\'s Fashion',\n          image: 'https://images.unsplash.com/photo-1483985988355-763728e1935b?w=300&h=200&fit=crop',\n          productCount: 15420,\n          description: 'Trendy outfits for every occasion',\n          trending: true,\n          discount: 30,\n          subcategories: ['Dresses', 'Tops', 'Bottoms', 'Accessories']\n        }, {\n          id: '2',\n          name: 'Men\\'s Fashion',\n          image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300&h=200&fit=crop',\n          productCount: 12850,\n          description: 'Stylish clothing for modern men',\n          trending: true,\n          discount: 25,\n          subcategories: ['Shirts', 'Pants', 'Jackets', 'Shoes']\n        }, {\n          id: '3',\n          name: 'Footwear',\n          image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=300&h=200&fit=crop',\n          productCount: 8960,\n          description: 'Step up your shoe game',\n          trending: false,\n          discount: 20,\n          subcategories: ['Sneakers', 'Formal', 'Casual', 'Sports']\n        }, {\n          id: '4',\n          name: 'Accessories',\n          image: 'https://images.unsplash.com/photo-1506629905607-d9c36e0a3f90?w=300&h=200&fit=crop',\n          productCount: 6750,\n          description: 'Complete your look',\n          trending: true,\n          subcategories: ['Bags', 'Jewelry', 'Watches', 'Belts']\n        }, {\n          id: '5',\n          name: 'Kids Fashion',\n          image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=300&h=200&fit=crop',\n          productCount: 4320,\n          description: 'Adorable styles for little ones',\n          trending: false,\n          discount: 35,\n          subcategories: ['Boys', 'Girls', 'Baby', 'Toys']\n        }, {\n          id: '6',\n          name: 'Sports & Fitness',\n          image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop',\n          productCount: 5680,\n          description: 'Gear up for your workout',\n          trending: true,\n          subcategories: ['Activewear', 'Equipment', 'Shoes', 'Supplements']\n        }, {\n          id: '7',\n          name: 'Beauty & Personal Care',\n          image: 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=300&h=200&fit=crop',\n          productCount: 7890,\n          description: 'Look and feel your best',\n          trending: false,\n          discount: 15,\n          subcategories: ['Skincare', 'Makeup', 'Haircare', 'Fragrance']\n        }, {\n          id: '8',\n          name: 'Home & Living',\n          image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=200&fit=crop',\n          productCount: 3450,\n          description: 'Style your space',\n          trending: false,\n          subcategories: ['Decor', 'Furniture', 'Kitchen', 'Bedding']\n        }];\n        _this.isLoading = false;\n        _this.updateSliderOnCategoriesLoad();\n      } catch (error) {\n        console.error('Error loading categories:', error);\n        _this.error = 'Failed to load categories';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onCategoryClick(category) {\n    this.router.navigate(['/category', category.id]);\n  }\n  formatProductCount(count) {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  onRetry() {\n    this.loadCategories();\n  }\n  trackByCategoryId(index, category) {\n    return category.id;\n  }\n  // Auto-sliding methods\n  startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.categories.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n  autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n  // Responsive methods\n  updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 480) {\n      this.cardWidth = 160;\n      this.visibleCards = 1;\n    } else if (width <= 768) {\n      this.cardWidth = 180;\n      this.visibleCards = 2;\n    } else if (width <= 1200) {\n      this.cardWidth = 200;\n      this.visibleCards = 3;\n    } else {\n      this.cardWidth = 220;\n      this.visibleCards = 4;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n  setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.categories.length - this.visibleCards);\n  }\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n  restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000);\n  }\n  // Update slider when categories load\n  updateSliderOnCategoriesLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n};\nShopByCategoryComponent = __decorate([Component({\n  selector: 'app-shop-by-category',\n  standalone: true,\n  imports: [CommonModule, IonicModule, CarouselModule],\n  templateUrl: './shop-by-category.component.html',\n  styleUrls: ['./shop-by-category.component.scss']\n})], ShopByCategoryComponent);\nexport { ShopByCategoryComponent };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}