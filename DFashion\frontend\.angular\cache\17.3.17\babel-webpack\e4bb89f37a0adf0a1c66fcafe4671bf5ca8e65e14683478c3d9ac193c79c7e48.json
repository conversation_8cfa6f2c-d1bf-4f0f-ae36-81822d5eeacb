{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@ionic/angular\";\nfunction SettingsComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"ion-spinner\", 5);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading settings...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SettingsComponent_div_2_li_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const permission_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", permission_r3, \" \");\n  }\n}\nfunction SettingsComponent_div_2_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12)(2, \"div\", 13);\n    i0.ɵɵtext(3, \"Email Notifications\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 14);\n    i0.ɵɵtext(5, \"Receive notifications via email\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 15)(7, \"ion-toggle\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingsComponent_div_2_div_57_Template_ion_toggle_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.emailNotifications, $event) || (ctx_r1.emailNotifications = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ionChange\", function SettingsComponent_div_2_div_57_Template_ion_toggle_ionChange_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.saveNotificationSettings());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.emailNotifications);\n  }\n}\nfunction SettingsComponent_div_2_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12)(2, \"div\", 13);\n    i0.ɵɵtext(3, \"Push Notifications\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 14);\n    i0.ɵɵtext(5, \"Receive push notifications on your device\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 15)(7, \"ion-toggle\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingsComponent_div_2_div_58_Template_ion_toggle_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.pushNotifications, $event) || (ctx_r1.pushNotifications = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ionChange\", function SettingsComponent_div_2_div_58_Template_ion_toggle_ionChange_7_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.saveNotificationSettings());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.pushNotifications);\n  }\n}\nfunction SettingsComponent_div_2_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"h3\", 10);\n    i0.ɵɵtext(2, \"Payment & Shipping\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 11)(4, \"div\", 12)(5, \"div\", 13);\n    i0.ɵɵtext(6, \"Payment Methods\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 14);\n    i0.ɵɵtext(8, \"Manage your saved payment methods\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 15)(10, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function SettingsComponent_div_2_div_67_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.managePaymentMethods());\n    });\n    i0.ɵɵelement(11, \"ion-icon\", 31);\n    i0.ɵɵtext(12, \" Manage \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 11)(14, \"div\", 12)(15, \"div\", 13);\n    i0.ɵɵtext(16, \"Shipping Addresses\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 14);\n    i0.ɵɵtext(18, \"Manage your delivery addresses\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 15)(20, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function SettingsComponent_div_2_div_67_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.manageAddresses());\n    });\n    i0.ɵɵelement(21, \"ion-icon\", 32);\n    i0.ɵɵtext(22, \" Manage \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction SettingsComponent_div_2_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"h3\", 10);\n    i0.ɵɵtext(2, \"Vendor Settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 11)(4, \"div\", 12)(5, \"div\", 13);\n    i0.ɵɵtext(6, \"Vendor Dashboard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 14);\n    i0.ɵɵtext(8, \"Access vendor-specific settings and preferences\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 15)(10, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function SettingsComponent_div_2_div_68_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.navigateToVendorSettings());\n    });\n    i0.ɵɵelement(11, \"ion-icon\", 33);\n    i0.ɵɵtext(12, \" Open \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction SettingsComponent_div_2_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"h3\", 10);\n    i0.ɵɵtext(2, \"Administrator Settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 11)(4, \"div\", 12)(5, \"div\", 13);\n    i0.ɵɵtext(6, \"Admin Panel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 14);\n    i0.ɵɵtext(8, \"Access system administration settings\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 15)(10, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function SettingsComponent_div_2_div_69_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.navigateToAdminSettings());\n    });\n    i0.ɵɵelement(11, \"ion-icon\", 34);\n    i0.ɵɵtext(12, \" Open \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction SettingsComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"h1\");\n    i0.ɵɵtext(3, \"Account Settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Manage your account preferences and permissions\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 8)(7, \"div\", 9)(8, \"h3\", 10);\n    i0.ɵɵtext(9, \"Account Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 11)(11, \"div\", 12)(12, \"div\", 13);\n    i0.ɵɵtext(13, \"Account Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 14);\n    i0.ɵɵtext(15, \"Your current role and permissions\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 15)(17, \"span\", 16);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 11)(20, \"div\", 12)(21, \"div\", 13);\n    i0.ɵɵtext(22, \"Profile Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 14);\n    i0.ɵɵtext(24, \"Update your personal information\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 15)(26, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function SettingsComponent_div_2_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.editProfile());\n    });\n    i0.ɵɵelement(27, \"ion-icon\", 18);\n    i0.ɵɵtext(28, \" Edit \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 11)(30, \"div\", 12)(31, \"div\", 13);\n    i0.ɵɵtext(32, \"Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 14);\n    i0.ɵɵtext(34, \"Change your account password\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 15)(36, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function SettingsComponent_div_2_Template_button_click_36_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.changePassword());\n    });\n    i0.ɵɵelement(37, \"ion-icon\", 19);\n    i0.ɵɵtext(38, \" Change \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(39, \"div\", 9)(40, \"h3\", 10);\n    i0.ɵɵtext(41, \"Your Permissions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"p\");\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"ul\", 20);\n    i0.ɵɵtemplate(45, SettingsComponent_div_2_li_45_Template, 2, 1, \"li\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 9)(47, \"h3\", 10);\n    i0.ɵɵtext(48, \"Notifications\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 11)(50, \"div\", 12)(51, \"div\", 13);\n    i0.ɵɵtext(52, \"Enable Notifications\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"div\", 14);\n    i0.ɵɵtext(54, \"Receive notifications about your account activity\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 15)(56, \"ion-toggle\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingsComponent_div_2_Template_ion_toggle_ngModelChange_56_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.notificationsEnabled, $event) || (ctx_r1.notificationsEnabled = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ionChange\", function SettingsComponent_div_2_Template_ion_toggle_ionChange_56_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.saveNotificationSettings());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(57, SettingsComponent_div_2_div_57_Template, 8, 1, \"div\", 23)(58, SettingsComponent_div_2_div_58_Template, 8, 1, \"div\", 23);\n    i0.ɵɵelementStart(59, \"div\", 11)(60, \"div\", 12)(61, \"div\", 13);\n    i0.ɵɵtext(62, \"Marketing Emails\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"div\", 14);\n    i0.ɵɵtext(64, \"Receive promotional emails and offers\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(65, \"div\", 15)(66, \"ion-toggle\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingsComponent_div_2_Template_ion_toggle_ngModelChange_66_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.marketingEmails, $event) || (ctx_r1.marketingEmails = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ionChange\", function SettingsComponent_div_2_Template_ion_toggle_ionChange_66_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.saveNotificationSettings());\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(67, SettingsComponent_div_2_div_67_Template, 23, 0, \"div\", 24)(68, SettingsComponent_div_2_div_68_Template, 13, 0, \"div\", 24)(69, SettingsComponent_div_2_div_69_Template, 13, 0, \"div\", 24);\n    i0.ɵɵelementStart(70, \"div\", 9)(71, \"h3\", 10);\n    i0.ɵɵtext(72, \"Privacy & Security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"div\", 11)(74, \"div\", 12)(75, \"div\", 13);\n    i0.ɵɵtext(76, \"Privacy Settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(77, \"div\", 14);\n    i0.ɵɵtext(78, \"Control who can see your information\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(79, \"div\", 15)(80, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function SettingsComponent_div_2_Template_button_click_80_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.viewPrivacySettings());\n    });\n    i0.ɵɵelement(81, \"ion-icon\", 25);\n    i0.ɵɵtext(82, \" Manage \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(83, \"div\", 11)(84, \"div\", 12)(85, \"div\", 13);\n    i0.ɵɵtext(86, \"Security Settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(87, \"div\", 14);\n    i0.ɵɵtext(88, \"Two-factor authentication and security options\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(89, \"div\", 15)(90, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function SettingsComponent_div_2_Template_button_click_90_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.viewSecuritySettings());\n    });\n    i0.ɵɵelement(91, \"ion-icon\", 26);\n    i0.ɵɵtext(92, \" Manage \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(93, \"div\", 9)(94, \"h3\", 10);\n    i0.ɵɵtext(95, \"Account Management\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(96, \"div\", 11)(97, \"div\", 12)(98, \"div\", 13);\n    i0.ɵɵtext(99, \"Deactivate Account\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(100, \"div\", 14);\n    i0.ɵɵtext(101, \"Temporarily disable your account\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(102, \"div\", 15)(103, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function SettingsComponent_div_2_Template_button_click_103_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.deactivateAccount());\n    });\n    i0.ɵɵelement(104, \"ion-icon\", 28);\n    i0.ɵɵtext(105, \" Deactivate \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(106, \"div\", 11)(107, \"div\", 12)(108, \"div\", 13);\n    i0.ɵɵtext(109, \"Delete Account\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(110, \"div\", 14);\n    i0.ɵɵtext(111, \"Permanently delete your account and all data\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(112, \"div\", 15)(113, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function SettingsComponent_div_2_Template_button_click_113_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.deleteAccount());\n    });\n    i0.ɵɵelement(114, \"ion-icon\", 29);\n    i0.ɵɵtext(115, \" Delete \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getRoleBadgeClass());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getRoleDisplayName(), \" \");\n    i0.ɵɵadvance(25);\n    i0.ɵɵtextInterpolate1(\"Based on your \", ctx_r1.getRoleDisplayName(), \" role, you have access to:\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getRolePermissions());\n    i0.ɵɵadvance(11);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.notificationsEnabled);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.notificationsEnabled);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.notificationsEnabled);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.marketingEmails);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.canManageAccount());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.canAccessVendorSettings());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.canAccessAdminSettings());\n  }\n}\nfunction SettingsComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"ion-icon\", 36);\n    i0.ɵɵelementStart(2, \"h3\");\n    i0.ɵɵtext(3, \"Access Denied\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Please log in to access settings.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function SettingsComponent_div_3_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateToLogin());\n    });\n    i0.ɵɵtext(7, \" Login \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let SettingsComponent = /*#__PURE__*/(() => {\n  class SettingsComponent {\n    constructor(authService, router) {\n      this.authService = authService;\n      this.router = router;\n      this.currentUser = null;\n      this.isLoading = true;\n      // Settings state\n      this.notificationsEnabled = true;\n      this.emailNotifications = true;\n      this.pushNotifications = true;\n      this.marketingEmails = false;\n    }\n    ngOnInit() {\n      this.loadUserSettings();\n    }\n    loadUserSettings() {\n      this.currentUser = this.authService.currentUserValue;\n      this.loadNotificationSettings();\n      this.isLoading = false;\n    }\n    loadNotificationSettings() {\n      // Load from localStorage or API\n      const settings = localStorage.getItem('userSettings');\n      if (settings) {\n        const parsed = JSON.parse(settings);\n        this.notificationsEnabled = parsed.notificationsEnabled ?? true;\n        this.emailNotifications = parsed.emailNotifications ?? true;\n        this.pushNotifications = parsed.pushNotifications ?? true;\n        this.marketingEmails = parsed.marketingEmails ?? false;\n      }\n    }\n    saveNotificationSettings() {\n      const settings = {\n        notificationsEnabled: this.notificationsEnabled,\n        emailNotifications: this.emailNotifications,\n        pushNotifications: this.pushNotifications,\n        marketingEmails: this.marketingEmails\n      };\n      localStorage.setItem('userSettings', JSON.stringify(settings));\n      this.showSuccessMessage('Notification settings saved successfully!');\n    }\n    // Role-based access methods\n    canAccessVendorSettings() {\n      return this.authService.isVendor() || this.authService.isAdmin();\n    }\n    canAccessAdminSettings() {\n      return this.authService.isAdmin();\n    }\n    canManageAccount() {\n      return this.authService.isAuthenticated;\n    }\n    // Navigation methods\n    editProfile() {\n      this.router.navigate(['/account/edit-profile']);\n    }\n    changePassword() {\n      this.router.navigate(['/account/change-password']);\n    }\n    managePaymentMethods() {\n      this.router.navigate(['/account/payment-methods']);\n    }\n    manageAddresses() {\n      this.router.navigate(['/account/addresses']);\n    }\n    viewPrivacySettings() {\n      this.router.navigate(['/account/privacy']);\n    }\n    viewSecuritySettings() {\n      this.router.navigate(['/account/security']);\n    }\n    navigateToVendorSettings() {\n      if (this.canAccessVendorSettings()) {\n        this.router.navigate(['/vendor/settings']);\n      }\n    }\n    navigateToAdminSettings() {\n      if (this.canAccessAdminSettings()) {\n        this.router.navigate(['/admin/settings']);\n      }\n    }\n    // Account management\n    deactivateAccount() {\n      if (confirm('Are you sure you want to deactivate your account? This action can be reversed by contacting support.')) {\n        // Implement account deactivation\n        console.log('Account deactivation requested');\n        this.showSuccessMessage('Account deactivation request submitted. You will receive an email confirmation.');\n      }\n    }\n    deleteAccount() {\n      if (confirm('Are you sure you want to permanently delete your account? This action cannot be undone.')) {\n        if (confirm('This will permanently delete all your data. Are you absolutely sure?')) {\n          // Implement account deletion\n          console.log('Account deletion requested');\n          this.showSuccessMessage('Account deletion request submitted. You will receive an email with further instructions.');\n        }\n      }\n    }\n    getRoleDisplayName() {\n      switch (this.currentUser?.role) {\n        case 'customer':\n          return 'Customer';\n        case 'vendor':\n          return 'Vendor';\n        case 'admin':\n          return 'Administrator';\n        default:\n          return 'User';\n      }\n    }\n    getRoleBadgeClass() {\n      switch (this.currentUser?.role) {\n        case 'customer':\n          return 'customer-badge';\n        case 'vendor':\n          return 'vendor-badge';\n        case 'admin':\n          return 'admin-badge';\n        default:\n          return 'role-badge';\n      }\n    }\n    getRolePermissions() {\n      switch (this.currentUser?.role) {\n        case 'customer':\n          return ['Browse and purchase products', 'Create and manage wishlist', 'View order history', 'Leave product reviews', 'Follow other users'];\n        case 'vendor':\n          return ['All customer permissions', 'Add and manage products', 'View sales analytics', 'Manage inventory', 'Process orders', 'Create promotional content'];\n        case 'admin':\n          return ['All vendor permissions', 'Manage all users', 'Access system analytics', 'Moderate content', 'Configure system settings', 'Manage vendor approvals'];\n        default:\n          return [];\n      }\n    }\n    showSuccessMessage(message) {\n      // You can replace this with a proper toast/notification service\n      alert(message);\n    }\n    navigateToLogin() {\n      this.router.navigate(['/auth/login']);\n    }\n    static {\n      this.ɵfac = function SettingsComponent_Factory(t) {\n        return new (t || SettingsComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SettingsComponent,\n        selectors: [[\"app-settings\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 4,\n        vars: 3,\n        consts: [[1, \"settings-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"settings-content\", 4, \"ngIf\"], [\"class\", \"no-user-state\", 4, \"ngIf\"], [1, \"loading-container\"], [\"name\", \"crescent\"], [1, \"settings-content\"], [1, \"settings-header\"], [1, \"settings-sections\"], [1, \"settings-section\"], [1, \"section-title\"], [1, \"setting-item\"], [1, \"setting-info\"], [1, \"setting-title\"], [1, \"setting-description\"], [1, \"setting-control\"], [1, \"role-badge\", 3, \"ngClass\"], [1, \"btn-primary\", 3, \"click\"], [\"name\", \"create-outline\"], [\"name\", \"key-outline\"], [1, \"permission-list\"], [\"class\", \"permission-item\", 4, \"ngFor\", \"ngForOf\"], [3, \"ngModelChange\", \"ionChange\", \"ngModel\"], [\"class\", \"setting-item\", 4, \"ngIf\"], [\"class\", \"settings-section\", 4, \"ngIf\"], [\"name\", \"eye-outline\"], [\"name\", \"shield-checkmark-outline\"], [1, \"btn-danger\", 3, \"click\"], [\"name\", \"pause-outline\"], [\"name\", \"trash-outline\"], [1, \"permission-item\"], [\"name\", \"card-outline\"], [\"name\", \"location-outline\"], [\"name\", \"storefront-outline\"], [\"name\", \"shield-outline\"], [1, \"no-user-state\"], [\"name\", \"settings-outline\", 1, \"large-icon\"]],\n        template: function SettingsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, SettingsComponent_div_1_Template, 4, 0, \"div\", 1)(2, SettingsComponent_div_2_Template, 116, 11, \"div\", 2)(3, SettingsComponent_div_3_Template, 8, 0, \"div\", 3);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.currentUser);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.currentUser);\n          }\n        },\n        dependencies: [CommonModule, i3.NgClass, i3.NgForOf, i3.NgIf, FormsModule, i4.NgControlStatus, i4.NgModel, IonicModule, i5.IonIcon, i5.IonSpinner, i5.IonToggle, i5.BooleanValueAccessor],\n        styles: [\"@charset \\\"UTF-8\\\";.settings-container[_ngcontent-%COMP%]{padding:20px;max-width:800px;margin:0 auto}.settings-header[_ngcontent-%COMP%]{margin-bottom:30px}.settings-sections[_ngcontent-%COMP%]{display:grid;gap:20px}.settings-section[_ngcontent-%COMP%]{background:#fff;border-radius:12px;padding:20px;box-shadow:0 2px 10px #0000001a}.section-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:600;margin-bottom:15px;color:#333}.setting-item[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:15px 0;border-bottom:1px solid #f0f0f0}.setting-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.setting-info[_ngcontent-%COMP%]{flex:1}.setting-title[_ngcontent-%COMP%]{font-weight:500;margin-bottom:4px}.setting-description[_ngcontent-%COMP%]{font-size:.9rem;color:#666}.setting-control[_ngcontent-%COMP%]{margin-left:20px}.btn-primary[_ngcontent-%COMP%]{background:#667eea;color:#fff;border:none;padding:8px 16px;border-radius:6px;cursor:pointer;font-size:.9rem}.btn-danger[_ngcontent-%COMP%]{background:#dc3545;color:#fff;border:none;padding:8px 16px;border-radius:6px;cursor:pointer;font-size:.9rem}.role-badge[_ngcontent-%COMP%]{background:#667eea;color:#fff;padding:4px 12px;border-radius:20px;font-size:.8rem}.admin-badge[_ngcontent-%COMP%]{background:#dc3545}.vendor-badge[_ngcontent-%COMP%]{background:#28a745}.customer-badge[_ngcontent-%COMP%]{background:#17a2b8}.permission-list[_ngcontent-%COMP%]{list-style:none;padding:0;margin:10px 0}.permission-item[_ngcontent-%COMP%]{padding:5px 0;font-size:.9rem;color:#666}.permission-item[_ngcontent-%COMP%]:before{content:\\\"\\\\2713\\\";color:#28a745;margin-right:8px}\"]\n      });\n    }\n  }\n  return SettingsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}