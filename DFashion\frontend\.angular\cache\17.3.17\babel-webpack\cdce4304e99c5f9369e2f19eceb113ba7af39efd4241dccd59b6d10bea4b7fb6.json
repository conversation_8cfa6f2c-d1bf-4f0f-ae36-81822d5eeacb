{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AdminLayoutComponent } from './layout/admin-layout.component';\nimport { AdminDashboardComponent } from './dashboard/admin-dashboard.component';\nimport { AdminLoginComponent } from './auth/admin-login.component';\nimport { UserManagementComponent } from './users/user-management.component';\nimport { ProductManagementComponent } from './products/product-management.component';\nimport { OrderManagementComponent } from './orders/order-management.component';\nimport { AnalyticsComponent } from './analytics/analytics.component';\nimport { SettingsComponent } from './settings/settings.component';\nimport { AdminAuthGuard } from './guards/admin-auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'login',\n  component: AdminLoginComponent\n}, {\n  path: '',\n  component: AdminLayoutComponent,\n  canActivate: [AdminAuthGuard],\n  children: [{\n    path: '',\n    redirectTo: 'dashboard',\n    pathMatch: 'full'\n  }, {\n    path: 'dashboard',\n    component: AdminDashboardComponent,\n    data: {\n      title: 'Dashboard',\n      permission: 'dashboard:view'\n    }\n  }, {\n    path: 'users',\n    component: UserManagementComponent,\n    data: {\n      title: 'User Management',\n      permission: 'users:view'\n    }\n  }, {\n    path: 'products',\n    component: ProductManagementComponent,\n    data: {\n      title: 'Product Management',\n      permission: 'products:view'\n    }\n  }, {\n    path: 'orders',\n    component: OrderManagementComponent,\n    data: {\n      title: 'Order Management',\n      permission: 'orders:view'\n    }\n  }, {\n    path: 'analytics',\n    component: AnalyticsComponent,\n    data: {\n      title: 'Analytics',\n      permission: 'analytics:view'\n    }\n  }, {\n    path: 'settings',\n    component: SettingsComponent,\n    data: {\n      title: 'Settings',\n      permission: 'settings:view'\n    }\n  }]\n}];\nexport let AdminRoutingModule = /*#__PURE__*/(() => {\n  class AdminRoutingModule {\n    static {\n      this.ɵfac = function AdminRoutingModule_Factory(t) {\n        return new (t || AdminRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AdminRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return AdminRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}