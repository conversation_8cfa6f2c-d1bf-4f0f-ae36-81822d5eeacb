{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { isValidDate } from '../util/isDate';\nimport { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createErrorClass } from '../util/createErrorClass';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { executeSchedule } from '../util/executeSchedule';\nexport const TimeoutError = createErrorClass(_super => function TimeoutErrorImpl(info = null) {\n  _super(this);\n  this.message = 'Timeout has occurred';\n  this.name = 'TimeoutError';\n  this.info = info;\n});\nexport function timeout(config, schedulerArg) {\n  const {\n    first,\n    each,\n    with: _with = timeoutErrorFactory,\n    scheduler = schedulerArg !== null && schedulerArg !== void 0 ? schedulerArg : asyncScheduler,\n    meta = null\n  } = isValidDate(config) ? {\n    first: config\n  } : typeof config === 'number' ? {\n    each: config\n  } : config;\n  if (first == null && each == null) {\n    throw new TypeError('No timeout provided.');\n  }\n  return operate((source, subscriber) => {\n    let originalSourceSubscription;\n    let timerSubscription;\n    let lastValue = null;\n    let seen = 0;\n    const startTimer = delay => {\n      timerSubscription = executeSchedule(subscriber, scheduler, () => {\n        try {\n          originalSourceSubscription.unsubscribe();\n          innerFrom(_with({\n            meta,\n            lastValue,\n            seen\n          })).subscribe(subscriber);\n        } catch (err) {\n          subscriber.error(err);\n        }\n      }, delay);\n    };\n    originalSourceSubscription = source.subscribe(createOperatorSubscriber(subscriber, value => {\n      timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n      seen++;\n      subscriber.next(lastValue = value);\n      each > 0 && startTimer(each);\n    }, undefined, undefined, () => {\n      if (!(timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.closed)) {\n        timerSubscription === null || timerSubscription === void 0 ? void 0 : timerSubscription.unsubscribe();\n      }\n      lastValue = null;\n    }));\n    !seen && startTimer(first != null ? typeof first === 'number' ? first : +first - scheduler.now() : each);\n  });\n}\nfunction timeoutErrorFactory(info) {\n  throw new TimeoutError(info);\n}\n//# sourceMappingURL=timeout.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}