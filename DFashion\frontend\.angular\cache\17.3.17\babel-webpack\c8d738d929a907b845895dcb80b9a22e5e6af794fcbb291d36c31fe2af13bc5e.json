{"ast": null, "code": "import { importProvidersFrom } from '@angular/core';\nimport { provideRouter } from '@angular/router';\nimport { provideHttpClient, withInterceptors } from '@angular/common/http';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { IonicModule } from '@ionic/angular';\nimport { IonicStorageModule } from '@ionic/storage-angular';\nimport { routes } from './app.routes';\nimport { authInterceptor } from './core/interceptors/auth.interceptor';\nexport const appConfig = {\n  providers: [provideRouter(routes), provideHttpClient(withInterceptors([authInterceptor])), provideAnimations(), importProvidersFrom(MatSnackBarModule, MatDialogModule, IonicModule.forRoot({\n    mode: 'ios',\n    rippleEffect: true,\n    animated: true\n  }), IonicStorageModule.forRoot())]\n};", "map": {"version": 3, "names": ["importProvidersFrom", "provideRouter", "provideHttpClient", "withInterceptors", "provideAnimations", "MatSnackBarModule", "MatDialogModule", "IonicModule", "IonicStorageModule", "routes", "authInterceptor", "appConfig", "providers", "forRoot", "mode", "rippleEffect", "animated"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\app.config.ts"], "sourcesContent": ["import { ApplicationConfig, importProvidersFrom } from '@angular/core';\nimport { provideRouter } from '@angular/router';\nimport { provideHttpClient, withInterceptors } from '@angular/common/http';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { IonicModule } from '@ionic/angular';\nimport { IonicStorageModule } from '@ionic/storage-angular';\n\nimport { routes } from './app.routes';\nimport { authInterceptor } from './core/interceptors/auth.interceptor';\n\nexport const appConfig: ApplicationConfig = {\n  providers: [\n    provideRouter(routes),\n    provideHttpClient(withInterceptors([authInterceptor])),\n    provideAnimations(),\n    importProvidersFrom(\n      MatSnackBarModule,\n      MatDialogModule,\n      IonicModule.forRoot({\n        mode: 'ios',\n        rippleEffect: true,\n        animated: true\n      }),\n      IonicStorageModule.forRoot()\n    )\n  ]\n};\n"], "mappings": "AAAA,SAA4BA,mBAAmB,QAAQ,eAAe;AACtE,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,sBAAsB;AAC1E,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,kBAAkB,QAAQ,wBAAwB;AAE3D,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,eAAe,QAAQ,sCAAsC;AAEtE,OAAO,MAAMC,SAAS,GAAsB;EAC1CC,SAAS,EAAE,CACTX,aAAa,CAACQ,MAAM,CAAC,EACrBP,iBAAiB,CAACC,gBAAgB,CAAC,CAACO,eAAe,CAAC,CAAC,CAAC,EACtDN,iBAAiB,EAAE,EACnBJ,mBAAmB,CACjBK,iBAAiB,EACjBC,eAAe,EACfC,WAAW,CAACM,OAAO,CAAC;IAClBC,IAAI,EAAE,KAAK;IACXC,YAAY,EAAE,IAAI;IAClBC,QAAQ,EAAE;GACX,CAAC,EACFR,kBAAkB,CAACK,OAAO,EAAE,CAC7B;CAEJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}