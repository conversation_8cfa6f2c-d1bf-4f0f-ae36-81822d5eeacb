{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let VendorService = /*#__PURE__*/(() => {\n  class VendorService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = `${environment.apiUrl}/vendor`;\n      this.statsSubject = new BehaviorSubject(null);\n      this.stats$ = this.statsSubject.asObservable();\n    }\n    /**\n     * Get vendor dashboard statistics\n     */\n    getDashboardStats() {\n      return this.http.get(`${this.apiUrl}/dashboard/stats`);\n    }\n    /**\n     * Get vendor products with pagination and filters\n     */\n    getProducts(params) {\n      let queryParams = '';\n      if (params) {\n        const searchParams = new URLSearchParams();\n        Object.entries(params).forEach(([key, value]) => {\n          if (value !== undefined && value !== null) {\n            searchParams.append(key, value.toString());\n          }\n        });\n        queryParams = searchParams.toString();\n      }\n      const url = queryParams ? `${this.apiUrl}/products?${queryParams}` : `${this.apiUrl}/products`;\n      return this.http.get(url);\n    }\n    /**\n     * Get vendor orders with pagination and filters\n     */\n    getOrders(params) {\n      let queryParams = '';\n      if (params) {\n        const searchParams = new URLSearchParams();\n        Object.entries(params).forEach(([key, value]) => {\n          if (value !== undefined && value !== null) {\n            searchParams.append(key, value.toString());\n          }\n        });\n        queryParams = searchParams.toString();\n      }\n      const url = queryParams ? `${this.apiUrl}/orders?${queryParams}` : `${this.apiUrl}/orders`;\n      return this.http.get(url);\n    }\n    /**\n     * Update order status\n     */\n    updateOrderStatus(orderId, status) {\n      return this.http.put(`${this.apiUrl}/orders/${orderId}/status`, {\n        status\n      });\n    }\n    /**\n     * Update local stats cache\n     */\n    updateStats(stats) {\n      this.statsSubject.next(stats);\n    }\n    /**\n     * Get current stats from cache\n     */\n    getCurrentStats() {\n      return this.statsSubject.value;\n    }\n    /**\n     * Get order status display text\n     */\n    getOrderStatusText(status) {\n      const statusMap = {\n        'pending': 'Pending',\n        'confirmed': 'Confirmed',\n        'processing': 'Processing',\n        'shipped': 'Shipped',\n        'delivered': 'Delivered',\n        'cancelled': 'Cancelled'\n      };\n      return statusMap[status] || status;\n    }\n    /**\n     * Get order status color\n     */\n    getOrderStatusColor(status) {\n      const colorMap = {\n        'pending': 'warning',\n        'confirmed': 'primary',\n        'processing': 'secondary',\n        'shipped': 'tertiary',\n        'delivered': 'success',\n        'cancelled': 'danger'\n      };\n      return colorMap[status] || 'medium';\n    }\n    /**\n     * Get payment status display text\n     */\n    getPaymentStatusText(status) {\n      const statusMap = {\n        'pending': 'Pending',\n        'paid': 'Paid',\n        'failed': 'Failed',\n        'refunded': 'Refunded'\n      };\n      return statusMap[status] || status;\n    }\n    /**\n     * Get payment status color\n     */\n    getPaymentStatusColor(status) {\n      const colorMap = {\n        'pending': 'warning',\n        'paid': 'success',\n        'failed': 'danger',\n        'refunded': 'secondary'\n      };\n      return colorMap[status] || 'medium';\n    }\n    /**\n     * Format currency amount\n     */\n    formatCurrency(amount) {\n      return new Intl.NumberFormat('en-IN', {\n        style: 'currency',\n        currency: 'INR',\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0\n      }).format(amount);\n    }\n    /**\n     * Get month name from number\n     */\n    getMonthName(monthNumber) {\n      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n      return months[monthNumber - 1] || '';\n    }\n    /**\n     * Calculate percentage change\n     */\n    calculatePercentageChange(current, previous) {\n      if (previous === 0) return current > 0 ? 100 : 0;\n      return (current - previous) / previous * 100;\n    }\n    /**\n     * Get available order statuses for vendor\n     */\n    getAvailableOrderStatuses() {\n      return [{\n        value: 'pending',\n        label: 'Pending',\n        color: 'warning'\n      }, {\n        value: 'confirmed',\n        label: 'Confirmed',\n        color: 'primary'\n      }, {\n        value: 'processing',\n        label: 'Processing',\n        color: 'secondary'\n      }, {\n        value: 'shipped',\n        label: 'Shipped',\n        color: 'tertiary'\n      }, {\n        value: 'delivered',\n        label: 'Delivered',\n        color: 'success'\n      }, {\n        value: 'cancelled',\n        label: 'Cancelled',\n        color: 'danger'\n      }];\n    }\n    /**\n     * Check if status transition is valid\n     */\n    isValidStatusTransition(currentStatus, newStatus) {\n      const validTransitions = {\n        'pending': ['confirmed', 'cancelled'],\n        'confirmed': ['processing', 'cancelled'],\n        'processing': ['shipped', 'cancelled'],\n        'shipped': ['delivered'],\n        'delivered': [],\n        'cancelled': []\n      };\n      return validTransitions[currentStatus]?.includes(newStatus) || false;\n    }\n    static {\n      this.ɵfac = function VendorService_Factory(t) {\n        return new (t || VendorService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: VendorService,\n        factory: VendorService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return VendorService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}