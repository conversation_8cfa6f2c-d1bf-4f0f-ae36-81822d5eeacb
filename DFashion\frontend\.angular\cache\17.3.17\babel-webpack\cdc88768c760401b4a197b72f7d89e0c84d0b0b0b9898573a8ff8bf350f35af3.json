{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/trending.service\";\nimport * as i2 from \"../../../../core/services/social-interactions.service\";\nimport * as i3 from \"../../../../core/services/cart.service\";\nimport * as i4 from \"../../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3, 4, 5, 6];\nconst _c1 = () => [1, 2, 3, 4, 5];\nfunction NewArrivalsComponent_div_11_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵelement(1, \"div\", 16);\n    i0.ɵɵelementStart(2, \"div\", 17);\n    i0.ɵɵelement(3, \"div\", 18)(4, \"div\", 19)(5, \"div\", 20);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NewArrivalsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13);\n    i0.ɵɵtemplate(2, NewArrivalsComponent_div_11_div_2_Template, 6, 0, \"div\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction NewArrivalsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"ion-icon\", 22);\n    i0.ɵɵelementStart(2, \"p\", 23);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_12_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 25);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction NewArrivalsComponent_div_13_div_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDiscountPercentage(product_r4), \"% OFF \");\n  }\n}\nfunction NewArrivalsComponent_div_13_div_1_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 56);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r4.originalPrice));\n  }\n}\nfunction NewArrivalsComponent_div_13_div_1_ion_icon_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ion-icon\", 37);\n  }\n  if (rf & 2) {\n    const star_r5 = ctx.$implicit;\n    const product_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r5 <= product_r4.rating.average);\n    i0.ɵɵproperty(\"name\", star_r5 <= product_r4.rating.average ? \"star\" : \"star-outline\");\n  }\n}\nfunction NewArrivalsComponent_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_13_div_1_Template_div_click_0_listener() {\n      const product_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onProductClick(product_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 29);\n    i0.ɵɵelement(2, \"img\", 30);\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵelement(4, \"ion-icon\", 32);\n    i0.ɵɵtext(5, \" New \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 33);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, NewArrivalsComponent_div_13_div_1_div_8_Template, 2, 1, \"div\", 34);\n    i0.ɵɵelementStart(9, \"div\", 35)(10, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_13_div_1_Template_button_click_10_listener($event) {\n      const product_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onLikeProduct(product_r4, $event));\n    });\n    i0.ɵɵelement(11, \"ion-icon\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_13_div_1_Template_button_click_12_listener($event) {\n      const product_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onShareProduct(product_r4, $event));\n    });\n    i0.ɵɵelement(13, \"ion-icon\", 39);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 40)(15, \"div\", 41);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"h3\", 42);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 43)(20, \"span\", 44);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, NewArrivalsComponent_div_13_div_1_span_22_Template, 2, 1, \"span\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 46)(24, \"div\", 47);\n    i0.ɵɵtemplate(25, NewArrivalsComponent_div_13_div_1_ion_icon_25_Template, 1, 3, \"ion-icon\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 49);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 50)(29, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_13_div_1_Template_button_click_29_listener($event) {\n      const product_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAddToCart(product_r4, $event));\n    });\n    i0.ɵɵelement(30, \"ion-icon\", 52);\n    i0.ɵɵtext(31, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_13_div_1_Template_button_click_32_listener($event) {\n      const product_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAddToWishlist(product_r4, $event));\n    });\n    i0.ɵɵelement(33, \"ion-icon\", 54);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r4.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r4.images[0].alt || product_r4.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDaysAgo(product_r4.createdAt), \" days ago \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDiscountPercentage(product_r4) > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r1.isProductLiked(product_r4._id));\n    i0.ɵɵattribute(\"aria-label\", \"Like \" + product_r4.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isProductLiked(product_r4._id) ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", \"Share \" + product_r4.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(product_r4.brand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r4.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r4.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r4.originalPrice && product_r4.originalPrice > product_r4.price);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(15, _c1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r4.rating.count, \")\");\n  }\n}\nfunction NewArrivalsComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtemplate(1, NewArrivalsComponent_div_13_div_1_Template, 34, 16, \"div\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.newArrivals)(\"ngForTrackBy\", ctx_r1.trackByProductId);\n  }\n}\nfunction NewArrivalsComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵelement(1, \"ion-icon\", 58);\n    i0.ɵɵelementStart(2, \"h3\", 59);\n    i0.ɵɵtext(3, \"No New Arrivals\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 60);\n    i0.ɵɵtext(5, \"Check back soon for fresh new styles\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let NewArrivalsComponent = /*#__PURE__*/(() => {\n  class NewArrivalsComponent {\n    constructor(trendingService, socialService, cartService, wishlistService, router) {\n      this.trendingService = trendingService;\n      this.socialService = socialService;\n      this.cartService = cartService;\n      this.wishlistService = wishlistService;\n      this.router = router;\n      this.newArrivals = [];\n      this.isLoading = true;\n      this.error = null;\n      this.likedProducts = new Set();\n      this.subscription = new Subscription();\n    }\n    ngOnInit() {\n      this.loadNewArrivals();\n      this.subscribeNewArrivals();\n      this.subscribeLikedProducts();\n    }\n    ngOnDestroy() {\n      this.subscription.unsubscribe();\n    }\n    subscribeNewArrivals() {\n      this.subscription.add(this.trendingService.newArrivals$.subscribe(products => {\n        this.newArrivals = products;\n        this.isLoading = false;\n      }));\n    }\n    subscribeLikedProducts() {\n      this.subscription.add(this.socialService.likedProducts$.subscribe(likedProducts => {\n        this.likedProducts = likedProducts;\n      }));\n    }\n    loadNewArrivals() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          _this.isLoading = true;\n          _this.error = null;\n          yield _this.trendingService.loadNewArrivals(1, 6);\n        } catch (error) {\n          console.error('Error loading new arrivals:', error);\n          _this.error = 'Failed to load new arrivals';\n          _this.isLoading = false;\n        }\n      })();\n    }\n    onProductClick(product) {\n      this.router.navigate(['/product', product._id]);\n    }\n    onLikeProduct(product, event) {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        event.stopPropagation();\n        try {\n          const result = yield _this2.socialService.likeProduct(product._id);\n          if (result.success) {\n            console.log(result.message);\n          } else {\n            console.error('Failed to like product:', result.message);\n          }\n        } catch (error) {\n          console.error('Error liking product:', error);\n        }\n      })();\n    }\n    onShareProduct(product, event) {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        event.stopPropagation();\n        try {\n          const productUrl = `${window.location.origin}/product/${product._id}`;\n          yield navigator.clipboard.writeText(productUrl);\n          yield _this3.socialService.shareProduct(product._id, {\n            platform: 'copy_link',\n            message: `Check out this fresh arrival: ${product.name} from ${product.brand}!`\n          });\n          console.log('Product link copied to clipboard!');\n        } catch (error) {\n          console.error('Error sharing product:', error);\n        }\n      })();\n    }\n    onAddToCart(product, event) {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        event.stopPropagation();\n        try {\n          yield _this4.cartService.addToCart(product._id, 1);\n          console.log('Product added to cart!');\n        } catch (error) {\n          console.error('Error adding to cart:', error);\n        }\n      })();\n    }\n    onAddToWishlist(product, event) {\n      var _this5 = this;\n      return _asyncToGenerator(function* () {\n        event.stopPropagation();\n        try {\n          yield _this5.wishlistService.addToWishlist(product._id);\n          console.log('Product added to wishlist!');\n        } catch (error) {\n          console.error('Error adding to wishlist:', error);\n        }\n      })();\n    }\n    getDiscountPercentage(product) {\n      if (product.originalPrice && product.originalPrice > product.price) {\n        return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n      }\n      return 0;\n    }\n    formatPrice(price) {\n      return new Intl.NumberFormat('en-IN', {\n        style: 'currency',\n        currency: 'INR',\n        minimumFractionDigits: 0\n      }).format(price);\n    }\n    getDaysAgo(createdAt) {\n      const now = new Date();\n      const created = new Date(createdAt);\n      const diffTime = Math.abs(now.getTime() - created.getTime());\n      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n      return diffDays;\n    }\n    onRetry() {\n      this.loadNewArrivals();\n    }\n    onViewAll() {\n      this.router.navigate(['/products'], {\n        queryParams: {\n          filter: 'new-arrivals'\n        }\n      });\n    }\n    isProductLiked(productId) {\n      return this.likedProducts.has(productId);\n    }\n    trackByProductId(index, product) {\n      return product._id;\n    }\n    static {\n      this.ɵfac = function NewArrivalsComponent_Factory(t) {\n        return new (t || NewArrivalsComponent)(i0.ɵɵdirectiveInject(i1.TrendingService), i0.ɵɵdirectiveInject(i2.SocialInteractionsService), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService), i0.ɵɵdirectiveInject(i5.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: NewArrivalsComponent,\n        selectors: [[\"app-new-arrivals\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 15,\n        vars: 4,\n        consts: [[1, \"new-arrivals-container\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"sparkles\", 1, \"title-icon\"], [1, \"section-subtitle\"], [1, \"view-all-btn\", 3, \"click\"], [\"name\", \"chevron-forward\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"products-grid\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-card\"], [1, \"loading-image\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"products-grid\"], [\"class\", \"product-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"src\", \"alt\"], [1, \"new-badge\"], [\"name\", \"sparkles\"], [1, \"days-badge\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"action-buttons\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"name\", \"share-outline\"], [1, \"product-info\"], [1, \"product-brand\"], [1, \"product-name\"], [1, \"price-section\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"rating-section\"], [1, \"stars\"], [3, \"name\", \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [1, \"product-actions\"], [1, \"cart-btn\", 3, \"click\"], [\"name\", \"bag-add-outline\"], [1, \"wishlist-btn\", 3, \"click\"], [\"name\", \"heart-outline\"], [1, \"discount-badge\"], [1, \"original-price\"], [1, \"empty-container\"], [\"name\", \"sparkles-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"]],\n        template: function NewArrivalsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n            i0.ɵɵelement(4, \"ion-icon\", 4);\n            i0.ɵɵtext(5, \" New Arrivals \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"p\", 5);\n            i0.ɵɵtext(7, \"Fresh styles just landed\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function NewArrivalsComponent_Template_button_click_8_listener() {\n              return ctx.onViewAll();\n            });\n            i0.ɵɵtext(9, \" View All \");\n            i0.ɵɵelement(10, \"ion-icon\", 7);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(11, NewArrivalsComponent_div_11_Template, 3, 2, \"div\", 8)(12, NewArrivalsComponent_div_12_Template, 7, 1, \"div\", 9)(13, NewArrivalsComponent_div_13_Template, 2, 2, \"div\", 10)(14, NewArrivalsComponent_div_14_Template, 6, 0, \"div\", 11);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.newArrivals.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.newArrivals.length === 0);\n          }\n        },\n        dependencies: [CommonModule, i6.NgForOf, i6.NgIf, IonicModule, i7.IonIcon],\n        styles: [\".new-arrivals-container[_ngcontent-%COMP%]{padding:20px;background:linear-gradient(135deg,#667eea,#764ba2);border-radius:16px;margin-bottom:24px}.section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:24px}.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{flex:1}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#fff;margin:0 0 8px;display:flex;align-items:center;gap:12px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%]{font-size:28px;color:gold}.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%]{font-size:14px;color:#fffc;margin:0}.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]{background:#fff3;color:#fff;border:2px solid rgba(255,255,255,.3);padding:12px 20px;border-radius:25px;font-weight:600;font-size:14px;display:flex;align-items:center;gap:8px;cursor:pointer;transition:all .3s ease;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover{background:#ffffff4d;border-color:#ffffff80;transform:translateY(-2px)}.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:20px}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]{background:#ffffff1a;border-radius:16px;overflow:hidden;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-image[_ngcontent-%COMP%]{width:100%;height:200px;background:#fff3;animation:_ngcontent-%COMP%_loading 1.5s infinite}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]{padding:16px}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%]{height:12px;background:#fff3;animation:_ngcontent-%COMP%_loading 1.5s infinite;border-radius:6px;margin-bottom:8px}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%]{width:40%}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%]{width:60%}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%]{width:80%}@keyframes _ngcontent-%COMP%_loading{0%,to{opacity:.6}50%{opacity:1}}.error-container[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%]{font-size:48px;color:#ff6b6b;margin-bottom:16px}.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{font-size:16px;color:#fffc;margin-bottom:20px}.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]{background:#fff3;color:#fff;border:2px solid rgba(255,255,255,.3);padding:12px 24px;border-radius:8px;font-weight:600;cursor:pointer;display:flex;align-items:center;gap:8px;margin:0 auto;transition:all .3s ease;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover{background:#ffffff4d;border-color:#ffffff80}.products-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:20px}.product-card[_ngcontent-%COMP%]{background:#ffffff1a;border-radius:16px;overflow:hidden;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.2);transition:all .3s ease;cursor:pointer}.product-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);background:#ffffff26;box-shadow:0 12px 40px #0003}.product-card[_ngcontent-%COMP%]:hover   .action-buttons[_ngcontent-%COMP%]{opacity:1;transform:translateY(0)}.product-image-container[_ngcontent-%COMP%]{position:relative;overflow:hidden}.product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]{width:100%;height:200px;object-fit:cover;transition:transform .3s ease}.product-image-container[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%]{position:absolute;top:12px;left:12px;background:linear-gradient(135deg,gold,#ffed4e);color:#333;padding:6px 12px;border-radius:20px;font-size:12px;font-weight:600;display:flex;align-items:center;gap:4px}.product-image-container[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:14px}.product-image-container[_ngcontent-%COMP%]   .days-badge[_ngcontent-%COMP%]{position:absolute;top:50px;left:12px;background:#ffffffe6;color:#333;padding:4px 8px;border-radius:12px;font-size:10px;font-weight:600}.product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%]{position:absolute;top:12px;right:12px;background:#dc3545;color:#fff;padding:6px 10px;border-radius:12px;font-size:12px;font-weight:700}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]{position:absolute;top:50%;right:12px;transform:translateY(-50%);display:flex;flex-direction:column;gap:8px;opacity:0;transition:all .3s ease}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;border:none;background:#ffffffe6;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px;color:#333}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover{background:#fff;transform:scale(1.1)}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%]{color:#dc3545}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]{background:#dc354526}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#dc3545}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%]{color:#007bff}.product-info[_ngcontent-%COMP%]{padding:16px}.product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%]{font-size:12px;color:#ffffffb3;text-transform:uppercase;font-weight:600;letter-spacing:.5px;margin-bottom:4px}.product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:#fff;margin:0 0 12px;line-height:1.4;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:12px}.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%]{font-size:18px;font-weight:700;color:gold}.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%]{font-size:14px;color:#fff9;text-decoration:line-through}.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:16px}.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]{display:flex;gap:2px}.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:14px;color:#ffffff4d}.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon.filled[_ngcontent-%COMP%]{color:gold}.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%]{font-size:12px;color:#fff9}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]{display:flex;gap:8px}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]{flex:1;background:#fff3;color:#fff;border:2px solid rgba(255,255,255,.3);padding:12px 16px;border-radius:8px;font-weight:600;font-size:14px;display:flex;align-items:center;justify-content:center;gap:8px;cursor:pointer;transition:all .3s ease;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]:hover{background:#ffffff4d;border-color:#ffffff80;transform:translateY(-2px)}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]{width:44px;height:44px;border:2px solid rgba(255,255,255,.3);background:#ffffff1a;border-radius:8px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px;color:#fffc}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover{background:#fff3;border-color:#ffffff80}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%]{color:gold}.empty-container[_ngcontent-%COMP%]{text-align:center;padding:60px 20px}.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]{font-size:64px;color:#fff6;margin-bottom:20px}.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#fff;margin-bottom:8px}.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%]{font-size:14px;color:#ffffffb3}@media (max-width: 768px){.new-arrivals-container[_ngcontent-%COMP%]{padding:16px}.section-header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:16px}.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]{align-self:flex-end}.products-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:16px}.section-title[_ngcontent-%COMP%]{font-size:20px}}\"]\n      });\n    }\n  }\n  return NewArrivalsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}