<div class="post-detail-container" *ngIf="post">
  <!-- Header -->
  <header class="detail-header">
    <button class="btn-back" (click)="goBack()">
      <i class="fas fa-arrow-left"></i>
    </button>
    <h1>Post</h1>
    <button class="btn-menu" (click)="showMenu()">
      <i class="fas fa-ellipsis-h"></i>
    </button>
  </header>

  <!-- Post Content -->
  <div class="post-detail-content">
    <!-- User Info -->
    <div class="post-header">
      <div class="user-info" (click)="viewProfile(post.user._id)">
        <img [src]="post.user.avatar || '/assets/images/default-avatar.png'" 
             [alt]="post.user.fullName" 
             class="user-avatar">
        <div class="user-details">
          <div class="username-row">
            <span class="username">{{ post.user.username }}</span>
            <i class="fas fa-check-circle verified" *ngIf="post.user.isVerified"></i>
          </div>
          <span class="post-time">{{ getTimeAgo(post.createdAt) }}</span>
        </div>
      </div>
      
      <button class="btn-follow" *ngIf="!isOwnPost">Follow</button>
    </div>

    <!-- Media -->
    <div class="post-media">
      <div class="media-container" *ngFor="let media of post.media; let i = index">
        <img *ngIf="media.type === 'image'" 
             [src]="media.url" 
             [alt]="media.alt"
             class="post-image">
        
        <video *ngIf="media.type === 'video'"
               [src]="media.url"
               class="post-video"
               controls>
        </video>
      </div>

      <!-- Product Tags -->
      <div class="product-tags" *ngIf="post.products.length > 0">
        <div class="product-tag" 
             *ngFor="let productTag of post.products"
             [style.left.%]="productTag.position.x"
             [style.top.%]="productTag.position.y"
             (click)="showProductDetails(productTag.product)">
          <div class="product-tag-icon">
            <i class="fas fa-shopping-bag"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="post-actions">
      <div class="primary-actions">
        <button class="action-btn like" 
                [class.liked]="post.isLiked" 
                (click)="toggleLike()">
          <i class="fas fa-heart"></i>
          <span>{{ post.likes.length }}</span>
        </button>
        
        <button class="action-btn comment" (click)="focusCommentInput()">
          <i class="fas fa-comment"></i>
          <span>{{ post.comments.length }}</span>
        </button>
        
        <button class="action-btn share" (click)="sharePost()">
          <i class="fas fa-paper-plane"></i>
        </button>
      </div>
      
      <button class="action-btn save" 
              [class.saved]="post.isSaved" 
              (click)="toggleSave()">
        <i class="fas fa-bookmark"></i>
      </button>
    </div>

    <!-- Caption -->
    <div class="post-caption">
      <span class="username">{{ post.user.username }}</span>
      <span class="caption-text">{{ post.caption }}</span>
      
      <div class="hashtags" *ngIf="post.hashtags.length > 0">
        <span class="hashtag" 
              *ngFor="let hashtag of post.hashtags"
              (click)="searchHashtag(hashtag)">
          #{{ hashtag }}
        </span>
      </div>
    </div>

    <!-- E-commerce Actions -->
    <div class="ecommerce-actions" *ngIf="post.products.length > 0">
      <button class="ecom-btn buy-now" (click)="buyNow()">
        <i class="fas fa-bolt"></i>
        Buy Now
      </button>
      <button class="ecom-btn add-cart" (click)="addToCart()">
        <i class="fas fa-shopping-cart"></i>
        Add to Cart
      </button>
      <button class="ecom-btn wishlist" (click)="addToWishlist()">
        <i class="fas fa-heart"></i>
        Wishlist
      </button>
    </div>

    <!-- Comments Section -->
    <div class="comments-section">
      <h3>Comments</h3>
      
      <div class="comments-list">
        <div class="comment" *ngFor="let comment of post.comments">
          <img [src]="comment.user.avatar || '/assets/images/default-avatar.png'" 
               [alt]="comment.user.fullName" 
               class="comment-avatar"
               (click)="viewProfile(comment.user._id)">
          <div class="comment-content">
            <div class="comment-header">
              <span class="comment-username" (click)="viewProfile(comment.user._id)">
                {{ comment.user.username }}
              </span>
              <span class="comment-time">{{ getTimeAgo(comment.commentedAt) }}</span>
            </div>
            <p class="comment-text">{{ comment.text }}</p>
          </div>
        </div>
      </div>
      
      <!-- Add Comment -->
      <div class="add-comment">
        <img [src]="currentUser?.avatar || '/assets/images/default-avatar.png'" 
             [alt]="currentUser?.fullName" 
             class="comment-avatar">
        <input type="text" 
               #commentInput
               [(ngModel)]="newComment" 
               placeholder="Add a comment..."
               (keyup.enter)="addComment()"
               class="comment-input">
        <button class="btn-post-comment" 
                (click)="addComment()"
                [disabled]="!newComment || !newComment.trim()">
          Post
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Product Modal -->
<div class="product-modal" *ngIf="selectedProduct" (click)="closeProductModal()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h3>{{ selectedProduct.name }}</h3>
      <button class="btn-close" (click)="closeProductModal()">
        <i class="fas fa-times"></i>
      </button>
    </div>
    
    <div class="modal-body">
      <img [src]="selectedProduct.images[0]?.url" 
           [alt]="selectedProduct.name" 
           class="product-image">
      
      <div class="product-info">
        <p class="brand">{{ selectedProduct.brand }}</p>
        <div class="price">
          <span class="current-price">₹{{ selectedProduct.price | number:'1.0-0' }}</span>
          <span class="original-price" *ngIf="selectedProduct.originalPrice">
            ₹{{ selectedProduct.originalPrice | number:'1.0-0' }}
          </span>
        </div>
      </div>
      
      <div class="modal-actions">
        <button class="btn-primary" (click)="buyProductNow()">Buy Now</button>
        <button class="btn-secondary" (click)="addProductToCart()">Add to Cart</button>
        <button class="btn-outline" (click)="addProductToWishlist()">Add to Wishlist</button>
      </div>
    </div>
  </div>
</div>
