{"ast": null, "code": "export const shopRoutes = [{\n  path: '',\n  loadComponent: () => import('./pages/shop/shop.component').then(m => m.ShopComponent)\n}, {\n  path: 'product/:id',\n  loadComponent: () => import('./pages/product-detail/product-detail.component').then(m => m.ProductDetailComponent)\n}, {\n  path: 'category/:category',\n  loadComponent: () => import('./pages/category/category.component').then(m => m.CategoryComponent)\n}, {\n  path: 'cart',\n  loadComponent: () => import('./pages/cart/cart.component').then(m => m.CartComponent)\n}, {\n  path: 'checkout',\n  loadComponent: () => import('./pages/checkout/checkout.component').then(m => m.CheckoutComponent)\n}, {\n  path: 'wishlist',\n  loadComponent: () => import('./pages/wishlist/wishlist.component').then(m => m.WishlistComponent)\n}];", "map": {"version": 3, "names": ["shopRoutes", "path", "loadComponent", "then", "m", "ShopComponent", "ProductDetailComponent", "CategoryComponent", "CartComponent", "CheckoutComponent", "WishlistComponent"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\shop\\shop.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\n\nexport const shopRoutes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./pages/shop/shop.component').then(m => m.ShopComponent)\n  },\n  {\n    path: 'product/:id',\n    loadComponent: () => import('./pages/product-detail/product-detail.component').then(m => m.ProductDetailComponent)\n  },\n  {\n    path: 'category/:category',\n    loadComponent: () => import('./pages/category/category.component').then(m => m.CategoryComponent)\n  },\n  {\n    path: 'cart',\n    loadComponent: () => import('./pages/cart/cart.component').then(m => m.CartComponent)\n  },\n  {\n    path: 'checkout',\n    loadComponent: () => import('./pages/checkout/checkout.component').then(m => m.CheckoutComponent)\n  },\n  {\n    path: 'wishlist',\n    loadComponent: () => import('./pages/wishlist/wishlist.component').then(m => m.WishlistComponent)\n  }\n];\n"], "mappings": "AAEA,OAAO,MAAMA,UAAU,GAAW,CAChC;EACEC,IAAI,EAAE,EAAE;EACRC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa;CACrF,EACD;EACEJ,IAAI,EAAE,aAAa;EACnBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,iDAAiD,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,sBAAsB;CAClH,EACD;EACEL,IAAI,EAAE,oBAAoB;EAC1BC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,iBAAiB;CACjG,EACD;EACEN,IAAI,EAAE,MAAM;EACZC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,aAAa;CACrF,EACD;EACEP,IAAI,EAAE,UAAU;EAChBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,iBAAiB;CACjG,EACD;EACER,IAAI,EAAE,UAAU;EAChBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACM,iBAAiB;CACjG,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}