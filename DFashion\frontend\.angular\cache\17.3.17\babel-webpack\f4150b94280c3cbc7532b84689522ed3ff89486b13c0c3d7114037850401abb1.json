{"ast": null, "code": "import { FocusKeyManager } from '@angular/cdk/a11y';\nimport * as i1 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { hasModifierKey, SPACE, ENTER } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { Directive, InjectionToken, EventEmitter, forwardRef, booleanAttribute, TemplateRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Inject, Optional, ContentChild, ViewChild, Input, Output, QueryList, numberAttribute, ContentChildren, NgModule } from '@angular/core';\nimport { _getFocusedElementPierceShadowDom } from '@angular/cdk/platform';\nimport { Subject, of } from 'rxjs';\nimport { startWith, takeUntil } from 'rxjs/operators';\nconst _c0 = [\"*\"];\nfunction CdkStep_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nlet CdkStepHeader = /*#__PURE__*/(() => {\n  class CdkStepHeader {\n    constructor(_elementRef) {\n      this._elementRef = _elementRef;\n    }\n    /** Focuses the step header. */\n    focus() {\n      this._elementRef.nativeElement.focus();\n    }\n    static {\n      this.ɵfac = function CdkStepHeader_Factory(t) {\n        return new (t || CdkStepHeader)(i0.ɵɵdirectiveInject(i0.ElementRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkStepHeader,\n        selectors: [[\"\", \"cdkStepHeader\", \"\"]],\n        hostAttrs: [\"role\", \"tab\"],\n        standalone: true\n      });\n    }\n  }\n  return CdkStepHeader;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CdkStepLabel = /*#__PURE__*/(() => {\n  class CdkStepLabel {\n    constructor(/** @docs-private */template) {\n      this.template = template;\n    }\n    static {\n      this.ɵfac = function CdkStepLabel_Factory(t) {\n        return new (t || CdkStepLabel)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkStepLabel,\n        selectors: [[\"\", \"cdkStepLabel\", \"\"]],\n        standalone: true\n      });\n    }\n  }\n  return CdkStepLabel;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Used to generate unique ID for each stepper component. */\nlet nextId = 0;\n/** Change event emitted on selection changes. */\nclass StepperSelectionEvent {}\n/** Enum to represent the different states of the steps. */\nconst STEP_STATE = {\n  NUMBER: 'number',\n  EDIT: 'edit',\n  DONE: 'done',\n  ERROR: 'error'\n};\n/** InjectionToken that can be used to specify the global stepper options. */\nconst STEPPER_GLOBAL_OPTIONS = /*#__PURE__*/new InjectionToken('STEPPER_GLOBAL_OPTIONS');\nlet CdkStep = /*#__PURE__*/(() => {\n  class CdkStep {\n    /** Whether step is marked as completed. */\n    get completed() {\n      return this._completedOverride == null ? this._getDefaultCompleted() : this._completedOverride;\n    }\n    set completed(value) {\n      this._completedOverride = value;\n    }\n    _getDefaultCompleted() {\n      return this.stepControl ? this.stepControl.valid && this.interacted : this.interacted;\n    }\n    /** Whether step has an error. */\n    get hasError() {\n      return this._customError == null ? this._getDefaultError() : this._customError;\n    }\n    set hasError(value) {\n      this._customError = value;\n    }\n    _getDefaultError() {\n      return this.stepControl && this.stepControl.invalid && this.interacted;\n    }\n    constructor(_stepper, stepperOptions) {\n      this._stepper = _stepper;\n      /** Whether user has attempted to move away from the step. */\n      this.interacted = false;\n      /** Emits when the user has attempted to move away from the step. */\n      this.interactedStream = new EventEmitter();\n      /** Whether the user can return to this step once it has been marked as completed. */\n      this.editable = true;\n      /** Whether the completion of step is optional. */\n      this.optional = false;\n      this._completedOverride = null;\n      this._customError = null;\n      this._stepperOptions = stepperOptions ? stepperOptions : {};\n      this._displayDefaultIndicatorType = this._stepperOptions.displayDefaultIndicatorType !== false;\n    }\n    /** Selects this step component. */\n    select() {\n      this._stepper.selected = this;\n    }\n    /** Resets the step to its initial state. Note that this includes resetting form data. */\n    reset() {\n      this.interacted = false;\n      if (this._completedOverride != null) {\n        this._completedOverride = false;\n      }\n      if (this._customError != null) {\n        this._customError = false;\n      }\n      if (this.stepControl) {\n        this.stepControl.reset();\n      }\n    }\n    ngOnChanges() {\n      // Since basically all inputs of the MatStep get proxied through the view down to the\n      // underlying MatStepHeader, we have to make sure that change detection runs correctly.\n      this._stepper._stateChanged();\n    }\n    _markAsInteracted() {\n      if (!this.interacted) {\n        this.interacted = true;\n        this.interactedStream.emit(this);\n      }\n    }\n    /** Determines whether the error state can be shown. */\n    _showError() {\n      // We want to show the error state either if the user opted into/out of it using the\n      // global options, or if they've explicitly set it through the `hasError` input.\n      return this._stepperOptions.showError ?? this._customError != null;\n    }\n    static {\n      this.ɵfac = function CdkStep_Factory(t) {\n        return new (t || CdkStep)(i0.ɵɵdirectiveInject(forwardRef(() => CdkStepper)), i0.ɵɵdirectiveInject(STEPPER_GLOBAL_OPTIONS, 8));\n      };\n    }\n    static {\n      this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n        type: CdkStep,\n        selectors: [[\"cdk-step\"]],\n        contentQueries: function CdkStep_ContentQueries(rf, ctx, dirIndex) {\n          if (rf & 1) {\n            i0.ɵɵcontentQuery(dirIndex, CdkStepLabel, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.stepLabel = _t.first);\n          }\n        },\n        viewQuery: function CdkStep_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(TemplateRef, 7);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.content = _t.first);\n          }\n        },\n        inputs: {\n          stepControl: \"stepControl\",\n          label: \"label\",\n          errorMessage: \"errorMessage\",\n          ariaLabel: [i0.ɵɵInputFlags.None, \"aria-label\", \"ariaLabel\"],\n          ariaLabelledby: [i0.ɵɵInputFlags.None, \"aria-labelledby\", \"ariaLabelledby\"],\n          state: \"state\",\n          editable: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"editable\", \"editable\", booleanAttribute],\n          optional: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"optional\", \"optional\", booleanAttribute],\n          completed: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"completed\", \"completed\", booleanAttribute],\n          hasError: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"hasError\", \"hasError\", booleanAttribute]\n        },\n        outputs: {\n          interactedStream: \"interacted\"\n        },\n        exportAs: [\"cdkStep\"],\n        standalone: true,\n        features: [i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n        ngContentSelectors: _c0,\n        decls: 1,\n        vars: 0,\n        template: function CdkStep_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵprojectionDef();\n            i0.ɵɵtemplate(0, CdkStep_ng_template_0_Template, 1, 0, \"ng-template\");\n          }\n        },\n        encapsulation: 2,\n        changeDetection: 0\n      });\n    }\n  }\n  return CdkStep;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CdkStepper = /*#__PURE__*/(() => {\n  class CdkStepper {\n    /** The index of the selected step. */\n    get selectedIndex() {\n      return this._selectedIndex;\n    }\n    set selectedIndex(index) {\n      if (this.steps && this._steps) {\n        // Ensure that the index can't be out of bounds.\n        if (!this._isValidIndex(index) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n          throw Error('cdkStepper: Cannot assign out-of-bounds value to `selectedIndex`.');\n        }\n        this.selected?._markAsInteracted();\n        if (this._selectedIndex !== index && !this._anyControlsInvalidOrPending(index) && (index >= this._selectedIndex || this.steps.toArray()[index].editable)) {\n          this._updateSelectedItemIndex(index);\n        }\n      } else {\n        this._selectedIndex = index;\n      }\n    }\n    /** The step that is selected. */\n    get selected() {\n      return this.steps ? this.steps.toArray()[this.selectedIndex] : undefined;\n    }\n    set selected(step) {\n      this.selectedIndex = step && this.steps ? this.steps.toArray().indexOf(step) : -1;\n    }\n    /** Orientation of the stepper. */\n    get orientation() {\n      return this._orientation;\n    }\n    set orientation(value) {\n      // This is a protected method so that `MatStepper` can hook into it.\n      this._orientation = value;\n      if (this._keyManager) {\n        this._keyManager.withVerticalOrientation(value === 'vertical');\n      }\n    }\n    constructor(_dir, _changeDetectorRef, _elementRef) {\n      this._dir = _dir;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._elementRef = _elementRef;\n      /** Emits when the component is destroyed. */\n      this._destroyed = new Subject();\n      /** Steps that belong to the current stepper, excluding ones from nested steppers. */\n      this.steps = new QueryList();\n      /** List of step headers sorted based on their DOM order. */\n      this._sortedHeaders = new QueryList();\n      /** Whether the validity of previous steps should be checked or not. */\n      this.linear = false;\n      this._selectedIndex = 0;\n      /** Event emitted when the selected step has changed. */\n      this.selectionChange = new EventEmitter();\n      /** Output to support two-way binding on `[(selectedIndex)]` */\n      this.selectedIndexChange = new EventEmitter();\n      this._orientation = 'horizontal';\n      this._groupId = nextId++;\n    }\n    ngAfterContentInit() {\n      this._steps.changes.pipe(startWith(this._steps), takeUntil(this._destroyed)).subscribe(steps => {\n        this.steps.reset(steps.filter(step => step._stepper === this));\n        this.steps.notifyOnChanges();\n      });\n    }\n    ngAfterViewInit() {\n      // If the step headers are defined outside of the `ngFor` that renders the steps, like in the\n      // Material stepper, they won't appear in the `QueryList` in the same order as they're\n      // rendered in the DOM which will lead to incorrect keyboard navigation. We need to sort\n      // them manually to ensure that they're correct. Alternatively, we can change the Material\n      // template to inline the headers in the `ngFor`, but that'll result in a lot of\n      // code duplication. See #23539.\n      this._stepHeader.changes.pipe(startWith(this._stepHeader), takeUntil(this._destroyed)).subscribe(headers => {\n        this._sortedHeaders.reset(headers.toArray().sort((a, b) => {\n          const documentPosition = a._elementRef.nativeElement.compareDocumentPosition(b._elementRef.nativeElement);\n          // `compareDocumentPosition` returns a bitmask so we have to use a bitwise operator.\n          // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n          // tslint:disable-next-line:no-bitwise\n          return documentPosition & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;\n        }));\n        this._sortedHeaders.notifyOnChanges();\n      });\n      // Note that while the step headers are content children by default, any components that\n      // extend this one might have them as view children. We initialize the keyboard handling in\n      // AfterViewInit so we're guaranteed for both view and content children to be defined.\n      this._keyManager = new FocusKeyManager(this._sortedHeaders).withWrap().withHomeAndEnd().withVerticalOrientation(this._orientation === 'vertical');\n      (this._dir ? this._dir.change : of()).pipe(startWith(this._layoutDirection()), takeUntil(this._destroyed)).subscribe(direction => this._keyManager.withHorizontalOrientation(direction));\n      this._keyManager.updateActiveItem(this._selectedIndex);\n      // No need to `takeUntil` here, because we're the ones destroying `steps`.\n      this.steps.changes.subscribe(() => {\n        if (!this.selected) {\n          this._selectedIndex = Math.max(this._selectedIndex - 1, 0);\n        }\n      });\n      // The logic which asserts that the selected index is within bounds doesn't run before the\n      // steps are initialized, because we don't how many steps there are yet so we may have an\n      // invalid index on init. If that's the case, auto-correct to the default so we don't throw.\n      if (!this._isValidIndex(this._selectedIndex)) {\n        this._selectedIndex = 0;\n      }\n    }\n    ngOnDestroy() {\n      this._keyManager?.destroy();\n      this.steps.destroy();\n      this._sortedHeaders.destroy();\n      this._destroyed.next();\n      this._destroyed.complete();\n    }\n    /** Selects and focuses the next step in list. */\n    next() {\n      this.selectedIndex = Math.min(this._selectedIndex + 1, this.steps.length - 1);\n    }\n    /** Selects and focuses the previous step in list. */\n    previous() {\n      this.selectedIndex = Math.max(this._selectedIndex - 1, 0);\n    }\n    /** Resets the stepper to its initial state. Note that this includes clearing form data. */\n    reset() {\n      this._updateSelectedItemIndex(0);\n      this.steps.forEach(step => step.reset());\n      this._stateChanged();\n    }\n    /** Returns a unique id for each step label element. */\n    _getStepLabelId(i) {\n      return `cdk-step-label-${this._groupId}-${i}`;\n    }\n    /** Returns unique id for each step content element. */\n    _getStepContentId(i) {\n      return `cdk-step-content-${this._groupId}-${i}`;\n    }\n    /** Marks the component to be change detected. */\n    _stateChanged() {\n      this._changeDetectorRef.markForCheck();\n    }\n    /** Returns position state of the step with the given index. */\n    _getAnimationDirection(index) {\n      const position = index - this._selectedIndex;\n      if (position < 0) {\n        return this._layoutDirection() === 'rtl' ? 'next' : 'previous';\n      } else if (position > 0) {\n        return this._layoutDirection() === 'rtl' ? 'previous' : 'next';\n      }\n      return 'current';\n    }\n    /** Returns the type of icon to be displayed. */\n    _getIndicatorType(index, state = STEP_STATE.NUMBER) {\n      const step = this.steps.toArray()[index];\n      const isCurrentStep = this._isCurrentStep(index);\n      return step._displayDefaultIndicatorType ? this._getDefaultIndicatorLogic(step, isCurrentStep) : this._getGuidelineLogic(step, isCurrentStep, state);\n    }\n    _getDefaultIndicatorLogic(step, isCurrentStep) {\n      if (step._showError() && step.hasError && !isCurrentStep) {\n        return STEP_STATE.ERROR;\n      } else if (!step.completed || isCurrentStep) {\n        return STEP_STATE.NUMBER;\n      } else {\n        return step.editable ? STEP_STATE.EDIT : STEP_STATE.DONE;\n      }\n    }\n    _getGuidelineLogic(step, isCurrentStep, state = STEP_STATE.NUMBER) {\n      if (step._showError() && step.hasError && !isCurrentStep) {\n        return STEP_STATE.ERROR;\n      } else if (step.completed && !isCurrentStep) {\n        return STEP_STATE.DONE;\n      } else if (step.completed && isCurrentStep) {\n        return state;\n      } else if (step.editable && isCurrentStep) {\n        return STEP_STATE.EDIT;\n      } else {\n        return state;\n      }\n    }\n    _isCurrentStep(index) {\n      return this._selectedIndex === index;\n    }\n    /** Returns the index of the currently-focused step header. */\n    _getFocusIndex() {\n      return this._keyManager ? this._keyManager.activeItemIndex : this._selectedIndex;\n    }\n    _updateSelectedItemIndex(newIndex) {\n      const stepsArray = this.steps.toArray();\n      this.selectionChange.emit({\n        selectedIndex: newIndex,\n        previouslySelectedIndex: this._selectedIndex,\n        selectedStep: stepsArray[newIndex],\n        previouslySelectedStep: stepsArray[this._selectedIndex]\n      });\n      // If focus is inside the stepper, move it to the next header, otherwise it may become\n      // lost when the active step content is hidden. We can't be more granular with the check\n      // (e.g. checking whether focus is inside the active step), because we don't have a\n      // reference to the elements that are rendering out the content.\n      this._containsFocus() ? this._keyManager.setActiveItem(newIndex) : this._keyManager.updateActiveItem(newIndex);\n      this._selectedIndex = newIndex;\n      this.selectedIndexChange.emit(this._selectedIndex);\n      this._stateChanged();\n    }\n    _onKeydown(event) {\n      const hasModifier = hasModifierKey(event);\n      const keyCode = event.keyCode;\n      const manager = this._keyManager;\n      if (manager.activeItemIndex != null && !hasModifier && (keyCode === SPACE || keyCode === ENTER)) {\n        this.selectedIndex = manager.activeItemIndex;\n        event.preventDefault();\n      } else {\n        manager.setFocusOrigin('keyboard').onKeydown(event);\n      }\n    }\n    _anyControlsInvalidOrPending(index) {\n      if (this.linear && index >= 0) {\n        return this.steps.toArray().slice(0, index).some(step => {\n          const control = step.stepControl;\n          const isIncomplete = control ? control.invalid || control.pending || !step.interacted : !step.completed;\n          return isIncomplete && !step.optional && !step._completedOverride;\n        });\n      }\n      return false;\n    }\n    _layoutDirection() {\n      return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Checks whether the stepper contains the focused element. */\n    _containsFocus() {\n      const stepperElement = this._elementRef.nativeElement;\n      const focusedElement = _getFocusedElementPierceShadowDom();\n      return stepperElement === focusedElement || stepperElement.contains(focusedElement);\n    }\n    /** Checks whether the passed-in index is a valid step index. */\n    _isValidIndex(index) {\n      return index > -1 && (!this.steps || index < this.steps.length);\n    }\n    static {\n      this.ɵfac = function CdkStepper_Factory(t) {\n        return new (t || CdkStepper)(i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkStepper,\n        selectors: [[\"\", \"cdkStepper\", \"\"]],\n        contentQueries: function CdkStepper_ContentQueries(rf, ctx, dirIndex) {\n          if (rf & 1) {\n            i0.ɵɵcontentQuery(dirIndex, CdkStep, 5);\n            i0.ɵɵcontentQuery(dirIndex, CdkStepHeader, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._steps = _t);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._stepHeader = _t);\n          }\n        },\n        inputs: {\n          linear: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"linear\", \"linear\", booleanAttribute],\n          selectedIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"selectedIndex\", \"selectedIndex\", numberAttribute],\n          selected: \"selected\",\n          orientation: \"orientation\"\n        },\n        outputs: {\n          selectionChange: \"selectionChange\",\n          selectedIndexChange: \"selectedIndexChange\"\n        },\n        exportAs: [\"cdkStepper\"],\n        standalone: true,\n        features: [i0.ɵɵInputTransformsFeature]\n      });\n    }\n  }\n  return CdkStepper;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Button that moves to the next step in a stepper workflow. */\nlet CdkStepperNext = /*#__PURE__*/(() => {\n  class CdkStepperNext {\n    constructor(_stepper) {\n      this._stepper = _stepper;\n      /** Type of the next button. Defaults to \"submit\" if not specified. */\n      this.type = 'submit';\n    }\n    static {\n      this.ɵfac = function CdkStepperNext_Factory(t) {\n        return new (t || CdkStepperNext)(i0.ɵɵdirectiveInject(CdkStepper));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkStepperNext,\n        selectors: [[\"button\", \"cdkStepperNext\", \"\"]],\n        hostVars: 1,\n        hostBindings: function CdkStepperNext_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"click\", function CdkStepperNext_click_HostBindingHandler() {\n              return ctx._stepper.next();\n            });\n          }\n          if (rf & 2) {\n            i0.ɵɵhostProperty(\"type\", ctx.type);\n          }\n        },\n        inputs: {\n          type: \"type\"\n        },\n        standalone: true\n      });\n    }\n  }\n  return CdkStepperNext;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/** Button that moves to the previous step in a stepper workflow. */\nlet CdkStepperPrevious = /*#__PURE__*/(() => {\n  class CdkStepperPrevious {\n    constructor(_stepper) {\n      this._stepper = _stepper;\n      /** Type of the previous button. Defaults to \"button\" if not specified. */\n      this.type = 'button';\n    }\n    static {\n      this.ɵfac = function CdkStepperPrevious_Factory(t) {\n        return new (t || CdkStepperPrevious)(i0.ɵɵdirectiveInject(CdkStepper));\n      };\n    }\n    static {\n      this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n        type: CdkStepperPrevious,\n        selectors: [[\"button\", \"cdkStepperPrevious\", \"\"]],\n        hostVars: 1,\n        hostBindings: function CdkStepperPrevious_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"click\", function CdkStepperPrevious_click_HostBindingHandler() {\n              return ctx._stepper.previous();\n            });\n          }\n          if (rf & 2) {\n            i0.ɵɵhostProperty(\"type\", ctx.type);\n          }\n        },\n        inputs: {\n          type: \"type\"\n        },\n        standalone: true\n      });\n    }\n  }\n  return CdkStepperPrevious;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CdkStepperModule = /*#__PURE__*/(() => {\n  class CdkStepperModule {\n    static {\n      this.ɵfac = function CdkStepperModule_Factory(t) {\n        return new (t || CdkStepperModule)();\n      };\n    }\n    static {\n      this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n        type: CdkStepperModule\n      });\n    }\n    static {\n      this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n        imports: [BidiModule]\n      });\n    }\n  }\n  return CdkStepperModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkStep, CdkStepHeader, CdkStepLabel, CdkStepper, CdkStepperModule, CdkStepperNext, CdkStepperPrevious, STEPPER_GLOBAL_OPTIONS, STEP_STATE, StepperSelectionEvent };\n//# sourceMappingURL=stepper.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}