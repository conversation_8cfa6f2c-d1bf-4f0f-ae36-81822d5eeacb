{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { c as componentOnReady } from './helpers-be245865.js';\nimport { b as printRequiredElementError } from './index-9b0d46f4.js';\nconst ION_CONTENT_TAG_NAME = 'ION-CONTENT';\nconst ION_CONTENT_ELEMENT_SELECTOR = 'ion-content';\nconst ION_CONTENT_CLASS_SELECTOR = '.ion-content-scroll-host';\n/**\n * Selector used for implementations reliant on `<ion-content>` for scroll event changes.\n *\n * Developers should use the `.ion-content-scroll-host` selector to target the element emitting\n * scroll events. With virtual scroll implementations this will be the host element for\n * the scroll viewport.\n */\nconst ION_CONTENT_SELECTOR = `${ION_CONTENT_ELEMENT_SELECTOR}, ${ION_CONTENT_CLASS_SELECTOR}`;\nconst isIonContent = el => el.tagName === ION_CONTENT_TAG_NAME;\n/**\n * Waits for the element host fully initialize before\n * returning the inner scroll element.\n *\n * For `ion-content` the scroll target will be the result\n * of the `getScrollElement` function.\n *\n * For custom implementations it will be the element host\n * or a selector within the host, if supplied through `scrollTarget`.\n */\nconst getScrollElement = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (el) {\n    if (isIonContent(el)) {\n      yield new Promise(resolve => componentOnReady(el, resolve));\n      return el.getScrollElement();\n    }\n    return el;\n  });\n  return function getScrollElement(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\n/**\n * Queries the element matching the selector for IonContent.\n * See ION_CONTENT_SELECTOR for the selector used.\n */\nconst findIonContent = el => {\n  /**\n   * First we try to query the custom scroll host selector in cases where\n   * the implementation is using an outer `ion-content` with an inner custom\n   * scroll container.\n   */\n  const customContentHost = el.querySelector(ION_CONTENT_CLASS_SELECTOR);\n  if (customContentHost) {\n    return customContentHost;\n  }\n  return el.querySelector(ION_CONTENT_SELECTOR);\n};\n/**\n * Queries the closest element matching the selector for IonContent.\n */\nconst findClosestIonContent = el => {\n  return el.closest(ION_CONTENT_SELECTOR);\n};\n/**\n * Scrolls to the top of the element. If an `ion-content` is found, it will scroll\n * using the public API `scrollToTop` with a duration.\n */\nconst scrollToTop = (el, durationMs) => {\n  if (isIonContent(el)) {\n    const content = el;\n    return content.scrollToTop(durationMs);\n  }\n  return Promise.resolve(el.scrollTo({\n    top: 0,\n    left: 0,\n    behavior: durationMs > 0 ? 'smooth' : 'auto'\n  }));\n};\n/**\n * Scrolls by a specified X/Y distance in the component. If an `ion-content` is found, it will scroll\n * using the public API `scrollByPoint` with a duration.\n */\nconst scrollByPoint = (el, x, y, durationMs) => {\n  if (isIonContent(el)) {\n    const content = el;\n    return content.scrollByPoint(x, y, durationMs);\n  }\n  return Promise.resolve(el.scrollBy({\n    top: y,\n    left: x,\n    behavior: durationMs > 0 ? 'smooth' : 'auto'\n  }));\n};\n/**\n * Prints an error informing developers that an implementation requires an element to be used\n * within either the `ion-content` selector or the `.ion-content-scroll-host` class.\n */\nconst printIonContentErrorMsg = el => {\n  return printRequiredElementError(el, ION_CONTENT_ELEMENT_SELECTOR);\n};\n/**\n * Several components in Ionic need to prevent scrolling\n * during a gesture (card modal, range, item sliding, etc).\n * Use this utility to account for ion-content and custom content hosts.\n */\nconst disableContentScrollY = contentEl => {\n  if (isIonContent(contentEl)) {\n    const ionContent = contentEl;\n    const initialScrollY = ionContent.scrollY;\n    ionContent.scrollY = false;\n    /**\n     * This should be passed into resetContentScrollY\n     * so that we can revert ion-content's scrollY to the\n     * correct state. For example, if scrollY = false\n     * initially, we do not want to enable scrolling\n     * when we call resetContentScrollY.\n     */\n    return initialScrollY;\n  } else {\n    contentEl.style.setProperty('overflow', 'hidden');\n    return true;\n  }\n};\nconst resetContentScrollY = (contentEl, initialScrollY) => {\n  if (isIonContent(contentEl)) {\n    contentEl.scrollY = initialScrollY;\n  } else {\n    contentEl.style.removeProperty('overflow');\n  }\n};\nexport { ION_CONTENT_CLASS_SELECTOR as I, findIonContent as a, ION_CONTENT_ELEMENT_SELECTOR as b, scrollByPoint as c, disableContentScrollY as d, findClosestIonContent as f, getScrollElement as g, isIonContent as i, printIonContentErrorMsg as p, resetContentScrollY as r, scrollToTop as s };", "map": {"version": 3, "names": ["c", "componentOnReady", "b", "printRequiredElementError", "ION_CONTENT_TAG_NAME", "ION_CONTENT_ELEMENT_SELECTOR", "ION_CONTENT_CLASS_SELECTOR", "ION_CONTENT_SELECTOR", "isIonContent", "el", "tagName", "getScrollElement", "_ref", "_asyncToGenerator", "Promise", "resolve", "_x", "apply", "arguments", "find<PERSON><PERSON><PERSON><PERSON>nt", "customContentHost", "querySelector", "findClosestIonContent", "closest", "scrollToTop", "durationMs", "content", "scrollTo", "top", "left", "behavior", "scrollByPoint", "x", "y", "scrollBy", "printIonContentErrorMsg", "disableContentScrollY", "contentEl", "ionContent", "initialScrollY", "scrollY", "style", "setProperty", "resetContentScrollY", "removeProperty", "I", "a", "d", "f", "g", "i", "p", "r", "s"], "sources": ["E:/Fashion/DFashion/frontend/node_modules/@ionic/core/dist/esm/index-f3946ac1.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { c as componentOnReady } from './helpers-be245865.js';\nimport { b as printRequiredElementError } from './index-9b0d46f4.js';\n\nconst ION_CONTENT_TAG_NAME = 'ION-CONTENT';\nconst ION_CONTENT_ELEMENT_SELECTOR = 'ion-content';\nconst ION_CONTENT_CLASS_SELECTOR = '.ion-content-scroll-host';\n/**\n * Selector used for implementations reliant on `<ion-content>` for scroll event changes.\n *\n * Developers should use the `.ion-content-scroll-host` selector to target the element emitting\n * scroll events. With virtual scroll implementations this will be the host element for\n * the scroll viewport.\n */\nconst ION_CONTENT_SELECTOR = `${ION_CONTENT_ELEMENT_SELECTOR}, ${ION_CONTENT_CLASS_SELECTOR}`;\nconst isIonContent = (el) => el.tagName === ION_CONTENT_TAG_NAME;\n/**\n * Waits for the element host fully initialize before\n * returning the inner scroll element.\n *\n * For `ion-content` the scroll target will be the result\n * of the `getScrollElement` function.\n *\n * For custom implementations it will be the element host\n * or a selector within the host, if supplied through `scrollTarget`.\n */\nconst getScrollElement = async (el) => {\n    if (isIonContent(el)) {\n        await new Promise((resolve) => componentOnReady(el, resolve));\n        return el.getScrollElement();\n    }\n    return el;\n};\n/**\n * Queries the element matching the selector for IonContent.\n * See ION_CONTENT_SELECTOR for the selector used.\n */\nconst findIonContent = (el) => {\n    /**\n     * First we try to query the custom scroll host selector in cases where\n     * the implementation is using an outer `ion-content` with an inner custom\n     * scroll container.\n     */\n    const customContentHost = el.querySelector(ION_CONTENT_CLASS_SELECTOR);\n    if (customContentHost) {\n        return customContentHost;\n    }\n    return el.querySelector(ION_CONTENT_SELECTOR);\n};\n/**\n * Queries the closest element matching the selector for IonContent.\n */\nconst findClosestIonContent = (el) => {\n    return el.closest(ION_CONTENT_SELECTOR);\n};\n/**\n * Scrolls to the top of the element. If an `ion-content` is found, it will scroll\n * using the public API `scrollToTop` with a duration.\n */\nconst scrollToTop = (el, durationMs) => {\n    if (isIonContent(el)) {\n        const content = el;\n        return content.scrollToTop(durationMs);\n    }\n    return Promise.resolve(el.scrollTo({\n        top: 0,\n        left: 0,\n        behavior: durationMs > 0 ? 'smooth' : 'auto',\n    }));\n};\n/**\n * Scrolls by a specified X/Y distance in the component. If an `ion-content` is found, it will scroll\n * using the public API `scrollByPoint` with a duration.\n */\nconst scrollByPoint = (el, x, y, durationMs) => {\n    if (isIonContent(el)) {\n        const content = el;\n        return content.scrollByPoint(x, y, durationMs);\n    }\n    return Promise.resolve(el.scrollBy({\n        top: y,\n        left: x,\n        behavior: durationMs > 0 ? 'smooth' : 'auto',\n    }));\n};\n/**\n * Prints an error informing developers that an implementation requires an element to be used\n * within either the `ion-content` selector or the `.ion-content-scroll-host` class.\n */\nconst printIonContentErrorMsg = (el) => {\n    return printRequiredElementError(el, ION_CONTENT_ELEMENT_SELECTOR);\n};\n/**\n * Several components in Ionic need to prevent scrolling\n * during a gesture (card modal, range, item sliding, etc).\n * Use this utility to account for ion-content and custom content hosts.\n */\nconst disableContentScrollY = (contentEl) => {\n    if (isIonContent(contentEl)) {\n        const ionContent = contentEl;\n        const initialScrollY = ionContent.scrollY;\n        ionContent.scrollY = false;\n        /**\n         * This should be passed into resetContentScrollY\n         * so that we can revert ion-content's scrollY to the\n         * correct state. For example, if scrollY = false\n         * initially, we do not want to enable scrolling\n         * when we call resetContentScrollY.\n         */\n        return initialScrollY;\n    }\n    else {\n        contentEl.style.setProperty('overflow', 'hidden');\n        return true;\n    }\n};\nconst resetContentScrollY = (contentEl, initialScrollY) => {\n    if (isIonContent(contentEl)) {\n        contentEl.scrollY = initialScrollY;\n    }\n    else {\n        contentEl.style.removeProperty('overflow');\n    }\n};\n\nexport { ION_CONTENT_CLASS_SELECTOR as I, findIonContent as a, ION_CONTENT_ELEMENT_SELECTOR as b, scrollByPoint as c, disableContentScrollY as d, findClosestIonContent as f, getScrollElement as g, isIonContent as i, printIonContentErrorMsg as p, resetContentScrollY as r, scrollToTop as s };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,QAAQ,uBAAuB;AAC7D,SAASC,CAAC,IAAIC,yBAAyB,QAAQ,qBAAqB;AAEpE,MAAMC,oBAAoB,GAAG,aAAa;AAC1C,MAAMC,4BAA4B,GAAG,aAAa;AAClD,MAAMC,0BAA0B,GAAG,0BAA0B;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,GAAG,GAAGF,4BAA4B,KAAKC,0BAA0B,EAAE;AAC7F,MAAME,YAAY,GAAIC,EAAE,IAAKA,EAAE,CAACC,OAAO,KAAKN,oBAAoB;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMO,gBAAgB;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOJ,EAAE,EAAK;IACnC,IAAID,YAAY,CAACC,EAAE,CAAC,EAAE;MAClB,MAAM,IAAIK,OAAO,CAAEC,OAAO,IAAKd,gBAAgB,CAACQ,EAAE,EAAEM,OAAO,CAAC,CAAC;MAC7D,OAAON,EAAE,CAACE,gBAAgB,CAAC,CAAC;IAChC;IACA,OAAOF,EAAE;EACb,CAAC;EAAA,gBANKE,gBAAgBA,CAAAK,EAAA;IAAA,OAAAJ,IAAA,CAAAK,KAAA,OAAAC,SAAA;EAAA;AAAA,GAMrB;AACD;AACA;AACA;AACA;AACA,MAAMC,cAAc,GAAIV,EAAE,IAAK;EAC3B;AACJ;AACA;AACA;AACA;EACI,MAAMW,iBAAiB,GAAGX,EAAE,CAACY,aAAa,CAACf,0BAA0B,CAAC;EACtE,IAAIc,iBAAiB,EAAE;IACnB,OAAOA,iBAAiB;EAC5B;EACA,OAAOX,EAAE,CAACY,aAAa,CAACd,oBAAoB,CAAC;AACjD,CAAC;AACD;AACA;AACA;AACA,MAAMe,qBAAqB,GAAIb,EAAE,IAAK;EAClC,OAAOA,EAAE,CAACc,OAAO,CAAChB,oBAAoB,CAAC;AAC3C,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMiB,WAAW,GAAGA,CAACf,EAAE,EAAEgB,UAAU,KAAK;EACpC,IAAIjB,YAAY,CAACC,EAAE,CAAC,EAAE;IAClB,MAAMiB,OAAO,GAAGjB,EAAE;IAClB,OAAOiB,OAAO,CAACF,WAAW,CAACC,UAAU,CAAC;EAC1C;EACA,OAAOX,OAAO,CAACC,OAAO,CAACN,EAAE,CAACkB,QAAQ,CAAC;IAC/BC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAEL,UAAU,GAAG,CAAC,GAAG,QAAQ,GAAG;EAC1C,CAAC,CAAC,CAAC;AACP,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMM,aAAa,GAAGA,CAACtB,EAAE,EAAEuB,CAAC,EAAEC,CAAC,EAAER,UAAU,KAAK;EAC5C,IAAIjB,YAAY,CAACC,EAAE,CAAC,EAAE;IAClB,MAAMiB,OAAO,GAAGjB,EAAE;IAClB,OAAOiB,OAAO,CAACK,aAAa,CAACC,CAAC,EAAEC,CAAC,EAAER,UAAU,CAAC;EAClD;EACA,OAAOX,OAAO,CAACC,OAAO,CAACN,EAAE,CAACyB,QAAQ,CAAC;IAC/BN,GAAG,EAAEK,CAAC;IACNJ,IAAI,EAAEG,CAAC;IACPF,QAAQ,EAAEL,UAAU,GAAG,CAAC,GAAG,QAAQ,GAAG;EAC1C,CAAC,CAAC,CAAC;AACP,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMU,uBAAuB,GAAI1B,EAAE,IAAK;EACpC,OAAON,yBAAyB,CAACM,EAAE,EAAEJ,4BAA4B,CAAC;AACtE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM+B,qBAAqB,GAAIC,SAAS,IAAK;EACzC,IAAI7B,YAAY,CAAC6B,SAAS,CAAC,EAAE;IACzB,MAAMC,UAAU,GAAGD,SAAS;IAC5B,MAAME,cAAc,GAAGD,UAAU,CAACE,OAAO;IACzCF,UAAU,CAACE,OAAO,GAAG,KAAK;IAC1B;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,OAAOD,cAAc;EACzB,CAAC,MACI;IACDF,SAAS,CAACI,KAAK,CAACC,WAAW,CAAC,UAAU,EAAE,QAAQ,CAAC;IACjD,OAAO,IAAI;EACf;AACJ,CAAC;AACD,MAAMC,mBAAmB,GAAGA,CAACN,SAAS,EAAEE,cAAc,KAAK;EACvD,IAAI/B,YAAY,CAAC6B,SAAS,CAAC,EAAE;IACzBA,SAAS,CAACG,OAAO,GAAGD,cAAc;EACtC,CAAC,MACI;IACDF,SAAS,CAACI,KAAK,CAACG,cAAc,CAAC,UAAU,CAAC;EAC9C;AACJ,CAAC;AAED,SAAStC,0BAA0B,IAAIuC,CAAC,EAAE1B,cAAc,IAAI2B,CAAC,EAAEzC,4BAA4B,IAAIH,CAAC,EAAE6B,aAAa,IAAI/B,CAAC,EAAEoC,qBAAqB,IAAIW,CAAC,EAAEzB,qBAAqB,IAAI0B,CAAC,EAAErC,gBAAgB,IAAIsC,CAAC,EAAEzC,YAAY,IAAI0C,CAAC,EAAEf,uBAAuB,IAAIgB,CAAC,EAAER,mBAAmB,IAAIS,CAAC,EAAE5B,WAAW,IAAI6B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}