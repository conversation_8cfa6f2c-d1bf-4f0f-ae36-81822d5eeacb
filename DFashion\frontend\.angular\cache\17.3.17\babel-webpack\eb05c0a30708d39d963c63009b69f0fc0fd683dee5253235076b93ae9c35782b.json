{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { g as getElementRoot } from './helpers-be245865.js';\nconst pickerInternalIosCss = \":host{display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:200px;direction:ltr;z-index:0}:host .picker-before,:host .picker-after{position:absolute;width:100%;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:1;pointer-events:none}:host .picker-before{top:0;height:83px}@supports (inset-inline-start: 0){:host .picker-before{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host .picker-before{left:0}:host-context([dir=rtl]) .picker-before{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(:dir(rtl)) .picker-before{left:unset;right:unset;right:0}}}:host .picker-after{top:116px;height:84px}@supports (inset-inline-start: 0){:host .picker-after{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host .picker-after{left:0}:host-context([dir=rtl]) .picker-after{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(:dir(rtl)) .picker-after{left:unset;right:unset;right:0}}}:host .picker-highlight{border-radius:8px;left:0;right:0;top:50%;bottom:0;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;position:absolute;width:calc(100% - 16px);height:34px;-webkit-transform:translateY(-50%);transform:translateY(-50%);background:var(--wheel-highlight-background);z-index:-1}:host input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}:host ::slotted(ion-picker-column-internal:first-of-type){text-align:start}:host ::slotted(ion-picker-column-internal:last-of-type){text-align:end}:host ::slotted(ion-picker-column-internal:only-child){text-align:center}:host .picker-before{background:-webkit-gradient(linear, left top, left bottom, color-stop(20%, rgba(var(--wheel-fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 1)), to(rgba(var(--wheel-fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 0.8)));background:linear-gradient(to bottom, rgba(var(--wheel-fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 1) 20%, rgba(var(--wheel-fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 0.8) 100%)}:host .picker-after{background:-webkit-gradient(linear, left bottom, left top, color-stop(20%, rgba(var(--wheel-fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 1)), to(rgba(var(--wheel-fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 0.8)));background:linear-gradient(to top, rgba(var(--wheel-fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 1) 20%, rgba(var(--wheel-fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 0.8) 100%)}:host .picker-highlight{background:var(--wheel-highlight-background, var(--ion-color-step-150, #eeeeef))}\";\nconst IonPickerInternalIosStyle0 = pickerInternalIosCss;\nconst pickerInternalMdCss = \":host{display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:200px;direction:ltr;z-index:0}:host .picker-before,:host .picker-after{position:absolute;width:100%;-webkit-transform:translateZ(0);transform:translateZ(0);z-index:1;pointer-events:none}:host .picker-before{top:0;height:83px}@supports (inset-inline-start: 0){:host .picker-before{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host .picker-before{left:0}:host-context([dir=rtl]) .picker-before{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(:dir(rtl)) .picker-before{left:unset;right:unset;right:0}}}:host .picker-after{top:116px;height:84px}@supports (inset-inline-start: 0){:host .picker-after{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host .picker-after{left:0}:host-context([dir=rtl]) .picker-after{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(:dir(rtl)) .picker-after{left:unset;right:unset;right:0}}}:host .picker-highlight{border-radius:8px;left:0;right:0;top:50%;bottom:0;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;position:absolute;width:calc(100% - 16px);height:34px;-webkit-transform:translateY(-50%);transform:translateY(-50%);background:var(--wheel-highlight-background);z-index:-1}:host input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}:host ::slotted(ion-picker-column-internal:first-of-type){text-align:start}:host ::slotted(ion-picker-column-internal:last-of-type){text-align:end}:host ::slotted(ion-picker-column-internal:only-child){text-align:center}:host .picker-before{background:-webkit-gradient(linear, left top, left bottom, color-stop(20%, rgba(var(--wheel-fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 1)), color-stop(90%, rgba(var(--wheel-fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 0)));background:linear-gradient(to bottom, rgba(var(--wheel-fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 1) 20%, rgba(var(--wheel-fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 0) 90%)}:host .picker-after{background:-webkit-gradient(linear, left bottom, left top, color-stop(30%, rgba(var(--wheel-fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 1)), color-stop(90%, rgba(var(--wheel-fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 0)));background:linear-gradient(to top, rgba(var(--wheel-fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 1) 30%, rgba(var(--wheel-fade-background-rgb, var(--background-rgb, var(--ion-background-color-rgb, 255, 255, 255))), 0) 90%)}\";\nconst IonPickerInternalMdStyle0 = pickerInternalMdCss;\nconst PickerInternal = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionInputModeChange = createEvent(this, \"ionInputModeChange\", 7);\n    this.useInputMode = false;\n    this.isInHighlightBounds = ev => {\n      const {\n        highlightEl\n      } = this;\n      if (!highlightEl) {\n        return false;\n      }\n      const bbox = highlightEl.getBoundingClientRect();\n      /**\n       * Check to see if the user clicked\n       * outside the bounds of the highlight.\n       */\n      const outsideX = ev.clientX < bbox.left || ev.clientX > bbox.right;\n      const outsideY = ev.clientY < bbox.top || ev.clientY > bbox.bottom;\n      if (outsideX || outsideY) {\n        return false;\n      }\n      return true;\n    };\n    /**\n     * If we are no longer focused\n     * on a picker column, then we should\n     * exit input mode. An exception is made\n     * for the input in the picker since having\n     * that focused means we are still in input mode.\n     */\n    this.onFocusOut = ev => {\n      // TODO(FW-2832): type\n      const {\n        relatedTarget\n      } = ev;\n      if (!relatedTarget || relatedTarget.tagName !== 'ION-PICKER-COLUMN-INTERNAL' && relatedTarget !== this.inputEl) {\n        this.exitInputMode();\n      }\n    };\n    /**\n     * When picker columns receive focus\n     * the parent picker needs to determine\n     * whether to enter/exit input mode.\n     */\n    this.onFocusIn = ev => {\n      // TODO(FW-2832): type\n      const {\n        target\n      } = ev;\n      /**\n       * Due to browser differences in how/when focus\n       * is dispatched on certain elements, we need to\n       * make sure that this function only ever runs when\n       * focusing a picker column.\n       */\n      if (target.tagName !== 'ION-PICKER-COLUMN-INTERNAL') {\n        return;\n      }\n      /**\n       * If we have actionOnClick\n       * then this means the user focused\n       * a picker column via mouse or\n       * touch (i.e. a PointerEvent). As a result,\n       * we should not enter/exit input mode\n       * until the click event has fired, which happens\n       * after the `focusin` event.\n       *\n       * Otherwise, the user likely focused\n       * the column using their keyboard and\n       * we should enter/exit input mode automatically.\n       */\n      if (!this.actionOnClick) {\n        const columnEl = target;\n        const allowInput = columnEl.numericInput;\n        if (allowInput) {\n          this.enterInputMode(columnEl, false);\n        } else {\n          this.exitInputMode();\n        }\n      }\n    };\n    /**\n     * On click we need to run an actionOnClick\n     * function that has been set in onPointerDown\n     * so that we enter/exit input mode correctly.\n     */\n    this.onClick = () => {\n      const {\n        actionOnClick\n      } = this;\n      if (actionOnClick) {\n        actionOnClick();\n        this.actionOnClick = undefined;\n      }\n    };\n    /**\n     * Clicking a column also focuses the column on\n     * certain browsers, so we use onPointerDown\n     * to tell the onFocusIn function that users\n     * are trying to click the column rather than\n     * focus the column using the keyboard. When the\n     * user completes the click, the onClick function\n     * runs and runs the actionOnClick callback.\n     */\n    this.onPointerDown = ev => {\n      const {\n        useInputMode,\n        inputModeColumn,\n        el\n      } = this;\n      if (this.isInHighlightBounds(ev)) {\n        /**\n         * If we were already in\n         * input mode, then we should determine\n         * if we tapped a particular column and\n         * should switch to input mode for\n         * that specific column.\n         */\n        if (useInputMode) {\n          /**\n           * If we tapped a picker column\n           * then we should either switch to input\n           * mode for that column or all columns.\n           * Otherwise we should exit input mode\n           * since we just tapped the highlight and\n           * not a column.\n           */\n          if (ev.target.tagName === 'ION-PICKER-COLUMN-INTERNAL') {\n            /**\n             * If user taps 2 different columns\n             * then we should just switch to input mode\n             * for the new column rather than switching to\n             * input mode for all columns.\n             */\n            if (inputModeColumn && inputModeColumn === ev.target) {\n              this.actionOnClick = () => {\n                this.enterInputMode();\n              };\n            } else {\n              this.actionOnClick = () => {\n                this.enterInputMode(ev.target);\n              };\n            }\n          } else {\n            this.actionOnClick = () => {\n              this.exitInputMode();\n            };\n          }\n          /**\n           * If we were not already in\n           * input mode, then we should\n           * enter input mode for all columns.\n           */\n        } else {\n          /**\n           * If there is only 1 numeric input column\n           * then we should skip multi column input.\n           */\n          const columns = el.querySelectorAll('ion-picker-column-internal.picker-column-numeric-input');\n          const columnEl = columns.length === 1 ? ev.target : undefined;\n          this.actionOnClick = () => {\n            this.enterInputMode(columnEl);\n          };\n        }\n        return;\n      }\n      this.actionOnClick = () => {\n        this.exitInputMode();\n      };\n    };\n    /**\n     * Enters input mode to allow\n     * for text entry of numeric values.\n     * If on mobile, we focus a hidden input\n     * field so that the on screen keyboard\n     * is brought up. When tabbing using a\n     * keyboard, picker columns receive an outline\n     * to indicate they are focused. As a result,\n     * we should not focus the hidden input as it\n     * would cause the outline to go away, preventing\n     * users from having any visual indication of which\n     * column is focused.\n     */\n    this.enterInputMode = (columnEl, focusInput = true) => {\n      const {\n        inputEl,\n        el\n      } = this;\n      if (!inputEl) {\n        return;\n      }\n      /**\n       * Only active input mode if there is at\n       * least one column that accepts numeric input.\n       */\n      const hasInputColumn = el.querySelector('ion-picker-column-internal.picker-column-numeric-input');\n      if (!hasInputColumn) {\n        return;\n      }\n      /**\n       * If columnEl is undefined then\n       * it is assumed that all numeric pickers\n       * are eligible for text entry.\n       * (i.e. hour and minute columns)\n       */\n      this.useInputMode = true;\n      this.inputModeColumn = columnEl;\n      /**\n       * Users with a keyboard and mouse can\n       * activate input mode where the input is\n       * focused as well as when it is not focused,\n       * so we need to make sure we clean up any\n       * old listeners.\n       */\n      if (focusInput) {\n        if (this.destroyKeypressListener) {\n          this.destroyKeypressListener();\n          this.destroyKeypressListener = undefined;\n        }\n        inputEl.focus();\n      } else {\n        // TODO FW-5900 Use keydown instead\n        el.addEventListener('keypress', this.onKeyPress);\n        this.destroyKeypressListener = () => {\n          el.removeEventListener('keypress', this.onKeyPress);\n        };\n      }\n      this.emitInputModeChange();\n    };\n    this.onKeyPress = ev => {\n      const {\n        inputEl\n      } = this;\n      if (!inputEl) {\n        return;\n      }\n      const parsedValue = parseInt(ev.key, 10);\n      /**\n       * Only numbers should be allowed\n       */\n      if (!Number.isNaN(parsedValue)) {\n        inputEl.value += ev.key;\n        this.onInputChange();\n      }\n    };\n    this.selectSingleColumn = () => {\n      const {\n        inputEl,\n        inputModeColumn,\n        singleColumnSearchTimeout\n      } = this;\n      if (!inputEl || !inputModeColumn) {\n        return;\n      }\n      const values = inputModeColumn.items.filter(item => item.disabled !== true);\n      /**\n       * If users pause for a bit, the search\n       * value should be reset similar to how a\n       * <select> behaves. So typing \"34\", waiting,\n       * then typing \"5\" should select \"05\".\n       */\n      if (singleColumnSearchTimeout) {\n        clearTimeout(singleColumnSearchTimeout);\n      }\n      this.singleColumnSearchTimeout = setTimeout(() => {\n        inputEl.value = '';\n        this.singleColumnSearchTimeout = undefined;\n      }, 1000);\n      /**\n       * For values that are longer than 2 digits long\n       * we should shift the value over 1 character\n       * to the left. So typing \"456\" would result in \"56\".\n       * TODO: If we want to support more than just\n       * time entry, we should update this value to be\n       * the max length of all of the picker items.\n       */\n      if (inputEl.value.length >= 3) {\n        const startIndex = inputEl.value.length - 2;\n        const newString = inputEl.value.substring(startIndex);\n        inputEl.value = newString;\n        this.selectSingleColumn();\n        return;\n      }\n      /**\n       * Checking the value of the input gets priority\n       * first. For example, if the value of the input\n       * is \"1\" and we entered \"2\", then the complete value\n       * is \"12\" and we should select hour 12.\n       *\n       * Regex removes any leading zeros from values like \"02\",\n       * but it keeps a single zero if there are only zeros in the string.\n       * 0+(?=[1-9]) --> Match 1 or more zeros that are followed by 1-9\n       * 0+(?=0$) --> Match 1 or more zeros that must be followed by one 0 and end.\n       */\n      const findItemFromCompleteValue = values.find(({\n        text\n      }) => {\n        const parsedText = text.replace(/^0+(?=[1-9])|0+(?=0$)/, '');\n        return parsedText === inputEl.value;\n      });\n      if (findItemFromCompleteValue) {\n        inputModeColumn.setValue(findItemFromCompleteValue.value);\n        return;\n      }\n      /**\n       * If we typed \"56\" to get minute 56, then typed \"7\",\n       * we should select \"07\" as \"567\" is not a valid minute.\n       */\n      if (inputEl.value.length === 2) {\n        const changedCharacter = inputEl.value.substring(inputEl.value.length - 1);\n        inputEl.value = changedCharacter;\n        this.selectSingleColumn();\n      }\n    };\n    /**\n     * Searches a list of column items for a particular\n     * value. This is currently used for numeric values.\n     * The zeroBehavior can be set to account for leading\n     * or trailing zeros when looking at the item text.\n     */\n    this.searchColumn = (colEl, value, zeroBehavior = 'start') => {\n      const behavior = zeroBehavior === 'start' ? /^0+/ : /0$/;\n      const item = colEl.items.find(({\n        text,\n        disabled\n      }) => disabled !== true && text.replace(behavior, '') === value);\n      if (item) {\n        colEl.setValue(item.value);\n      }\n    };\n    this.selectMultiColumn = () => {\n      const {\n        inputEl,\n        el\n      } = this;\n      if (!inputEl) {\n        return;\n      }\n      const numericPickers = Array.from(el.querySelectorAll('ion-picker-column-internal')).filter(col => col.numericInput);\n      const firstColumn = numericPickers[0];\n      const lastColumn = numericPickers[1];\n      let value = inputEl.value;\n      let minuteValue;\n      switch (value.length) {\n        case 1:\n          this.searchColumn(firstColumn, value);\n          break;\n        case 2:\n          /**\n           * If the first character is `0` or `1` it is\n           * possible that users are trying to type `09`\n           * or `11` into the hour field, so we should look\n           * at that first.\n           */\n          const firstCharacter = inputEl.value.substring(0, 1);\n          value = firstCharacter === '0' || firstCharacter === '1' ? inputEl.value : firstCharacter;\n          this.searchColumn(firstColumn, value);\n          /**\n           * If only checked the first value,\n           * we can check the second value\n           * for a match in the minutes column\n           */\n          if (value.length === 1) {\n            minuteValue = inputEl.value.substring(inputEl.value.length - 1);\n            this.searchColumn(lastColumn, minuteValue, 'end');\n          }\n          break;\n        case 3:\n          /**\n           * If the first character is `0` or `1` it is\n           * possible that users are trying to type `09`\n           * or `11` into the hour field, so we should look\n           * at that first.\n           */\n          const firstCharacterAgain = inputEl.value.substring(0, 1);\n          value = firstCharacterAgain === '0' || firstCharacterAgain === '1' ? inputEl.value.substring(0, 2) : firstCharacterAgain;\n          this.searchColumn(firstColumn, value);\n          /**\n           * If only checked the first value,\n           * we can check the second value\n           * for a match in the minutes column\n           */\n          minuteValue = value.length === 1 ? inputEl.value.substring(1) : inputEl.value.substring(2);\n          this.searchColumn(lastColumn, minuteValue, 'end');\n          break;\n        case 4:\n          /**\n           * If the first character is `0` or `1` it is\n           * possible that users are trying to type `09`\n           * or `11` into the hour field, so we should look\n           * at that first.\n           */\n          const firstCharacterAgainAgain = inputEl.value.substring(0, 1);\n          value = firstCharacterAgainAgain === '0' || firstCharacterAgainAgain === '1' ? inputEl.value.substring(0, 2) : firstCharacterAgainAgain;\n          this.searchColumn(firstColumn, value);\n          /**\n           * If only checked the first value,\n           * we can check the second value\n           * for a match in the minutes column\n           */\n          const minuteValueAgain = value.length === 1 ? inputEl.value.substring(1, inputEl.value.length) : inputEl.value.substring(2, inputEl.value.length);\n          this.searchColumn(lastColumn, minuteValueAgain, 'end');\n          break;\n        default:\n          const startIndex = inputEl.value.length - 4;\n          const newString = inputEl.value.substring(startIndex);\n          inputEl.value = newString;\n          this.selectMultiColumn();\n          break;\n      }\n    };\n    /**\n     * Searches the value of the active column\n     * to determine which value users are trying\n     * to select\n     */\n    this.onInputChange = () => {\n      const {\n        useInputMode,\n        inputEl,\n        inputModeColumn\n      } = this;\n      if (!useInputMode || !inputEl) {\n        return;\n      }\n      if (inputModeColumn) {\n        this.selectSingleColumn();\n      } else {\n        this.selectMultiColumn();\n      }\n    };\n    /**\n     * Emit ionInputModeChange. Picker columns\n     * listen for this event to determine whether\n     * or not their column is \"active\" for text input.\n     */\n    this.emitInputModeChange = () => {\n      const {\n        useInputMode,\n        inputModeColumn\n      } = this;\n      this.ionInputModeChange.emit({\n        useInputMode,\n        inputModeColumn\n      });\n    };\n  }\n  /**\n   * When the picker is interacted with\n   * we need to prevent touchstart so other\n   * gestures do not fire. For example,\n   * scrolling on the wheel picker\n   * in ion-datetime should not cause\n   * a card modal to swipe to close.\n   */\n  preventTouchStartPropagation(ev) {\n    ev.stopPropagation();\n  }\n  componentWillLoad() {\n    getElementRoot(this.el).addEventListener('focusin', this.onFocusIn);\n    getElementRoot(this.el).addEventListener('focusout', this.onFocusOut);\n  }\n  /**\n   * @internal\n   * Exits text entry mode for the picker\n   * This method blurs the hidden input\n   * and cause the keyboard to dismiss.\n   */\n  exitInputMode() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const {\n        inputEl,\n        useInputMode\n      } = _this;\n      if (!useInputMode || !inputEl) {\n        return;\n      }\n      _this.useInputMode = false;\n      _this.inputModeColumn = undefined;\n      inputEl.blur();\n      inputEl.value = '';\n      if (_this.destroyKeypressListener) {\n        _this.destroyKeypressListener();\n        _this.destroyKeypressListener = undefined;\n      }\n      _this.emitInputModeChange();\n    })();\n  }\n  render() {\n    return h(Host, {\n      key: '01cbd466787242ad070b01909714089570b4d67f',\n      onPointerDown: ev => this.onPointerDown(ev),\n      onClick: () => this.onClick()\n    }, h(\"input\", {\n      key: '7ff8c0a74c107610a6f0dd9fbc2fc7a4a6dc2468',\n      \"aria-hidden\": \"true\",\n      tabindex: -1,\n      inputmode: \"numeric\",\n      type: \"number\",\n      onKeyDown: ev => {\n        var _a;\n        /**\n         * The \"Enter\" key represents\n         * the user submitting their time\n         * selection, so we should blur the\n         * input (and therefore close the keyboard)\n         *\n         * Updating the picker's state to no longer\n         * be in input mode is handled in the onBlur\n         * callback below.\n         */\n        if (ev.key === 'Enter') {\n          (_a = this.inputEl) === null || _a === void 0 ? void 0 : _a.blur();\n        }\n      },\n      ref: el => this.inputEl = el,\n      onInput: () => this.onInputChange(),\n      onBlur: () => this.exitInputMode()\n    }), h(\"div\", {\n      key: '4700c9d877f54ae8f3fb173122193c27637f70a4',\n      class: \"picker-before\"\n    }), h(\"div\", {\n      key: '7ceae834b15d559f3819ec2116f83669cf6665fc',\n      class: \"picker-after\"\n    }), h(\"div\", {\n      key: '2d3bfda76279c2ee14edc067c53651be23b8b525',\n      class: \"picker-highlight\",\n      ref: el => this.highlightEl = el\n    }), h(\"slot\", {\n      key: '4797def7a3882a8a911ad47949b76f58a9f448d1'\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nPickerInternal.style = {\n  ios: IonPickerInternalIosStyle0,\n  md: IonPickerInternalMdStyle0\n};\nexport { PickerInternal as ion_picker_internal };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}