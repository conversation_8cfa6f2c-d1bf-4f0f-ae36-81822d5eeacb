{"ast": null, "code": "import { BehaviorSubject, fromEvent } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nexport class MobileOptimizationService {\n  constructor() {\n    this.deviceInfo$ = new BehaviorSubject(this.getDeviceInfo());\n    this.viewportBreakpoints$ = new BehaviorSubject(this.getViewportBreakpoints());\n    this.isKeyboardOpen$ = new BehaviorSubject(false);\n    this.initializeListeners();\n  }\n  // Device Information\n  getDeviceInfo() {\n    const userAgent = navigator.userAgent;\n    const screenWidth = window.innerWidth;\n    const screenHeight = window.innerHeight;\n    return {\n      isMobile: this.isMobileDevice(),\n      isTablet: this.isTabletDevice(),\n      isDesktop: this.isDesktopDevice(),\n      screenWidth,\n      screenHeight,\n      orientation: screenWidth > screenHeight ? 'landscape' : 'portrait',\n      devicePixelRatio: window.devicePixelRatio || 1,\n      touchSupport: 'ontouchstart' in window || navigator.maxTouchPoints > 0,\n      platform: this.getPlatform(),\n      userAgent\n    };\n  }\n  // Viewport Breakpoints\n  getViewportBreakpoints() {\n    const width = window.innerWidth;\n    return {\n      xs: width < 576,\n      sm: width >= 576 && width < 768,\n      md: width >= 768 && width < 992,\n      lg: width >= 992 && width < 1200,\n      xl: width >= 1200 && width < 1400,\n      xxl: width >= 1400\n    };\n  }\n  // Observable Streams\n  getDeviceInfo$() {\n    return this.deviceInfo$.asObservable();\n  }\n  getViewportBreakpoints$() {\n    return this.viewportBreakpoints$.asObservable();\n  }\n  getIsKeyboardOpen$() {\n    return this.isKeyboardOpen$.asObservable();\n  }\n  // Device Detection Methods\n  isMobileDevice() {\n    const userAgent = navigator.userAgent;\n    const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;\n    return mobileRegex.test(userAgent) || window.innerWidth <= 768;\n  }\n  isTabletDevice() {\n    const userAgent = navigator.userAgent;\n    const tabletRegex = /iPad|Android(?!.*Mobile)/i;\n    return tabletRegex.test(userAgent) || window.innerWidth > 768 && window.innerWidth <= 1024;\n  }\n  isDesktopDevice() {\n    return !this.isMobileDevice() && !this.isTabletDevice();\n  }\n  getPlatform() {\n    const userAgent = navigator.userAgent;\n    if (/iPhone|iPad|iPod/i.test(userAgent)) return 'iOS';\n    if (/Android/i.test(userAgent)) return 'Android';\n    if (/Windows/i.test(userAgent)) return 'Windows';\n    if (/Mac/i.test(userAgent)) return 'macOS';\n    if (/Linux/i.test(userAgent)) return 'Linux';\n    return 'Unknown';\n  }\n  // Touch and Gesture Support\n  isTouchDevice() {\n    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;\n  }\n  supportsHover() {\n    return window.matchMedia('(hover: hover)').matches;\n  }\n  // Viewport Utilities\n  getViewportWidth() {\n    return window.innerWidth;\n  }\n  getViewportHeight() {\n    return window.innerHeight;\n  }\n  getScrollbarWidth() {\n    const outer = document.createElement('div');\n    outer.style.visibility = 'hidden';\n    outer.style.overflow = 'scroll';\n    outer.style.msOverflowStyle = 'scrollbar';\n    document.body.appendChild(outer);\n    const inner = document.createElement('div');\n    outer.appendChild(inner);\n    const scrollbarWidth = outer.offsetWidth - inner.offsetWidth;\n    outer.parentNode?.removeChild(outer);\n    return scrollbarWidth;\n  }\n  // Safe Area Support (for iOS notch)\n  getSafeAreaInsets() {\n    const style = getComputedStyle(document.documentElement);\n    return {\n      top: parseInt(style.getPropertyValue('--sat') || '0', 10),\n      right: parseInt(style.getPropertyValue('--sar') || '0', 10),\n      bottom: parseInt(style.getPropertyValue('--sab') || '0', 10),\n      left: parseInt(style.getPropertyValue('--sal') || '0', 10)\n    };\n  }\n  // Performance Optimization\n  enableGPUAcceleration(element) {\n    element.style.transform = 'translateZ(0)';\n    element.style.willChange = 'transform';\n  }\n  disableGPUAcceleration(element) {\n    element.style.transform = '';\n    element.style.willChange = '';\n  }\n  // Scroll Management\n  disableBodyScroll() {\n    document.body.style.overflow = 'hidden';\n    document.body.style.position = 'fixed';\n    document.body.style.width = '100%';\n  }\n  enableBodyScroll() {\n    document.body.style.overflow = '';\n    document.body.style.position = '';\n    document.body.style.width = '';\n  }\n  // Touch Event Helpers\n  getTouchCoordinates(event) {\n    const touch = event.touches[0] || event.changedTouches[0];\n    return {\n      x: touch.clientX,\n      y: touch.clientY\n    };\n  }\n  // Responsive Image Loading\n  getOptimalImageSize(containerWidth) {\n    const devicePixelRatio = window.devicePixelRatio || 1;\n    const targetWidth = containerWidth * devicePixelRatio;\n    if (targetWidth <= 400) return 'w=400';\n    if (targetWidth <= 800) return 'w=800';\n    if (targetWidth <= 1200) return 'w=1200';\n    if (targetWidth <= 1600) return 'w=1600';\n    return 'w=2000';\n  }\n  // Keyboard Detection (for mobile)\n  detectKeyboard() {\n    const initialViewportHeight = window.innerHeight;\n    fromEvent(window, 'resize').pipe(debounceTime(100), map(() => window.innerHeight), distinctUntilChanged()).subscribe(currentHeight => {\n      const heightDifference = initialViewportHeight - currentHeight;\n      const isKeyboardOpen = heightDifference > 150; // Threshold for keyboard detection\n      this.isKeyboardOpen$.next(isKeyboardOpen);\n    });\n  }\n  // Initialize Event Listeners\n  initializeListeners() {\n    // Resize listener\n    fromEvent(window, 'resize').pipe(debounceTime(250)).subscribe(() => {\n      this.deviceInfo$.next(this.getDeviceInfo());\n      this.viewportBreakpoints$.next(this.getViewportBreakpoints());\n    });\n    // Orientation change listener\n    fromEvent(window, 'orientationchange').pipe(debounceTime(500)).subscribe(() => {\n      this.deviceInfo$.next(this.getDeviceInfo());\n      this.viewportBreakpoints$.next(this.getViewportBreakpoints());\n    });\n    // Keyboard detection for mobile\n    if (this.isMobileDevice()) {\n      this.detectKeyboard();\n    }\n    // Add CSS custom properties for safe area\n    this.updateSafeAreaProperties();\n  }\n  // Update CSS Custom Properties for Safe Area\n  updateSafeAreaProperties() {\n    const root = document.documentElement;\n    // Set safe area inset properties\n    root.style.setProperty('--sat', 'env(safe-area-inset-top)');\n    root.style.setProperty('--sar', 'env(safe-area-inset-right)');\n    root.style.setProperty('--sab', 'env(safe-area-inset-bottom)');\n    root.style.setProperty('--sal', 'env(safe-area-inset-left)');\n  }\n  // Utility Methods\n  isCurrentBreakpoint(breakpoint) {\n    return this.viewportBreakpoints$.value[breakpoint];\n  }\n  getCurrentBreakpoint() {\n    const breakpoints = this.viewportBreakpoints$.value;\n    if (breakpoints.xxl) return 'xxl';\n    if (breakpoints.xl) return 'xl';\n    if (breakpoints.lg) return 'lg';\n    if (breakpoints.md) return 'md';\n    if (breakpoints.sm) return 'sm';\n    return 'xs';\n  }\n  // Performance Monitoring\n  measurePerformance(name, fn) {\n    const start = performance.now();\n    fn();\n    const end = performance.now();\n    const duration = end - start;\n    console.log(`${name} took ${duration.toFixed(2)} milliseconds`);\n    return duration;\n  }\n  // Memory Management\n  cleanupEventListeners() {\n    // This would be called in component ngOnDestroy\n    // Implementation depends on specific use case\n  }\n  static {\n    this.ɵfac = function MobileOptimizationService_Factory(t) {\n      return new (t || MobileOptimizationService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MobileOptimizationService,\n      factory: MobileOptimizationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "fromEvent", "debounceTime", "distinctUntilChanged", "map", "MobileOptimizationService", "constructor", "deviceInfo$", "getDeviceInfo", "viewportBreakpoints$", "getViewportBreakpoints", "isKeyboardOpen$", "initializeListeners", "userAgent", "navigator", "screenWidth", "window", "innerWidth", "screenHeight", "innerHeight", "isMobile", "isMobileDevice", "isTablet", "isTabletDevice", "isDesktop", "isDesktopDevice", "orientation", "devicePixelRatio", "touchSupport", "maxTouchPoints", "platform", "getPlatform", "width", "xs", "sm", "md", "lg", "xl", "xxl", "getDeviceInfo$", "asObservable", "getViewportBreakpoints$", "getIsKeyboardOpen$", "mobileRegex", "test", "tabletRegex", "isTouchDevice", "supportsHover", "matchMedia", "matches", "getViewportWidth", "getViewportHeight", "getScrollbarWidth", "outer", "document", "createElement", "style", "visibility", "overflow", "msOverflowStyle", "body", "append<PERSON><PERSON><PERSON>", "inner", "scrollbarWidth", "offsetWidth", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "getSafeAreaInsets", "getComputedStyle", "documentElement", "top", "parseInt", "getPropertyValue", "right", "bottom", "left", "enableGPUAcceleration", "element", "transform", "<PERSON><PERSON><PERSON><PERSON>", "disableGPUAcceleration", "disableBodyScroll", "position", "enableBodyScroll", "getTouchCoordinates", "event", "touch", "touches", "changedTouches", "x", "clientX", "y", "clientY", "getOptimalImageSize", "containerWidth", "targetWidth", "detectKeyboard", "initialViewportHeight", "pipe", "subscribe", "currentHeight", "heightDifference", "isKeyboardOpen", "next", "updateSafeAreaProperties", "root", "setProperty", "isCurrentBreakpoint", "breakpoint", "value", "getCurrentBreakpoint", "breakpoints", "measurePerformance", "name", "fn", "start", "performance", "now", "end", "duration", "console", "log", "toFixed", "cleanupEventListeners", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\core\\services\\mobile-optimization.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject, fromEvent, Observable } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, map } from 'rxjs/operators';\n\nexport interface DeviceInfo {\n  isMobile: boolean;\n  isTablet: boolean;\n  isDesktop: boolean;\n  screenWidth: number;\n  screenHeight: number;\n  orientation: 'portrait' | 'landscape';\n  devicePixelRatio: number;\n  touchSupport: boolean;\n  platform: string;\n  userAgent: string;\n}\n\nexport interface ViewportBreakpoints {\n  xs: boolean; // < 576px\n  sm: boolean; // >= 576px\n  md: boolean; // >= 768px\n  lg: boolean; // >= 992px\n  xl: boolean; // >= 1200px\n  xxl: boolean; // >= 1400px\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class MobileOptimizationService {\n  private deviceInfo$ = new BehaviorSubject<DeviceInfo>(this.getDeviceInfo());\n  private viewportBreakpoints$ = new BehaviorSubject<ViewportBreakpoints>(this.getViewportBreakpoints());\n  private isKeyboardOpen$ = new BehaviorSubject<boolean>(false);\n\n  constructor() {\n    this.initializeListeners();\n  }\n\n  // Device Information\n  getDeviceInfo(): DeviceInfo {\n    const userAgent = navigator.userAgent;\n    const screenWidth = window.innerWidth;\n    const screenHeight = window.innerHeight;\n    \n    return {\n      isMobile: this.isMobileDevice(),\n      isTablet: this.isTabletDevice(),\n      isDesktop: this.isDesktopDevice(),\n      screenWidth,\n      screenHeight,\n      orientation: screenWidth > screenHeight ? 'landscape' : 'portrait',\n      devicePixelRatio: window.devicePixelRatio || 1,\n      touchSupport: 'ontouchstart' in window || navigator.maxTouchPoints > 0,\n      platform: this.getPlatform(),\n      userAgent\n    };\n  }\n\n  // Viewport Breakpoints\n  getViewportBreakpoints(): ViewportBreakpoints {\n    const width = window.innerWidth;\n    \n    return {\n      xs: width < 576,\n      sm: width >= 576 && width < 768,\n      md: width >= 768 && width < 992,\n      lg: width >= 992 && width < 1200,\n      xl: width >= 1200 && width < 1400,\n      xxl: width >= 1400\n    };\n  }\n\n  // Observable Streams\n  getDeviceInfo$(): Observable<DeviceInfo> {\n    return this.deviceInfo$.asObservable();\n  }\n\n  getViewportBreakpoints$(): Observable<ViewportBreakpoints> {\n    return this.viewportBreakpoints$.asObservable();\n  }\n\n  getIsKeyboardOpen$(): Observable<boolean> {\n    return this.isKeyboardOpen$.asObservable();\n  }\n\n  // Device Detection Methods\n  private isMobileDevice(): boolean {\n    const userAgent = navigator.userAgent;\n    const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;\n    return mobileRegex.test(userAgent) || window.innerWidth <= 768;\n  }\n\n  private isTabletDevice(): boolean {\n    const userAgent = navigator.userAgent;\n    const tabletRegex = /iPad|Android(?!.*Mobile)/i;\n    return tabletRegex.test(userAgent) || (window.innerWidth > 768 && window.innerWidth <= 1024);\n  }\n\n  private isDesktopDevice(): boolean {\n    return !this.isMobileDevice() && !this.isTabletDevice();\n  }\n\n  private getPlatform(): string {\n    const userAgent = navigator.userAgent;\n    \n    if (/iPhone|iPad|iPod/i.test(userAgent)) return 'iOS';\n    if (/Android/i.test(userAgent)) return 'Android';\n    if (/Windows/i.test(userAgent)) return 'Windows';\n    if (/Mac/i.test(userAgent)) return 'macOS';\n    if (/Linux/i.test(userAgent)) return 'Linux';\n    \n    return 'Unknown';\n  }\n\n  // Touch and Gesture Support\n  isTouchDevice(): boolean {\n    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;\n  }\n\n  supportsHover(): boolean {\n    return window.matchMedia('(hover: hover)').matches;\n  }\n\n  // Viewport Utilities\n  getViewportWidth(): number {\n    return window.innerWidth;\n  }\n\n  getViewportHeight(): number {\n    return window.innerHeight;\n  }\n\n  getScrollbarWidth(): number {\n    const outer = document.createElement('div');\n    outer.style.visibility = 'hidden';\n    outer.style.overflow = 'scroll';\n    (outer.style as any).msOverflowStyle = 'scrollbar';\n    document.body.appendChild(outer);\n\n    const inner = document.createElement('div');\n    outer.appendChild(inner);\n\n    const scrollbarWidth = outer.offsetWidth - inner.offsetWidth;\n    outer.parentNode?.removeChild(outer);\n\n    return scrollbarWidth;\n  }\n\n  // Safe Area Support (for iOS notch)\n  getSafeAreaInsets(): { top: number; right: number; bottom: number; left: number } {\n    const style = getComputedStyle(document.documentElement);\n    \n    return {\n      top: parseInt(style.getPropertyValue('--sat') || '0', 10),\n      right: parseInt(style.getPropertyValue('--sar') || '0', 10),\n      bottom: parseInt(style.getPropertyValue('--sab') || '0', 10),\n      left: parseInt(style.getPropertyValue('--sal') || '0', 10)\n    };\n  }\n\n  // Performance Optimization\n  enableGPUAcceleration(element: HTMLElement): void {\n    element.style.transform = 'translateZ(0)';\n    element.style.willChange = 'transform';\n  }\n\n  disableGPUAcceleration(element: HTMLElement): void {\n    element.style.transform = '';\n    element.style.willChange = '';\n  }\n\n  // Scroll Management\n  disableBodyScroll(): void {\n    document.body.style.overflow = 'hidden';\n    document.body.style.position = 'fixed';\n    document.body.style.width = '100%';\n  }\n\n  enableBodyScroll(): void {\n    document.body.style.overflow = '';\n    document.body.style.position = '';\n    document.body.style.width = '';\n  }\n\n  // Touch Event Helpers\n  getTouchCoordinates(event: TouchEvent): { x: number; y: number } {\n    const touch = event.touches[0] || event.changedTouches[0];\n    return {\n      x: touch.clientX,\n      y: touch.clientY\n    };\n  }\n\n  // Responsive Image Loading\n  getOptimalImageSize(containerWidth: number): string {\n    const devicePixelRatio = window.devicePixelRatio || 1;\n    const targetWidth = containerWidth * devicePixelRatio;\n    \n    if (targetWidth <= 400) return 'w=400';\n    if (targetWidth <= 800) return 'w=800';\n    if (targetWidth <= 1200) return 'w=1200';\n    if (targetWidth <= 1600) return 'w=1600';\n    return 'w=2000';\n  }\n\n  // Keyboard Detection (for mobile)\n  private detectKeyboard(): void {\n    const initialViewportHeight = window.innerHeight;\n    \n    fromEvent(window, 'resize')\n      .pipe(\n        debounceTime(100),\n        map(() => window.innerHeight),\n        distinctUntilChanged()\n      )\n      .subscribe(currentHeight => {\n        const heightDifference = initialViewportHeight - currentHeight;\n        const isKeyboardOpen = heightDifference > 150; // Threshold for keyboard detection\n        this.isKeyboardOpen$.next(isKeyboardOpen);\n      });\n  }\n\n  // Initialize Event Listeners\n  private initializeListeners(): void {\n    // Resize listener\n    fromEvent(window, 'resize')\n      .pipe(debounceTime(250))\n      .subscribe(() => {\n        this.deviceInfo$.next(this.getDeviceInfo());\n        this.viewportBreakpoints$.next(this.getViewportBreakpoints());\n      });\n\n    // Orientation change listener\n    fromEvent(window, 'orientationchange')\n      .pipe(debounceTime(500))\n      .subscribe(() => {\n        this.deviceInfo$.next(this.getDeviceInfo());\n        this.viewportBreakpoints$.next(this.getViewportBreakpoints());\n      });\n\n    // Keyboard detection for mobile\n    if (this.isMobileDevice()) {\n      this.detectKeyboard();\n    }\n\n    // Add CSS custom properties for safe area\n    this.updateSafeAreaProperties();\n  }\n\n  // Update CSS Custom Properties for Safe Area\n  private updateSafeAreaProperties(): void {\n    const root = document.documentElement;\n    \n    // Set safe area inset properties\n    root.style.setProperty('--sat', 'env(safe-area-inset-top)');\n    root.style.setProperty('--sar', 'env(safe-area-inset-right)');\n    root.style.setProperty('--sab', 'env(safe-area-inset-bottom)');\n    root.style.setProperty('--sal', 'env(safe-area-inset-left)');\n  }\n\n  // Utility Methods\n  isCurrentBreakpoint(breakpoint: keyof ViewportBreakpoints): boolean {\n    return this.viewportBreakpoints$.value[breakpoint];\n  }\n\n  getCurrentBreakpoint(): string {\n    const breakpoints = this.viewportBreakpoints$.value;\n    \n    if (breakpoints.xxl) return 'xxl';\n    if (breakpoints.xl) return 'xl';\n    if (breakpoints.lg) return 'lg';\n    if (breakpoints.md) return 'md';\n    if (breakpoints.sm) return 'sm';\n    return 'xs';\n  }\n\n  // Performance Monitoring\n  measurePerformance(name: string, fn: () => void): number {\n    const start = performance.now();\n    fn();\n    const end = performance.now();\n    const duration = end - start;\n    \n    console.log(`${name} took ${duration.toFixed(2)} milliseconds`);\n    return duration;\n  }\n\n  // Memory Management\n  cleanupEventListeners(): void {\n    // This would be called in component ngOnDestroy\n    // Implementation depends on specific use case\n  }\n}\n"], "mappings": "AACA,SAASA,eAAe,EAAEC,SAAS,QAAoB,MAAM;AAC7D,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,GAAG,QAAQ,gBAAgB;;AA2BxE,OAAM,MAAOC,yBAAyB;EAKpCC,YAAA;IAJQ,KAAAC,WAAW,GAAG,IAAIP,eAAe,CAAa,IAAI,CAACQ,aAAa,EAAE,CAAC;IACnE,KAAAC,oBAAoB,GAAG,IAAIT,eAAe,CAAsB,IAAI,CAACU,sBAAsB,EAAE,CAAC;IAC9F,KAAAC,eAAe,GAAG,IAAIX,eAAe,CAAU,KAAK,CAAC;IAG3D,IAAI,CAACY,mBAAmB,EAAE;EAC5B;EAEA;EACAJ,aAAaA,CAAA;IACX,MAAMK,SAAS,GAAGC,SAAS,CAACD,SAAS;IACrC,MAAME,WAAW,GAAGC,MAAM,CAACC,UAAU;IACrC,MAAMC,YAAY,GAAGF,MAAM,CAACG,WAAW;IAEvC,OAAO;MACLC,QAAQ,EAAE,IAAI,CAACC,cAAc,EAAE;MAC/BC,QAAQ,EAAE,IAAI,CAACC,cAAc,EAAE;MAC/BC,SAAS,EAAE,IAAI,CAACC,eAAe,EAAE;MACjCV,WAAW;MACXG,YAAY;MACZQ,WAAW,EAAEX,WAAW,GAAGG,YAAY,GAAG,WAAW,GAAG,UAAU;MAClES,gBAAgB,EAAEX,MAAM,CAACW,gBAAgB,IAAI,CAAC;MAC9CC,YAAY,EAAE,cAAc,IAAIZ,MAAM,IAAIF,SAAS,CAACe,cAAc,GAAG,CAAC;MACtEC,QAAQ,EAAE,IAAI,CAACC,WAAW,EAAE;MAC5BlB;KACD;EACH;EAEA;EACAH,sBAAsBA,CAAA;IACpB,MAAMsB,KAAK,GAAGhB,MAAM,CAACC,UAAU;IAE/B,OAAO;MACLgB,EAAE,EAAED,KAAK,GAAG,GAAG;MACfE,EAAE,EAAEF,KAAK,IAAI,GAAG,IAAIA,KAAK,GAAG,GAAG;MAC/BG,EAAE,EAAEH,KAAK,IAAI,GAAG,IAAIA,KAAK,GAAG,GAAG;MAC/BI,EAAE,EAAEJ,KAAK,IAAI,GAAG,IAAIA,KAAK,GAAG,IAAI;MAChCK,EAAE,EAAEL,KAAK,IAAI,IAAI,IAAIA,KAAK,GAAG,IAAI;MACjCM,GAAG,EAAEN,KAAK,IAAI;KACf;EACH;EAEA;EACAO,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAChC,WAAW,CAACiC,YAAY,EAAE;EACxC;EAEAC,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAAChC,oBAAoB,CAAC+B,YAAY,EAAE;EACjD;EAEAE,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAAC/B,eAAe,CAAC6B,YAAY,EAAE;EAC5C;EAEA;EACQnB,cAAcA,CAAA;IACpB,MAAMR,SAAS,GAAGC,SAAS,CAACD,SAAS;IACrC,MAAM8B,WAAW,GAAG,gEAAgE;IACpF,OAAOA,WAAW,CAACC,IAAI,CAAC/B,SAAS,CAAC,IAAIG,MAAM,CAACC,UAAU,IAAI,GAAG;EAChE;EAEQM,cAAcA,CAAA;IACpB,MAAMV,SAAS,GAAGC,SAAS,CAACD,SAAS;IACrC,MAAMgC,WAAW,GAAG,2BAA2B;IAC/C,OAAOA,WAAW,CAACD,IAAI,CAAC/B,SAAS,CAAC,IAAKG,MAAM,CAACC,UAAU,GAAG,GAAG,IAAID,MAAM,CAACC,UAAU,IAAI,IAAK;EAC9F;EAEQQ,eAAeA,CAAA;IACrB,OAAO,CAAC,IAAI,CAACJ,cAAc,EAAE,IAAI,CAAC,IAAI,CAACE,cAAc,EAAE;EACzD;EAEQQ,WAAWA,CAAA;IACjB,MAAMlB,SAAS,GAAGC,SAAS,CAACD,SAAS;IAErC,IAAI,mBAAmB,CAAC+B,IAAI,CAAC/B,SAAS,CAAC,EAAE,OAAO,KAAK;IACrD,IAAI,UAAU,CAAC+B,IAAI,CAAC/B,SAAS,CAAC,EAAE,OAAO,SAAS;IAChD,IAAI,UAAU,CAAC+B,IAAI,CAAC/B,SAAS,CAAC,EAAE,OAAO,SAAS;IAChD,IAAI,MAAM,CAAC+B,IAAI,CAAC/B,SAAS,CAAC,EAAE,OAAO,OAAO;IAC1C,IAAI,QAAQ,CAAC+B,IAAI,CAAC/B,SAAS,CAAC,EAAE,OAAO,OAAO;IAE5C,OAAO,SAAS;EAClB;EAEA;EACAiC,aAAaA,CAAA;IACX,OAAO,cAAc,IAAI9B,MAAM,IAAIF,SAAS,CAACe,cAAc,GAAG,CAAC;EACjE;EAEAkB,aAAaA,CAAA;IACX,OAAO/B,MAAM,CAACgC,UAAU,CAAC,gBAAgB,CAAC,CAACC,OAAO;EACpD;EAEA;EACAC,gBAAgBA,CAAA;IACd,OAAOlC,MAAM,CAACC,UAAU;EAC1B;EAEAkC,iBAAiBA,CAAA;IACf,OAAOnC,MAAM,CAACG,WAAW;EAC3B;EAEAiC,iBAAiBA,CAAA;IACf,MAAMC,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC3CF,KAAK,CAACG,KAAK,CAACC,UAAU,GAAG,QAAQ;IACjCJ,KAAK,CAACG,KAAK,CAACE,QAAQ,GAAG,QAAQ;IAC9BL,KAAK,CAACG,KAAa,CAACG,eAAe,GAAG,WAAW;IAClDL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,KAAK,CAAC;IAEhC,MAAMS,KAAK,GAAGR,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC3CF,KAAK,CAACQ,WAAW,CAACC,KAAK,CAAC;IAExB,MAAMC,cAAc,GAAGV,KAAK,CAACW,WAAW,GAAGF,KAAK,CAACE,WAAW;IAC5DX,KAAK,CAACY,UAAU,EAAEC,WAAW,CAACb,KAAK,CAAC;IAEpC,OAAOU,cAAc;EACvB;EAEA;EACAI,iBAAiBA,CAAA;IACf,MAAMX,KAAK,GAAGY,gBAAgB,CAACd,QAAQ,CAACe,eAAe,CAAC;IAExD,OAAO;MACLC,GAAG,EAAEC,QAAQ,CAACf,KAAK,CAACgB,gBAAgB,CAAC,OAAO,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC;MACzDC,KAAK,EAAEF,QAAQ,CAACf,KAAK,CAACgB,gBAAgB,CAAC,OAAO,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC;MAC3DE,MAAM,EAAEH,QAAQ,CAACf,KAAK,CAACgB,gBAAgB,CAAC,OAAO,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC;MAC5DG,IAAI,EAAEJ,QAAQ,CAACf,KAAK,CAACgB,gBAAgB,CAAC,OAAO,CAAC,IAAI,GAAG,EAAE,EAAE;KAC1D;EACH;EAEA;EACAI,qBAAqBA,CAACC,OAAoB;IACxCA,OAAO,CAACrB,KAAK,CAACsB,SAAS,GAAG,eAAe;IACzCD,OAAO,CAACrB,KAAK,CAACuB,UAAU,GAAG,WAAW;EACxC;EAEAC,sBAAsBA,CAACH,OAAoB;IACzCA,OAAO,CAACrB,KAAK,CAACsB,SAAS,GAAG,EAAE;IAC5BD,OAAO,CAACrB,KAAK,CAACuB,UAAU,GAAG,EAAE;EAC/B;EAEA;EACAE,iBAAiBA,CAAA;IACf3B,QAAQ,CAACM,IAAI,CAACJ,KAAK,CAACE,QAAQ,GAAG,QAAQ;IACvCJ,QAAQ,CAACM,IAAI,CAACJ,KAAK,CAAC0B,QAAQ,GAAG,OAAO;IACtC5B,QAAQ,CAACM,IAAI,CAACJ,KAAK,CAACxB,KAAK,GAAG,MAAM;EACpC;EAEAmD,gBAAgBA,CAAA;IACd7B,QAAQ,CAACM,IAAI,CAACJ,KAAK,CAACE,QAAQ,GAAG,EAAE;IACjCJ,QAAQ,CAACM,IAAI,CAACJ,KAAK,CAAC0B,QAAQ,GAAG,EAAE;IACjC5B,QAAQ,CAACM,IAAI,CAACJ,KAAK,CAACxB,KAAK,GAAG,EAAE;EAChC;EAEA;EACAoD,mBAAmBA,CAACC,KAAiB;IACnC,MAAMC,KAAK,GAAGD,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC,IAAIF,KAAK,CAACG,cAAc,CAAC,CAAC,CAAC;IACzD,OAAO;MACLC,CAAC,EAAEH,KAAK,CAACI,OAAO;MAChBC,CAAC,EAAEL,KAAK,CAACM;KACV;EACH;EAEA;EACAC,mBAAmBA,CAACC,cAAsB;IACxC,MAAMnE,gBAAgB,GAAGX,MAAM,CAACW,gBAAgB,IAAI,CAAC;IACrD,MAAMoE,WAAW,GAAGD,cAAc,GAAGnE,gBAAgB;IAErD,IAAIoE,WAAW,IAAI,GAAG,EAAE,OAAO,OAAO;IACtC,IAAIA,WAAW,IAAI,GAAG,EAAE,OAAO,OAAO;IACtC,IAAIA,WAAW,IAAI,IAAI,EAAE,OAAO,QAAQ;IACxC,IAAIA,WAAW,IAAI,IAAI,EAAE,OAAO,QAAQ;IACxC,OAAO,QAAQ;EACjB;EAEA;EACQC,cAAcA,CAAA;IACpB,MAAMC,qBAAqB,GAAGjF,MAAM,CAACG,WAAW;IAEhDlB,SAAS,CAACe,MAAM,EAAE,QAAQ,CAAC,CACxBkF,IAAI,CACHhG,YAAY,CAAC,GAAG,CAAC,EACjBE,GAAG,CAAC,MAAMY,MAAM,CAACG,WAAW,CAAC,EAC7BhB,oBAAoB,EAAE,CACvB,CACAgG,SAAS,CAACC,aAAa,IAAG;MACzB,MAAMC,gBAAgB,GAAGJ,qBAAqB,GAAGG,aAAa;MAC9D,MAAME,cAAc,GAAGD,gBAAgB,GAAG,GAAG,CAAC,CAAC;MAC/C,IAAI,CAAC1F,eAAe,CAAC4F,IAAI,CAACD,cAAc,CAAC;IAC3C,CAAC,CAAC;EACN;EAEA;EACQ1F,mBAAmBA,CAAA;IACzB;IACAX,SAAS,CAACe,MAAM,EAAE,QAAQ,CAAC,CACxBkF,IAAI,CAAChG,YAAY,CAAC,GAAG,CAAC,CAAC,CACvBiG,SAAS,CAAC,MAAK;MACd,IAAI,CAAC5F,WAAW,CAACgG,IAAI,CAAC,IAAI,CAAC/F,aAAa,EAAE,CAAC;MAC3C,IAAI,CAACC,oBAAoB,CAAC8F,IAAI,CAAC,IAAI,CAAC7F,sBAAsB,EAAE,CAAC;IAC/D,CAAC,CAAC;IAEJ;IACAT,SAAS,CAACe,MAAM,EAAE,mBAAmB,CAAC,CACnCkF,IAAI,CAAChG,YAAY,CAAC,GAAG,CAAC,CAAC,CACvBiG,SAAS,CAAC,MAAK;MACd,IAAI,CAAC5F,WAAW,CAACgG,IAAI,CAAC,IAAI,CAAC/F,aAAa,EAAE,CAAC;MAC3C,IAAI,CAACC,oBAAoB,CAAC8F,IAAI,CAAC,IAAI,CAAC7F,sBAAsB,EAAE,CAAC;IAC/D,CAAC,CAAC;IAEJ;IACA,IAAI,IAAI,CAACW,cAAc,EAAE,EAAE;MACzB,IAAI,CAAC2E,cAAc,EAAE;;IAGvB;IACA,IAAI,CAACQ,wBAAwB,EAAE;EACjC;EAEA;EACQA,wBAAwBA,CAAA;IAC9B,MAAMC,IAAI,GAAGnD,QAAQ,CAACe,eAAe;IAErC;IACAoC,IAAI,CAACjD,KAAK,CAACkD,WAAW,CAAC,OAAO,EAAE,0BAA0B,CAAC;IAC3DD,IAAI,CAACjD,KAAK,CAACkD,WAAW,CAAC,OAAO,EAAE,4BAA4B,CAAC;IAC7DD,IAAI,CAACjD,KAAK,CAACkD,WAAW,CAAC,OAAO,EAAE,6BAA6B,CAAC;IAC9DD,IAAI,CAACjD,KAAK,CAACkD,WAAW,CAAC,OAAO,EAAE,2BAA2B,CAAC;EAC9D;EAEA;EACAC,mBAAmBA,CAACC,UAAqC;IACvD,OAAO,IAAI,CAACnG,oBAAoB,CAACoG,KAAK,CAACD,UAAU,CAAC;EACpD;EAEAE,oBAAoBA,CAAA;IAClB,MAAMC,WAAW,GAAG,IAAI,CAACtG,oBAAoB,CAACoG,KAAK;IAEnD,IAAIE,WAAW,CAACzE,GAAG,EAAE,OAAO,KAAK;IACjC,IAAIyE,WAAW,CAAC1E,EAAE,EAAE,OAAO,IAAI;IAC/B,IAAI0E,WAAW,CAAC3E,EAAE,EAAE,OAAO,IAAI;IAC/B,IAAI2E,WAAW,CAAC5E,EAAE,EAAE,OAAO,IAAI;IAC/B,IAAI4E,WAAW,CAAC7E,EAAE,EAAE,OAAO,IAAI;IAC/B,OAAO,IAAI;EACb;EAEA;EACA8E,kBAAkBA,CAACC,IAAY,EAAEC,EAAc;IAC7C,MAAMC,KAAK,GAAGC,WAAW,CAACC,GAAG,EAAE;IAC/BH,EAAE,EAAE;IACJ,MAAMI,GAAG,GAAGF,WAAW,CAACC,GAAG,EAAE;IAC7B,MAAME,QAAQ,GAAGD,GAAG,GAAGH,KAAK;IAE5BK,OAAO,CAACC,GAAG,CAAC,GAAGR,IAAI,SAASM,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;IAC/D,OAAOH,QAAQ;EACjB;EAEA;EACAI,qBAAqBA,CAAA;IACnB;IACA;EAAA;;;uBArQStH,yBAAyB;IAAA;EAAA;;;aAAzBA,yBAAyB;MAAAuH,OAAA,EAAzBvH,yBAAyB,CAAAwH,IAAA;MAAAC,UAAA,EAFxB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}