{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../services/admin-auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nfunction AdminLoginComponent_mat_error_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"email\"), \" \");\n  }\n}\nfunction AdminLoginComponent_mat_error_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"password\"), \" \");\n  }\n}\nfunction AdminLoginComponent_mat_spinner_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 21);\n  }\n}\nfunction AdminLoginComponent_span_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminLoginComponent_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Signing In...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let AdminLoginComponent = /*#__PURE__*/(() => {\n  class AdminLoginComponent {\n    constructor(fb, authService, router, snackBar) {\n      this.fb = fb;\n      this.authService = authService;\n      this.router = router;\n      this.snackBar = snackBar;\n      this.isLoading = false;\n      this.hidePassword = true;\n      this.loginForm = this.fb.group({\n        email: ['', [Validators.required, Validators.email]],\n        password: ['', [Validators.required, Validators.minLength(6)]]\n      });\n    }\n    ngOnInit() {\n      // Redirect if already authenticated\n      if (this.authService.isAuthenticated()) {\n        this.router.navigate(['/admin/dashboard']);\n      }\n    }\n    onSubmit() {\n      if (this.loginForm.valid) {\n        this.isLoading = true;\n        const {\n          email,\n          password\n        } = this.loginForm.value;\n        this.authService.login(email, password).subscribe({\n          next: response => {\n            this.isLoading = false;\n            if (response.success) {\n              this.snackBar.open('Login successful!', 'Close', {\n                duration: 3000,\n                panelClass: ['success-snackbar']\n              });\n              this.router.navigate(['/admin/dashboard']);\n            }\n          },\n          error: error => {\n            this.isLoading = false;\n            const errorMessage = error.error?.message || 'Login failed. Please try again.';\n            this.snackBar.open(errorMessage, 'Close', {\n              duration: 5000,\n              panelClass: ['error-snackbar']\n            });\n          }\n        });\n      } else {\n        this.markFormGroupTouched();\n      }\n    }\n    markFormGroupTouched() {\n      Object.keys(this.loginForm.controls).forEach(key => {\n        const control = this.loginForm.get(key);\n        control?.markAsTouched();\n      });\n    }\n    getErrorMessage(fieldName) {\n      const control = this.loginForm.get(fieldName);\n      if (control?.hasError('required')) {\n        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;\n      }\n      if (control?.hasError('email')) {\n        return 'Please enter a valid email address';\n      }\n      if (control?.hasError('minlength')) {\n        return 'Password must be at least 6 characters long';\n      }\n      return '';\n    }\n    static {\n      this.ɵfac = function AdminLoginComponent_Factory(t) {\n        return new (t || AdminLoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AdminAuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AdminLoginComponent,\n        selectors: [[\"app-admin-login\"]],\n        decls: 42,\n        vars: 11,\n        consts: [[1, \"login-container\"], [1, \"login-card\"], [1, \"login-header\"], [1, \"logo\"], [1, \"logo-icon\"], [1, \"subtitle\"], [1, \"login-form\", 3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", \"autocomplete\", \"email\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"placeholder\", \"Enter your password\", \"autocomplete\", \"current-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"login-button\", \"full-width\", 3, \"disabled\"], [\"diameter\", \"20\", \"class\", \"login-spinner\", 4, \"ngIf\"], [1, \"login-footer\"], [1, \"footer-links\"], [\"href\", \"#\", 1, \"footer-link\"], [1, \"separator\"], [1, \"login-background\"], [1, \"bg-pattern\"], [\"diameter\", \"20\", 1, \"login-spinner\"]],\n        template: function AdminLoginComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"mat-icon\", 4);\n            i0.ɵɵtext(5, \"shopping_bag\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"h1\");\n            i0.ɵɵtext(7, \"DFashion Admin\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"p\", 5);\n            i0.ɵɵtext(9, \"Sign in to your admin account\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"form\", 6);\n            i0.ɵɵlistener(\"ngSubmit\", function AdminLoginComponent_Template_form_ngSubmit_10_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(11, \"mat-form-field\", 7)(12, \"mat-label\");\n            i0.ɵɵtext(13, \"Email Address\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(14, \"input\", 8);\n            i0.ɵɵelementStart(15, \"mat-icon\", 9);\n            i0.ɵɵtext(16, \"email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(17, AdminLoginComponent_mat_error_17_Template, 2, 1, \"mat-error\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"mat-form-field\", 7)(19, \"mat-label\");\n            i0.ɵɵtext(20, \"Password\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(21, \"input\", 11);\n            i0.ɵɵelementStart(22, \"button\", 12);\n            i0.ɵɵlistener(\"click\", function AdminLoginComponent_Template_button_click_22_listener() {\n              return ctx.hidePassword = !ctx.hidePassword;\n            });\n            i0.ɵɵelementStart(23, \"mat-icon\");\n            i0.ɵɵtext(24);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(25, AdminLoginComponent_mat_error_25_Template, 2, 1, \"mat-error\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"button\", 13);\n            i0.ɵɵtemplate(27, AdminLoginComponent_mat_spinner_27_Template, 1, 0, \"mat-spinner\", 14)(28, AdminLoginComponent_span_28_Template, 2, 0, \"span\", 10)(29, AdminLoginComponent_span_29_Template, 2, 0, \"span\", 10);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(30, \"div\", 15)(31, \"p\");\n            i0.ɵɵtext(32, \"\\u00A9 2024 DFashion. All rights reserved.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"div\", 16)(34, \"a\", 17);\n            i0.ɵɵtext(35, \"Privacy Policy\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"span\", 18);\n            i0.ɵɵtext(37, \"\\u2022\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"a\", 17);\n            i0.ɵɵtext(39, \"Terms of Service\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(40, \"div\", 19);\n            i0.ɵɵelement(41, \"div\", 20);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            let tmp_1_0;\n            let tmp_6_0;\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_1_0.touched));\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n            i0.ɵɵadvance();\n            i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hidePassword);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_6_0.touched));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          }\n        },\n        dependencies: [i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.MatIcon, i7.MatButton, i7.MatIconButton, i8.MatInput, i9.MatFormField, i9.MatLabel, i9.MatError, i9.MatSuffix, i10.MatProgressSpinner],\n        styles: [\".login-container[_ngcontent-%COMP%]{min-height:100vh;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#667eea,#764ba2);position:relative;overflow:hidden;padding:2rem}.login-container[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background:radial-gradient(circle at 20% 80%,rgba(120,119,198,.3) 0%,transparent 50%),radial-gradient(circle at 80% 20%,rgba(255,119,198,.3) 0%,transparent 50%),radial-gradient(circle at 40% 40%,rgba(120,219,255,.3) 0%,transparent 50%);animation:_ngcontent-%COMP%_backgroundShift 20s ease-in-out infinite}.login-background[_ngcontent-%COMP%]{position:absolute;inset:0;z-index:0}.login-background[_ngcontent-%COMP%]   .bg-pattern[_ngcontent-%COMP%]{width:100%;height:100%;background-image:radial-gradient(circle at 25% 25%,rgba(255,255,255,.1) 0%,transparent 50%),radial-gradient(circle at 75% 75%,rgba(255,255,255,.1) 0%,transparent 50%);animation:_ngcontent-%COMP%_float 20s ease-in-out infinite}@keyframes _ngcontent-%COMP%_backgroundShift{0%,to{background:radial-gradient(circle at 20% 80%,rgba(120,119,198,.3) 0%,transparent 50%),radial-gradient(circle at 80% 20%,rgba(255,119,198,.3) 0%,transparent 50%),radial-gradient(circle at 40% 40%,rgba(120,219,255,.3) 0%,transparent 50%)}50%{background:radial-gradient(circle at 80% 20%,rgba(120,119,198,.3) 0%,transparent 50%),radial-gradient(circle at 20% 80%,rgba(255,119,198,.3) 0%,transparent 50%),radial-gradient(circle at 60% 60%,rgba(120,219,255,.3) 0%,transparent 50%)}}.login-card[_ngcontent-%COMP%]{background:#fffffffa;-webkit-backdrop-filter:blur(25px);backdrop-filter:blur(25px);border-radius:28px;box-shadow:0 40px 80px #0003,0 0 0 1px #fff3,inset 0 1px #ffffff4d;padding:3.5rem;width:100%;max-width:540px;position:relative;z-index:1;max-height:90vh;overflow-y:auto;border:1px solid rgba(255,255,255,.3)}.login-card[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background:linear-gradient(135deg,#ffffff1a,#ffffff0d);border-radius:28px;pointer-events:none}.login-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:2rem}.login-header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:.5rem;margin-bottom:1rem}.login-header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   .logo-icon[_ngcontent-%COMP%]{font-size:2.5rem;width:2.5rem;height:2.5rem;color:#667eea}.login-header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0;font-size:2.5rem;font-weight:700;background:linear-gradient(135deg,#667eea,#764ba2);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text;letter-spacing:-.02em}.login-header[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%]{color:#666;margin:0;font-size:1rem}.login-form[_ngcontent-%COMP%]{margin-bottom:2rem}.login-form[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%]{width:100%;margin-bottom:1rem}.login-form[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%]{height:56px;font-size:1.125rem;font-weight:600;margin-top:1.5rem;position:relative;border-radius:16px;background:linear-gradient(135deg,#667eea,#764ba2);border:none;color:#fff;transition:all .3s ease;box-shadow:0 8px 24px #667eea4d}.login-form[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-2px);box-shadow:0 12px 32px #667eea66}.login-form[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%]:active{transform:translateY(0)}.login-form[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%]:disabled{opacity:.7;cursor:not-allowed}.login-form[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%]   .login-spinner[_ngcontent-%COMP%]{margin-right:.5rem}.demo-section[_ngcontent-%COMP%]{margin-bottom:2rem}.demo-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{text-align:center;margin:1.5rem 0 .5rem;color:#333;font-size:1.1rem;font-weight:500}.demo-section[_ngcontent-%COMP%]   .demo-description[_ngcontent-%COMP%]{text-align:center;color:#666;font-size:.9rem;margin-bottom:1rem}.demo-section[_ngcontent-%COMP%]   .demo-accounts[_ngcontent-%COMP%]{display:grid;gap:.75rem;max-height:300px;overflow-y:auto}.demo-section[_ngcontent-%COMP%]   .demo-accounts[_ngcontent-%COMP%]   .demo-account-card[_ngcontent-%COMP%]{cursor:pointer;transition:all .3s ease;border:1px solid #e0e0e0;border-radius:16px;overflow:hidden;background:#fffc;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.demo-section[_ngcontent-%COMP%]   .demo-accounts[_ngcontent-%COMP%]   .demo-account-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 24px #667eea26;border-color:#667eea;background:#fffffff2}.demo-section[_ngcontent-%COMP%]   .demo-accounts[_ngcontent-%COMP%]   .demo-account-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:1rem!important}.demo-section[_ngcontent-%COMP%]   .demo-accounts[_ngcontent-%COMP%]   .demo-account-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .account-info[_ngcontent-%COMP%]{flex:1}.demo-section[_ngcontent-%COMP%]   .demo-accounts[_ngcontent-%COMP%]   .demo-account-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .account-info[_ngcontent-%COMP%]   .account-role[_ngcontent-%COMP%]{font-weight:500;color:#333;font-size:.95rem}.demo-section[_ngcontent-%COMP%]   .demo-accounts[_ngcontent-%COMP%]   .demo-account-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .account-info[_ngcontent-%COMP%]   .account-department[_ngcontent-%COMP%]{color:#667eea;font-size:.85rem;margin:.25rem 0}.demo-section[_ngcontent-%COMP%]   .demo-accounts[_ngcontent-%COMP%]   .demo-account-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .account-info[_ngcontent-%COMP%]   .account-email[_ngcontent-%COMP%]{color:#666;font-size:.8rem;font-family:Courier New,monospace}.demo-section[_ngcontent-%COMP%]   .demo-accounts[_ngcontent-%COMP%]   .demo-account-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%]   .account-icon[_ngcontent-%COMP%]{color:#667eea;opacity:.7}.login-footer[_ngcontent-%COMP%]{text-align:center;color:#666;font-size:.85rem}.login-footer[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 .5rem}.login-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;gap:.5rem}.login-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   .footer-link[_ngcontent-%COMP%]{color:#667eea;text-decoration:none;transition:color .2s ease}.login-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   .footer-link[_ngcontent-%COMP%]:hover{color:#764ba2;text-decoration:underline}.login-footer[_ngcontent-%COMP%]   .footer-links[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]{color:#ccc}@keyframes _ngcontent-%COMP%_float{0%,to{transform:translateY(0) rotate(0)}50%{transform:translateY(-20px) rotate(180deg)}}@keyframes _ngcontent-%COMP%_shimmer{0%{background-position:-200px 0}to{background-position:calc(200px + 100%) 0}}.login-card[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);background-size:200px 100%;animation:_ngcontent-%COMP%_shimmer 3s infinite;border-radius:24px;pointer-events:none}@media (max-width: 600px){.login-container[_ngcontent-%COMP%]{padding:1rem}.login-card[_ngcontent-%COMP%]{padding:2rem 1.5rem;max-width:none;border-radius:20px}.login-header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem}.demo-accounts[_ngcontent-%COMP%]{max-height:250px}.login-button[_ngcontent-%COMP%]{height:52px;font-size:1rem}}  .success-snackbar{background-color:#4caf50!important;color:#fff!important}  .error-snackbar{background-color:#f44336!important;color:#fff!important}  .mat-form-field-appearance-outline .mat-form-field-outline{color:#e0e0e0}  .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick{color:#667eea}  .mat-form-field-appearance-outline .mat-form-field-label{color:#666}  .mat-form-field-appearance-outline.mat-focused .mat-form-field-label{color:#667eea}  .mat-primary .mat-button-focus-overlay{background-color:#667eea}.login-spinner[_ngcontent-%COMP%]     circle{stroke:#fff}\"]\n      });\n    }\n  }\n  return AdminLoginComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}