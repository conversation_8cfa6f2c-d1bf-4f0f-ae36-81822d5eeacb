.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  
  ion-spinner {
    margin-bottom: 16px;
  }
  
  p {
    color: var(--ion-color-medium);
    margin: 0;
  }
}

.profile-content {
  padding: 0;
}

.user-header {
  background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-secondary) 100%);
  padding: 32px 24px;
  text-align: center;
  color: white;
  
  .user-avatar {
    width: 100px;
    height: 100px;
    margin: 0 auto 16px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid rgba(255, 255, 255, 0.3);
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .user-info {
    h2 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
    }
    
    .user-email {
      margin: 0 0 4px 0;
      font-size: 16px;
      opacity: 0.9;
    }
    
    .join-date {
      margin: 0;
      font-size: 14px;
      opacity: 0.7;
    }
  }
}

.quick-stats {
  display: flex;
  justify-content: space-around;
  padding: 24px;
  background: white;
  margin-bottom: 16px;
  
  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    
    ion-icon {
      font-size: 32px;
      margin-bottom: 8px;
    }
    
    .stat-info {
      display: flex;
      flex-direction: column;
      
      .stat-number {
        font-size: 20px;
        font-weight: 600;
        color: var(--ion-color-dark);
        margin-bottom: 2px;
      }
      
      .stat-label {
        font-size: 12px;
        color: var(--ion-color-medium);
      }
    }
  }
}

.menu-section {
  margin-bottom: 24px;
  
  .menu-item {
    --padding-start: 24px;
    --padding-end: 24px;
    --min-height: 56px;
    
    ion-icon[slot="start"] {
      margin-right: 16px;
      font-size: 24px;
    }
    
    ion-label h3 {
      font-size: 16px;
      font-weight: 500;
      color: var(--ion-color-dark);
      margin: 0;
    }
  }
}

.logout-section {
  padding: 0 24px 24px;
}

.guest-content {
  padding: 0;
}

.guest-header {
  background: linear-gradient(135deg, var(--ion-color-light) 0%, var(--ion-color-medium) 100%);
  padding: 48px 24px;
  text-align: center;
  color: var(--ion-color-dark);
  
  .guest-avatar {
    margin-bottom: 24px;
    
    ion-icon {
      font-size: 100px;
    }
  }
  
  h2 {
    margin: 0 0 16px 0;
    font-size: 28px;
    font-weight: 600;
  }
  
  p {
    margin: 0;
    font-size: 16px;
    opacity: 0.8;
    line-height: 1.5;
  }
}

.auth-buttons {
  padding: 32px 24px;
  background: white;
  margin-bottom: 16px;
  
  ion-button {
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.guest-menu {
  .menu-item {
    --padding-start: 24px;
    --padding-end: 24px;
    --min-height: 56px;
    
    ion-icon[slot="start"] {
      margin-right: 16px;
      font-size: 24px;
    }
    
    ion-label h3 {
      font-size: 16px;
      font-weight: 500;
      color: var(--ion-color-dark);
      margin: 0;
    }
  }
}

.bottom-spacing {
  height: 80px;
}
