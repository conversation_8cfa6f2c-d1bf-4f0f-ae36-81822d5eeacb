{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/trending.service\";\nimport * as i2 from \"../../../../core/services/social-interactions.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3, 4];\nconst _c1 = () => [1, 2, 3];\nconst _c2 = () => [1, 2, 3, 4, 5];\nfunction FeaturedBrandsComponent_div_8_div_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 19);\n  }\n}\nfunction FeaturedBrandsComponent_div_8_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵelement(2, \"div\", 15)(3, \"div\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 17);\n    i0.ɵɵtemplate(5, FeaturedBrandsComponent_div_8_div_2_div_5_Template, 1, 0, \"div\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c1));\n  }\n}\nfunction FeaturedBrandsComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11);\n    i0.ɵɵtemplate(2, FeaturedBrandsComponent_div_8_div_2_Template, 6, 2, \"div\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction FeaturedBrandsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵelement(1, \"ion-icon\", 21);\n    i0.ɵɵelementStart(2, \"p\", 22);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_9_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 24);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction FeaturedBrandsComponent_div_10_div_1_div_25_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r6.originalPrice));\n  }\n}\nfunction FeaturedBrandsComponent_div_10_div_1_div_25_ion_icon_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ion-icon\", 50);\n  }\n  if (rf & 2) {\n    const star_r7 = ctx.$implicit;\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r7 <= product_r6.rating.average);\n    i0.ɵɵproperty(\"name\", star_r7 <= product_r6.rating.average ? \"star\" : \"star-outline\");\n  }\n}\nfunction FeaturedBrandsComponent_div_10_div_1_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_10_div_1_div_25_Template_div_click_0_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onProductClick(product_r6, $event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 46);\n    i0.ɵɵelement(2, \"img\", 47);\n    i0.ɵɵelementStart(3, \"div\", 48)(4, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_10_div_1_div_25_Template_button_click_4_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onLikeProduct(product_r6, $event));\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_10_div_1_div_25_Template_button_click_6_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onShareProduct(product_r6, $event));\n    });\n    i0.ɵɵelement(7, \"ion-icon\", 52);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 53)(9, \"h5\", 54);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 55)(12, \"span\", 56);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, FeaturedBrandsComponent_div_10_div_1_div_25_span_14_Template, 2, 1, \"span\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 58)(16, \"div\", 59);\n    i0.ɵɵtemplate(17, FeaturedBrandsComponent_div_10_div_1_div_25_ion_icon_17_Template, 1, 3, \"ion-icon\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 61);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r6.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r6.images[0].alt || product_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r1.isProductLiked(product_r6._id));\n    i0.ɵɵattribute(\"aria-label\", \"Like \" + product_r6.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isProductLiked(product_r6._id) ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", \"Share \" + product_r6.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(product_r6.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r6.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r6.originalPrice && product_r6.originalPrice > product_r6.price);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(12, _c2));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r6.rating.count, \")\");\n  }\n}\nfunction FeaturedBrandsComponent_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_10_div_1_Template_div_click_0_listener() {\n      const brand_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onBrandClick(brand_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 28)(2, \"div\", 29)(3, \"h3\", 30);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 31)(6, \"div\", 32);\n    i0.ɵɵelement(7, \"ion-icon\", 33);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 32);\n    i0.ɵɵelement(11, \"ion-icon\", 34);\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 32);\n    i0.ɵɵelement(15, \"ion-icon\", 35);\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(18, \"div\", 36);\n    i0.ɵɵelement(19, \"ion-icon\", 37);\n    i0.ɵɵtext(20, \" Featured \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 38)(22, \"h4\", 39);\n    i0.ɵɵtext(23, \"Top Products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 40);\n    i0.ɵɵtemplate(25, FeaturedBrandsComponent_div_10_div_1_div_25_Template, 20, 13, \"div\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 42)(27, \"button\", 43)(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"ion-icon\", 44);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const brand_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(brand_r4.brand);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", brand_r4.productCount, \" Products\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", brand_r4.avgRating, \"/5\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(brand_r4.totalViews), \" Views\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", brand_r4.topProducts)(\"ngForTrackBy\", ctx_r1.trackByProductId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"View All \", brand_r4.brand, \" Products\");\n  }\n}\nfunction FeaturedBrandsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtemplate(1, FeaturedBrandsComponent_div_10_div_1_Template, 31, 7, \"div\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.featuredBrands)(\"ngForTrackBy\", ctx_r1.trackByBrandName);\n  }\n}\nfunction FeaturedBrandsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"ion-icon\", 64);\n    i0.ɵɵelementStart(2, \"h3\", 65);\n    i0.ɵɵtext(3, \"No Featured Brands\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 66);\n    i0.ɵɵtext(5, \"Check back later for featured brand collections\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class FeaturedBrandsComponent {\n  constructor(trendingService, socialService, router) {\n    this.trendingService = trendingService;\n    this.socialService = socialService;\n    this.router = router;\n    this.featuredBrands = [];\n    this.isLoading = true;\n    this.error = null;\n    this.likedProducts = new Set();\n    this.subscription = new Subscription();\n  }\n  ngOnInit() {\n    this.loadFeaturedBrands();\n    this.subscribeFeaturedBrands();\n    this.subscribeLikedProducts();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n  subscribeFeaturedBrands() {\n    this.subscription.add(this.trendingService.featuredBrands$.subscribe(brands => {\n      this.featuredBrands = brands;\n      this.isLoading = false;\n    }));\n  }\n  subscribeLikedProducts() {\n    this.subscription.add(this.socialService.likedProducts$.subscribe(likedProducts => {\n      this.likedProducts = likedProducts;\n    }));\n  }\n  loadFeaturedBrands() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        yield _this.trendingService.loadFeaturedBrands();\n      } catch (error) {\n        console.error('Error loading featured brands:', error);\n        _this.error = 'Failed to load featured brands';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onBrandClick(brand) {\n    this.router.navigate(['/products'], {\n      queryParams: {\n        brand: brand.brand\n      }\n    });\n  }\n  onProductClick(product, event) {\n    event.stopPropagation();\n    this.router.navigate(['/product', product._id]);\n  }\n  onLikeProduct(product, event) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const result = yield _this2.socialService.likeProduct(product._id);\n        if (result.success) {\n          console.log(result.message);\n        } else {\n          console.error('Failed to like product:', result.message);\n        }\n      } catch (error) {\n        console.error('Error liking product:', error);\n      }\n    })();\n  }\n  onShareProduct(product, event) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const productUrl = `${window.location.origin}/product/${product._id}`;\n        yield navigator.clipboard.writeText(productUrl);\n        yield _this3.socialService.shareProduct(product._id, {\n          platform: 'copy_link',\n          message: `Check out this amazing ${product.name} from ${product.brand}!`\n        });\n        console.log('Product link copied to clipboard!');\n      } catch (error) {\n        console.error('Error sharing product:', error);\n      }\n    })();\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  onRetry() {\n    this.loadFeaturedBrands();\n  }\n  trackByBrandName(index, brand) {\n    return brand.brand;\n  }\n  isProductLiked(productId) {\n    return this.likedProducts.has(productId);\n  }\n  trackByProductId(index, product) {\n    return product._id;\n  }\n  static {\n    this.ɵfac = function FeaturedBrandsComponent_Factory(t) {\n      return new (t || FeaturedBrandsComponent)(i0.ɵɵdirectiveInject(i1.TrendingService), i0.ɵɵdirectiveInject(i2.SocialInteractionsService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FeaturedBrandsComponent,\n      selectors: [[\"app-featured-brands\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 12,\n      vars: 4,\n      consts: [[1, \"featured-brands-container\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"diamond\", 1, \"title-icon\"], [1, \"section-subtitle\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"brands-grid\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-brand-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-brand-card\"], [1, \"loading-header\"], [1, \"loading-brand-name\"], [1, \"loading-stats\"], [1, \"loading-products\"], [\"class\", \"loading-product\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-product\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"brands-grid\"], [\"class\", \"brand-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"brand-card\", 3, \"click\"], [1, \"brand-header\"], [1, \"brand-info\"], [1, \"brand-name\"], [1, \"brand-stats\"], [1, \"stat-item\"], [\"name\", \"bag-outline\"], [\"name\", \"star\"], [\"name\", \"eye-outline\"], [1, \"brand-badge\"], [\"name\", \"diamond\"], [1, \"top-products\"], [1, \"products-title\"], [1, \"products-list\"], [\"class\", \"product-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"view-more-section\"], [1, \"view-more-btn\"], [\"name\", \"chevron-forward\"], [1, \"product-item\", 3, \"click\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"src\", \"alt\"], [1, \"product-actions\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"name\", \"share-outline\"], [1, \"product-details\"], [1, \"product-name\"], [1, \"product-price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"product-rating\"], [1, \"stars\"], [3, \"name\", \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-count\"], [1, \"original-price\"], [1, \"empty-container\"], [\"name\", \"diamond-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"]],\n      template: function FeaturedBrandsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n          i0.ɵɵelement(4, \"ion-icon\", 4);\n          i0.ɵɵtext(5, \" Featured Brands \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \"Top brands with amazing collections\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(8, FeaturedBrandsComponent_div_8_Template, 3, 2, \"div\", 6)(9, FeaturedBrandsComponent_div_9_Template, 7, 1, \"div\", 7)(10, FeaturedBrandsComponent_div_10_Template, 2, 2, \"div\", 8)(11, FeaturedBrandsComponent_div_11_Template, 6, 0, \"div\", 9);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.featuredBrands.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.featuredBrands.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, IonicModule, i5.IonIcon],\n      styles: [\".featured-brands-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n  color: white;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 12px;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #ffd700;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.8);\\n  margin: 0;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 20px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  padding: 20px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-header[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-header[_ngcontent-%COMP%]   .loading-brand-name[_ngcontent-%COMP%] {\\n  height: 24px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 8px;\\n  margin-bottom: 8px;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-header[_ngcontent-%COMP%]   .loading-stats[_ngcontent-%COMP%] {\\n  height: 16px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 8px;\\n  width: 70%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-products[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-products[_ngcontent-%COMP%]   .loading-product[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 120px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 12px;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0%, 100% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n.error-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #ff6b6b;\\n  margin-bottom: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: rgba(255, 255, 255, 0.8);\\n  margin-bottom: 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 12px 24px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 auto;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n}\\n\\n.brands-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 20px;\\n}\\n\\n.brand-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 20px;\\n  padding: 24px;\\n  -webkit-backdrop-filter: blur(15px);\\n          backdrop-filter: blur(15px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n  cursor: pointer;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.brand-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\\n  opacity: 0;\\n  transition: opacity 0.4s ease;\\n  pointer-events: none;\\n}\\n.brand-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-12px) scale(1.02);\\n  background: rgba(255, 255, 255, 0.18);\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3), 0 8px 20px rgba(255, 255, 255, 0.1) inset;\\n  border-color: rgba(255, 255, 255, 0.3);\\n}\\n.brand-card[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n\\n.brand-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  margin-bottom: 24px;\\n  position: relative;\\n  z-index: 1;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding-right: 16px;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-name[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 800;\\n  color: white;\\n  margin: 0 0 16px 0;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n  letter-spacing: -0.5px;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  font-size: 13px;\\n  color: rgba(255, 255, 255, 0.9);\\n  background: rgba(255, 255, 255, 0.1);\\n  padding: 6px 12px;\\n  border-radius: 12px;\\n  -webkit-backdrop-filter: blur(5px);\\n          backdrop-filter: blur(5px);\\n  transition: all 0.3s ease;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.15);\\n  transform: translateX(4px);\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #ffd700;\\n  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);\\n  color: #333;\\n  padding: 10px 16px;\\n  border-radius: 25px;\\n  font-size: 13px;\\n  font-weight: 700;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4), 0 2px 4px rgba(0, 0, 0, 0.2);\\n  transition: all 0.3s ease;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5), 0 4px 8px rgba(0, 0, 0, 0.3);\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  animation: _ngcontent-%COMP%_sparkle 2s ease-in-out infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_sparkle {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n}\\n.top-products[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: white;\\n  margin: 0 0 16px 0;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  overflow-x: auto;\\n  padding-bottom: 8px;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 4px;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 2px;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 2px;\\n}\\n\\n.product-item[_ngcontent-%COMP%] {\\n  flex: 0 0 140px;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 12px;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.product-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  background: rgba(255, 255, 255, 0.15);\\n}\\n.product-item[_ngcontent-%COMP%]:hover   .product-actions[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.product-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100px;\\n  object-fit: cover;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  right: 8px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 28px;\\n  height: 28px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #333;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  transform: scale(1.1);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%] {\\n  background: rgba(220, 53, 69, 0.2);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.product-details[_ngcontent-%COMP%] {\\n  padding: 12px;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: white;\\n  margin: 0 0 8px 0;\\n  line-height: 1.3;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  margin-bottom: 8px;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 700;\\n  color: #ffd700;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: rgba(255, 255, 255, 0.6);\\n  text-decoration: line-through;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1px;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: rgba(255, 255, 255, 0.3);\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon.filled[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .rating-count[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 2px solid rgba(255, 255, 255, 0.2);\\n  color: white;\\n  padding: 12px 16px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  border-color: rgba(255, 255, 255, 0.4);\\n}\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: rgba(255, 255, 255, 0.4);\\n  margin-bottom: 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 8px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n@media (max-width: 768px) {\\n  .featured-brands-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .brands-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 16px;\\n  }\\n  .brand-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n  }\\n  .brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%] {\\n    align-self: flex-start;\\n  }\\n  .brand-stats[_ngcontent-%COMP%] {\\n    flex-direction: row !important;\\n    flex-wrap: wrap;\\n    gap: 12px !important;\\n  }\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subscription", "IonicModule", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵtemplate", "FeaturedBrandsComponent_div_8_div_2_div_5_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c1", "FeaturedBrandsComponent_div_8_div_2_Template", "_c0", "ɵɵtext", "ɵɵlistener", "FeaturedBrandsComponent_div_9_Template_button_click_4_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onRetry", "ɵɵtextInterpolate", "error", "formatPrice", "product_r6", "originalPrice", "ɵɵclassProp", "star_r7", "rating", "average", "FeaturedBrandsComponent_div_10_div_1_div_25_Template_div_click_0_listener", "$event", "_r5", "$implicit", "onProductClick", "FeaturedBrandsComponent_div_10_div_1_div_25_Template_button_click_4_listener", "onLikeProduct", "FeaturedBrandsComponent_div_10_div_1_div_25_Template_button_click_6_listener", "onShareProduct", "FeaturedBrandsComponent_div_10_div_1_div_25_span_14_Template", "FeaturedBrandsComponent_div_10_div_1_div_25_ion_icon_17_Template", "images", "url", "ɵɵsanitizeUrl", "alt", "name", "isProductLiked", "_id", "price", "_c2", "ɵɵtextInterpolate1", "count", "FeaturedBrandsComponent_div_10_div_1_Template_div_click_0_listener", "brand_r4", "_r3", "onBrandClick", "FeaturedBrandsComponent_div_10_div_1_div_25_Template", "brand", "productCount", "avgRating", "formatNumber", "totalViews", "topProducts", "trackByProductId", "FeaturedBrandsComponent_div_10_div_1_Template", "featuredB<PERSON>s", "trackByBrandName", "FeaturedBrandsComponent", "constructor", "trendingService", "socialService", "router", "isLoading", "likedProducts", "Set", "subscription", "ngOnInit", "loadFeaturedBrands", "subscribeFeaturedBrands", "subscribeLikedProducts", "ngOnDestroy", "unsubscribe", "add", "featuredBrands$", "subscribe", "brands", "likedProducts$", "_this", "_asyncToGenerator", "console", "navigate", "queryParams", "product", "event", "stopPropagation", "_this2", "result", "likeProduct", "success", "log", "message", "_this3", "productUrl", "window", "location", "origin", "navigator", "clipboard", "writeText", "shareProduct", "platform", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "num", "toFixed", "toString", "index", "productId", "has", "ɵɵdirectiveInject", "i1", "TrendingService", "i2", "SocialInteractionsService", "i3", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "FeaturedBrandsComponent_Template", "rf", "ctx", "FeaturedBrandsComponent_div_8_Template", "FeaturedBrandsComponent_div_9_Template", "FeaturedBrandsComponent_div_10_Template", "FeaturedBrandsComponent_div_11_Template", "length", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "IonIcon", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\featured-brands\\featured-brands.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\featured-brands\\featured-brands.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { TrendingService, FeaturedBrand } from '../../../../core/services/trending.service';\nimport { Product } from '../../../../core/models/product.model';\nimport { SocialInteractionsService } from '../../../../core/services/social-interactions.service';\nimport { IonicModule } from '@ionic/angular';\n\n@Component({\n  selector: 'app-featured-brands',\n  standalone: true,\n  imports: [CommonModule, IonicModule],\n  templateUrl: './featured-brands.component.html',\n  styleUrls: ['./featured-brands.component.scss']\n})\nexport class FeaturedBrandsComponent implements OnInit, OnDestroy {\n  featuredBrands: FeaturedBrand[] = [];\n  isLoading = true;\n  error: string | null = null;\n  likedProducts = new Set<string>();\n  private subscription: Subscription = new Subscription();\n\n  constructor(\n    private trendingService: TrendingService,\n    private socialService: SocialInteractionsService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadFeaturedBrands();\n    this.subscribeFeaturedBrands();\n    this.subscribeLikedProducts();\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n\n  private subscribeFeaturedBrands() {\n    this.subscription.add(\n      this.trendingService.featuredBrands$.subscribe(brands => {\n        this.featuredBrands = brands;\n        this.isLoading = false;\n      })\n    );\n  }\n\n  private subscribeLikedProducts() {\n    this.subscription.add(\n      this.socialService.likedProducts$.subscribe(likedProducts => {\n        this.likedProducts = likedProducts;\n      })\n    );\n  }\n\n  private async loadFeaturedBrands() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      await this.trendingService.loadFeaturedBrands();\n    } catch (error) {\n      console.error('Error loading featured brands:', error);\n      this.error = 'Failed to load featured brands';\n      this.isLoading = false;\n    }\n  }\n\n  onBrandClick(brand: FeaturedBrand) {\n    this.router.navigate(['/products'], { \n      queryParams: { brand: brand.brand } \n    });\n  }\n\n  onProductClick(product: Product, event: Event) {\n    event.stopPropagation();\n    this.router.navigate(['/product', product._id]);\n  }\n\n  async onLikeProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      const result = await this.socialService.likeProduct(product._id);\n      if (result.success) {\n        console.log(result.message);\n      } else {\n        console.error('Failed to like product:', result.message);\n      }\n    } catch (error) {\n      console.error('Error liking product:', error);\n    }\n  }\n\n  async onShareProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      const productUrl = `${window.location.origin}/product/${product._id}`;\n      await navigator.clipboard.writeText(productUrl);\n\n      await this.socialService.shareProduct(product._id, {\n        platform: 'copy_link',\n        message: `Check out this amazing ${product.name} from ${product.brand}!`\n      });\n\n      console.log('Product link copied to clipboard!');\n    } catch (error) {\n      console.error('Error sharing product:', error);\n    }\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  onRetry() {\n    this.loadFeaturedBrands();\n  }\n\n  trackByBrandName(index: number, brand: FeaturedBrand): string {\n    return brand.brand;\n  }\n\n  isProductLiked(productId: string): boolean {\n    return this.likedProducts.has(productId);\n  }\n\n  trackByProductId(index: number, product: Product): string {\n    return product._id;\n  }\n}\n", "<div class=\"featured-brands-container\">\n  <!-- Header -->\n  <div class=\"section-header\">\n    <div class=\"header-content\">\n      <h2 class=\"section-title\">\n        <ion-icon name=\"diamond\" class=\"title-icon\"></ion-icon>\n        Featured Brands\n      </h2>\n      <p class=\"section-subtitle\">Top brands with amazing collections</p>\n    </div>\n  </div>\n\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <div class=\"loading-grid\">\n      <div *ngFor=\"let item of [1,2,3,4]\" class=\"loading-brand-card\">\n        <div class=\"loading-header\">\n          <div class=\"loading-brand-name\"></div>\n          <div class=\"loading-stats\"></div>\n        </div>\n        <div class=\"loading-products\">\n          <div *ngFor=\"let prod of [1,2,3]\" class=\"loading-product\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error && !isLoading\" class=\"error-container\">\n    <ion-icon name=\"alert-circle\" class=\"error-icon\"></ion-icon>\n    <p class=\"error-message\">{{ error }}</p>\n    <button class=\"retry-btn\" (click)=\"onRetry()\">\n      <ion-icon name=\"refresh\"></ion-icon>\n      Try Again\n    </button>\n  </div>\n\n  <!-- Brands Grid -->\n  <div *ngIf=\"!isLoading && !error && featuredBrands.length > 0\" class=\"brands-grid\">\n    <div \n      *ngFor=\"let brand of featuredBrands; trackBy: trackByBrandName\" \n      class=\"brand-card\"\n      (click)=\"onBrandClick(brand)\"\n    >\n      <!-- Brand Header -->\n      <div class=\"brand-header\">\n        <div class=\"brand-info\">\n          <h3 class=\"brand-name\">{{ brand.brand }}</h3>\n          <div class=\"brand-stats\">\n            <div class=\"stat-item\">\n              <ion-icon name=\"bag-outline\"></ion-icon>\n              <span>{{ brand.productCount }} Products</span>\n            </div>\n            <div class=\"stat-item\">\n              <ion-icon name=\"star\"></ion-icon>\n              <span>{{ brand.avgRating }}/5</span>\n            </div>\n            <div class=\"stat-item\">\n              <ion-icon name=\"eye-outline\"></ion-icon>\n              <span>{{ formatNumber(brand.totalViews) }} Views</span>\n            </div>\n          </div>\n        </div>\n        <div class=\"brand-badge\">\n          <ion-icon name=\"diamond\"></ion-icon>\n          Featured\n        </div>\n      </div>\n\n      <!-- Top Products -->\n      <div class=\"top-products\">\n        <h4 class=\"products-title\">Top Products</h4>\n        <div class=\"products-list\">\n          <div \n            *ngFor=\"let product of brand.topProducts; trackBy: trackByProductId\" \n            class=\"product-item\"\n            (click)=\"onProductClick(product, $event)\"\n          >\n            <div class=\"product-image-container\">\n              <img \n                [src]=\"product.images[0].url\"\n                [alt]=\"product.images[0].alt || product.name\"\n                class=\"product-image\"\n                loading=\"lazy\"\n              />\n              \n              <!-- Action Buttons -->\n              <div class=\"product-actions\">\n                <button\n                  class=\"action-btn like-btn\"\n                  [class.liked]=\"isProductLiked(product._id)\"\n                  (click)=\"onLikeProduct(product, $event)\"\n                  [attr.aria-label]=\"'Like ' + product.name\"\n                >\n                  <ion-icon [name]=\"isProductLiked(product._id) ? 'heart' : 'heart-outline'\"></ion-icon>\n                </button>\n                <button \n                  class=\"action-btn share-btn\" \n                  (click)=\"onShareProduct(product, $event)\"\n                  [attr.aria-label]=\"'Share ' + product.name\"\n                >\n                  <ion-icon name=\"share-outline\"></ion-icon>\n                </button>\n              </div>\n            </div>\n\n            <div class=\"product-details\">\n              <h5 class=\"product-name\">{{ product.name }}</h5>\n              <div class=\"product-price\">\n                <span class=\"current-price\">{{ formatPrice(product.price) }}</span>\n                <span *ngIf=\"product.originalPrice && product.originalPrice > product.price\" \n                      class=\"original-price\">{{ formatPrice(product.originalPrice) }}</span>\n              </div>\n              <div class=\"product-rating\">\n                <div class=\"stars\">\n                  <ion-icon \n                    *ngFor=\"let star of [1,2,3,4,5]\" \n                    [name]=\"star <= product.rating.average ? 'star' : 'star-outline'\"\n                    [class.filled]=\"star <= product.rating.average\"\n                  ></ion-icon>\n                </div>\n                <span class=\"rating-count\">({{ product.rating.count }})</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- View More Button -->\n      <div class=\"view-more-section\">\n        <button class=\"view-more-btn\">\n          <span>View All {{ brand.brand }} Products</span>\n          <ion-icon name=\"chevron-forward\"></ion-icon>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Empty State -->\n  <div *ngIf=\"!isLoading && !error && featuredBrands.length === 0\" class=\"empty-container\">\n    <ion-icon name=\"diamond-outline\" class=\"empty-icon\"></ion-icon>\n    <h3 class=\"empty-title\">No Featured Brands</h3>\n    <p class=\"empty-message\">Check back later for featured brand collections</p>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AAInC,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;;ICclCC,EAAA,CAAAC,SAAA,cAAgE;;;;;IALlED,EADF,CAAAE,cAAA,cAA+D,cACjC;IAE1BF,EADA,CAAAC,SAAA,cAAsC,cACL;IACnCD,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,cAAA,cAA8B;IAC5BF,EAAA,CAAAI,UAAA,IAAAC,kDAAA,kBAA0D;IAE9DL,EADE,CAAAG,YAAA,EAAM,EACF;;;IAFoBH,EAAA,CAAAM,SAAA,GAAU;IAAVN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAU;;;;;IAPtCT,EADF,CAAAE,cAAA,cAAiD,cACrB;IACxBF,EAAA,CAAAI,UAAA,IAAAM,4CAAA,kBAA+D;IAUnEV,EADE,CAAAG,YAAA,EAAM,EACF;;;IAVoBH,EAAA,CAAAM,SAAA,GAAY;IAAZN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAG,GAAA,EAAY;;;;;;IAatCX,EAAA,CAAAE,cAAA,cAAyD;IACvDF,EAAA,CAAAC,SAAA,mBAA4D;IAC5DD,EAAA,CAAAE,cAAA,YAAyB;IAAAF,EAAA,CAAAY,MAAA,GAAW;IAAAZ,EAAA,CAAAG,YAAA,EAAI;IACxCH,EAAA,CAAAE,cAAA,iBAA8C;IAApBF,EAAA,CAAAa,UAAA,mBAAAC,+DAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAG,OAAA,EAAS;IAAA,EAAC;IAC3CpB,EAAA,CAAAC,SAAA,mBAAoC;IACpCD,EAAA,CAAAY,MAAA,kBACF;IACFZ,EADE,CAAAG,YAAA,EAAS,EACL;;;;IALqBH,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAqB,iBAAA,CAAAJ,MAAA,CAAAK,KAAA,CAAW;;;;;IAgFxBtB,EAAA,CAAAE,cAAA,eAC6B;IAAAF,EAAA,CAAAY,MAAA,GAAwC;IAAAZ,EAAA,CAAAG,YAAA,EAAO;;;;;IAA/CH,EAAA,CAAAM,SAAA,EAAwC;IAAxCN,EAAA,CAAAqB,iBAAA,CAAAJ,MAAA,CAAAM,WAAA,CAAAC,UAAA,CAAAC,aAAA,EAAwC;;;;;IAInEzB,EAAA,CAAAC,SAAA,mBAIY;;;;;IADVD,EAAA,CAAA0B,WAAA,WAAAC,OAAA,IAAAH,UAAA,CAAAI,MAAA,CAAAC,OAAA,CAA+C;IAD/C7B,EAAA,CAAAO,UAAA,SAAAoB,OAAA,IAAAH,UAAA,CAAAI,MAAA,CAAAC,OAAA,2BAAiE;;;;;;IA5C3E7B,EAAA,CAAAE,cAAA,cAIC;IADCF,EAAA,CAAAa,UAAA,mBAAAiB,0EAAAC,MAAA;MAAA,MAAAP,UAAA,GAAAxB,EAAA,CAAAe,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAiB,cAAA,CAAAV,UAAA,EAAAO,MAAA,CAA+B;IAAA,EAAC;IAEzC/B,EAAA,CAAAE,cAAA,cAAqC;IACnCF,EAAA,CAAAC,SAAA,cAKE;IAIAD,EADF,CAAAE,cAAA,cAA6B,iBAM1B;IAFCF,EAAA,CAAAa,UAAA,mBAAAsB,6EAAAJ,MAAA;MAAA,MAAAP,UAAA,GAAAxB,EAAA,CAAAe,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAmB,aAAA,CAAAZ,UAAA,EAAAO,MAAA,CAA8B;IAAA,EAAC;IAGxC/B,EAAA,CAAAC,SAAA,mBAAsF;IACxFD,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAE,cAAA,iBAIC;IAFCF,EAAA,CAAAa,UAAA,mBAAAwB,6EAAAN,MAAA;MAAA,MAAAP,UAAA,GAAAxB,EAAA,CAAAe,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAqB,cAAA,CAAAd,UAAA,EAAAO,MAAA,CAA+B;IAAA,EAAC;IAGzC/B,EAAA,CAAAC,SAAA,mBAA0C;IAGhDD,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAGJH,EADF,CAAAE,cAAA,cAA6B,aACF;IAAAF,EAAA,CAAAY,MAAA,IAAkB;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IAE9CH,EADF,CAAAE,cAAA,eAA2B,gBACG;IAAAF,EAAA,CAAAY,MAAA,IAAgC;IAAAZ,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAI,UAAA,KAAAmC,4DAAA,mBAC6B;IAC/BvC,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAE,cAAA,eAA4B,eACP;IACjBF,EAAA,CAAAI,UAAA,KAAAoC,gEAAA,uBAIC;IACHxC,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,cAAA,gBAA2B;IAAAF,EAAA,CAAAY,MAAA,IAA4B;IAG7DZ,EAH6D,CAAAG,YAAA,EAAO,EAC1D,EACF,EACF;;;;;IA5CAH,EAAA,CAAAM,SAAA,GAA6B;IAC7BN,EADA,CAAAO,UAAA,QAAAiB,UAAA,CAAAiB,MAAA,IAAAC,GAAA,EAAA1C,EAAA,CAAA2C,aAAA,CAA6B,QAAAnB,UAAA,CAAAiB,MAAA,IAAAG,GAAA,IAAApB,UAAA,CAAAqB,IAAA,CACgB;IAS3C7C,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAA0B,WAAA,UAAAT,MAAA,CAAA6B,cAAA,CAAAtB,UAAA,CAAAuB,GAAA,EAA2C;;IAIjC/C,EAAA,CAAAM,SAAA,EAAgE;IAAhEN,EAAA,CAAAO,UAAA,SAAAU,MAAA,CAAA6B,cAAA,CAAAtB,UAAA,CAAAuB,GAAA,8BAAgE;IAK1E/C,EAAA,CAAAM,SAAA,EAA2C;;IAQtBN,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAqB,iBAAA,CAAAG,UAAA,CAAAqB,IAAA,CAAkB;IAEb7C,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAqB,iBAAA,CAAAJ,MAAA,CAAAM,WAAA,CAAAC,UAAA,CAAAwB,KAAA,EAAgC;IACrDhD,EAAA,CAAAM,SAAA,EAAoE;IAApEN,EAAA,CAAAO,UAAA,SAAAiB,UAAA,CAAAC,aAAA,IAAAD,UAAA,CAAAC,aAAA,GAAAD,UAAA,CAAAwB,KAAA,CAAoE;IAMtDhD,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,KAAAyC,GAAA,EAAc;IAKRjD,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAkD,kBAAA,MAAA1B,UAAA,CAAAI,MAAA,CAAAuB,KAAA,MAA4B;;;;;;IAlFnEnD,EAAA,CAAAE,cAAA,cAIC;IADCF,EAAA,CAAAa,UAAA,mBAAAuC,mEAAA;MAAA,MAAAC,QAAA,GAAArD,EAAA,CAAAe,aAAA,CAAAuC,GAAA,EAAArB,SAAA;MAAA,MAAAhB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAsC,YAAA,CAAAF,QAAA,CAAmB;IAAA,EAAC;IAKzBrD,EAFJ,CAAAE,cAAA,cAA0B,cACA,aACC;IAAAF,EAAA,CAAAY,MAAA,GAAiB;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IAE3CH,EADF,CAAAE,cAAA,cAAyB,cACA;IACrBF,EAAA,CAAAC,SAAA,mBAAwC;IACxCD,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAY,MAAA,GAAiC;IACzCZ,EADyC,CAAAG,YAAA,EAAO,EAC1C;IACNH,EAAA,CAAAE,cAAA,eAAuB;IACrBF,EAAA,CAAAC,SAAA,oBAAiC;IACjCD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAY,MAAA,IAAuB;IAC/BZ,EAD+B,CAAAG,YAAA,EAAO,EAChC;IACNH,EAAA,CAAAE,cAAA,eAAuB;IACrBF,EAAA,CAAAC,SAAA,oBAAwC;IACxCD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAY,MAAA,IAA0C;IAGtDZ,EAHsD,CAAAG,YAAA,EAAO,EACnD,EACF,EACF;IACNH,EAAA,CAAAE,cAAA,eAAyB;IACvBF,EAAA,CAAAC,SAAA,oBAAoC;IACpCD,EAAA,CAAAY,MAAA,kBACF;IACFZ,EADE,CAAAG,YAAA,EAAM,EACF;IAIJH,EADF,CAAAE,cAAA,eAA0B,cACG;IAAAF,EAAA,CAAAY,MAAA,oBAAY;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAE,cAAA,eAA2B;IACzBF,EAAA,CAAAI,UAAA,KAAAoD,oDAAA,oBAIC;IAiDLxD,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAFJ,CAAAE,cAAA,eAA+B,kBACC,YACtB;IAAAF,EAAA,CAAAY,MAAA,IAAmC;IAAAZ,EAAA,CAAAG,YAAA,EAAO;IAChDH,EAAA,CAAAC,SAAA,oBAA4C;IAGlDD,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;;IAxFuBH,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAAqB,iBAAA,CAAAgC,QAAA,CAAAI,KAAA,CAAiB;IAI9BzD,EAAA,CAAAM,SAAA,GAAiC;IAAjCN,EAAA,CAAAkD,kBAAA,KAAAG,QAAA,CAAAK,YAAA,cAAiC;IAIjC1D,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAkD,kBAAA,KAAAG,QAAA,CAAAM,SAAA,OAAuB;IAIvB3D,EAAA,CAAAM,SAAA,GAA0C;IAA1CN,EAAA,CAAAkD,kBAAA,KAAAjC,MAAA,CAAA2C,YAAA,CAAAP,QAAA,CAAAQ,UAAA,YAA0C;IAe9B7D,EAAA,CAAAM,SAAA,GAAsB;IAAAN,EAAtB,CAAAO,UAAA,YAAA8C,QAAA,CAAAS,WAAA,CAAsB,iBAAA7C,MAAA,CAAA8C,gBAAA,CAAyB;IAyD/D/D,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAAkD,kBAAA,cAAAG,QAAA,CAAAI,KAAA,cAAmC;;;;;IA7FjDzD,EAAA,CAAAE,cAAA,cAAmF;IACjFF,EAAA,CAAAI,UAAA,IAAA4D,6CAAA,mBAIC;IA6FHhE,EAAA,CAAAG,YAAA,EAAM;;;;IAhGgBH,EAAA,CAAAM,SAAA,EAAmB;IAAAN,EAAnB,CAAAO,UAAA,YAAAU,MAAA,CAAAgD,cAAA,CAAmB,iBAAAhD,MAAA,CAAAiD,gBAAA,CAAyB;;;;;IAmGlElE,EAAA,CAAAE,cAAA,cAAyF;IACvFF,EAAA,CAAAC,SAAA,mBAA+D;IAC/DD,EAAA,CAAAE,cAAA,aAAwB;IAAAF,EAAA,CAAAY,MAAA,yBAAkB;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IAC/CH,EAAA,CAAAE,cAAA,YAAyB;IAAAF,EAAA,CAAAY,MAAA,sDAA+C;IAC1EZ,EAD0E,CAAAG,YAAA,EAAI,EACxE;;;AD/HR,OAAM,MAAOgE,uBAAuB;EAOlCC,YACUC,eAAgC,EAChCC,aAAwC,EACxCC,MAAc;IAFd,KAAAF,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IAThB,KAAAN,cAAc,GAAoB,EAAE;IACpC,KAAAO,SAAS,GAAG,IAAI;IAChB,KAAAlD,KAAK,GAAkB,IAAI;IAC3B,KAAAmD,aAAa,GAAG,IAAIC,GAAG,EAAU;IACzB,KAAAC,YAAY,GAAiB,IAAI7E,YAAY,EAAE;EAMpD;EAEH8E,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACL,YAAY,CAACM,WAAW,EAAE;EACjC;EAEQH,uBAAuBA,CAAA;IAC7B,IAAI,CAACH,YAAY,CAACO,GAAG,CACnB,IAAI,CAACb,eAAe,CAACc,eAAe,CAACC,SAAS,CAACC,MAAM,IAAG;MACtD,IAAI,CAACpB,cAAc,GAAGoB,MAAM;MAC5B,IAAI,CAACb,SAAS,GAAG,KAAK;IACxB,CAAC,CAAC,CACH;EACH;EAEQO,sBAAsBA,CAAA;IAC5B,IAAI,CAACJ,YAAY,CAACO,GAAG,CACnB,IAAI,CAACZ,aAAa,CAACgB,cAAc,CAACF,SAAS,CAACX,aAAa,IAAG;MAC1D,IAAI,CAACA,aAAa,GAAGA,aAAa;IACpC,CAAC,CAAC,CACH;EACH;EAEcI,kBAAkBA,CAAA;IAAA,IAAAU,KAAA;IAAA,OAAAC,iBAAA;MAC9B,IAAI;QACFD,KAAI,CAACf,SAAS,GAAG,IAAI;QACrBe,KAAI,CAACjE,KAAK,GAAG,IAAI;QACjB,MAAMiE,KAAI,CAAClB,eAAe,CAACQ,kBAAkB,EAAE;OAChD,CAAC,OAAOvD,KAAK,EAAE;QACdmE,OAAO,CAACnE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtDiE,KAAI,CAACjE,KAAK,GAAG,gCAAgC;QAC7CiE,KAAI,CAACf,SAAS,GAAG,KAAK;;IACvB;EACH;EAEAjB,YAAYA,CAACE,KAAoB;IAC/B,IAAI,CAACc,MAAM,CAACmB,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;MAClCC,WAAW,EAAE;QAAElC,KAAK,EAAEA,KAAK,CAACA;MAAK;KAClC,CAAC;EACJ;EAEAvB,cAAcA,CAAC0D,OAAgB,EAAEC,KAAY;IAC3CA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,CAACvB,MAAM,CAACmB,QAAQ,CAAC,CAAC,UAAU,EAAEE,OAAO,CAAC7C,GAAG,CAAC,CAAC;EACjD;EAEMX,aAAaA,CAACwD,OAAgB,EAAEC,KAAY;IAAA,IAAAE,MAAA;IAAA,OAAAP,iBAAA;MAChDK,KAAK,CAACC,eAAe,EAAE;MACvB,IAAI;QACF,MAAME,MAAM,SAASD,MAAI,CAACzB,aAAa,CAAC2B,WAAW,CAACL,OAAO,CAAC7C,GAAG,CAAC;QAChE,IAAIiD,MAAM,CAACE,OAAO,EAAE;UAClBT,OAAO,CAACU,GAAG,CAACH,MAAM,CAACI,OAAO,CAAC;SAC5B,MAAM;UACLX,OAAO,CAACnE,KAAK,CAAC,yBAAyB,EAAE0E,MAAM,CAACI,OAAO,CAAC;;OAE3D,CAAC,OAAO9E,KAAK,EAAE;QACdmE,OAAO,CAACnE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;IAC9C;EACH;EAEMgB,cAAcA,CAACsD,OAAgB,EAAEC,KAAY;IAAA,IAAAQ,MAAA;IAAA,OAAAb,iBAAA;MACjDK,KAAK,CAACC,eAAe,EAAE;MACvB,IAAI;QACF,MAAMQ,UAAU,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,YAAYb,OAAO,CAAC7C,GAAG,EAAE;QACrE,MAAM2D,SAAS,CAACC,SAAS,CAACC,SAAS,CAACN,UAAU,CAAC;QAE/C,MAAMD,MAAI,CAAC/B,aAAa,CAACuC,YAAY,CAACjB,OAAO,CAAC7C,GAAG,EAAE;UACjD+D,QAAQ,EAAE,WAAW;UACrBV,OAAO,EAAE,0BAA0BR,OAAO,CAAC/C,IAAI,SAAS+C,OAAO,CAACnC,KAAK;SACtE,CAAC;QAEFgC,OAAO,CAACU,GAAG,CAAC,mCAAmC,CAAC;OACjD,CAAC,OAAO7E,KAAK,EAAE;QACdmE,OAAO,CAACnE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;IAC/C;EACH;EAEAC,WAAWA,CAACyB,KAAa;IACvB,OAAO,IAAI+D,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACpE,KAAK,CAAC;EAClB;EAEAY,YAAYA,CAACyD,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EAEAnG,OAAOA,CAAA;IACL,IAAI,CAACyD,kBAAkB,EAAE;EAC3B;EAEAX,gBAAgBA,CAACsD,KAAa,EAAE/D,KAAoB;IAClD,OAAOA,KAAK,CAACA,KAAK;EACpB;EAEAX,cAAcA,CAAC2E,SAAiB;IAC9B,OAAO,IAAI,CAAChD,aAAa,CAACiD,GAAG,CAACD,SAAS,CAAC;EAC1C;EAEA1D,gBAAgBA,CAACyD,KAAa,EAAE5B,OAAgB;IAC9C,OAAOA,OAAO,CAAC7C,GAAG;EACpB;;;uBA7HWoB,uBAAuB,EAAAnE,EAAA,CAAA2H,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAA7H,EAAA,CAAA2H,iBAAA,CAAAG,EAAA,CAAAC,yBAAA,GAAA/H,EAAA,CAAA2H,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAvB9D,uBAAuB;MAAA+D,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAApI,EAAA,CAAAqI,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ9B3I,EAJN,CAAAE,cAAA,aAAuC,aAET,aACE,YACA;UACxBF,EAAA,CAAAC,SAAA,kBAAuD;UACvDD,EAAA,CAAAY,MAAA,wBACF;UAAAZ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAE,cAAA,WAA4B;UAAAF,EAAA,CAAAY,MAAA,0CAAmC;UAEnEZ,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAiINH,EA9HA,CAAAI,UAAA,IAAAyI,sCAAA,iBAAiD,IAAAC,sCAAA,iBAeQ,KAAAC,uCAAA,iBAU0B,KAAAC,uCAAA,iBAqGM;UAK3FhJ,EAAA,CAAAG,YAAA,EAAM;;;UAnIEH,EAAA,CAAAM,SAAA,GAAe;UAAfN,EAAA,CAAAO,UAAA,SAAAqI,GAAA,CAAApE,SAAA,CAAe;UAefxE,EAAA,CAAAM,SAAA,EAAyB;UAAzBN,EAAA,CAAAO,UAAA,SAAAqI,GAAA,CAAAtH,KAAA,KAAAsH,GAAA,CAAApE,SAAA,CAAyB;UAUzBxE,EAAA,CAAAM,SAAA,EAAuD;UAAvDN,EAAA,CAAAO,UAAA,UAAAqI,GAAA,CAAApE,SAAA,KAAAoE,GAAA,CAAAtH,KAAA,IAAAsH,GAAA,CAAA3E,cAAA,CAAAgF,MAAA,KAAuD;UAqGvDjJ,EAAA,CAAAM,SAAA,EAAyD;UAAzDN,EAAA,CAAAO,UAAA,UAAAqI,GAAA,CAAApE,SAAA,KAAAoE,GAAA,CAAAtH,KAAA,IAAAsH,GAAA,CAAA3E,cAAA,CAAAgF,MAAA,OAAyD;;;qBD/HrDpJ,YAAY,EAAAqJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAErJ,WAAW,EAAAsJ,EAAA,CAAAC,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}