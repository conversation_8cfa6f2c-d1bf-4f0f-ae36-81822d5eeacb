<ion-header [translucent]="true">
  <ion-toolbar color="primary">
    <ion-title>Categories</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="clearFilters()" *ngIf="selectedCategory || searchQuery || selectedBrands.length > 0">
        <ion-icon name="refresh"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-refresher slot="fixed" (ionRefresh)="doRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <!-- Search Bar -->
  <div class="search-section">
    <ion-searchbar
      [(ngModel)]="searchQuery"
      (ionInput)="onSearchChange()"
      placeholder="Search products..."
      debounce="300">
    </ion-searchbar>
  </div>

  <!-- Categories Grid -->
  <div class="categories-section">
    <h3>Shop by Category</h3>
    <div class="categories-grid">
      <div 
        *ngFor="let category of categories" 
        class="category-item"
        [class.selected]="selectedCategory === category.id"
        (click)="onCategorySelect(category)">
        <div class="category-icon" [style.background]="'var(--ion-color-' + category.color + ')'">
          <ion-icon [name]="category.icon"></ion-icon>
        </div>
        <span>{{ category.name }}</span>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="filters-section">
    <ion-segment [(ngModel)]="sortBy" (ionChange)="onSortChange()">
      <ion-segment-button value="name">
        <ion-label>Name</ion-label>
      </ion-segment-button>
      <ion-segment-button value="price-low">
        <ion-label>Price ↑</ion-label>
      </ion-segment-button>
      <ion-segment-button value="price-high">
        <ion-label>Price ↓</ion-label>
      </ion-segment-button>
      <ion-segment-button value="rating">
        <ion-label>Rating</ion-label>
      </ion-segment-button>
    </ion-segment>
  </div>

  <!-- Price Range Filter -->
  <div class="price-filter">
    <ion-item>
      <ion-label>Price Range: ₹{{ priceRange.lower }} - ₹{{ priceRange.upper }}</ion-label>
    </ion-item>
    <ion-range
      dual-knobs="true"
      min="0"
      max="10000"
      step="100"
      [(ngModel)]="priceRange"
      (ionChange)="onPriceRangeChange()">
    </ion-range>
  </div>

  <!-- Brand Filters -->
  <div class="brand-filters" *ngIf="availableBrands.length > 0">
    <h4>Brands</h4>
    <div class="brand-chips">
      <ion-chip 
        *ngFor="let brand of availableBrands" 
        [color]="selectedBrands.includes(brand) ? 'primary' : 'medium'"
        (click)="onBrandToggle(brand)">
        <ion-label>{{ brand }}</ion-label>
      </ion-chip>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <ion-spinner name="crescent"></ion-spinner>
    <p>Loading products...</p>
  </div>

  <!-- Products Grid -->
  <div *ngIf="!isLoading" class="products-section">
    <div class="section-header">
      <h3>Products ({{ filteredProducts.length }})</h3>
    </div>

    <!-- Empty State -->
    <div *ngIf="filteredProducts.length === 0" class="empty-state">
      <ion-icon name="search" color="medium"></ion-icon>
      <h3>No products found</h3>
      <p>Try adjusting your filters</p>
      <ion-button fill="outline" (click)="clearFilters()">Clear Filters</ion-button>
    </div>

    <!-- Products Grid -->
    <div class="products-grid" *ngIf="filteredProducts.length > 0">
      <div 
        *ngFor="let product of filteredProducts" 
        class="product-card"
        (click)="onProductClick(product)">
        <div class="product-image">
          <img [src]="getProductImage(product)" [alt]="product.name">
          <div class="product-badge" *ngIf="getDiscountPercentage(product) > 0">
            <span>{{ getDiscountPercentage(product) }}% OFF</span>
          </div>
          <div class="product-actions">
            <ion-button 
              fill="clear" 
              size="small" 
              (click)="addToWishlist(product, $event)">
              <ion-icon name="heart-outline"></ion-icon>
            </ion-button>
            <ion-button 
              fill="clear" 
              size="small" 
              (click)="addToCart(product, $event)">
              <ion-icon name="bag-outline"></ion-icon>
            </ion-button>
          </div>
        </div>
        <div class="product-info">
          <h4>{{ product.name }}</h4>
          <p class="brand">{{ product.brand }}</p>
          <div class="product-price">
            <span class="current-price">₹{{ product.price }}</span>
            <span class="original-price" *ngIf="product.originalPrice && product.originalPrice > product.price">
              ₹{{ product.originalPrice }}
            </span>
          </div>
          <div class="product-rating" *ngIf="product.rating?.average">
            <ion-icon name="star" *ngFor="let star of [1,2,3,4,5]"
              [class.filled]="star <= product.rating.average"></ion-icon>
            <span>({{ product.rating.count }})</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Bottom Spacing -->
  <div class="bottom-spacing"></div>
</ion-content>
