import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { TrendingService } from '../../../../core/services/trending.service';
import { Product } from '../../../../core/models/product.model';
import { SocialInteractionsService } from '../../../../core/services/social-interactions.service';
import { CartService } from '../../../../core/services/cart.service';
import { WishlistService } from '../../../../core/services/wishlist.service';
import { IonicModule } from '@ionic/angular';
import { CarouselModule } from 'ngx-owl-carousel-o';

@Component({
  selector: 'app-trending-products',
  standalone: true,
  imports: [CommonModule, IonicModule, CarouselModule],
  templateUrl: './trending-products.component.html',
  styleUrls: ['./trending-products.component.scss']
})
export class TrendingProductsComponent implements OnInit, OnDestroy {
  trendingProducts: Product[] = [];
  isLoading = true;
  error: string | null = null;
  likedProducts = new Set<string>();
  private subscription: Subscription = new Subscription();

  // Slider properties
  currentSlide = 0;
  translateX = 0;
  cardWidth = 280; // Width of each product card including margin
  visibleCards = 4; // Number of cards visible at once
  dots: number[] = [];

  get maxSlide(): number {
    return Math.max(0, Math.ceil(this.trendingProducts.length / this.visibleCards) - 1);
  }

  constructor(
    private trendingService: TrendingService,
    private socialService: SocialInteractionsService,
    private cartService: CartService,
    private wishlistService: WishlistService,
    private router: Router
  ) {
    // Listen for window resize
    window.addEventListener('resize', () => {
      this.updateResponsiveSettings();
      this.generateDots();
      this.currentSlide = 0; // Reset to first slide on resize
      this.updateTranslateX();
    });
  }

  ngOnInit() {
    this.loadTrendingProducts();
    this.subscribeTrendingProducts();
    this.subscribeLikedProducts();
    this.updateResponsiveSettings();
    this.generateDots();
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  private subscribeTrendingProducts() {
    this.subscription.add(
      this.trendingService.trendingProducts$.subscribe(products => {
        this.trendingProducts = products;
        this.isLoading = false;
        this.generateDots(); // Regenerate dots when products change
        this.currentSlide = 0; // Reset to first slide
        this.updateTranslateX();
      })
    );
  }

  private subscribeLikedProducts() {
    this.subscription.add(
      this.socialService.likedProducts$.subscribe(likedProducts => {
        this.likedProducts = likedProducts;
      })
    );
  }

  private async loadTrendingProducts() {
    try {
      this.isLoading = true;
      this.error = null;
      await this.trendingService.loadTrendingProducts(1, 8);
    } catch (error) {
      console.error('Error loading trending products:', error);
      this.error = 'Failed to load trending products';
      this.isLoading = false;
    }
  }

  onProductClick(product: Product) {
    this.router.navigate(['/product', product._id]);
  }

  async onLikeProduct(product: Product, event: Event) {
    event.stopPropagation();
    try {
      const result = await this.socialService.likeProduct(product._id);
      if (result.success) {
        console.log(result.message);
      } else {
        console.error('Failed to like product:', result.message);
      }
    } catch (error) {
      console.error('Error liking product:', error);
    }
  }

  async onShareProduct(product: Product, event: Event) {
    event.stopPropagation();
    try {
      // For now, copy link to clipboard
      const productUrl = `${window.location.origin}/product/${product._id}`;
      await navigator.clipboard.writeText(productUrl);

      // Track the share
      await this.socialService.shareProduct(product._id, {
        platform: 'copy_link',
        message: `Check out this amazing ${product.name} from ${product.brand}!`
      });

      console.log('Product link copied to clipboard!');
    } catch (error) {
      console.error('Error sharing product:', error);
    }
  }

  async onAddToCart(product: Product, event: Event) {
    event.stopPropagation();
    try {
      await this.cartService.addToCart(product._id, 1);
      console.log('Product added to cart!');
    } catch (error) {
      console.error('Error adding to cart:', error);
    }
  }

  async onAddToWishlist(product: Product, event: Event) {
    event.stopPropagation();
    try {
      await this.wishlistService.addToWishlist(product._id);
      console.log('Product added to wishlist!');
    } catch (error) {
      console.error('Error adding to wishlist:', error);
    }
  }

  getDiscountPercentage(product: Product): number {
    if (product.originalPrice && product.originalPrice > product.price) {
      return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);
    }
    return 0;
  }

  formatPrice(price: number): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0
    }).format(price);
  }

  onRetry() {
    this.loadTrendingProducts();
  }

  onViewAll() {
    this.router.navigate(['/products'], {
      queryParams: { filter: 'trending' }
    });
  }

  isProductLiked(productId: string): boolean {
    return this.likedProducts.has(productId);
  }

  trackByProductId(index: number, product: Product): string {
    return product._id;
  }

  // Slider Methods
  updateResponsiveSettings() {
    const width = window.innerWidth;
    if (width < 576) {
      this.visibleCards = 1;
      this.cardWidth = width - 40; // Account for padding
    } else if (width < 768) {
      this.visibleCards = 2;
      this.cardWidth = 280;
    } else if (width < 992) {
      this.visibleCards = 3;
      this.cardWidth = 280;
    } else {
      this.visibleCards = 4;
      this.cardWidth = 280;
    }
  }

  generateDots() {
    const totalDots = Math.ceil(this.trendingProducts.length / this.visibleCards);
    this.dots = Array(totalDots).fill(0).map((_, i) => i);
  }

  slidePrev() {
    if (this.currentSlide > 0) {
      this.currentSlide--;
      this.updateTranslateX();
    }
  }

  slideNext() {
    if (this.currentSlide < this.maxSlide) {
      this.currentSlide++;
      this.updateTranslateX();
    }
  }

  goToSlide(slideIndex: number) {
    this.currentSlide = slideIndex;
    this.updateTranslateX();
  }

  private updateTranslateX() {
    this.translateX = -(this.currentSlide * this.visibleCards * this.cardWidth);
  }
}
