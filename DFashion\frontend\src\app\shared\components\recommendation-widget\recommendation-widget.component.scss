.recommendation-widget {
  background: var(--ion-color-light);
  border-radius: 12px;
  padding: 16px;
  margin: 16px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .header-content {
      display: flex;
      align-items: center;
      gap: 8px;

      ion-icon {
        font-size: 20px;
      }

      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--ion-color-dark);
      }
    }
  }

  .loading-container,
  .error-container,
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px 16px;
    text-align: center;

    ion-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    p {
      margin: 8px 0 16px 0;
      color: var(--ion-color-medium);
    }
  }

  .recommendations-container {
    &.horizontal-scroll {
      display: flex;
      gap: 16px;
      overflow-x: auto;
      padding-bottom: 8px;
      scroll-behavior: smooth;

      &::-webkit-scrollbar {
        height: 4px;
      }

      &::-webkit-scrollbar-track {
        background: var(--ion-color-light-shade);
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb {
        background: var(--ion-color-medium);
        border-radius: 2px;
      }

      .recommendation-card {
        flex: 0 0 280px;
        max-width: 280px;
      }
    }

    &.grid-layout {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 16px;
    }
  }

  .recommendation-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    }

    .product-image {
      position: relative;
      width: 100%;
      height: 200px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      &:hover img {
        transform: scale(1.05);
      }

      .quick-actions {
        position: absolute;
        top: 8px;
        right: 8px;
        display: flex;
        flex-direction: column;
        gap: 4px;
        opacity: 0;
        transition: opacity 0.3s ease;

        .action-btn {
          --background: rgba(255, 255, 255, 0.9);
          --color: var(--ion-color-dark);
          width: 32px;
          height: 32px;
          border-radius: 50%;
          backdrop-filter: blur(10px);

          &:hover {
            --background: white;
          }
        }
      }

      &:hover .quick-actions {
        opacity: 1;
      }

      .recommendation-badge {
        position: absolute;
        bottom: 8px;
        left: 8px;
        background: var(--ion-color-primary);
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 10px;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        span {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 120px;
          display: block;
        }
      }
    }

    .product-info {
      padding: 12px;

      .product-name {
        font-size: 14px;
        font-weight: 600;
        margin: 0 0 8px 0;
        color: var(--ion-color-dark);
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-height: 1.3;
      }

      .product-meta {
        display: flex;
        gap: 8px;
        margin-bottom: 8px;

        .brand,
        .category {
          font-size: 11px;
          padding: 2px 6px;
          border-radius: 8px;
          background: var(--ion-color-light);
          color: var(--ion-color-medium);
          text-transform: uppercase;
          font-weight: 500;
        }

        .brand {
          background: var(--ion-color-primary-tint);
          color: var(--ion-color-primary);
        }
      }

      .price-rating {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .price {
          display: flex;
          align-items: center;
          gap: 6px;

          .current-price {
            font-size: 16px;
            font-weight: 700;
            color: var(--ion-color-primary);
          }

          .original-price {
            font-size: 12px;
            color: var(--ion-color-medium);
            text-decoration: line-through;
          }
        }

        .rating {
          display: flex;
          align-items: center;
          gap: 2px;
          font-size: 12px;

          ion-icon {
            font-size: 14px;
          }

          .review-count {
            color: var(--ion-color-medium);
          }
        }
      }

      .vendor-info {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-bottom: 12px;

        .vendor-avatar {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          object-fit: cover;
        }

        .vendor-name {
          font-size: 11px;
          color: var(--ion-color-medium);
          font-weight: 500;
        }
      }

      .action-buttons {
        display: flex;
        gap: 8px;
        align-items: center;

        .add-to-cart-btn {
          flex: 1;
          --border-radius: 8px;
          height: 32px;
          font-size: 12px;
          font-weight: 600;
        }

        .share-btn {
          --color: var(--ion-color-medium);
          width: 32px;
          height: 32px;
        }
      }

      .recommendation-score {
        margin-top: 8px;
        font-size: 10px;
        color: var(--ion-color-medium);
        text-align: center;
        padding: 4px;
        background: var(--ion-color-light);
        border-radius: 4px;
      }
    }
  }

  // Mobile optimizations
  @media (max-width: 768px) {
    padding: 12px;

    .recommendations-container.grid-layout {
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 12px;
    }

    .recommendation-card {
      .product-image {
        height: 160px;
      }

      .product-info {
        padding: 10px;

        .product-name {
          font-size: 13px;
        }

        .price-rating .price .current-price {
          font-size: 14px;
        }
      }
    }
  }

  // Layout specific styles
  &.layout-horizontal {
    .recommendations-container {
      margin: 0 -16px;
      padding: 0 16px;
    }
  }

  &.layout-grid {
    .recommendation-card:hover {
      transform: translateY(-4px);
    }
  }
}
