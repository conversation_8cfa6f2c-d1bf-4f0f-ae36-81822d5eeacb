{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class MediaService {\n  constructor() {\n    this.mediaErrors = new BehaviorSubject([]);\n    this.mediaErrors$ = this.mediaErrors.asObservable();\n    // Fallback images for different scenarios\n    this.fallbackImages = {\n      user: '/assets/images/default-avatar.svg',\n      product: '/assets/images/default-product.svg',\n      post: '/assets/images/default-post.svg',\n      story: '/assets/images/default-story.svg'\n    };\n    // Backup fallback images (simple colored placeholders)\n    this.backupFallbacks = {\n      user: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFNUU3RUIiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik0xMiAxNEM5LjMzIDEzLjk5IDcuMDEgMTUuNjIgNiAxOEMxMC4wMSAyMCAxMy45OSAyMCAxOCAxOEMxNi45OSAxNS42MiAxNC42NyAxMy45OSAxMiAxNFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+Cjwvc3ZnPgo=',\n      product: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyOEMxNi42ODYzIDI4IDEzLjUwNTQgMjYuNjgzOSAxMS4xNzE2IDI0LjM1MDNDOC44Mzc4NCAyMi4wMTY3IDcuNTIxNzMgMTguODM1OCA3LjUyMTczIDE1LjUyMTdDNy41MjE3MyAxMi4yMDc2IDguODM3ODQgOS4wMjY3IDExLjE3MTYgNi42OTMwNEMxMy41MDU0IDQuMzU5MzggMTYuNjg2MyAzLjA0MzQ4IDIwIDMuMDQzNDhDMjMuMzEzNyAzLjA0MzQ4IDI2LjQ5NDYgNC4zNTkzOCAyOC44Mjg0IDYuNjkzMDRDMzEuMTYyMiA5LjAyNjcgMzIuNDc4MyAxMi4yMDc2IDMyLjQ3ODMgMTUuNTIxN0MzMi40NzgzIDE4LjgzNTggMzEuMTYyMiAyMi4wMTY3IDI4LjgyODQgMjQuMzUwM0MyNi40OTQ2IDI2LjY4MzkgMjMuMzEzNyAyOCAyMCAyOFoiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+',\n      post: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRkJGQ0ZEIi8+CjxwYXRoIGQ9Ik0yMCAyOEMxNi42ODYzIDI4IDEzLjUwNTQgMjYuNjgzOSAxMS4xNzE2IDI0LjM1MDNDOC44Mzc4NCAyMi4wMTY3IDcuNTIxNzMgMTguODM1OCA3LjUyMTczIDE1LjUyMTdDNy41MjE3MyAxMi4yMDc2IDguODM3ODQgOS4wMjY3IDExLjE3MTYgNi42OTMwNEMxMy41MDU0IDQuMzU5MzggMTYuNjg2MyAzLjA0MzQ4IDIwIDMuMDQzNDhDMjMuMzEzNyAzLjA0MzQ4IDI2LjQ5NDYgNC4zNTkzOCAyOC44Mjg0IDYuNjkzMDRDMzEuMTYyMiA5LjAyNjcgMzIuNDc4MyAxMi4yMDc2IDMyLjQ3ODMgMTUuNTIxN0MzMi40NzgzIDE4LjgzNTggMzEuMTYyMiAyMi4wMTY3IDI4LjgyODQgMjQuMzUwM0MyNi40OTQ2IDI2LjY4MzkgMjMuMzEzNyAyOCAyMCAyOFoiIGZpbGw9IiNFNUU3RUIiLz4KPC9zdmc+',\n      story: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRkVGM0Y0Ii8+CjxwYXRoIGQ9Ik0yMCAyOEMxNi42ODYzIDI4IDEzLjUwNTQgMjYuNjgzOSAxMS4xNzE2IDI0LjM1MDNDOC44Mzc4NCAyMi4wMTY3IDcuNTIxNzMgMTguODM1OCA3LjUyMTczIDE1LjUyMTdDNy41MjE3MyAxMi4yMDc2IDguODM3ODQgOS4wMjY3IDExLjE3MTYgNi42OTMwNEMxMy41MDU0IDQuMzU5MzggMTYuNjg2MyAzLjA0MzQ4IDIwIDMuMDQzNDhDMjMuMzEzNyAzLjA0MzQ4IDI2LjQ5NDYgNC4zNTkzOCAyOC44Mjg0IDYuNjkzMDRDMzEuMTYyMiA5LjAyNjcgMzIuNDc4MyAxMi4yMDc2IDMyLjQ3ODMgMTUuNTIxN0MzMi40NzgzIDE4LjgzNTggMzEuMTYyMiAyMi4wMTY3IDI4LjgyODQgMjQuMzUwM0MyNi40OTQ2IDI2LjY4MzkgMjMuMzEzNyAyOCAyMCAyOFoiIGZpbGw9IiNGQ0E1QTUiLz4KPC9zdmc+'\n    };\n    // Video library - loaded from API\n    this.videos = [];\n    // Broken URL patterns to fix\n    this.brokenUrlPatterns = ['/uploads/stories/images/', '/uploads/stories/videos/', 'sample-videos.com', 'localhost:4200/assets/', 'file://'];\n  }\n  /**\n   * Get a reliable fallback image that always works\n   */\n  getReliableFallback(type = 'post') {\n    return this.backupFallbacks[type];\n  }\n  /**\n   * Check if an image URL is likely to fail\n   */\n  isLikelyToFail(url) {\n    return this.isExternalImageUrl(url) || this.isBrokenUrl(url);\n  }\n  /**\n   * Get a safe image URL with fallback handling and broken URL fixing\n   */\n  getSafeImageUrl(url, type = 'post') {\n    if (!url || url.trim() === '') {\n      return this.backupFallbacks[type]; // Use base64 fallback for empty URLs\n    }\n    // Fix broken URLs\n    const fixedUrl = this.fixBrokenUrl(url, type);\n    if (fixedUrl !== url) {\n      return fixedUrl;\n    }\n    // Handle localhost URLs that might be broken\n    if (url.includes('localhost:4200/assets/')) {\n      const assetPath = url.split('localhost:4200')[1];\n      return assetPath;\n    }\n    // For external images that might fail, provide a more reliable fallback\n    if (this.isExternalImageUrl(url)) {\n      // Return the URL but we know it might fail and will fallback gracefully\n      return url;\n    }\n    // Check if URL is valid\n    try {\n      new URL(url);\n      return url;\n    } catch {\n      // If not a valid URL, treat as relative path\n      if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {\n        return url;\n      }\n      // Return base64 fallback for invalid URLs\n      return this.backupFallbacks[type];\n    }\n  }\n  /**\n   * Fix broken URLs by replacing them with working alternatives\n   */\n  fixBrokenUrl(url, type) {\n    // Check for broken patterns\n    for (const pattern of this.brokenUrlPatterns) {\n      if (url.includes(pattern)) {\n        // Replace with appropriate fallback or working URL\n        if (pattern === '/uploads/stories/images/' || pattern === '/uploads/stories/videos/') {\n          return this.getReplacementMediaUrl(url, type);\n        }\n        if (pattern === 'sample-videos.com') {\n          return this.getReliableFallback(type);\n        }\n        if (pattern === 'localhost:4200/assets/') {\n          // Extract the asset path and return it as relative\n          const assetPath = url.split('localhost:4200')[1];\n          return assetPath;\n        }\n        if (pattern === 'file://') {\n          return this.fallbackImages[type];\n        }\n      }\n    }\n    return url;\n  }\n  /**\n   * Get replacement media URL for broken local paths\n   */\n  getReplacementMediaUrl(originalUrl, type) {\n    // Map broken local URLs to working Unsplash URLs based on content\n    const urlMappings = {\n      'summer-collection': 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=800',\n      'behind-scenes': 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=800',\n      'customer-spotlight': 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=800',\n      'styling-tips': 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800',\n      'design': 'https://images.unsplash.com/photo-1558769132-cb1aea458c5e?w=800'\n    };\n    // Try to match content from filename\n    for (const [key, replacementUrl] of Object.entries(urlMappings)) {\n      if (originalUrl.toLowerCase().includes(key)) {\n        return replacementUrl;\n      }\n    }\n    // Return appropriate fallback\n    return this.fallbackImages[type];\n  }\n  /**\n   * Handle image load errors with progressive fallback\n   */\n  handleImageError(event, fallbackType = 'post') {\n    const img = event.target;\n    if (!img) return;\n    const originalSrc = img.src;\n    // First try: Use SVG fallback from assets\n    if (!originalSrc.includes(this.fallbackImages[fallbackType]) && !originalSrc.startsWith('data:')) {\n      img.src = this.fallbackImages[fallbackType];\n      // Only log meaningful errors (not external image failures)\n      if (!this.isExternalImageUrl(originalSrc) && !originalSrc.includes('localhost:4200')) {\n        this.logMediaError({\n          id: originalSrc,\n          type: 'load_error',\n          message: `Failed to load image: ${originalSrc}`,\n          fallbackUrl: this.fallbackImages[fallbackType]\n        });\n      }\n      return;\n    }\n    // Second try: Use base64 backup fallback if SVG also fails\n    if (originalSrc.includes(this.fallbackImages[fallbackType])) {\n      img.src = this.backupFallbacks[fallbackType];\n      // Only warn for local asset failures, not external\n      if (!this.isExternalImageUrl(originalSrc)) {\n        console.warn(`SVG fallback failed, using backup for ${fallbackType}:`, originalSrc);\n      }\n      return;\n    }\n  }\n  /**\n   * Check if URL is an external image (Unsplash, etc.)\n   */\n  isExternalImageUrl(url) {\n    const externalDomains = ['unsplash.com', 'images.unsplash.com', 'picsum.photos', 'via.placeholder.com', 'placehold.it', 'placeholder.com'];\n    return externalDomains.some(domain => url.includes(domain));\n  }\n  /**\n   * Check if URL is a video\n   */\n  isVideoUrl(url) {\n    if (!url) return false;\n    const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi'];\n    const lowerUrl = url.toLowerCase();\n    return videoExtensions.some(ext => lowerUrl.includes(ext)) || lowerUrl.includes('video') || lowerUrl.includes('.mp4');\n  }\n  /**\n   * Get video thumbnail\n   */\n  getVideoThumbnail(videoUrl) {\n    // For videos, use fallback\n    return this.fallbackImages.post;\n  }\n  /**\n   * Get video duration\n   */\n  getVideoDuration(videoUrl) {\n    return 30; // Default 30 seconds\n  }\n  /**\n   * Process media items from database\n   */\n  processMediaItems(mediaArray) {\n    if (!mediaArray || !Array.isArray(mediaArray)) {\n      return [];\n    }\n    return mediaArray.map((media, index) => {\n      const isVideo = this.isVideoUrl(media.url);\n      return {\n        id: media._id || `media_${index}`,\n        type: isVideo ? 'video' : 'image',\n        url: this.getSafeImageUrl(media.url),\n        thumbnailUrl: isVideo ? this.getVideoThumbnail(media.url) : undefined,\n        alt: media.alt || '',\n        duration: isVideo ? this.getVideoDuration(media.url) : undefined,\n        aspectRatio: media.aspectRatio || (isVideo ? 16 / 9 : 1),\n        size: media.size\n      };\n    });\n  }\n  /**\n   * Check if URL is broken\n   */\n  isBrokenUrl(url) {\n    return this.brokenUrlPatterns.some(pattern => url.includes(pattern));\n  }\n  /**\n   * Log media errors for debugging (with smart filtering)\n   */\n  logMediaError(error) {\n    const currentErrors = this.mediaErrors.value;\n    this.mediaErrors.next([...currentErrors, error]);\n    // Only log to console if it's not an external image failure\n    if (!this.isExternalImageUrl(error.id)) {\n      console.warn('Media Error:', error);\n    }\n  }\n  /**\n   * Clear media errors\n   */\n  clearMediaErrors() {\n    this.mediaErrors.next([]);\n  }\n  /**\n   * Preload media for better performance with graceful error handling\n   */\n  preloadMedia(mediaItems) {\n    const promises = mediaItems.map(media => {\n      return new Promise(resolve => {\n        if (media.type === 'image') {\n          const img = new Image();\n          img.onload = () => resolve();\n          img.onerror = () => {\n            // Only log errors for non-external images\n            if (!this.isExternalImageUrl(media.url)) {\n              console.warn(`Failed to preload image: ${media.url}`);\n            }\n            resolve(); // Resolve anyway to not break the promise chain\n          };\n          img.src = media.url;\n        } else if (media.type === 'video') {\n          const video = document.createElement('video');\n          video.onloadeddata = () => resolve();\n          video.onerror = () => {\n            // Only log errors for non-external videos\n            if (!this.isExternalImageUrl(media.url)) {\n              console.warn(`Failed to preload video: ${media.url}`);\n            }\n            resolve(); // Resolve anyway to not break the promise chain\n          };\n          video.src = media.url;\n          video.load();\n        } else {\n          resolve(); // Unknown type, just resolve\n        }\n      });\n    });\n    return Promise.all(promises);\n  }\n  /**\n   * Optimize image URL with size parameters\n   */\n  optimizeImageUrl(url, width, height, quality = 80) {\n    if (!url || this.isExternalImageUrl(url)) {\n      return url; // Don't modify external URLs\n    }\n    // For local images, we can add optimization parameters if the backend supports it\n    const params = new URLSearchParams();\n    if (width) params.append('w', width.toString());\n    if (height) params.append('h', height.toString());\n    if (quality !== 80) params.append('q', quality.toString());\n    const separator = url.includes('?') ? '&' : '?';\n    return params.toString() ? `${url}${separator}${params.toString()}` : url;\n  }\n  /**\n   * Get responsive image URLs for different screen sizes\n   */\n  getResponsiveImageUrls(url) {\n    if (!url) return {};\n    return {\n      thumbnail: this.optimizeImageUrl(url, 150, 150, 70),\n      small: this.optimizeImageUrl(url, 300, 300, 75),\n      medium: this.optimizeImageUrl(url, 600, 600, 80),\n      large: this.optimizeImageUrl(url, 1200, 1200, 85),\n      original: url\n    };\n  }\n  /**\n   * Create a lazy loading image element with proper error handling\n   */\n  createLazyImage(src, alt = '', className = '') {\n    const img = document.createElement('img');\n    img.alt = alt;\n    img.className = className;\n    img.loading = 'lazy';\n    // Set up error handling\n    img.onerror = event => {\n      if (typeof event === 'string') {\n        this.handleImageError(new Event('error'), 'post');\n      } else {\n        this.handleImageError(event, 'post');\n      }\n    };\n    // Use intersection observer for lazy loading\n    const observer = new IntersectionObserver(entries => {\n      entries.forEach(entry => {\n        if (entry.isIntersecting) {\n          img.src = this.getSafeImageUrl(src);\n          observer.unobserve(img);\n        }\n      });\n    }, {\n      rootMargin: '50px'\n    });\n    observer.observe(img);\n    return img;\n  }\n  /**\n   * Batch preload images with progress tracking\n   */\n  batchPreloadImages(urls, onProgress) {\n    let loaded = 0;\n    const total = urls.length;\n    const promises = urls.map(url => {\n      return new Promise(resolve => {\n        const img = new Image();\n        img.onload = img.onerror = () => {\n          loaded++;\n          if (onProgress) onProgress(loaded, total);\n          resolve();\n        };\n        img.src = this.getSafeImageUrl(url);\n      });\n    });\n    return Promise.all(promises).then(() => {});\n  }\n  static {\n    this.ɵfac = function MediaService_Factory(t) {\n      return new (t || MediaService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MediaService,\n      factory: MediaService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "MediaService", "constructor", "mediaErrors", "mediaErrors$", "asObservable", "fallbackImages", "user", "product", "post", "story", "backupFallbacks", "videos", "brokenUrlPatterns", "getReliable<PERSON><PERSON>back", "type", "isLikelyToFail", "url", "isExternalImageUrl", "isBrokenUrl", "getSafeImageUrl", "trim", "fixedUrl", "fixBrokenUrl", "includes", "assetPath", "split", "URL", "startsWith", "pattern", "getReplacementMediaUrl", "originalUrl", "urlMappings", "key", "replacementUrl", "Object", "entries", "toLowerCase", "handleImageError", "event", "fallbackType", "img", "target", "originalSrc", "src", "logMediaError", "id", "message", "fallbackUrl", "console", "warn", "externalDomains", "some", "domain", "isVideoUrl", "videoExtensions", "lowerUrl", "ext", "getVideoThumbnail", "videoUrl", "getVideoDuration", "processMediaItems", "mediaArray", "Array", "isArray", "map", "media", "index", "isVideo", "_id", "thumbnailUrl", "undefined", "alt", "duration", "aspectRatio", "size", "error", "currentErrors", "value", "next", "clearMediaErrors", "preloadMedia", "mediaItems", "promises", "Promise", "resolve", "Image", "onload", "onerror", "video", "document", "createElement", "onloadeddata", "load", "all", "optimizeImageUrl", "width", "height", "quality", "params", "URLSearchParams", "append", "toString", "separator", "getResponsiveImageUrls", "thumbnail", "small", "medium", "large", "original", "createLazyImage", "className", "loading", "Event", "observer", "IntersectionObserver", "for<PERSON>ach", "entry", "isIntersecting", "unobserve", "rootMargin", "observe", "batchPreloadImages", "urls", "onProgress", "loaded", "total", "length", "then", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\core\\services\\media.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\n\nexport interface MediaItem {\n  id: string;\n  type: 'image' | 'video';\n  url: string;\n  thumbnailUrl?: string;\n  alt?: string;\n  duration?: number; // for videos in seconds\n  aspectRatio?: number;\n  size?: number; // file size in bytes\n}\n\nexport interface MediaError {\n  id: string;\n  type: 'load_error' | 'network_error' | 'format_error';\n  message: string;\n  fallbackUrl?: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class MediaService {\n  private mediaErrors = new BehaviorSubject<MediaError[]>([]);\n  public mediaErrors$ = this.mediaErrors.asObservable();\n\n  // Fallback images for different scenarios\n  private readonly fallbackImages = {\n    user: '/assets/images/default-avatar.svg',\n    product: '/assets/images/default-product.svg',\n    post: '/assets/images/default-post.svg',\n    story: '/assets/images/default-story.svg'\n  };\n\n  // Backup fallback images (simple colored placeholders)\n  private readonly backupFallbacks = {\n    user: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFNUU3RUIiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik0xMiAxNEM5LjMzIDEzLjk5IDcuMDEgMTUuNjIgNiAxOEMxMC4wMSAyMCAxMy45OSAyMCAxOCAxOEMxNi45OSAxNS42MiAxNC42NyAxMy45OSAxMiAxNFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+Cjwvc3ZnPgo=',\n    product: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyOEMxNi42ODYzIDI4IDEzLjUwNTQgMjYuNjgzOSAxMS4xNzE2IDI0LjM1MDNDOC44Mzc4NCAyMi4wMTY3IDcuNTIxNzMgMTguODM1OCA3LjUyMTczIDE1LjUyMTdDNy41MjE3MyAxMi4yMDc2IDguODM3ODQgOS4wMjY3IDExLjE3MTYgNi42OTMwNEMxMy41MDU0IDQuMzU5MzggMTYuNjg2MyAzLjA0MzQ4IDIwIDMuMDQzNDhDMjMuMzEzNyAzLjA0MzQ4IDI2LjQ5NDYgNC4zNTkzOCAyOC44Mjg0IDYuNjkzMDRDMzEuMTYyMiA5LjAyNjcgMzIuNDc4MyAxMi4yMDc2IDMyLjQ3ODMgMTUuNTIxN0MzMi40NzgzIDE4LjgzNTggMzEuMTYyMiAyMi4wMTY3IDI4LjgyODQgMjQuMzUwM0MyNi40OTQ2IDI2LjY4MzkgMjMuMzEzNyAyOCAyMCAyOFoiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+',\n    post: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRkJGQ0ZEIi8+CjxwYXRoIGQ9Ik0yMCAyOEMxNi42ODYzIDI4IDEzLjUwNTQgMjYuNjgzOSAxMS4xNzE2IDI0LjM1MDNDOC44Mzc4NCAyMi4wMTY3IDcuNTIxNzMgMTguODM1OCA3LjUyMTczIDE1LjUyMTdDNy41MjE3MyAxMi4yMDc2IDguODM3ODQgOS4wMjY3IDExLjE3MTYgNi42OTMwNEMxMy41MDU0IDQuMzU5MzggMTYuNjg2MyAzLjA0MzQ4IDIwIDMuMDQzNDhDMjMuMzEzNyAzLjA0MzQ4IDI2LjQ5NDYgNC4zNTkzOCAyOC44Mjg0IDYuNjkzMDRDMzEuMTYyMiA5LjAyNjcgMzIuNDc4MyAxMi4yMDc2IDMyLjQ3ODMgMTUuNTIxN0MzMi40NzgzIDE4LjgzNTggMzEuMTYyMiAyMi4wMTY3IDI4LjgyODQgMjQuMzUwM0MyNi40OTQ2IDI2LjY4MzkgMjMuMzEzNyAyOCAyMCAyOFoiIGZpbGw9IiNFNUU3RUIiLz4KPC9zdmc+',\n    story: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRkVGM0Y0Ii8+CjxwYXRoIGQ9Ik0yMCAyOEMxNi42ODYzIDI4IDEzLjUwNTQgMjYuNjgzOSAxMS4xNzE2IDI0LjM1MDNDOC44Mzc4NCAyMi4wMTY3IDcuNTIxNzMgMTguODM1OCA3LjUyMTczIDE1LjUyMTdDNy41MjE3MyAxMi4yMDc2IDguODM3ODQgOS4wMjY3IDExLjE3MTYgNi42OTMwNEMxMy41MDU0IDQuMzU5MzggMTYuNjg2MyAzLjA0MzQ4IDIwIDMuMDQzNDhDMjMuMzEzNyAzLjA0MzQ4IDI2LjQ5NDYgNC4zNTkzOCAyOC44Mjg0IDYuNjkzMDRDMzEuMTYyMiA5LjAyNjcgMzIuNDc4MyAxMi4yMDc2IDMyLjQ3ODMgMTUuNTIxN0MzMi40NzgzIDE4LjgzNTggMzEuMTYyMiAyMi4wMTY3IDI4LjgyODQgMjQuMzUwM0MyNi40OTQ2IDI2LjY4MzkgMjMuMzEzNyAyOCAyMCAyOFoiIGZpbGw9IiNGQ0E1QTUiLz4KPC9zdmc+'\n  };\n\n  // Video library - loaded from API\n  private readonly videos: any[] = [];\n\n  // Broken URL patterns to fix\n  private readonly brokenUrlPatterns = [\n    '/uploads/stories/images/',\n    '/uploads/stories/videos/',\n    'sample-videos.com',\n    'localhost:4200/assets/',\n    'file://'\n  ];\n\n  constructor() {}\n\n  /**\n   * Get a reliable fallback image that always works\n   */\n  getReliableFallback(type: 'user' | 'product' | 'post' | 'story' = 'post'): string {\n    return this.backupFallbacks[type];\n  }\n\n  /**\n   * Check if an image URL is likely to fail\n   */\n  isLikelyToFail(url: string): boolean {\n    return this.isExternalImageUrl(url) || this.isBrokenUrl(url);\n  }\n\n  /**\n   * Get a safe image URL with fallback handling and broken URL fixing\n   */\n  getSafeImageUrl(url: string | undefined, type: 'user' | 'product' | 'post' | 'story' = 'post'): string {\n    if (!url || url.trim() === '') {\n      return this.backupFallbacks[type]; // Use base64 fallback for empty URLs\n    }\n\n    // Fix broken URLs\n    const fixedUrl = this.fixBrokenUrl(url, type);\n    if (fixedUrl !== url) {\n      return fixedUrl;\n    }\n\n    // Handle localhost URLs that might be broken\n    if (url.includes('localhost:4200/assets/')) {\n      const assetPath = url.split('localhost:4200')[1];\n      return assetPath;\n    }\n\n    // For external images that might fail, provide a more reliable fallback\n    if (this.isExternalImageUrl(url)) {\n      // Return the URL but we know it might fail and will fallback gracefully\n      return url;\n    }\n\n    // Check if URL is valid\n    try {\n      new URL(url);\n      return url;\n    } catch {\n      // If not a valid URL, treat as relative path\n      if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {\n        return url;\n      }\n      // Return base64 fallback for invalid URLs\n      return this.backupFallbacks[type];\n    }\n  }\n\n  /**\n   * Fix broken URLs by replacing them with working alternatives\n   */\n  private fixBrokenUrl(url: string, type: 'user' | 'product' | 'post' | 'story'): string {\n    // Check for broken patterns\n    for (const pattern of this.brokenUrlPatterns) {\n      if (url.includes(pattern)) {\n        // Replace with appropriate fallback or working URL\n        if (pattern === '/uploads/stories/images/' || pattern === '/uploads/stories/videos/') {\n          return this.getReplacementMediaUrl(url, type);\n        }\n        if (pattern === 'sample-videos.com') {\n          return this.getReliableFallback(type);\n        }\n        if (pattern === 'localhost:4200/assets/') {\n          // Extract the asset path and return it as relative\n          const assetPath = url.split('localhost:4200')[1];\n          return assetPath;\n        }\n        if (pattern === 'file://') {\n          return this.fallbackImages[type];\n        }\n      }\n    }\n    return url;\n  }\n\n  /**\n   * Get replacement media URL for broken local paths\n   */\n  private getReplacementMediaUrl(originalUrl: string, type: 'user' | 'product' | 'post' | 'story'): string {\n    // Map broken local URLs to working Unsplash URLs based on content\n    const urlMappings: { [key: string]: string } = {\n      'summer-collection': 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=800',\n      'behind-scenes': 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=800',\n      'customer-spotlight': 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=800',\n      'styling-tips': 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800',\n      'design': 'https://images.unsplash.com/photo-1558769132-cb1aea458c5e?w=800'\n    };\n\n    // Try to match content from filename\n    for (const [key, replacementUrl] of Object.entries(urlMappings)) {\n      if (originalUrl.toLowerCase().includes(key)) {\n        return replacementUrl;\n      }\n    }\n\n    // Return appropriate fallback\n    return this.fallbackImages[type];\n  }\n\n  /**\n   * Handle image load errors with progressive fallback\n   */\n  handleImageError(event: Event, fallbackType: 'user' | 'product' | 'post' | 'story' = 'post'): void {\n    const img = event.target as HTMLImageElement;\n    if (!img) return;\n\n    const originalSrc = img.src;\n\n    // First try: Use SVG fallback from assets\n    if (!originalSrc.includes(this.fallbackImages[fallbackType]) && !originalSrc.startsWith('data:')) {\n      img.src = this.fallbackImages[fallbackType];\n\n      // Only log meaningful errors (not external image failures)\n      if (!this.isExternalImageUrl(originalSrc) && !originalSrc.includes('localhost:4200')) {\n        this.logMediaError({\n          id: originalSrc,\n          type: 'load_error',\n          message: `Failed to load image: ${originalSrc}`,\n          fallbackUrl: this.fallbackImages[fallbackType]\n        });\n      }\n      return;\n    }\n\n    // Second try: Use base64 backup fallback if SVG also fails\n    if (originalSrc.includes(this.fallbackImages[fallbackType])) {\n      img.src = this.backupFallbacks[fallbackType];\n      // Only warn for local asset failures, not external\n      if (!this.isExternalImageUrl(originalSrc)) {\n        console.warn(`SVG fallback failed, using backup for ${fallbackType}:`, originalSrc);\n      }\n      return;\n    }\n  }\n\n  /**\n   * Check if URL is an external image (Unsplash, etc.)\n   */\n  private isExternalImageUrl(url: string): boolean {\n    const externalDomains = [\n      'unsplash.com',\n      'images.unsplash.com',\n      'picsum.photos',\n      'via.placeholder.com',\n      'placehold.it',\n      'placeholder.com'\n    ];\n\n    return externalDomains.some(domain => url.includes(domain));\n  }\n\n\n\n\n\n\n\n  /**\n   * Check if URL is a video\n   */\n  isVideoUrl(url: string): boolean {\n    if (!url) return false;\n    const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi'];\n    const lowerUrl = url.toLowerCase();\n    return videoExtensions.some(ext => lowerUrl.includes(ext)) || \n           lowerUrl.includes('video') || \n           lowerUrl.includes('.mp4');\n  }\n\n  /**\n   * Get video thumbnail\n   */\n  getVideoThumbnail(videoUrl: string): string {\n    // For videos, use fallback\n    return this.fallbackImages.post;\n  }\n\n  /**\n   * Get video duration\n   */\n  getVideoDuration(videoUrl: string): number {\n    return 30; // Default 30 seconds\n  }\n\n  /**\n   * Process media items from database\n   */\n  processMediaItems(mediaArray: any[]): MediaItem[] {\n    if (!mediaArray || !Array.isArray(mediaArray)) {\n      return [];\n    }\n\n    return mediaArray.map((media, index) => {\n      const isVideo = this.isVideoUrl(media.url);\n      \n      return {\n        id: media._id || `media_${index}`,\n        type: isVideo ? 'video' : 'image',\n        url: this.getSafeImageUrl(media.url),\n        thumbnailUrl: isVideo ? this.getVideoThumbnail(media.url) : undefined,\n        alt: media.alt || '',\n        duration: isVideo ? this.getVideoDuration(media.url) : undefined,\n        aspectRatio: media.aspectRatio || (isVideo ? 16/9 : 1),\n        size: media.size\n      };\n    });\n  }\n\n\n\n  /**\n   * Check if URL is broken\n   */\n  private isBrokenUrl(url: string): boolean {\n    return this.brokenUrlPatterns.some(pattern => url.includes(pattern));\n  }\n\n  /**\n   * Log media errors for debugging (with smart filtering)\n   */\n  private logMediaError(error: MediaError): void {\n    const currentErrors = this.mediaErrors.value;\n    this.mediaErrors.next([...currentErrors, error]);\n\n    // Only log to console if it's not an external image failure\n    if (!this.isExternalImageUrl(error.id)) {\n      console.warn('Media Error:', error);\n    }\n  }\n\n  /**\n   * Clear media errors\n   */\n  clearMediaErrors(): void {\n    this.mediaErrors.next([]);\n  }\n\n  /**\n   * Preload media for better performance with graceful error handling\n   */\n  preloadMedia(mediaItems: MediaItem[]): Promise<void[]> {\n    const promises = mediaItems.map(media => {\n      return new Promise<void>((resolve) => {\n        if (media.type === 'image') {\n          const img = new Image();\n          img.onload = () => resolve();\n          img.onerror = () => {\n            // Only log errors for non-external images\n            if (!this.isExternalImageUrl(media.url)) {\n              console.warn(`Failed to preload image: ${media.url}`);\n            }\n            resolve(); // Resolve anyway to not break the promise chain\n          };\n          img.src = media.url;\n        } else if (media.type === 'video') {\n          const video = document.createElement('video');\n          video.onloadeddata = () => resolve();\n          video.onerror = () => {\n            // Only log errors for non-external videos\n            if (!this.isExternalImageUrl(media.url)) {\n              console.warn(`Failed to preload video: ${media.url}`);\n            }\n            resolve(); // Resolve anyway to not break the promise chain\n          };\n          video.src = media.url;\n          video.load();\n        } else {\n          resolve(); // Unknown type, just resolve\n        }\n      });\n    });\n\n    return Promise.all(promises);\n  }\n\n  /**\n   * Optimize image URL with size parameters\n   */\n  optimizeImageUrl(url: string, width?: number, height?: number, quality: number = 80): string {\n    if (!url || this.isExternalImageUrl(url)) {\n      return url; // Don't modify external URLs\n    }\n\n    // For local images, we can add optimization parameters if the backend supports it\n    const params = new URLSearchParams();\n    if (width) params.append('w', width.toString());\n    if (height) params.append('h', height.toString());\n    if (quality !== 80) params.append('q', quality.toString());\n\n    const separator = url.includes('?') ? '&' : '?';\n    return params.toString() ? `${url}${separator}${params.toString()}` : url;\n  }\n\n  /**\n   * Get responsive image URLs for different screen sizes\n   */\n  getResponsiveImageUrls(url: string): { [key: string]: string } {\n    if (!url) return {};\n\n    return {\n      thumbnail: this.optimizeImageUrl(url, 150, 150, 70),\n      small: this.optimizeImageUrl(url, 300, 300, 75),\n      medium: this.optimizeImageUrl(url, 600, 600, 80),\n      large: this.optimizeImageUrl(url, 1200, 1200, 85),\n      original: url\n    };\n  }\n\n  /**\n   * Create a lazy loading image element with proper error handling\n   */\n  createLazyImage(src: string, alt: string = '', className: string = ''): HTMLImageElement {\n    const img = document.createElement('img');\n    img.alt = alt;\n    img.className = className;\n    img.loading = 'lazy';\n\n    // Set up error handling\n    img.onerror = (event: string | Event) => {\n      if (typeof event === 'string') {\n        this.handleImageError(new Event('error'), 'post');\n      } else {\n        this.handleImageError(event, 'post');\n      }\n    };\n\n    // Use intersection observer for lazy loading\n    const observer = new IntersectionObserver((entries) => {\n      entries.forEach(entry => {\n        if (entry.isIntersecting) {\n          img.src = this.getSafeImageUrl(src);\n          observer.unobserve(img);\n        }\n      });\n    }, { rootMargin: '50px' });\n\n    observer.observe(img);\n    return img;\n  }\n\n  /**\n   * Batch preload images with progress tracking\n   */\n  batchPreloadImages(urls: string[], onProgress?: (loaded: number, total: number) => void): Promise<void> {\n    let loaded = 0;\n    const total = urls.length;\n\n    const promises = urls.map(url => {\n      return new Promise<void>((resolve) => {\n        const img = new Image();\n        img.onload = img.onerror = () => {\n          loaded++;\n          if (onProgress) onProgress(loaded, total);\n          resolve();\n        };\n        img.src = this.getSafeImageUrl(url);\n      });\n    });\n\n    return Promise.all(promises).then(() => {});\n  }\n}\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,MAAM;;AAuBtC,OAAM,MAAOC,YAAY;EAgCvBC,YAAA;IA/BQ,KAAAC,WAAW,GAAG,IAAIH,eAAe,CAAe,EAAE,CAAC;IACpD,KAAAI,YAAY,GAAG,IAAI,CAACD,WAAW,CAACE,YAAY,EAAE;IAErD;IACiB,KAAAC,cAAc,GAAG;MAChCC,IAAI,EAAE,mCAAmC;MACzCC,OAAO,EAAE,oCAAoC;MAC7CC,IAAI,EAAE,iCAAiC;MACvCC,KAAK,EAAE;KACR;IAED;IACiB,KAAAC,eAAe,GAAG;MACjCJ,IAAI,EAAE,osBAAosB;MAC1sBC,OAAO,EAAE,4uBAA4uB;MACrvBC,IAAI,EAAE,4uBAA4uB;MAClvBC,KAAK,EAAE;KACR;IAED;IACiB,KAAAE,MAAM,GAAU,EAAE;IAEnC;IACiB,KAAAC,iBAAiB,GAAG,CACnC,0BAA0B,EAC1B,0BAA0B,EAC1B,mBAAmB,EACnB,wBAAwB,EACxB,SAAS,CACV;EAEc;EAEf;;;EAGAC,mBAAmBA,CAACC,IAAA,GAA8C,MAAM;IACtE,OAAO,IAAI,CAACJ,eAAe,CAACI,IAAI,CAAC;EACnC;EAEA;;;EAGAC,cAAcA,CAACC,GAAW;IACxB,OAAO,IAAI,CAACC,kBAAkB,CAACD,GAAG,CAAC,IAAI,IAAI,CAACE,WAAW,CAACF,GAAG,CAAC;EAC9D;EAEA;;;EAGAG,eAAeA,CAACH,GAAuB,EAAEF,IAAA,GAA8C,MAAM;IAC3F,IAAI,CAACE,GAAG,IAAIA,GAAG,CAACI,IAAI,EAAE,KAAK,EAAE,EAAE;MAC7B,OAAO,IAAI,CAACV,eAAe,CAACI,IAAI,CAAC,CAAC,CAAC;;IAGrC;IACA,MAAMO,QAAQ,GAAG,IAAI,CAACC,YAAY,CAACN,GAAG,EAAEF,IAAI,CAAC;IAC7C,IAAIO,QAAQ,KAAKL,GAAG,EAAE;MACpB,OAAOK,QAAQ;;IAGjB;IACA,IAAIL,GAAG,CAACO,QAAQ,CAAC,wBAAwB,CAAC,EAAE;MAC1C,MAAMC,SAAS,GAAGR,GAAG,CAACS,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;MAChD,OAAOD,SAAS;;IAGlB;IACA,IAAI,IAAI,CAACP,kBAAkB,CAACD,GAAG,CAAC,EAAE;MAChC;MACA,OAAOA,GAAG;;IAGZ;IACA,IAAI;MACF,IAAIU,GAAG,CAACV,GAAG,CAAC;MACZ,OAAOA,GAAG;KACX,CAAC,MAAM;MACN;MACA,IAAIA,GAAG,CAACW,UAAU,CAAC,GAAG,CAAC,IAAIX,GAAG,CAACW,UAAU,CAAC,IAAI,CAAC,IAAIX,GAAG,CAACW,UAAU,CAAC,KAAK,CAAC,EAAE;QACxE,OAAOX,GAAG;;MAEZ;MACA,OAAO,IAAI,CAACN,eAAe,CAACI,IAAI,CAAC;;EAErC;EAEA;;;EAGQQ,YAAYA,CAACN,GAAW,EAAEF,IAA2C;IAC3E;IACA,KAAK,MAAMc,OAAO,IAAI,IAAI,CAAChB,iBAAiB,EAAE;MAC5C,IAAII,GAAG,CAACO,QAAQ,CAACK,OAAO,CAAC,EAAE;QACzB;QACA,IAAIA,OAAO,KAAK,0BAA0B,IAAIA,OAAO,KAAK,0BAA0B,EAAE;UACpF,OAAO,IAAI,CAACC,sBAAsB,CAACb,GAAG,EAAEF,IAAI,CAAC;;QAE/C,IAAIc,OAAO,KAAK,mBAAmB,EAAE;UACnC,OAAO,IAAI,CAACf,mBAAmB,CAACC,IAAI,CAAC;;QAEvC,IAAIc,OAAO,KAAK,wBAAwB,EAAE;UACxC;UACA,MAAMJ,SAAS,GAAGR,GAAG,CAACS,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;UAChD,OAAOD,SAAS;;QAElB,IAAII,OAAO,KAAK,SAAS,EAAE;UACzB,OAAO,IAAI,CAACvB,cAAc,CAACS,IAAI,CAAC;;;;IAItC,OAAOE,GAAG;EACZ;EAEA;;;EAGQa,sBAAsBA,CAACC,WAAmB,EAAEhB,IAA2C;IAC7F;IACA,MAAMiB,WAAW,GAA8B;MAC7C,mBAAmB,EAAE,oEAAoE;MACzF,eAAe,EAAE,oEAAoE;MACrF,oBAAoB,EAAE,oEAAoE;MAC1F,cAAc,EAAE,oEAAoE;MACpF,QAAQ,EAAE;KACX;IAED;IACA,KAAK,MAAM,CAACC,GAAG,EAAEC,cAAc,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACJ,WAAW,CAAC,EAAE;MAC/D,IAAID,WAAW,CAACM,WAAW,EAAE,CAACb,QAAQ,CAACS,GAAG,CAAC,EAAE;QAC3C,OAAOC,cAAc;;;IAIzB;IACA,OAAO,IAAI,CAAC5B,cAAc,CAACS,IAAI,CAAC;EAClC;EAEA;;;EAGAuB,gBAAgBA,CAACC,KAAY,EAAEC,YAAA,GAAsD,MAAM;IACzF,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAA0B;IAC5C,IAAI,CAACD,GAAG,EAAE;IAEV,MAAME,WAAW,GAAGF,GAAG,CAACG,GAAG;IAE3B;IACA,IAAI,CAACD,WAAW,CAACnB,QAAQ,CAAC,IAAI,CAAClB,cAAc,CAACkC,YAAY,CAAC,CAAC,IAAI,CAACG,WAAW,CAACf,UAAU,CAAC,OAAO,CAAC,EAAE;MAChGa,GAAG,CAACG,GAAG,GAAG,IAAI,CAACtC,cAAc,CAACkC,YAAY,CAAC;MAE3C;MACA,IAAI,CAAC,IAAI,CAACtB,kBAAkB,CAACyB,WAAW,CAAC,IAAI,CAACA,WAAW,CAACnB,QAAQ,CAAC,gBAAgB,CAAC,EAAE;QACpF,IAAI,CAACqB,aAAa,CAAC;UACjBC,EAAE,EAAEH,WAAW;UACf5B,IAAI,EAAE,YAAY;UAClBgC,OAAO,EAAE,yBAAyBJ,WAAW,EAAE;UAC/CK,WAAW,EAAE,IAAI,CAAC1C,cAAc,CAACkC,YAAY;SAC9C,CAAC;;MAEJ;;IAGF;IACA,IAAIG,WAAW,CAACnB,QAAQ,CAAC,IAAI,CAAClB,cAAc,CAACkC,YAAY,CAAC,CAAC,EAAE;MAC3DC,GAAG,CAACG,GAAG,GAAG,IAAI,CAACjC,eAAe,CAAC6B,YAAY,CAAC;MAC5C;MACA,IAAI,CAAC,IAAI,CAACtB,kBAAkB,CAACyB,WAAW,CAAC,EAAE;QACzCM,OAAO,CAACC,IAAI,CAAC,yCAAyCV,YAAY,GAAG,EAAEG,WAAW,CAAC;;MAErF;;EAEJ;EAEA;;;EAGQzB,kBAAkBA,CAACD,GAAW;IACpC,MAAMkC,eAAe,GAAG,CACtB,cAAc,EACd,qBAAqB,EACrB,eAAe,EACf,qBAAqB,EACrB,cAAc,EACd,iBAAiB,CAClB;IAED,OAAOA,eAAe,CAACC,IAAI,CAACC,MAAM,IAAIpC,GAAG,CAACO,QAAQ,CAAC6B,MAAM,CAAC,CAAC;EAC7D;EAQA;;;EAGAC,UAAUA,CAACrC,GAAW;IACpB,IAAI,CAACA,GAAG,EAAE,OAAO,KAAK;IACtB,MAAMsC,eAAe,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IACjE,MAAMC,QAAQ,GAAGvC,GAAG,CAACoB,WAAW,EAAE;IAClC,OAAOkB,eAAe,CAACH,IAAI,CAACK,GAAG,IAAID,QAAQ,CAAChC,QAAQ,CAACiC,GAAG,CAAC,CAAC,IACnDD,QAAQ,CAAChC,QAAQ,CAAC,OAAO,CAAC,IAC1BgC,QAAQ,CAAChC,QAAQ,CAAC,MAAM,CAAC;EAClC;EAEA;;;EAGAkC,iBAAiBA,CAACC,QAAgB;IAChC;IACA,OAAO,IAAI,CAACrD,cAAc,CAACG,IAAI;EACjC;EAEA;;;EAGAmD,gBAAgBA,CAACD,QAAgB;IAC/B,OAAO,EAAE,CAAC,CAAC;EACb;EAEA;;;EAGAE,iBAAiBA,CAACC,UAAiB;IACjC,IAAI,CAACA,UAAU,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,EAAE;MAC7C,OAAO,EAAE;;IAGX,OAAOA,UAAU,CAACG,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAI;MACrC,MAAMC,OAAO,GAAG,IAAI,CAACd,UAAU,CAACY,KAAK,CAACjD,GAAG,CAAC;MAE1C,OAAO;QACL6B,EAAE,EAAEoB,KAAK,CAACG,GAAG,IAAI,SAASF,KAAK,EAAE;QACjCpD,IAAI,EAAEqD,OAAO,GAAG,OAAO,GAAG,OAAO;QACjCnD,GAAG,EAAE,IAAI,CAACG,eAAe,CAAC8C,KAAK,CAACjD,GAAG,CAAC;QACpCqD,YAAY,EAAEF,OAAO,GAAG,IAAI,CAACV,iBAAiB,CAACQ,KAAK,CAACjD,GAAG,CAAC,GAAGsD,SAAS;QACrEC,GAAG,EAAEN,KAAK,CAACM,GAAG,IAAI,EAAE;QACpBC,QAAQ,EAAEL,OAAO,GAAG,IAAI,CAACR,gBAAgB,CAACM,KAAK,CAACjD,GAAG,CAAC,GAAGsD,SAAS;QAChEG,WAAW,EAAER,KAAK,CAACQ,WAAW,KAAKN,OAAO,GAAG,EAAE,GAAC,CAAC,GAAG,CAAC,CAAC;QACtDO,IAAI,EAAET,KAAK,CAACS;OACb;IACH,CAAC,CAAC;EACJ;EAIA;;;EAGQxD,WAAWA,CAACF,GAAW;IAC7B,OAAO,IAAI,CAACJ,iBAAiB,CAACuC,IAAI,CAACvB,OAAO,IAAIZ,GAAG,CAACO,QAAQ,CAACK,OAAO,CAAC,CAAC;EACtE;EAEA;;;EAGQgB,aAAaA,CAAC+B,KAAiB;IACrC,MAAMC,aAAa,GAAG,IAAI,CAAC1E,WAAW,CAAC2E,KAAK;IAC5C,IAAI,CAAC3E,WAAW,CAAC4E,IAAI,CAAC,CAAC,GAAGF,aAAa,EAAED,KAAK,CAAC,CAAC;IAEhD;IACA,IAAI,CAAC,IAAI,CAAC1D,kBAAkB,CAAC0D,KAAK,CAAC9B,EAAE,CAAC,EAAE;MACtCG,OAAO,CAACC,IAAI,CAAC,cAAc,EAAE0B,KAAK,CAAC;;EAEvC;EAEA;;;EAGAI,gBAAgBA,CAAA;IACd,IAAI,CAAC7E,WAAW,CAAC4E,IAAI,CAAC,EAAE,CAAC;EAC3B;EAEA;;;EAGAE,YAAYA,CAACC,UAAuB;IAClC,MAAMC,QAAQ,GAAGD,UAAU,CAACjB,GAAG,CAACC,KAAK,IAAG;MACtC,OAAO,IAAIkB,OAAO,CAAQC,OAAO,IAAI;QACnC,IAAInB,KAAK,CAACnD,IAAI,KAAK,OAAO,EAAE;UAC1B,MAAM0B,GAAG,GAAG,IAAI6C,KAAK,EAAE;UACvB7C,GAAG,CAAC8C,MAAM,GAAG,MAAMF,OAAO,EAAE;UAC5B5C,GAAG,CAAC+C,OAAO,GAAG,MAAK;YACjB;YACA,IAAI,CAAC,IAAI,CAACtE,kBAAkB,CAACgD,KAAK,CAACjD,GAAG,CAAC,EAAE;cACvCgC,OAAO,CAACC,IAAI,CAAC,4BAA4BgB,KAAK,CAACjD,GAAG,EAAE,CAAC;;YAEvDoE,OAAO,EAAE,CAAC,CAAC;UACb,CAAC;UACD5C,GAAG,CAACG,GAAG,GAAGsB,KAAK,CAACjD,GAAG;SACpB,MAAM,IAAIiD,KAAK,CAACnD,IAAI,KAAK,OAAO,EAAE;UACjC,MAAM0E,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;UAC7CF,KAAK,CAACG,YAAY,GAAG,MAAMP,OAAO,EAAE;UACpCI,KAAK,CAACD,OAAO,GAAG,MAAK;YACnB;YACA,IAAI,CAAC,IAAI,CAACtE,kBAAkB,CAACgD,KAAK,CAACjD,GAAG,CAAC,EAAE;cACvCgC,OAAO,CAACC,IAAI,CAAC,4BAA4BgB,KAAK,CAACjD,GAAG,EAAE,CAAC;;YAEvDoE,OAAO,EAAE,CAAC,CAAC;UACb,CAAC;UACDI,KAAK,CAAC7C,GAAG,GAAGsB,KAAK,CAACjD,GAAG;UACrBwE,KAAK,CAACI,IAAI,EAAE;SACb,MAAM;UACLR,OAAO,EAAE,CAAC,CAAC;;MAEf,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAOD,OAAO,CAACU,GAAG,CAACX,QAAQ,CAAC;EAC9B;EAEA;;;EAGAY,gBAAgBA,CAAC9E,GAAW,EAAE+E,KAAc,EAAEC,MAAe,EAAEC,OAAA,GAAkB,EAAE;IACjF,IAAI,CAACjF,GAAG,IAAI,IAAI,CAACC,kBAAkB,CAACD,GAAG,CAAC,EAAE;MACxC,OAAOA,GAAG,CAAC,CAAC;;IAGd;IACA,MAAMkF,MAAM,GAAG,IAAIC,eAAe,EAAE;IACpC,IAAIJ,KAAK,EAAEG,MAAM,CAACE,MAAM,CAAC,GAAG,EAAEL,KAAK,CAACM,QAAQ,EAAE,CAAC;IAC/C,IAAIL,MAAM,EAAEE,MAAM,CAACE,MAAM,CAAC,GAAG,EAAEJ,MAAM,CAACK,QAAQ,EAAE,CAAC;IACjD,IAAIJ,OAAO,KAAK,EAAE,EAAEC,MAAM,CAACE,MAAM,CAAC,GAAG,EAAEH,OAAO,CAACI,QAAQ,EAAE,CAAC;IAE1D,MAAMC,SAAS,GAAGtF,GAAG,CAACO,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;IAC/C,OAAO2E,MAAM,CAACG,QAAQ,EAAE,GAAG,GAAGrF,GAAG,GAAGsF,SAAS,GAAGJ,MAAM,CAACG,QAAQ,EAAE,EAAE,GAAGrF,GAAG;EAC3E;EAEA;;;EAGAuF,sBAAsBA,CAACvF,GAAW;IAChC,IAAI,CAACA,GAAG,EAAE,OAAO,EAAE;IAEnB,OAAO;MACLwF,SAAS,EAAE,IAAI,CAACV,gBAAgB,CAAC9E,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;MACnDyF,KAAK,EAAE,IAAI,CAACX,gBAAgB,CAAC9E,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;MAC/C0F,MAAM,EAAE,IAAI,CAACZ,gBAAgB,CAAC9E,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;MAChD2F,KAAK,EAAE,IAAI,CAACb,gBAAgB,CAAC9E,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC;MACjD4F,QAAQ,EAAE5F;KACX;EACH;EAEA;;;EAGA6F,eAAeA,CAAClE,GAAW,EAAE4B,GAAA,GAAc,EAAE,EAAEuC,SAAA,GAAoB,EAAE;IACnE,MAAMtE,GAAG,GAAGiD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACzClD,GAAG,CAAC+B,GAAG,GAAGA,GAAG;IACb/B,GAAG,CAACsE,SAAS,GAAGA,SAAS;IACzBtE,GAAG,CAACuE,OAAO,GAAG,MAAM;IAEpB;IACAvE,GAAG,CAAC+C,OAAO,GAAIjD,KAAqB,IAAI;MACtC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAI,CAACD,gBAAgB,CAAC,IAAI2E,KAAK,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC;OAClD,MAAM;QACL,IAAI,CAAC3E,gBAAgB,CAACC,KAAK,EAAE,MAAM,CAAC;;IAExC,CAAC;IAED;IACA,MAAM2E,QAAQ,GAAG,IAAIC,oBAAoB,CAAE/E,OAAO,IAAI;MACpDA,OAAO,CAACgF,OAAO,CAACC,KAAK,IAAG;QACtB,IAAIA,KAAK,CAACC,cAAc,EAAE;UACxB7E,GAAG,CAACG,GAAG,GAAG,IAAI,CAACxB,eAAe,CAACwB,GAAG,CAAC;UACnCsE,QAAQ,CAACK,SAAS,CAAC9E,GAAG,CAAC;;MAE3B,CAAC,CAAC;IACJ,CAAC,EAAE;MAAE+E,UAAU,EAAE;IAAM,CAAE,CAAC;IAE1BN,QAAQ,CAACO,OAAO,CAAChF,GAAG,CAAC;IACrB,OAAOA,GAAG;EACZ;EAEA;;;EAGAiF,kBAAkBA,CAACC,IAAc,EAAEC,UAAoD;IACrF,IAAIC,MAAM,GAAG,CAAC;IACd,MAAMC,KAAK,GAAGH,IAAI,CAACI,MAAM;IAEzB,MAAM5C,QAAQ,GAAGwC,IAAI,CAAC1D,GAAG,CAAChD,GAAG,IAAG;MAC9B,OAAO,IAAImE,OAAO,CAAQC,OAAO,IAAI;QACnC,MAAM5C,GAAG,GAAG,IAAI6C,KAAK,EAAE;QACvB7C,GAAG,CAAC8C,MAAM,GAAG9C,GAAG,CAAC+C,OAAO,GAAG,MAAK;UAC9BqC,MAAM,EAAE;UACR,IAAID,UAAU,EAAEA,UAAU,CAACC,MAAM,EAAEC,KAAK,CAAC;UACzCzC,OAAO,EAAE;QACX,CAAC;QACD5C,GAAG,CAACG,GAAG,GAAG,IAAI,CAACxB,eAAe,CAACH,GAAG,CAAC;MACrC,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAOmE,OAAO,CAACU,GAAG,CAACX,QAAQ,CAAC,CAAC6C,IAAI,CAAC,MAAK,CAAE,CAAC,CAAC;EAC7C;;;uBAhZW/H,YAAY;IAAA;EAAA;;;aAAZA,YAAY;MAAAgI,OAAA,EAAZhI,YAAY,CAAAiI,IAAA;MAAAC,UAAA,EAFX;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}