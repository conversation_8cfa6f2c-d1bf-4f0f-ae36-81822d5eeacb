{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/trending.service\";\nimport * as i2 from \"../../../../core/services/social-interactions.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3, 4];\nconst _c1 = () => [1, 2, 3];\nconst _c2 = () => [1, 2, 3, 4, 5];\nfunction FeaturedBrandsComponent_div_8_div_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 19);\n  }\n}\nfunction FeaturedBrandsComponent_div_8_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵelement(2, \"div\", 15)(3, \"div\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 17);\n    i0.ɵɵtemplate(5, FeaturedBrandsComponent_div_8_div_2_div_5_Template, 1, 0, \"div\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c1));\n  }\n}\nfunction FeaturedBrandsComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11);\n    i0.ɵɵtemplate(2, FeaturedBrandsComponent_div_8_div_2_Template, 6, 2, \"div\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction FeaturedBrandsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵelement(1, \"ion-icon\", 21);\n    i0.ɵɵelementStart(2, \"p\", 22);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_9_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 24);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction FeaturedBrandsComponent_div_10_div_1_div_25_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r6.originalPrice));\n  }\n}\nfunction FeaturedBrandsComponent_div_10_div_1_div_25_ion_icon_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ion-icon\", 50);\n  }\n  if (rf & 2) {\n    const star_r7 = ctx.$implicit;\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r7 <= product_r6.rating.average);\n    i0.ɵɵproperty(\"name\", star_r7 <= product_r6.rating.average ? \"star\" : \"star-outline\");\n  }\n}\nfunction FeaturedBrandsComponent_div_10_div_1_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_10_div_1_div_25_Template_div_click_0_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onProductClick(product_r6, $event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 46);\n    i0.ɵɵelement(2, \"img\", 47);\n    i0.ɵɵelementStart(3, \"div\", 48)(4, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_10_div_1_div_25_Template_button_click_4_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onLikeProduct(product_r6, $event));\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_10_div_1_div_25_Template_button_click_6_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onShareProduct(product_r6, $event));\n    });\n    i0.ɵɵelement(7, \"ion-icon\", 52);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 53)(9, \"h5\", 54);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 55)(12, \"span\", 56);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, FeaturedBrandsComponent_div_10_div_1_div_25_span_14_Template, 2, 1, \"span\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 58)(16, \"div\", 59);\n    i0.ɵɵtemplate(17, FeaturedBrandsComponent_div_10_div_1_div_25_ion_icon_17_Template, 1, 3, \"ion-icon\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 61);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r6.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r6.images[0].alt || product_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r1.isProductLiked(product_r6._id));\n    i0.ɵɵattribute(\"aria-label\", \"Like \" + product_r6.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isProductLiked(product_r6._id) ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", \"Share \" + product_r6.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(product_r6.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r6.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r6.originalPrice && product_r6.originalPrice > product_r6.price);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(12, _c2));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r6.rating.count, \")\");\n  }\n}\nfunction FeaturedBrandsComponent_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_10_div_1_Template_div_click_0_listener() {\n      const brand_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onBrandClick(brand_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 28)(2, \"div\", 29)(3, \"h3\", 30);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 31)(6, \"div\", 32);\n    i0.ɵɵelement(7, \"ion-icon\", 33);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 32);\n    i0.ɵɵelement(11, \"ion-icon\", 34);\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 32);\n    i0.ɵɵelement(15, \"ion-icon\", 35);\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(18, \"div\", 36);\n    i0.ɵɵelement(19, \"ion-icon\", 37);\n    i0.ɵɵtext(20, \" Featured \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 38)(22, \"h4\", 39);\n    i0.ɵɵtext(23, \"Top Products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 40);\n    i0.ɵɵtemplate(25, FeaturedBrandsComponent_div_10_div_1_div_25_Template, 20, 13, \"div\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 42)(27, \"button\", 43)(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"ion-icon\", 44);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const brand_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(brand_r4.brand);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", brand_r4.productCount, \" Products\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", brand_r4.avgRating, \"/5\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(brand_r4.totalViews), \" Views\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", brand_r4.topProducts)(\"ngForTrackBy\", ctx_r1.trackByProductId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"View All \", brand_r4.brand, \" Products\");\n  }\n}\nfunction FeaturedBrandsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtemplate(1, FeaturedBrandsComponent_div_10_div_1_Template, 31, 7, \"div\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.featuredBrands)(\"ngForTrackBy\", ctx_r1.trackByBrandName);\n  }\n}\nfunction FeaturedBrandsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"ion-icon\", 64);\n    i0.ɵɵelementStart(2, \"h3\", 65);\n    i0.ɵɵtext(3, \"No Featured Brands\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 66);\n    i0.ɵɵtext(5, \"Check back later for featured brand collections\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class FeaturedBrandsComponent {\n  constructor(trendingService, socialService, router) {\n    this.trendingService = trendingService;\n    this.socialService = socialService;\n    this.router = router;\n    this.featuredBrands = [];\n    this.isLoading = true;\n    this.error = null;\n    this.likedProducts = new Set();\n    this.subscription = new Subscription();\n    // Slider properties\n    this.currentSlide = 0;\n    this.slideOffset = 0;\n    this.cardWidth = 320; // Width of each brand card including margin\n    this.visibleCards = 3; // Number of cards visible at once\n    this.maxSlide = 0;\n    this.autoSlideDelay = 4000; // 4 seconds for brands\n    this.isAutoSliding = true;\n    this.isPaused = false;\n  }\n  ngOnInit() {\n    this.loadFeaturedBrands();\n    this.subscribeFeaturedBrands();\n    this.subscribeLikedProducts();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n  subscribeFeaturedBrands() {\n    this.subscription.add(this.trendingService.featuredBrands$.subscribe(brands => {\n      this.featuredBrands = brands;\n      this.isLoading = false;\n    }));\n  }\n  subscribeLikedProducts() {\n    this.subscription.add(this.socialService.likedProducts$.subscribe(likedProducts => {\n      this.likedProducts = likedProducts;\n    }));\n  }\n  loadFeaturedBrands() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        yield _this.trendingService.loadFeaturedBrands();\n      } catch (error) {\n        console.error('Error loading featured brands:', error);\n        _this.error = 'Failed to load featured brands';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onBrandClick(brand) {\n    this.router.navigate(['/products'], {\n      queryParams: {\n        brand: brand.brand\n      }\n    });\n  }\n  onProductClick(product, event) {\n    event.stopPropagation();\n    this.router.navigate(['/product', product._id]);\n  }\n  onLikeProduct(product, event) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const result = yield _this2.socialService.likeProduct(product._id);\n        if (result.success) {\n          console.log(result.message);\n        } else {\n          console.error('Failed to like product:', result.message);\n        }\n      } catch (error) {\n        console.error('Error liking product:', error);\n      }\n    })();\n  }\n  onShareProduct(product, event) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const productUrl = `${window.location.origin}/product/${product._id}`;\n        yield navigator.clipboard.writeText(productUrl);\n        yield _this3.socialService.shareProduct(product._id, {\n          platform: 'copy_link',\n          message: `Check out this amazing ${product.name} from ${product.brand}!`\n        });\n        console.log('Product link copied to clipboard!');\n      } catch (error) {\n        console.error('Error sharing product:', error);\n      }\n    })();\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  onRetry() {\n    this.loadFeaturedBrands();\n  }\n  trackByBrandName(index, brand) {\n    return brand.brand;\n  }\n  isProductLiked(productId) {\n    return this.likedProducts.has(productId);\n  }\n  trackByProductId(index, product) {\n    return product._id;\n  }\n  // Auto-sliding methods\n  startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.featuredBrands.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n  autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n  // Responsive methods\n  updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 768) {\n      this.cardWidth = 280;\n      this.visibleCards = 1;\n    } else if (width <= 1200) {\n      this.cardWidth = 320;\n      this.visibleCards = 2;\n    } else {\n      this.cardWidth = 340;\n      this.visibleCards = 3;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n  setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.featuredBrands.length - this.visibleCards);\n  }\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n  restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000);\n  }\n  // Update slider when brands load\n  updateSliderOnBrandsLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n  static {\n    this.ɵfac = function FeaturedBrandsComponent_Factory(t) {\n      return new (t || FeaturedBrandsComponent)(i0.ɵɵdirectiveInject(i1.TrendingService), i0.ɵɵdirectiveInject(i2.SocialInteractionsService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FeaturedBrandsComponent,\n      selectors: [[\"app-featured-brands\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 12,\n      vars: 4,\n      consts: [[1, \"featured-brands-container\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"diamond\", 1, \"title-icon\"], [1, \"section-subtitle\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"brands-grid\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-brand-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-brand-card\"], [1, \"loading-header\"], [1, \"loading-brand-name\"], [1, \"loading-stats\"], [1, \"loading-products\"], [\"class\", \"loading-product\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-product\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"brands-grid\"], [\"class\", \"brand-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"brand-card\", 3, \"click\"], [1, \"brand-header\"], [1, \"brand-info\"], [1, \"brand-name\"], [1, \"brand-stats\"], [1, \"stat-item\"], [\"name\", \"bag-outline\"], [\"name\", \"star\"], [\"name\", \"eye-outline\"], [1, \"brand-badge\"], [\"name\", \"diamond\"], [1, \"top-products\"], [1, \"products-title\"], [1, \"products-list\"], [\"class\", \"product-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"view-more-section\"], [1, \"view-more-btn\"], [\"name\", \"chevron-forward\"], [1, \"product-item\", 3, \"click\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"src\", \"alt\"], [1, \"product-actions\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"name\", \"share-outline\"], [1, \"product-details\"], [1, \"product-name\"], [1, \"product-price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"product-rating\"], [1, \"stars\"], [3, \"name\", \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-count\"], [1, \"original-price\"], [1, \"empty-container\"], [\"name\", \"diamond-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"]],\n      template: function FeaturedBrandsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n          i0.ɵɵelement(4, \"ion-icon\", 4);\n          i0.ɵɵtext(5, \" Featured Brands \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \"Top brands with amazing collections\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(8, FeaturedBrandsComponent_div_8_Template, 3, 2, \"div\", 6)(9, FeaturedBrandsComponent_div_9_Template, 7, 1, \"div\", 7)(10, FeaturedBrandsComponent_div_10_Template, 2, 2, \"div\", 8)(11, FeaturedBrandsComponent_div_11_Template, 6, 0, \"div\", 9);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.featuredBrands.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.featuredBrands.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, IonicModule, i5.IonIcon, CarouselModule],\n      styles: [\".featured-brands-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n  color: white;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 12px;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #ffd700;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.8);\\n  margin: 0;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 20px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  padding: 20px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-header[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-header[_ngcontent-%COMP%]   .loading-brand-name[_ngcontent-%COMP%] {\\n  height: 24px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 8px;\\n  margin-bottom: 8px;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-header[_ngcontent-%COMP%]   .loading-stats[_ngcontent-%COMP%] {\\n  height: 16px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 8px;\\n  width: 70%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-products[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-products[_ngcontent-%COMP%]   .loading-product[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 120px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 12px;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0%, 100% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n.error-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #ff6b6b;\\n  margin-bottom: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: rgba(255, 255, 255, 0.8);\\n  margin-bottom: 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 12px 24px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 auto;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n}\\n\\n.brands-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 20px;\\n}\\n\\n.brand-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  padding: 20px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.brand-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);\\n}\\n\\n.brand-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  margin-bottom: 20px;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-name[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 12px 0;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 6px;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.8);\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #ffd700;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);\\n  color: #333;\\n  padding: 8px 12px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n\\n.top-products[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: white;\\n  margin: 0 0 16px 0;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  overflow-x: auto;\\n  padding-bottom: 8px;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 4px;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 2px;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 2px;\\n}\\n\\n.product-item[_ngcontent-%COMP%] {\\n  flex: 0 0 140px;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 12px;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.product-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  background: rgba(255, 255, 255, 0.15);\\n}\\n.product-item[_ngcontent-%COMP%]:hover   .product-actions[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.product-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100px;\\n  object-fit: cover;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  right: 8px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 28px;\\n  height: 28px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #333;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  transform: scale(1.1);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%] {\\n  background: rgba(220, 53, 69, 0.2);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.product-details[_ngcontent-%COMP%] {\\n  padding: 12px;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: white;\\n  margin: 0 0 8px 0;\\n  line-height: 1.3;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  margin-bottom: 8px;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 700;\\n  color: #ffd700;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: rgba(255, 255, 255, 0.6);\\n  text-decoration: line-through;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1px;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: rgba(255, 255, 255, 0.3);\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon.filled[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .rating-count[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 2px solid rgba(255, 255, 255, 0.2);\\n  color: white;\\n  padding: 12px 16px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  border-color: rgba(255, 255, 255, 0.4);\\n}\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: rgba(255, 255, 255, 0.4);\\n  margin-bottom: 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 8px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n@media (max-width: 768px) {\\n  .featured-brands-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .brands-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 16px;\\n  }\\n  .brand-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n  }\\n  .brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%] {\\n    align-self: flex-start;\\n  }\\n  .brand-stats[_ngcontent-%COMP%] {\\n    flex-direction: row !important;\\n    flex-wrap: wrap;\\n    gap: 12px !important;\\n  }\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subscription", "IonicModule", "CarouselModule", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵtemplate", "FeaturedBrandsComponent_div_8_div_2_div_5_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c1", "FeaturedBrandsComponent_div_8_div_2_Template", "_c0", "ɵɵtext", "ɵɵlistener", "FeaturedBrandsComponent_div_9_Template_button_click_4_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onRetry", "ɵɵtextInterpolate", "error", "formatPrice", "product_r6", "originalPrice", "ɵɵclassProp", "star_r7", "rating", "average", "FeaturedBrandsComponent_div_10_div_1_div_25_Template_div_click_0_listener", "$event", "_r5", "$implicit", "onProductClick", "FeaturedBrandsComponent_div_10_div_1_div_25_Template_button_click_4_listener", "onLikeProduct", "FeaturedBrandsComponent_div_10_div_1_div_25_Template_button_click_6_listener", "onShareProduct", "FeaturedBrandsComponent_div_10_div_1_div_25_span_14_Template", "FeaturedBrandsComponent_div_10_div_1_div_25_ion_icon_17_Template", "images", "url", "ɵɵsanitizeUrl", "alt", "name", "isProductLiked", "_id", "price", "_c2", "ɵɵtextInterpolate1", "count", "FeaturedBrandsComponent_div_10_div_1_Template_div_click_0_listener", "brand_r4", "_r3", "onBrandClick", "FeaturedBrandsComponent_div_10_div_1_div_25_Template", "brand", "productCount", "avgRating", "formatNumber", "totalViews", "topProducts", "trackByProductId", "FeaturedBrandsComponent_div_10_div_1_Template", "featuredB<PERSON>s", "trackByBrandName", "FeaturedBrandsComponent", "constructor", "trendingService", "socialService", "router", "isLoading", "likedProducts", "Set", "subscription", "currentSlide", "slideOffset", "<PERSON><PERSON><PERSON><PERSON>", "visibleCards", "maxSlide", "autoSlideDelay", "isAutoSliding", "isPaused", "ngOnInit", "loadFeaturedBrands", "subscribeFeaturedBrands", "subscribeLikedProducts", "ngOnDestroy", "unsubscribe", "add", "featuredBrands$", "subscribe", "brands", "likedProducts$", "_this", "_asyncToGenerator", "console", "navigate", "queryParams", "product", "event", "stopPropagation", "_this2", "result", "likeProduct", "success", "log", "message", "_this3", "productUrl", "window", "location", "origin", "navigator", "clipboard", "writeText", "shareProduct", "platform", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "num", "toFixed", "toString", "index", "productId", "has", "startAutoSlide", "stopAutoSlide", "autoSlideInterval", "setInterval", "length", "autoSlideNext", "clearInterval", "updateSlideOffset", "pauseAutoSlide", "resumeAutoSlide", "updateResponsiveSettings", "width", "innerWidth", "updateSliderLimits", "setupResizeListener", "addEventListener", "Math", "max", "slidePrev", "restartAutoSlideAfterInteraction", "slideNext", "setTimeout", "updateSliderOnBrandsLoad", "ɵɵdirectiveInject", "i1", "TrendingService", "i2", "SocialInteractionsService", "i3", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "FeaturedBrandsComponent_Template", "rf", "ctx", "FeaturedBrandsComponent_div_8_Template", "FeaturedBrandsComponent_div_9_Template", "FeaturedBrandsComponent_div_10_Template", "FeaturedBrandsComponent_div_11_Template", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "IonIcon", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\featured-brands\\featured-brands.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\featured-brands\\featured-brands.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { TrendingService, FeaturedBrand } from '../../../../core/services/trending.service';\nimport { Product } from '../../../../core/models/product.model';\nimport { SocialInteractionsService } from '../../../../core/services/social-interactions.service';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\n\n@Component({\n  selector: 'app-featured-brands',\n  standalone: true,\n  imports: [CommonModule, IonicModule, CarouselModule],\n  templateUrl: './featured-brands.component.html',\n  styleUrls: ['./featured-brands.component.scss']\n})\nexport class FeaturedBrandsComponent implements OnInit, OnDestroy {\n  featuredBrands: FeaturedBrand[] = [];\n  isLoading = true;\n  error: string | null = null;\n  likedProducts = new Set<string>();\n  private subscription: Subscription = new Subscription();\n\n  // Slider properties\n  currentSlide = 0;\n  slideOffset = 0;\n  cardWidth = 320; // Width of each brand card including margin\n  visibleCards = 3; // Number of cards visible at once\n  maxSlide = 0;\n\n  // Auto-sliding properties\n  autoSlideInterval: any;\n  autoSlideDelay = 4000; // 4 seconds for brands\n  isAutoSliding = true;\n  isPaused = false;\n\n  constructor(\n    private trendingService: TrendingService,\n    private socialService: SocialInteractionsService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadFeaturedBrands();\n    this.subscribeFeaturedBrands();\n    this.subscribeLikedProducts();\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n\n  private subscribeFeaturedBrands() {\n    this.subscription.add(\n      this.trendingService.featuredBrands$.subscribe(brands => {\n        this.featuredBrands = brands;\n        this.isLoading = false;\n      })\n    );\n  }\n\n  private subscribeLikedProducts() {\n    this.subscription.add(\n      this.socialService.likedProducts$.subscribe(likedProducts => {\n        this.likedProducts = likedProducts;\n      })\n    );\n  }\n\n  private async loadFeaturedBrands() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      await this.trendingService.loadFeaturedBrands();\n    } catch (error) {\n      console.error('Error loading featured brands:', error);\n      this.error = 'Failed to load featured brands';\n      this.isLoading = false;\n    }\n  }\n\n  onBrandClick(brand: FeaturedBrand) {\n    this.router.navigate(['/products'], { \n      queryParams: { brand: brand.brand } \n    });\n  }\n\n  onProductClick(product: Product, event: Event) {\n    event.stopPropagation();\n    this.router.navigate(['/product', product._id]);\n  }\n\n  async onLikeProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      const result = await this.socialService.likeProduct(product._id);\n      if (result.success) {\n        console.log(result.message);\n      } else {\n        console.error('Failed to like product:', result.message);\n      }\n    } catch (error) {\n      console.error('Error liking product:', error);\n    }\n  }\n\n  async onShareProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      const productUrl = `${window.location.origin}/product/${product._id}`;\n      await navigator.clipboard.writeText(productUrl);\n\n      await this.socialService.shareProduct(product._id, {\n        platform: 'copy_link',\n        message: `Check out this amazing ${product.name} from ${product.brand}!`\n      });\n\n      console.log('Product link copied to clipboard!');\n    } catch (error) {\n      console.error('Error sharing product:', error);\n    }\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  onRetry() {\n    this.loadFeaturedBrands();\n  }\n\n  trackByBrandName(index: number, brand: FeaturedBrand): string {\n    return brand.brand;\n  }\n\n  isProductLiked(productId: string): boolean {\n    return this.likedProducts.has(productId);\n  }\n\n  trackByProductId(index: number, product: Product): string {\n    return product._id;\n  }\n\n  // Auto-sliding methods\n  private startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n\n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.featuredBrands.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n\n  private stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n\n  private autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n\n  // Responsive methods\n  private updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 768) {\n      this.cardWidth = 280;\n      this.visibleCards = 1;\n    } else if (width <= 1200) {\n      this.cardWidth = 320;\n      this.visibleCards = 2;\n    } else {\n      this.cardWidth = 340;\n      this.visibleCards = 3;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n\n  private setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.featuredBrands.length - this.visibleCards);\n  }\n\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n\n  private updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n\n  private restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000);\n  }\n\n  // Update slider when brands load\n  private updateSliderOnBrandsLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n}\n", "<div class=\"featured-brands-container\">\n  <!-- Header -->\n  <div class=\"section-header\">\n    <div class=\"header-content\">\n      <h2 class=\"section-title\">\n        <ion-icon name=\"diamond\" class=\"title-icon\"></ion-icon>\n        Featured Brands\n      </h2>\n      <p class=\"section-subtitle\">Top brands with amazing collections</p>\n    </div>\n  </div>\n\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <div class=\"loading-grid\">\n      <div *ngFor=\"let item of [1,2,3,4]\" class=\"loading-brand-card\">\n        <div class=\"loading-header\">\n          <div class=\"loading-brand-name\"></div>\n          <div class=\"loading-stats\"></div>\n        </div>\n        <div class=\"loading-products\">\n          <div *ngFor=\"let prod of [1,2,3]\" class=\"loading-product\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error && !isLoading\" class=\"error-container\">\n    <ion-icon name=\"alert-circle\" class=\"error-icon\"></ion-icon>\n    <p class=\"error-message\">{{ error }}</p>\n    <button class=\"retry-btn\" (click)=\"onRetry()\">\n      <ion-icon name=\"refresh\"></ion-icon>\n      Try Again\n    </button>\n  </div>\n\n  <!-- Brands Grid -->\n  <div *ngIf=\"!isLoading && !error && featuredBrands.length > 0\" class=\"brands-grid\">\n    <div \n      *ngFor=\"let brand of featuredBrands; trackBy: trackByBrandName\" \n      class=\"brand-card\"\n      (click)=\"onBrandClick(brand)\"\n    >\n      <!-- Brand Header -->\n      <div class=\"brand-header\">\n        <div class=\"brand-info\">\n          <h3 class=\"brand-name\">{{ brand.brand }}</h3>\n          <div class=\"brand-stats\">\n            <div class=\"stat-item\">\n              <ion-icon name=\"bag-outline\"></ion-icon>\n              <span>{{ brand.productCount }} Products</span>\n            </div>\n            <div class=\"stat-item\">\n              <ion-icon name=\"star\"></ion-icon>\n              <span>{{ brand.avgRating }}/5</span>\n            </div>\n            <div class=\"stat-item\">\n              <ion-icon name=\"eye-outline\"></ion-icon>\n              <span>{{ formatNumber(brand.totalViews) }} Views</span>\n            </div>\n          </div>\n        </div>\n        <div class=\"brand-badge\">\n          <ion-icon name=\"diamond\"></ion-icon>\n          Featured\n        </div>\n      </div>\n\n      <!-- Top Products -->\n      <div class=\"top-products\">\n        <h4 class=\"products-title\">Top Products</h4>\n        <div class=\"products-list\">\n          <div \n            *ngFor=\"let product of brand.topProducts; trackBy: trackByProductId\" \n            class=\"product-item\"\n            (click)=\"onProductClick(product, $event)\"\n          >\n            <div class=\"product-image-container\">\n              <img \n                [src]=\"product.images[0].url\"\n                [alt]=\"product.images[0].alt || product.name\"\n                class=\"product-image\"\n                loading=\"lazy\"\n              />\n              \n              <!-- Action Buttons -->\n              <div class=\"product-actions\">\n                <button\n                  class=\"action-btn like-btn\"\n                  [class.liked]=\"isProductLiked(product._id)\"\n                  (click)=\"onLikeProduct(product, $event)\"\n                  [attr.aria-label]=\"'Like ' + product.name\"\n                >\n                  <ion-icon [name]=\"isProductLiked(product._id) ? 'heart' : 'heart-outline'\"></ion-icon>\n                </button>\n                <button \n                  class=\"action-btn share-btn\" \n                  (click)=\"onShareProduct(product, $event)\"\n                  [attr.aria-label]=\"'Share ' + product.name\"\n                >\n                  <ion-icon name=\"share-outline\"></ion-icon>\n                </button>\n              </div>\n            </div>\n\n            <div class=\"product-details\">\n              <h5 class=\"product-name\">{{ product.name }}</h5>\n              <div class=\"product-price\">\n                <span class=\"current-price\">{{ formatPrice(product.price) }}</span>\n                <span *ngIf=\"product.originalPrice && product.originalPrice > product.price\" \n                      class=\"original-price\">{{ formatPrice(product.originalPrice) }}</span>\n              </div>\n              <div class=\"product-rating\">\n                <div class=\"stars\">\n                  <ion-icon \n                    *ngFor=\"let star of [1,2,3,4,5]\" \n                    [name]=\"star <= product.rating.average ? 'star' : 'star-outline'\"\n                    [class.filled]=\"star <= product.rating.average\"\n                  ></ion-icon>\n                </div>\n                <span class=\"rating-count\">({{ product.rating.count }})</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- View More Button -->\n      <div class=\"view-more-section\">\n        <button class=\"view-more-btn\">\n          <span>View All {{ brand.brand }} Products</span>\n          <ion-icon name=\"chevron-forward\"></ion-icon>\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Empty State -->\n  <div *ngIf=\"!isLoading && !error && featuredBrands.length === 0\" class=\"empty-container\">\n    <ion-icon name=\"diamond-outline\" class=\"empty-icon\"></ion-icon>\n    <h3 class=\"empty-title\">No Featured Brands</h3>\n    <p class=\"empty-message\">Check back later for featured brand collections</p>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AAInC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,oBAAoB;;;;;;;;;;;;ICazCC,EAAA,CAAAC,SAAA,cAAgE;;;;;IALlED,EADF,CAAAE,cAAA,cAA+D,cACjC;IAE1BF,EADA,CAAAC,SAAA,cAAsC,cACL;IACnCD,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,cAAA,cAA8B;IAC5BF,EAAA,CAAAI,UAAA,IAAAC,kDAAA,kBAA0D;IAE9DL,EADE,CAAAG,YAAA,EAAM,EACF;;;IAFoBH,EAAA,CAAAM,SAAA,GAAU;IAAVN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAU;;;;;IAPtCT,EADF,CAAAE,cAAA,cAAiD,cACrB;IACxBF,EAAA,CAAAI,UAAA,IAAAM,4CAAA,kBAA+D;IAUnEV,EADE,CAAAG,YAAA,EAAM,EACF;;;IAVoBH,EAAA,CAAAM,SAAA,GAAY;IAAZN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAG,GAAA,EAAY;;;;;;IAatCX,EAAA,CAAAE,cAAA,cAAyD;IACvDF,EAAA,CAAAC,SAAA,mBAA4D;IAC5DD,EAAA,CAAAE,cAAA,YAAyB;IAAAF,EAAA,CAAAY,MAAA,GAAW;IAAAZ,EAAA,CAAAG,YAAA,EAAI;IACxCH,EAAA,CAAAE,cAAA,iBAA8C;IAApBF,EAAA,CAAAa,UAAA,mBAAAC,+DAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAG,OAAA,EAAS;IAAA,EAAC;IAC3CpB,EAAA,CAAAC,SAAA,mBAAoC;IACpCD,EAAA,CAAAY,MAAA,kBACF;IACFZ,EADE,CAAAG,YAAA,EAAS,EACL;;;;IALqBH,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAqB,iBAAA,CAAAJ,MAAA,CAAAK,KAAA,CAAW;;;;;IAgFxBtB,EAAA,CAAAE,cAAA,eAC6B;IAAAF,EAAA,CAAAY,MAAA,GAAwC;IAAAZ,EAAA,CAAAG,YAAA,EAAO;;;;;IAA/CH,EAAA,CAAAM,SAAA,EAAwC;IAAxCN,EAAA,CAAAqB,iBAAA,CAAAJ,MAAA,CAAAM,WAAA,CAAAC,UAAA,CAAAC,aAAA,EAAwC;;;;;IAInEzB,EAAA,CAAAC,SAAA,mBAIY;;;;;IADVD,EAAA,CAAA0B,WAAA,WAAAC,OAAA,IAAAH,UAAA,CAAAI,MAAA,CAAAC,OAAA,CAA+C;IAD/C7B,EAAA,CAAAO,UAAA,SAAAoB,OAAA,IAAAH,UAAA,CAAAI,MAAA,CAAAC,OAAA,2BAAiE;;;;;;IA5C3E7B,EAAA,CAAAE,cAAA,cAIC;IADCF,EAAA,CAAAa,UAAA,mBAAAiB,0EAAAC,MAAA;MAAA,MAAAP,UAAA,GAAAxB,EAAA,CAAAe,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAiB,cAAA,CAAAV,UAAA,EAAAO,MAAA,CAA+B;IAAA,EAAC;IAEzC/B,EAAA,CAAAE,cAAA,cAAqC;IACnCF,EAAA,CAAAC,SAAA,cAKE;IAIAD,EADF,CAAAE,cAAA,cAA6B,iBAM1B;IAFCF,EAAA,CAAAa,UAAA,mBAAAsB,6EAAAJ,MAAA;MAAA,MAAAP,UAAA,GAAAxB,EAAA,CAAAe,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAmB,aAAA,CAAAZ,UAAA,EAAAO,MAAA,CAA8B;IAAA,EAAC;IAGxC/B,EAAA,CAAAC,SAAA,mBAAsF;IACxFD,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAE,cAAA,iBAIC;IAFCF,EAAA,CAAAa,UAAA,mBAAAwB,6EAAAN,MAAA;MAAA,MAAAP,UAAA,GAAAxB,EAAA,CAAAe,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAqB,cAAA,CAAAd,UAAA,EAAAO,MAAA,CAA+B;IAAA,EAAC;IAGzC/B,EAAA,CAAAC,SAAA,mBAA0C;IAGhDD,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAGJH,EADF,CAAAE,cAAA,cAA6B,aACF;IAAAF,EAAA,CAAAY,MAAA,IAAkB;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IAE9CH,EADF,CAAAE,cAAA,eAA2B,gBACG;IAAAF,EAAA,CAAAY,MAAA,IAAgC;IAAAZ,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAI,UAAA,KAAAmC,4DAAA,mBAC6B;IAC/BvC,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAE,cAAA,eAA4B,eACP;IACjBF,EAAA,CAAAI,UAAA,KAAAoC,gEAAA,uBAIC;IACHxC,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,cAAA,gBAA2B;IAAAF,EAAA,CAAAY,MAAA,IAA4B;IAG7DZ,EAH6D,CAAAG,YAAA,EAAO,EAC1D,EACF,EACF;;;;;IA5CAH,EAAA,CAAAM,SAAA,GAA6B;IAC7BN,EADA,CAAAO,UAAA,QAAAiB,UAAA,CAAAiB,MAAA,IAAAC,GAAA,EAAA1C,EAAA,CAAA2C,aAAA,CAA6B,QAAAnB,UAAA,CAAAiB,MAAA,IAAAG,GAAA,IAAApB,UAAA,CAAAqB,IAAA,CACgB;IAS3C7C,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAA0B,WAAA,UAAAT,MAAA,CAAA6B,cAAA,CAAAtB,UAAA,CAAAuB,GAAA,EAA2C;;IAIjC/C,EAAA,CAAAM,SAAA,EAAgE;IAAhEN,EAAA,CAAAO,UAAA,SAAAU,MAAA,CAAA6B,cAAA,CAAAtB,UAAA,CAAAuB,GAAA,8BAAgE;IAK1E/C,EAAA,CAAAM,SAAA,EAA2C;;IAQtBN,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAqB,iBAAA,CAAAG,UAAA,CAAAqB,IAAA,CAAkB;IAEb7C,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAqB,iBAAA,CAAAJ,MAAA,CAAAM,WAAA,CAAAC,UAAA,CAAAwB,KAAA,EAAgC;IACrDhD,EAAA,CAAAM,SAAA,EAAoE;IAApEN,EAAA,CAAAO,UAAA,SAAAiB,UAAA,CAAAC,aAAA,IAAAD,UAAA,CAAAC,aAAA,GAAAD,UAAA,CAAAwB,KAAA,CAAoE;IAMtDhD,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,KAAAyC,GAAA,EAAc;IAKRjD,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAkD,kBAAA,MAAA1B,UAAA,CAAAI,MAAA,CAAAuB,KAAA,MAA4B;;;;;;IAlFnEnD,EAAA,CAAAE,cAAA,cAIC;IADCF,EAAA,CAAAa,UAAA,mBAAAuC,mEAAA;MAAA,MAAAC,QAAA,GAAArD,EAAA,CAAAe,aAAA,CAAAuC,GAAA,EAAArB,SAAA;MAAA,MAAAhB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAsC,YAAA,CAAAF,QAAA,CAAmB;IAAA,EAAC;IAKzBrD,EAFJ,CAAAE,cAAA,cAA0B,cACA,aACC;IAAAF,EAAA,CAAAY,MAAA,GAAiB;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IAE3CH,EADF,CAAAE,cAAA,cAAyB,cACA;IACrBF,EAAA,CAAAC,SAAA,mBAAwC;IACxCD,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAY,MAAA,GAAiC;IACzCZ,EADyC,CAAAG,YAAA,EAAO,EAC1C;IACNH,EAAA,CAAAE,cAAA,eAAuB;IACrBF,EAAA,CAAAC,SAAA,oBAAiC;IACjCD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAY,MAAA,IAAuB;IAC/BZ,EAD+B,CAAAG,YAAA,EAAO,EAChC;IACNH,EAAA,CAAAE,cAAA,eAAuB;IACrBF,EAAA,CAAAC,SAAA,oBAAwC;IACxCD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAY,MAAA,IAA0C;IAGtDZ,EAHsD,CAAAG,YAAA,EAAO,EACnD,EACF,EACF;IACNH,EAAA,CAAAE,cAAA,eAAyB;IACvBF,EAAA,CAAAC,SAAA,oBAAoC;IACpCD,EAAA,CAAAY,MAAA,kBACF;IACFZ,EADE,CAAAG,YAAA,EAAM,EACF;IAIJH,EADF,CAAAE,cAAA,eAA0B,cACG;IAAAF,EAAA,CAAAY,MAAA,oBAAY;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAE,cAAA,eAA2B;IACzBF,EAAA,CAAAI,UAAA,KAAAoD,oDAAA,oBAIC;IAiDLxD,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAFJ,CAAAE,cAAA,eAA+B,kBACC,YACtB;IAAAF,EAAA,CAAAY,MAAA,IAAmC;IAAAZ,EAAA,CAAAG,YAAA,EAAO;IAChDH,EAAA,CAAAC,SAAA,oBAA4C;IAGlDD,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;;IAxFuBH,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAAqB,iBAAA,CAAAgC,QAAA,CAAAI,KAAA,CAAiB;IAI9BzD,EAAA,CAAAM,SAAA,GAAiC;IAAjCN,EAAA,CAAAkD,kBAAA,KAAAG,QAAA,CAAAK,YAAA,cAAiC;IAIjC1D,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAkD,kBAAA,KAAAG,QAAA,CAAAM,SAAA,OAAuB;IAIvB3D,EAAA,CAAAM,SAAA,GAA0C;IAA1CN,EAAA,CAAAkD,kBAAA,KAAAjC,MAAA,CAAA2C,YAAA,CAAAP,QAAA,CAAAQ,UAAA,YAA0C;IAe9B7D,EAAA,CAAAM,SAAA,GAAsB;IAAAN,EAAtB,CAAAO,UAAA,YAAA8C,QAAA,CAAAS,WAAA,CAAsB,iBAAA7C,MAAA,CAAA8C,gBAAA,CAAyB;IAyD/D/D,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAAkD,kBAAA,cAAAG,QAAA,CAAAI,KAAA,cAAmC;;;;;IA7FjDzD,EAAA,CAAAE,cAAA,cAAmF;IACjFF,EAAA,CAAAI,UAAA,IAAA4D,6CAAA,mBAIC;IA6FHhE,EAAA,CAAAG,YAAA,EAAM;;;;IAhGgBH,EAAA,CAAAM,SAAA,EAAmB;IAAAN,EAAnB,CAAAO,UAAA,YAAAU,MAAA,CAAAgD,cAAA,CAAmB,iBAAAhD,MAAA,CAAAiD,gBAAA,CAAyB;;;;;IAmGlElE,EAAA,CAAAE,cAAA,cAAyF;IACvFF,EAAA,CAAAC,SAAA,mBAA+D;IAC/DD,EAAA,CAAAE,cAAA,aAAwB;IAAAF,EAAA,CAAAY,MAAA,yBAAkB;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IAC/CH,EAAA,CAAAE,cAAA,YAAyB;IAAAF,EAAA,CAAAY,MAAA,sDAA+C;IAC1EZ,EAD0E,CAAAG,YAAA,EAAI,EACxE;;;AD9HR,OAAM,MAAOgE,uBAAuB;EAoBlCC,YACUC,eAAgC,EAChCC,aAAwC,EACxCC,MAAc;IAFd,KAAAF,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IAtBhB,KAAAN,cAAc,GAAoB,EAAE;IACpC,KAAAO,SAAS,GAAG,IAAI;IAChB,KAAAlD,KAAK,GAAkB,IAAI;IAC3B,KAAAmD,aAAa,GAAG,IAAIC,GAAG,EAAU;IACzB,KAAAC,YAAY,GAAiB,IAAI9E,YAAY,EAAE;IAEvD;IACA,KAAA+E,YAAY,GAAG,CAAC;IAChB,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,SAAS,GAAG,GAAG,CAAC,CAAC;IACjB,KAAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAClB,KAAAC,QAAQ,GAAG,CAAC;IAIZ,KAAAC,cAAc,GAAG,IAAI,CAAC,CAAC;IACvB,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,QAAQ,GAAG,KAAK;EAMb;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACb,YAAY,CAACc,WAAW,EAAE;EACjC;EAEQH,uBAAuBA,CAAA;IAC7B,IAAI,CAACX,YAAY,CAACe,GAAG,CACnB,IAAI,CAACrB,eAAe,CAACsB,eAAe,CAACC,SAAS,CAACC,MAAM,IAAG;MACtD,IAAI,CAAC5B,cAAc,GAAG4B,MAAM;MAC5B,IAAI,CAACrB,SAAS,GAAG,KAAK;IACxB,CAAC,CAAC,CACH;EACH;EAEQe,sBAAsBA,CAAA;IAC5B,IAAI,CAACZ,YAAY,CAACe,GAAG,CACnB,IAAI,CAACpB,aAAa,CAACwB,cAAc,CAACF,SAAS,CAACnB,aAAa,IAAG;MAC1D,IAAI,CAACA,aAAa,GAAGA,aAAa;IACpC,CAAC,CAAC,CACH;EACH;EAEcY,kBAAkBA,CAAA;IAAA,IAAAU,KAAA;IAAA,OAAAC,iBAAA;MAC9B,IAAI;QACFD,KAAI,CAACvB,SAAS,GAAG,IAAI;QACrBuB,KAAI,CAACzE,KAAK,GAAG,IAAI;QACjB,MAAMyE,KAAI,CAAC1B,eAAe,CAACgB,kBAAkB,EAAE;OAChD,CAAC,OAAO/D,KAAK,EAAE;QACd2E,OAAO,CAAC3E,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtDyE,KAAI,CAACzE,KAAK,GAAG,gCAAgC;QAC7CyE,KAAI,CAACvB,SAAS,GAAG,KAAK;;IACvB;EACH;EAEAjB,YAAYA,CAACE,KAAoB;IAC/B,IAAI,CAACc,MAAM,CAAC2B,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;MAClCC,WAAW,EAAE;QAAE1C,KAAK,EAAEA,KAAK,CAACA;MAAK;KAClC,CAAC;EACJ;EAEAvB,cAAcA,CAACkE,OAAgB,EAAEC,KAAY;IAC3CA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,CAAC/B,MAAM,CAAC2B,QAAQ,CAAC,CAAC,UAAU,EAAEE,OAAO,CAACrD,GAAG,CAAC,CAAC;EACjD;EAEMX,aAAaA,CAACgE,OAAgB,EAAEC,KAAY;IAAA,IAAAE,MAAA;IAAA,OAAAP,iBAAA;MAChDK,KAAK,CAACC,eAAe,EAAE;MACvB,IAAI;QACF,MAAME,MAAM,SAASD,MAAI,CAACjC,aAAa,CAACmC,WAAW,CAACL,OAAO,CAACrD,GAAG,CAAC;QAChE,IAAIyD,MAAM,CAACE,OAAO,EAAE;UAClBT,OAAO,CAACU,GAAG,CAACH,MAAM,CAACI,OAAO,CAAC;SAC5B,MAAM;UACLX,OAAO,CAAC3E,KAAK,CAAC,yBAAyB,EAAEkF,MAAM,CAACI,OAAO,CAAC;;OAE3D,CAAC,OAAOtF,KAAK,EAAE;QACd2E,OAAO,CAAC3E,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;IAC9C;EACH;EAEMgB,cAAcA,CAAC8D,OAAgB,EAAEC,KAAY;IAAA,IAAAQ,MAAA;IAAA,OAAAb,iBAAA;MACjDK,KAAK,CAACC,eAAe,EAAE;MACvB,IAAI;QACF,MAAMQ,UAAU,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,YAAYb,OAAO,CAACrD,GAAG,EAAE;QACrE,MAAMmE,SAAS,CAACC,SAAS,CAACC,SAAS,CAACN,UAAU,CAAC;QAE/C,MAAMD,MAAI,CAACvC,aAAa,CAAC+C,YAAY,CAACjB,OAAO,CAACrD,GAAG,EAAE;UACjDuE,QAAQ,EAAE,WAAW;UACrBV,OAAO,EAAE,0BAA0BR,OAAO,CAACvD,IAAI,SAASuD,OAAO,CAAC3C,KAAK;SACtE,CAAC;QAEFwC,OAAO,CAACU,GAAG,CAAC,mCAAmC,CAAC;OACjD,CAAC,OAAOrF,KAAK,EAAE;QACd2E,OAAO,CAAC3E,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;IAC/C;EACH;EAEAC,WAAWA,CAACyB,KAAa;IACvB,OAAO,IAAIuE,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAAC5E,KAAK,CAAC;EAClB;EAEAY,YAAYA,CAACiE,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EAEA3G,OAAOA,CAAA;IACL,IAAI,CAACiE,kBAAkB,EAAE;EAC3B;EAEAnB,gBAAgBA,CAAC8D,KAAa,EAAEvE,KAAoB;IAClD,OAAOA,KAAK,CAACA,KAAK;EACpB;EAEAX,cAAcA,CAACmF,SAAiB;IAC9B,OAAO,IAAI,CAACxD,aAAa,CAACyD,GAAG,CAACD,SAAS,CAAC;EAC1C;EAEAlE,gBAAgBA,CAACiE,KAAa,EAAE5B,OAAgB;IAC9C,OAAOA,OAAO,CAACrD,GAAG;EACpB;EAEA;EACQoF,cAAcA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACjD,aAAa,IAAI,IAAI,CAACC,QAAQ,EAAE;IAE1C,IAAI,CAACiD,aAAa,EAAE;IACpB,IAAI,CAACC,iBAAiB,GAAGC,WAAW,CAAC,MAAK;MACxC,IAAI,CAAC,IAAI,CAACnD,QAAQ,IAAI,IAAI,CAAClB,cAAc,CAACsE,MAAM,GAAG,IAAI,CAACxD,YAAY,EAAE;QACpE,IAAI,CAACyD,aAAa,EAAE;;IAExB,CAAC,EAAE,IAAI,CAACvD,cAAc,CAAC;EACzB;EAEQmD,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACC,iBAAiB,EAAE;MAC1BI,aAAa,CAAC,IAAI,CAACJ,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;EAEjC;EAEQG,aAAaA,CAAA;IACnB,IAAI,IAAI,CAAC5D,YAAY,IAAI,IAAI,CAACI,QAAQ,EAAE;MACtC,IAAI,CAACJ,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;IAErB,IAAI,CAAC8D,iBAAiB,EAAE;EAC1B;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAACxD,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACiD,aAAa,EAAE;EACtB;EAEAQ,eAAeA,CAAA;IACb,IAAI,CAACzD,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACgD,cAAc,EAAE;EACvB;EAEA;EACQU,wBAAwBA,CAAA;IAC9B,MAAMC,KAAK,GAAG/B,MAAM,CAACgC,UAAU;IAC/B,IAAID,KAAK,IAAI,GAAG,EAAE;MAChB,IAAI,CAAChE,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAI+D,KAAK,IAAI,IAAI,EAAE;MACxB,IAAI,CAAChE,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACD,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;;IAEvB,IAAI,CAACiE,kBAAkB,EAAE;IACzB,IAAI,CAACN,iBAAiB,EAAE;EAC1B;EAEQO,mBAAmBA,CAAA;IACzBlC,MAAM,CAACmC,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MACrC,IAAI,CAACL,wBAAwB,EAAE;IACjC,CAAC,CAAC;EACJ;EAEA;EACAG,kBAAkBA,CAAA;IAChB,IAAI,CAAChE,QAAQ,GAAGmE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACnF,cAAc,CAACsE,MAAM,GAAG,IAAI,CAACxD,YAAY,CAAC;EAC7E;EAEAsE,SAASA,CAAA;IACP,IAAI,IAAI,CAACzE,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,IAAI,CAAC8D,iBAAiB,EAAE;MACxB,IAAI,CAACY,gCAAgC,EAAE;;EAE3C;EAEAC,SAASA,CAAA;IACP,IAAI,IAAI,CAAC3E,YAAY,GAAG,IAAI,CAACI,QAAQ,EAAE;MACrC,IAAI,CAACJ,YAAY,EAAE;MACnB,IAAI,CAAC8D,iBAAiB,EAAE;MACxB,IAAI,CAACY,gCAAgC,EAAE;;EAE3C;EAEQZ,iBAAiBA,CAAA;IACvB,IAAI,CAAC7D,WAAW,GAAG,CAAC,IAAI,CAACD,YAAY,GAAG,IAAI,CAACE,SAAS;EACxD;EAEQwE,gCAAgCA,CAAA;IACtC,IAAI,CAAClB,aAAa,EAAE;IACpBoB,UAAU,CAAC,MAAK;MACd,IAAI,CAACrB,cAAc,EAAE;IACvB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACQsB,wBAAwBA,CAAA;IAC9BD,UAAU,CAAC,MAAK;MACd,IAAI,CAACR,kBAAkB,EAAE;MACzB,IAAI,CAACpE,YAAY,GAAG,CAAC;MACrB,IAAI,CAACC,WAAW,GAAG,CAAC;MACpB,IAAI,CAACsD,cAAc,EAAE;IACvB,CAAC,EAAE,GAAG,CAAC;EACT;;;uBAjPWhE,uBAAuB,EAAAnE,EAAA,CAAA0J,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAA5J,EAAA,CAAA0J,iBAAA,CAAAG,EAAA,CAAAC,yBAAA,GAAA9J,EAAA,CAAA0J,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAvB7F,uBAAuB;MAAA8F,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAnK,EAAA,CAAAoK,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCb9B1K,EAJN,CAAAE,cAAA,aAAuC,aAET,aACE,YACA;UACxBF,EAAA,CAAAC,SAAA,kBAAuD;UACvDD,EAAA,CAAAY,MAAA,wBACF;UAAAZ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAE,cAAA,WAA4B;UAAAF,EAAA,CAAAY,MAAA,0CAAmC;UAEnEZ,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAiINH,EA9HA,CAAAI,UAAA,IAAAwK,sCAAA,iBAAiD,IAAAC,sCAAA,iBAeQ,KAAAC,uCAAA,iBAU0B,KAAAC,uCAAA,iBAqGM;UAK3F/K,EAAA,CAAAG,YAAA,EAAM;;;UAnIEH,EAAA,CAAAM,SAAA,GAAe;UAAfN,EAAA,CAAAO,UAAA,SAAAoK,GAAA,CAAAnG,SAAA,CAAe;UAefxE,EAAA,CAAAM,SAAA,EAAyB;UAAzBN,EAAA,CAAAO,UAAA,SAAAoK,GAAA,CAAArJ,KAAA,KAAAqJ,GAAA,CAAAnG,SAAA,CAAyB;UAUzBxE,EAAA,CAAAM,SAAA,EAAuD;UAAvDN,EAAA,CAAAO,UAAA,UAAAoK,GAAA,CAAAnG,SAAA,KAAAmG,GAAA,CAAArJ,KAAA,IAAAqJ,GAAA,CAAA1G,cAAA,CAAAsE,MAAA,KAAuD;UAqGvDvI,EAAA,CAAAM,SAAA,EAAyD;UAAzDN,EAAA,CAAAO,UAAA,UAAAoK,GAAA,CAAAnG,SAAA,KAAAmG,GAAA,CAAArJ,KAAA,IAAAqJ,GAAA,CAAA1G,cAAA,CAAAsE,MAAA,OAAyD;;;qBD9HrD3I,YAAY,EAAAoL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEpL,WAAW,EAAAqL,EAAA,CAAAC,OAAA,EAAErL,cAAc;MAAAsL,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}