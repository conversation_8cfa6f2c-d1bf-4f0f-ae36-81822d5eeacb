{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport { TrendingProductsComponent } from '../trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../new-arrivals/new-arrivals.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/product.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@ionic/angular\";\nimport * as i5 from \"ngx-owl-carousel-o\";\nconst _c0 = () => [1, 2, 3];\nconst _c1 = a0 => [\"/category\", a0];\nfunction SidebarComponent_div_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵelement(2, \"div\", 15)(3, \"div\", 16)(4, \"div\", 17);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SidebarComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵtemplate(1, SidebarComponent_div_7_div_1_Template, 5, 0, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction SidebarComponent_div_8_2_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"img\", 22);\n    i0.ɵɵelementStart(2, \"div\", 23)(3, \"h5\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_8_2_ng_template_0_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const user_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.followUser(user_r2.id));\n    });\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", user_r2.avatar, i0.ɵɵsanitizeUrl)(\"alt\", user_r2.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r2.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r2.followedBy);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", user_r2.isFollowing ? \"Following\" : \"Follow\", \" \");\n  }\n}\nfunction SidebarComponent_div_8_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, SidebarComponent_div_8_2_ng_template_0_Template, 9, 5, \"ng-template\", 20);\n  }\n}\nfunction SidebarComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"owl-carousel-o\", 19);\n    i0.ɵɵtemplate(2, SidebarComponent_div_8_2_Template, 1, 0, null, 10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r2.suggestionsCarouselOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.suggestedUsers);\n  }\n}\nfunction SidebarComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelement(1, \"ion-icon\", 26);\n    i0.ɵɵelementStart(2, \"h3\", 27);\n    i0.ɵɵtext(3, \"No Suggestions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 28);\n    i0.ɵɵtext(5, \"Check back later for user suggestions\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SidebarComponent_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵelement(2, \"div\", 15)(3, \"div\", 16)(4, \"div\", 17);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SidebarComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵtemplate(1, SidebarComponent_div_13_div_1_Template, 5, 0, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction SidebarComponent_div_14_2_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelement(1, \"img\", 22);\n    i0.ɵɵelementStart(2, \"div\", 32)(3, \"h5\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 33)(8, \"span\", 34);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 35);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_14_2_ng_template_0_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const influencer_r5 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.followInfluencer(influencer_r5.id));\n    });\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const influencer_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", influencer_r5.avatar, i0.ɵɵsanitizeUrl)(\"alt\", influencer_r5.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(influencer_r5.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.formatFollowerCount(influencer_r5.followersCount), \" followers\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", influencer_r5.postsCount, \" posts\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", influencer_r5.engagement, \"% engagement\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", influencer_r5.isFollowing ? \"Following\" : \"Follow\", \" \");\n  }\n}\nfunction SidebarComponent_div_14_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, SidebarComponent_div_14_2_ng_template_0_Template, 14, 7, \"ng-template\", 20);\n  }\n}\nfunction SidebarComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"owl-carousel-o\", 30);\n    i0.ɵɵtemplate(2, SidebarComponent_div_14_2_Template, 1, 0, null, 10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r2.influencersCarouselOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.topInfluencers);\n  }\n}\nfunction SidebarComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelement(1, \"ion-icon\", 26);\n    i0.ɵɵelementStart(2, \"h3\", 27);\n    i0.ɵɵtext(3, \"No Influencers Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 28);\n    i0.ɵɵtext(5, \"Check back later for top fashion influencers\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SidebarComponent_21_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelement(1, \"img\", 22);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c1, category_r6.slug));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", category_r6.image, i0.ɵɵsanitizeUrl)(\"alt\", category_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r6.name);\n  }\n}\nfunction SidebarComponent_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, SidebarComponent_21_ng_template_0_Template, 4, 6, \"ng-template\", 20);\n  }\n}\nexport class SidebarComponent {\n  constructor(productService, router) {\n    this.productService = productService;\n    this.router = router;\n    this.suggestedUsers = [];\n    this.trendingProducts = [];\n    this.topInfluencers = [];\n    this.categories = [];\n    this.isLoadingInfluencers = false;\n    this.isLoadingSuggestions = false;\n    // Carousel Options for different sections\n    this.suggestionsCarouselOptions = {\n      loop: true,\n      mouseDrag: true,\n      touchDrag: true,\n      pullDrag: false,\n      dots: false,\n      navSpeed: 700,\n      navText: ['<', '>'],\n      nav: true,\n      autoplay: true,\n      autoplayTimeout: 6000,\n      autoplayHoverPause: true,\n      margin: 2,\n      responsive: {\n        0: {\n          items: 1\n        },\n        600: {\n          items: 2\n        },\n        900: {\n          items: 3\n        }\n      }\n    };\n    this.influencersCarouselOptions = {\n      loop: true,\n      mouseDrag: true,\n      touchDrag: true,\n      pullDrag: false,\n      dots: false,\n      navSpeed: 700,\n      navText: ['<', '>'],\n      nav: true,\n      autoplay: true,\n      autoplayTimeout: 7000,\n      autoplayHoverPause: true,\n      margin: 2,\n      responsive: {\n        0: {\n          items: 1\n        },\n        600: {\n          items: 1.5\n        },\n        900: {\n          items: 2\n        }\n      }\n    };\n    this.categoriesCarouselOptions = {\n      loop: true,\n      mouseDrag: true,\n      touchDrag: true,\n      pullDrag: false,\n      dots: false,\n      navSpeed: 700,\n      navText: ['<', '>'],\n      nav: true,\n      autoplay: true,\n      autoplayTimeout: 5000,\n      autoplayHoverPause: true,\n      margin: 2,\n      responsive: {\n        0: {\n          items: 2\n        },\n        600: {\n          items: 3\n        },\n        900: {\n          items: 4\n        }\n      }\n    };\n  }\n  ngOnInit() {\n    this.loadSuggestedUsers();\n    this.loadTrendingProducts();\n    this.loadTopInfluencers();\n    this.loadCategories();\n  }\n  loadSuggestedUsers() {\n    this.isLoadingSuggestions = true;\n    // Simulate API call\n    setTimeout(() => {\n      this.suggestedUsers = [{\n        id: '1',\n        username: '@arjun_style',\n        fullName: 'Arjun Kumar',\n        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',\n        followedBy: 'Followed by maya_fashion + 12 others',\n        isFollowing: false\n      }, {\n        id: '2',\n        username: '@sneha_trends',\n        fullName: 'Sneha Reddy',\n        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100',\n        followedBy: 'Followed by raj_style + 8 others',\n        isFollowing: false\n      }, {\n        id: '3',\n        username: '@fashion_vikram',\n        fullName: 'Vikram Singh',\n        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100',\n        followedBy: 'Followed by priya_chic + 15 others',\n        isFollowing: true\n      }];\n      this.isLoadingSuggestions = false;\n    }, 1200);\n  }\n  loadTrendingProducts() {\n    // Load from API - empty for now\n    this.trendingProducts = [];\n  }\n  loadTopInfluencers() {\n    this.isLoadingInfluencers = true;\n    // Simulate API call\n    setTimeout(() => {\n      this.topInfluencers = [{\n        id: '1',\n        username: '@fashionista_maya',\n        fullName: 'Maya Sharma',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=100',\n        followersCount: 125000,\n        postsCount: 342,\n        engagement: 8.5,\n        isFollowing: false\n      }, {\n        id: '2',\n        username: '@style_guru_raj',\n        fullName: 'Raj Patel',\n        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100',\n        followersCount: 89000,\n        postsCount: 198,\n        engagement: 7.2,\n        isFollowing: true\n      }, {\n        id: '3',\n        username: '@trendy_priya',\n        fullName: 'Priya Singh',\n        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100',\n        followersCount: 67000,\n        postsCount: 156,\n        engagement: 9.1,\n        isFollowing: false\n      }];\n      this.isLoadingInfluencers = false;\n    }, 1500);\n  }\n  loadCategories() {\n    this.categories = [{\n      name: 'Women',\n      slug: 'women',\n      image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=100'\n    }, {\n      name: 'Men',\n      slug: 'men',\n      image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=100'\n    }, {\n      name: 'Kids',\n      slug: 'children',\n      image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=100'\n    }, {\n      name: 'Ethnic',\n      slug: 'ethnic',\n      image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=100'\n    }];\n  }\n  formatFollowerCount(count) {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'k';\n    }\n    return count.toString();\n  }\n  followUser(userId) {\n    const user = this.suggestedUsers.find(u => u.id === userId);\n    if (user) {\n      user.isFollowing = !user.isFollowing;\n    }\n  }\n  followInfluencer(influencerId) {\n    const influencer = this.topInfluencers.find(i => i.id === influencerId);\n    if (influencer) {\n      influencer.isFollowing = !influencer.isFollowing;\n    }\n  }\n  quickBuy(productId) {\n    console.log('Quick buy product:', productId);\n    // TODO: Implement quick buy functionality\n  }\n  browseCategory(categorySlug) {\n    console.log('Browse category:', categorySlug);\n    this.router.navigate(['/category', categorySlug]);\n  }\n  static {\n    this.ɵfac = function SidebarComponent_Factory(t) {\n      return new (t || SidebarComponent)(i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SidebarComponent,\n      selectors: [[\"app-sidebar\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 22,\n      vars: 8,\n      consts: [[1, \"sidebar\"], [1, \"suggestions\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"suggestions-slider-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"influencers\"], [\"class\", \"influencers-slider-container\", 4, \"ngIf\"], [1, \"categories\"], [1, \"categories-slider-container\"], [1, \"categories-slider\", 3, \"options\"], [4, \"ngFor\", \"ngForOf\"], [1, \"loading-container\"], [\"class\", \"loading-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-card\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [1, \"suggestions-slider-container\"], [1, \"suggestions-slider\", 3, \"options\"], [\"carouselSlide\", \"\"], [1, \"suggestion-item\"], [3, \"src\", \"alt\"], [1, \"suggestion-info\"], [1, \"follow-btn\", 3, \"click\"], [1, \"empty-container\"], [\"name\", \"people-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"], [1, \"influencers-slider-container\"], [1, \"influencers-slider\", 3, \"options\"], [1, \"influencer-item\"], [1, \"influencer-info\"], [1, \"influencer-stats\"], [1, \"posts-count\"], [1, \"engagement\"], [\"routerLinkActive\", \"active\", \"tabindex\", \"0\", 1, \"category-item\", 3, \"routerLink\"]],\n      template: function SidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"aside\", 0);\n          i0.ɵɵelement(1, \"app-trending-products\")(2, \"app-featured-brands\")(3, \"app-new-arrivals\");\n          i0.ɵɵelementStart(4, \"div\", 1)(5, \"h3\");\n          i0.ɵɵtext(6, \"Suggested for you\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, SidebarComponent_div_7_Template, 2, 2, \"div\", 2)(8, SidebarComponent_div_8_Template, 3, 2, \"div\", 3)(9, SidebarComponent_div_9_Template, 6, 0, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 5)(11, \"h3\");\n          i0.ɵɵtext(12, \"Top Fashion Influencers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(13, SidebarComponent_div_13_Template, 2, 2, \"div\", 2)(14, SidebarComponent_div_14_Template, 3, 2, \"div\", 6)(15, SidebarComponent_div_15_Template, 6, 0, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 7)(17, \"h3\");\n          i0.ɵɵtext(18, \"Shop by Category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 8)(20, \"owl-carousel-o\", 9);\n          i0.ɵɵtemplate(21, SidebarComponent_21_Template, 1, 0, null, 10);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingSuggestions);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingSuggestions && ctx.suggestedUsers.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingSuggestions && ctx.suggestedUsers.length === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingInfluencers);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingInfluencers && ctx.topInfluencers.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingInfluencers && ctx.topInfluencers.length === 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"options\", ctx.categoriesCarouselOptions);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, RouterModule, i2.RouterLink, i2.RouterLinkActive, IonicModule, i4.IonIcon, i4.RouterLinkDelegate, CarouselModule, i5.CarouselComponent, i5.CarouselSlideDirective, TrendingProductsComponent, FeaturedBrandsComponent, NewArrivalsComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.sidebar[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 80px;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n  max-height: calc(100vh - 100px);\\n  overflow-y: auto;\\n  padding-right: 8px;\\n}\\n\\napp-view-add-stories[_ngcontent-%COMP%], app-trending-products[_ngcontent-%COMP%], app-featured-brands[_ngcontent-%COMP%], app-new-arrivals[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: block;\\n  margin-bottom: 24px;\\n}\\n\\n.suggestions[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n  box-shadow: 0 8px 32px rgba(255, 107, 107, 0.2);\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestions-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestions-slider-container[_ngcontent-%COMP%]   .suggestions-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 100%;\\n  pointer-events: none;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestions-slider-container[_ngcontent-%COMP%]   .suggestions-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-prev[_ngcontent-%COMP%], .suggestions[_ngcontent-%COMP%]   .suggestions-slider-container[_ngcontent-%COMP%]   .suggestions-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-next[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 40px;\\n  height: 40px;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  border: none;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 18px;\\n  font-weight: bold;\\n  cursor: pointer;\\n  pointer-events: all;\\n  transition: all 0.3s ease;\\n  z-index: 10;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestions-slider-container[_ngcontent-%COMP%]   .suggestions-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-prev[_ngcontent-%COMP%]:hover, .suggestions[_ngcontent-%COMP%]   .suggestions-slider-container[_ngcontent-%COMP%]   .suggestions-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-next[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.9);\\n  transform: scale(1.1);\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestions-slider-container[_ngcontent-%COMP%]   .suggestions-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-prev[_ngcontent-%COMP%] {\\n  left: -20px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestions-slider-container[_ngcontent-%COMP%]   .suggestions-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-next[_ngcontent-%COMP%] {\\n  right: -20px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .suggestions-slider-container[_ngcontent-%COMP%]   .suggestions-slider[_ngcontent-%COMP%]   .owl-dots[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.categories[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n  box-shadow: 0 8px 32px rgba(0, 210, 211, 0.2);\\n}\\n\\n.trending[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  padding: 16px;\\n}\\n\\n.influencers[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencers-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencers-slider-container[_ngcontent-%COMP%]   .influencers-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 100%;\\n  pointer-events: none;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencers-slider-container[_ngcontent-%COMP%]   .influencers-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-prev[_ngcontent-%COMP%], .influencers[_ngcontent-%COMP%]   .influencers-slider-container[_ngcontent-%COMP%]   .influencers-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-next[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 40px;\\n  height: 40px;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  border: none;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 18px;\\n  font-weight: bold;\\n  cursor: pointer;\\n  pointer-events: all;\\n  transition: all 0.3s ease;\\n  z-index: 10;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencers-slider-container[_ngcontent-%COMP%]   .influencers-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-prev[_ngcontent-%COMP%]:hover, .influencers[_ngcontent-%COMP%]   .influencers-slider-container[_ngcontent-%COMP%]   .influencers-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-next[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.9);\\n  transform: scale(1.1);\\n}\\n.influencers[_ngcontent-%COMP%]   .influencers-slider-container[_ngcontent-%COMP%]   .influencers-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-prev[_ngcontent-%COMP%] {\\n  left: -20px;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencers-slider-container[_ngcontent-%COMP%]   .influencers-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-next[_ngcontent-%COMP%] {\\n  right: -20px;\\n}\\n.influencers[_ngcontent-%COMP%]   .influencers-slider-container[_ngcontent-%COMP%]   .influencers-slider[_ngcontent-%COMP%]   .owl-dots[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDCA1\\\";\\n  font-size: 28px;\\n}\\n\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDECD\\uFE0F\\\";\\n  font-size: 28px;\\n}\\n\\n.trending[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin-bottom: 16px;\\n  color: #8e8e8e;\\n}\\n\\n.influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDC51\\\";\\n  font-size: 28px;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 16px;\\n  margin-bottom: 16px;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 16px;\\n  margin-bottom: 16px;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);\\n}\\n\\n.influencer-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid rgba(255, 255, 255, 0.3);\\n  transition: transform 0.3s ease;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.suggestion-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.suggestion-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 4px;\\n}\\n\\n.suggestion-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 8px 16px;\\n  border-radius: 8px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n  transform: translateY(-2px);\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  padding: 16px;\\n  margin-bottom: 16px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.suggestions[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  background: rgba(255, 255, 255, 0.2);\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  border-radius: 6px;\\n  margin-bottom: 8px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.suggestions[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.suggestions[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: rgba(255, 255, 255, 0.4);\\n  margin-bottom: 16px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 8px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.influencer-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 16px;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid rgba(255, 255, 255, 0.3);\\n  transition: transform 0.3s ease;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.influencer-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.influencer-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 4px;\\n}\\n\\n.influencer-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.7);\\n  margin-bottom: 8px;\\n}\\n\\n.influencer-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n  margin-bottom: 12px;\\n}\\n\\n.influencer-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: rgba(255, 255, 255, 0.6);\\n  background: rgba(255, 255, 255, 0.1);\\n  padding: 4px 8px;\\n  border-radius: 8px;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  -webkit-backdrop-filter: blur(5px);\\n          backdrop-filter: blur(5px);\\n}\\n\\n.follow-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 8px 16px;\\n  border-radius: 8px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  align-self: flex-start;\\n}\\n\\n.follow-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n  transform: translateY(-2px);\\n}\\n\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  padding: 16px;\\n  margin-bottom: 16px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  background: rgba(255, 255, 255, 0.2);\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  border-radius: 6px;\\n  margin-bottom: 8px;\\n}\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0%, 100% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n.influencers[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.influencers[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: rgba(255, 255, 255, 0.4);\\n  margin-bottom: 16px;\\n}\\n.influencers[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 8px;\\n}\\n.influencers[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.trending-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n  padding-bottom: 16px;\\n  border-bottom: 1px solid #f1f5f9;\\n}\\n\\n.trending-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n  margin-bottom: 0;\\n  padding-bottom: 0;\\n}\\n\\n.trending-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 8px;\\n  object-fit: cover;\\n}\\n\\n.trending-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.trending-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin-bottom: 4px;\\n}\\n\\n.trending-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: var(--primary-color);\\n  margin-bottom: 8px;\\n}\\n\\n.original-price[_ngcontent-%COMP%] {\\n  text-decoration: line-through;\\n  color: #8e8e8e;\\n  font-weight: 400;\\n  margin-left: 4px;\\n}\\n\\n.trending-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n\\n.trending-badge[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 600;\\n  background: #fef3c7;\\n  color: #92400e;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n\\n.views[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #8e8e8e;\\n}\\n\\n.quick-buy-btn[_ngcontent-%COMP%] {\\n  background: #f1f5f9;\\n  color: #64748b;\\n  border: 1px solid #e2e8f0;\\n  padding: 6px 12px;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n  align-self: flex-start;\\n}\\n\\n.quick-buy-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-color);\\n  color: #fff;\\n  border-color: var(--primary-color);\\n}\\n\\n.categories[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1f2937;\\n  margin-bottom: 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDECD\\uFE0F\\\";\\n  font-size: 18px;\\n}\\n\\n.category-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 16px;\\n}\\n\\n.category-item[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 20px 16px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-align: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  text-decoration: none;\\n  color: white;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.category-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);\\n}\\n\\n.category-item.active[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  border-color: rgba(255, 255, 255, 0.4);\\n  transform: translateY(-4px);\\n}\\n\\n.category-item.active[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n.category-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  margin-bottom: 12px;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  transition: transform 0.3s ease;\\n}\\n\\n.category-item[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.category-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: white;\\n  text-align: center;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.category-item[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.category-item[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n\\n@media (max-width: 1024px) {\\n  .sidebar[_ngcontent-%COMP%] {\\n    order: -1;\\n    position: static;\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n    gap: 20px;\\n    max-height: none;\\n    overflow-y: visible;\\n  }\\n  app-view-add-stories[_ngcontent-%COMP%], app-trending-products[_ngcontent-%COMP%], app-featured-brands[_ngcontent-%COMP%], app-new-arrivals[_ngcontent-%COMP%] {\\n    margin-bottom: 16px;\\n  }\\n  .suggestions[_ngcontent-%COMP%], .influencers[_ngcontent-%COMP%], .categories[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .suggestion-item[_ngcontent-%COMP%], .influencer-item[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .suggestion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n  }\\n  .category-grid[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n  .category-item[_ngcontent-%COMP%] {\\n    padding: 16px 12px;\\n  }\\n  .category-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .sidebar[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  app-view-add-stories[_ngcontent-%COMP%], app-trending-products[_ngcontent-%COMP%], app-featured-brands[_ngcontent-%COMP%], app-new-arrivals[_ngcontent-%COMP%] {\\n    margin-bottom: 12px;\\n  }\\n  .suggestions[_ngcontent-%COMP%], .influencers[_ngcontent-%COMP%], .categories[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .suggestion-item[_ngcontent-%COMP%], .influencer-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n    padding: 16px;\\n  }\\n  .suggestion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n    margin-bottom: 12px;\\n  }\\n  .influencer-stats[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    justify-content: center;\\n    gap: 8px;\\n  }\\n  .category-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 12px;\\n  }\\n  .category-item[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    text-align: left;\\n    padding: 12px;\\n    gap: 12px;\\n  }\\n  .category-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n    margin-bottom: 0;\\n  }\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "IonicModule", "CarouselModule", "TrendingProductsComponent", "FeaturedBrandsComponent", "NewArrivalsComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "SidebarComponent_div_7_div_1_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵtext", "ɵɵlistener", "SidebarComponent_div_8_2_ng_template_0_Template_button_click_7_listener", "ɵɵrestoreView", "_r1", "user_r2", "ɵɵnextContext", "$implicit", "ctx_r2", "ɵɵresetView", "followUser", "id", "avatar", "ɵɵsanitizeUrl", "fullName", "ɵɵtextInterpolate", "username", "<PERSON><PERSON><PERSON>", "ɵɵtextInterpolate1", "isFollowing", "SidebarComponent_div_8_2_ng_template_0_Template", "SidebarComponent_div_8_2_Template", "suggestionsCarouselOptions", "suggestedUsers", "SidebarComponent_div_13_div_1_Template", "SidebarComponent_div_14_2_ng_template_0_Template_button_click_12_listener", "_r4", "influencer_r5", "followInfluencer", "formatFollowerCount", "followersCount", "postsCount", "engagement", "SidebarComponent_div_14_2_ng_template_0_Template", "SidebarComponent_div_14_2_Template", "influencersCarouselOptions", "topInfluencers", "ɵɵpureFunction1", "_c1", "category_r6", "slug", "image", "name", "SidebarComponent_21_ng_template_0_Template", "SidebarComponent", "constructor", "productService", "router", "trendingProducts", "categories", "isLoadingInfluencers", "isLoadingSuggestions", "loop", "mouseDrag", "touchDrag", "pullDrag", "dots", "navSpeed", "navText", "nav", "autoplay", "autoplayTimeout", "autoplayHoverPause", "margin", "responsive", "items", "categoriesCarouselOptions", "ngOnInit", "loadSuggestedUsers", "loadTrendingProducts", "loadTopInfluencers", "loadCategories", "setTimeout", "count", "toFixed", "toString", "userId", "user", "find", "u", "influencerId", "influencer", "i", "quickBuy", "productId", "console", "log", "browseCategory", "categorySlug", "navigate", "ɵɵdirectiveInject", "i1", "ProductService", "i2", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SidebarComponent_Template", "rf", "ctx", "SidebarComponent_div_7_Template", "SidebarComponent_div_8_Template", "SidebarComponent_div_9_Template", "SidebarComponent_div_13_Template", "SidebarComponent_div_14_Template", "SidebarComponent_div_15_Template", "SidebarComponent_21_Template", "length", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "RouterLink", "RouterLinkActive", "i4", "IonIcon", "RouterLinkDelegate", "i5", "CarouselComponent", "CarouselSlideDirective", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\sidebar\\sidebar.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\sidebar\\sidebar.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport { OwlOptions } from 'ngx-owl-carousel-o';\n\nimport { ProductService } from '../../../../core/services/product.service';\nimport { Product } from '../../../../core/models/product.model';\nimport { TrendingProductsComponent } from '../trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../new-arrivals/new-arrivals.component';\n\n@Component({\n  selector: 'app-sidebar',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    IonicModule,\n    CarouselModule,\n    TrendingProductsComponent,\n    FeaturedBrandsComponent,\n    NewArrivalsComponent\n  ],\n  templateUrl: './sidebar.component.html',\n  styleUrls: ['./sidebar.component.scss']\n})\nexport class SidebarComponent implements OnInit {\n  suggestedUsers: any[] = [];\n  trendingProducts: Product[] = [];\n  topInfluencers: any[] = [];\n  categories: any[] = [];\n  isLoadingInfluencers: boolean = false;\n  isLoadingSuggestions: boolean = false;\n\n  // Carousel Options for different sections\n  suggestionsCarouselOptions: OwlOptions = {\n    loop: true,\n    mouseDrag: true,\n    touchDrag: true,\n    pullDrag: false,\n    dots: false,\n    navSpeed: 700,\n    navText: ['<', '>'],\n    nav: true,\n    autoplay: true,\n    autoplayTimeout: 6000,\n    autoplayHoverPause: true,\n    margin: 2,\n    responsive: {\n      0: {\n        items: 1\n      },\n      600: {\n        items: 2\n      },\n      900: {\n        items: 3\n      }\n    }\n  };\n\n  influencersCarouselOptions: OwlOptions = {\n    loop: true,\n    mouseDrag: true,\n    touchDrag: true,\n    pullDrag: false,\n    dots: false,\n    navSpeed: 700,\n    navText: ['<', '>'],\n    nav: true,\n    autoplay: true,\n    autoplayTimeout: 7000,\n    autoplayHoverPause: true,\n    margin: 2,\n    responsive: {\n      0: {\n        items: 1\n      },\n      600: {\n        items: 1.5\n      },\n      900: {\n        items: 2\n      }\n    }\n  };\n\n  categoriesCarouselOptions: OwlOptions = {\n    loop: true,\n    mouseDrag: true,\n    touchDrag: true,\n    pullDrag: false,\n    dots: false,\n    navSpeed: 700,\n    navText: ['<', '>'],\n    nav: true,\n    autoplay: true,\n    autoplayTimeout: 5000,\n    autoplayHoverPause: true,\n    margin: 2,\n    responsive: {\n      0: {\n        items: 2\n      },\n      600: {\n        items: 3\n      },\n      900: {\n        items: 4\n      }\n    }\n  };\n\n  constructor(\n    private productService: ProductService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadSuggestedUsers();\n    this.loadTrendingProducts();\n    this.loadTopInfluencers();\n    this.loadCategories();\n  }\n\n  loadSuggestedUsers() {\n    this.isLoadingSuggestions = true;\n\n    // Simulate API call\n    setTimeout(() => {\n      this.suggestedUsers = [\n        {\n          id: '1',\n          username: '@arjun_style',\n          fullName: 'Arjun Kumar',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',\n          followedBy: 'Followed by maya_fashion + 12 others',\n          isFollowing: false\n        },\n        {\n          id: '2',\n          username: '@sneha_trends',\n          fullName: 'Sneha Reddy',\n          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100',\n          followedBy: 'Followed by raj_style + 8 others',\n          isFollowing: false\n        },\n        {\n          id: '3',\n          username: '@fashion_vikram',\n          fullName: 'Vikram Singh',\n          avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100',\n          followedBy: 'Followed by priya_chic + 15 others',\n          isFollowing: true\n        }\n      ];\n      this.isLoadingSuggestions = false;\n    }, 1200);\n  }\n\n  loadTrendingProducts() {\n    // Load from API - empty for now\n    this.trendingProducts = [];\n  }\n\n  loadTopInfluencers() {\n    this.isLoadingInfluencers = true;\n\n    // Simulate API call\n    setTimeout(() => {\n      this.topInfluencers = [\n        {\n          id: '1',\n          username: '@fashionista_maya',\n          fullName: 'Maya Sharma',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=100',\n          followersCount: 125000,\n          postsCount: 342,\n          engagement: 8.5,\n          isFollowing: false\n        },\n        {\n          id: '2',\n          username: '@style_guru_raj',\n          fullName: 'Raj Patel',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100',\n          followersCount: 89000,\n          postsCount: 198,\n          engagement: 7.2,\n          isFollowing: true\n        },\n        {\n          id: '3',\n          username: '@trendy_priya',\n          fullName: 'Priya Singh',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100',\n          followersCount: 67000,\n          postsCount: 156,\n          engagement: 9.1,\n          isFollowing: false\n        }\n      ];\n      this.isLoadingInfluencers = false;\n    }, 1500);\n  }\n\n  loadCategories() {\n    this.categories = [\n      {\n        name: 'Women',\n        slug: 'women',\n        image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=100'\n      },\n      {\n        name: 'Men',\n        slug: 'men',\n        image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=100'\n      },\n      {\n        name: 'Kids',\n        slug: 'children',\n        image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=100'\n      },\n      {\n        name: 'Ethnic',\n        slug: 'ethnic',\n        image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=100'\n      }\n    ];\n  }\n\n  formatFollowerCount(count: number): string {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'k';\n    }\n    return count.toString();\n  }\n\n  followUser(userId: string) {\n    const user = this.suggestedUsers.find(u => u.id === userId);\n    if (user) {\n      user.isFollowing = !user.isFollowing;\n    }\n  }\n\n  followInfluencer(influencerId: string) {\n    const influencer = this.topInfluencers.find(i => i.id === influencerId);\n    if (influencer) {\n      influencer.isFollowing = !influencer.isFollowing;\n    }\n  }\n\n  quickBuy(productId: string) {\n    console.log('Quick buy product:', productId);\n    // TODO: Implement quick buy functionality\n  }\n\n  browseCategory(categorySlug: string) {\n    console.log('Browse category:', categorySlug);\n    this.router.navigate(['/category', categorySlug]);\n  }\n}\n", "<aside class=\"sidebar\">\n  <!-- Instagram-style Stories -->\n \n\n  <!-- Trending Products Section -->\n  <app-trending-products></app-trending-products>\n\n  <!-- Featured Brands Section -->\n  <app-featured-brands></app-featured-brands>\n\n  <!-- New Arrivals Section -->\n  <app-new-arrivals></app-new-arrivals>\n\n  <!-- Suggested for you -->\n  <div class=\"suggestions\">\n    <h3>Suggested for you</h3>\n\n    <!-- Loading State -->\n    <div *ngIf=\"isLoadingSuggestions\" class=\"loading-container\">\n      <div class=\"loading-card\" *ngFor=\"let item of [1,2,3]\">\n        <div class=\"loading-content\">\n          <div class=\"loading-line short\"></div>\n          <div class=\"loading-line medium\"></div>\n          <div class=\"loading-line long\"></div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Suggestions Slider -->\n    <div *ngIf=\"!isLoadingSuggestions && suggestedUsers.length > 0\" class=\"suggestions-slider-container\">\n      <owl-carousel-o [options]=\"suggestionsCarouselOptions\" class=\"suggestions-slider\">\n        <ng-template carouselSlide *ngFor=\"let user of suggestedUsers\">\n          <div class=\"suggestion-item\">\n            <img [src]=\"user.avatar\" [alt]=\"user.fullName\">\n            <div class=\"suggestion-info\">\n              <h5>{{ user.username }}</h5>\n              <p>{{ user.followedBy }}</p>\n            </div>\n            <button class=\"follow-btn\" (click)=\"followUser(user.id)\">\n              {{ user.isFollowing ? 'Following' : 'Follow' }}\n            </button>\n          </div>\n        </ng-template>\n      </owl-carousel-o>\n    </div>\n\n    <!-- Empty State -->\n    <div *ngIf=\"!isLoadingSuggestions && suggestedUsers.length === 0\" class=\"empty-container\">\n      <ion-icon name=\"people-outline\" class=\"empty-icon\"></ion-icon>\n      <h3 class=\"empty-title\">No Suggestions</h3>\n      <p class=\"empty-message\">Check back later for user suggestions</p>\n    </div>\n  </div>\n\n  <!-- Top Fashion Influencers -->\n  <div class=\"influencers\">\n    <h3>Top Fashion Influencers</h3>\n\n    <!-- Loading State -->\n    <div *ngIf=\"isLoadingInfluencers\" class=\"loading-container\">\n      <div class=\"loading-card\" *ngFor=\"let item of [1,2,3]\">\n        <div class=\"loading-content\">\n          <div class=\"loading-line short\"></div>\n          <div class=\"loading-line medium\"></div>\n          <div class=\"loading-line long\"></div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Influencers Slider -->\n    <div *ngIf=\"!isLoadingInfluencers && topInfluencers.length > 0\" class=\"influencers-slider-container\">\n      <owl-carousel-o [options]=\"influencersCarouselOptions\" class=\"influencers-slider\">\n        <ng-template carouselSlide *ngFor=\"let influencer of topInfluencers\">\n          <div class=\"influencer-item\">\n            <img [src]=\"influencer.avatar\" [alt]=\"influencer.fullName\">\n            <div class=\"influencer-info\">\n              <h5>{{ influencer.username }}</h5>\n              <p>{{ formatFollowerCount(influencer.followersCount) }} followers</p>\n              <div class=\"influencer-stats\">\n                <span class=\"posts-count\">{{ influencer.postsCount }} posts</span>\n                <span class=\"engagement\">{{ influencer.engagement }}% engagement</span>\n              </div>\n              <button class=\"follow-btn\" (click)=\"followInfluencer(influencer.id)\">\n                {{ influencer.isFollowing ? 'Following' : 'Follow' }}\n              </button>\n            </div>\n          </div>\n        </ng-template>\n      </owl-carousel-o>\n    </div>\n\n    <!-- Empty State -->\n    <div *ngIf=\"!isLoadingInfluencers && topInfluencers.length === 0\" class=\"empty-container\">\n      <ion-icon name=\"people-outline\" class=\"empty-icon\"></ion-icon>\n      <h3 class=\"empty-title\">No Influencers Found</h3>\n      <p class=\"empty-message\">Check back later for top fashion influencers</p>\n    </div>\n  </div>\n\n  <!-- Shop by Category -->\n  <div class=\"categories\">\n    <h3>Shop by Category</h3>\n    <div class=\"categories-slider-container\">\n      <owl-carousel-o [options]=\"categoriesCarouselOptions\" class=\"categories-slider\">\n        <ng-template carouselSlide *ngFor=\"let category of categories\">\n          <div\n            class=\"category-item\"\n            [routerLink]=\"['/category', category.slug]\"\n            routerLinkActive=\"active\"\n            tabindex=\"0\"\n          >\n            <img [src]=\"category.image\" [alt]=\"category.name\">\n            <span>{{ category.name }}</span>\n          </div>\n        </ng-template>\n      </owl-carousel-o>\n    </div>\n  </div>\n\n\n</aside>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,oBAAoB;AAKnD,SAASC,yBAAyB,QAAQ,kDAAkD;AAC5F,SAASC,uBAAuB,QAAQ,8CAA8C;AACtF,SAASC,oBAAoB,QAAQ,wCAAwC;;;;;;;;;;;ICSrEC,EADF,CAAAC,cAAA,cAAuD,cACxB;IAG3BD,EAFA,CAAAE,SAAA,cAAsC,cACC,cACF;IAEzCF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAPRH,EAAA,CAAAC,cAAA,cAA4D;IAC1DD,EAAA,CAAAI,UAAA,IAAAC,qCAAA,kBAAuD;IAOzDL,EAAA,CAAAG,YAAA,EAAM;;;IAPuCH,EAAA,CAAAM,SAAA,EAAU;IAAVN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAU;;;;;;IAajDT,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAE,SAAA,cAA+C;IAE7CF,EADF,CAAAC,cAAA,cAA6B,SACvB;IAAAD,EAAA,CAAAU,MAAA,GAAmB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,GAAqB;IAC1BV,EAD0B,CAAAG,YAAA,EAAI,EACxB;IACNH,EAAA,CAAAC,cAAA,iBAAyD;IAA9BD,EAAA,CAAAW,UAAA,mBAAAC,wEAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,OAAA,GAAAf,EAAA,CAAAgB,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAE,UAAA,CAAAL,OAAA,CAAAM,EAAA,CAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAG,YAAA,EAAS,EACL;;;;IARCH,EAAA,CAAAM,SAAA,EAAmB;IAACN,EAApB,CAAAO,UAAA,QAAAQ,OAAA,CAAAO,MAAA,EAAAtB,EAAA,CAAAuB,aAAA,CAAmB,QAAAR,OAAA,CAAAS,QAAA,CAAsB;IAExCxB,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAyB,iBAAA,CAAAV,OAAA,CAAAW,QAAA,CAAmB;IACpB1B,EAAA,CAAAM,SAAA,GAAqB;IAArBN,EAAA,CAAAyB,iBAAA,CAAAV,OAAA,CAAAY,UAAA,CAAqB;IAGxB3B,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAA4B,kBAAA,MAAAb,OAAA,CAAAc,WAAA,+BACF;;;;;IATJ7B,EAAA,CAAAI,UAAA,IAAA0B,+CAAA,0BAA+D;;;;;IADjE9B,EADF,CAAAC,cAAA,cAAqG,yBACjB;IAChFD,EAAA,CAAAI,UAAA,IAAA2B,iCAAA,iBAA+D;IAanE/B,EADE,CAAAG,YAAA,EAAiB,EACb;;;;IAdYH,EAAA,CAAAM,SAAA,EAAsC;IAAtCN,EAAA,CAAAO,UAAA,YAAAW,MAAA,CAAAc,0BAAA,CAAsC;IACRhC,EAAA,CAAAM,SAAA,EAAiB;IAAjBN,EAAA,CAAAO,UAAA,YAAAW,MAAA,CAAAe,cAAA,CAAiB;;;;;IAgBjEjC,EAAA,CAAAC,cAAA,cAA0F;IACxFD,EAAA,CAAAE,SAAA,mBAA8D;IAC9DF,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAU,MAAA,qBAAc;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAU,MAAA,4CAAqC;IAChEV,EADgE,CAAAG,YAAA,EAAI,EAC9D;;;;;IAUFH,EADF,CAAAC,cAAA,cAAuD,cACxB;IAG3BD,EAFA,CAAAE,SAAA,cAAsC,cACC,cACF;IAEzCF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAPRH,EAAA,CAAAC,cAAA,cAA4D;IAC1DD,EAAA,CAAAI,UAAA,IAAA8B,sCAAA,kBAAuD;IAOzDlC,EAAA,CAAAG,YAAA,EAAM;;;IAPuCH,EAAA,CAAAM,SAAA,EAAU;IAAVN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAU;;;;;;IAajDT,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAE,SAAA,cAA2D;IAEzDF,EADF,CAAAC,cAAA,cAA6B,SACvB;IAAAD,EAAA,CAAAU,MAAA,GAAyB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,GAA8D;IAAAV,EAAA,CAAAG,YAAA,EAAI;IAEnEH,EADF,CAAAC,cAAA,cAA8B,eACF;IAAAD,EAAA,CAAAU,MAAA,GAAiC;IAAAV,EAAA,CAAAG,YAAA,EAAO;IAClEH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAU,MAAA,IAAuC;IAClEV,EADkE,CAAAG,YAAA,EAAO,EACnE;IACNH,EAAA,CAAAC,cAAA,kBAAqE;IAA1CD,EAAA,CAAAW,UAAA,mBAAAwB,0EAAA;MAAAnC,EAAA,CAAAa,aAAA,CAAAuB,GAAA;MAAA,MAAAC,aAAA,GAAArC,EAAA,CAAAgB,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAlB,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAmB,WAAA,CAASD,MAAA,CAAAoB,gBAAA,CAAAD,aAAA,CAAAhB,EAAA,CAA+B;IAAA,EAAC;IAClErB,EAAA,CAAAU,MAAA,IACF;IAEJV,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;;IAZCH,EAAA,CAAAM,SAAA,EAAyB;IAACN,EAA1B,CAAAO,UAAA,QAAA8B,aAAA,CAAAf,MAAA,EAAAtB,EAAA,CAAAuB,aAAA,CAAyB,QAAAc,aAAA,CAAAb,QAAA,CAA4B;IAEpDxB,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAyB,iBAAA,CAAAY,aAAA,CAAAX,QAAA,CAAyB;IAC1B1B,EAAA,CAAAM,SAAA,GAA8D;IAA9DN,EAAA,CAAA4B,kBAAA,KAAAV,MAAA,CAAAqB,mBAAA,CAAAF,aAAA,CAAAG,cAAA,gBAA8D;IAErCxC,EAAA,CAAAM,SAAA,GAAiC;IAAjCN,EAAA,CAAA4B,kBAAA,KAAAS,aAAA,CAAAI,UAAA,WAAiC;IAClCzC,EAAA,CAAAM,SAAA,GAAuC;IAAvCN,EAAA,CAAA4B,kBAAA,KAAAS,aAAA,CAAAK,UAAA,iBAAuC;IAGhE1C,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAA4B,kBAAA,MAAAS,aAAA,CAAAR,WAAA,+BACF;;;;;IAZN7B,EAAA,CAAAI,UAAA,IAAAuC,gDAAA,2BAAqE;;;;;IADvE3C,EADF,CAAAC,cAAA,cAAqG,yBACjB;IAChFD,EAAA,CAAAI,UAAA,IAAAwC,kCAAA,iBAAqE;IAiBzE5C,EADE,CAAAG,YAAA,EAAiB,EACb;;;;IAlBYH,EAAA,CAAAM,SAAA,EAAsC;IAAtCN,EAAA,CAAAO,UAAA,YAAAW,MAAA,CAAA2B,0BAAA,CAAsC;IACF7C,EAAA,CAAAM,SAAA,EAAiB;IAAjBN,EAAA,CAAAO,UAAA,YAAAW,MAAA,CAAA4B,cAAA,CAAiB;;;;;IAoBvE9C,EAAA,CAAAC,cAAA,cAA0F;IACxFD,EAAA,CAAAE,SAAA,mBAA8D;IAC9DF,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAU,MAAA,2BAAoB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAU,MAAA,mDAA4C;IACvEV,EADuE,CAAAG,YAAA,EAAI,EACrE;;;;;IASAH,EAAA,CAAAC,cAAA,cAKC;IACCD,EAAA,CAAAE,SAAA,cAAkD;IAClDF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,GAAmB;IAC3BV,EAD2B,CAAAG,YAAA,EAAO,EAC5B;;;;IANJH,EAAA,CAAAO,UAAA,eAAAP,EAAA,CAAA+C,eAAA,IAAAC,GAAA,EAAAC,WAAA,CAAAC,IAAA,EAA2C;IAItClD,EAAA,CAAAM,SAAA,EAAsB;IAACN,EAAvB,CAAAO,UAAA,QAAA0C,WAAA,CAAAE,KAAA,EAAAnD,EAAA,CAAAuB,aAAA,CAAsB,QAAA0B,WAAA,CAAAG,IAAA,CAAsB;IAC3CpD,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAyB,iBAAA,CAAAwB,WAAA,CAAAG,IAAA,CAAmB;;;;;IAR7BpD,EAAA,CAAAI,UAAA,IAAAiD,0CAAA,0BAA+D;;;AD5EvE,OAAM,MAAOC,gBAAgB;EAuF3BC,YACUC,cAA8B,EAC9BC,MAAc;IADd,KAAAD,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IAxFhB,KAAAxB,cAAc,GAAU,EAAE;IAC1B,KAAAyB,gBAAgB,GAAc,EAAE;IAChC,KAAAZ,cAAc,GAAU,EAAE;IAC1B,KAAAa,UAAU,GAAU,EAAE;IACtB,KAAAC,oBAAoB,GAAY,KAAK;IACrC,KAAAC,oBAAoB,GAAY,KAAK;IAErC;IACA,KAAA7B,0BAA0B,GAAe;MACvC8B,IAAI,EAAE,IAAI;MACVC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,KAAK;MACfC,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;MACnBC,GAAG,EAAE,IAAI;MACTC,QAAQ,EAAE,IAAI;MACdC,eAAe,EAAE,IAAI;MACrBC,kBAAkB,EAAE,IAAI;MACxBC,MAAM,EAAE,CAAC;MACTC,UAAU,EAAE;QACV,CAAC,EAAE;UACDC,KAAK,EAAE;SACR;QACD,GAAG,EAAE;UACHA,KAAK,EAAE;SACR;QACD,GAAG,EAAE;UACHA,KAAK,EAAE;;;KAGZ;IAED,KAAA9B,0BAA0B,GAAe;MACvCiB,IAAI,EAAE,IAAI;MACVC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,KAAK;MACfC,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;MACnBC,GAAG,EAAE,IAAI;MACTC,QAAQ,EAAE,IAAI;MACdC,eAAe,EAAE,IAAI;MACrBC,kBAAkB,EAAE,IAAI;MACxBC,MAAM,EAAE,CAAC;MACTC,UAAU,EAAE;QACV,CAAC,EAAE;UACDC,KAAK,EAAE;SACR;QACD,GAAG,EAAE;UACHA,KAAK,EAAE;SACR;QACD,GAAG,EAAE;UACHA,KAAK,EAAE;;;KAGZ;IAED,KAAAC,yBAAyB,GAAe;MACtCd,IAAI,EAAE,IAAI;MACVC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,KAAK;MACfC,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;MACnBC,GAAG,EAAE,IAAI;MACTC,QAAQ,EAAE,IAAI;MACdC,eAAe,EAAE,IAAI;MACrBC,kBAAkB,EAAE,IAAI;MACxBC,MAAM,EAAE,CAAC;MACTC,UAAU,EAAE;QACV,CAAC,EAAE;UACDC,KAAK,EAAE;SACR;QACD,GAAG,EAAE;UACHA,KAAK,EAAE;SACR;QACD,GAAG,EAAE;UACHA,KAAK,EAAE;;;KAGZ;EAKE;EAEHE,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAH,kBAAkBA,CAAA;IAChB,IAAI,CAACjB,oBAAoB,GAAG,IAAI;IAEhC;IACAqB,UAAU,CAAC,MAAK;MACd,IAAI,CAACjD,cAAc,GAAG,CACpB;QACEZ,EAAE,EAAE,GAAG;QACPK,QAAQ,EAAE,cAAc;QACxBF,QAAQ,EAAE,aAAa;QACvBF,MAAM,EAAE,oEAAoE;QAC5EK,UAAU,EAAE,sCAAsC;QAClDE,WAAW,EAAE;OACd,EACD;QACER,EAAE,EAAE,GAAG;QACPK,QAAQ,EAAE,eAAe;QACzBF,QAAQ,EAAE,aAAa;QACvBF,MAAM,EAAE,iEAAiE;QACzEK,UAAU,EAAE,kCAAkC;QAC9CE,WAAW,EAAE;OACd,EACD;QACER,EAAE,EAAE,GAAG;QACPK,QAAQ,EAAE,iBAAiB;QAC3BF,QAAQ,EAAE,cAAc;QACxBF,MAAM,EAAE,oEAAoE;QAC5EK,UAAU,EAAE,oCAAoC;QAChDE,WAAW,EAAE;OACd,CACF;MACD,IAAI,CAACgC,oBAAoB,GAAG,KAAK;IACnC,CAAC,EAAE,IAAI,CAAC;EACV;EAEAkB,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAACrB,gBAAgB,GAAG,EAAE;EAC5B;EAEAsB,kBAAkBA,CAAA;IAChB,IAAI,CAACpB,oBAAoB,GAAG,IAAI;IAEhC;IACAsB,UAAU,CAAC,MAAK;MACd,IAAI,CAACpC,cAAc,GAAG,CACpB;QACEzB,EAAE,EAAE,GAAG;QACPK,QAAQ,EAAE,mBAAmB;QAC7BF,QAAQ,EAAE,aAAa;QACvBF,MAAM,EAAE,oEAAoE;QAC5EkB,cAAc,EAAE,MAAM;QACtBC,UAAU,EAAE,GAAG;QACfC,UAAU,EAAE,GAAG;QACfb,WAAW,EAAE;OACd,EACD;QACER,EAAE,EAAE,GAAG;QACPK,QAAQ,EAAE,iBAAiB;QAC3BF,QAAQ,EAAE,WAAW;QACrBF,MAAM,EAAE,oEAAoE;QAC5EkB,cAAc,EAAE,KAAK;QACrBC,UAAU,EAAE,GAAG;QACfC,UAAU,EAAE,GAAG;QACfb,WAAW,EAAE;OACd,EACD;QACER,EAAE,EAAE,GAAG;QACPK,QAAQ,EAAE,eAAe;QACzBF,QAAQ,EAAE,aAAa;QACvBF,MAAM,EAAE,oEAAoE;QAC5EkB,cAAc,EAAE,KAAK;QACrBC,UAAU,EAAE,GAAG;QACfC,UAAU,EAAE,GAAG;QACfb,WAAW,EAAE;OACd,CACF;MACD,IAAI,CAAC+B,oBAAoB,GAAG,KAAK;IACnC,CAAC,EAAE,IAAI,CAAC;EACV;EAEAqB,cAAcA,CAAA;IACZ,IAAI,CAACtB,UAAU,GAAG,CAChB;MACEP,IAAI,EAAE,OAAO;MACbF,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;KACR,EACD;MACEC,IAAI,EAAE,KAAK;MACXF,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE;KACR,EACD;MACEC,IAAI,EAAE,MAAM;MACZF,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE;KACR,EACD;MACEC,IAAI,EAAE,QAAQ;MACdF,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;KACR,CACF;EACH;EAEAZ,mBAAmBA,CAAC4C,KAAa;IAC/B,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEAjE,UAAUA,CAACkE,MAAc;IACvB,MAAMC,IAAI,GAAG,IAAI,CAACtD,cAAc,CAACuD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpE,EAAE,KAAKiE,MAAM,CAAC;IAC3D,IAAIC,IAAI,EAAE;MACRA,IAAI,CAAC1D,WAAW,GAAG,CAAC0D,IAAI,CAAC1D,WAAW;;EAExC;EAEAS,gBAAgBA,CAACoD,YAAoB;IACnC,MAAMC,UAAU,GAAG,IAAI,CAAC7C,cAAc,CAAC0C,IAAI,CAACI,CAAC,IAAIA,CAAC,CAACvE,EAAE,KAAKqE,YAAY,CAAC;IACvE,IAAIC,UAAU,EAAE;MACdA,UAAU,CAAC9D,WAAW,GAAG,CAAC8D,UAAU,CAAC9D,WAAW;;EAEpD;EAEAgE,QAAQA,CAACC,SAAiB;IACxBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,SAAS,CAAC;IAC5C;EACF;EAEAG,cAAcA,CAACC,YAAoB;IACjCH,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEE,YAAY,CAAC;IAC7C,IAAI,CAACzC,MAAM,CAAC0C,QAAQ,CAAC,CAAC,WAAW,EAAED,YAAY,CAAC,CAAC;EACnD;;;uBA1OW5C,gBAAgB,EAAAtD,EAAA,CAAAoG,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAtG,EAAA,CAAAoG,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAhBlD,gBAAgB;MAAAmD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA3G,EAAA,CAAA4G,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC5B7BlH,EAAA,CAAAC,cAAA,eAAuB;UAWrBD,EANA,CAAAE,SAAA,4BAA+C,0BAGJ,uBAGN;UAInCF,EADF,CAAAC,cAAA,aAAyB,SACnB;UAAAD,EAAA,CAAAU,MAAA,wBAAiB;UAAAV,EAAA,CAAAG,YAAA,EAAK;UAgC1BH,EA7BA,CAAAI,UAAA,IAAAgH,+BAAA,iBAA4D,IAAAC,+BAAA,iBAWyC,IAAAC,+BAAA,iBAkBX;UAK5FtH,EAAA,CAAAG,YAAA,EAAM;UAIJH,EADF,CAAAC,cAAA,cAAyB,UACnB;UAAAD,EAAA,CAAAU,MAAA,+BAAuB;UAAAV,EAAA,CAAAG,YAAA,EAAK;UAoChCH,EAjCA,CAAAI,UAAA,KAAAmH,gCAAA,iBAA4D,KAAAC,gCAAA,iBAWyC,KAAAC,gCAAA,iBAsBX;UAK5FzH,EAAA,CAAAG,YAAA,EAAM;UAIJH,EADF,CAAAC,cAAA,cAAwB,UAClB;UAAAD,EAAA,CAAAU,MAAA,wBAAgB;UAAAV,EAAA,CAAAG,YAAA,EAAK;UAEvBH,EADF,CAAAC,cAAA,cAAyC,yBACyC;UAC9ED,EAAA,CAAAI,UAAA,KAAAsH,4BAAA,iBAA+D;UAgBvE1H,EALM,CAAAG,YAAA,EAAiB,EACb,EACF,EAGA;;;UAtGEH,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAO,UAAA,SAAA4G,GAAA,CAAAtD,oBAAA,CAA0B;UAW1B7D,EAAA,CAAAM,SAAA,EAAwD;UAAxDN,EAAA,CAAAO,UAAA,UAAA4G,GAAA,CAAAtD,oBAAA,IAAAsD,GAAA,CAAAlF,cAAA,CAAA0F,MAAA,KAAwD;UAkBxD3H,EAAA,CAAAM,SAAA,EAA0D;UAA1DN,EAAA,CAAAO,UAAA,UAAA4G,GAAA,CAAAtD,oBAAA,IAAAsD,GAAA,CAAAlF,cAAA,CAAA0F,MAAA,OAA0D;UAY1D3H,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAO,UAAA,SAAA4G,GAAA,CAAAvD,oBAAA,CAA0B;UAW1B5D,EAAA,CAAAM,SAAA,EAAwD;UAAxDN,EAAA,CAAAO,UAAA,UAAA4G,GAAA,CAAAvD,oBAAA,IAAAuD,GAAA,CAAArE,cAAA,CAAA6E,MAAA,KAAwD;UAsBxD3H,EAAA,CAAAM,SAAA,EAA0D;UAA1DN,EAAA,CAAAO,UAAA,UAAA4G,GAAA,CAAAvD,oBAAA,IAAAuD,GAAA,CAAArE,cAAA,CAAA6E,MAAA,OAA0D;UAW9C3H,EAAA,CAAAM,SAAA,GAAqC;UAArCN,EAAA,CAAAO,UAAA,YAAA4G,GAAA,CAAAvC,yBAAA,CAAqC;UACH5E,EAAA,CAAAM,SAAA,EAAa;UAAbN,EAAA,CAAAO,UAAA,YAAA4G,GAAA,CAAAxD,UAAA,CAAa;;;qBDvFjElE,YAAY,EAAAmI,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZpI,YAAY,EAAA6G,EAAA,CAAAwB,UAAA,EAAAxB,EAAA,CAAAyB,gBAAA,EACZrI,WAAW,EAAAsI,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,kBAAA,EACXvI,cAAc,EAAAwI,EAAA,CAAAC,iBAAA,EAAAD,EAAA,CAAAE,sBAAA,EACdzI,yBAAyB,EACzBC,uBAAuB,EACvBC,oBAAoB;MAAAwI,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}