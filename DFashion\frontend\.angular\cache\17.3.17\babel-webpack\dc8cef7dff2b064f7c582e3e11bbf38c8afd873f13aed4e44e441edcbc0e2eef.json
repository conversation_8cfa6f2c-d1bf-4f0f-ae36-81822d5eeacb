{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { TrendingProductsComponent } from '../trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../new-arrivals/new-arrivals.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/product.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = () => [1, 2, 3];\nconst _c1 = a0 => [\"/category\", a0];\nfunction SidebarComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelement(1, \"img\", 11);\n    i0.ɵɵelementStart(2, \"div\", 12)(3, \"h5\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_7_Template_button_click_7_listener() {\n      const user_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.followUser(user_r2.id));\n    });\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", user_r2.avatar, i0.ɵɵsanitizeUrl)(\"alt\", user_r2.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r2.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r2.followedBy);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", user_r2.isFollowing ? \"Following\" : \"Follow\", \" \");\n  }\n}\nfunction SidebarComponent_div_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17);\n    i0.ɵɵelement(2, \"div\", 18)(3, \"div\", 19)(4, \"div\", 20);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SidebarComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtemplate(1, SidebarComponent_div_11_div_1_Template, 5, 0, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction SidebarComponent_div_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵelement(1, \"img\", 11);\n    i0.ɵɵelementStart(2, \"div\", 23)(3, \"h5\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 24)(8, \"span\", 25);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 26);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_12_div_1_Template_button_click_12_listener() {\n      const influencer_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.followInfluencer(influencer_r5.id));\n    });\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const influencer_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", influencer_r5.avatar, i0.ɵɵsanitizeUrl)(\"alt\", influencer_r5.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(influencer_r5.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.formatFollowerCount(influencer_r5.followersCount), \" followers\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", influencer_r5.postsCount, \" posts\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", influencer_r5.engagement, \"% engagement\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", influencer_r5.isFollowing ? \"Following\" : \"Follow\", \" \");\n  }\n}\nfunction SidebarComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, SidebarComponent_div_12_div_1_Template, 14, 7, \"div\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.topInfluencers);\n  }\n}\nfunction SidebarComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"ion-icon\", 28);\n    i0.ɵɵelementStart(2, \"h3\", 29);\n    i0.ɵɵtext(3, \"No Influencers Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 30);\n    i0.ɵɵtext(5, \"Check back later for top fashion influencers\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SidebarComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelement(1, \"img\", 11);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c1, category_r6.slug));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", category_r6.image, i0.ɵɵsanitizeUrl)(\"alt\", category_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r6.name);\n  }\n}\nexport class SidebarComponent {\n  constructor(productService, router) {\n    this.productService = productService;\n    this.router = router;\n    this.suggestedUsers = [];\n    this.trendingProducts = [];\n    this.topInfluencers = [];\n    this.categories = [];\n    this.isLoadingInfluencers = false;\n  }\n  ngOnInit() {\n    this.loadSuggestedUsers();\n    this.loadTrendingProducts();\n    this.loadTopInfluencers();\n    this.loadCategories();\n  }\n  loadSuggestedUsers() {\n    // Load from API - empty for now\n    this.suggestedUsers = [];\n  }\n  loadTrendingProducts() {\n    // Load from API - empty for now\n    this.trendingProducts = [];\n  }\n  loadTopInfluencers() {\n    // Load from API - empty for now\n    this.topInfluencers = [];\n  }\n  loadCategories() {\n    this.categories = [{\n      name: 'Women',\n      slug: 'women',\n      image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=100'\n    }, {\n      name: 'Men',\n      slug: 'men',\n      image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=100'\n    }, {\n      name: 'Kids',\n      slug: 'children',\n      image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=100'\n    }, {\n      name: 'Ethnic',\n      slug: 'ethnic',\n      image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=100'\n    }];\n  }\n  formatFollowerCount(count) {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'k';\n    }\n    return count.toString();\n  }\n  followUser(userId) {\n    const user = this.suggestedUsers.find(u => u.id === userId);\n    if (user) {\n      user.isFollowing = !user.isFollowing;\n    }\n  }\n  followInfluencer(influencerId) {\n    const influencer = this.topInfluencers.find(i => i.id === influencerId);\n    if (influencer) {\n      influencer.isFollowing = !influencer.isFollowing;\n    }\n  }\n  quickBuy(productId) {\n    console.log('Quick buy product:', productId);\n    // TODO: Implement quick buy functionality\n  }\n  browseCategory(categorySlug) {\n    console.log('Browse category:', categorySlug);\n    this.router.navigate(['/category', categorySlug]);\n  }\n  static {\n    this.ɵfac = function SidebarComponent_Factory(t) {\n      return new (t || SidebarComponent)(i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SidebarComponent,\n      selectors: [[\"app-sidebar\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 19,\n      vars: 5,\n      consts: [[1, \"sidebar\"], [1, \"suggestions\"], [\"class\", \"suggestion-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"influencers\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"categories\"], [1, \"category-grid\"], [\"class\", \"category-item\", \"routerLinkActive\", \"active\", \"tabindex\", \"0\", 3, \"routerLink\", 4, \"ngFor\", \"ngForOf\"], [1, \"suggestion-item\"], [3, \"src\", \"alt\"], [1, \"suggestion-info\"], [1, \"follow-btn\", 3, \"click\"], [1, \"loading-container\"], [\"class\", \"loading-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-card\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [\"class\", \"influencer-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"influencer-item\"], [1, \"influencer-info\"], [1, \"influencer-stats\"], [1, \"posts-count\"], [1, \"engagement\"], [1, \"empty-container\"], [\"name\", \"people-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"], [\"routerLinkActive\", \"active\", \"tabindex\", \"0\", 1, \"category-item\", 3, \"routerLink\"]],\n      template: function SidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"aside\", 0);\n          i0.ɵɵelement(1, \"app-trending-products\")(2, \"app-featured-brands\")(3, \"app-new-arrivals\");\n          i0.ɵɵelementStart(4, \"div\", 1)(5, \"h3\");\n          i0.ɵɵtext(6, \"Suggested for you\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, SidebarComponent_div_7_Template, 9, 5, \"div\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 3)(9, \"h3\");\n          i0.ɵɵtext(10, \"Top Fashion Influencers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(11, SidebarComponent_div_11_Template, 2, 2, \"div\", 4)(12, SidebarComponent_div_12_Template, 2, 1, \"div\", 5)(13, SidebarComponent_div_13_Template, 6, 0, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 7)(15, \"h3\");\n          i0.ɵɵtext(16, \"Shop by Category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 8);\n          i0.ɵɵtemplate(18, SidebarComponent_div_18_Template, 4, 6, \"div\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.suggestedUsers);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingInfluencers);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingInfluencers && ctx.topInfluencers.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingInfluencers && ctx.topInfluencers.length === 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, RouterModule, i2.RouterLink, i2.RouterLinkActive, TrendingProductsComponent, FeaturedBrandsComponent, NewArrivalsComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.sidebar[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 80px;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n  max-height: calc(100vh - 100px);\\n  overflow-y: auto;\\n  padding-right: 8px;\\n}\\n\\napp-view-add-stories[_ngcontent-%COMP%], app-trending-products[_ngcontent-%COMP%], app-featured-brands[_ngcontent-%COMP%], app-new-arrivals[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: block;\\n  margin-bottom: 24px;\\n}\\n\\n.suggestions[_ngcontent-%COMP%], .trending[_ngcontent-%COMP%], .categories[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  padding: 16px;\\n}\\n\\n.influencers[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .trending[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin-bottom: 16px;\\n  color: #8e8e8e;\\n}\\n\\n.influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDC51\\\";\\n  font-size: 28px;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin-bottom: 12px;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 16px;\\n  margin-bottom: 16px;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);\\n}\\n\\n.influencer-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n\\n.suggestion-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.suggestion-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin-bottom: 2px;\\n}\\n\\n.suggestion-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #8e8e8e;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 16px;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid rgba(255, 255, 255, 0.3);\\n  transition: transform 0.3s ease;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.influencer-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.influencer-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 4px;\\n}\\n\\n.influencer-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.7);\\n  margin-bottom: 8px;\\n}\\n\\n.influencer-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n  margin-bottom: 12px;\\n}\\n\\n.influencer-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: rgba(255, 255, 255, 0.6);\\n  background: rgba(255, 255, 255, 0.1);\\n  padding: 4px 8px;\\n  border-radius: 8px;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  -webkit-backdrop-filter: blur(5px);\\n          backdrop-filter: blur(5px);\\n}\\n\\n.follow-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 8px 16px;\\n  border-radius: 8px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  align-self: flex-start;\\n}\\n\\n.follow-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n  transform: translateY(-2px);\\n}\\n\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  padding: 16px;\\n  margin-bottom: 16px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  background: rgba(255, 255, 255, 0.2);\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  border-radius: 6px;\\n  margin-bottom: 8px;\\n}\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0%, 100% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n.influencers[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.influencers[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: rgba(255, 255, 255, 0.4);\\n  margin-bottom: 16px;\\n}\\n.influencers[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 8px;\\n}\\n.influencers[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.trending-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n  padding-bottom: 16px;\\n  border-bottom: 1px solid #f1f5f9;\\n}\\n\\n.trending-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n  margin-bottom: 0;\\n  padding-bottom: 0;\\n}\\n\\n.trending-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 8px;\\n  object-fit: cover;\\n}\\n\\n.trending-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.trending-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin-bottom: 4px;\\n}\\n\\n.trending-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: var(--primary-color);\\n  margin-bottom: 8px;\\n}\\n\\n.original-price[_ngcontent-%COMP%] {\\n  text-decoration: line-through;\\n  color: #8e8e8e;\\n  font-weight: 400;\\n  margin-left: 4px;\\n}\\n\\n.trending-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n\\n.trending-badge[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 600;\\n  background: #fef3c7;\\n  color: #92400e;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n\\n.views[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #8e8e8e;\\n}\\n\\n.quick-buy-btn[_ngcontent-%COMP%] {\\n  background: #f1f5f9;\\n  color: #64748b;\\n  border: 1px solid #e2e8f0;\\n  padding: 6px 12px;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n  align-self: flex-start;\\n}\\n\\n.quick-buy-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-color);\\n  color: #fff;\\n  border-color: var(--primary-color);\\n}\\n\\n.categories[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1f2937;\\n  margin-bottom: 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDECD\\uFE0F\\\";\\n  font-size: 18px;\\n}\\n\\n.category-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 12px;\\n}\\n\\n.category-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 12px;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  text-decoration: none;\\n  color: inherit;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.category-item[_ngcontent-%COMP%]:hover {\\n  background: #f8fafc;\\n  border-color: var(--primary-color);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n.category-item.active[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  border-color: var(--primary-color);\\n  color: white;\\n}\\n\\n.category-item.active[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n.category-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 8px;\\n  object-fit: cover;\\n  margin-bottom: 8px;\\n  transition: transform 0.3s ease;\\n}\\n\\n.category-item[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.category-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 500;\\n  text-align: center;\\n  transition: color 0.3s ease;\\n}\\n\\n.category-item[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.5s ease;\\n}\\n\\n.category-item[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n\\n@media (max-width: 1024px) {\\n  .sidebar[_ngcontent-%COMP%] {\\n    order: -1;\\n    position: static;\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n    gap: 20px;\\n    max-height: none;\\n    overflow-y: visible;\\n  }\\n  app-view-add-stories[_ngcontent-%COMP%], app-trending-products[_ngcontent-%COMP%], app-featured-brands[_ngcontent-%COMP%], app-new-arrivals[_ngcontent-%COMP%] {\\n    margin-bottom: 16px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .sidebar[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  app-view-add-stories[_ngcontent-%COMP%], app-trending-products[_ngcontent-%COMP%], app-featured-brands[_ngcontent-%COMP%], app-new-arrivals[_ngcontent-%COMP%] {\\n    margin-bottom: 12px;\\n  }\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "TrendingProductsComponent", "FeaturedBrandsComponent", "NewArrivalsComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "SidebarComponent_div_7_Template_button_click_7_listener", "user_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "followUser", "id", "ɵɵadvance", "ɵɵproperty", "avatar", "ɵɵsanitizeUrl", "fullName", "ɵɵtextInterpolate", "username", "<PERSON><PERSON><PERSON>", "ɵɵtextInterpolate1", "isFollowing", "ɵɵtemplate", "SidebarComponent_div_11_div_1_Template", "ɵɵpureFunction0", "_c0", "SidebarComponent_div_12_div_1_Template_button_click_12_listener", "influencer_r5", "_r4", "followInfluencer", "formatFollowerCount", "followersCount", "postsCount", "engagement", "SidebarComponent_div_12_div_1_Template", "topInfluencers", "ɵɵpureFunction1", "_c1", "category_r6", "slug", "image", "name", "SidebarComponent", "constructor", "productService", "router", "suggestedUsers", "trendingProducts", "categories", "isLoadingInfluencers", "ngOnInit", "loadSuggestedUsers", "loadTrendingProducts", "loadTopInfluencers", "loadCategories", "count", "toFixed", "toString", "userId", "user", "find", "u", "influencerId", "influencer", "i", "quickBuy", "productId", "console", "log", "browseCategory", "categorySlug", "navigate", "ɵɵdirectiveInject", "i1", "ProductService", "i2", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SidebarComponent_Template", "rf", "ctx", "SidebarComponent_div_7_Template", "SidebarComponent_div_11_Template", "SidebarComponent_div_12_Template", "SidebarComponent_div_13_Template", "SidebarComponent_div_18_Template", "length", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "RouterLink", "RouterLinkActive", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\sidebar\\sidebar.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\sidebar\\sidebar.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, RouterModule } from '@angular/router';\n\nimport { ProductService } from '../../../../core/services/product.service';\nimport { Product } from '../../../../core/models/product.model';\nimport { TrendingProductsComponent } from '../trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../new-arrivals/new-arrivals.component';\n\n@Component({\n  selector: 'app-sidebar',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    TrendingProductsComponent,\n    FeaturedBrandsComponent,\n    NewArrivalsComponent\n  ],\n  templateUrl: './sidebar.component.html',\n  styleUrls: ['./sidebar.component.scss']\n})\nexport class SidebarComponent implements OnInit {\n  suggestedUsers: any[] = [];\n  trendingProducts: Product[] = [];\n  topInfluencers: any[] = [];\n  categories: any[] = [];\n  isLoadingInfluencers: boolean = false;\n\n  constructor(\n    private productService: ProductService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadSuggestedUsers();\n    this.loadTrendingProducts();\n    this.loadTopInfluencers();\n    this.loadCategories();\n  }\n\n  loadSuggestedUsers() {\n    // Load from API - empty for now\n    this.suggestedUsers = [];\n  }\n\n  loadTrendingProducts() {\n    // Load from API - empty for now\n    this.trendingProducts = [];\n  }\n\n  loadTopInfluencers() {\n    // Load from API - empty for now\n    this.topInfluencers = [];\n  }\n\n  loadCategories() {\n    this.categories = [\n      {\n        name: 'Women',\n        slug: 'women',\n        image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=100'\n      },\n      {\n        name: 'Men',\n        slug: 'men',\n        image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=100'\n      },\n      {\n        name: 'Kids',\n        slug: 'children',\n        image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=100'\n      },\n      {\n        name: 'Ethnic',\n        slug: 'ethnic',\n        image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=100'\n      }\n    ];\n  }\n\n  formatFollowerCount(count: number): string {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'k';\n    }\n    return count.toString();\n  }\n\n  followUser(userId: string) {\n    const user = this.suggestedUsers.find(u => u.id === userId);\n    if (user) {\n      user.isFollowing = !user.isFollowing;\n    }\n  }\n\n  followInfluencer(influencerId: string) {\n    const influencer = this.topInfluencers.find(i => i.id === influencerId);\n    if (influencer) {\n      influencer.isFollowing = !influencer.isFollowing;\n    }\n  }\n\n  quickBuy(productId: string) {\n    console.log('Quick buy product:', productId);\n    // TODO: Implement quick buy functionality\n  }\n\n  browseCategory(categorySlug: string) {\n    console.log('Browse category:', categorySlug);\n    this.router.navigate(['/category', categorySlug]);\n  }\n}\n", "<aside class=\"sidebar\">\n  <!-- Instagram-style Stories -->\n \n\n  <!-- Trending Products Section -->\n  <app-trending-products></app-trending-products>\n\n  <!-- Featured Brands Section -->\n  <app-featured-brands></app-featured-brands>\n\n  <!-- New Arrivals Section -->\n  <app-new-arrivals></app-new-arrivals>\n\n  <!-- Suggested for you -->\n  <div class=\"suggestions\">\n    <h3>Suggested for you</h3>\n    <div *ngFor=\"let user of suggestedUsers\" class=\"suggestion-item\">\n      <img [src]=\"user.avatar\" [alt]=\"user.fullName\">\n      <div class=\"suggestion-info\">\n        <h5>{{ user.username }}</h5>\n        <p>{{ user.followedBy }}</p>\n      </div>\n      <button class=\"follow-btn\" (click)=\"followUser(user.id)\">\n        {{ user.isFollowing ? 'Following' : 'Follow' }}\n      </button>\n    </div>\n  </div>\n\n  <!-- Top Fashion Influencers -->\n  <div class=\"influencers\">\n    <h3>Top Fashion Influencers</h3>\n\n    <!-- Loading State -->\n    <div *ngIf=\"isLoadingInfluencers\" class=\"loading-container\">\n      <div class=\"loading-card\" *ngFor=\"let item of [1,2,3]\">\n        <div class=\"loading-content\">\n          <div class=\"loading-line short\"></div>\n          <div class=\"loading-line medium\"></div>\n          <div class=\"loading-line long\"></div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Influencers List -->\n    <div *ngIf=\"!isLoadingInfluencers && topInfluencers.length > 0\">\n      <div *ngFor=\"let influencer of topInfluencers\" class=\"influencer-item\">\n        <img [src]=\"influencer.avatar\" [alt]=\"influencer.fullName\">\n        <div class=\"influencer-info\">\n          <h5>{{ influencer.username }}</h5>\n          <p>{{ formatFollowerCount(influencer.followersCount) }} followers</p>\n          <div class=\"influencer-stats\">\n            <span class=\"posts-count\">{{ influencer.postsCount }} posts</span>\n            <span class=\"engagement\">{{ influencer.engagement }}% engagement</span>\n          </div>\n          <button class=\"follow-btn\" (click)=\"followInfluencer(influencer.id)\">\n            {{ influencer.isFollowing ? 'Following' : 'Follow' }}\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Empty State -->\n    <div *ngIf=\"!isLoadingInfluencers && topInfluencers.length === 0\" class=\"empty-container\">\n      <ion-icon name=\"people-outline\" class=\"empty-icon\"></ion-icon>\n      <h3 class=\"empty-title\">No Influencers Found</h3>\n      <p class=\"empty-message\">Check back later for top fashion influencers</p>\n    </div>\n  </div>\n\n  <!-- Shop by Category -->\n  <div class=\"categories\">\n    <h3>Shop by Category</h3>\n    <div class=\"category-grid\">\n      <div\n        *ngFor=\"let category of categories\"\n        class=\"category-item\"\n        [routerLink]=\"['/category', category.slug]\"\n        routerLinkActive=\"active\"\n        tabindex=\"0\"\n      >\n        <img [src]=\"category.image\" [alt]=\"category.name\">\n        <span>{{ category.name }}</span>\n      </div>\n    </div>\n  </div>\n\n\n</aside>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiBC,YAAY,QAAQ,iBAAiB;AAItD,SAASC,yBAAyB,QAAQ,kDAAkD;AAC5F,SAASC,uBAAuB,QAAQ,8CAA8C;AACtF,SAASC,oBAAoB,QAAQ,wCAAwC;;;;;;;;;;ICQzEC,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAE,SAAA,cAA+C;IAE7CF,EADF,CAAAC,cAAA,cAA6B,SACvB;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5BJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAqB;IAC1BH,EAD0B,CAAAI,YAAA,EAAI,EACxB;IACNJ,EAAA,CAAAC,cAAA,iBAAyD;IAA9BD,EAAA,CAAAK,UAAA,mBAAAC,wDAAA;MAAA,MAAAC,OAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,UAAA,CAAAP,OAAA,CAAAQ,EAAA,CAAmB;IAAA,EAAC;IACtDf,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;;;;IARCJ,EAAA,CAAAgB,SAAA,EAAmB;IAAChB,EAApB,CAAAiB,UAAA,QAAAV,OAAA,CAAAW,MAAA,EAAAlB,EAAA,CAAAmB,aAAA,CAAmB,QAAAZ,OAAA,CAAAa,QAAA,CAAsB;IAExCpB,EAAA,CAAAgB,SAAA,GAAmB;IAAnBhB,EAAA,CAAAqB,iBAAA,CAAAd,OAAA,CAAAe,QAAA,CAAmB;IACpBtB,EAAA,CAAAgB,SAAA,GAAqB;IAArBhB,EAAA,CAAAqB,iBAAA,CAAAd,OAAA,CAAAgB,UAAA,CAAqB;IAGxBvB,EAAA,CAAAgB,SAAA,GACF;IADEhB,EAAA,CAAAwB,kBAAA,MAAAjB,OAAA,CAAAkB,WAAA,+BACF;;;;;IAWEzB,EADF,CAAAC,cAAA,cAAuD,cACxB;IAG3BD,EAFA,CAAAE,SAAA,cAAsC,cACC,cACF;IAEzCF,EADE,CAAAI,YAAA,EAAM,EACF;;;;;IAPRJ,EAAA,CAAAC,cAAA,cAA4D;IAC1DD,EAAA,CAAA0B,UAAA,IAAAC,sCAAA,kBAAuD;IAOzD3B,EAAA,CAAAI,YAAA,EAAM;;;IAPuCJ,EAAA,CAAAgB,SAAA,EAAU;IAAVhB,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAA4B,eAAA,IAAAC,GAAA,EAAU;;;;;;IAWrD7B,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAE,SAAA,cAA2D;IAEzDF,EADF,CAAAC,cAAA,cAA6B,SACvB;IAAAD,EAAA,CAAAG,MAAA,GAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAA8D;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEnEJ,EADF,CAAAC,cAAA,cAA8B,eACF;IAAAD,EAAA,CAAAG,MAAA,GAAiC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAClEJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,IAAuC;IAClEH,EADkE,CAAAI,YAAA,EAAO,EACnE;IACNJ,EAAA,CAAAC,cAAA,kBAAqE;IAA1CD,EAAA,CAAAK,UAAA,mBAAAyB,gEAAA;MAAA,MAAAC,aAAA,GAAA/B,EAAA,CAAAQ,aAAA,CAAAwB,GAAA,EAAAtB,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAsB,gBAAA,CAAAF,aAAA,CAAAhB,EAAA,CAA+B;IAAA,EAAC;IAClEf,EAAA,CAAAG,MAAA,IACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;;;;;IAZCJ,EAAA,CAAAgB,SAAA,EAAyB;IAAChB,EAA1B,CAAAiB,UAAA,QAAAc,aAAA,CAAAb,MAAA,EAAAlB,EAAA,CAAAmB,aAAA,CAAyB,QAAAY,aAAA,CAAAX,QAAA,CAA4B;IAEpDpB,EAAA,CAAAgB,SAAA,GAAyB;IAAzBhB,EAAA,CAAAqB,iBAAA,CAAAU,aAAA,CAAAT,QAAA,CAAyB;IAC1BtB,EAAA,CAAAgB,SAAA,GAA8D;IAA9DhB,EAAA,CAAAwB,kBAAA,KAAAb,MAAA,CAAAuB,mBAAA,CAAAH,aAAA,CAAAI,cAAA,gBAA8D;IAErCnC,EAAA,CAAAgB,SAAA,GAAiC;IAAjChB,EAAA,CAAAwB,kBAAA,KAAAO,aAAA,CAAAK,UAAA,WAAiC;IAClCpC,EAAA,CAAAgB,SAAA,GAAuC;IAAvChB,EAAA,CAAAwB,kBAAA,KAAAO,aAAA,CAAAM,UAAA,iBAAuC;IAGhErC,EAAA,CAAAgB,SAAA,GACF;IADEhB,EAAA,CAAAwB,kBAAA,MAAAO,aAAA,CAAAN,WAAA,+BACF;;;;;IAZNzB,EAAA,CAAAC,cAAA,UAAgE;IAC9DD,EAAA,CAAA0B,UAAA,IAAAY,sCAAA,mBAAuE;IAczEtC,EAAA,CAAAI,YAAA,EAAM;;;;IAdwBJ,EAAA,CAAAgB,SAAA,EAAiB;IAAjBhB,EAAA,CAAAiB,UAAA,YAAAN,MAAA,CAAA4B,cAAA,CAAiB;;;;;IAiB/CvC,EAAA,CAAAC,cAAA,cAA0F;IACxFD,EAAA,CAAAE,SAAA,mBAA8D;IAC9DF,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAG,MAAA,2BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACjDJ,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAG,MAAA,mDAA4C;IACvEH,EADuE,CAAAI,YAAA,EAAI,EACrE;;;;;IAOJJ,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAE,SAAA,cAAkD;IAClDF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAC3BH,EAD2B,CAAAI,YAAA,EAAO,EAC5B;;;;IANJJ,EAAA,CAAAiB,UAAA,eAAAjB,EAAA,CAAAwC,eAAA,IAAAC,GAAA,EAAAC,WAAA,CAAAC,IAAA,EAA2C;IAItC3C,EAAA,CAAAgB,SAAA,EAAsB;IAAChB,EAAvB,CAAAiB,UAAA,QAAAyB,WAAA,CAAAE,KAAA,EAAA5C,EAAA,CAAAmB,aAAA,CAAsB,QAAAuB,WAAA,CAAAG,IAAA,CAAsB;IAC3C7C,EAAA,CAAAgB,SAAA,GAAmB;IAAnBhB,EAAA,CAAAqB,iBAAA,CAAAqB,WAAA,CAAAG,IAAA,CAAmB;;;AD1DjC,OAAM,MAAOC,gBAAgB;EAO3BC,YACUC,cAA8B,EAC9BC,MAAc;IADd,KAAAD,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IARhB,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,gBAAgB,GAAc,EAAE;IAChC,KAAAZ,cAAc,GAAU,EAAE;IAC1B,KAAAa,UAAU,GAAU,EAAE;IACtB,KAAAC,oBAAoB,GAAY,KAAK;EAKlC;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAH,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACL,cAAc,GAAG,EAAE;EAC1B;EAEAM,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAACL,gBAAgB,GAAG,EAAE;EAC5B;EAEAM,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAAClB,cAAc,GAAG,EAAE;EAC1B;EAEAmB,cAAcA,CAAA;IACZ,IAAI,CAACN,UAAU,GAAG,CAChB;MACEP,IAAI,EAAE,OAAO;MACbF,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;KACR,EACD;MACEC,IAAI,EAAE,KAAK;MACXF,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE;KACR,EACD;MACEC,IAAI,EAAE,MAAM;MACZF,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE;KACR,EACD;MACEC,IAAI,EAAE,QAAQ;MACdF,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;KACR,CACF;EACH;EAEAV,mBAAmBA,CAACyB,KAAa;IAC/B,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEA/C,UAAUA,CAACgD,MAAc;IACvB,MAAMC,IAAI,GAAG,IAAI,CAACb,cAAc,CAACc,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClD,EAAE,KAAK+C,MAAM,CAAC;IAC3D,IAAIC,IAAI,EAAE;MACRA,IAAI,CAACtC,WAAW,GAAG,CAACsC,IAAI,CAACtC,WAAW;;EAExC;EAEAQ,gBAAgBA,CAACiC,YAAoB;IACnC,MAAMC,UAAU,GAAG,IAAI,CAAC5B,cAAc,CAACyB,IAAI,CAACI,CAAC,IAAIA,CAAC,CAACrD,EAAE,KAAKmD,YAAY,CAAC;IACvE,IAAIC,UAAU,EAAE;MACdA,UAAU,CAAC1C,WAAW,GAAG,CAAC0C,UAAU,CAAC1C,WAAW;;EAEpD;EAEA4C,QAAQA,CAACC,SAAiB;IACxBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,SAAS,CAAC;IAC5C;EACF;EAEAG,cAAcA,CAACC,YAAoB;IACjCH,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEE,YAAY,CAAC;IAC7C,IAAI,CAACzB,MAAM,CAAC0B,QAAQ,CAAC,CAAC,WAAW,EAAED,YAAY,CAAC,CAAC;EACnD;;;uBAxFW5B,gBAAgB,EAAA9C,EAAA,CAAA4E,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA9E,EAAA,CAAA4E,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAhBlC,gBAAgB;MAAAmC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAnF,EAAA,CAAAoF,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvB7B1F,EAAA,CAAAC,cAAA,eAAuB;UAWrBD,EANA,CAAAE,SAAA,4BAA+C,0BAGJ,uBAGN;UAInCF,EADF,CAAAC,cAAA,aAAyB,SACnB;UAAAD,EAAA,CAAAG,MAAA,wBAAiB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC1BJ,EAAA,CAAA0B,UAAA,IAAAkE,+BAAA,iBAAiE;UAUnE5F,EAAA,CAAAI,YAAA,EAAM;UAIJJ,EADF,CAAAC,cAAA,aAAyB,SACnB;UAAAD,EAAA,CAAAG,MAAA,+BAAuB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAgChCJ,EA7BA,CAAA0B,UAAA,KAAAmE,gCAAA,iBAA4D,KAAAC,gCAAA,iBAWI,KAAAC,gCAAA,iBAkB0B;UAK5F/F,EAAA,CAAAI,YAAA,EAAM;UAIJJ,EADF,CAAAC,cAAA,cAAwB,UAClB;UAAAD,EAAA,CAAAG,MAAA,wBAAgB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACzBJ,EAAA,CAAAC,cAAA,cAA2B;UACzBD,EAAA,CAAA0B,UAAA,KAAAsE,gCAAA,iBAMC;UAQPhG,EAJI,CAAAI,YAAA,EAAM,EACF,EAGA;;;UAvEkBJ,EAAA,CAAAgB,SAAA,GAAiB;UAAjBhB,EAAA,CAAAiB,UAAA,YAAA0E,GAAA,CAAAzC,cAAA,CAAiB;UAiBjClD,EAAA,CAAAgB,SAAA,GAA0B;UAA1BhB,EAAA,CAAAiB,UAAA,SAAA0E,GAAA,CAAAtC,oBAAA,CAA0B;UAW1BrD,EAAA,CAAAgB,SAAA,EAAwD;UAAxDhB,EAAA,CAAAiB,UAAA,UAAA0E,GAAA,CAAAtC,oBAAA,IAAAsC,GAAA,CAAApD,cAAA,CAAA0D,MAAA,KAAwD;UAkBxDjG,EAAA,CAAAgB,SAAA,EAA0D;UAA1DhB,EAAA,CAAAiB,UAAA,UAAA0E,GAAA,CAAAtC,oBAAA,IAAAsC,GAAA,CAAApD,cAAA,CAAA0D,MAAA,OAA0D;UAYvCjG,EAAA,CAAAgB,SAAA,GAAa;UAAbhB,EAAA,CAAAiB,UAAA,YAAA0E,GAAA,CAAAvC,UAAA,CAAa;;;qBD5DtCzD,YAAY,EAAAuG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZxG,YAAY,EAAAmF,EAAA,CAAAsB,UAAA,EAAAtB,EAAA,CAAAuB,gBAAA,EACZzG,yBAAyB,EACzBC,uBAAuB,EACvBC,oBAAoB;MAAAwG,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}