{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nfunction ShopComponent_div_34_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const sub_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(sub_r4);\n  }\n}\nfunction ShopComponent_div_34_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 41);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"+\", category_r2.subcategories.length - 3, \" more\");\n  }\n}\nfunction ShopComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_34_Template_div_click_0_listener() {\n      const category_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navigateToCategory(category_r2.id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 33);\n    i0.ɵɵelement(2, \"img\", 34);\n    i0.ɵɵelementStart(3, \"div\", 35)(4, \"span\", 36);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 37)(7, \"h3\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 38);\n    i0.ɵɵtemplate(10, ShopComponent_div_34_span_10_Template, 2, 1, \"span\", 39)(11, ShopComponent_div_34_span_11_Template, 2, 1, \"span\", 40);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const category_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", category_r2.image, i0.ɵɵsanitizeUrl)(\"alt\", category_r2.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", category_r2.productCount, \"+ Products\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(category_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", category_r2.subcategories.slice(0, 3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", category_r2.subcategories.length > 3);\n  }\n}\nfunction ShopComponent_div_42_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 45);\n    i0.ɵɵtext(1, \"Popular\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ShopComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_42_Template_div_click_0_listener() {\n      const brand_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navigateToBrand(brand_r6.id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 43);\n    i0.ɵɵelement(2, \"img\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, ShopComponent_div_42_span_5_Template, 2, 0, \"span\", 44);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const brand_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", brand_r6.logo, i0.ɵɵsanitizeUrl)(\"alt\", brand_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(brand_r6.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", brand_r6.isPopular);\n  }\n}\nfunction ShopComponent_div_52_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1, \"Trending\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ShopComponent_div_52_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 65);\n    i0.ɵɵtext(1, \"New\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ShopComponent_div_52_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r8 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getDiscountPercentage(product_r8), \"% OFF \");\n  }\n}\nfunction ShopComponent_div_52_div_17_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 72);\n  }\n}\nfunction ShopComponent_div_52_div_17_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 73);\n  }\n}\nfunction ShopComponent_div_52_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"div\", 68);\n    i0.ɵɵtemplate(2, ShopComponent_div_52_div_17_i_2_Template, 1, 0, \"i\", 69)(3, ShopComponent_div_52_div_17_i_3_Template, 1, 0, \"i\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 71);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r8 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getStars(product_r8.rating.average));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getEmptyStars(product_r8.rating.average));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", product_r8.rating.average, \" (\", product_r8.rating.count, \")\");\n  }\n}\nfunction ShopComponent_div_52_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind2(2, 1, product_r8.originalPrice, \"1.0-0\"), \" \");\n  }\n}\nfunction ShopComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_52_Template_div_click_0_listener() {\n      const product_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.viewProduct(product_r8));\n    });\n    i0.ɵɵelementStart(1, \"div\", 47);\n    i0.ɵɵelement(2, \"img\", 34);\n    i0.ɵɵelementStart(3, \"div\", 48);\n    i0.ɵɵtemplate(4, ShopComponent_div_52_span_4_Template, 2, 0, \"span\", 49)(5, ShopComponent_div_52_span_5_Template, 2, 0, \"span\", 50)(6, ShopComponent_div_52_span_6_Template, 2, 1, \"span\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 52)(8, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_52_Template_button_click_8_listener($event) {\n      const product_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.addToWishlist(product_r8, $event));\n    });\n    i0.ɵɵelement(9, \"i\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_52_Template_button_click_10_listener($event) {\n      const product_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.quickAddToCart(product_r8, $event));\n    });\n    i0.ɵɵelement(11, \"i\", 56);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 57)(13, \"h4\", 58);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\", 59);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, ShopComponent_div_52_div_17_Template, 6, 4, \"div\", 60);\n    i0.ɵɵelementStart(18, \"div\", 61)(19, \"span\", 62);\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, ShopComponent_div_52_span_22_Template, 3, 4, \"span\", 63);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r8 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.getProductImage(product_r8), i0.ɵɵsanitizeUrl)(\"alt\", product_r8.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r8.isTrending);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r8.isNew);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r8.originalPrice && product_r8.originalPrice > product_r8.price);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(product_r8.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r8.brand);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r8.rating);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(21, 10, product_r8.price, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r8.originalPrice && product_r8.originalPrice > product_r8.price);\n  }\n}\nfunction ShopComponent_div_62_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getDiscountPercentage(product_r10), \"% OFF \");\n  }\n}\nfunction ShopComponent_div_62_div_17_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 72);\n  }\n}\nfunction ShopComponent_div_62_div_17_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 73);\n  }\n}\nfunction ShopComponent_div_62_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"div\", 68);\n    i0.ɵɵtemplate(2, ShopComponent_div_62_div_17_i_2_Template, 1, 0, \"i\", 69)(3, ShopComponent_div_62_div_17_i_3_Template, 1, 0, \"i\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 71);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getStars(product_r10.rating.average));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getEmptyStars(product_r10.rating.average));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", product_r10.rating.average, \" (\", product_r10.rating.count, \")\");\n  }\n}\nfunction ShopComponent_div_62_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind2(2, 1, product_r10.originalPrice, \"1.0-0\"), \" \");\n  }\n}\nfunction ShopComponent_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_62_Template_div_click_0_listener() {\n      const product_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.viewProduct(product_r10));\n    });\n    i0.ɵɵelementStart(1, \"div\", 47);\n    i0.ɵɵelement(2, \"img\", 34);\n    i0.ɵɵelementStart(3, \"div\", 48)(4, \"span\", 65);\n    i0.ɵɵtext(5, \"New\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ShopComponent_div_62_span_6_Template, 2, 1, \"span\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 52)(8, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_62_Template_button_click_8_listener($event) {\n      const product_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.addToWishlist(product_r10, $event));\n    });\n    i0.ɵɵelement(9, \"i\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_62_Template_button_click_10_listener($event) {\n      const product_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.quickAddToCart(product_r10, $event));\n    });\n    i0.ɵɵelement(11, \"i\", 56);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 57)(13, \"h4\", 58);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\", 59);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, ShopComponent_div_62_div_17_Template, 6, 4, \"div\", 60);\n    i0.ɵɵelementStart(18, \"div\", 61)(19, \"span\", 62);\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, ShopComponent_div_62_span_22_Template, 3, 4, \"span\", 63);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r10 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.getProductImage(product_r10), i0.ɵɵsanitizeUrl)(\"alt\", product_r10.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", product_r10.originalPrice && product_r10.originalPrice > product_r10.price);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(product_r10.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r10.brand);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r10.rating);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(21, 8, product_r10.price, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r10.originalPrice && product_r10.originalPrice > product_r10.price);\n  }\n}\nexport class ShopComponent {\n  constructor(router) {\n    this.router = router;\n    this.searchQuery = '';\n    this.categories = [];\n    this.featuredBrands = [];\n    this.trendingProducts = [];\n    this.newArrivals = [];\n  }\n  ngOnInit() {\n    this.loadCategories();\n    this.loadFeaturedBrands();\n    this.loadTrendingProducts();\n    this.loadNewArrivals();\n  }\n  loadCategories() {\n    // Load categories from real API\n    this.categories = [];\n  }\n  loadFeaturedBrands() {\n    // Load from real API\n    this.featuredBrands = [];\n  }\n  loadTrendingProducts() {\n    // Load from real API\n    this.trendingProducts = [];\n  }\n  loadNewArrivals() {\n    // Load from real API\n    this.newArrivals = [];\n  }\n  search() {\n    if (this.searchQuery.trim()) {\n      this.router.navigate(['/search'], {\n        queryParams: {\n          q: this.searchQuery\n        }\n      });\n    }\n  }\n  navigateToCategory(categoryId) {\n    this.router.navigate(['/category', categoryId]);\n  }\n  navigateToBrand(brandId) {\n    this.router.navigate(['/brand', brandId]);\n  }\n  viewProduct(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  viewAllTrending() {\n    this.router.navigate(['/category/trending']);\n  }\n  viewAllNew() {\n    this.router.navigate(['/category/new-arrivals']);\n  }\n  addToWishlist(product, event) {\n    event.stopPropagation();\n    // TODO: Check authentication first\n    if (!this.isAuthenticated()) {\n      this.showLoginPrompt('add to wishlist');\n      return;\n    }\n    // TODO: Implement wishlist API call\n    console.log('Add to wishlist:', product);\n    this.showSuccessMessage(`${product.name} added to wishlist!`);\n  }\n  quickAddToCart(product, event) {\n    event.stopPropagation();\n    // TODO: Check authentication first\n    if (!this.isAuthenticated()) {\n      this.showLoginPrompt('add to cart');\n      return;\n    }\n    // TODO: Implement add to cart API call\n    console.log('Add to cart:', product);\n    this.showSuccessMessage(`${product.name} added to cart!`);\n  }\n  getProductImage(product) {\n    return product.images[0]?.url || '/assets/images/placeholder.jpg';\n  }\n  getDiscountPercentage(product) {\n    if (!product.originalPrice || product.originalPrice <= product.price) return 0;\n    return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n  }\n  getStars(rating) {\n    return Array(Math.floor(rating)).fill(0);\n  }\n  getEmptyStars(rating) {\n    return Array(5 - Math.floor(rating)).fill(0);\n  }\n  isAuthenticated() {\n    // TODO: Implement actual authentication check\n    return localStorage.getItem('authToken') !== null;\n  }\n  showLoginPrompt(action) {\n    const message = `Please login to ${action}`;\n    if (confirm(`${message}. Would you like to login now?`)) {\n      this.router.navigate(['/auth/login']);\n    }\n  }\n  showSuccessMessage(message) {\n    // TODO: Implement proper toast/notification system\n    alert(message);\n  }\n  static {\n    this.ɵfac = function ShopComponent_Factory(t) {\n      return new (t || ShopComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ShopComponent,\n      selectors: [[\"app-shop\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 92,\n      vars: 5,\n      consts: [[1, \"shop-container\"], [1, \"hero-banner\"], [1, \"hero-content\"], [1, \"hero-search\"], [\"type\", \"text\", \"placeholder\", \"Search for products, brands, categories...\", 3, \"ngModelChange\", \"ngModel\"], [3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"hero-stats\"], [1, \"stat\"], [1, \"stat-number\"], [1, \"stat-label\"], [1, \"categories-section\"], [1, \"section-header\"], [1, \"categories-grid\"], [\"class\", \"category-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"brands-section\"], [1, \"brands-grid\"], [\"class\", \"brand-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"trending-section\"], [1, \"view-all-btn\", 3, \"click\"], [1, \"products-grid\"], [\"class\", \"product-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"new-arrivals-section\"], [1, \"quick-links-section\"], [1, \"quick-links-grid\"], [1, \"quick-link\", 3, \"click\"], [1, \"fas\", \"fa-female\"], [1, \"fas\", \"fa-male\"], [1, \"fas\", \"fa-child\"], [1, \"fas\", \"fa-star-and-crescent\"], [1, \"fas\", \"fa-gem\"], [1, \"fas\", \"fa-shoe-prints\"], [1, \"category-card\", 3, \"click\"], [1, \"category-image\"], [\"loading\", \"lazy\", 3, \"src\", \"alt\"], [1, \"category-overlay\"], [1, \"product-count\"], [1, \"category-info\"], [1, \"subcategories\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"more\", 4, \"ngIf\"], [1, \"more\"], [1, \"brand-card\", 3, \"click\"], [1, \"brand-logo\"], [\"class\", \"popular-badge\", 4, \"ngIf\"], [1, \"popular-badge\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image\"], [1, \"product-badges\"], [\"class\", \"badge trending\", 4, \"ngIf\"], [\"class\", \"badge new\", 4, \"ngIf\"], [\"class\", \"badge discount\", 4, \"ngIf\"], [1, \"product-actions\"], [1, \"btn-wishlist\", 3, \"click\"], [1, \"far\", \"fa-heart\"], [1, \"btn-quick-add\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"product-info\"], [1, \"product-name\"], [1, \"product-brand\"], [\"class\", \"product-rating\", 4, \"ngIf\"], [1, \"product-price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"badge\", \"trending\"], [1, \"badge\", \"new\"], [1, \"badge\", \"discount\"], [1, \"product-rating\"], [1, \"stars\"], [\"class\", \"fas fa-star\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"far fa-star\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [1, \"fas\", \"fa-star\"], [1, \"far\", \"fa-star\"], [1, \"original-price\"]],\n      template: function ShopComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n          i0.ɵɵtext(4, \"Discover Fashion That Defines You\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\");\n          i0.ɵɵtext(6, \"Explore thousands of products from top brands\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 3)(8, \"input\", 4);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ShopComponent_Template_input_ngModelChange_8_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery, $event) || (ctx.searchQuery = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function ShopComponent_Template_button_click_9_listener() {\n            return ctx.search();\n          });\n          i0.ɵɵelement(10, \"i\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 7)(12, \"div\", 8)(13, \"span\", 9);\n          i0.ɵɵtext(14, \"10K+\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"span\", 10);\n          i0.ɵɵtext(16, \"Products\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 8)(18, \"span\", 9);\n          i0.ɵɵtext(19, \"500+\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"span\", 10);\n          i0.ɵɵtext(21, \"Brands\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 8)(23, \"span\", 9);\n          i0.ɵɵtext(24, \"50K+\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"span\", 10);\n          i0.ɵɵtext(26, \"Happy Customers\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(27, \"section\", 11)(28, \"div\", 12)(29, \"h2\");\n          i0.ɵɵtext(30, \"Shop by Category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"p\");\n          i0.ɵɵtext(32, \"Find exactly what you're looking for\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 13);\n          i0.ɵɵtemplate(34, ShopComponent_div_34_Template, 12, 6, \"div\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"section\", 15)(36, \"div\", 12)(37, \"h2\");\n          i0.ɵɵtext(38, \"Featured Brands\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"p\");\n          i0.ɵɵtext(40, \"Shop from your favorite brands\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"div\", 16);\n          i0.ɵɵtemplate(42, ShopComponent_div_42_Template, 6, 4, \"div\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"section\", 18)(44, \"div\", 12)(45, \"h2\");\n          i0.ɵɵtext(46, \"Trending Now\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"p\");\n          i0.ɵɵtext(48, \"What everyone's buying\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function ShopComponent_Template_button_click_49_listener() {\n            return ctx.viewAllTrending();\n          });\n          i0.ɵɵtext(50, \"View All\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"div\", 20);\n          i0.ɵɵtemplate(52, ShopComponent_div_52_Template, 23, 13, \"div\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"section\", 22)(54, \"div\", 12)(55, \"h2\");\n          i0.ɵɵtext(56, \"New Arrivals\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"p\");\n          i0.ɵɵtext(58, \"Fresh styles just dropped\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function ShopComponent_Template_button_click_59_listener() {\n            return ctx.viewAllNew();\n          });\n          i0.ɵɵtext(60, \"View All\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(61, \"div\", 20);\n          i0.ɵɵtemplate(62, ShopComponent_div_62_Template, 23, 11, \"div\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(63, \"section\", 23)(64, \"div\", 12)(65, \"h2\");\n          i0.ɵɵtext(66, \"Quick Links\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"div\", 24)(68, \"div\", 25);\n          i0.ɵɵlistener(\"click\", function ShopComponent_Template_div_click_68_listener() {\n            return ctx.navigateToCategory(\"women\");\n          });\n          i0.ɵɵelement(69, \"i\", 26);\n          i0.ɵɵelementStart(70, \"span\");\n          i0.ɵɵtext(71, \"Women's Fashion\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(72, \"div\", 25);\n          i0.ɵɵlistener(\"click\", function ShopComponent_Template_div_click_72_listener() {\n            return ctx.navigateToCategory(\"men\");\n          });\n          i0.ɵɵelement(73, \"i\", 27);\n          i0.ɵɵelementStart(74, \"span\");\n          i0.ɵɵtext(75, \"Men's Fashion\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(76, \"div\", 25);\n          i0.ɵɵlistener(\"click\", function ShopComponent_Template_div_click_76_listener() {\n            return ctx.navigateToCategory(\"kids\");\n          });\n          i0.ɵɵelement(77, \"i\", 28);\n          i0.ɵɵelementStart(78, \"span\");\n          i0.ɵɵtext(79, \"Kids' Fashion\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(80, \"div\", 25);\n          i0.ɵɵlistener(\"click\", function ShopComponent_Template_div_click_80_listener() {\n            return ctx.navigateToCategory(\"ethnic\");\n          });\n          i0.ɵɵelement(81, \"i\", 29);\n          i0.ɵɵelementStart(82, \"span\");\n          i0.ɵɵtext(83, \"Ethnic Wear\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(84, \"div\", 25);\n          i0.ɵɵlistener(\"click\", function ShopComponent_Template_div_click_84_listener() {\n            return ctx.navigateToCategory(\"accessories\");\n          });\n          i0.ɵɵelement(85, \"i\", 30);\n          i0.ɵɵelementStart(86, \"span\");\n          i0.ɵɵtext(87, \"Accessories\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(88, \"div\", 25);\n          i0.ɵɵlistener(\"click\", function ShopComponent_Template_div_click_88_listener() {\n            return ctx.navigateToCategory(\"shoes\");\n          });\n          i0.ɵɵelement(89, \"i\", 31);\n          i0.ɵɵelementStart(90, \"span\");\n          i0.ɵɵtext(91, \"Footwear\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(26);\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.featuredBrands);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngForOf\", ctx.trendingProducts);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngForOf\", ctx.newArrivals);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.DecimalPipe, FormsModule, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel],\n      styles: [\".shop-container[_ngcontent-%COMP%] {\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  padding: 0 20px;\\n}\\n\\n\\n\\n.hero-banner[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  padding: 60px 40px;\\n  border-radius: 20px;\\n  margin: 20px 0 40px;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.hero-banner[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grain\\\" width=\\\"100\\\" height=\\\"100\\\" patternUnits=\\\"userSpaceOnUse\\\"><circle cx=\\\"25\\\" cy=\\\"25\\\" r=\\\"1\\\" fill=\\\"white\\\" opacity=\\\"0.1\\\"/><circle cx=\\\"75\\\" cy=\\\"75\\\" r=\\\"1\\\" fill=\\\"white\\\" opacity=\\\"0.1\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grain)\\\"/></svg>');\\n  opacity: 0.3;\\n}\\n\\n.hero-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 2;\\n  text-align: center;\\n  max-width: 600px;\\n  margin: 0 auto;\\n}\\n\\n.hero-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  font-weight: 700;\\n  margin-bottom: 16px;\\n  line-height: 1.2;\\n}\\n\\n.hero-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  margin-bottom: 32px;\\n  opacity: 0.9;\\n}\\n\\n.hero-search[_ngcontent-%COMP%] {\\n  display: flex;\\n  max-width: 500px;\\n  margin: 0 auto 40px;\\n  background: white;\\n  border-radius: 50px;\\n  overflow: hidden;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\\n}\\n\\n.hero-search[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 16px 24px;\\n  border: none;\\n  font-size: 1rem;\\n  color: #333;\\n}\\n\\n.hero-search[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n}\\n\\n.hero-search[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 16px 24px;\\n  background: #007bff;\\n  border: none;\\n  color: white;\\n  cursor: pointer;\\n  transition: background 0.2s;\\n}\\n\\n.hero-search[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background: #0056b3;\\n}\\n\\n.hero-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 60px;\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n.stat[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.stat-number[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 2rem;\\n  font-weight: 700;\\n  margin-bottom: 4px;\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  opacity: 0.8;\\n}\\n\\n\\n\\n.section-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 40px;\\n  position: relative;\\n}\\n\\n.section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  margin-bottom: 8px;\\n  color: #333;\\n}\\n\\n.section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  color: #666;\\n  margin: 0;\\n}\\n\\n.view-all-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 0;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  background: #007bff;\\n  color: white;\\n  border: none;\\n  padding: 10px 20px;\\n  border-radius: 25px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n}\\n\\n.view-all-btn[_ngcontent-%COMP%]:hover {\\n  background: #0056b3;\\n  transform: translateY(-50%) scale(1.05);\\n}\\n\\n\\n\\n.categories-section[_ngcontent-%COMP%] {\\n  margin: 60px 0;\\n}\\n\\n.categories-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 24px;\\n}\\n\\n.category-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n\\n.category-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n}\\n\\n.category-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 200px;\\n  overflow: hidden;\\n}\\n\\n.category-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n\\n.category-card[_ngcontent-%COMP%]:hover   .category-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.category-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));\\n  padding: 20px;\\n  color: white;\\n}\\n\\n.product-count[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n}\\n\\n.category-info[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n.category-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n  margin-bottom: 8px;\\n  color: #333;\\n}\\n\\n.subcategories[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n}\\n\\n.subcategories[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  color: #666;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 0.8rem;\\n}\\n\\n.subcategories[_ngcontent-%COMP%]   .more[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  color: white;\\n}\\n\\n\\n\\n.brands-section[_ngcontent-%COMP%] {\\n  margin: 60px 0;\\n  background: #f8f9fa;\\n  padding: 40px;\\n  border-radius: 20px;\\n}\\n\\n.brands-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\\n  gap: 24px;\\n}\\n\\n.brand-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  padding: 20px;\\n  text-align: center;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  position: relative;\\n}\\n\\n.brand-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);\\n}\\n\\n.brand-logo[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  margin: 0 auto 12px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  background: #f8f9fa;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.brand-logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.brand-card[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #333;\\n  margin: 0;\\n}\\n\\n.popular-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  right: 8px;\\n  background: #ff6b6b;\\n  color: white;\\n  padding: 2px 8px;\\n  border-radius: 10px;\\n  font-size: 0.7rem;\\n  font-weight: 500;\\n}\\n\\n\\n\\n.trending-section[_ngcontent-%COMP%], .new-arrivals-section[_ngcontent-%COMP%] {\\n  margin: 60px 0;\\n}\\n\\n.products-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\\n  gap: 24px;\\n}\\n\\n.product-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n\\n.product-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\\n}\\n\\n.product-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 280px;\\n  overflow: hidden;\\n}\\n\\n.product-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n\\n.product-card[_ngcontent-%COMP%]:hover   .product-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n\\n.product-badges[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  left: 12px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n}\\n\\n.badge.trending[_ngcontent-%COMP%] {\\n  background: #ff6b6b;\\n  color: white;\\n}\\n\\n.badge.new[_ngcontent-%COMP%] {\\n  background: #4ecdc4;\\n  color: white;\\n}\\n\\n.badge.discount[_ngcontent-%COMP%] {\\n  background: #ffa726;\\n  color: white;\\n}\\n\\n.product-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.product-card[_ngcontent-%COMP%]:hover   .product-actions[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.btn-wishlist[_ngcontent-%COMP%], .btn-quick-add[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  border: none;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #333;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.2s ease;\\n}\\n\\n.btn-wishlist[_ngcontent-%COMP%]:hover {\\n  background: #ff6b6b;\\n  color: white;\\n}\\n\\n.btn-quick-add[_ngcontent-%COMP%]:hover {\\n  background: #007bff;\\n  color: white;\\n}\\n\\n.product-info[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n\\n.product-name[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  margin-bottom: 4px;\\n  color: #333;\\n  line-height: 1.3;\\n}\\n\\n.product-brand[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.85rem;\\n  margin-bottom: 8px;\\n}\\n\\n.product-rating[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 8px;\\n}\\n\\n.stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1px;\\n}\\n\\n.stars[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: #ffc107;\\n}\\n\\n.rating-text[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #666;\\n}\\n\\n.product-price[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.current-price[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 700;\\n  color: #333;\\n}\\n\\n.original-price[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #999;\\n  text-decoration: line-through;\\n}\\n\\n\\n\\n.quick-links-section[_ngcontent-%COMP%] {\\n  margin: 60px 0;\\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\\n  padding: 40px;\\n  border-radius: 20px;\\n  color: white;\\n}\\n\\n.quick-links-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 20px;\\n}\\n\\n.quick-link[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: 12px;\\n  padding: 24px;\\n  text-align: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n\\n.quick-link[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  transform: translateY(-4px);\\n}\\n\\n.quick-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  margin-bottom: 12px;\\n  display: block;\\n}\\n\\n.quick-link[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .hero-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .hero-stats[_ngcontent-%COMP%] {\\n    gap: 30px;\\n  }\\n  .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .view-all-btn[_ngcontent-%COMP%] {\\n    position: static;\\n    transform: none;\\n    margin-top: 16px;\\n  }\\n  .categories-grid[_ngcontent-%COMP%], .products-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n    gap: 16px;\\n  }\\n  .brands-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\\n  }\\n  .quick-links-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "sub_r4", "ɵɵtextInterpolate1", "category_r2", "subcategories", "length", "ɵɵlistener", "ShopComponent_div_34_Template_div_click_0_listener", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "navigateToCategory", "id", "ɵɵelement", "ɵɵtemplate", "ShopComponent_div_34_span_10_Template", "ShopComponent_div_34_span_11_Template", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "name", "productCount", "slice", "ShopComponent_div_42_Template_div_click_0_listener", "brand_r6", "_r5", "navigate<PERSON><PERSON>Brand", "ShopComponent_div_42_span_5_Template", "logo", "isPopular", "getDiscountPercentage", "product_r8", "ShopComponent_div_52_div_17_i_2_Template", "ShopComponent_div_52_div_17_i_3_Template", "getStars", "rating", "average", "getEmptyStars", "ɵɵtextInterpolate2", "count", "ɵɵpipeBind2", "originalPrice", "ShopComponent_div_52_Template_div_click_0_listener", "_r7", "viewProduct", "ShopComponent_div_52_span_4_Template", "ShopComponent_div_52_span_5_Template", "ShopComponent_div_52_span_6_Template", "ShopComponent_div_52_Template_button_click_8_listener", "$event", "addToWishlist", "ShopComponent_div_52_Template_button_click_10_listener", "quickAddToCart", "ShopComponent_div_52_div_17_Template", "ShopComponent_div_52_span_22_Template", "getProductImage", "isTrending", "isNew", "price", "brand", "product_r10", "ShopComponent_div_62_div_17_i_2_Template", "ShopComponent_div_62_div_17_i_3_Template", "ShopComponent_div_62_Template_div_click_0_listener", "_r9", "ShopComponent_div_62_span_6_Template", "ShopComponent_div_62_Template_button_click_8_listener", "ShopComponent_div_62_Template_button_click_10_listener", "ShopComponent_div_62_div_17_Template", "ShopComponent_div_62_span_22_Template", "ShopComponent", "constructor", "router", "searchQuery", "categories", "featuredB<PERSON>s", "trendingProducts", "newArrivals", "ngOnInit", "loadCategories", "loadFeaturedBrands", "loadTrendingProducts", "loadNewArrivals", "search", "trim", "navigate", "queryParams", "q", "categoryId", "brandId", "product", "_id", "viewAllTrending", "viewAllNew", "event", "stopPropagation", "isAuthenticated", "showLoginPrompt", "console", "log", "showSuccessMessage", "images", "url", "Math", "round", "Array", "floor", "fill", "localStorage", "getItem", "action", "message", "confirm", "alert", "ɵɵdirectiveInject", "i1", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ShopComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "ShopComponent_Template_input_ngModelChange_8_listener", "ɵɵtwoWayBindingSet", "ShopComponent_Template_button_click_9_listener", "ShopComponent_div_34_Template", "ShopComponent_div_42_Template", "ShopComponent_Template_button_click_49_listener", "ShopComponent_div_52_Template", "ShopComponent_Template_button_click_59_listener", "ShopComponent_div_62_Template", "ShopComponent_Template_div_click_68_listener", "ShopComponent_Template_div_click_72_listener", "ShopComponent_Template_div_click_76_listener", "ShopComponent_Template_div_click_80_listener", "ShopComponent_Template_div_click_84_listener", "ShopComponent_Template_div_click_88_listener", "ɵɵtwoWayProperty", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i3", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\shop\\shop.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\n\ninterface Category {\n  id: string;\n  name: string;\n  image: string;\n  subcategories: string[];\n  productCount: number;\n}\n\ninterface Brand {\n  id: string;\n  name: string;\n  logo: string;\n  isPopular: boolean;\n}\n\ninterface Product {\n  _id: string;\n  name: string;\n  price: number;\n  originalPrice?: number;\n  images: { url: string; alt: string }[];\n  brand: string;\n  rating: { average: number; count: number };\n  category: string;\n  subcategory: string;\n  tags: string[];\n  isNew: boolean;\n  isTrending: boolean;\n}\n\n@Component({\n  selector: 'app-shop',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <div class=\"shop-container\">\n      <!-- Hero Banner -->\n      <div class=\"hero-banner\">\n        <div class=\"hero-content\">\n          <h1>Discover Fashion That Defines You</h1>\n          <p>Explore thousands of products from top brands</p>\n          <div class=\"hero-search\">\n            <input type=\"text\" placeholder=\"Search for products, brands, categories...\" [(ngModel)]=\"searchQuery\">\n            <button (click)=\"search()\">\n              <i class=\"fas fa-search\"></i>\n            </button>\n          </div>\n        </div>\n        <div class=\"hero-stats\">\n          <div class=\"stat\">\n            <span class=\"stat-number\">10K+</span>\n            <span class=\"stat-label\">Products</span>\n          </div>\n          <div class=\"stat\">\n            <span class=\"stat-number\">500+</span>\n            <span class=\"stat-label\">Brands</span>\n          </div>\n          <div class=\"stat\">\n            <span class=\"stat-number\">50K+</span>\n            <span class=\"stat-label\">Happy Customers</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Categories Section -->\n      <section class=\"categories-section\">\n        <div class=\"section-header\">\n          <h2>Shop by Category</h2>\n          <p>Find exactly what you're looking for</p>\n        </div>\n\n        <div class=\"categories-grid\">\n          <div class=\"category-card\" *ngFor=\"let category of categories\" (click)=\"navigateToCategory(category.id)\">\n            <div class=\"category-image\">\n              <img [src]=\"category.image\" [alt]=\"category.name\" loading=\"lazy\">\n              <div class=\"category-overlay\">\n                <span class=\"product-count\">{{ category.productCount }}+ Products</span>\n              </div>\n            </div>\n            <div class=\"category-info\">\n              <h3>{{ category.name }}</h3>\n              <div class=\"subcategories\">\n                <span *ngFor=\"let sub of category.subcategories.slice(0, 3)\">{{ sub }}</span>\n                <span *ngIf=\"category.subcategories.length > 3\" class=\"more\">+{{ category.subcategories.length - 3 }} more</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Featured Brands -->\n      <section class=\"brands-section\">\n        <div class=\"section-header\">\n          <h2>Featured Brands</h2>\n          <p>Shop from your favorite brands</p>\n        </div>\n\n        <div class=\"brands-grid\">\n          <div class=\"brand-card\" *ngFor=\"let brand of featuredBrands\" (click)=\"navigateToBrand(brand.id)\">\n            <div class=\"brand-logo\">\n              <img [src]=\"brand.logo\" [alt]=\"brand.name\" loading=\"lazy\">\n            </div>\n            <h4>{{ brand.name }}</h4>\n            <span class=\"popular-badge\" *ngIf=\"brand.isPopular\">Popular</span>\n          </div>\n        </div>\n      </section>\n\n      <!-- Trending Products -->\n      <section class=\"trending-section\">\n        <div class=\"section-header\">\n          <h2>Trending Now</h2>\n          <p>What everyone's buying</p>\n          <button class=\"view-all-btn\" (click)=\"viewAllTrending()\">View All</button>\n        </div>\n\n        <div class=\"products-grid\">\n          <div class=\"product-card\" *ngFor=\"let product of trendingProducts\" (click)=\"viewProduct(product)\">\n            <div class=\"product-image\">\n              <img [src]=\"getProductImage(product)\" [alt]=\"product.name\" loading=\"lazy\">\n              <div class=\"product-badges\">\n                <span class=\"badge trending\" *ngIf=\"product.isTrending\">Trending</span>\n                <span class=\"badge new\" *ngIf=\"product.isNew\">New</span>\n                <span class=\"badge discount\" *ngIf=\"product.originalPrice && product.originalPrice > product.price\">\n                  {{ getDiscountPercentage(product) }}% OFF\n                </span>\n              </div>\n              <div class=\"product-actions\">\n                <button class=\"btn-wishlist\" (click)=\"addToWishlist(product, $event)\">\n                  <i class=\"far fa-heart\"></i>\n                </button>\n                <button class=\"btn-quick-add\" (click)=\"quickAddToCart(product, $event)\">\n                  <i class=\"fas fa-shopping-cart\"></i>\n                </button>\n              </div>\n            </div>\n\n            <div class=\"product-info\">\n              <h4 class=\"product-name\">{{ product.name }}</h4>\n              <p class=\"product-brand\">{{ product.brand }}</p>\n\n              <div class=\"product-rating\" *ngIf=\"product.rating\">\n                <div class=\"stars\">\n                  <i class=\"fas fa-star\" *ngFor=\"let star of getStars(product.rating.average)\"></i>\n                  <i class=\"far fa-star\" *ngFor=\"let star of getEmptyStars(product.rating.average)\"></i>\n                </div>\n                <span class=\"rating-text\">{{ product.rating.average }} ({{ product.rating.count }})</span>\n              </div>\n\n              <div class=\"product-price\">\n                <span class=\"current-price\">₹{{ product.price | number:'1.0-0' }}</span>\n                <span class=\"original-price\" *ngIf=\"product.originalPrice && product.originalPrice > product.price\">\n                  ₹{{ product.originalPrice | number:'1.0-0' }}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- New Arrivals -->\n      <section class=\"new-arrivals-section\">\n        <div class=\"section-header\">\n          <h2>New Arrivals</h2>\n          <p>Fresh styles just dropped</p>\n          <button class=\"view-all-btn\" (click)=\"viewAllNew()\">View All</button>\n        </div>\n\n        <div class=\"products-grid\">\n          <div class=\"product-card\" *ngFor=\"let product of newArrivals\" (click)=\"viewProduct(product)\">\n            <div class=\"product-image\">\n              <img [src]=\"getProductImage(product)\" [alt]=\"product.name\" loading=\"lazy\">\n              <div class=\"product-badges\">\n                <span class=\"badge new\">New</span>\n                <span class=\"badge discount\" *ngIf=\"product.originalPrice && product.originalPrice > product.price\">\n                  {{ getDiscountPercentage(product) }}% OFF\n                </span>\n              </div>\n              <div class=\"product-actions\">\n                <button class=\"btn-wishlist\" (click)=\"addToWishlist(product, $event)\">\n                  <i class=\"far fa-heart\"></i>\n                </button>\n                <button class=\"btn-quick-add\" (click)=\"quickAddToCart(product, $event)\">\n                  <i class=\"fas fa-shopping-cart\"></i>\n                </button>\n              </div>\n            </div>\n\n            <div class=\"product-info\">\n              <h4 class=\"product-name\">{{ product.name }}</h4>\n              <p class=\"product-brand\">{{ product.brand }}</p>\n\n              <div class=\"product-rating\" *ngIf=\"product.rating\">\n                <div class=\"stars\">\n                  <i class=\"fas fa-star\" *ngFor=\"let star of getStars(product.rating.average)\"></i>\n                  <i class=\"far fa-star\" *ngFor=\"let star of getEmptyStars(product.rating.average)\"></i>\n                </div>\n                <span class=\"rating-text\">{{ product.rating.average }} ({{ product.rating.count }})</span>\n              </div>\n\n              <div class=\"product-price\">\n                <span class=\"current-price\">₹{{ product.price | number:'1.0-0' }}</span>\n                <span class=\"original-price\" *ngIf=\"product.originalPrice && product.originalPrice > product.price\">\n                  ₹{{ product.originalPrice | number:'1.0-0' }}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Quick Links -->\n      <section class=\"quick-links-section\">\n        <div class=\"section-header\">\n          <h2>Quick Links</h2>\n        </div>\n\n        <div class=\"quick-links-grid\">\n          <div class=\"quick-link\" (click)=\"navigateToCategory('women')\">\n            <i class=\"fas fa-female\"></i>\n            <span>Women's Fashion</span>\n          </div>\n          <div class=\"quick-link\" (click)=\"navigateToCategory('men')\">\n            <i class=\"fas fa-male\"></i>\n            <span>Men's Fashion</span>\n          </div>\n          <div class=\"quick-link\" (click)=\"navigateToCategory('kids')\">\n            <i class=\"fas fa-child\"></i>\n            <span>Kids' Fashion</span>\n          </div>\n          <div class=\"quick-link\" (click)=\"navigateToCategory('ethnic')\">\n            <i class=\"fas fa-star-and-crescent\"></i>\n            <span>Ethnic Wear</span>\n          </div>\n          <div class=\"quick-link\" (click)=\"navigateToCategory('accessories')\">\n            <i class=\"fas fa-gem\"></i>\n            <span>Accessories</span>\n          </div>\n          <div class=\"quick-link\" (click)=\"navigateToCategory('shoes')\">\n            <i class=\"fas fa-shoe-prints\"></i>\n            <span>Footwear</span>\n          </div>\n        </div>\n      </section>\n    </div>\n  `,\n  styles: [`\n    .shop-container {\n      max-width: 1400px;\n      margin: 0 auto;\n      padding: 0 20px;\n    }\n\n    /* Hero Banner */\n    .hero-banner {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      color: white;\n      padding: 60px 40px;\n      border-radius: 20px;\n      margin: 20px 0 40px;\n      position: relative;\n      overflow: hidden;\n    }\n\n    .hero-banner::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"1\" fill=\"white\" opacity=\"0.1\"/><circle cx=\"75\" cy=\"75\" r=\"1\" fill=\"white\" opacity=\"0.1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');\n      opacity: 0.3;\n    }\n\n    .hero-content {\n      position: relative;\n      z-index: 2;\n      text-align: center;\n      max-width: 600px;\n      margin: 0 auto;\n    }\n\n    .hero-content h1 {\n      font-size: 3rem;\n      font-weight: 700;\n      margin-bottom: 16px;\n      line-height: 1.2;\n    }\n\n    .hero-content p {\n      font-size: 1.2rem;\n      margin-bottom: 32px;\n      opacity: 0.9;\n    }\n\n    .hero-search {\n      display: flex;\n      max-width: 500px;\n      margin: 0 auto 40px;\n      background: white;\n      border-radius: 50px;\n      overflow: hidden;\n      box-shadow: 0 10px 30px rgba(0,0,0,0.2);\n    }\n\n    .hero-search input {\n      flex: 1;\n      padding: 16px 24px;\n      border: none;\n      font-size: 1rem;\n      color: #333;\n    }\n\n    .hero-search input::placeholder {\n      color: #999;\n    }\n\n    .hero-search button {\n      padding: 16px 24px;\n      background: #007bff;\n      border: none;\n      color: white;\n      cursor: pointer;\n      transition: background 0.2s;\n    }\n\n    .hero-search button:hover {\n      background: #0056b3;\n    }\n\n    .hero-stats {\n      display: flex;\n      justify-content: center;\n      gap: 60px;\n      position: relative;\n      z-index: 2;\n    }\n\n    .stat {\n      text-align: center;\n    }\n\n    .stat-number {\n      display: block;\n      font-size: 2rem;\n      font-weight: 700;\n      margin-bottom: 4px;\n    }\n\n    .stat-label {\n      font-size: 0.9rem;\n      opacity: 0.8;\n    }\n\n    /* Section Headers */\n    .section-header {\n      text-align: center;\n      margin-bottom: 40px;\n      position: relative;\n    }\n\n    .section-header h2 {\n      font-size: 2.5rem;\n      font-weight: 700;\n      margin-bottom: 8px;\n      color: #333;\n    }\n\n    .section-header p {\n      font-size: 1.1rem;\n      color: #666;\n      margin: 0;\n    }\n\n    .view-all-btn {\n      position: absolute;\n      right: 0;\n      top: 50%;\n      transform: translateY(-50%);\n      background: #007bff;\n      color: white;\n      border: none;\n      padding: 10px 20px;\n      border-radius: 25px;\n      font-weight: 500;\n      cursor: pointer;\n      transition: all 0.2s;\n    }\n\n    .view-all-btn:hover {\n      background: #0056b3;\n      transform: translateY(-50%) scale(1.05);\n    }\n\n    /* Categories Section */\n    .categories-section {\n      margin: 60px 0;\n    }\n\n    .categories-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n      gap: 24px;\n    }\n\n    .category-card {\n      background: white;\n      border-radius: 16px;\n      overflow: hidden;\n      box-shadow: 0 4px 20px rgba(0,0,0,0.1);\n      transition: all 0.3s ease;\n      cursor: pointer;\n    }\n\n    .category-card:hover {\n      transform: translateY(-8px);\n      box-shadow: 0 12px 40px rgba(0,0,0,0.15);\n    }\n\n    .category-image {\n      position: relative;\n      height: 200px;\n      overflow: hidden;\n    }\n\n    .category-image img {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n      transition: transform 0.3s ease;\n    }\n\n    .category-card:hover .category-image img {\n      transform: scale(1.1);\n    }\n\n    .category-overlay {\n      position: absolute;\n      bottom: 0;\n      left: 0;\n      right: 0;\n      background: linear-gradient(transparent, rgba(0,0,0,0.7));\n      padding: 20px;\n      color: white;\n    }\n\n    .product-count {\n      font-size: 0.9rem;\n      font-weight: 500;\n    }\n\n    .category-info {\n      padding: 20px;\n    }\n\n    .category-info h3 {\n      font-size: 1.3rem;\n      font-weight: 600;\n      margin-bottom: 8px;\n      color: #333;\n    }\n\n    .subcategories {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 8px;\n    }\n\n    .subcategories span {\n      background: #f8f9fa;\n      color: #666;\n      padding: 4px 8px;\n      border-radius: 12px;\n      font-size: 0.8rem;\n    }\n\n    .subcategories .more {\n      background: #007bff;\n      color: white;\n    }\n\n    /* Brands Section */\n    .brands-section {\n      margin: 60px 0;\n      background: #f8f9fa;\n      padding: 40px;\n      border-radius: 20px;\n    }\n\n    .brands-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n      gap: 24px;\n    }\n\n    .brand-card {\n      background: white;\n      border-radius: 12px;\n      padding: 20px;\n      text-align: center;\n      transition: all 0.3s ease;\n      cursor: pointer;\n      position: relative;\n    }\n\n    .brand-card:hover {\n      transform: translateY(-4px);\n      box-shadow: 0 8px 24px rgba(0,0,0,0.1);\n    }\n\n    .brand-logo {\n      width: 80px;\n      height: 80px;\n      margin: 0 auto 12px;\n      border-radius: 50%;\n      overflow: hidden;\n      background: #f8f9fa;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .brand-logo img {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n    }\n\n    .brand-card h4 {\n      font-size: 1rem;\n      font-weight: 600;\n      color: #333;\n      margin: 0;\n    }\n\n    .popular-badge {\n      position: absolute;\n      top: 8px;\n      right: 8px;\n      background: #ff6b6b;\n      color: white;\n      padding: 2px 8px;\n      border-radius: 10px;\n      font-size: 0.7rem;\n      font-weight: 500;\n    }\n\n    /* Products Grid */\n    .trending-section,\n    .new-arrivals-section {\n      margin: 60px 0;\n    }\n\n    .products-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n      gap: 24px;\n    }\n\n    .product-card {\n      background: white;\n      border-radius: 12px;\n      overflow: hidden;\n      box-shadow: 0 2px 12px rgba(0,0,0,0.08);\n      transition: all 0.3s ease;\n      cursor: pointer;\n    }\n\n    .product-card:hover {\n      transform: translateY(-4px);\n      box-shadow: 0 8px 24px rgba(0,0,0,0.15);\n    }\n\n    .product-image {\n      position: relative;\n      height: 280px;\n      overflow: hidden;\n    }\n\n    .product-image img {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n      transition: transform 0.3s ease;\n    }\n\n    .product-card:hover .product-image img {\n      transform: scale(1.05);\n    }\n\n    .product-badges {\n      position: absolute;\n      top: 12px;\n      left: 12px;\n      display: flex;\n      flex-direction: column;\n      gap: 4px;\n    }\n\n    .badge {\n      padding: 4px 8px;\n      border-radius: 12px;\n      font-size: 0.7rem;\n      font-weight: 600;\n      text-transform: uppercase;\n    }\n\n    .badge.trending {\n      background: #ff6b6b;\n      color: white;\n    }\n\n    .badge.new {\n      background: #4ecdc4;\n      color: white;\n    }\n\n    .badge.discount {\n      background: #ffa726;\n      color: white;\n    }\n\n    .product-actions {\n      position: absolute;\n      top: 12px;\n      right: 12px;\n      display: flex;\n      flex-direction: column;\n      gap: 8px;\n      opacity: 0;\n      transition: opacity 0.3s ease;\n    }\n\n    .product-card:hover .product-actions {\n      opacity: 1;\n    }\n\n    .btn-wishlist,\n    .btn-quick-add {\n      width: 36px;\n      height: 36px;\n      border: none;\n      border-radius: 50%;\n      background: rgba(255,255,255,0.9);\n      color: #333;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      transition: all 0.2s ease;\n    }\n\n    .btn-wishlist:hover {\n      background: #ff6b6b;\n      color: white;\n    }\n\n    .btn-quick-add:hover {\n      background: #007bff;\n      color: white;\n    }\n\n    .product-info {\n      padding: 16px;\n    }\n\n    .product-name {\n      font-size: 1rem;\n      font-weight: 600;\n      margin-bottom: 4px;\n      color: #333;\n      line-height: 1.3;\n    }\n\n    .product-brand {\n      color: #666;\n      font-size: 0.85rem;\n      margin-bottom: 8px;\n    }\n\n    .product-rating {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      margin-bottom: 8px;\n    }\n\n    .stars {\n      display: flex;\n      gap: 1px;\n    }\n\n    .stars i {\n      font-size: 0.7rem;\n      color: #ffc107;\n    }\n\n    .rating-text {\n      font-size: 0.75rem;\n      color: #666;\n    }\n\n    .product-price {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .current-price {\n      font-size: 1.1rem;\n      font-weight: 700;\n      color: #333;\n    }\n\n    .original-price {\n      font-size: 0.85rem;\n      color: #999;\n      text-decoration: line-through;\n    }\n\n    /* Quick Links */\n    .quick-links-section {\n      margin: 60px 0;\n      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n      padding: 40px;\n      border-radius: 20px;\n      color: white;\n    }\n\n    .quick-links-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 20px;\n    }\n\n    .quick-link {\n      background: rgba(255,255,255,0.1);\n      backdrop-filter: blur(10px);\n      border-radius: 12px;\n      padding: 24px;\n      text-align: center;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      border: 1px solid rgba(255,255,255,0.2);\n    }\n\n    .quick-link:hover {\n      background: rgba(255,255,255,0.2);\n      transform: translateY(-4px);\n    }\n\n    .quick-link i {\n      font-size: 2rem;\n      margin-bottom: 12px;\n      display: block;\n    }\n\n    .quick-link span {\n      font-weight: 500;\n    }\n\n    /* Responsive Design */\n    @media (max-width: 768px) {\n      .hero-content h1 {\n        font-size: 2rem;\n      }\n\n      .hero-stats {\n        gap: 30px;\n      }\n\n      .section-header h2 {\n        font-size: 2rem;\n      }\n\n      .view-all-btn {\n        position: static;\n        transform: none;\n        margin-top: 16px;\n      }\n\n      .categories-grid,\n      .products-grid {\n        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n        gap: 16px;\n      }\n\n      .brands-grid {\n        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n      }\n\n      .quick-links-grid {\n        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n      }\n    }\n  `]\n})\nexport class ShopComponent implements OnInit {\n  searchQuery = '';\n  categories: Category[] = [];\n  featuredBrands: Brand[] = [];\n  trendingProducts: Product[] = [];\n  newArrivals: Product[] = [];\n\n  constructor(private router: Router) {}\n\n  ngOnInit() {\n    this.loadCategories();\n    this.loadFeaturedBrands();\n    this.loadTrendingProducts();\n    this.loadNewArrivals();\n  }\n\n  loadCategories() {\n    // Load categories from real API\n    this.categories = [];\n  }\n\n  loadFeaturedBrands() {\n    // Load from real API\n    this.featuredBrands = [];\n  }\n\n  loadTrendingProducts() {\n    // Load from real API\n    this.trendingProducts = [];\n  }\n\n  loadNewArrivals() {\n    // Load from real API\n    this.newArrivals = [];\n  }\n\n  search() {\n    if (this.searchQuery.trim()) {\n      this.router.navigate(['/search'], { queryParams: { q: this.searchQuery } });\n    }\n  }\n\n  navigateToCategory(categoryId: string) {\n    this.router.navigate(['/category', categoryId]);\n  }\n\n  navigateToBrand(brandId: string) {\n    this.router.navigate(['/brand', brandId]);\n  }\n\n  viewProduct(product: Product) {\n    this.router.navigate(['/product', product._id]);\n  }\n\n\n\n  viewAllTrending() {\n    this.router.navigate(['/category/trending']);\n  }\n\n  viewAllNew() {\n    this.router.navigate(['/category/new-arrivals']);\n  }\n\n  addToWishlist(product: Product, event: Event) {\n    event.stopPropagation();\n    // TODO: Check authentication first\n    if (!this.isAuthenticated()) {\n      this.showLoginPrompt('add to wishlist');\n      return;\n    }\n    // TODO: Implement wishlist API call\n    console.log('Add to wishlist:', product);\n    this.showSuccessMessage(`${product.name} added to wishlist!`);\n  }\n\n  quickAddToCart(product: Product, event: Event) {\n    event.stopPropagation();\n    // TODO: Check authentication first\n    if (!this.isAuthenticated()) {\n      this.showLoginPrompt('add to cart');\n      return;\n    }\n    // TODO: Implement add to cart API call\n    console.log('Add to cart:', product);\n    this.showSuccessMessage(`${product.name} added to cart!`);\n  }\n\n  getProductImage(product: Product): string {\n    return product.images[0]?.url || '/assets/images/placeholder.jpg';\n  }\n\n  getDiscountPercentage(product: Product): number {\n    if (!product.originalPrice || product.originalPrice <= product.price) return 0;\n    return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);\n  }\n\n  getStars(rating: number): number[] {\n    return Array(Math.floor(rating)).fill(0);\n  }\n\n  getEmptyStars(rating: number): number[] {\n    return Array(5 - Math.floor(rating)).fill(0);\n  }\n\n  private isAuthenticated(): boolean {\n    // TODO: Implement actual authentication check\n    return localStorage.getItem('authToken') !== null;\n  }\n\n  private showLoginPrompt(action: string) {\n    const message = `Please login to ${action}`;\n    if (confirm(`${message}. Would you like to login now?`)) {\n      this.router.navigate(['/auth/login']);\n    }\n  }\n\n  private showSuccessMessage(message: string) {\n    // TODO: Implement proper toast/notification system\n    alert(message);\n  }\n}"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;IAoF5BC,EAAA,CAAAC,cAAA,WAA6D;IAAAD,EAAA,CAAAE,MAAA,GAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAhBH,EAAA,CAAAI,SAAA,EAAS;IAATJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAS;;;;;IACtEN,EAAA,CAAAC,cAAA,eAA6D;IAAAD,EAAA,CAAAE,MAAA,GAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAApDH,EAAA,CAAAI,SAAA,EAA6C;IAA7CJ,EAAA,CAAAO,kBAAA,MAAAC,WAAA,CAAAC,aAAA,CAAAC,MAAA,cAA6C;;;;;;IAXhHV,EAAA,CAAAC,cAAA,cAAyG;IAA1CD,EAAA,CAAAW,UAAA,mBAAAC,mDAAA;MAAA,MAAAJ,WAAA,GAAAR,EAAA,CAAAa,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAG,kBAAA,CAAAX,WAAA,CAAAY,EAAA,CAA+B;IAAA,EAAC;IACtGpB,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAqB,SAAA,cAAiE;IAE/DrB,EADF,CAAAC,cAAA,cAA8B,eACA;IAAAD,EAAA,CAAAE,MAAA,GAAqC;IAErEF,EAFqE,CAAAG,YAAA,EAAO,EACpE,EACF;IAEJH,EADF,CAAAC,cAAA,cAA2B,SACrB;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,cAA2B;IAEzBD,EADA,CAAAsB,UAAA,KAAAC,qCAAA,mBAA6D,KAAAC,qCAAA,mBACA;IAGnExB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAZGH,EAAA,CAAAI,SAAA,GAAsB;IAACJ,EAAvB,CAAAyB,UAAA,QAAAjB,WAAA,CAAAkB,KAAA,EAAA1B,EAAA,CAAA2B,aAAA,CAAsB,QAAAnB,WAAA,CAAAoB,IAAA,CAAsB;IAEnB5B,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAO,kBAAA,KAAAC,WAAA,CAAAqB,YAAA,eAAqC;IAI/D7B,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAG,WAAA,CAAAoB,IAAA,CAAmB;IAEC5B,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAyB,UAAA,YAAAjB,WAAA,CAAAC,aAAA,CAAAqB,KAAA,OAAqC;IACpD9B,EAAA,CAAAI,SAAA,EAAuC;IAAvCJ,EAAA,CAAAyB,UAAA,SAAAjB,WAAA,CAAAC,aAAA,CAAAC,MAAA,KAAuC;;;;;IAoBlDV,EAAA,CAAAC,cAAA,eAAoD;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IALpEH,EAAA,CAAAC,cAAA,cAAiG;IAApCD,EAAA,CAAAW,UAAA,mBAAAoB,mDAAA;MAAA,MAAAC,QAAA,GAAAhC,EAAA,CAAAa,aAAA,CAAAoB,GAAA,EAAAlB,SAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAkB,eAAA,CAAAF,QAAA,CAAAZ,EAAA,CAAyB;IAAA,EAAC;IAC9FpB,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAqB,SAAA,cAA0D;IAC5DrB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAsB,UAAA,IAAAa,oCAAA,mBAAoD;IACtDnC,EAAA,CAAAG,YAAA,EAAM;;;;IAJGH,EAAA,CAAAI,SAAA,GAAkB;IAACJ,EAAnB,CAAAyB,UAAA,QAAAO,QAAA,CAAAI,IAAA,EAAApC,EAAA,CAAA2B,aAAA,CAAkB,QAAAK,QAAA,CAAAJ,IAAA,CAAmB;IAExC5B,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAA2B,QAAA,CAAAJ,IAAA,CAAgB;IACS5B,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAyB,UAAA,SAAAO,QAAA,CAAAK,SAAA,CAAqB;;;;;IAkB9CrC,EAAA,CAAAC,cAAA,eAAwD;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACvEH,EAAA,CAAAC,cAAA,eAA8C;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACxDH,EAAA,CAAAC,cAAA,eAAoG;IAClGD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAO,kBAAA,MAAAS,MAAA,CAAAsB,qBAAA,CAAAC,UAAA,YACF;;;;;IAkBEvC,EAAA,CAAAqB,SAAA,YAAiF;;;;;IACjFrB,EAAA,CAAAqB,SAAA,YAAsF;;;;;IAFxFrB,EADF,CAAAC,cAAA,cAAmD,cAC9B;IAEjBD,EADA,CAAAsB,UAAA,IAAAkB,wCAAA,gBAA6E,IAAAC,wCAAA,gBACK;IACpFzC,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAyD;IACrFF,EADqF,CAAAG,YAAA,EAAO,EACtF;;;;;IAJsCH,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAyB,UAAA,YAAAT,MAAA,CAAA0B,QAAA,CAAAH,UAAA,CAAAI,MAAA,CAAAC,OAAA,EAAmC;IACnC5C,EAAA,CAAAI,SAAA,EAAwC;IAAxCJ,EAAA,CAAAyB,UAAA,YAAAT,MAAA,CAAA6B,aAAA,CAAAN,UAAA,CAAAI,MAAA,CAAAC,OAAA,EAAwC;IAExD5C,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAA8C,kBAAA,KAAAP,UAAA,CAAAI,MAAA,CAAAC,OAAA,QAAAL,UAAA,CAAAI,MAAA,CAAAI,KAAA,MAAyD;;;;;IAKnF/C,EAAA,CAAAC,cAAA,eAAoG;IAClGD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAO,kBAAA,YAAAP,EAAA,CAAAgD,WAAA,OAAAT,UAAA,CAAAU,aAAA,gBACF;;;;;;IApCNjD,EAAA,CAAAC,cAAA,cAAkG;IAA/BD,EAAA,CAAAW,UAAA,mBAAAuC,mDAAA;MAAA,MAAAX,UAAA,GAAAvC,EAAA,CAAAa,aAAA,CAAAsC,GAAA,EAAApC,SAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAoC,WAAA,CAAAb,UAAA,CAAoB;IAAA,EAAC;IAC/FvC,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAqB,SAAA,cAA0E;IAC1ErB,EAAA,CAAAC,cAAA,cAA4B;IAG1BD,EAFA,CAAAsB,UAAA,IAAA+B,oCAAA,mBAAwD,IAAAC,oCAAA,mBACV,IAAAC,oCAAA,mBACsD;IAGtGvD,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA6B,iBAC2C;IAAzCD,EAAA,CAAAW,UAAA,mBAAA6C,sDAAAC,MAAA;MAAA,MAAAlB,UAAA,GAAAvC,EAAA,CAAAa,aAAA,CAAAsC,GAAA,EAAApC,SAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAA0C,aAAA,CAAAnB,UAAA,EAAAkB,MAAA,CAA8B;IAAA,EAAC;IACnEzD,EAAA,CAAAqB,SAAA,YAA4B;IAC9BrB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAwE;IAA1CD,EAAA,CAAAW,UAAA,mBAAAgD,uDAAAF,MAAA;MAAA,MAAAlB,UAAA,GAAAvC,EAAA,CAAAa,aAAA,CAAAsC,GAAA,EAAApC,SAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAA4C,cAAA,CAAArB,UAAA,EAAAkB,MAAA,CAA+B;IAAA,EAAC;IACrEzD,EAAA,CAAAqB,SAAA,aAAoC;IAG1CrB,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAGJH,EADF,CAAAC,cAAA,eAA0B,cACC;IAAAD,EAAA,CAAAE,MAAA,IAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEhDH,EAAA,CAAAsB,UAAA,KAAAuC,oCAAA,kBAAmD;IASjD7D,EADF,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAE,MAAA,IAAqC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxEH,EAAA,CAAAsB,UAAA,KAAAwC,qCAAA,mBAAoG;IAK1G9D,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;;IArCGH,EAAA,CAAAI,SAAA,GAAgC;IAACJ,EAAjC,CAAAyB,UAAA,QAAAT,MAAA,CAAA+C,eAAA,CAAAxB,UAAA,GAAAvC,EAAA,CAAA2B,aAAA,CAAgC,QAAAY,UAAA,CAAAX,IAAA,CAAqB;IAE1B5B,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAyB,UAAA,SAAAc,UAAA,CAAAyB,UAAA,CAAwB;IAC7BhE,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAyB,UAAA,SAAAc,UAAA,CAAA0B,KAAA,CAAmB;IACdjE,EAAA,CAAAI,SAAA,EAAoE;IAApEJ,EAAA,CAAAyB,UAAA,SAAAc,UAAA,CAAAU,aAAA,IAAAV,UAAA,CAAAU,aAAA,GAAAV,UAAA,CAAA2B,KAAA,CAAoE;IAe3ElE,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAkC,UAAA,CAAAX,IAAA,CAAkB;IAClB5B,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAkC,UAAA,CAAA4B,KAAA,CAAmB;IAEfnE,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAyB,UAAA,SAAAc,UAAA,CAAAI,MAAA,CAAoB;IASnB3C,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAO,kBAAA,WAAAP,EAAA,CAAAgD,WAAA,SAAAT,UAAA,CAAA2B,KAAA,eAAqC;IACnClE,EAAA,CAAAI,SAAA,GAAoE;IAApEJ,EAAA,CAAAyB,UAAA,SAAAc,UAAA,CAAAU,aAAA,IAAAV,UAAA,CAAAU,aAAA,GAAAV,UAAA,CAAA2B,KAAA,CAAoE;;;;;IAuBlGlE,EAAA,CAAAC,cAAA,eAAoG;IAClGD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAO,kBAAA,MAAAS,MAAA,CAAAsB,qBAAA,CAAA8B,WAAA,YACF;;;;;IAkBEpE,EAAA,CAAAqB,SAAA,YAAiF;;;;;IACjFrB,EAAA,CAAAqB,SAAA,YAAsF;;;;;IAFxFrB,EADF,CAAAC,cAAA,cAAmD,cAC9B;IAEjBD,EADA,CAAAsB,UAAA,IAAA+C,wCAAA,gBAA6E,IAAAC,wCAAA,gBACK;IACpFtE,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAyD;IACrFF,EADqF,CAAAG,YAAA,EAAO,EACtF;;;;;IAJsCH,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAyB,UAAA,YAAAT,MAAA,CAAA0B,QAAA,CAAA0B,WAAA,CAAAzB,MAAA,CAAAC,OAAA,EAAmC;IACnC5C,EAAA,CAAAI,SAAA,EAAwC;IAAxCJ,EAAA,CAAAyB,UAAA,YAAAT,MAAA,CAAA6B,aAAA,CAAAuB,WAAA,CAAAzB,MAAA,CAAAC,OAAA,EAAwC;IAExD5C,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAA8C,kBAAA,KAAAsB,WAAA,CAAAzB,MAAA,CAAAC,OAAA,QAAAwB,WAAA,CAAAzB,MAAA,CAAAI,KAAA,MAAyD;;;;;IAKnF/C,EAAA,CAAAC,cAAA,eAAoG;IAClGD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAO,kBAAA,YAAAP,EAAA,CAAAgD,WAAA,OAAAoB,WAAA,CAAAnB,aAAA,gBACF;;;;;;IAnCNjD,EAAA,CAAAC,cAAA,cAA6F;IAA/BD,EAAA,CAAAW,UAAA,mBAAA4D,mDAAA;MAAA,MAAAH,WAAA,GAAApE,EAAA,CAAAa,aAAA,CAAA2D,GAAA,EAAAzD,SAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAAoC,WAAA,CAAAgB,WAAA,CAAoB;IAAA,EAAC;IAC1FpE,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAqB,SAAA,cAA0E;IAExErB,EADF,CAAAC,cAAA,cAA4B,eACF;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClCH,EAAA,CAAAsB,UAAA,IAAAmD,oCAAA,mBAAoG;IAGtGzE,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA6B,iBAC2C;IAAzCD,EAAA,CAAAW,UAAA,mBAAA+D,sDAAAjB,MAAA;MAAA,MAAAW,WAAA,GAAApE,EAAA,CAAAa,aAAA,CAAA2D,GAAA,EAAAzD,SAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAA0C,aAAA,CAAAU,WAAA,EAAAX,MAAA,CAA8B;IAAA,EAAC;IACnEzD,EAAA,CAAAqB,SAAA,YAA4B;IAC9BrB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAwE;IAA1CD,EAAA,CAAAW,UAAA,mBAAAgE,uDAAAlB,MAAA;MAAA,MAAAW,WAAA,GAAApE,EAAA,CAAAa,aAAA,CAAA2D,GAAA,EAAAzD,SAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASF,MAAA,CAAA4C,cAAA,CAAAQ,WAAA,EAAAX,MAAA,CAA+B;IAAA,EAAC;IACrEzD,EAAA,CAAAqB,SAAA,aAAoC;IAG1CrB,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAGJH,EADF,CAAAC,cAAA,eAA0B,cACC;IAAAD,EAAA,CAAAE,MAAA,IAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEhDH,EAAA,CAAAsB,UAAA,KAAAsD,oCAAA,kBAAmD;IASjD5E,EADF,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAE,MAAA,IAAqC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxEH,EAAA,CAAAsB,UAAA,KAAAuD,qCAAA,mBAAoG;IAK1G7E,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;;IApCGH,EAAA,CAAAI,SAAA,GAAgC;IAACJ,EAAjC,CAAAyB,UAAA,QAAAT,MAAA,CAAA+C,eAAA,CAAAK,WAAA,GAAApE,EAAA,CAAA2B,aAAA,CAAgC,QAAAyC,WAAA,CAAAxC,IAAA,CAAqB;IAG1B5B,EAAA,CAAAI,SAAA,GAAoE;IAApEJ,EAAA,CAAAyB,UAAA,SAAA2C,WAAA,CAAAnB,aAAA,IAAAmB,WAAA,CAAAnB,aAAA,GAAAmB,WAAA,CAAAF,KAAA,CAAoE;IAe3ElE,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAA+D,WAAA,CAAAxC,IAAA,CAAkB;IAClB5B,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAA+D,WAAA,CAAAD,KAAA,CAAmB;IAEfnE,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAyB,UAAA,SAAA2C,WAAA,CAAAzB,MAAA,CAAoB;IASnB3C,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAO,kBAAA,WAAAP,EAAA,CAAAgD,WAAA,QAAAoB,WAAA,CAAAF,KAAA,eAAqC;IACnClE,EAAA,CAAAI,SAAA,GAAoE;IAApEJ,EAAA,CAAAyB,UAAA,SAAA2C,WAAA,CAAAnB,aAAA,IAAAmB,WAAA,CAAAnB,aAAA,GAAAmB,WAAA,CAAAF,KAAA,CAAoE;;;AAolBlH,OAAM,MAAOY,aAAa;EAOxBC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAN1B,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,UAAU,GAAe,EAAE;IAC3B,KAAAC,cAAc,GAAY,EAAE;IAC5B,KAAAC,gBAAgB,GAAc,EAAE;IAChC,KAAAC,WAAW,GAAc,EAAE;EAEU;EAErCC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAH,cAAcA,CAAA;IACZ;IACA,IAAI,CAACL,UAAU,GAAG,EAAE;EACtB;EAEAM,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACL,cAAc,GAAG,EAAE;EAC1B;EAEAM,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAACL,gBAAgB,GAAG,EAAE;EAC5B;EAEAM,eAAeA,CAAA;IACb;IACA,IAAI,CAACL,WAAW,GAAG,EAAE;EACvB;EAEAM,MAAMA,CAAA;IACJ,IAAI,IAAI,CAACV,WAAW,CAACW,IAAI,EAAE,EAAE;MAC3B,IAAI,CAACZ,MAAM,CAACa,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE;QAAEC,WAAW,EAAE;UAAEC,CAAC,EAAE,IAAI,CAACd;QAAW;MAAE,CAAE,CAAC;;EAE/E;EAEA9D,kBAAkBA,CAAC6E,UAAkB;IACnC,IAAI,CAAChB,MAAM,CAACa,QAAQ,CAAC,CAAC,WAAW,EAAEG,UAAU,CAAC,CAAC;EACjD;EAEA9D,eAAeA,CAAC+D,OAAe;IAC7B,IAAI,CAACjB,MAAM,CAACa,QAAQ,CAAC,CAAC,QAAQ,EAAEI,OAAO,CAAC,CAAC;EAC3C;EAEA7C,WAAWA,CAAC8C,OAAgB;IAC1B,IAAI,CAAClB,MAAM,CAACa,QAAQ,CAAC,CAAC,UAAU,EAAEK,OAAO,CAACC,GAAG,CAAC,CAAC;EACjD;EAIAC,eAAeA,CAAA;IACb,IAAI,CAACpB,MAAM,CAACa,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAC9C;EAEAQ,UAAUA,CAAA;IACR,IAAI,CAACrB,MAAM,CAACa,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;EAClD;EAEAnC,aAAaA,CAACwC,OAAgB,EAAEI,KAAY;IAC1CA,KAAK,CAACC,eAAe,EAAE;IACvB;IACA,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE,EAAE;MAC3B,IAAI,CAACC,eAAe,CAAC,iBAAiB,CAAC;MACvC;;IAEF;IACAC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAET,OAAO,CAAC;IACxC,IAAI,CAACU,kBAAkB,CAAC,GAAGV,OAAO,CAACtE,IAAI,qBAAqB,CAAC;EAC/D;EAEAgC,cAAcA,CAACsC,OAAgB,EAAEI,KAAY;IAC3CA,KAAK,CAACC,eAAe,EAAE;IACvB;IACA,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE,EAAE;MAC3B,IAAI,CAACC,eAAe,CAAC,aAAa,CAAC;MACnC;;IAEF;IACAC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAET,OAAO,CAAC;IACpC,IAAI,CAACU,kBAAkB,CAAC,GAAGV,OAAO,CAACtE,IAAI,iBAAiB,CAAC;EAC3D;EAEAmC,eAAeA,CAACmC,OAAgB;IAC9B,OAAOA,OAAO,CAACW,MAAM,CAAC,CAAC,CAAC,EAAEC,GAAG,IAAI,gCAAgC;EACnE;EAEAxE,qBAAqBA,CAAC4D,OAAgB;IACpC,IAAI,CAACA,OAAO,CAACjD,aAAa,IAAIiD,OAAO,CAACjD,aAAa,IAAIiD,OAAO,CAAChC,KAAK,EAAE,OAAO,CAAC;IAC9E,OAAO6C,IAAI,CAACC,KAAK,CAAE,CAACd,OAAO,CAACjD,aAAa,GAAGiD,OAAO,CAAChC,KAAK,IAAIgC,OAAO,CAACjD,aAAa,GAAI,GAAG,CAAC;EAC5F;EAEAP,QAAQA,CAACC,MAAc;IACrB,OAAOsE,KAAK,CAACF,IAAI,CAACG,KAAK,CAACvE,MAAM,CAAC,CAAC,CAACwE,IAAI,CAAC,CAAC,CAAC;EAC1C;EAEAtE,aAAaA,CAACF,MAAc;IAC1B,OAAOsE,KAAK,CAAC,CAAC,GAAGF,IAAI,CAACG,KAAK,CAACvE,MAAM,CAAC,CAAC,CAACwE,IAAI,CAAC,CAAC,CAAC;EAC9C;EAEQX,eAAeA,CAAA;IACrB;IACA,OAAOY,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,KAAK,IAAI;EACnD;EAEQZ,eAAeA,CAACa,MAAc;IACpC,MAAMC,OAAO,GAAG,mBAAmBD,MAAM,EAAE;IAC3C,IAAIE,OAAO,CAAC,GAAGD,OAAO,gCAAgC,CAAC,EAAE;MACvD,IAAI,CAACvC,MAAM,CAACa,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;;EAEzC;EAEQe,kBAAkBA,CAACW,OAAe;IACxC;IACAE,KAAK,CAACF,OAAO,CAAC;EAChB;;;uBAxHWzC,aAAa,EAAA9E,EAAA,CAAA0H,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAb9C,aAAa;MAAA+C,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA/H,EAAA,CAAAgI,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAvvBhBtI,EAJN,CAAAC,cAAA,aAA4B,aAED,aACG,SACpB;UAAAD,EAAA,CAAAE,MAAA,wCAAiC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC1CH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,oDAA6C;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAElDH,EADF,CAAAC,cAAA,aAAyB,eAC+E;UAA1BD,EAAA,CAAAwI,gBAAA,2BAAAC,sDAAAhF,MAAA;YAAAzD,EAAA,CAAA0I,kBAAA,CAAAH,GAAA,CAAAtD,WAAA,EAAAxB,MAAA,MAAA8E,GAAA,CAAAtD,WAAA,GAAAxB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAyB;UAArGzD,EAAA,CAAAG,YAAA,EAAsG;UACtGH,EAAA,CAAAC,cAAA,gBAA2B;UAAnBD,EAAA,CAAAW,UAAA,mBAAAgI,+CAAA;YAAA,OAASJ,GAAA,CAAA5C,MAAA,EAAQ;UAAA,EAAC;UACxB3F,EAAA,CAAAqB,SAAA,YAA6B;UAGnCrB,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;UAGFH,EAFJ,CAAAC,cAAA,cAAwB,cACJ,eACU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACrCH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UACnCF,EADmC,CAAAG,YAAA,EAAO,EACpC;UAEJH,EADF,CAAAC,cAAA,cAAkB,eACU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACrCH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UACjCF,EADiC,CAAAG,YAAA,EAAO,EAClC;UAEJH,EADF,CAAAC,cAAA,cAAkB,eACU;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACrCH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAG9CF,EAH8C,CAAAG,YAAA,EAAO,EAC3C,EACF,EACF;UAKFH,EAFJ,CAAAC,cAAA,mBAAoC,eACN,UACtB;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,4CAAoC;UACzCF,EADyC,CAAAG,YAAA,EAAI,EACvC;UAENH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAsB,UAAA,KAAAsH,6BAAA,mBAAyG;UAgB7G5I,EADE,CAAAG,YAAA,EAAM,EACE;UAKNH,EAFJ,CAAAC,cAAA,mBAAgC,eACF,UACtB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,sCAA8B;UACnCF,EADmC,CAAAG,YAAA,EAAI,EACjC;UAENH,EAAA,CAAAC,cAAA,eAAyB;UACvBD,EAAA,CAAAsB,UAAA,KAAAuH,6BAAA,kBAAiG;UAQrG7I,EADE,CAAAG,YAAA,EAAM,EACE;UAKNH,EAFJ,CAAAC,cAAA,mBAAkC,eACJ,UACtB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,8BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC7BH,EAAA,CAAAC,cAAA,kBAAyD;UAA5BD,EAAA,CAAAW,UAAA,mBAAAmI,gDAAA;YAAA,OAASP,GAAA,CAAAnC,eAAA,EAAiB;UAAA,EAAC;UAACpG,EAAA,CAAAE,MAAA,gBAAQ;UACnEF,EADmE,CAAAG,YAAA,EAAS,EACtE;UAENH,EAAA,CAAAC,cAAA,eAA2B;UACzBD,EAAA,CAAAsB,UAAA,KAAAyH,6BAAA,oBAAkG;UAyCtG/I,EADE,CAAAG,YAAA,EAAM,EACE;UAKNH,EAFJ,CAAAC,cAAA,mBAAsC,eACR,UACtB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAChCH,EAAA,CAAAC,cAAA,kBAAoD;UAAvBD,EAAA,CAAAW,UAAA,mBAAAqI,gDAAA;YAAA,OAAST,GAAA,CAAAlC,UAAA,EAAY;UAAA,EAAC;UAACrG,EAAA,CAAAE,MAAA,gBAAQ;UAC9DF,EAD8D,CAAAG,YAAA,EAAS,EACjE;UAENH,EAAA,CAAAC,cAAA,eAA2B;UACzBD,EAAA,CAAAsB,UAAA,KAAA2H,6BAAA,oBAA6F;UAwCjGjJ,EADE,CAAAG,YAAA,EAAM,EACE;UAKNH,EAFJ,CAAAC,cAAA,mBAAqC,eACP,UACtB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UACjBF,EADiB,CAAAG,YAAA,EAAK,EAChB;UAGJH,EADF,CAAAC,cAAA,eAA8B,eACkC;UAAtCD,EAAA,CAAAW,UAAA,mBAAAuI,6CAAA;YAAA,OAASX,GAAA,CAAApH,kBAAA,CAAmB,OAAO,CAAC;UAAA,EAAC;UAC3DnB,EAAA,CAAAqB,SAAA,aAA6B;UAC7BrB,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UACvBF,EADuB,CAAAG,YAAA,EAAO,EACxB;UACNH,EAAA,CAAAC,cAAA,eAA4D;UAApCD,EAAA,CAAAW,UAAA,mBAAAwI,6CAAA;YAAA,OAASZ,GAAA,CAAApH,kBAAA,CAAmB,KAAK,CAAC;UAAA,EAAC;UACzDnB,EAAA,CAAAqB,SAAA,aAA2B;UAC3BrB,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UACrBF,EADqB,CAAAG,YAAA,EAAO,EACtB;UACNH,EAAA,CAAAC,cAAA,eAA6D;UAArCD,EAAA,CAAAW,UAAA,mBAAAyI,6CAAA;YAAA,OAASb,GAAA,CAAApH,kBAAA,CAAmB,MAAM,CAAC;UAAA,EAAC;UAC1DnB,EAAA,CAAAqB,SAAA,aAA4B;UAC5BrB,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UACrBF,EADqB,CAAAG,YAAA,EAAO,EACtB;UACNH,EAAA,CAAAC,cAAA,eAA+D;UAAvCD,EAAA,CAAAW,UAAA,mBAAA0I,6CAAA;YAAA,OAASd,GAAA,CAAApH,kBAAA,CAAmB,QAAQ,CAAC;UAAA,EAAC;UAC5DnB,EAAA,CAAAqB,SAAA,aAAwC;UACxCrB,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UACnBF,EADmB,CAAAG,YAAA,EAAO,EACpB;UACNH,EAAA,CAAAC,cAAA,eAAoE;UAA5CD,EAAA,CAAAW,UAAA,mBAAA2I,6CAAA;YAAA,OAASf,GAAA,CAAApH,kBAAA,CAAmB,aAAa,CAAC;UAAA,EAAC;UACjEnB,EAAA,CAAAqB,SAAA,aAA0B;UAC1BrB,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UACnBF,EADmB,CAAAG,YAAA,EAAO,EACpB;UACNH,EAAA,CAAAC,cAAA,eAA8D;UAAtCD,EAAA,CAAAW,UAAA,mBAAA4I,6CAAA;YAAA,OAAShB,GAAA,CAAApH,kBAAA,CAAmB,OAAO,CAAC;UAAA,EAAC;UAC3DnB,EAAA,CAAAqB,SAAA,aAAkC;UAClCrB,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAItBF,EAJsB,CAAAG,YAAA,EAAO,EACjB,EACF,EACE,EACN;;;UA1M8EH,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAAwJ,gBAAA,YAAAjB,GAAA,CAAAtD,WAAA,CAAyB;UA8BvDjF,EAAA,CAAAI,SAAA,IAAa;UAAbJ,EAAA,CAAAyB,UAAA,YAAA8G,GAAA,CAAArD,UAAA,CAAa;UA0BnBlF,EAAA,CAAAI,SAAA,GAAiB;UAAjBJ,EAAA,CAAAyB,UAAA,YAAA8G,GAAA,CAAApD,cAAA,CAAiB;UAmBbnF,EAAA,CAAAI,SAAA,IAAmB;UAAnBJ,EAAA,CAAAyB,UAAA,YAAA8G,GAAA,CAAAnD,gBAAA,CAAmB;UAoDnBpF,EAAA,CAAAI,SAAA,IAAc;UAAdJ,EAAA,CAAAyB,UAAA,YAAA8G,GAAA,CAAAlD,WAAA,CAAc;;;qBAxI1DvF,YAAY,EAAA2J,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAE7J,WAAW,EAAA8J,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}