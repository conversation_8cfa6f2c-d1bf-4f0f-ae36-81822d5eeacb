{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { writeTask, Build } from '@stencil/core/internal/client';\nimport { r as raf } from './helpers.js';\nconst LIFECYCLE_WILL_ENTER = 'ionViewWillEnter';\nconst LIFECYCLE_DID_ENTER = 'ionViewDidEnter';\nconst LIFECYCLE_WILL_LEAVE = 'ionViewWillLeave';\nconst LIFECYCLE_DID_LEAVE = 'ionViewDidLeave';\nconst LIFECYCLE_WILL_UNLOAD = 'ionViewWillUnload';\nconst iosTransitionAnimation = () => import('./ios.transition.js');\nconst mdTransitionAnimation = () => import('./md.transition.js');\n// TODO(FW-2832): types\nconst transition = opts => {\n  return new Promise((resolve, reject) => {\n    writeTask(() => {\n      beforeTransition(opts);\n      runTransition(opts).then(result => {\n        if (result.animation) {\n          result.animation.destroy();\n        }\n        afterTransition(opts);\n        resolve(result);\n      }, error => {\n        afterTransition(opts);\n        reject(error);\n      });\n    });\n  });\n};\nconst beforeTransition = opts => {\n  const enteringEl = opts.enteringEl;\n  const leavingEl = opts.leavingEl;\n  setZIndex(enteringEl, leavingEl, opts.direction);\n  if (opts.showGoBack) {\n    enteringEl.classList.add('can-go-back');\n  } else {\n    enteringEl.classList.remove('can-go-back');\n  }\n  setPageHidden(enteringEl, false);\n  /**\n   * When transitioning, the page should not\n   * respond to click events. This resolves small\n   * issues like users double tapping the ion-back-button.\n   * These pointer events are removed in `afterTransition`.\n   */\n  enteringEl.style.setProperty('pointer-events', 'none');\n  if (leavingEl) {\n    setPageHidden(leavingEl, false);\n    leavingEl.style.setProperty('pointer-events', 'none');\n  }\n};\nconst runTransition = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (opts) {\n    const animationBuilder = yield getAnimationBuilder(opts);\n    const ani = animationBuilder && Build.isBrowser ? animation(animationBuilder, opts) : noAnimation(opts); // fast path for no animation\n    return ani;\n  });\n  return function runTransition(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nconst afterTransition = opts => {\n  const enteringEl = opts.enteringEl;\n  const leavingEl = opts.leavingEl;\n  enteringEl.classList.remove('ion-page-invisible');\n  enteringEl.style.removeProperty('pointer-events');\n  if (leavingEl !== undefined) {\n    leavingEl.classList.remove('ion-page-invisible');\n    leavingEl.style.removeProperty('pointer-events');\n  }\n};\nconst getAnimationBuilder = /*#__PURE__*/function () {\n  var _ref2 = _asyncToGenerator(function* (opts) {\n    if (!opts.leavingEl || !opts.animated || opts.duration === 0) {\n      return undefined;\n    }\n    if (opts.animationBuilder) {\n      return opts.animationBuilder;\n    }\n    const getAnimation = opts.mode === 'ios' ? (yield iosTransitionAnimation()).iosTransitionAnimation : (yield mdTransitionAnimation()).mdTransitionAnimation;\n    return getAnimation;\n  });\n  return function getAnimationBuilder(_x2) {\n    return _ref2.apply(this, arguments);\n  };\n}();\nconst animation = /*#__PURE__*/function () {\n  var _ref3 = _asyncToGenerator(function* (animationBuilder, opts) {\n    yield waitForReady(opts, true);\n    const trans = animationBuilder(opts.baseEl, opts);\n    fireWillEvents(opts.enteringEl, opts.leavingEl);\n    const didComplete = yield playTransition(trans, opts);\n    if (opts.progressCallback) {\n      opts.progressCallback(undefined);\n    }\n    if (didComplete) {\n      fireDidEvents(opts.enteringEl, opts.leavingEl);\n    }\n    return {\n      hasCompleted: didComplete,\n      animation: trans\n    };\n  });\n  return function animation(_x3, _x4) {\n    return _ref3.apply(this, arguments);\n  };\n}();\nconst noAnimation = /*#__PURE__*/function () {\n  var _ref4 = _asyncToGenerator(function* (opts) {\n    const enteringEl = opts.enteringEl;\n    const leavingEl = opts.leavingEl;\n    yield waitForReady(opts, false);\n    fireWillEvents(enteringEl, leavingEl);\n    fireDidEvents(enteringEl, leavingEl);\n    return {\n      hasCompleted: true\n    };\n  });\n  return function noAnimation(_x5) {\n    return _ref4.apply(this, arguments);\n  };\n}();\nconst waitForReady = /*#__PURE__*/function () {\n  var _ref5 = _asyncToGenerator(function* (opts, defaultDeep) {\n    const deep = opts.deepWait !== undefined ? opts.deepWait : defaultDeep;\n    if (deep) {\n      yield Promise.all([_deepReady(opts.enteringEl), _deepReady(opts.leavingEl)]);\n    }\n    yield notifyViewReady(opts.viewIsReady, opts.enteringEl);\n  });\n  return function waitForReady(_x6, _x7) {\n    return _ref5.apply(this, arguments);\n  };\n}();\nconst notifyViewReady = /*#__PURE__*/function () {\n  var _ref6 = _asyncToGenerator(function* (viewIsReady, enteringEl) {\n    if (viewIsReady) {\n      yield viewIsReady(enteringEl);\n    }\n  });\n  return function notifyViewReady(_x8, _x9) {\n    return _ref6.apply(this, arguments);\n  };\n}();\nconst playTransition = (trans, opts) => {\n  const progressCallback = opts.progressCallback;\n  const promise = new Promise(resolve => {\n    trans.onFinish(currentStep => resolve(currentStep === 1));\n  });\n  // cool, let's do this, start the transition\n  if (progressCallback) {\n    // this is a swipe to go back, just get the transition progress ready\n    // kick off the swipe animation start\n    trans.progressStart(true);\n    progressCallback(trans);\n  } else {\n    // only the top level transition should actually start \"play\"\n    // kick it off and let it play through\n    // ******** DOM WRITE ****************\n    trans.play();\n  }\n  // create a callback for when the animation is done\n  return promise;\n};\nconst fireWillEvents = (enteringEl, leavingEl) => {\n  lifecycle(leavingEl, LIFECYCLE_WILL_LEAVE);\n  lifecycle(enteringEl, LIFECYCLE_WILL_ENTER);\n};\nconst fireDidEvents = (enteringEl, leavingEl) => {\n  lifecycle(enteringEl, LIFECYCLE_DID_ENTER);\n  lifecycle(leavingEl, LIFECYCLE_DID_LEAVE);\n};\nconst lifecycle = (el, eventName) => {\n  if (el) {\n    const ev = new CustomEvent(eventName, {\n      bubbles: false,\n      cancelable: false\n    });\n    el.dispatchEvent(ev);\n  }\n};\n/**\n * Wait two request animation frame loops.\n * This allows the framework implementations enough time to mount\n * the user-defined contents. This is often needed when using inline\n * modals and popovers that accept user components. For popover,\n * the contents must be mounted for the popover to be sized correctly.\n * For modals, the contents must be mounted for iOS to run the\n * transition correctly.\n *\n * On Angular and React, a single raf is enough time, but for Vue\n * we need to wait two rafs. As a result we are using two rafs for\n * all frameworks to ensure contents are mounted.\n */\nconst waitForMount = () => {\n  return new Promise(resolve => raf(() => raf(() => resolve())));\n};\nconst _deepReady = /*#__PURE__*/function () {\n  var _ref7 = _asyncToGenerator(function* (el) {\n    const element = el;\n    if (element) {\n      if (element.componentOnReady != null) {\n        // eslint-disable-next-line custom-rules/no-component-on-ready-method\n        const stencilEl = yield element.componentOnReady();\n        if (stencilEl != null) {\n          return;\n        }\n        /**\n         * Custom elements in Stencil will have __registerHost.\n         */\n      } else if (element.__registerHost != null) {\n        /**\n         * Non-lazy loaded custom elements need to wait\n         * one frame for component to be loaded.\n         */\n        const waitForCustomElement = new Promise(resolve => raf(resolve));\n        yield waitForCustomElement;\n        return;\n      }\n      yield Promise.all(Array.from(element.children).map(_deepReady));\n    }\n  });\n  return function deepReady(_x0) {\n    return _ref7.apply(this, arguments);\n  };\n}();\nconst setPageHidden = (el, hidden) => {\n  if (hidden) {\n    el.setAttribute('aria-hidden', 'true');\n    el.classList.add('ion-page-hidden');\n  } else {\n    el.hidden = false;\n    el.removeAttribute('aria-hidden');\n    el.classList.remove('ion-page-hidden');\n  }\n};\nconst setZIndex = (enteringEl, leavingEl, direction) => {\n  if (enteringEl !== undefined) {\n    enteringEl.style.zIndex = direction === 'back' ? '99' : '101';\n  }\n  if (leavingEl !== undefined) {\n    leavingEl.style.zIndex = '100';\n  }\n};\nconst getIonPageElement = element => {\n  if (element.classList.contains('ion-page')) {\n    return element;\n  }\n  const ionPage = element.querySelector(':scope > .ion-page, :scope > ion-nav, :scope > ion-tabs');\n  if (ionPage) {\n    return ionPage;\n  }\n  // idk, return the original element so at least something animates and we don't have a null pointer\n  return element;\n};\nexport { LIFECYCLE_WILL_ENTER as L, LIFECYCLE_DID_ENTER as a, LIFECYCLE_WILL_LEAVE as b, LIFECYCLE_DID_LEAVE as c, LIFECYCLE_WILL_UNLOAD as d, _deepReady as e, getIonPageElement as g, lifecycle as l, setPageHidden as s, transition as t, waitForMount as w };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}