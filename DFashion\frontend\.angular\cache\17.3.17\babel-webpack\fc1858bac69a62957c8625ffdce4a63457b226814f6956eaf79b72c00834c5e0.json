{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nlet ShopComponent = class ShopComponent {\n  constructor(router, productService) {\n    this.router = router;\n    this.productService = productService;\n    this.searchQuery = '';\n    this.categories = [];\n    this.featuredBrands = [];\n    this.trendingProducts = [];\n    this.newArrivals = [];\n  }\n  ngOnInit() {\n    this.loadCategories();\n    this.loadFeaturedBrands();\n    this.loadTrendingProducts();\n    this.loadNewArrivals();\n  }\n  loadCategories() {\n    // Load categories from real API\n    this.productService.getCategories().subscribe({\n      next: response => {\n        this.categories = response?.data || [];\n      },\n      error: error => {\n        console.error('Error loading categories:', error);\n        this.categories = [];\n      }\n    });\n  }\n  loadFeaturedBrands() {\n    // Load from real API\n    this.productService.getFeaturedBrands().subscribe({\n      next: response => {\n        this.featuredBrands = response?.data || [];\n      },\n      error: error => {\n        console.error('Error loading featured brands:', error);\n        this.featuredBrands = [];\n      }\n    });\n  }\n  loadTrendingProducts() {\n    // Load from real API\n    this.productService.getTrendingProducts().subscribe({\n      next: response => {\n        this.trendingProducts = response?.data || [];\n      },\n      error: error => {\n        console.error('Error loading trending products:', error);\n        this.trendingProducts = [];\n      }\n    });\n  }\n  loadNewArrivals() {\n    // Load from real API\n    this.productService.getNewArrivals().subscribe({\n      next: response => {\n        this.newArrivals = response?.data || [];\n      },\n      error: error => {\n        console.error('Error loading new arrivals:', error);\n        this.newArrivals = [];\n      }\n    });\n  }\n  search() {\n    if (this.searchQuery.trim()) {\n      this.router.navigate(['/search'], {\n        queryParams: {\n          q: this.searchQuery\n        }\n      });\n    }\n  }\n  navigateToCategory(categoryId) {\n    this.router.navigate(['/category', categoryId]);\n  }\n  navigateToBrand(brandId) {\n    this.router.navigate(['/brand', brandId]);\n  }\n  viewProduct(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  viewAllTrending() {\n    this.router.navigate(['/category/trending']);\n  }\n  viewAllNew() {\n    this.router.navigate(['/category/new-arrivals']);\n  }\n  addToWishlist(product, event) {\n    event.stopPropagation();\n    // TODO: Check authentication first\n    if (!this.isAuthenticated()) {\n      this.showLoginPrompt('add to wishlist');\n      return;\n    }\n    // TODO: Implement wishlist API call\n    console.log('Add to wishlist:', product);\n    this.showSuccessMessage(`${product.name} added to wishlist!`);\n  }\n  quickAddToCart(product, event) {\n    event.stopPropagation();\n    // TODO: Check authentication first\n    if (!this.isAuthenticated()) {\n      this.showLoginPrompt('add to cart');\n      return;\n    }\n    // TODO: Implement add to cart API call\n    console.log('Add to cart:', product);\n    this.showSuccessMessage(`${product.name} added to cart!`);\n  }\n  getProductImage(product) {\n    return product.images[0]?.url || '/assets/images/placeholder.jpg';\n  }\n  getDiscountPercentage(product) {\n    if (!product.originalPrice || product.originalPrice <= product.price) return 0;\n    return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n  }\n  getStars(rating) {\n    return Array(Math.floor(rating)).fill(0);\n  }\n  getEmptyStars(rating) {\n    return Array(5 - Math.floor(rating)).fill(0);\n  }\n  isAuthenticated() {\n    // TODO: Implement actual authentication check\n    return localStorage.getItem('authToken') !== null;\n  }\n  showLoginPrompt(action) {\n    const message = `Please login to ${action}`;\n    if (confirm(`${message}. Would you like to login now?`)) {\n      this.router.navigate(['/auth/login']);\n    }\n  }\n  showSuccessMessage(message) {\n    // TODO: Implement proper toast/notification system\n    alert(message);\n  }\n};\nShopComponent = __decorate([Component({\n  selector: 'app-shop',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <div class=\"shop-container\">\n      <!-- Hero Banner -->\n      <div class=\"hero-banner\">\n        <div class=\"hero-content\">\n          <h1>Discover Fashion That Defines You</h1>\n          <p>Explore thousands of products from top brands</p>\n          <div class=\"hero-search\">\n            <input type=\"text\" placeholder=\"Search for products, brands, categories...\" [(ngModel)]=\"searchQuery\">\n            <button (click)=\"search()\">\n              <i class=\"fas fa-search\"></i>\n            </button>\n          </div>\n        </div>\n        <div class=\"hero-stats\">\n          <div class=\"stat\">\n            <span class=\"stat-number\">10K+</span>\n            <span class=\"stat-label\">Products</span>\n          </div>\n          <div class=\"stat\">\n            <span class=\"stat-number\">500+</span>\n            <span class=\"stat-label\">Brands</span>\n          </div>\n          <div class=\"stat\">\n            <span class=\"stat-number\">50K+</span>\n            <span class=\"stat-label\">Happy Customers</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Categories Section -->\n      <section class=\"categories-section\">\n        <div class=\"section-header\">\n          <h2>Shop by Category</h2>\n          <p>Find exactly what you're looking for</p>\n        </div>\n\n        <div class=\"categories-grid\">\n          <div class=\"category-card\" *ngFor=\"let category of categories\" (click)=\"navigateToCategory(category.id)\">\n            <div class=\"category-image\">\n              <img [src]=\"category.image\" [alt]=\"category.name\" loading=\"lazy\">\n              <div class=\"category-overlay\">\n                <span class=\"product-count\">{{ category.productCount }}+ Products</span>\n              </div>\n            </div>\n            <div class=\"category-info\">\n              <h3>{{ category.name }}</h3>\n              <div class=\"subcategories\">\n                <span *ngFor=\"let sub of category.subcategories.slice(0, 3)\">{{ sub }}</span>\n                <span *ngIf=\"category.subcategories.length > 3\" class=\"more\">+{{ category.subcategories.length - 3 }} more</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Featured Brands -->\n      <section class=\"brands-section\">\n        <div class=\"section-header\">\n          <h2>Featured Brands</h2>\n          <p>Shop from your favorite brands</p>\n        </div>\n\n        <div class=\"brands-grid\">\n          <div class=\"brand-card\" *ngFor=\"let brand of featuredBrands\" (click)=\"navigateToBrand(brand.id)\">\n            <div class=\"brand-logo\">\n              <img [src]=\"brand.logo\" [alt]=\"brand.name\" loading=\"lazy\">\n            </div>\n            <h4>{{ brand.name }}</h4>\n            <span class=\"popular-badge\" *ngIf=\"brand.isPopular\">Popular</span>\n          </div>\n        </div>\n      </section>\n\n      <!-- Trending Products -->\n      <section class=\"trending-section\">\n        <div class=\"section-header\">\n          <h2>Trending Now</h2>\n          <p>What everyone's buying</p>\n          <button class=\"view-all-btn\" (click)=\"viewAllTrending()\">View All</button>\n        </div>\n\n        <div class=\"products-grid\">\n          <div class=\"product-card\" *ngFor=\"let product of trendingProducts\" (click)=\"viewProduct(product)\">\n            <div class=\"product-image\">\n              <img [src]=\"getProductImage(product)\" [alt]=\"product.name\" loading=\"lazy\">\n              <div class=\"product-badges\">\n                <span class=\"badge trending\" *ngIf=\"product.isTrending\">Trending</span>\n                <span class=\"badge new\" *ngIf=\"product.isNew\">New</span>\n                <span class=\"badge discount\" *ngIf=\"product.originalPrice && product.originalPrice > product.price\">\n                  {{ getDiscountPercentage(product) }}% OFF\n                </span>\n              </div>\n              <div class=\"product-actions\">\n                <button class=\"btn-wishlist\" (click)=\"addToWishlist(product, $event)\">\n                  <i class=\"far fa-heart\"></i>\n                </button>\n                <button class=\"btn-quick-add\" (click)=\"quickAddToCart(product, $event)\">\n                  <i class=\"fas fa-shopping-cart\"></i>\n                </button>\n              </div>\n            </div>\n\n            <div class=\"product-info\">\n              <h4 class=\"product-name\">{{ product.name }}</h4>\n              <p class=\"product-brand\">{{ product.brand }}</p>\n\n              <div class=\"product-rating\" *ngIf=\"product.rating\">\n                <div class=\"stars\">\n                  <i class=\"fas fa-star\" *ngFor=\"let star of getStars(product.rating.average)\"></i>\n                  <i class=\"far fa-star\" *ngFor=\"let star of getEmptyStars(product.rating.average)\"></i>\n                </div>\n                <span class=\"rating-text\">{{ product.rating.average }} ({{ product.rating.count }})</span>\n              </div>\n\n              <div class=\"product-price\">\n                <span class=\"current-price\">₹{{ product.price | number:'1.0-0' }}</span>\n                <span class=\"original-price\" *ngIf=\"product.originalPrice && product.originalPrice > product.price\">\n                  ₹{{ product.originalPrice | number:'1.0-0' }}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- New Arrivals -->\n      <section class=\"new-arrivals-section\">\n        <div class=\"section-header\">\n          <h2>New Arrivals</h2>\n          <p>Fresh styles just dropped</p>\n          <button class=\"view-all-btn\" (click)=\"viewAllNew()\">View All</button>\n        </div>\n\n        <div class=\"products-grid\">\n          <div class=\"product-card\" *ngFor=\"let product of newArrivals\" (click)=\"viewProduct(product)\">\n            <div class=\"product-image\">\n              <img [src]=\"getProductImage(product)\" [alt]=\"product.name\" loading=\"lazy\">\n              <div class=\"product-badges\">\n                <span class=\"badge new\">New</span>\n                <span class=\"badge discount\" *ngIf=\"product.originalPrice && product.originalPrice > product.price\">\n                  {{ getDiscountPercentage(product) }}% OFF\n                </span>\n              </div>\n              <div class=\"product-actions\">\n                <button class=\"btn-wishlist\" (click)=\"addToWishlist(product, $event)\">\n                  <i class=\"far fa-heart\"></i>\n                </button>\n                <button class=\"btn-quick-add\" (click)=\"quickAddToCart(product, $event)\">\n                  <i class=\"fas fa-shopping-cart\"></i>\n                </button>\n              </div>\n            </div>\n\n            <div class=\"product-info\">\n              <h4 class=\"product-name\">{{ product.name }}</h4>\n              <p class=\"product-brand\">{{ product.brand }}</p>\n\n              <div class=\"product-rating\" *ngIf=\"product.rating\">\n                <div class=\"stars\">\n                  <i class=\"fas fa-star\" *ngFor=\"let star of getStars(product.rating.average)\"></i>\n                  <i class=\"far fa-star\" *ngFor=\"let star of getEmptyStars(product.rating.average)\"></i>\n                </div>\n                <span class=\"rating-text\">{{ product.rating.average }} ({{ product.rating.count }})</span>\n              </div>\n\n              <div class=\"product-price\">\n                <span class=\"current-price\">₹{{ product.price | number:'1.0-0' }}</span>\n                <span class=\"original-price\" *ngIf=\"product.originalPrice && product.originalPrice > product.price\">\n                  ₹{{ product.originalPrice | number:'1.0-0' }}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Quick Links -->\n      <section class=\"quick-links-section\">\n        <div class=\"section-header\">\n          <h2>Quick Links</h2>\n        </div>\n\n        <div class=\"quick-links-grid\">\n          <div class=\"quick-link\" (click)=\"navigateToCategory('women')\">\n            <i class=\"fas fa-female\"></i>\n            <span>Women's Fashion</span>\n          </div>\n          <div class=\"quick-link\" (click)=\"navigateToCategory('men')\">\n            <i class=\"fas fa-male\"></i>\n            <span>Men's Fashion</span>\n          </div>\n          <div class=\"quick-link\" (click)=\"navigateToCategory('kids')\">\n            <i class=\"fas fa-child\"></i>\n            <span>Kids' Fashion</span>\n          </div>\n          <div class=\"quick-link\" (click)=\"navigateToCategory('ethnic')\">\n            <i class=\"fas fa-star-and-crescent\"></i>\n            <span>Ethnic Wear</span>\n          </div>\n          <div class=\"quick-link\" (click)=\"navigateToCategory('accessories')\">\n            <i class=\"fas fa-gem\"></i>\n            <span>Accessories</span>\n          </div>\n          <div class=\"quick-link\" (click)=\"navigateToCategory('shoes')\">\n            <i class=\"fas fa-shoe-prints\"></i>\n            <span>Footwear</span>\n          </div>\n        </div>\n      </section>\n    </div>\n  `,\n  styles: [`\n    .shop-container {\n      max-width: 1400px;\n      margin: 0 auto;\n      padding: 0 20px;\n    }\n\n    /* Hero Banner */\n    .hero-banner {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      color: white;\n      padding: 60px 40px;\n      border-radius: 20px;\n      margin: 20px 0 40px;\n      position: relative;\n      overflow: hidden;\n    }\n\n    .hero-banner::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"1\" fill=\"white\" opacity=\"0.1\"/><circle cx=\"75\" cy=\"75\" r=\"1\" fill=\"white\" opacity=\"0.1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');\n      opacity: 0.3;\n    }\n\n    .hero-content {\n      position: relative;\n      z-index: 2;\n      text-align: center;\n      max-width: 600px;\n      margin: 0 auto;\n    }\n\n    .hero-content h1 {\n      font-size: 3rem;\n      font-weight: 700;\n      margin-bottom: 16px;\n      line-height: 1.2;\n    }\n\n    .hero-content p {\n      font-size: 1.2rem;\n      margin-bottom: 32px;\n      opacity: 0.9;\n    }\n\n    .hero-search {\n      display: flex;\n      max-width: 500px;\n      margin: 0 auto 40px;\n      background: white;\n      border-radius: 50px;\n      overflow: hidden;\n      box-shadow: 0 10px 30px rgba(0,0,0,0.2);\n    }\n\n    .hero-search input {\n      flex: 1;\n      padding: 16px 24px;\n      border: none;\n      font-size: 1rem;\n      color: #333;\n    }\n\n    .hero-search input::placeholder {\n      color: #999;\n    }\n\n    .hero-search button {\n      padding: 16px 24px;\n      background: #007bff;\n      border: none;\n      color: white;\n      cursor: pointer;\n      transition: background 0.2s;\n    }\n\n    .hero-search button:hover {\n      background: #0056b3;\n    }\n\n    .hero-stats {\n      display: flex;\n      justify-content: center;\n      gap: 60px;\n      position: relative;\n      z-index: 2;\n    }\n\n    .stat {\n      text-align: center;\n    }\n\n    .stat-number {\n      display: block;\n      font-size: 2rem;\n      font-weight: 700;\n      margin-bottom: 4px;\n    }\n\n    .stat-label {\n      font-size: 0.9rem;\n      opacity: 0.8;\n    }\n\n    /* Section Headers */\n    .section-header {\n      text-align: center;\n      margin-bottom: 40px;\n      position: relative;\n    }\n\n    .section-header h2 {\n      font-size: 2.5rem;\n      font-weight: 700;\n      margin-bottom: 8px;\n      color: #333;\n    }\n\n    .section-header p {\n      font-size: 1.1rem;\n      color: #666;\n      margin: 0;\n    }\n\n    .view-all-btn {\n      position: absolute;\n      right: 0;\n      top: 50%;\n      transform: translateY(-50%);\n      background: #007bff;\n      color: white;\n      border: none;\n      padding: 10px 20px;\n      border-radius: 25px;\n      font-weight: 500;\n      cursor: pointer;\n      transition: all 0.2s;\n    }\n\n    .view-all-btn:hover {\n      background: #0056b3;\n      transform: translateY(-50%) scale(1.05);\n    }\n\n    /* Categories Section */\n    .categories-section {\n      margin: 60px 0;\n    }\n\n    .categories-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n      gap: 24px;\n    }\n\n    .category-card {\n      background: white;\n      border-radius: 16px;\n      overflow: hidden;\n      box-shadow: 0 4px 20px rgba(0,0,0,0.1);\n      transition: all 0.3s ease;\n      cursor: pointer;\n    }\n\n    .category-card:hover {\n      transform: translateY(-8px);\n      box-shadow: 0 12px 40px rgba(0,0,0,0.15);\n    }\n\n    .category-image {\n      position: relative;\n      height: 200px;\n      overflow: hidden;\n    }\n\n    .category-image img {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n      transition: transform 0.3s ease;\n    }\n\n    .category-card:hover .category-image img {\n      transform: scale(1.1);\n    }\n\n    .category-overlay {\n      position: absolute;\n      bottom: 0;\n      left: 0;\n      right: 0;\n      background: linear-gradient(transparent, rgba(0,0,0,0.7));\n      padding: 20px;\n      color: white;\n    }\n\n    .product-count {\n      font-size: 0.9rem;\n      font-weight: 500;\n    }\n\n    .category-info {\n      padding: 20px;\n    }\n\n    .category-info h3 {\n      font-size: 1.3rem;\n      font-weight: 600;\n      margin-bottom: 8px;\n      color: #333;\n    }\n\n    .subcategories {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 8px;\n    }\n\n    .subcategories span {\n      background: #f8f9fa;\n      color: #666;\n      padding: 4px 8px;\n      border-radius: 12px;\n      font-size: 0.8rem;\n    }\n\n    .subcategories .more {\n      background: #007bff;\n      color: white;\n    }\n\n    /* Brands Section */\n    .brands-section {\n      margin: 60px 0;\n      background: #f8f9fa;\n      padding: 40px;\n      border-radius: 20px;\n    }\n\n    .brands-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n      gap: 24px;\n    }\n\n    .brand-card {\n      background: white;\n      border-radius: 12px;\n      padding: 20px;\n      text-align: center;\n      transition: all 0.3s ease;\n      cursor: pointer;\n      position: relative;\n    }\n\n    .brand-card:hover {\n      transform: translateY(-4px);\n      box-shadow: 0 8px 24px rgba(0,0,0,0.1);\n    }\n\n    .brand-logo {\n      width: 80px;\n      height: 80px;\n      margin: 0 auto 12px;\n      border-radius: 50%;\n      overflow: hidden;\n      background: #f8f9fa;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .brand-logo img {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n    }\n\n    .brand-card h4 {\n      font-size: 1rem;\n      font-weight: 600;\n      color: #333;\n      margin: 0;\n    }\n\n    .popular-badge {\n      position: absolute;\n      top: 8px;\n      right: 8px;\n      background: #ff6b6b;\n      color: white;\n      padding: 2px 8px;\n      border-radius: 10px;\n      font-size: 0.7rem;\n      font-weight: 500;\n    }\n\n    /* Products Grid */\n    .trending-section,\n    .new-arrivals-section {\n      margin: 60px 0;\n    }\n\n    .products-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n      gap: 24px;\n    }\n\n    .product-card {\n      background: white;\n      border-radius: 12px;\n      overflow: hidden;\n      box-shadow: 0 2px 12px rgba(0,0,0,0.08);\n      transition: all 0.3s ease;\n      cursor: pointer;\n    }\n\n    .product-card:hover {\n      transform: translateY(-4px);\n      box-shadow: 0 8px 24px rgba(0,0,0,0.15);\n    }\n\n    .product-image {\n      position: relative;\n      height: 280px;\n      overflow: hidden;\n    }\n\n    .product-image img {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n      transition: transform 0.3s ease;\n    }\n\n    .product-card:hover .product-image img {\n      transform: scale(1.05);\n    }\n\n    .product-badges {\n      position: absolute;\n      top: 12px;\n      left: 12px;\n      display: flex;\n      flex-direction: column;\n      gap: 4px;\n    }\n\n    .badge {\n      padding: 4px 8px;\n      border-radius: 12px;\n      font-size: 0.7rem;\n      font-weight: 600;\n      text-transform: uppercase;\n    }\n\n    .badge.trending {\n      background: #ff6b6b;\n      color: white;\n    }\n\n    .badge.new {\n      background: #4ecdc4;\n      color: white;\n    }\n\n    .badge.discount {\n      background: #ffa726;\n      color: white;\n    }\n\n    .product-actions {\n      position: absolute;\n      top: 12px;\n      right: 12px;\n      display: flex;\n      flex-direction: column;\n      gap: 8px;\n      opacity: 0;\n      transition: opacity 0.3s ease;\n    }\n\n    .product-card:hover .product-actions {\n      opacity: 1;\n    }\n\n    .btn-wishlist,\n    .btn-quick-add {\n      width: 36px;\n      height: 36px;\n      border: none;\n      border-radius: 50%;\n      background: rgba(255,255,255,0.9);\n      color: #333;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      transition: all 0.2s ease;\n    }\n\n    .btn-wishlist:hover {\n      background: #ff6b6b;\n      color: white;\n    }\n\n    .btn-quick-add:hover {\n      background: #007bff;\n      color: white;\n    }\n\n    .product-info {\n      padding: 16px;\n    }\n\n    .product-name {\n      font-size: 1rem;\n      font-weight: 600;\n      margin-bottom: 4px;\n      color: #333;\n      line-height: 1.3;\n    }\n\n    .product-brand {\n      color: #666;\n      font-size: 0.85rem;\n      margin-bottom: 8px;\n    }\n\n    .product-rating {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      margin-bottom: 8px;\n    }\n\n    .stars {\n      display: flex;\n      gap: 1px;\n    }\n\n    .stars i {\n      font-size: 0.7rem;\n      color: #ffc107;\n    }\n\n    .rating-text {\n      font-size: 0.75rem;\n      color: #666;\n    }\n\n    .product-price {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .current-price {\n      font-size: 1.1rem;\n      font-weight: 700;\n      color: #333;\n    }\n\n    .original-price {\n      font-size: 0.85rem;\n      color: #999;\n      text-decoration: line-through;\n    }\n\n    /* Quick Links */\n    .quick-links-section {\n      margin: 60px 0;\n      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n      padding: 40px;\n      border-radius: 20px;\n      color: white;\n    }\n\n    .quick-links-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 20px;\n    }\n\n    .quick-link {\n      background: rgba(255,255,255,0.1);\n      backdrop-filter: blur(10px);\n      border-radius: 12px;\n      padding: 24px;\n      text-align: center;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      border: 1px solid rgba(255,255,255,0.2);\n    }\n\n    .quick-link:hover {\n      background: rgba(255,255,255,0.2);\n      transform: translateY(-4px);\n    }\n\n    .quick-link i {\n      font-size: 2rem;\n      margin-bottom: 12px;\n      display: block;\n    }\n\n    .quick-link span {\n      font-weight: 500;\n    }\n\n    /* Responsive Design */\n    @media (max-width: 768px) {\n      .hero-content h1 {\n        font-size: 2rem;\n      }\n\n      .hero-stats {\n        gap: 30px;\n      }\n\n      .section-header h2 {\n        font-size: 2rem;\n      }\n\n      .view-all-btn {\n        position: static;\n        transform: none;\n        margin-top: 16px;\n      }\n\n      .categories-grid,\n      .products-grid {\n        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n        gap: 16px;\n      }\n\n      .brands-grid {\n        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n      }\n\n      .quick-links-grid {\n        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n      }\n    }\n  `]\n})], ShopComponent);\nexport { ShopComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "FormsModule", "ShopComponent", "constructor", "router", "productService", "searchQuery", "categories", "featuredB<PERSON>s", "trendingProducts", "newArrivals", "ngOnInit", "loadCategories", "loadFeaturedBrands", "loadTrendingProducts", "loadNewArrivals", "getCategories", "subscribe", "next", "response", "data", "error", "console", "getFeaturedBrands", "getTrendingProducts", "getNewArrivals", "search", "trim", "navigate", "queryParams", "q", "navigateToCategory", "categoryId", "navigate<PERSON><PERSON>Brand", "brandId", "viewProduct", "product", "_id", "viewAllTrending", "viewAllNew", "addToWishlist", "event", "stopPropagation", "isAuthenticated", "showLoginPrompt", "log", "showSuccessMessage", "name", "quickAddToCart", "getProductImage", "images", "url", "getDiscountPercentage", "originalPrice", "price", "Math", "round", "getStars", "rating", "Array", "floor", "fill", "getEmptyStars", "localStorage", "getItem", "action", "message", "confirm", "alert", "__decorate", "selector", "standalone", "imports", "template", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\shop\\shop.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\n\ninterface Category {\n  id: string;\n  name: string;\n  image: string;\n  subcategories: string[];\n  productCount: number;\n}\n\ninterface Brand {\n  id: string;\n  name: string;\n  logo: string;\n  isPopular: boolean;\n}\n\ninterface Product {\n  _id: string;\n  name: string;\n  price: number;\n  originalPrice?: number;\n  images: { url: string; alt: string }[];\n  brand: string;\n  rating: { average: number; count: number };\n  category: string;\n  subcategory: string;\n  tags: string[];\n  isNew: boolean;\n  isTrending: boolean;\n}\n\n@Component({\n  selector: 'app-shop',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <div class=\"shop-container\">\n      <!-- Hero Banner -->\n      <div class=\"hero-banner\">\n        <div class=\"hero-content\">\n          <h1>Discover Fashion That Defines You</h1>\n          <p>Explore thousands of products from top brands</p>\n          <div class=\"hero-search\">\n            <input type=\"text\" placeholder=\"Search for products, brands, categories...\" [(ngModel)]=\"searchQuery\">\n            <button (click)=\"search()\">\n              <i class=\"fas fa-search\"></i>\n            </button>\n          </div>\n        </div>\n        <div class=\"hero-stats\">\n          <div class=\"stat\">\n            <span class=\"stat-number\">10K+</span>\n            <span class=\"stat-label\">Products</span>\n          </div>\n          <div class=\"stat\">\n            <span class=\"stat-number\">500+</span>\n            <span class=\"stat-label\">Brands</span>\n          </div>\n          <div class=\"stat\">\n            <span class=\"stat-number\">50K+</span>\n            <span class=\"stat-label\">Happy Customers</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Categories Section -->\n      <section class=\"categories-section\">\n        <div class=\"section-header\">\n          <h2>Shop by Category</h2>\n          <p>Find exactly what you're looking for</p>\n        </div>\n\n        <div class=\"categories-grid\">\n          <div class=\"category-card\" *ngFor=\"let category of categories\" (click)=\"navigateToCategory(category.id)\">\n            <div class=\"category-image\">\n              <img [src]=\"category.image\" [alt]=\"category.name\" loading=\"lazy\">\n              <div class=\"category-overlay\">\n                <span class=\"product-count\">{{ category.productCount }}+ Products</span>\n              </div>\n            </div>\n            <div class=\"category-info\">\n              <h3>{{ category.name }}</h3>\n              <div class=\"subcategories\">\n                <span *ngFor=\"let sub of category.subcategories.slice(0, 3)\">{{ sub }}</span>\n                <span *ngIf=\"category.subcategories.length > 3\" class=\"more\">+{{ category.subcategories.length - 3 }} more</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Featured Brands -->\n      <section class=\"brands-section\">\n        <div class=\"section-header\">\n          <h2>Featured Brands</h2>\n          <p>Shop from your favorite brands</p>\n        </div>\n\n        <div class=\"brands-grid\">\n          <div class=\"brand-card\" *ngFor=\"let brand of featuredBrands\" (click)=\"navigateToBrand(brand.id)\">\n            <div class=\"brand-logo\">\n              <img [src]=\"brand.logo\" [alt]=\"brand.name\" loading=\"lazy\">\n            </div>\n            <h4>{{ brand.name }}</h4>\n            <span class=\"popular-badge\" *ngIf=\"brand.isPopular\">Popular</span>\n          </div>\n        </div>\n      </section>\n\n      <!-- Trending Products -->\n      <section class=\"trending-section\">\n        <div class=\"section-header\">\n          <h2>Trending Now</h2>\n          <p>What everyone's buying</p>\n          <button class=\"view-all-btn\" (click)=\"viewAllTrending()\">View All</button>\n        </div>\n\n        <div class=\"products-grid\">\n          <div class=\"product-card\" *ngFor=\"let product of trendingProducts\" (click)=\"viewProduct(product)\">\n            <div class=\"product-image\">\n              <img [src]=\"getProductImage(product)\" [alt]=\"product.name\" loading=\"lazy\">\n              <div class=\"product-badges\">\n                <span class=\"badge trending\" *ngIf=\"product.isTrending\">Trending</span>\n                <span class=\"badge new\" *ngIf=\"product.isNew\">New</span>\n                <span class=\"badge discount\" *ngIf=\"product.originalPrice && product.originalPrice > product.price\">\n                  {{ getDiscountPercentage(product) }}% OFF\n                </span>\n              </div>\n              <div class=\"product-actions\">\n                <button class=\"btn-wishlist\" (click)=\"addToWishlist(product, $event)\">\n                  <i class=\"far fa-heart\"></i>\n                </button>\n                <button class=\"btn-quick-add\" (click)=\"quickAddToCart(product, $event)\">\n                  <i class=\"fas fa-shopping-cart\"></i>\n                </button>\n              </div>\n            </div>\n\n            <div class=\"product-info\">\n              <h4 class=\"product-name\">{{ product.name }}</h4>\n              <p class=\"product-brand\">{{ product.brand }}</p>\n\n              <div class=\"product-rating\" *ngIf=\"product.rating\">\n                <div class=\"stars\">\n                  <i class=\"fas fa-star\" *ngFor=\"let star of getStars(product.rating.average)\"></i>\n                  <i class=\"far fa-star\" *ngFor=\"let star of getEmptyStars(product.rating.average)\"></i>\n                </div>\n                <span class=\"rating-text\">{{ product.rating.average }} ({{ product.rating.count }})</span>\n              </div>\n\n              <div class=\"product-price\">\n                <span class=\"current-price\">₹{{ product.price | number:'1.0-0' }}</span>\n                <span class=\"original-price\" *ngIf=\"product.originalPrice && product.originalPrice > product.price\">\n                  ₹{{ product.originalPrice | number:'1.0-0' }}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- New Arrivals -->\n      <section class=\"new-arrivals-section\">\n        <div class=\"section-header\">\n          <h2>New Arrivals</h2>\n          <p>Fresh styles just dropped</p>\n          <button class=\"view-all-btn\" (click)=\"viewAllNew()\">View All</button>\n        </div>\n\n        <div class=\"products-grid\">\n          <div class=\"product-card\" *ngFor=\"let product of newArrivals\" (click)=\"viewProduct(product)\">\n            <div class=\"product-image\">\n              <img [src]=\"getProductImage(product)\" [alt]=\"product.name\" loading=\"lazy\">\n              <div class=\"product-badges\">\n                <span class=\"badge new\">New</span>\n                <span class=\"badge discount\" *ngIf=\"product.originalPrice && product.originalPrice > product.price\">\n                  {{ getDiscountPercentage(product) }}% OFF\n                </span>\n              </div>\n              <div class=\"product-actions\">\n                <button class=\"btn-wishlist\" (click)=\"addToWishlist(product, $event)\">\n                  <i class=\"far fa-heart\"></i>\n                </button>\n                <button class=\"btn-quick-add\" (click)=\"quickAddToCart(product, $event)\">\n                  <i class=\"fas fa-shopping-cart\"></i>\n                </button>\n              </div>\n            </div>\n\n            <div class=\"product-info\">\n              <h4 class=\"product-name\">{{ product.name }}</h4>\n              <p class=\"product-brand\">{{ product.brand }}</p>\n\n              <div class=\"product-rating\" *ngIf=\"product.rating\">\n                <div class=\"stars\">\n                  <i class=\"fas fa-star\" *ngFor=\"let star of getStars(product.rating.average)\"></i>\n                  <i class=\"far fa-star\" *ngFor=\"let star of getEmptyStars(product.rating.average)\"></i>\n                </div>\n                <span class=\"rating-text\">{{ product.rating.average }} ({{ product.rating.count }})</span>\n              </div>\n\n              <div class=\"product-price\">\n                <span class=\"current-price\">₹{{ product.price | number:'1.0-0' }}</span>\n                <span class=\"original-price\" *ngIf=\"product.originalPrice && product.originalPrice > product.price\">\n                  ₹{{ product.originalPrice | number:'1.0-0' }}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Quick Links -->\n      <section class=\"quick-links-section\">\n        <div class=\"section-header\">\n          <h2>Quick Links</h2>\n        </div>\n\n        <div class=\"quick-links-grid\">\n          <div class=\"quick-link\" (click)=\"navigateToCategory('women')\">\n            <i class=\"fas fa-female\"></i>\n            <span>Women's Fashion</span>\n          </div>\n          <div class=\"quick-link\" (click)=\"navigateToCategory('men')\">\n            <i class=\"fas fa-male\"></i>\n            <span>Men's Fashion</span>\n          </div>\n          <div class=\"quick-link\" (click)=\"navigateToCategory('kids')\">\n            <i class=\"fas fa-child\"></i>\n            <span>Kids' Fashion</span>\n          </div>\n          <div class=\"quick-link\" (click)=\"navigateToCategory('ethnic')\">\n            <i class=\"fas fa-star-and-crescent\"></i>\n            <span>Ethnic Wear</span>\n          </div>\n          <div class=\"quick-link\" (click)=\"navigateToCategory('accessories')\">\n            <i class=\"fas fa-gem\"></i>\n            <span>Accessories</span>\n          </div>\n          <div class=\"quick-link\" (click)=\"navigateToCategory('shoes')\">\n            <i class=\"fas fa-shoe-prints\"></i>\n            <span>Footwear</span>\n          </div>\n        </div>\n      </section>\n    </div>\n  `,\n  styles: [`\n    .shop-container {\n      max-width: 1400px;\n      margin: 0 auto;\n      padding: 0 20px;\n    }\n\n    /* Hero Banner */\n    .hero-banner {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      color: white;\n      padding: 60px 40px;\n      border-radius: 20px;\n      margin: 20px 0 40px;\n      position: relative;\n      overflow: hidden;\n    }\n\n    .hero-banner::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"1\" fill=\"white\" opacity=\"0.1\"/><circle cx=\"75\" cy=\"75\" r=\"1\" fill=\"white\" opacity=\"0.1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');\n      opacity: 0.3;\n    }\n\n    .hero-content {\n      position: relative;\n      z-index: 2;\n      text-align: center;\n      max-width: 600px;\n      margin: 0 auto;\n    }\n\n    .hero-content h1 {\n      font-size: 3rem;\n      font-weight: 700;\n      margin-bottom: 16px;\n      line-height: 1.2;\n    }\n\n    .hero-content p {\n      font-size: 1.2rem;\n      margin-bottom: 32px;\n      opacity: 0.9;\n    }\n\n    .hero-search {\n      display: flex;\n      max-width: 500px;\n      margin: 0 auto 40px;\n      background: white;\n      border-radius: 50px;\n      overflow: hidden;\n      box-shadow: 0 10px 30px rgba(0,0,0,0.2);\n    }\n\n    .hero-search input {\n      flex: 1;\n      padding: 16px 24px;\n      border: none;\n      font-size: 1rem;\n      color: #333;\n    }\n\n    .hero-search input::placeholder {\n      color: #999;\n    }\n\n    .hero-search button {\n      padding: 16px 24px;\n      background: #007bff;\n      border: none;\n      color: white;\n      cursor: pointer;\n      transition: background 0.2s;\n    }\n\n    .hero-search button:hover {\n      background: #0056b3;\n    }\n\n    .hero-stats {\n      display: flex;\n      justify-content: center;\n      gap: 60px;\n      position: relative;\n      z-index: 2;\n    }\n\n    .stat {\n      text-align: center;\n    }\n\n    .stat-number {\n      display: block;\n      font-size: 2rem;\n      font-weight: 700;\n      margin-bottom: 4px;\n    }\n\n    .stat-label {\n      font-size: 0.9rem;\n      opacity: 0.8;\n    }\n\n    /* Section Headers */\n    .section-header {\n      text-align: center;\n      margin-bottom: 40px;\n      position: relative;\n    }\n\n    .section-header h2 {\n      font-size: 2.5rem;\n      font-weight: 700;\n      margin-bottom: 8px;\n      color: #333;\n    }\n\n    .section-header p {\n      font-size: 1.1rem;\n      color: #666;\n      margin: 0;\n    }\n\n    .view-all-btn {\n      position: absolute;\n      right: 0;\n      top: 50%;\n      transform: translateY(-50%);\n      background: #007bff;\n      color: white;\n      border: none;\n      padding: 10px 20px;\n      border-radius: 25px;\n      font-weight: 500;\n      cursor: pointer;\n      transition: all 0.2s;\n    }\n\n    .view-all-btn:hover {\n      background: #0056b3;\n      transform: translateY(-50%) scale(1.05);\n    }\n\n    /* Categories Section */\n    .categories-section {\n      margin: 60px 0;\n    }\n\n    .categories-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n      gap: 24px;\n    }\n\n    .category-card {\n      background: white;\n      border-radius: 16px;\n      overflow: hidden;\n      box-shadow: 0 4px 20px rgba(0,0,0,0.1);\n      transition: all 0.3s ease;\n      cursor: pointer;\n    }\n\n    .category-card:hover {\n      transform: translateY(-8px);\n      box-shadow: 0 12px 40px rgba(0,0,0,0.15);\n    }\n\n    .category-image {\n      position: relative;\n      height: 200px;\n      overflow: hidden;\n    }\n\n    .category-image img {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n      transition: transform 0.3s ease;\n    }\n\n    .category-card:hover .category-image img {\n      transform: scale(1.1);\n    }\n\n    .category-overlay {\n      position: absolute;\n      bottom: 0;\n      left: 0;\n      right: 0;\n      background: linear-gradient(transparent, rgba(0,0,0,0.7));\n      padding: 20px;\n      color: white;\n    }\n\n    .product-count {\n      font-size: 0.9rem;\n      font-weight: 500;\n    }\n\n    .category-info {\n      padding: 20px;\n    }\n\n    .category-info h3 {\n      font-size: 1.3rem;\n      font-weight: 600;\n      margin-bottom: 8px;\n      color: #333;\n    }\n\n    .subcategories {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 8px;\n    }\n\n    .subcategories span {\n      background: #f8f9fa;\n      color: #666;\n      padding: 4px 8px;\n      border-radius: 12px;\n      font-size: 0.8rem;\n    }\n\n    .subcategories .more {\n      background: #007bff;\n      color: white;\n    }\n\n    /* Brands Section */\n    .brands-section {\n      margin: 60px 0;\n      background: #f8f9fa;\n      padding: 40px;\n      border-radius: 20px;\n    }\n\n    .brands-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n      gap: 24px;\n    }\n\n    .brand-card {\n      background: white;\n      border-radius: 12px;\n      padding: 20px;\n      text-align: center;\n      transition: all 0.3s ease;\n      cursor: pointer;\n      position: relative;\n    }\n\n    .brand-card:hover {\n      transform: translateY(-4px);\n      box-shadow: 0 8px 24px rgba(0,0,0,0.1);\n    }\n\n    .brand-logo {\n      width: 80px;\n      height: 80px;\n      margin: 0 auto 12px;\n      border-radius: 50%;\n      overflow: hidden;\n      background: #f8f9fa;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .brand-logo img {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n    }\n\n    .brand-card h4 {\n      font-size: 1rem;\n      font-weight: 600;\n      color: #333;\n      margin: 0;\n    }\n\n    .popular-badge {\n      position: absolute;\n      top: 8px;\n      right: 8px;\n      background: #ff6b6b;\n      color: white;\n      padding: 2px 8px;\n      border-radius: 10px;\n      font-size: 0.7rem;\n      font-weight: 500;\n    }\n\n    /* Products Grid */\n    .trending-section,\n    .new-arrivals-section {\n      margin: 60px 0;\n    }\n\n    .products-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n      gap: 24px;\n    }\n\n    .product-card {\n      background: white;\n      border-radius: 12px;\n      overflow: hidden;\n      box-shadow: 0 2px 12px rgba(0,0,0,0.08);\n      transition: all 0.3s ease;\n      cursor: pointer;\n    }\n\n    .product-card:hover {\n      transform: translateY(-4px);\n      box-shadow: 0 8px 24px rgba(0,0,0,0.15);\n    }\n\n    .product-image {\n      position: relative;\n      height: 280px;\n      overflow: hidden;\n    }\n\n    .product-image img {\n      width: 100%;\n      height: 100%;\n      object-fit: cover;\n      transition: transform 0.3s ease;\n    }\n\n    .product-card:hover .product-image img {\n      transform: scale(1.05);\n    }\n\n    .product-badges {\n      position: absolute;\n      top: 12px;\n      left: 12px;\n      display: flex;\n      flex-direction: column;\n      gap: 4px;\n    }\n\n    .badge {\n      padding: 4px 8px;\n      border-radius: 12px;\n      font-size: 0.7rem;\n      font-weight: 600;\n      text-transform: uppercase;\n    }\n\n    .badge.trending {\n      background: #ff6b6b;\n      color: white;\n    }\n\n    .badge.new {\n      background: #4ecdc4;\n      color: white;\n    }\n\n    .badge.discount {\n      background: #ffa726;\n      color: white;\n    }\n\n    .product-actions {\n      position: absolute;\n      top: 12px;\n      right: 12px;\n      display: flex;\n      flex-direction: column;\n      gap: 8px;\n      opacity: 0;\n      transition: opacity 0.3s ease;\n    }\n\n    .product-card:hover .product-actions {\n      opacity: 1;\n    }\n\n    .btn-wishlist,\n    .btn-quick-add {\n      width: 36px;\n      height: 36px;\n      border: none;\n      border-radius: 50%;\n      background: rgba(255,255,255,0.9);\n      color: #333;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      transition: all 0.2s ease;\n    }\n\n    .btn-wishlist:hover {\n      background: #ff6b6b;\n      color: white;\n    }\n\n    .btn-quick-add:hover {\n      background: #007bff;\n      color: white;\n    }\n\n    .product-info {\n      padding: 16px;\n    }\n\n    .product-name {\n      font-size: 1rem;\n      font-weight: 600;\n      margin-bottom: 4px;\n      color: #333;\n      line-height: 1.3;\n    }\n\n    .product-brand {\n      color: #666;\n      font-size: 0.85rem;\n      margin-bottom: 8px;\n    }\n\n    .product-rating {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      margin-bottom: 8px;\n    }\n\n    .stars {\n      display: flex;\n      gap: 1px;\n    }\n\n    .stars i {\n      font-size: 0.7rem;\n      color: #ffc107;\n    }\n\n    .rating-text {\n      font-size: 0.75rem;\n      color: #666;\n    }\n\n    .product-price {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .current-price {\n      font-size: 1.1rem;\n      font-weight: 700;\n      color: #333;\n    }\n\n    .original-price {\n      font-size: 0.85rem;\n      color: #999;\n      text-decoration: line-through;\n    }\n\n    /* Quick Links */\n    .quick-links-section {\n      margin: 60px 0;\n      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n      padding: 40px;\n      border-radius: 20px;\n      color: white;\n    }\n\n    .quick-links-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 20px;\n    }\n\n    .quick-link {\n      background: rgba(255,255,255,0.1);\n      backdrop-filter: blur(10px);\n      border-radius: 12px;\n      padding: 24px;\n      text-align: center;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      border: 1px solid rgba(255,255,255,0.2);\n    }\n\n    .quick-link:hover {\n      background: rgba(255,255,255,0.2);\n      transform: translateY(-4px);\n    }\n\n    .quick-link i {\n      font-size: 2rem;\n      margin-bottom: 12px;\n      display: block;\n    }\n\n    .quick-link span {\n      font-weight: 500;\n    }\n\n    /* Responsive Design */\n    @media (max-width: 768px) {\n      .hero-content h1 {\n        font-size: 2rem;\n      }\n\n      .hero-stats {\n        gap: 30px;\n      }\n\n      .section-header h2 {\n        font-size: 2rem;\n      }\n\n      .view-all-btn {\n        position: static;\n        transform: none;\n        margin-top: 16px;\n      }\n\n      .categories-grid,\n      .products-grid {\n        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n        gap: 16px;\n      }\n\n      .brands-grid {\n        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n      }\n\n      .quick-links-grid {\n        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n      }\n    }\n  `]\n})\nexport class ShopComponent implements OnInit {\n  searchQuery = '';\n  categories: Category[] = [];\n  featuredBrands: Brand[] = [];\n  trendingProducts: Product[] = [];\n  newArrivals: Product[] = [];\n\n  constructor(\n    private router: Router,\n    private productService: ProductService\n  ) {}\n\n  ngOnInit() {\n    this.loadCategories();\n    this.loadFeaturedBrands();\n    this.loadTrendingProducts();\n    this.loadNewArrivals();\n  }\n\n  loadCategories() {\n    // Load categories from real API\n    this.productService.getCategories().subscribe({\n      next: (response) => {\n        this.categories = response?.data || [];\n      },\n      error: (error) => {\n        console.error('Error loading categories:', error);\n        this.categories = [];\n      }\n    });\n  }\n\n  loadFeaturedBrands() {\n    // Load from real API\n    this.productService.getFeaturedBrands().subscribe({\n      next: (response) => {\n        this.featuredBrands = response?.data || [];\n      },\n      error: (error) => {\n        console.error('Error loading featured brands:', error);\n        this.featuredBrands = [];\n      }\n    });\n  }\n\n  loadTrendingProducts() {\n    // Load from real API\n    this.productService.getTrendingProducts().subscribe({\n      next: (response) => {\n        this.trendingProducts = response?.data || [];\n      },\n      error: (error) => {\n        console.error('Error loading trending products:', error);\n        this.trendingProducts = [];\n      }\n    });\n  }\n\n  loadNewArrivals() {\n    // Load from real API\n    this.productService.getNewArrivals().subscribe({\n      next: (response) => {\n        this.newArrivals = response?.data || [];\n      },\n      error: (error) => {\n        console.error('Error loading new arrivals:', error);\n        this.newArrivals = [];\n      }\n    });\n  }\n\n  search() {\n    if (this.searchQuery.trim()) {\n      this.router.navigate(['/search'], { queryParams: { q: this.searchQuery } });\n    }\n  }\n\n  navigateToCategory(categoryId: string) {\n    this.router.navigate(['/category', categoryId]);\n  }\n\n  navigateToBrand(brandId: string) {\n    this.router.navigate(['/brand', brandId]);\n  }\n\n  viewProduct(product: Product) {\n    this.router.navigate(['/product', product._id]);\n  }\n\n\n\n  viewAllTrending() {\n    this.router.navigate(['/category/trending']);\n  }\n\n  viewAllNew() {\n    this.router.navigate(['/category/new-arrivals']);\n  }\n\n  addToWishlist(product: Product, event: Event) {\n    event.stopPropagation();\n    // TODO: Check authentication first\n    if (!this.isAuthenticated()) {\n      this.showLoginPrompt('add to wishlist');\n      return;\n    }\n    // TODO: Implement wishlist API call\n    console.log('Add to wishlist:', product);\n    this.showSuccessMessage(`${product.name} added to wishlist!`);\n  }\n\n  quickAddToCart(product: Product, event: Event) {\n    event.stopPropagation();\n    // TODO: Check authentication first\n    if (!this.isAuthenticated()) {\n      this.showLoginPrompt('add to cart');\n      return;\n    }\n    // TODO: Implement add to cart API call\n    console.log('Add to cart:', product);\n    this.showSuccessMessage(`${product.name} added to cart!`);\n  }\n\n  getProductImage(product: Product): string {\n    return product.images[0]?.url || '/assets/images/placeholder.jpg';\n  }\n\n  getDiscountPercentage(product: Product): number {\n    if (!product.originalPrice || product.originalPrice <= product.price) return 0;\n    return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);\n  }\n\n  getStars(rating: number): number[] {\n    return Array(Math.floor(rating)).fill(0);\n  }\n\n  getEmptyStars(rating: number): number[] {\n    return Array(5 - Math.floor(rating)).fill(0);\n  }\n\n  private isAuthenticated(): boolean {\n    // TODO: Implement actual authentication check\n    return localStorage.getItem('authToken') !== null;\n  }\n\n  private showLoginPrompt(action: string) {\n    const message = `Please login to ${action}`;\n    if (confirm(`${message}. Would you like to login now?`)) {\n      this.router.navigate(['/auth/login']);\n    }\n  }\n\n  private showSuccessMessage(message: string) {\n    // TODO: Implement proper toast/notification system\n    alert(message);\n  }\n}"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,QAAQ,gBAAgB;AAgyBrC,IAAMC,aAAa,GAAnB,MAAMA,aAAa;EAOxBC,YACUC,MAAc,EACdC,cAA8B;IAD9B,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IARxB,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAC,UAAU,GAAe,EAAE;IAC3B,KAAAC,cAAc,GAAY,EAAE;IAC5B,KAAAC,gBAAgB,GAAc,EAAE;IAChC,KAAAC,WAAW,GAAc,EAAE;EAKxB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAH,cAAcA,CAAA;IACZ;IACA,IAAI,CAACP,cAAc,CAACW,aAAa,EAAE,CAACC,SAAS,CAAC;MAC5CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACZ,UAAU,GAAGY,QAAQ,EAAEC,IAAI,IAAI,EAAE;MACxC,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAACd,UAAU,GAAG,EAAE;MACtB;KACD,CAAC;EACJ;EAEAM,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACR,cAAc,CAACkB,iBAAiB,EAAE,CAACN,SAAS,CAAC;MAChDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACX,cAAc,GAAGW,QAAQ,EAAEC,IAAI,IAAI,EAAE;MAC5C,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAACb,cAAc,GAAG,EAAE;MAC1B;KACD,CAAC;EACJ;EAEAM,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAACT,cAAc,CAACmB,mBAAmB,EAAE,CAACP,SAAS,CAAC;MAClDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACV,gBAAgB,GAAGU,QAAQ,EAAEC,IAAI,IAAI,EAAE;MAC9C,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI,CAACZ,gBAAgB,GAAG,EAAE;MAC5B;KACD,CAAC;EACJ;EAEAM,eAAeA,CAAA;IACb;IACA,IAAI,CAACV,cAAc,CAACoB,cAAc,EAAE,CAACR,SAAS,CAAC;MAC7CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACT,WAAW,GAAGS,QAAQ,EAAEC,IAAI,IAAI,EAAE;MACzC,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAI,CAACX,WAAW,GAAG,EAAE;MACvB;KACD,CAAC;EACJ;EAEAgB,MAAMA,CAAA;IACJ,IAAI,IAAI,CAACpB,WAAW,CAACqB,IAAI,EAAE,EAAE;MAC3B,IAAI,CAACvB,MAAM,CAACwB,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE;QAAEC,WAAW,EAAE;UAAEC,CAAC,EAAE,IAAI,CAACxB;QAAW;MAAE,CAAE,CAAC;;EAE/E;EAEAyB,kBAAkBA,CAACC,UAAkB;IACnC,IAAI,CAAC5B,MAAM,CAACwB,QAAQ,CAAC,CAAC,WAAW,EAAEI,UAAU,CAAC,CAAC;EACjD;EAEAC,eAAeA,CAACC,OAAe;IAC7B,IAAI,CAAC9B,MAAM,CAACwB,QAAQ,CAAC,CAAC,QAAQ,EAAEM,OAAO,CAAC,CAAC;EAC3C;EAEAC,WAAWA,CAACC,OAAgB;IAC1B,IAAI,CAAChC,MAAM,CAACwB,QAAQ,CAAC,CAAC,UAAU,EAAEQ,OAAO,CAACC,GAAG,CAAC,CAAC;EACjD;EAIAC,eAAeA,CAAA;IACb,IAAI,CAAClC,MAAM,CAACwB,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CAAC;EAC9C;EAEAW,UAAUA,CAAA;IACR,IAAI,CAACnC,MAAM,CAACwB,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;EAClD;EAEAY,aAAaA,CAACJ,OAAgB,EAAEK,KAAY;IAC1CA,KAAK,CAACC,eAAe,EAAE;IACvB;IACA,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE,EAAE;MAC3B,IAAI,CAACC,eAAe,CAAC,iBAAiB,CAAC;MACvC;;IAEF;IACAtB,OAAO,CAACuB,GAAG,CAAC,kBAAkB,EAAET,OAAO,CAAC;IACxC,IAAI,CAACU,kBAAkB,CAAC,GAAGV,OAAO,CAACW,IAAI,qBAAqB,CAAC;EAC/D;EAEAC,cAAcA,CAACZ,OAAgB,EAAEK,KAAY;IAC3CA,KAAK,CAACC,eAAe,EAAE;IACvB;IACA,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE,EAAE;MAC3B,IAAI,CAACC,eAAe,CAAC,aAAa,CAAC;MACnC;;IAEF;IACAtB,OAAO,CAACuB,GAAG,CAAC,cAAc,EAAET,OAAO,CAAC;IACpC,IAAI,CAACU,kBAAkB,CAAC,GAAGV,OAAO,CAACW,IAAI,iBAAiB,CAAC;EAC3D;EAEAE,eAAeA,CAACb,OAAgB;IAC9B,OAAOA,OAAO,CAACc,MAAM,CAAC,CAAC,CAAC,EAAEC,GAAG,IAAI,gCAAgC;EACnE;EAEAC,qBAAqBA,CAAChB,OAAgB;IACpC,IAAI,CAACA,OAAO,CAACiB,aAAa,IAAIjB,OAAO,CAACiB,aAAa,IAAIjB,OAAO,CAACkB,KAAK,EAAE,OAAO,CAAC;IAC9E,OAAOC,IAAI,CAACC,KAAK,CAAE,CAACpB,OAAO,CAACiB,aAAa,GAAGjB,OAAO,CAACkB,KAAK,IAAIlB,OAAO,CAACiB,aAAa,GAAI,GAAG,CAAC;EAC5F;EAEAI,QAAQA,CAACC,MAAc;IACrB,OAAOC,KAAK,CAACJ,IAAI,CAACK,KAAK,CAACF,MAAM,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC;EAC1C;EAEAC,aAAaA,CAACJ,MAAc;IAC1B,OAAOC,KAAK,CAAC,CAAC,GAAGJ,IAAI,CAACK,KAAK,CAACF,MAAM,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC;EAC9C;EAEQlB,eAAeA,CAAA;IACrB;IACA,OAAOoB,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,KAAK,IAAI;EACnD;EAEQpB,eAAeA,CAACqB,MAAc;IACpC,MAAMC,OAAO,GAAG,mBAAmBD,MAAM,EAAE;IAC3C,IAAIE,OAAO,CAAC,GAAGD,OAAO,gCAAgC,CAAC,EAAE;MACvD,IAAI,CAAC9D,MAAM,CAACwB,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;;EAEzC;EAEQkB,kBAAkBA,CAACoB,OAAe;IACxC;IACAE,KAAK,CAACF,OAAO,CAAC;EAChB;CACD;AA5JYhE,aAAa,GAAAmE,UAAA,EAhwBzBtE,SAAS,CAAC;EACTuE,QAAQ,EAAE,UAAU;EACpBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACxE,YAAY,EAAEC,WAAW,CAAC;EACpCwE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmNT;EACDC,MAAM,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAsiBR;CACF,CAAC,C,EACWxE,aAAa,CA4JzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}