{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class NotificationService {\n  constructor() {\n    this.notificationsSubject = new BehaviorSubject([]);\n    this.notifications$ = this.notificationsSubject.asObservable();\n  }\n  show(notification) {\n    const id = Date.now().toString();\n    const newNotification = {\n      ...notification,\n      id,\n      duration: notification.duration || 5000\n    };\n    const currentNotifications = this.notificationsSubject.value;\n    this.notificationsSubject.next([...currentNotifications, newNotification]);\n    // Auto remove after duration\n    if (newNotification.duration && newNotification.duration > 0) {\n      setTimeout(() => {\n        this.remove(id);\n      }, newNotification.duration);\n    }\n  }\n  success(title, message, duration) {\n    this.show({\n      type: 'success',\n      title,\n      message,\n      duration\n    });\n  }\n  error(title, message, duration) {\n    this.show({\n      type: 'error',\n      title,\n      message,\n      duration\n    });\n  }\n  info(title, message, duration) {\n    this.show({\n      type: 'info',\n      title,\n      message,\n      duration\n    });\n  }\n  warning(title, message, duration) {\n    this.show({\n      type: 'warning',\n      title,\n      message,\n      duration\n    });\n  }\n  remove(id) {\n    const currentNotifications = this.notificationsSubject.value;\n    const filteredNotifications = currentNotifications.filter(n => n.id !== id);\n    this.notificationsSubject.next(filteredNotifications);\n  }\n  clear() {\n    this.notificationsSubject.next([]);\n  }\n  static {\n    this.ɵfac = function NotificationService_Factory(t) {\n      return new (t || NotificationService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: NotificationService,\n      factory: NotificationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "NotificationService", "constructor", "notificationsSubject", "notifications$", "asObservable", "show", "notification", "id", "Date", "now", "toString", "newNotification", "duration", "currentNotifications", "value", "next", "setTimeout", "remove", "success", "title", "message", "type", "error", "info", "warning", "filteredNotifications", "filter", "n", "clear", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\core\\services\\notification.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\n\nexport interface Notification {\n  id: string;\n  type: 'success' | 'error' | 'info' | 'warning';\n  title: string;\n  message: string;\n  duration?: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class NotificationService {\n  private notificationsSubject = new BehaviorSubject<Notification[]>([]);\n  public notifications$ = this.notificationsSubject.asObservable();\n\n  constructor() {}\n\n  show(notification: Omit<Notification, 'id'>): void {\n    const id = Date.now().toString();\n    const newNotification: Notification = {\n      ...notification,\n      id,\n      duration: notification.duration || 5000\n    };\n\n    const currentNotifications = this.notificationsSubject.value;\n    this.notificationsSubject.next([...currentNotifications, newNotification]);\n\n    // Auto remove after duration\n    if (newNotification.duration && newNotification.duration > 0) {\n      setTimeout(() => {\n        this.remove(id);\n      }, newNotification.duration);\n    }\n  }\n\n  success(title: string, message: string, duration?: number): void {\n    this.show({ type: 'success', title, message, duration });\n  }\n\n  error(title: string, message: string, duration?: number): void {\n    this.show({ type: 'error', title, message, duration });\n  }\n\n  info(title: string, message: string, duration?: number): void {\n    this.show({ type: 'info', title, message, duration });\n  }\n\n  warning(title: string, message: string, duration?: number): void {\n    this.show({ type: 'warning', title, message, duration });\n  }\n\n  remove(id: string): void {\n    const currentNotifications = this.notificationsSubject.value;\n    const filteredNotifications = currentNotifications.filter(n => n.id !== id);\n    this.notificationsSubject.next(filteredNotifications);\n  }\n\n  clear(): void {\n    this.notificationsSubject.next([]);\n  }\n}\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,MAAM;;AAatC,OAAM,MAAOC,mBAAmB;EAI9BC,YAAA;IAHQ,KAAAC,oBAAoB,GAAG,IAAIH,eAAe,CAAiB,EAAE,CAAC;IAC/D,KAAAI,cAAc,GAAG,IAAI,CAACD,oBAAoB,CAACE,YAAY,EAAE;EAEjD;EAEfC,IAAIA,CAACC,YAAsC;IACzC,MAAMC,EAAE,GAAGC,IAAI,CAACC,GAAG,EAAE,CAACC,QAAQ,EAAE;IAChC,MAAMC,eAAe,GAAiB;MACpC,GAAGL,YAAY;MACfC,EAAE;MACFK,QAAQ,EAAEN,YAAY,CAACM,QAAQ,IAAI;KACpC;IAED,MAAMC,oBAAoB,GAAG,IAAI,CAACX,oBAAoB,CAACY,KAAK;IAC5D,IAAI,CAACZ,oBAAoB,CAACa,IAAI,CAAC,CAAC,GAAGF,oBAAoB,EAAEF,eAAe,CAAC,CAAC;IAE1E;IACA,IAAIA,eAAe,CAACC,QAAQ,IAAID,eAAe,CAACC,QAAQ,GAAG,CAAC,EAAE;MAC5DI,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,MAAM,CAACV,EAAE,CAAC;MACjB,CAAC,EAAEI,eAAe,CAACC,QAAQ,CAAC;;EAEhC;EAEAM,OAAOA,CAACC,KAAa,EAAEC,OAAe,EAAER,QAAiB;IACvD,IAAI,CAACP,IAAI,CAAC;MAAEgB,IAAI,EAAE,SAAS;MAAEF,KAAK;MAAEC,OAAO;MAAER;IAAQ,CAAE,CAAC;EAC1D;EAEAU,KAAKA,CAACH,KAAa,EAAEC,OAAe,EAAER,QAAiB;IACrD,IAAI,CAACP,IAAI,CAAC;MAAEgB,IAAI,EAAE,OAAO;MAAEF,KAAK;MAAEC,OAAO;MAAER;IAAQ,CAAE,CAAC;EACxD;EAEAW,IAAIA,CAACJ,KAAa,EAAEC,OAAe,EAAER,QAAiB;IACpD,IAAI,CAACP,IAAI,CAAC;MAAEgB,IAAI,EAAE,MAAM;MAAEF,KAAK;MAAEC,OAAO;MAAER;IAAQ,CAAE,CAAC;EACvD;EAEAY,OAAOA,CAACL,KAAa,EAAEC,OAAe,EAAER,QAAiB;IACvD,IAAI,CAACP,IAAI,CAAC;MAAEgB,IAAI,EAAE,SAAS;MAAEF,KAAK;MAAEC,OAAO;MAAER;IAAQ,CAAE,CAAC;EAC1D;EAEAK,MAAMA,CAACV,EAAU;IACf,MAAMM,oBAAoB,GAAG,IAAI,CAACX,oBAAoB,CAACY,KAAK;IAC5D,MAAMW,qBAAqB,GAAGZ,oBAAoB,CAACa,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACpB,EAAE,KAAKA,EAAE,CAAC;IAC3E,IAAI,CAACL,oBAAoB,CAACa,IAAI,CAACU,qBAAqB,CAAC;EACvD;EAEAG,KAAKA,CAAA;IACH,IAAI,CAAC1B,oBAAoB,CAACa,IAAI,CAAC,EAAE,CAAC;EACpC;;;uBAjDWf,mBAAmB;IAAA;EAAA;;;aAAnBA,mBAAmB;MAAA6B,OAAA,EAAnB7B,mBAAmB,CAAA8B,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}