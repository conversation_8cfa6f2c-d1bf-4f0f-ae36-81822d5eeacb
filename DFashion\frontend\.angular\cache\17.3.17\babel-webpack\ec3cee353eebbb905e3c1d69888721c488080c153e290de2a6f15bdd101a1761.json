{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/notification.service\";\nimport * as i2 from \"@angular/common\";\nfunction NotificationComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementStart(4, \"strong\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function NotificationComponent_div_1_Template_button_click_6_listener() {\n      const notification_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.close(notification_r2.id));\n    });\n    i0.ɵɵtext(7, \" \\u00D7 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"p\", 6);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const notification_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(\"notification-\" + notification_r2.type);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r2.getIcon(notification_r2.type));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(notification_r2.title);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(notification_r2.message);\n  }\n}\nexport class NotificationComponent {\n  constructor(notificationService) {\n    this.notificationService = notificationService;\n    this.notifications = [];\n  }\n  ngOnInit() {\n    this.notificationService.notifications$.subscribe(notifications => {\n      this.notifications = notifications;\n    });\n  }\n  getIcon(type) {\n    switch (type) {\n      case 'success':\n        return 'fas fa-check-circle';\n      case 'error':\n        return 'fas fa-exclamation-circle';\n      case 'warning':\n        return 'fas fa-exclamation-triangle';\n      case 'info':\n      default:\n        return 'fas fa-info-circle';\n    }\n  }\n  close(id) {\n    this.notificationService.remove(id);\n  }\n  static {\n    this.ɵfac = function NotificationComponent_Factory(t) {\n      return new (t || NotificationComponent)(i0.ɵɵdirectiveInject(i1.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NotificationComponent,\n      selectors: [[\"app-notification\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 1,\n      consts: [[1, \"notifications-container\"], [\"class\", \"notification\", 3, \"class\", 4, \"ngFor\", \"ngForOf\"], [1, \"notification\"], [1, \"notification-content\"], [1, \"notification-header\"], [\"aria-label\", \"Close notification\", 1, \"close-btn\", 3, \"click\"], [1, \"notification-message\"]],\n      template: function NotificationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, NotificationComponent_div_1_Template, 10, 6, \"div\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.notifications);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf],\n      styles: [\".notifications-container[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 80px;\\n  right: 20px;\\n  z-index: 10000;\\n  max-width: 400px;\\n}\\n\\n.notification[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  margin-bottom: 12px;\\n  overflow: hidden;\\n  animation: _ngcontent-%COMP%_slideIn 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  from {\\n    transform: translateX(100%);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateX(0);\\n    opacity: 1;\\n  }\\n}\\n.notification-success[_ngcontent-%COMP%] {\\n  border-left: 4px solid #10b981;\\n}\\n\\n.notification-error[_ngcontent-%COMP%] {\\n  border-left: 4px solid #ef4444;\\n}\\n\\n.notification-info[_ngcontent-%COMP%] {\\n  border-left: 4px solid #3b82f6;\\n}\\n\\n.notification-warning[_ngcontent-%COMP%] {\\n  border-left: 4px solid #f59e0b;\\n}\\n\\n.notification-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n\\n.notification-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 8px;\\n}\\n\\n.notification-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.notification-success[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #10b981;\\n}\\n\\n.notification-error[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n}\\n\\n.notification-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #3b82f6;\\n}\\n\\n.notification-warning[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #f59e0b;\\n}\\n\\n.notification-header[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  flex: 1;\\n  font-size: 14px;\\n  font-weight: 600;\\n}\\n\\n.close-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  font-size: 18px;\\n  cursor: pointer;\\n  color: #6b7280;\\n  padding: 0;\\n  width: 20px;\\n  height: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.close-btn[_ngcontent-%COMP%]:hover {\\n  color: #374151;\\n}\\n\\n.notification-message[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  color: #6b7280;\\n  margin: 0;\\n  line-height: 1.4;\\n}\\n\\n@media (max-width: 768px) {\\n  .notifications-container[_ngcontent-%COMP%] {\\n    left: 20px;\\n    right: 20px;\\n    max-width: none;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "NotificationComponent_div_1_Template_button_click_6_listener", "notification_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "close", "id", "ɵɵclassMap", "type", "ɵɵadvance", "getIcon", "ɵɵtextInterpolate", "title", "message", "NotificationComponent", "constructor", "notificationService", "notifications", "ngOnInit", "notifications$", "subscribe", "remove", "ɵɵdirectiveInject", "i1", "NotificationService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "NotificationComponent_Template", "rf", "ctx", "ɵɵtemplate", "NotificationComponent_div_1_Template", "ɵɵproperty", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\shared\\components\\notification\\notification.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\shared\\components\\notification\\notification.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NotificationService, Notification } from '../../../core/services/notification.service';\n\n@Component({\n  selector: 'app-notification',\n  standalone: true,\n  imports: [CommonModule],\n  templateUrl: './notification.component.html',\n  styleUrls: ['./notification.component.scss']\n})\nexport class NotificationComponent implements OnInit {\n  notifications: Notification[] = [];\n\n  constructor(private notificationService: NotificationService) {}\n\n  ngOnInit() {\n    this.notificationService.notifications$.subscribe(notifications => {\n      this.notifications = notifications;\n    });\n  }\n\n  getIcon(type: string): string {\n    switch (type) {\n      case 'success':\n        return 'fas fa-check-circle';\n      case 'error':\n        return 'fas fa-exclamation-circle';\n      case 'warning':\n        return 'fas fa-exclamation-triangle';\n      case 'info':\n      default:\n        return 'fas fa-info-circle';\n    }\n  }\n\n  close(id: string) {\n    this.notificationService.remove(id);\n  }\n}\n", "<div class=\"notifications-container\">\n  <div\n    *ngFor=\"let notification of notifications\"\n    class=\"notification\"\n    [class]=\"'notification-' + notification.type\"\n  >\n    <div class=\"notification-content\">\n      <div class=\"notification-header\">\n        <i [class]=\"getIcon(notification.type)\"></i>\n        <strong>{{ notification.title }}</strong>\n        <button \n          class=\"close-btn\" \n          (click)=\"close(notification.id)\"\n          aria-label=\"Close notification\"\n        >\n          &times;\n        </button>\n      </div>\n      <p class=\"notification-message\">{{ notification.message }}</p>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;;;;;ICMxCC,EANJ,CAAAC,cAAA,aAIC,aACmC,aACC;IAC/BD,EAAA,CAAAE,SAAA,QAA4C;IAC5CF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAG,MAAA,GAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACzCJ,EAAA,CAAAC,cAAA,gBAIC;IAFCD,EAAA,CAAAK,UAAA,mBAAAC,6DAAA;MAAA,MAAAC,eAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,KAAA,CAAAP,eAAA,CAAAQ,EAAA,CAAsB;IAAA,EAAC;IAGhCf,EAAA,CAAAG,MAAA,eACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;IACNJ,EAAA,CAAAC,cAAA,WAAgC;IAAAD,EAAA,CAAAG,MAAA,GAA0B;IAE9DH,EAF8D,CAAAI,YAAA,EAAI,EAC1D,EACF;;;;;IAhBJJ,EAAA,CAAAgB,UAAA,mBAAAT,eAAA,CAAAU,IAAA,CAA6C;IAItCjB,EAAA,CAAAkB,SAAA,GAAoC;IAApClB,EAAA,CAAAgB,UAAA,CAAAL,MAAA,CAAAQ,OAAA,CAAAZ,eAAA,CAAAU,IAAA,EAAoC;IAC/BjB,EAAA,CAAAkB,SAAA,GAAwB;IAAxBlB,EAAA,CAAAoB,iBAAA,CAAAb,eAAA,CAAAc,KAAA,CAAwB;IASFrB,EAAA,CAAAkB,SAAA,GAA0B;IAA1BlB,EAAA,CAAAoB,iBAAA,CAAAb,eAAA,CAAAe,OAAA,CAA0B;;;ADPhE,OAAM,MAAOC,qBAAqB;EAGhCC,YAAoBC,mBAAwC;IAAxC,KAAAA,mBAAmB,GAAnBA,mBAAmB;IAFvC,KAAAC,aAAa,GAAmB,EAAE;EAE6B;EAE/DC,QAAQA,CAAA;IACN,IAAI,CAACF,mBAAmB,CAACG,cAAc,CAACC,SAAS,CAACH,aAAa,IAAG;MAChE,IAAI,CAACA,aAAa,GAAGA,aAAa;IACpC,CAAC,CAAC;EACJ;EAEAP,OAAOA,CAACF,IAAY;IAClB,QAAQA,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,qBAAqB;MAC9B,KAAK,OAAO;QACV,OAAO,2BAA2B;MACpC,KAAK,SAAS;QACZ,OAAO,6BAA6B;MACtC,KAAK,MAAM;MACX;QACE,OAAO,oBAAoB;;EAEjC;EAEAH,KAAKA,CAACC,EAAU;IACd,IAAI,CAACU,mBAAmB,CAACK,MAAM,CAACf,EAAE,CAAC;EACrC;;;uBA3BWQ,qBAAqB,EAAAvB,EAAA,CAAA+B,iBAAA,CAAAC,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAArBV,qBAAqB;MAAAW,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAApC,EAAA,CAAAqC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXlC3C,EAAA,CAAAC,cAAA,aAAqC;UACnCD,EAAA,CAAA6C,UAAA,IAAAC,oCAAA,kBAIC;UAgBH9C,EAAA,CAAAI,YAAA,EAAM;;;UAnBuBJ,EAAA,CAAAkB,SAAA,EAAgB;UAAhBlB,EAAA,CAAA+C,UAAA,YAAAH,GAAA,CAAAlB,aAAA,CAAgB;;;qBDKjC3B,YAAY,EAAAiD,EAAA,CAAAC,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}