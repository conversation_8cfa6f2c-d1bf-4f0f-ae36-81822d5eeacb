<div class="user-dialog">
  <h2 mat-dialog-title>{{ isEditMode ? 'Edit User' : 'Add New User' }}</h2>
  
  <mat-dialog-content>
    <form [formGroup]="userForm" class="user-form">
      <div class="form-row">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Full Name</mat-label>
          <input matInput formControlName="fullName" placeholder="Enter full name">
          <mat-error *ngIf="userForm.get('fullName')?.hasError('required')">
            Full name is required
          </mat-error>
        </mat-form-field>
      </div>

      <div class="form-row">
        <mat-form-field appearance="outline" class="half-width">
          <mat-label>Email</mat-label>
          <input matInput type="email" formControlName="email" placeholder="Enter email">
          <mat-error *ngIf="userForm.get('email')?.hasError('required')">
            Email is required
          </mat-error>
          <mat-error *ngIf="userForm.get('email')?.hasError('email')">
            Please enter a valid email
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="half-width">
          <mat-label>Username</mat-label>
          <input matInput formControlName="username" placeholder="Enter username">
          <mat-error *ngIf="userForm.get('username')?.hasError('required')">
            Username is required
          </mat-error>
        </mat-form-field>
      </div>

      <div class="form-row" *ngIf="!isEditMode">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Password</mat-label>
          <input matInput type="password" formControlName="password" placeholder="Enter password">
          <mat-error *ngIf="userForm.get('password')?.hasError('required')">
            Password is required
          </mat-error>
          <mat-error *ngIf="userForm.get('password')?.hasError('minlength')">
            Password must be at least 6 characters
          </mat-error>
        </mat-form-field>
      </div>

      <div class="form-row">
        <mat-form-field appearance="outline" class="half-width">
          <mat-label>Role</mat-label>
          <mat-select formControlName="role">
            <mat-option *ngFor="let role of roles" [value]="role.value">
              {{ role.label }}
            </mat-option>
          </mat-select>
          <mat-error *ngIf="userForm.get('role')?.hasError('required')">
            Role is required
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="half-width">
          <mat-label>Department</mat-label>
          <mat-select formControlName="department">
            <mat-option *ngFor="let dept of departments" [value]="dept.value">
              {{ dept.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <div class="form-row" *ngIf="showEmployeeFields">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Employee ID</mat-label>
          <input matInput formControlName="employeeId" placeholder="Enter employee ID">
        </mat-form-field>
      </div>

      <div class="form-row">
        <mat-slide-toggle formControlName="isActive" color="primary">
          Active User
        </mat-slide-toggle>
      </div>
    </form>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button (click)="onCancel()">Cancel</button>
    <button mat-raised-button 
            color="primary" 
            (click)="onSave()"
            [disabled]="userForm.invalid || isLoading">
      <mat-spinner *ngIf="isLoading" diameter="20" class="save-spinner"></mat-spinner>
      {{ isEditMode ? 'Update' : 'Create' }}
    </button>
  </mat-dialog-actions>
</div>
