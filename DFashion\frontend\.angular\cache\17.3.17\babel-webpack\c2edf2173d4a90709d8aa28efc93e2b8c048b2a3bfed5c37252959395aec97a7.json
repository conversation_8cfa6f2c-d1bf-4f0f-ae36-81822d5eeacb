{"ast": null, "code": "import { map, take } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport let AuthGuard = /*#__PURE__*/(() => {\n  class AuthGuard {\n    constructor(authService, router) {\n      this.authService = authService;\n      this.router = router;\n    }\n    canActivate() {\n      return this.authService.isAuthenticated$.pipe(take(1), map(isAuthenticated => {\n        if (isAuthenticated) {\n          return true;\n        } else {\n          return this.router.createUrlTree(['/auth/login']);\n        }\n      }));\n    }\n    static {\n      this.ɵfac = function AuthGuard_Factory(t) {\n        return new (t || AuthGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthGuard,\n        factory: AuthGuard.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AuthGuard;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}