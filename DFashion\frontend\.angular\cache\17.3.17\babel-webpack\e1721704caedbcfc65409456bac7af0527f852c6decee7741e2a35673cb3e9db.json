{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ViewAddStoriesComponent } from '../../components/view-add-stories/view-add-stories.component';\nimport { FeedComponent } from '../../components/feed/feed.component';\nimport { SidebarComponent } from '../../components/sidebar/sidebar.component';\nimport * as i0 from \"@angular/core\";\nexport let HomeComponent = /*#__PURE__*/(() => {\n  class HomeComponent {\n    constructor() {}\n    ngOnInit() {\n      // Home component initialization\n    }\n    static {\n      this.ɵfac = function HomeComponent_Factory(t) {\n        return new (t || HomeComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: HomeComponent,\n        selectors: [[\"app-home\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 6,\n        vars: 0,\n        consts: [[1, \"home-container\"], [1, \"content-grid\"], [1, \"main-content\"]],\n        template: function HomeComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n            i0.ɵɵelement(3, \"app-view-add-stories\")(4, \"app-feed\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(5, \"app-sidebar\");\n            i0.ɵɵelementEnd()();\n          }\n        },\n        dependencies: [CommonModule, ViewAddStoriesComponent, FeedComponent, SidebarComponent],\n        styles: [\".home-container[_ngcontent-%COMP%]{padding:20px 0;min-height:calc(100vh - 60px)}.content-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 400px;gap:40px;max-width:1000px;margin:0 auto;padding:0 20px}.main-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:24px}@media (max-width: 1024px){.content-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;max-width:600px}}@media (max-width: 768px){.home-container[_ngcontent-%COMP%]{padding:16px 0}.content-grid[_ngcontent-%COMP%]{padding:0 16px;gap:20px}.main-content[_ngcontent-%COMP%]{gap:20px}}\"]\n      });\n    }\n  }\n  return HomeComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}