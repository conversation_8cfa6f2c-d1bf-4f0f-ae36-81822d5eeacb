.search-section {
  padding: 16px;
  background: var(--ion-color-light);
}

.categories-section {
  padding: 16px;
  
  h3 {
    margin: 0 0 16px 0;
    color: var(--ion-color-dark);
    font-weight: 600;
  }
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 8px;
  border-radius: 12px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  
  &.selected {
    transform: scale(1.05);
    box-shadow: 0 4px 16px rgba(var(--ion-color-primary-rgb), 0.3);
    border: 2px solid var(--ion-color-primary);
  }
  
  .category-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    
    ion-icon {
      font-size: 24px;
      color: white;
    }
  }
  
  span {
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    color: var(--ion-color-dark);
  }
}

.filters-section {
  padding: 16px;
  background: white;
  border-bottom: 1px solid var(--ion-color-light);
}

.price-filter {
  padding: 16px;
  background: white;
  border-bottom: 1px solid var(--ion-color-light);
}

.brand-filters {
  padding: 16px;
  background: white;
  border-bottom: 1px solid var(--ion-color-light);
  
  h4 {
    margin: 0 0 12px 0;
    color: var(--ion-color-dark);
    font-weight: 600;
  }
}

.brand-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  
  ion-spinner {
    margin-bottom: 16px;
  }
  
  p {
    color: var(--ion-color-medium);
    margin: 0;
  }
}

.products-section {
  padding: 16px;
  
  .section-header {
    margin-bottom: 16px;
    
    h3 {
      margin: 0;
      color: var(--ion-color-dark);
      font-weight: 600;
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  
  ion-icon {
    font-size: 64px;
    margin-bottom: 16px;
  }
  
  h3 {
    margin: 0 0 8px 0;
    color: var(--ion-color-dark);
  }
  
  p {
    margin: 0 0 24px 0;
    color: var(--ion-color-medium);
  }
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.product-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  cursor: pointer;
  
  &:active {
    transform: scale(0.98);
  }
  
  .product-image {
    position: relative;
    width: 100%;
    height: 180px;
    overflow: hidden;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .product-badge {
      position: absolute;
      top: 8px;
      left: 8px;
      background: var(--ion-color-danger);
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 10px;
      font-weight: 600;
    }
    
    .product-actions {
      position: absolute;
      top: 8px;
      right: 8px;
      display: flex;
      flex-direction: column;
      gap: 4px;
      
      ion-button {
        --background: rgba(255, 255, 255, 0.9);
        --color: var(--ion-color-dark);
        --border-radius: 50%;
        width: 32px;
        height: 32px;
        
        ion-icon {
          font-size: 16px;
        }
      }
    }
  }
  
  .product-info {
    padding: 12px;
    
    h4 {
      margin: 0 0 4px 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--ion-color-dark);
      line-height: 1.3;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    
    .brand {
      margin: 0 0 8px 0;
      font-size: 12px;
      color: var(--ion-color-medium);
    }
    
    .product-price {
      margin-bottom: 8px;
      
      .current-price {
        font-size: 16px;
        font-weight: 600;
        color: var(--ion-color-primary);
      }
      
      .original-price {
        font-size: 12px;
        color: var(--ion-color-medium);
        text-decoration: line-through;
        margin-left: 8px;
      }
    }
    
    .product-rating {
      display: flex;
      align-items: center;
      gap: 4px;
      
      ion-icon {
        font-size: 12px;
        color: var(--ion-color-light);
        
        &.filled {
          color: var(--ion-color-warning);
        }
      }
      
      span {
        font-size: 10px;
        color: var(--ion-color-medium);
      }
    }
  }
}

.bottom-spacing {
  height: 80px;
}
