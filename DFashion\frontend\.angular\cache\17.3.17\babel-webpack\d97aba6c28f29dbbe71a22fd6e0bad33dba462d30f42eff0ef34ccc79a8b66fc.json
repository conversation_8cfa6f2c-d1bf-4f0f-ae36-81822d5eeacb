{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { forwardRef, InjectionToken, EventEmitter, booleanAttribute, Directive, Output, ContentChildren, Input, numberAttribute, ANIMATION_MODULE_TYPE, ElementRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, Attribute, ViewChild, NgModule } from '@angular/core';\nimport { MatRipple, _MatInternalFormField, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport * as i2 from '@angular/cdk/collections';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\n\n// Increasing integer for generating unique ids for radio components.\nconst _c0 = [\"input\"];\nconst _c1 = [\"formField\"];\nconst _c2 = [\"*\"];\nlet nextUniqueId = 0;\n/** Change event object emitted by radio button and radio group. */\nclass MatRadioChange {\n  constructor(/** The radio button that emits the change event. */\n  source, /** The value of the radio button. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\n/**\n * Provider Expression that allows mat-radio-group to register as a ControlValueAccessor. This\n * allows it to support [(ngModel)] and ngControl.\n * @docs-private\n */\nconst MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatRadioGroup),\n  multi: true\n};\n/**\n * Injection token that can be used to inject instances of `MatRadioGroup`. It serves as\n * alternative token to the actual `MatRadioGroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_RADIO_GROUP = new InjectionToken('MatRadioGroup');\nconst MAT_RADIO_DEFAULT_OPTIONS = new InjectionToken('mat-radio-default-options', {\n  providedIn: 'root',\n  factory: MAT_RADIO_DEFAULT_OPTIONS_FACTORY\n});\nfunction MAT_RADIO_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    color: 'accent'\n  };\n}\n/**\n * A group of radio buttons. May contain one or more `<mat-radio-button>` elements.\n */\nclass MatRadioGroup {\n  /** Name of the radio button group. All radio buttons inside this group will use this name. */\n  get name() {\n    return this._name;\n  }\n  set name(value) {\n    this._name = value;\n    this._updateRadioButtonNames();\n  }\n  /** Whether the labels should appear after or before the radio-buttons. Defaults to 'after' */\n  get labelPosition() {\n    return this._labelPosition;\n  }\n  set labelPosition(v) {\n    this._labelPosition = v === 'before' ? 'before' : 'after';\n    this._markRadiosForCheck();\n  }\n  /**\n   * Value for the radio-group. Should equal the value of the selected radio button if there is\n   * a corresponding radio button with a matching value. If there is not such a corresponding\n   * radio button, this value persists to be applied in case a new radio button is added with a\n   * matching value.\n   */\n  get value() {\n    return this._value;\n  }\n  set value(newValue) {\n    if (this._value !== newValue) {\n      // Set this before proceeding to ensure no circular loop occurs with selection.\n      this._value = newValue;\n      this._updateSelectedRadioFromValue();\n      this._checkSelectedRadioButton();\n    }\n  }\n  _checkSelectedRadioButton() {\n    if (this._selected && !this._selected.checked) {\n      this._selected.checked = true;\n    }\n  }\n  /**\n   * The currently selected radio button. If set to a new radio button, the radio group value\n   * will be updated to match the new selected button.\n   */\n  get selected() {\n    return this._selected;\n  }\n  set selected(selected) {\n    this._selected = selected;\n    this.value = selected ? selected.value : null;\n    this._checkSelectedRadioButton();\n  }\n  /** Whether the radio group is disabled */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._markRadiosForCheck();\n  }\n  /** Whether the radio group is required */\n  get required() {\n    return this._required;\n  }\n  set required(value) {\n    this._required = value;\n    this._markRadiosForCheck();\n  }\n  constructor(_changeDetector) {\n    this._changeDetector = _changeDetector;\n    /** Selected value for the radio group. */\n    this._value = null;\n    /** The HTML name attribute applied to radio buttons in this group. */\n    this._name = `mat-radio-group-${nextUniqueId++}`;\n    /** The currently selected radio button. Should match value. */\n    this._selected = null;\n    /** Whether the `value` has been set to its initial value. */\n    this._isInitialized = false;\n    /** Whether the labels should appear after or before the radio-buttons. Defaults to 'after' */\n    this._labelPosition = 'after';\n    /** Whether the radio group is disabled. */\n    this._disabled = false;\n    /** Whether the radio group is required. */\n    this._required = false;\n    /** The method to be called in order to update ngModel */\n    this._controlValueAccessorChangeFn = () => {};\n    /**\n     * onTouch function registered via registerOnTouch (ControlValueAccessor).\n     * @docs-private\n     */\n    this.onTouched = () => {};\n    /**\n     * Event emitted when the group value changes.\n     * Change events are only emitted when the value changes due to user interaction with\n     * a radio button (the same behavior as `<input type-\"radio\">`).\n     */\n    this.change = new EventEmitter();\n  }\n  /**\n   * Initialize properties once content children are available.\n   * This allows us to propagate relevant attributes to associated buttons.\n   */\n  ngAfterContentInit() {\n    // Mark this component as initialized in AfterContentInit because the initial value can\n    // possibly be set by NgModel on MatRadioGroup, and it is possible that the OnInit of the\n    // NgModel occurs *after* the OnInit of the MatRadioGroup.\n    this._isInitialized = true;\n    // Clear the `selected` button when it's destroyed since the tabindex of the rest of the\n    // buttons depends on it. Note that we don't clear the `value`, because the radio button\n    // may be swapped out with a similar one and there are some internal apps that depend on\n    // that behavior.\n    this._buttonChanges = this._radios.changes.subscribe(() => {\n      if (this.selected && !this._radios.find(radio => radio === this.selected)) {\n        this._selected = null;\n      }\n    });\n  }\n  ngOnDestroy() {\n    this._buttonChanges?.unsubscribe();\n  }\n  /**\n   * Mark this group as being \"touched\" (for ngModel). Meant to be called by the contained\n   * radio buttons upon their blur.\n   */\n  _touch() {\n    if (this.onTouched) {\n      this.onTouched();\n    }\n  }\n  _updateRadioButtonNames() {\n    if (this._radios) {\n      this._radios.forEach(radio => {\n        radio.name = this.name;\n        radio._markForCheck();\n      });\n    }\n  }\n  /** Updates the `selected` radio button from the internal _value state. */\n  _updateSelectedRadioFromValue() {\n    // If the value already matches the selected radio, do nothing.\n    const isAlreadySelected = this._selected !== null && this._selected.value === this._value;\n    if (this._radios && !isAlreadySelected) {\n      this._selected = null;\n      this._radios.forEach(radio => {\n        radio.checked = this.value === radio.value;\n        if (radio.checked) {\n          this._selected = radio;\n        }\n      });\n    }\n  }\n  /** Dispatch change event with current selection and group value. */\n  _emitChangeEvent() {\n    if (this._isInitialized) {\n      this.change.emit(new MatRadioChange(this._selected, this._value));\n    }\n  }\n  _markRadiosForCheck() {\n    if (this._radios) {\n      this._radios.forEach(radio => radio._markForCheck());\n    }\n  }\n  /**\n   * Sets the model value. Implemented as part of ControlValueAccessor.\n   * @param value\n   */\n  writeValue(value) {\n    this.value = value;\n    this._changeDetector.markForCheck();\n  }\n  /**\n   * Registers a callback to be triggered when the model value changes.\n   * Implemented as part of ControlValueAccessor.\n   * @param fn Callback to be registered.\n   */\n  registerOnChange(fn) {\n    this._controlValueAccessorChangeFn = fn;\n  }\n  /**\n   * Registers a callback to be triggered when the control is touched.\n   * Implemented as part of ControlValueAccessor.\n   * @param fn Callback to be registered.\n   */\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  /**\n   * Sets the disabled state of the control. Implemented as a part of ControlValueAccessor.\n   * @param isDisabled Whether the control should be disabled.\n   */\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n    this._changeDetector.markForCheck();\n  }\n  static {\n    this.ɵfac = function MatRadioGroup_Factory(t) {\n      return new (t || MatRadioGroup)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatRadioGroup,\n      selectors: [[\"mat-radio-group\"]],\n      contentQueries: function MatRadioGroup_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatRadioButton, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._radios = _t);\n        }\n      },\n      hostAttrs: [\"role\", \"radiogroup\", 1, \"mat-mdc-radio-group\"],\n      inputs: {\n        color: \"color\",\n        name: \"name\",\n        labelPosition: \"labelPosition\",\n        value: \"value\",\n        selected: \"selected\",\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n        required: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"required\", \"required\", booleanAttribute]\n      },\n      outputs: {\n        change: \"change\"\n      },\n      exportAs: [\"matRadioGroup\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR, {\n        provide: MAT_RADIO_GROUP,\n        useExisting: MatRadioGroup\n      }]), i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRadioGroup, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-radio-group',\n      exportAs: 'matRadioGroup',\n      providers: [MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR, {\n        provide: MAT_RADIO_GROUP,\n        useExisting: MatRadioGroup\n      }],\n      host: {\n        'role': 'radiogroup',\n        'class': 'mat-mdc-radio-group'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }], {\n    change: [{\n      type: Output\n    }],\n    _radios: [{\n      type: ContentChildren,\n      args: [forwardRef(() => MatRadioButton), {\n        descendants: true\n      }]\n    }],\n    color: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    labelPosition: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    selected: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    required: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass MatRadioButton {\n  /** Whether this radio button is checked. */\n  get checked() {\n    return this._checked;\n  }\n  set checked(value) {\n    if (this._checked !== value) {\n      this._checked = value;\n      if (value && this.radioGroup && this.radioGroup.value !== this.value) {\n        this.radioGroup.selected = this;\n      } else if (!value && this.radioGroup && this.radioGroup.value === this.value) {\n        // When unchecking the selected radio button, update the selected radio\n        // property on the group.\n        this.radioGroup.selected = null;\n      }\n      if (value) {\n        // Notify all radio buttons with the same name to un-check.\n        this._radioDispatcher.notify(this.id, this.name);\n      }\n      this._changeDetector.markForCheck();\n    }\n  }\n  /** The value of this radio button. */\n  get value() {\n    return this._value;\n  }\n  set value(value) {\n    if (this._value !== value) {\n      this._value = value;\n      if (this.radioGroup !== null) {\n        if (!this.checked) {\n          // Update checked when the value changed to match the radio group's value\n          this.checked = this.radioGroup.value === value;\n        }\n        if (this.checked) {\n          this.radioGroup.selected = this;\n        }\n      }\n    }\n  }\n  /** Whether the label should appear after or before the radio button. Defaults to 'after' */\n  get labelPosition() {\n    return this._labelPosition || this.radioGroup && this.radioGroup.labelPosition || 'after';\n  }\n  set labelPosition(value) {\n    this._labelPosition = value;\n  }\n  /** Whether the radio button is disabled. */\n  get disabled() {\n    return this._disabled || this.radioGroup !== null && this.radioGroup.disabled;\n  }\n  set disabled(value) {\n    this._setDisabled(value);\n  }\n  /** Whether the radio button is required. */\n  get required() {\n    return this._required || this.radioGroup && this.radioGroup.required;\n  }\n  set required(value) {\n    this._required = value;\n  }\n  /** Theme color of the radio button. */\n  get color() {\n    // As per Material design specifications the selection control radio should use the accent color\n    // palette by default. https://material.io/guidelines/components/selection-controls.html\n    return this._color || this.radioGroup && this.radioGroup.color || this._providerOverride && this._providerOverride.color || 'accent';\n  }\n  set color(newValue) {\n    this._color = newValue;\n  }\n  /** ID of the native input element inside `<mat-radio-button>` */\n  get inputId() {\n    return `${this.id || this._uniqueId}-input`;\n  }\n  constructor(radioGroup, _elementRef, _changeDetector, _focusMonitor, _radioDispatcher, animationMode, _providerOverride, tabIndex) {\n    this._elementRef = _elementRef;\n    this._changeDetector = _changeDetector;\n    this._focusMonitor = _focusMonitor;\n    this._radioDispatcher = _radioDispatcher;\n    this._providerOverride = _providerOverride;\n    this._uniqueId = `mat-radio-${++nextUniqueId}`;\n    /** The unique ID for the radio button. */\n    this.id = this._uniqueId;\n    /** Whether ripples are disabled inside the radio button */\n    this.disableRipple = false;\n    /** Tabindex of the radio button. */\n    this.tabIndex = 0;\n    /**\n     * Event emitted when the checked state of this radio button changes.\n     * Change events are only emitted when the value changes due to user interaction with\n     * the radio button (the same behavior as `<input type-\"radio\">`).\n     */\n    this.change = new EventEmitter();\n    /** Whether this radio is checked. */\n    this._checked = false;\n    /** Value assigned to this radio. */\n    this._value = null;\n    /** Unregister function for _radioDispatcher */\n    this._removeUniqueSelectionListener = () => {};\n    // Assertions. Ideally these should be stripped out by the compiler.\n    // TODO(jelbourn): Assert that there's no name binding AND a parent radio group.\n    this.radioGroup = radioGroup;\n    this._noopAnimations = animationMode === 'NoopAnimations';\n    if (tabIndex) {\n      this.tabIndex = numberAttribute(tabIndex, 0);\n    }\n  }\n  /** Focuses the radio button. */\n  focus(options, origin) {\n    if (origin) {\n      this._focusMonitor.focusVia(this._inputElement, origin, options);\n    } else {\n      this._inputElement.nativeElement.focus(options);\n    }\n  }\n  /**\n   * Marks the radio button as needing checking for change detection.\n   * This method is exposed because the parent radio group will directly\n   * update bound properties of the radio button.\n   */\n  _markForCheck() {\n    // When group value changes, the button will not be notified. Use `markForCheck` to explicit\n    // update radio button's status\n    this._changeDetector.markForCheck();\n  }\n  ngOnInit() {\n    if (this.radioGroup) {\n      // If the radio is inside a radio group, determine if it should be checked\n      this.checked = this.radioGroup.value === this._value;\n      if (this.checked) {\n        this.radioGroup.selected = this;\n      }\n      // Copy name from parent radio group\n      this.name = this.radioGroup.name;\n    }\n    this._removeUniqueSelectionListener = this._radioDispatcher.listen((id, name) => {\n      if (id !== this.id && name === this.name) {\n        this.checked = false;\n      }\n    });\n  }\n  ngDoCheck() {\n    this._updateTabIndex();\n  }\n  ngAfterViewInit() {\n    this._updateTabIndex();\n    this._focusMonitor.monitor(this._elementRef, true).subscribe(focusOrigin => {\n      if (!focusOrigin && this.radioGroup) {\n        this.radioGroup._touch();\n      }\n    });\n  }\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    this._removeUniqueSelectionListener();\n  }\n  /** Dispatch change event with current value. */\n  _emitChangeEvent() {\n    this.change.emit(new MatRadioChange(this, this._value));\n  }\n  _isRippleDisabled() {\n    return this.disableRipple || this.disabled;\n  }\n  _onInputClick(event) {\n    // We have to stop propagation for click events on the visual hidden input element.\n    // By default, when a user clicks on a label element, a generated click event will be\n    // dispatched on the associated input element. Since we are using a label element as our\n    // root container, the click event on the `radio-button` will be executed twice.\n    // The real click event will bubble up, and the generated click event also tries to bubble up.\n    // This will lead to multiple click events.\n    // Preventing bubbling for the second event will solve that issue.\n    event.stopPropagation();\n  }\n  /** Triggered when the radio button receives an interaction from the user. */\n  _onInputInteraction(event) {\n    // We always have to stop propagation on the change event.\n    // Otherwise the change event, from the input element, will bubble up and\n    // emit its event object to the `change` output.\n    event.stopPropagation();\n    if (!this.checked && !this.disabled) {\n      const groupValueChanged = this.radioGroup && this.value !== this.radioGroup.value;\n      this.checked = true;\n      this._emitChangeEvent();\n      if (this.radioGroup) {\n        this.radioGroup._controlValueAccessorChangeFn(this.value);\n        if (groupValueChanged) {\n          this.radioGroup._emitChangeEvent();\n        }\n      }\n    }\n  }\n  /** Triggered when the user clicks on the touch target. */\n  _onTouchTargetClick(event) {\n    this._onInputInteraction(event);\n    if (!this.disabled) {\n      // Normally the input should be focused already, but if the click\n      // comes from the touch target, then we might have to focus it ourselves.\n      this._inputElement.nativeElement.focus();\n    }\n  }\n  /** Sets the disabled state and marks for check if a change occurred. */\n  _setDisabled(value) {\n    if (this._disabled !== value) {\n      this._disabled = value;\n      this._changeDetector.markForCheck();\n    }\n  }\n  /** Gets the tabindex for the underlying input element. */\n  _updateTabIndex() {\n    const group = this.radioGroup;\n    let value;\n    // Implement a roving tabindex if the button is inside a group. For most cases this isn't\n    // necessary, because the browser handles the tab order for inputs inside a group automatically,\n    // but we need an explicitly higher tabindex for the selected button in order for things like\n    // the focus trap to pick it up correctly.\n    if (!group || !group.selected || this.disabled) {\n      value = this.tabIndex;\n    } else {\n      value = group.selected === this ? this.tabIndex : -1;\n    }\n    if (value !== this._previousTabIndex) {\n      // We have to set the tabindex directly on the DOM node, because it depends on\n      // the selected state which is prone to \"changed after checked errors\".\n      const input = this._inputElement?.nativeElement;\n      if (input) {\n        input.setAttribute('tabindex', value + '');\n        this._previousTabIndex = value;\n      }\n    }\n  }\n  static {\n    this.ɵfac = function MatRadioButton_Factory(t) {\n      return new (t || MatRadioButton)(i0.ɵɵdirectiveInject(MAT_RADIO_GROUP, 8), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(i2.UniqueSelectionDispatcher), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_RADIO_DEFAULT_OPTIONS, 8), i0.ɵɵinjectAttribute('tabindex'));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatRadioButton,\n      selectors: [[\"mat-radio-button\"]],\n      viewQuery: function MatRadioButton_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 7, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inputElement = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._rippleTrigger = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-radio-button\"],\n      hostVars: 15,\n      hostBindings: function MatRadioButton_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focus\", function MatRadioButton_focus_HostBindingHandler() {\n            return ctx._inputElement.nativeElement.focus();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"id\", ctx.id)(\"tabindex\", null)(\"aria-label\", null)(\"aria-labelledby\", null)(\"aria-describedby\", null);\n          i0.ɵɵclassProp(\"mat-primary\", ctx.color === \"primary\")(\"mat-accent\", ctx.color === \"accent\")(\"mat-warn\", ctx.color === \"warn\")(\"mat-mdc-radio-checked\", ctx.checked)(\"_mat-animation-noopable\", ctx._noopAnimations);\n        }\n      },\n      inputs: {\n        id: \"id\",\n        name: \"name\",\n        ariaLabel: [i0.ɵɵInputFlags.None, \"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [i0.ɵɵInputFlags.None, \"aria-labelledby\", \"ariaLabelledby\"],\n        ariaDescribedby: [i0.ɵɵInputFlags.None, \"aria-describedby\", \"ariaDescribedby\"],\n        disableRipple: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disableRipple\", \"disableRipple\", booleanAttribute],\n        tabIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)],\n        checked: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"checked\", \"checked\", booleanAttribute],\n        value: \"value\",\n        labelPosition: \"labelPosition\",\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n        required: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"required\", \"required\", booleanAttribute],\n        color: \"color\"\n      },\n      outputs: {\n        change: \"change\"\n      },\n      exportAs: [\"matRadioButton\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c2,\n      decls: 13,\n      vars: 16,\n      consts: [[\"formField\", \"\"], [\"input\", \"\"], [\"mat-internal-form-field\", \"\", 3, \"labelPosition\"], [1, \"mdc-radio\"], [1, \"mat-mdc-radio-touch-target\", 3, \"click\"], [\"type\", \"radio\", 1, \"mdc-radio__native-control\", 3, \"change\", \"id\", \"checked\", \"disabled\", \"required\"], [1, \"mdc-radio__background\"], [1, \"mdc-radio__outer-circle\"], [1, \"mdc-radio__inner-circle\"], [\"mat-ripple\", \"\", 1, \"mat-radio-ripple\", \"mat-mdc-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\", \"matRippleCentered\"], [1, \"mat-ripple-element\", \"mat-radio-persistent-ripple\"], [1, \"mdc-label\", 3, \"for\"]],\n      template: function MatRadioButton_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 2, 0)(2, \"div\", 3)(3, \"div\", 4);\n          i0.ɵɵlistener(\"click\", function MatRadioButton_Template_div_click_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._onTouchTargetClick($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"input\", 5, 1);\n          i0.ɵɵlistener(\"change\", function MatRadioButton_Template_input_change_4_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._onInputInteraction($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 6);\n          i0.ɵɵelement(7, \"div\", 7)(8, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 9);\n          i0.ɵɵelement(10, \"div\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"label\", 11);\n          i0.ɵɵprojection(12);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"labelPosition\", ctx.labelPosition);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"mdc-radio--disabled\", ctx.disabled);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"id\", ctx.inputId)(\"checked\", ctx.checked)(\"disabled\", ctx.disabled)(\"required\", ctx.required);\n          i0.ɵɵattribute(\"name\", ctx.name)(\"value\", ctx.value)(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx.ariaLabelledby)(\"aria-describedby\", ctx.ariaDescribedby);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"matRippleTrigger\", ctx._rippleTrigger.nativeElement)(\"matRippleDisabled\", ctx._isRippleDisabled())(\"matRippleCentered\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"for\", ctx.inputId);\n        }\n      },\n      dependencies: [MatRipple, _MatInternalFormField],\n      styles: [\".mdc-radio{display:inline-block;position:relative;flex:0 0 auto;box-sizing:content-box;width:20px;height:20px;cursor:pointer;will-change:opacity,transform,border-color,color}.mdc-radio[hidden]{display:none}.mdc-radio__background{display:inline-block;position:relative;box-sizing:border-box;width:20px;height:20px}.mdc-radio__background::before{position:absolute;transform:scale(0, 0);border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\";transition:opacity 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__outer-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;border-width:2px;border-style:solid;border-radius:50%;transition:border-color 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__inner-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;transform:scale(0, 0);border-width:10px;border-style:solid;border-radius:50%;transition:transform 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1),border-color 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1}.mdc-radio--touch{margin-top:4px;margin-bottom:4px;margin-right:4px;margin-left:4px}.mdc-radio--touch .mdc-radio__native-control{top:calc((40px - 48px) / 2);right:calc((40px - 48px) / 2);left:calc((40px - 48px) / 2);width:48px;height:48px}.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring{border-color:CanvasText}}.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring::after,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring::after,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring::after{border-color:CanvasText}}.mdc-radio__native-control:checked+.mdc-radio__background,.mdc-radio__native-control:disabled+.mdc-radio__background{transition:opacity 120ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__outer-circle{transition:border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle,.mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{transition:transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1),border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio--disabled{cursor:default;pointer-events:none}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle{transform:scale(0.5);transition:transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1),border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:disabled+.mdc-radio__background,[aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background{cursor:default}.mdc-radio__native-control:focus+.mdc-radio__background::before{transform:scale(1);opacity:.12;transition:opacity 120ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-radio-button .mdc-radio{padding:calc((var(--mdc-radio-state-layer-size) - 20px) / 2)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-selected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-disabled-selected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle{opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-unselected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{opacity:var(--mdc-radio-disabled-unselected-icon-opacity)}.mat-mdc-radio-button .mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-focus-icon-color)}.mat-mdc-radio-button .mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-focus-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before{top:calc(-1 * (var(--mdc-radio-state-layer-size) - 20px) / 2);left:calc(-1 * (var(--mdc-radio-state-layer-size) - 20px) / 2);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control{top:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);right:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);left:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:not([disabled]):not(:focus)~.mdc-radio__background::before{opacity:.04;transform:scale(1)}.mat-mdc-radio-button.mat-mdc-radio-checked .mdc-radio__background::before{background-color:var(--mat-radio-checked-ripple-color)}.mat-mdc-radio-button.mat-mdc-radio-checked .mat-ripple-element{background-color:var(--mat-radio-checked-ripple-color)}.mat-mdc-radio-button .mdc-radio--disabled+label{color:var(--mat-radio-disabled-label-color)}.mat-mdc-radio-button .mat-radio-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:50%}.mat-mdc-radio-button .mat-radio-ripple .mat-ripple-element{opacity:.14}.mat-mdc-radio-button .mat-radio-ripple::before{border-radius:50%}.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__background::before,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__outer-circle,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__inner-circle{transition:none !important}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:focus:enabled:not(:checked)~.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-focus-icon-color, black)}.mat-mdc-radio-button.cdk-focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-radio-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-radio-touch-target-display)}[dir=rtl] .mat-mdc-radio-touch-target{left:0;right:50%;transform:translate(50%, -50%)}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRadioButton, [{\n    type: Component,\n    args: [{\n      selector: 'mat-radio-button',\n      host: {\n        'class': 'mat-mdc-radio-button',\n        '[attr.id]': 'id',\n        '[class.mat-primary]': 'color === \"primary\"',\n        '[class.mat-accent]': 'color === \"accent\"',\n        '[class.mat-warn]': 'color === \"warn\"',\n        '[class.mat-mdc-radio-checked]': 'checked',\n        '[class._mat-animation-noopable]': '_noopAnimations',\n        // Needs to be removed since it causes some a11y issues (see #21266).\n        '[attr.tabindex]': 'null',\n        '[attr.aria-label]': 'null',\n        '[attr.aria-labelledby]': 'null',\n        '[attr.aria-describedby]': 'null',\n        // Note: under normal conditions focus shouldn't land on this element, however it may be\n        // programmatically set, for example inside of a focus trap, in this case we want to forward\n        // the focus to the native element.\n        '(focus)': '_inputElement.nativeElement.focus()'\n      },\n      exportAs: 'matRadioButton',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: true,\n      imports: [MatRipple, _MatInternalFormField],\n      template: \"<div mat-internal-form-field [labelPosition]=\\\"labelPosition\\\" #formField>\\n  <div class=\\\"mdc-radio\\\" [class.mdc-radio--disabled]=\\\"disabled\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-radio-touch-target\\\" (click)=\\\"_onTouchTargetClick($event)\\\"></div>\\n    <input #input class=\\\"mdc-radio__native-control\\\" type=\\\"radio\\\"\\n           [id]=\\\"inputId\\\"\\n           [checked]=\\\"checked\\\"\\n           [disabled]=\\\"disabled\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [required]=\\\"required\\\"\\n           [attr.aria-label]=\\\"ariaLabel\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           (change)=\\\"_onInputInteraction($event)\\\">\\n    <div class=\\\"mdc-radio__background\\\">\\n      <div class=\\\"mdc-radio__outer-circle\\\"></div>\\n      <div class=\\\"mdc-radio__inner-circle\\\"></div>\\n    </div>\\n    <div mat-ripple class=\\\"mat-radio-ripple mat-mdc-focus-indicator\\\"\\n         [matRippleTrigger]=\\\"_rippleTrigger.nativeElement\\\"\\n         [matRippleDisabled]=\\\"_isRippleDisabled()\\\"\\n         [matRippleCentered]=\\\"true\\\">\\n      <div class=\\\"mat-ripple-element mat-radio-persistent-ripple\\\"></div>\\n    </div>\\n  </div>\\n  <label class=\\\"mdc-label\\\" [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\",\n      styles: [\".mdc-radio{display:inline-block;position:relative;flex:0 0 auto;box-sizing:content-box;width:20px;height:20px;cursor:pointer;will-change:opacity,transform,border-color,color}.mdc-radio[hidden]{display:none}.mdc-radio__background{display:inline-block;position:relative;box-sizing:border-box;width:20px;height:20px}.mdc-radio__background::before{position:absolute;transform:scale(0, 0);border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\";transition:opacity 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__outer-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;border-width:2px;border-style:solid;border-radius:50%;transition:border-color 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__inner-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;transform:scale(0, 0);border-width:10px;border-style:solid;border-radius:50%;transition:transform 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1),border-color 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1}.mdc-radio--touch{margin-top:4px;margin-bottom:4px;margin-right:4px;margin-left:4px}.mdc-radio--touch .mdc-radio__native-control{top:calc((40px - 48px) / 2);right:calc((40px - 48px) / 2);left:calc((40px - 48px) / 2);width:48px;height:48px}.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring{border-color:CanvasText}}.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring::after,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring::after,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring::after{border-color:CanvasText}}.mdc-radio__native-control:checked+.mdc-radio__background,.mdc-radio__native-control:disabled+.mdc-radio__background{transition:opacity 120ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__outer-circle{transition:border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle,.mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{transition:transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1),border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio--disabled{cursor:default;pointer-events:none}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle{transform:scale(0.5);transition:transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1),border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:disabled+.mdc-radio__background,[aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background{cursor:default}.mdc-radio__native-control:focus+.mdc-radio__background::before{transform:scale(1);opacity:.12;transition:opacity 120ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-radio-button .mdc-radio{padding:calc((var(--mdc-radio-state-layer-size) - 20px) / 2)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-selected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-disabled-selected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle{opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-unselected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{opacity:var(--mdc-radio-disabled-unselected-icon-opacity)}.mat-mdc-radio-button .mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-focus-icon-color)}.mat-mdc-radio-button .mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-focus-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before{top:calc(-1 * (var(--mdc-radio-state-layer-size) - 20px) / 2);left:calc(-1 * (var(--mdc-radio-state-layer-size) - 20px) / 2);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control{top:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);right:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);left:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:not([disabled]):not(:focus)~.mdc-radio__background::before{opacity:.04;transform:scale(1)}.mat-mdc-radio-button.mat-mdc-radio-checked .mdc-radio__background::before{background-color:var(--mat-radio-checked-ripple-color)}.mat-mdc-radio-button.mat-mdc-radio-checked .mat-ripple-element{background-color:var(--mat-radio-checked-ripple-color)}.mat-mdc-radio-button .mdc-radio--disabled+label{color:var(--mat-radio-disabled-label-color)}.mat-mdc-radio-button .mat-radio-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:50%}.mat-mdc-radio-button .mat-radio-ripple .mat-ripple-element{opacity:.14}.mat-mdc-radio-button .mat-radio-ripple::before{border-radius:50%}.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__background::before,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__outer-circle,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__inner-circle{transition:none !important}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:focus:enabled:not(:checked)~.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-focus-icon-color, black)}.mat-mdc-radio-button.cdk-focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-radio-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-radio-touch-target-display)}[dir=rtl] .mat-mdc-radio-touch-target{left:0;right:50%;transform:translate(50%, -50%)}\"]\n    }]\n  }], () => [{\n    type: MatRadioGroup,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_RADIO_GROUP]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.FocusMonitor\n  }, {\n    type: i2.UniqueSelectionDispatcher\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_RADIO_DEFAULT_OPTIONS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['tabindex']\n    }]\n  }], {\n    id: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    ariaDescribedby: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? 0 : numberAttribute(value)\n      }]\n    }],\n    checked: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    value: [{\n      type: Input\n    }],\n    labelPosition: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    required: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    color: [{\n      type: Input\n    }],\n    change: [{\n      type: Output\n    }],\n    _inputElement: [{\n      type: ViewChild,\n      args: ['input']\n    }],\n    _rippleTrigger: [{\n      type: ViewChild,\n      args: ['formField', {\n        read: ElementRef,\n        static: true\n      }]\n    }]\n  });\n})();\nclass MatRadioModule {\n  static {\n    this.ɵfac = function MatRadioModule_Factory(t) {\n      return new (t || MatRadioModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatRadioModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, CommonModule, MatRippleModule, MatRadioButton, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRadioModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, CommonModule, MatRippleModule, MatRadioGroup, MatRadioButton],\n      exports: [MatCommonModule, MatRadioGroup, MatRadioButton]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_RADIO_DEFAULT_OPTIONS, MAT_RADIO_DEFAULT_OPTIONS_FACTORY, MAT_RADIO_GROUP, MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR, MatRadioButton, MatRadioChange, MatRadioGroup, MatRadioModule };", "map": {"version": 3, "names": ["i0", "forwardRef", "InjectionToken", "EventEmitter", "booleanAttribute", "Directive", "Output", "ContentChildren", "Input", "numberAttribute", "ANIMATION_MODULE_TYPE", "ElementRef", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Optional", "Inject", "Attribute", "ViewChild", "NgModule", "<PERSON><PERSON><PERSON><PERSON>", "_MatInternalFormField", "MatCommonModule", "MatRippleModule", "i1", "i2", "NG_VALUE_ACCESSOR", "CommonModule", "_c0", "_c1", "_c2", "nextUniqueId", "MatRadioChange", "constructor", "source", "value", "MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR", "provide", "useExisting", "MatRadioGroup", "multi", "MAT_RADIO_GROUP", "MAT_RADIO_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_RADIO_DEFAULT_OPTIONS_FACTORY", "color", "name", "_name", "_updateRadioButtonNames", "labelPosition", "_labelPosition", "v", "_markRadiosForCheck", "_value", "newValue", "_updateSelectedRadioFromValue", "_checkSelectedRadioButton", "_selected", "checked", "selected", "disabled", "_disabled", "required", "_required", "_changeDetector", "_isInitialized", "_controlValueAccessorChangeFn", "onTouched", "change", "ngAfterContentInit", "_buttonChanges", "_radios", "changes", "subscribe", "find", "radio", "ngOnDestroy", "unsubscribe", "_touch", "for<PERSON>ach", "_mark<PERSON><PERSON><PERSON><PERSON><PERSON>", "isAlreadySelected", "_emitChangeEvent", "emit", "writeValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "ɵfac", "MatRadioGroup_Factory", "t", "ɵɵdirectiveInject", "ChangeDetectorRef", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "contentQueries", "MatRadioGroup_ContentQueries", "rf", "ctx", "dirIndex", "ɵɵcontentQuery", "MatRadioButton", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "hostAttrs", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "outputs", "exportAs", "standalone", "features", "ɵɵProvidersFeature", "ɵɵInputTransformsFeature", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "host", "descendants", "transform", "_checked", "radioGroup", "_radioDispatcher", "notify", "id", "_setDisabled", "_color", "_providerOverride", "inputId", "_uniqueId", "_elementRef", "_focusMonitor", "animationMode", "tabIndex", "disable<PERSON><PERSON><PERSON>", "_removeUniqueSelectionListener", "_noopAnimations", "focus", "options", "origin", "focusVia", "_inputElement", "nativeElement", "ngOnInit", "listen", "ngDoCheck", "_updateTabIndex", "ngAfterViewInit", "monitor", "<PERSON><PERSON><PERSON><PERSON>", "stopMonitoring", "_isRippleDisabled", "_onInputClick", "event", "stopPropagation", "_onInputInteraction", "groupValueChanged", "_onTouchTargetClick", "group", "_previousTabIndex", "input", "setAttribute", "MatRadioButton_Factory", "FocusMonitor", "UniqueSelectionDispatcher", "ɵɵinjectAttribute", "ɵcmp", "ɵɵdefineComponent", "viewQuery", "MatRadioButton_Query", "ɵɵviewQuery", "first", "_rippleTrigger", "hostVars", "hostBindings", "MatRadioButton_HostBindings", "ɵɵlistener", "MatRadioButton_focus_HostBindingHandler", "ɵɵattribute", "ɵɵclassProp", "aria<PERSON><PERSON><PERSON>", "None", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON><PERSON>", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "MatRadioButton_Template", "_r1", "ɵɵgetCurrentView", "ɵɵprojectionDef", "ɵɵelementStart", "MatRadio<PERSON><PERSON><PERSON>_Template_div_click_3_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "ɵɵelementEnd", "MatRadio<PERSON><PERSON>on_Template_input_change_4_listener", "ɵɵelement", "ɵɵprojection", "ɵɵproperty", "ɵɵadvance", "dependencies", "styles", "encapsulation", "changeDetection", "OnPush", "imports", "decorators", "undefined", "read", "static", "MatRadioModule", "MatRadioModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports"], "sources": ["E:/Fashion/DFashion/frontend/node_modules/@angular/material/fesm2022/radio.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { forwardRef, InjectionToken, EventEmitter, booleanAttribute, Directive, Output, ContentChildren, Input, numberAttribute, ANIMATION_MODULE_TYPE, ElementRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, Attribute, ViewChild, NgModule } from '@angular/core';\nimport { MatRipple, _MatInternalFormField, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport * as i2 from '@angular/cdk/collections';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\n\n// Increasing integer for generating unique ids for radio components.\nlet nextUniqueId = 0;\n/** Change event object emitted by radio button and radio group. */\nclass MatRadioChange {\n    constructor(\n    /** The radio button that emits the change event. */\n    source, \n    /** The value of the radio button. */\n    value) {\n        this.source = source;\n        this.value = value;\n    }\n}\n/**\n * Provider Expression that allows mat-radio-group to register as a ControlValueAccessor. This\n * allows it to support [(ngModel)] and ngControl.\n * @docs-private\n */\nconst MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MatRadioGroup),\n    multi: true,\n};\n/**\n * Injection token that can be used to inject instances of `MatRadioGroup`. It serves as\n * alternative token to the actual `MatRadioGroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_RADIO_GROUP = new InjectionToken('MatRadioGroup');\nconst MAT_RADIO_DEFAULT_OPTIONS = new InjectionToken('mat-radio-default-options', {\n    providedIn: 'root',\n    factory: MAT_RADIO_DEFAULT_OPTIONS_FACTORY,\n});\nfunction MAT_RADIO_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        color: 'accent',\n    };\n}\n/**\n * A group of radio buttons. May contain one or more `<mat-radio-button>` elements.\n */\nclass MatRadioGroup {\n    /** Name of the radio button group. All radio buttons inside this group will use this name. */\n    get name() {\n        return this._name;\n    }\n    set name(value) {\n        this._name = value;\n        this._updateRadioButtonNames();\n    }\n    /** Whether the labels should appear after or before the radio-buttons. Defaults to 'after' */\n    get labelPosition() {\n        return this._labelPosition;\n    }\n    set labelPosition(v) {\n        this._labelPosition = v === 'before' ? 'before' : 'after';\n        this._markRadiosForCheck();\n    }\n    /**\n     * Value for the radio-group. Should equal the value of the selected radio button if there is\n     * a corresponding radio button with a matching value. If there is not such a corresponding\n     * radio button, this value persists to be applied in case a new radio button is added with a\n     * matching value.\n     */\n    get value() {\n        return this._value;\n    }\n    set value(newValue) {\n        if (this._value !== newValue) {\n            // Set this before proceeding to ensure no circular loop occurs with selection.\n            this._value = newValue;\n            this._updateSelectedRadioFromValue();\n            this._checkSelectedRadioButton();\n        }\n    }\n    _checkSelectedRadioButton() {\n        if (this._selected && !this._selected.checked) {\n            this._selected.checked = true;\n        }\n    }\n    /**\n     * The currently selected radio button. If set to a new radio button, the radio group value\n     * will be updated to match the new selected button.\n     */\n    get selected() {\n        return this._selected;\n    }\n    set selected(selected) {\n        this._selected = selected;\n        this.value = selected ? selected.value : null;\n        this._checkSelectedRadioButton();\n    }\n    /** Whether the radio group is disabled */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = value;\n        this._markRadiosForCheck();\n    }\n    /** Whether the radio group is required */\n    get required() {\n        return this._required;\n    }\n    set required(value) {\n        this._required = value;\n        this._markRadiosForCheck();\n    }\n    constructor(_changeDetector) {\n        this._changeDetector = _changeDetector;\n        /** Selected value for the radio group. */\n        this._value = null;\n        /** The HTML name attribute applied to radio buttons in this group. */\n        this._name = `mat-radio-group-${nextUniqueId++}`;\n        /** The currently selected radio button. Should match value. */\n        this._selected = null;\n        /** Whether the `value` has been set to its initial value. */\n        this._isInitialized = false;\n        /** Whether the labels should appear after or before the radio-buttons. Defaults to 'after' */\n        this._labelPosition = 'after';\n        /** Whether the radio group is disabled. */\n        this._disabled = false;\n        /** Whether the radio group is required. */\n        this._required = false;\n        /** The method to be called in order to update ngModel */\n        this._controlValueAccessorChangeFn = () => { };\n        /**\n         * onTouch function registered via registerOnTouch (ControlValueAccessor).\n         * @docs-private\n         */\n        this.onTouched = () => { };\n        /**\n         * Event emitted when the group value changes.\n         * Change events are only emitted when the value changes due to user interaction with\n         * a radio button (the same behavior as `<input type-\"radio\">`).\n         */\n        this.change = new EventEmitter();\n    }\n    /**\n     * Initialize properties once content children are available.\n     * This allows us to propagate relevant attributes to associated buttons.\n     */\n    ngAfterContentInit() {\n        // Mark this component as initialized in AfterContentInit because the initial value can\n        // possibly be set by NgModel on MatRadioGroup, and it is possible that the OnInit of the\n        // NgModel occurs *after* the OnInit of the MatRadioGroup.\n        this._isInitialized = true;\n        // Clear the `selected` button when it's destroyed since the tabindex of the rest of the\n        // buttons depends on it. Note that we don't clear the `value`, because the radio button\n        // may be swapped out with a similar one and there are some internal apps that depend on\n        // that behavior.\n        this._buttonChanges = this._radios.changes.subscribe(() => {\n            if (this.selected && !this._radios.find(radio => radio === this.selected)) {\n                this._selected = null;\n            }\n        });\n    }\n    ngOnDestroy() {\n        this._buttonChanges?.unsubscribe();\n    }\n    /**\n     * Mark this group as being \"touched\" (for ngModel). Meant to be called by the contained\n     * radio buttons upon their blur.\n     */\n    _touch() {\n        if (this.onTouched) {\n            this.onTouched();\n        }\n    }\n    _updateRadioButtonNames() {\n        if (this._radios) {\n            this._radios.forEach(radio => {\n                radio.name = this.name;\n                radio._markForCheck();\n            });\n        }\n    }\n    /** Updates the `selected` radio button from the internal _value state. */\n    _updateSelectedRadioFromValue() {\n        // If the value already matches the selected radio, do nothing.\n        const isAlreadySelected = this._selected !== null && this._selected.value === this._value;\n        if (this._radios && !isAlreadySelected) {\n            this._selected = null;\n            this._radios.forEach(radio => {\n                radio.checked = this.value === radio.value;\n                if (radio.checked) {\n                    this._selected = radio;\n                }\n            });\n        }\n    }\n    /** Dispatch change event with current selection and group value. */\n    _emitChangeEvent() {\n        if (this._isInitialized) {\n            this.change.emit(new MatRadioChange(this._selected, this._value));\n        }\n    }\n    _markRadiosForCheck() {\n        if (this._radios) {\n            this._radios.forEach(radio => radio._markForCheck());\n        }\n    }\n    /**\n     * Sets the model value. Implemented as part of ControlValueAccessor.\n     * @param value\n     */\n    writeValue(value) {\n        this.value = value;\n        this._changeDetector.markForCheck();\n    }\n    /**\n     * Registers a callback to be triggered when the model value changes.\n     * Implemented as part of ControlValueAccessor.\n     * @param fn Callback to be registered.\n     */\n    registerOnChange(fn) {\n        this._controlValueAccessorChangeFn = fn;\n    }\n    /**\n     * Registers a callback to be triggered when the control is touched.\n     * Implemented as part of ControlValueAccessor.\n     * @param fn Callback to be registered.\n     */\n    registerOnTouched(fn) {\n        this.onTouched = fn;\n    }\n    /**\n     * Sets the disabled state of the control. Implemented as a part of ControlValueAccessor.\n     * @param isDisabled Whether the control should be disabled.\n     */\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n        this._changeDetector.markForCheck();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatRadioGroup, deps: [{ token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatRadioGroup, isStandalone: true, selector: \"mat-radio-group\", inputs: { color: \"color\", name: \"name\", labelPosition: \"labelPosition\", value: \"value\", selected: \"selected\", disabled: [\"disabled\", \"disabled\", booleanAttribute], required: [\"required\", \"required\", booleanAttribute] }, outputs: { change: \"change\" }, host: { attributes: { \"role\": \"radiogroup\" }, classAttribute: \"mat-mdc-radio-group\" }, providers: [\n            MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR,\n            { provide: MAT_RADIO_GROUP, useExisting: MatRadioGroup },\n        ], queries: [{ propertyName: \"_radios\", predicate: i0.forwardRef(() => MatRadioButton), descendants: true }], exportAs: [\"matRadioGroup\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatRadioGroup, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-radio-group',\n                    exportAs: 'matRadioGroup',\n                    providers: [\n                        MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR,\n                        { provide: MAT_RADIO_GROUP, useExisting: MatRadioGroup },\n                    ],\n                    host: {\n                        'role': 'radiogroup',\n                        'class': 'mat-mdc-radio-group',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }], propDecorators: { change: [{\n                type: Output\n            }], _radios: [{\n                type: ContentChildren,\n                args: [forwardRef(() => MatRadioButton), { descendants: true }]\n            }], color: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], labelPosition: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], selected: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], required: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\nclass MatRadioButton {\n    /** Whether this radio button is checked. */\n    get checked() {\n        return this._checked;\n    }\n    set checked(value) {\n        if (this._checked !== value) {\n            this._checked = value;\n            if (value && this.radioGroup && this.radioGroup.value !== this.value) {\n                this.radioGroup.selected = this;\n            }\n            else if (!value && this.radioGroup && this.radioGroup.value === this.value) {\n                // When unchecking the selected radio button, update the selected radio\n                // property on the group.\n                this.radioGroup.selected = null;\n            }\n            if (value) {\n                // Notify all radio buttons with the same name to un-check.\n                this._radioDispatcher.notify(this.id, this.name);\n            }\n            this._changeDetector.markForCheck();\n        }\n    }\n    /** The value of this radio button. */\n    get value() {\n        return this._value;\n    }\n    set value(value) {\n        if (this._value !== value) {\n            this._value = value;\n            if (this.radioGroup !== null) {\n                if (!this.checked) {\n                    // Update checked when the value changed to match the radio group's value\n                    this.checked = this.radioGroup.value === value;\n                }\n                if (this.checked) {\n                    this.radioGroup.selected = this;\n                }\n            }\n        }\n    }\n    /** Whether the label should appear after or before the radio button. Defaults to 'after' */\n    get labelPosition() {\n        return this._labelPosition || (this.radioGroup && this.radioGroup.labelPosition) || 'after';\n    }\n    set labelPosition(value) {\n        this._labelPosition = value;\n    }\n    /** Whether the radio button is disabled. */\n    get disabled() {\n        return this._disabled || (this.radioGroup !== null && this.radioGroup.disabled);\n    }\n    set disabled(value) {\n        this._setDisabled(value);\n    }\n    /** Whether the radio button is required. */\n    get required() {\n        return this._required || (this.radioGroup && this.radioGroup.required);\n    }\n    set required(value) {\n        this._required = value;\n    }\n    /** Theme color of the radio button. */\n    get color() {\n        // As per Material design specifications the selection control radio should use the accent color\n        // palette by default. https://material.io/guidelines/components/selection-controls.html\n        return (this._color ||\n            (this.radioGroup && this.radioGroup.color) ||\n            (this._providerOverride && this._providerOverride.color) ||\n            'accent');\n    }\n    set color(newValue) {\n        this._color = newValue;\n    }\n    /** ID of the native input element inside `<mat-radio-button>` */\n    get inputId() {\n        return `${this.id || this._uniqueId}-input`;\n    }\n    constructor(radioGroup, _elementRef, _changeDetector, _focusMonitor, _radioDispatcher, animationMode, _providerOverride, tabIndex) {\n        this._elementRef = _elementRef;\n        this._changeDetector = _changeDetector;\n        this._focusMonitor = _focusMonitor;\n        this._radioDispatcher = _radioDispatcher;\n        this._providerOverride = _providerOverride;\n        this._uniqueId = `mat-radio-${++nextUniqueId}`;\n        /** The unique ID for the radio button. */\n        this.id = this._uniqueId;\n        /** Whether ripples are disabled inside the radio button */\n        this.disableRipple = false;\n        /** Tabindex of the radio button. */\n        this.tabIndex = 0;\n        /**\n         * Event emitted when the checked state of this radio button changes.\n         * Change events are only emitted when the value changes due to user interaction with\n         * the radio button (the same behavior as `<input type-\"radio\">`).\n         */\n        this.change = new EventEmitter();\n        /** Whether this radio is checked. */\n        this._checked = false;\n        /** Value assigned to this radio. */\n        this._value = null;\n        /** Unregister function for _radioDispatcher */\n        this._removeUniqueSelectionListener = () => { };\n        // Assertions. Ideally these should be stripped out by the compiler.\n        // TODO(jelbourn): Assert that there's no name binding AND a parent radio group.\n        this.radioGroup = radioGroup;\n        this._noopAnimations = animationMode === 'NoopAnimations';\n        if (tabIndex) {\n            this.tabIndex = numberAttribute(tabIndex, 0);\n        }\n    }\n    /** Focuses the radio button. */\n    focus(options, origin) {\n        if (origin) {\n            this._focusMonitor.focusVia(this._inputElement, origin, options);\n        }\n        else {\n            this._inputElement.nativeElement.focus(options);\n        }\n    }\n    /**\n     * Marks the radio button as needing checking for change detection.\n     * This method is exposed because the parent radio group will directly\n     * update bound properties of the radio button.\n     */\n    _markForCheck() {\n        // When group value changes, the button will not be notified. Use `markForCheck` to explicit\n        // update radio button's status\n        this._changeDetector.markForCheck();\n    }\n    ngOnInit() {\n        if (this.radioGroup) {\n            // If the radio is inside a radio group, determine if it should be checked\n            this.checked = this.radioGroup.value === this._value;\n            if (this.checked) {\n                this.radioGroup.selected = this;\n            }\n            // Copy name from parent radio group\n            this.name = this.radioGroup.name;\n        }\n        this._removeUniqueSelectionListener = this._radioDispatcher.listen((id, name) => {\n            if (id !== this.id && name === this.name) {\n                this.checked = false;\n            }\n        });\n    }\n    ngDoCheck() {\n        this._updateTabIndex();\n    }\n    ngAfterViewInit() {\n        this._updateTabIndex();\n        this._focusMonitor.monitor(this._elementRef, true).subscribe(focusOrigin => {\n            if (!focusOrigin && this.radioGroup) {\n                this.radioGroup._touch();\n            }\n        });\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this._elementRef);\n        this._removeUniqueSelectionListener();\n    }\n    /** Dispatch change event with current value. */\n    _emitChangeEvent() {\n        this.change.emit(new MatRadioChange(this, this._value));\n    }\n    _isRippleDisabled() {\n        return this.disableRipple || this.disabled;\n    }\n    _onInputClick(event) {\n        // We have to stop propagation for click events on the visual hidden input element.\n        // By default, when a user clicks on a label element, a generated click event will be\n        // dispatched on the associated input element. Since we are using a label element as our\n        // root container, the click event on the `radio-button` will be executed twice.\n        // The real click event will bubble up, and the generated click event also tries to bubble up.\n        // This will lead to multiple click events.\n        // Preventing bubbling for the second event will solve that issue.\n        event.stopPropagation();\n    }\n    /** Triggered when the radio button receives an interaction from the user. */\n    _onInputInteraction(event) {\n        // We always have to stop propagation on the change event.\n        // Otherwise the change event, from the input element, will bubble up and\n        // emit its event object to the `change` output.\n        event.stopPropagation();\n        if (!this.checked && !this.disabled) {\n            const groupValueChanged = this.radioGroup && this.value !== this.radioGroup.value;\n            this.checked = true;\n            this._emitChangeEvent();\n            if (this.radioGroup) {\n                this.radioGroup._controlValueAccessorChangeFn(this.value);\n                if (groupValueChanged) {\n                    this.radioGroup._emitChangeEvent();\n                }\n            }\n        }\n    }\n    /** Triggered when the user clicks on the touch target. */\n    _onTouchTargetClick(event) {\n        this._onInputInteraction(event);\n        if (!this.disabled) {\n            // Normally the input should be focused already, but if the click\n            // comes from the touch target, then we might have to focus it ourselves.\n            this._inputElement.nativeElement.focus();\n        }\n    }\n    /** Sets the disabled state and marks for check if a change occurred. */\n    _setDisabled(value) {\n        if (this._disabled !== value) {\n            this._disabled = value;\n            this._changeDetector.markForCheck();\n        }\n    }\n    /** Gets the tabindex for the underlying input element. */\n    _updateTabIndex() {\n        const group = this.radioGroup;\n        let value;\n        // Implement a roving tabindex if the button is inside a group. For most cases this isn't\n        // necessary, because the browser handles the tab order for inputs inside a group automatically,\n        // but we need an explicitly higher tabindex for the selected button in order for things like\n        // the focus trap to pick it up correctly.\n        if (!group || !group.selected || this.disabled) {\n            value = this.tabIndex;\n        }\n        else {\n            value = group.selected === this ? this.tabIndex : -1;\n        }\n        if (value !== this._previousTabIndex) {\n            // We have to set the tabindex directly on the DOM node, because it depends on\n            // the selected state which is prone to \"changed after checked errors\".\n            const input = this._inputElement?.nativeElement;\n            if (input) {\n                input.setAttribute('tabindex', value + '');\n                this._previousTabIndex = value;\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatRadioButton, deps: [{ token: MAT_RADIO_GROUP, optional: true }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i1.FocusMonitor }, { token: i2.UniqueSelectionDispatcher }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: MAT_RADIO_DEFAULT_OPTIONS, optional: true }, { token: 'tabindex', attribute: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatRadioButton, isStandalone: true, selector: \"mat-radio-button\", inputs: { id: \"id\", name: \"name\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"], disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], tabIndex: [\"tabIndex\", \"tabIndex\", (value) => (value == null ? 0 : numberAttribute(value))], checked: [\"checked\", \"checked\", booleanAttribute], value: \"value\", labelPosition: \"labelPosition\", disabled: [\"disabled\", \"disabled\", booleanAttribute], required: [\"required\", \"required\", booleanAttribute], color: \"color\" }, outputs: { change: \"change\" }, host: { listeners: { \"focus\": \"_inputElement.nativeElement.focus()\" }, properties: { \"attr.id\": \"id\", \"class.mat-primary\": \"color === \\\"primary\\\"\", \"class.mat-accent\": \"color === \\\"accent\\\"\", \"class.mat-warn\": \"color === \\\"warn\\\"\", \"class.mat-mdc-radio-checked\": \"checked\", \"class._mat-animation-noopable\": \"_noopAnimations\", \"attr.tabindex\": \"null\", \"attr.aria-label\": \"null\", \"attr.aria-labelledby\": \"null\", \"attr.aria-describedby\": \"null\" }, classAttribute: \"mat-mdc-radio-button\" }, viewQueries: [{ propertyName: \"_inputElement\", first: true, predicate: [\"input\"], descendants: true }, { propertyName: \"_rippleTrigger\", first: true, predicate: [\"formField\"], descendants: true, read: ElementRef, static: true }], exportAs: [\"matRadioButton\"], ngImport: i0, template: \"<div mat-internal-form-field [labelPosition]=\\\"labelPosition\\\" #formField>\\n  <div class=\\\"mdc-radio\\\" [class.mdc-radio--disabled]=\\\"disabled\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-radio-touch-target\\\" (click)=\\\"_onTouchTargetClick($event)\\\"></div>\\n    <input #input class=\\\"mdc-radio__native-control\\\" type=\\\"radio\\\"\\n           [id]=\\\"inputId\\\"\\n           [checked]=\\\"checked\\\"\\n           [disabled]=\\\"disabled\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [required]=\\\"required\\\"\\n           [attr.aria-label]=\\\"ariaLabel\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           (change)=\\\"_onInputInteraction($event)\\\">\\n    <div class=\\\"mdc-radio__background\\\">\\n      <div class=\\\"mdc-radio__outer-circle\\\"></div>\\n      <div class=\\\"mdc-radio__inner-circle\\\"></div>\\n    </div>\\n    <div mat-ripple class=\\\"mat-radio-ripple mat-mdc-focus-indicator\\\"\\n         [matRippleTrigger]=\\\"_rippleTrigger.nativeElement\\\"\\n         [matRippleDisabled]=\\\"_isRippleDisabled()\\\"\\n         [matRippleCentered]=\\\"true\\\">\\n      <div class=\\\"mat-ripple-element mat-radio-persistent-ripple\\\"></div>\\n    </div>\\n  </div>\\n  <label class=\\\"mdc-label\\\" [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\", styles: [\".mdc-radio{display:inline-block;position:relative;flex:0 0 auto;box-sizing:content-box;width:20px;height:20px;cursor:pointer;will-change:opacity,transform,border-color,color}.mdc-radio[hidden]{display:none}.mdc-radio__background{display:inline-block;position:relative;box-sizing:border-box;width:20px;height:20px}.mdc-radio__background::before{position:absolute;transform:scale(0, 0);border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\";transition:opacity 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__outer-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;border-width:2px;border-style:solid;border-radius:50%;transition:border-color 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__inner-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;transform:scale(0, 0);border-width:10px;border-style:solid;border-radius:50%;transition:transform 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1),border-color 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1}.mdc-radio--touch{margin-top:4px;margin-bottom:4px;margin-right:4px;margin-left:4px}.mdc-radio--touch .mdc-radio__native-control{top:calc((40px - 48px) / 2);right:calc((40px - 48px) / 2);left:calc((40px - 48px) / 2);width:48px;height:48px}.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring{border-color:CanvasText}}.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring::after,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring::after,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring::after{border-color:CanvasText}}.mdc-radio__native-control:checked+.mdc-radio__background,.mdc-radio__native-control:disabled+.mdc-radio__background{transition:opacity 120ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__outer-circle{transition:border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle,.mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{transition:transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1),border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio--disabled{cursor:default;pointer-events:none}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle{transform:scale(0.5);transition:transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1),border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:disabled+.mdc-radio__background,[aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background{cursor:default}.mdc-radio__native-control:focus+.mdc-radio__background::before{transform:scale(1);opacity:.12;transition:opacity 120ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-radio-button .mdc-radio{padding:calc((var(--mdc-radio-state-layer-size) - 20px) / 2)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-selected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-disabled-selected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle{opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-unselected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{opacity:var(--mdc-radio-disabled-unselected-icon-opacity)}.mat-mdc-radio-button .mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-focus-icon-color)}.mat-mdc-radio-button .mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-focus-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before{top:calc(-1 * (var(--mdc-radio-state-layer-size) - 20px) / 2);left:calc(-1 * (var(--mdc-radio-state-layer-size) - 20px) / 2);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control{top:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);right:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);left:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:not([disabled]):not(:focus)~.mdc-radio__background::before{opacity:.04;transform:scale(1)}.mat-mdc-radio-button.mat-mdc-radio-checked .mdc-radio__background::before{background-color:var(--mat-radio-checked-ripple-color)}.mat-mdc-radio-button.mat-mdc-radio-checked .mat-ripple-element{background-color:var(--mat-radio-checked-ripple-color)}.mat-mdc-radio-button .mdc-radio--disabled+label{color:var(--mat-radio-disabled-label-color)}.mat-mdc-radio-button .mat-radio-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:50%}.mat-mdc-radio-button .mat-radio-ripple .mat-ripple-element{opacity:.14}.mat-mdc-radio-button .mat-radio-ripple::before{border-radius:50%}.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__background::before,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__outer-circle,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__inner-circle{transition:none !important}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:focus:enabled:not(:checked)~.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-focus-icon-color, black)}.mat-mdc-radio-button.cdk-focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-radio-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-radio-touch-target-display)}[dir=rtl] .mat-mdc-radio-touch-target{left:0;right:50%;transform:translate(50%, -50%)}\"], dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"component\", type: _MatInternalFormField, selector: \"div[mat-internal-form-field]\", inputs: [\"labelPosition\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatRadioButton, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-radio-button', host: {\n                        'class': 'mat-mdc-radio-button',\n                        '[attr.id]': 'id',\n                        '[class.mat-primary]': 'color === \"primary\"',\n                        '[class.mat-accent]': 'color === \"accent\"',\n                        '[class.mat-warn]': 'color === \"warn\"',\n                        '[class.mat-mdc-radio-checked]': 'checked',\n                        '[class._mat-animation-noopable]': '_noopAnimations',\n                        // Needs to be removed since it causes some a11y issues (see #21266).\n                        '[attr.tabindex]': 'null',\n                        '[attr.aria-label]': 'null',\n                        '[attr.aria-labelledby]': 'null',\n                        '[attr.aria-describedby]': 'null',\n                        // Note: under normal conditions focus shouldn't land on this element, however it may be\n                        // programmatically set, for example inside of a focus trap, in this case we want to forward\n                        // the focus to the native element.\n                        '(focus)': '_inputElement.nativeElement.focus()',\n                    }, exportAs: 'matRadioButton', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, standalone: true, imports: [MatRipple, _MatInternalFormField], template: \"<div mat-internal-form-field [labelPosition]=\\\"labelPosition\\\" #formField>\\n  <div class=\\\"mdc-radio\\\" [class.mdc-radio--disabled]=\\\"disabled\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-radio-touch-target\\\" (click)=\\\"_onTouchTargetClick($event)\\\"></div>\\n    <input #input class=\\\"mdc-radio__native-control\\\" type=\\\"radio\\\"\\n           [id]=\\\"inputId\\\"\\n           [checked]=\\\"checked\\\"\\n           [disabled]=\\\"disabled\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [required]=\\\"required\\\"\\n           [attr.aria-label]=\\\"ariaLabel\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           (change)=\\\"_onInputInteraction($event)\\\">\\n    <div class=\\\"mdc-radio__background\\\">\\n      <div class=\\\"mdc-radio__outer-circle\\\"></div>\\n      <div class=\\\"mdc-radio__inner-circle\\\"></div>\\n    </div>\\n    <div mat-ripple class=\\\"mat-radio-ripple mat-mdc-focus-indicator\\\"\\n         [matRippleTrigger]=\\\"_rippleTrigger.nativeElement\\\"\\n         [matRippleDisabled]=\\\"_isRippleDisabled()\\\"\\n         [matRippleCentered]=\\\"true\\\">\\n      <div class=\\\"mat-ripple-element mat-radio-persistent-ripple\\\"></div>\\n    </div>\\n  </div>\\n  <label class=\\\"mdc-label\\\" [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\", styles: [\".mdc-radio{display:inline-block;position:relative;flex:0 0 auto;box-sizing:content-box;width:20px;height:20px;cursor:pointer;will-change:opacity,transform,border-color,color}.mdc-radio[hidden]{display:none}.mdc-radio__background{display:inline-block;position:relative;box-sizing:border-box;width:20px;height:20px}.mdc-radio__background::before{position:absolute;transform:scale(0, 0);border-radius:50%;opacity:0;pointer-events:none;content:\\\"\\\";transition:opacity 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__outer-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;border-width:2px;border-style:solid;border-radius:50%;transition:border-color 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__inner-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;transform:scale(0, 0);border-width:10px;border-style:solid;border-radius:50%;transition:transform 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1),border-color 120ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-radio__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1}.mdc-radio--touch{margin-top:4px;margin-bottom:4px;margin-right:4px;margin-left:4px}.mdc-radio--touch .mdc-radio__native-control{top:calc((40px - 48px) / 2);right:calc((40px - 48px) / 2);left:calc((40px - 48px) / 2);width:48px;height:48px}.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring{border-color:CanvasText}}.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring::after,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__focus-ring::after,.mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__focus-ring::after{border-color:CanvasText}}.mdc-radio__native-control:checked+.mdc-radio__background,.mdc-radio__native-control:disabled+.mdc-radio__background{transition:opacity 120ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__outer-circle{transition:border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle,.mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{transition:transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1),border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio--disabled{cursor:default;pointer-events:none}.mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle{transform:scale(0.5);transition:transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1),border-color 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-radio__native-control:disabled+.mdc-radio__background,[aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background{cursor:default}.mdc-radio__native-control:focus+.mdc-radio__background::before{transform:scale(1);opacity:.12;transition:opacity 120ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 120ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-radio-button .mdc-radio{padding:calc((var(--mdc-radio-state-layer-size) - 20px) / 2)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-selected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-disabled-selected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle{opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-unselected-icon-color)}.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{opacity:var(--mdc-radio-disabled-unselected-icon-opacity)}.mat-mdc-radio-button .mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-focus-icon-color)}.mat-mdc-radio-button .mdc-radio.mdc-ripple-upgraded--background-focused .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio:not(.mdc-ripple-upgraded):focus .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-focus-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-hover-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-icon-color)}.mat-mdc-radio-button .mdc-radio:not(:disabled):active .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-pressed-icon-color)}.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before{top:calc(-1 * (var(--mdc-radio-state-layer-size) - 20px) / 2);left:calc(-1 * (var(--mdc-radio-state-layer-size) - 20px) / 2);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control{top:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);right:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);left:calc((var(--mdc-radio-state-layer-size) - var(--mdc-radio-state-layer-size)) / 2);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:not([disabled]):not(:focus)~.mdc-radio__background::before{opacity:.04;transform:scale(1)}.mat-mdc-radio-button.mat-mdc-radio-checked .mdc-radio__background::before{background-color:var(--mat-radio-checked-ripple-color)}.mat-mdc-radio-button.mat-mdc-radio-checked .mat-ripple-element{background-color:var(--mat-radio-checked-ripple-color)}.mat-mdc-radio-button .mdc-radio--disabled+label{color:var(--mat-radio-disabled-label-color)}.mat-mdc-radio-button .mat-radio-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:50%}.mat-mdc-radio-button .mat-radio-ripple .mat-ripple-element{opacity:.14}.mat-mdc-radio-button .mat-radio-ripple::before{border-radius:50%}.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__background::before,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__outer-circle,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__inner-circle{transition:none !important}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:focus:enabled:not(:checked)~.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-focus-icon-color, black)}.mat-mdc-radio-button.cdk-focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-radio-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-radio-touch-target-display)}[dir=rtl] .mat-mdc-radio-touch-target{left:0;right:50%;transform:translate(50%, -50%)}\"] }]\n        }], ctorParameters: () => [{ type: MatRadioGroup, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_RADIO_GROUP]\n                }] }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1.FocusMonitor }, { type: i2.UniqueSelectionDispatcher }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_RADIO_DEFAULT_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }], propDecorators: { id: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], ariaDescribedby: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], tabIndex: [{\n                type: Input,\n                args: [{\n                        transform: (value) => (value == null ? 0 : numberAttribute(value)),\n                    }]\n            }], checked: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], value: [{\n                type: Input\n            }], labelPosition: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], required: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], color: [{\n                type: Input\n            }], change: [{\n                type: Output\n            }], _inputElement: [{\n                type: ViewChild,\n                args: ['input']\n            }], _rippleTrigger: [{\n                type: ViewChild,\n                args: ['formField', { read: ElementRef, static: true }]\n            }] } });\n\nclass MatRadioModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatRadioModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatRadioModule, imports: [MatCommonModule, CommonModule, MatRippleModule, MatRadioGroup, MatRadioButton], exports: [MatCommonModule, MatRadioGroup, MatRadioButton] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatRadioModule, imports: [MatCommonModule, CommonModule, MatRippleModule, MatRadioButton, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatRadioModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, CommonModule, MatRippleModule, MatRadioGroup, MatRadioButton],\n                    exports: [MatCommonModule, MatRadioGroup, MatRadioButton],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_RADIO_DEFAULT_OPTIONS, MAT_RADIO_DEFAULT_OPTIONS_FACTORY, MAT_RADIO_GROUP, MAT_RADIO_GROUP_CONTROL_VALUE_ACCESSOR, MatRadioButton, MatRadioChange, MatRadioGroup, MatRadioModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,cAAc,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,MAAM,EAAEC,eAAe,EAAEC,KAAK,EAAEC,eAAe,EAAEC,qBAAqB,EAAEC,UAAU,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAClS,SAASC,SAAS,EAAEC,qBAAqB,EAAEC,eAAe,EAAEC,eAAe,QAAQ,wBAAwB;AAC3G,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,OAAO,KAAKC,EAAE,MAAM,0BAA0B;AAC9C,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,YAAY,QAAQ,iBAAiB;;AAE9C;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AACA,IAAIC,YAAY,GAAG,CAAC;AACpB;AACA,MAAMC,cAAc,CAAC;EACjBC,WAAWA,CACX;EACAC,MAAM,EACN;EACAC,KAAK,EAAE;IACH,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;EACtB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sCAAsC,GAAG;EAC3CC,OAAO,EAAEX,iBAAiB;EAC1BY,WAAW,EAAErC,UAAU,CAAC,MAAMsC,aAAa,CAAC;EAC5CC,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAG,IAAIvC,cAAc,CAAC,eAAe,CAAC;AAC3D,MAAMwC,yBAAyB,GAAG,IAAIxC,cAAc,CAAC,2BAA2B,EAAE;EAC9EyC,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF,SAASA,iCAAiCA,CAAA,EAAG;EACzC,OAAO;IACHC,KAAK,EAAE;EACX,CAAC;AACL;AACA;AACA;AACA;AACA,MAAMP,aAAa,CAAC;EAChB;EACA,IAAIQ,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK;EACrB;EACA,IAAID,IAAIA,CAACZ,KAAK,EAAE;IACZ,IAAI,CAACa,KAAK,GAAGb,KAAK;IAClB,IAAI,CAACc,uBAAuB,CAAC,CAAC;EAClC;EACA;EACA,IAAIC,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACC,cAAc;EAC9B;EACA,IAAID,aAAaA,CAACE,CAAC,EAAE;IACjB,IAAI,CAACD,cAAc,GAAGC,CAAC,KAAK,QAAQ,GAAG,QAAQ,GAAG,OAAO;IACzD,IAAI,CAACC,mBAAmB,CAAC,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIlB,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACmB,MAAM;EACtB;EACA,IAAInB,KAAKA,CAACoB,QAAQ,EAAE;IAChB,IAAI,IAAI,CAACD,MAAM,KAAKC,QAAQ,EAAE;MAC1B;MACA,IAAI,CAACD,MAAM,GAAGC,QAAQ;MACtB,IAAI,CAACC,6BAA6B,CAAC,CAAC;MACpC,IAAI,CAACC,yBAAyB,CAAC,CAAC;IACpC;EACJ;EACAA,yBAAyBA,CAAA,EAAG;IACxB,IAAI,IAAI,CAACC,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACC,OAAO,EAAE;MAC3C,IAAI,CAACD,SAAS,CAACC,OAAO,GAAG,IAAI;IACjC;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACF,SAAS;EACzB;EACA,IAAIE,QAAQA,CAACA,QAAQ,EAAE;IACnB,IAAI,CAACF,SAAS,GAAGE,QAAQ;IACzB,IAAI,CAACzB,KAAK,GAAGyB,QAAQ,GAAGA,QAAQ,CAACzB,KAAK,GAAG,IAAI;IAC7C,IAAI,CAACsB,yBAAyB,CAAC,CAAC;EACpC;EACA;EACA,IAAII,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAAC1B,KAAK,EAAE;IAChB,IAAI,CAAC2B,SAAS,GAAG3B,KAAK;IACtB,IAAI,CAACkB,mBAAmB,CAAC,CAAC;EAC9B;EACA;EACA,IAAIU,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAAC5B,KAAK,EAAE;IAChB,IAAI,CAAC6B,SAAS,GAAG7B,KAAK;IACtB,IAAI,CAACkB,mBAAmB,CAAC,CAAC;EAC9B;EACApB,WAAWA,CAACgC,eAAe,EAAE;IACzB,IAAI,CAACA,eAAe,GAAGA,eAAe;IACtC;IACA,IAAI,CAACX,MAAM,GAAG,IAAI;IAClB;IACA,IAAI,CAACN,KAAK,GAAG,mBAAmBjB,YAAY,EAAE,EAAE;IAChD;IACA,IAAI,CAAC2B,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACQ,cAAc,GAAG,KAAK;IAC3B;IACA,IAAI,CAACf,cAAc,GAAG,OAAO;IAC7B;IACA,IAAI,CAACW,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACE,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACG,6BAA6B,GAAG,MAAM,CAAE,CAAC;IAC9C;AACR;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,MAAM,CAAE,CAAC;IAC1B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,IAAIlE,YAAY,CAAC,CAAC;EACpC;EACA;AACJ;AACA;AACA;EACImE,kBAAkBA,CAAA,EAAG;IACjB;IACA;IACA;IACA,IAAI,CAACJ,cAAc,GAAG,IAAI;IAC1B;IACA;IACA;IACA;IACA,IAAI,CAACK,cAAc,GAAG,IAAI,CAACC,OAAO,CAACC,OAAO,CAACC,SAAS,CAAC,MAAM;MACvD,IAAI,IAAI,CAACd,QAAQ,IAAI,CAAC,IAAI,CAACY,OAAO,CAACG,IAAI,CAACC,KAAK,IAAIA,KAAK,KAAK,IAAI,CAAChB,QAAQ,CAAC,EAAE;QACvE,IAAI,CAACF,SAAS,GAAG,IAAI;MACzB;IACJ,CAAC,CAAC;EACN;EACAmB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACN,cAAc,EAAEO,WAAW,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;EACIC,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACX,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAAC,CAAC;IACpB;EACJ;EACAnB,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACuB,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACQ,OAAO,CAACJ,KAAK,IAAI;QAC1BA,KAAK,CAAC7B,IAAI,GAAG,IAAI,CAACA,IAAI;QACtB6B,KAAK,CAACK,aAAa,CAAC,CAAC;MACzB,CAAC,CAAC;IACN;EACJ;EACA;EACAzB,6BAA6BA,CAAA,EAAG;IAC5B;IACA,MAAM0B,iBAAiB,GAAG,IAAI,CAACxB,SAAS,KAAK,IAAI,IAAI,IAAI,CAACA,SAAS,CAACvB,KAAK,KAAK,IAAI,CAACmB,MAAM;IACzF,IAAI,IAAI,CAACkB,OAAO,IAAI,CAACU,iBAAiB,EAAE;MACpC,IAAI,CAACxB,SAAS,GAAG,IAAI;MACrB,IAAI,CAACc,OAAO,CAACQ,OAAO,CAACJ,KAAK,IAAI;QAC1BA,KAAK,CAACjB,OAAO,GAAG,IAAI,CAACxB,KAAK,KAAKyC,KAAK,CAACzC,KAAK;QAC1C,IAAIyC,KAAK,CAACjB,OAAO,EAAE;UACf,IAAI,CAACD,SAAS,GAAGkB,KAAK;QAC1B;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACAO,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACjB,cAAc,EAAE;MACrB,IAAI,CAACG,MAAM,CAACe,IAAI,CAAC,IAAIpD,cAAc,CAAC,IAAI,CAAC0B,SAAS,EAAE,IAAI,CAACJ,MAAM,CAAC,CAAC;IACrE;EACJ;EACAD,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACmB,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACQ,OAAO,CAACJ,KAAK,IAAIA,KAAK,CAACK,aAAa,CAAC,CAAC,CAAC;IACxD;EACJ;EACA;AACJ;AACA;AACA;EACII,UAAUA,CAAClD,KAAK,EAAE;IACd,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC8B,eAAe,CAACqB,YAAY,CAAC,CAAC;EACvC;EACA;AACJ;AACA;AACA;AACA;EACIC,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACrB,6BAA6B,GAAGqB,EAAE;EAC3C;EACA;AACJ;AACA;AACA;AACA;EACIC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAACpB,SAAS,GAAGoB,EAAE;EACvB;EACA;AACJ;AACA;AACA;EACIE,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAAC9B,QAAQ,GAAG8B,UAAU;IAC1B,IAAI,CAAC1B,eAAe,CAACqB,YAAY,CAAC,CAAC;EACvC;EACA;IAAS,IAAI,CAACM,IAAI,YAAAC,sBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFvD,aAAa,EAAvBvC,EAAE,CAAA+F,iBAAA,CAAuC/F,EAAE,CAACgG,iBAAiB;IAAA,CAA4C;EAAE;EAC3M;IAAS,IAAI,CAACC,IAAI,kBAD8EjG,EAAE,CAAAkG,iBAAA;MAAAC,IAAA,EACJ5D,aAAa;MAAA6D,SAAA;MAAAC,cAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA,EAAAC,QAAA;QAAA,IAAAF,EAAA;UADXvG,EAAE,CAAA0G,cAAA,CAAAD,QAAA,EAIvBE,cAAc;QAAA;QAAA,IAAAJ,EAAA;UAAA,IAAAK,EAAA;UAJO5G,EAAE,CAAA6G,cAAA,CAAAD,EAAA,GAAF5G,EAAE,CAAA8G,WAAA,QAAAN,GAAA,CAAAhC,OAAA,GAAAoC,EAAA;QAAA;MAAA;MAAAG,SAAA,WACqV,YAAY;MAAAC,MAAA;QAAAlE,KAAA;QAAAC,IAAA;QAAAG,aAAA;QAAAf,KAAA;QAAAyB,QAAA;QAAAC,QAAA,GADnW7D,EAAE,CAAAiH,YAAA,CAAAC,0BAAA,0BAC6M9G,gBAAgB;QAAA2D,QAAA,GAD/N/D,EAAE,CAAAiH,YAAA,CAAAC,0BAAA,0BACmQ9G,gBAAgB;MAAA;MAAA+G,OAAA;QAAA9C,MAAA;MAAA;MAAA+C,QAAA;MAAAC,UAAA;MAAAC,QAAA,GADrRtH,EAAE,CAAAuH,kBAAA,CACyZ,CACnfnF,sCAAsC,EACtC;QAAEC,OAAO,EAAEI,eAAe;QAAEH,WAAW,EAAEC;MAAc,CAAC,CAC3D,GAJ2FvC,EAAE,CAAAwH,wBAAA;IAAA,EAI4D;EAAE;AACpK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KANoGzH,EAAE,CAAA0H,iBAAA,CAMXnF,aAAa,EAAc,CAAC;IAC3G4D,IAAI,EAAE9F,SAAS;IACfsH,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BR,QAAQ,EAAE,eAAe;MACzBS,SAAS,EAAE,CACPzF,sCAAsC,EACtC;QAAEC,OAAO,EAAEI,eAAe;QAAEH,WAAW,EAAEC;MAAc,CAAC,CAC3D;MACDuF,IAAI,EAAE;QACF,MAAM,EAAE,YAAY;QACpB,OAAO,EAAE;MACb,CAAC;MACDT,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAElB,IAAI,EAAEnG,EAAE,CAACgG;EAAkB,CAAC,CAAC,EAAkB;IAAE3B,MAAM,EAAE,CAAC;MAC/E8B,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAEkE,OAAO,EAAE,CAAC;MACV2B,IAAI,EAAE5F,eAAe;MACrBoH,IAAI,EAAE,CAAC1H,UAAU,CAAC,MAAM0G,cAAc,CAAC,EAAE;QAAEoB,WAAW,EAAE;MAAK,CAAC;IAClE,CAAC,CAAC;IAAEjF,KAAK,EAAE,CAAC;MACRqD,IAAI,EAAE3F;IACV,CAAC,CAAC;IAAEuC,IAAI,EAAE,CAAC;MACPoD,IAAI,EAAE3F;IACV,CAAC,CAAC;IAAE0C,aAAa,EAAE,CAAC;MAChBiD,IAAI,EAAE3F;IACV,CAAC,CAAC;IAAE2B,KAAK,EAAE,CAAC;MACRgE,IAAI,EAAE3F;IACV,CAAC,CAAC;IAAEoD,QAAQ,EAAE,CAAC;MACXuC,IAAI,EAAE3F;IACV,CAAC,CAAC;IAAEqD,QAAQ,EAAE,CAAC;MACXsC,IAAI,EAAE3F,KAAK;MACXmH,IAAI,EAAE,CAAC;QAAEK,SAAS,EAAE5H;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2D,QAAQ,EAAE,CAAC;MACXoC,IAAI,EAAE3F,KAAK;MACXmH,IAAI,EAAE,CAAC;QAAEK,SAAS,EAAE5H;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMuG,cAAc,CAAC;EACjB;EACA,IAAIhD,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACsE,QAAQ;EACxB;EACA,IAAItE,OAAOA,CAACxB,KAAK,EAAE;IACf,IAAI,IAAI,CAAC8F,QAAQ,KAAK9F,KAAK,EAAE;MACzB,IAAI,CAAC8F,QAAQ,GAAG9F,KAAK;MACrB,IAAIA,KAAK,IAAI,IAAI,CAAC+F,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC/F,KAAK,KAAK,IAAI,CAACA,KAAK,EAAE;QAClE,IAAI,CAAC+F,UAAU,CAACtE,QAAQ,GAAG,IAAI;MACnC,CAAC,MACI,IAAI,CAACzB,KAAK,IAAI,IAAI,CAAC+F,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC/F,KAAK,KAAK,IAAI,CAACA,KAAK,EAAE;QACxE;QACA;QACA,IAAI,CAAC+F,UAAU,CAACtE,QAAQ,GAAG,IAAI;MACnC;MACA,IAAIzB,KAAK,EAAE;QACP;QACA,IAAI,CAACgG,gBAAgB,CAACC,MAAM,CAAC,IAAI,CAACC,EAAE,EAAE,IAAI,CAACtF,IAAI,CAAC;MACpD;MACA,IAAI,CAACkB,eAAe,CAACqB,YAAY,CAAC,CAAC;IACvC;EACJ;EACA;EACA,IAAInD,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACmB,MAAM;EACtB;EACA,IAAInB,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,IAAI,CAACmB,MAAM,KAAKnB,KAAK,EAAE;MACvB,IAAI,CAACmB,MAAM,GAAGnB,KAAK;MACnB,IAAI,IAAI,CAAC+F,UAAU,KAAK,IAAI,EAAE;QAC1B,IAAI,CAAC,IAAI,CAACvE,OAAO,EAAE;UACf;UACA,IAAI,CAACA,OAAO,GAAG,IAAI,CAACuE,UAAU,CAAC/F,KAAK,KAAKA,KAAK;QAClD;QACA,IAAI,IAAI,CAACwB,OAAO,EAAE;UACd,IAAI,CAACuE,UAAU,CAACtE,QAAQ,GAAG,IAAI;QACnC;MACJ;IACJ;EACJ;EACA;EACA,IAAIV,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACC,cAAc,IAAK,IAAI,CAAC+E,UAAU,IAAI,IAAI,CAACA,UAAU,CAAChF,aAAc,IAAI,OAAO;EAC/F;EACA,IAAIA,aAAaA,CAACf,KAAK,EAAE;IACrB,IAAI,CAACgB,cAAc,GAAGhB,KAAK;EAC/B;EACA;EACA,IAAI0B,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAK,IAAI,CAACoE,UAAU,KAAK,IAAI,IAAI,IAAI,CAACA,UAAU,CAACrE,QAAS;EACnF;EACA,IAAIA,QAAQA,CAAC1B,KAAK,EAAE;IAChB,IAAI,CAACmG,YAAY,CAACnG,KAAK,CAAC;EAC5B;EACA;EACA,IAAI4B,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAK,IAAI,CAACkE,UAAU,IAAI,IAAI,CAACA,UAAU,CAACnE,QAAS;EAC1E;EACA,IAAIA,QAAQA,CAAC5B,KAAK,EAAE;IAChB,IAAI,CAAC6B,SAAS,GAAG7B,KAAK;EAC1B;EACA;EACA,IAAIW,KAAKA,CAAA,EAAG;IACR;IACA;IACA,OAAQ,IAAI,CAACyF,MAAM,IACd,IAAI,CAACL,UAAU,IAAI,IAAI,CAACA,UAAU,CAACpF,KAAM,IACzC,IAAI,CAAC0F,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAAC1F,KAAM,IACxD,QAAQ;EAChB;EACA,IAAIA,KAAKA,CAACS,QAAQ,EAAE;IAChB,IAAI,CAACgF,MAAM,GAAGhF,QAAQ;EAC1B;EACA;EACA,IAAIkF,OAAOA,CAAA,EAAG;IACV,OAAO,GAAG,IAAI,CAACJ,EAAE,IAAI,IAAI,CAACK,SAAS,QAAQ;EAC/C;EACAzG,WAAWA,CAACiG,UAAU,EAAES,WAAW,EAAE1E,eAAe,EAAE2E,aAAa,EAAET,gBAAgB,EAAEU,aAAa,EAAEL,iBAAiB,EAAEM,QAAQ,EAAE;IAC/H,IAAI,CAACH,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC1E,eAAe,GAAGA,eAAe;IACtC,IAAI,CAAC2E,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACT,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACK,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACE,SAAS,GAAG,aAAa,EAAE3G,YAAY,EAAE;IAC9C;IACA,IAAI,CAACsG,EAAE,GAAG,IAAI,CAACK,SAAS;IACxB;IACA,IAAI,CAACK,aAAa,GAAG,KAAK;IAC1B;IACA,IAAI,CAACD,QAAQ,GAAG,CAAC;IACjB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACzE,MAAM,GAAG,IAAIlE,YAAY,CAAC,CAAC;IAChC;IACA,IAAI,CAAC8H,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAAC3E,MAAM,GAAG,IAAI;IAClB;IACA,IAAI,CAAC0F,8BAA8B,GAAG,MAAM,CAAE,CAAC;IAC/C;IACA;IACA,IAAI,CAACd,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACe,eAAe,GAAGJ,aAAa,KAAK,gBAAgB;IACzD,IAAIC,QAAQ,EAAE;MACV,IAAI,CAACA,QAAQ,GAAGrI,eAAe,CAACqI,QAAQ,EAAE,CAAC,CAAC;IAChD;EACJ;EACA;EACAI,KAAKA,CAACC,OAAO,EAAEC,MAAM,EAAE;IACnB,IAAIA,MAAM,EAAE;MACR,IAAI,CAACR,aAAa,CAACS,QAAQ,CAAC,IAAI,CAACC,aAAa,EAAEF,MAAM,EAAED,OAAO,CAAC;IACpE,CAAC,MACI;MACD,IAAI,CAACG,aAAa,CAACC,aAAa,CAACL,KAAK,CAACC,OAAO,CAAC;IACnD;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIlE,aAAaA,CAAA,EAAG;IACZ;IACA;IACA,IAAI,CAAChB,eAAe,CAACqB,YAAY,CAAC,CAAC;EACvC;EACAkE,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACtB,UAAU,EAAE;MACjB;MACA,IAAI,CAACvE,OAAO,GAAG,IAAI,CAACuE,UAAU,CAAC/F,KAAK,KAAK,IAAI,CAACmB,MAAM;MACpD,IAAI,IAAI,CAACK,OAAO,EAAE;QACd,IAAI,CAACuE,UAAU,CAACtE,QAAQ,GAAG,IAAI;MACnC;MACA;MACA,IAAI,CAACb,IAAI,GAAG,IAAI,CAACmF,UAAU,CAACnF,IAAI;IACpC;IACA,IAAI,CAACiG,8BAA8B,GAAG,IAAI,CAACb,gBAAgB,CAACsB,MAAM,CAAC,CAACpB,EAAE,EAAEtF,IAAI,KAAK;MAC7E,IAAIsF,EAAE,KAAK,IAAI,CAACA,EAAE,IAAItF,IAAI,KAAK,IAAI,CAACA,IAAI,EAAE;QACtC,IAAI,CAACY,OAAO,GAAG,KAAK;MACxB;IACJ,CAAC,CAAC;EACN;EACA+F,SAASA,CAAA,EAAG;IACR,IAAI,CAACC,eAAe,CAAC,CAAC;EAC1B;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACD,eAAe,CAAC,CAAC;IACtB,IAAI,CAACf,aAAa,CAACiB,OAAO,CAAC,IAAI,CAAClB,WAAW,EAAE,IAAI,CAAC,CAACjE,SAAS,CAACoF,WAAW,IAAI;MACxE,IAAI,CAACA,WAAW,IAAI,IAAI,CAAC5B,UAAU,EAAE;QACjC,IAAI,CAACA,UAAU,CAACnD,MAAM,CAAC,CAAC;MAC5B;IACJ,CAAC,CAAC;EACN;EACAF,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC+D,aAAa,CAACmB,cAAc,CAAC,IAAI,CAACpB,WAAW,CAAC;IACnD,IAAI,CAACK,8BAA8B,CAAC,CAAC;EACzC;EACA;EACA7D,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACd,MAAM,CAACe,IAAI,CAAC,IAAIpD,cAAc,CAAC,IAAI,EAAE,IAAI,CAACsB,MAAM,CAAC,CAAC;EAC3D;EACA0G,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACjB,aAAa,IAAI,IAAI,CAAClF,QAAQ;EAC9C;EACAoG,aAAaA,CAACC,KAAK,EAAE;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACAA,KAAK,CAACC,eAAe,CAAC,CAAC;EAC3B;EACA;EACAC,mBAAmBA,CAACF,KAAK,EAAE;IACvB;IACA;IACA;IACAA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB,IAAI,CAAC,IAAI,CAACxG,OAAO,IAAI,CAAC,IAAI,CAACE,QAAQ,EAAE;MACjC,MAAMwG,iBAAiB,GAAG,IAAI,CAACnC,UAAU,IAAI,IAAI,CAAC/F,KAAK,KAAK,IAAI,CAAC+F,UAAU,CAAC/F,KAAK;MACjF,IAAI,CAACwB,OAAO,GAAG,IAAI;MACnB,IAAI,CAACwB,gBAAgB,CAAC,CAAC;MACvB,IAAI,IAAI,CAAC+C,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,CAAC/D,6BAA6B,CAAC,IAAI,CAAChC,KAAK,CAAC;QACzD,IAAIkI,iBAAiB,EAAE;UACnB,IAAI,CAACnC,UAAU,CAAC/C,gBAAgB,CAAC,CAAC;QACtC;MACJ;IACJ;EACJ;EACA;EACAmF,mBAAmBA,CAACJ,KAAK,EAAE;IACvB,IAAI,CAACE,mBAAmB,CAACF,KAAK,CAAC;IAC/B,IAAI,CAAC,IAAI,CAACrG,QAAQ,EAAE;MAChB;MACA;MACA,IAAI,CAACyF,aAAa,CAACC,aAAa,CAACL,KAAK,CAAC,CAAC;IAC5C;EACJ;EACA;EACAZ,YAAYA,CAACnG,KAAK,EAAE;IAChB,IAAI,IAAI,CAAC2B,SAAS,KAAK3B,KAAK,EAAE;MAC1B,IAAI,CAAC2B,SAAS,GAAG3B,KAAK;MACtB,IAAI,CAAC8B,eAAe,CAACqB,YAAY,CAAC,CAAC;IACvC;EACJ;EACA;EACAqE,eAAeA,CAAA,EAAG;IACd,MAAMY,KAAK,GAAG,IAAI,CAACrC,UAAU;IAC7B,IAAI/F,KAAK;IACT;IACA;IACA;IACA;IACA,IAAI,CAACoI,KAAK,IAAI,CAACA,KAAK,CAAC3G,QAAQ,IAAI,IAAI,CAACC,QAAQ,EAAE;MAC5C1B,KAAK,GAAG,IAAI,CAAC2G,QAAQ;IACzB,CAAC,MACI;MACD3G,KAAK,GAAGoI,KAAK,CAAC3G,QAAQ,KAAK,IAAI,GAAG,IAAI,CAACkF,QAAQ,GAAG,CAAC,CAAC;IACxD;IACA,IAAI3G,KAAK,KAAK,IAAI,CAACqI,iBAAiB,EAAE;MAClC;MACA;MACA,MAAMC,KAAK,GAAG,IAAI,CAACnB,aAAa,EAAEC,aAAa;MAC/C,IAAIkB,KAAK,EAAE;QACPA,KAAK,CAACC,YAAY,CAAC,UAAU,EAAEvI,KAAK,GAAG,EAAE,CAAC;QAC1C,IAAI,CAACqI,iBAAiB,GAAGrI,KAAK;MAClC;IACJ;EACJ;EACA;IAAS,IAAI,CAACyD,IAAI,YAAA+E,uBAAA7E,CAAA;MAAA,YAAAA,CAAA,IAAwFa,cAAc,EAvRxB3G,EAAE,CAAA+F,iBAAA,CAuRwCtD,eAAe,MAvRzDzC,EAAE,CAAA+F,iBAAA,CAuRoF/F,EAAE,CAACW,UAAU,GAvRnGX,EAAE,CAAA+F,iBAAA,CAuR8G/F,EAAE,CAACgG,iBAAiB,GAvRpIhG,EAAE,CAAA+F,iBAAA,CAuR+IvE,EAAE,CAACoJ,YAAY,GAvRhK5K,EAAE,CAAA+F,iBAAA,CAuR2KtE,EAAE,CAACoJ,yBAAyB,GAvRzM7K,EAAE,CAAA+F,iBAAA,CAuRoNrF,qBAAqB,MAvR3OV,EAAE,CAAA+F,iBAAA,CAuRsQrD,yBAAyB,MAvRjS1C,EAAE,CAAA8K,iBAAA,CAuR4T,UAAU;IAAA,CAA6D;EAAE;EACve;IAAS,IAAI,CAACC,IAAI,kBAxR8E/K,EAAE,CAAAgL,iBAAA;MAAA7E,IAAA,EAwRJQ,cAAc;MAAAP,SAAA;MAAA6E,SAAA,WAAAC,qBAAA3E,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAxRZvG,EAAE,CAAAmL,WAAA,CAAAvJ,GAAA;UAAF5B,EAAE,CAAAmL,WAAA,CAAAtJ,GAAA,KAwR20ClB,UAAU;QAAA;QAAA,IAAA4F,EAAA;UAAA,IAAAK,EAAA;UAxRv1C5G,EAAE,CAAA6G,cAAA,CAAAD,EAAA,GAAF5G,EAAE,CAAA8G,WAAA,QAAAN,GAAA,CAAA8C,aAAA,GAAA1C,EAAA,CAAAwE,KAAA;UAAFpL,EAAE,CAAA6G,cAAA,CAAAD,EAAA,GAAF5G,EAAE,CAAA8G,WAAA,QAAAN,GAAA,CAAA6E,cAAA,GAAAzE,EAAA,CAAAwE,KAAA;QAAA;MAAA;MAAArE,SAAA;MAAAuE,QAAA;MAAAC,YAAA,WAAAC,4BAAAjF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFvG,EAAE,CAAAyL,UAAA,mBAAAC,wCAAA;YAAA,OAwRJlF,GAAA,CAAA8C,aAAA,CAAAC,aAAA,CAAAL,KAAA,CAAkC,CAAC;UAAA,CAAtB,CAAC;QAAA;QAAA,IAAA3C,EAAA;UAxRZvG,EAAE,CAAA2L,WAAA,OAAAnF,GAAA,CAAA6B,EAAA,cAwRJ,IAAI,gBAAJ,IAAI,qBAAJ,IAAI,sBAAJ,IAAI;UAxRFrI,EAAE,CAAA4L,WAAA,gBAAApF,GAAA,CAAA1D,KAAA,KAwRM,SAAG,CAAC,eAAA0D,GAAA,CAAA1D,KAAA,KAAJ,QAAG,CAAC,aAAA0D,GAAA,CAAA1D,KAAA,KAAJ,MAAG,CAAC,0BAAA0D,GAAA,CAAA7C,OAAD,CAAC,4BAAA6C,GAAA,CAAAyC,eAAD,CAAC;QAAA;MAAA;MAAAjC,MAAA;QAAAqB,EAAA;QAAAtF,IAAA;QAAA8I,SAAA,GAxRZ7L,EAAE,CAAAiH,YAAA,CAAA6E,IAAA;QAAAC,cAAA,GAAF/L,EAAE,CAAAiH,YAAA,CAAA6E,IAAA;QAAAE,eAAA,GAAFhM,EAAE,CAAAiH,YAAA,CAAA6E,IAAA;QAAA/C,aAAA,GAAF/I,EAAE,CAAAiH,YAAA,CAAAC,0BAAA,oCAwR2S9G,gBAAgB;QAAA0I,QAAA,GAxR7T9I,EAAE,CAAAiH,YAAA,CAAAC,0BAAA,0BAwRkW/E,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG1B,eAAe,CAAC0B,KAAK,CAAE;QAAAwB,OAAA,GAxR1Z3D,EAAE,CAAAiH,YAAA,CAAAC,0BAAA,wBAwR2b9G,gBAAgB;QAAA+B,KAAA;QAAAe,aAAA;QAAAW,QAAA,GAxR7c7D,EAAE,CAAAiH,YAAA,CAAAC,0BAAA,0BAwRiiB9G,gBAAgB;QAAA2D,QAAA,GAxRnjB/D,EAAE,CAAAiH,YAAA,CAAAC,0BAAA,0BAwRulB9G,gBAAgB;QAAA0C,KAAA;MAAA;MAAAqE,OAAA;QAAA9C,MAAA;MAAA;MAAA+C,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAxRzmBtH,EAAE,CAAAwH,wBAAA,EAAFxH,EAAE,CAAAiM,mBAAA;MAAAC,kBAAA,EAAApK,GAAA;MAAAqK,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAhG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAiG,GAAA,GAAFxM,EAAE,CAAAyM,gBAAA;UAAFzM,EAAE,CAAA0M,eAAA;UAAF1M,EAAE,CAAA2M,cAAA,eAwRw+C,CAAC,YAAqE,CAAC,YAAuJ,CAAC;UAxRzsD3M,EAAE,CAAAyL,UAAA,mBAAAmB,6CAAAC,MAAA;YAAF7M,EAAE,CAAA8M,aAAA,CAAAN,GAAA;YAAA,OAAFxM,EAAE,CAAA+M,WAAA,CAwRyqDvG,GAAA,CAAA8D,mBAAA,CAAAuC,MAA0B,CAAC;UAAA,CAAC,CAAC;UAxRxsD7M,EAAE,CAAAgN,YAAA,CAwR4sD,CAAC;UAxR/sDhN,EAAE,CAAA2M,cAAA,iBAwR6qE,CAAC;UAxRhrE3M,EAAE,CAAAyL,UAAA,oBAAAwB,gDAAAJ,MAAA;YAAF7M,EAAE,CAAA8M,aAAA,CAAAN,GAAA;YAAA,OAAFxM,EAAE,CAAA+M,WAAA,CAwRgpEvG,GAAA,CAAA4D,mBAAA,CAAAyC,MAA0B,CAAC;UAAA,CAAC,CAAC;UAxR/qE7M,EAAE,CAAAgN,YAAA,CAwR6qE,CAAC;UAxRhrEhN,EAAE,CAAA2M,cAAA,YAwRwtE,CAAC;UAxR3tE3M,EAAE,CAAAkN,SAAA,YAwR6wE,CAAC,YAAoD,CAAC;UAxRr0ElN,EAAE,CAAAgN,YAAA,CAwR80E,CAAC;UAxRj1EhN,EAAE,CAAA2M,cAAA,YAwRkjF,CAAC;UAxRrjF3M,EAAE,CAAAkN,SAAA,cAwR8nF,CAAC;UAxRjoFlN,EAAE,CAAAgN,YAAA,CAwR0oF,CAAC,CAAS,CAAC;UAxRvpFhN,EAAE,CAAA2M,cAAA,gBAwRqsF,CAAC;UAxRxsF3M,EAAE,CAAAmN,YAAA,GAwRouF,CAAC;UAxRvuFnN,EAAE,CAAAgN,YAAA,CAwRgvF,CAAC,CAAO,CAAC;QAAA;QAAA,IAAAzG,EAAA;UAxR3vFvG,EAAE,CAAAoN,UAAA,kBAAA5G,GAAA,CAAAtD,aAwR49C,CAAC;UAxR/9ClD,EAAE,CAAAqN,SAAA,EAwR6iD,CAAC;UAxRhjDrN,EAAE,CAAA4L,WAAA,wBAAApF,GAAA,CAAA3C,QAwR6iD,CAAC;UAxRhjD7D,EAAE,CAAAqN,SAAA,EAwR+yD,CAAC;UAxRlzDrN,EAAE,CAAAoN,UAAA,OAAA5G,GAAA,CAAAiC,OAwR+yD,CAAC,YAAAjC,GAAA,CAAA7C,OAAiC,CAAC,aAAA6C,GAAA,CAAA3C,QAAmC,CAAC,aAAA2C,GAAA,CAAAzC,QAAuG,CAAC;UAxRh+D/D,EAAE,CAAA2L,WAAA,SAAAnF,GAAA,CAAAzD,IAAA,WAAAyD,GAAA,CAAArE,KAAA,gBAAAqE,GAAA,CAAAqF,SAAA,qBAAArF,GAAA,CAAAuF,cAAA,sBAAAvF,GAAA,CAAAwF,eAAA;UAAFhM,EAAE,CAAAqN,SAAA,EAwRo9E,CAAC;UAxRv9ErN,EAAE,CAAAoN,UAAA,qBAAA5G,GAAA,CAAA6E,cAAA,CAAA9B,aAwRo9E,CAAC,sBAAA/C,GAAA,CAAAwD,iBAAA,EAAqD,CAAC,0BAAsC,CAAC;UAxRpjFhK,EAAE,CAAAqN,SAAA,EAwRosF,CAAC;UAxRvsFrN,EAAE,CAAAoN,UAAA,QAAA5G,GAAA,CAAAiC,OAwRosF,CAAC;QAAA;MAAA;MAAA6E,YAAA,GAAwgVlM,SAAS,EAAwPC,qBAAqB;MAAAkM,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAyK;EAAE;AACpvb;AACA;EAAA,QAAAhG,SAAA,oBAAAA,SAAA,KA1RoGzH,EAAE,CAAA0H,iBAAA,CA0RXf,cAAc,EAAc,CAAC;IAC5GR,IAAI,EAAEvF,SAAS;IACf+G,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,kBAAkB;MAAEE,IAAI,EAAE;QACjC,OAAO,EAAE,sBAAsB;QAC/B,WAAW,EAAE,IAAI;QACjB,qBAAqB,EAAE,qBAAqB;QAC5C,oBAAoB,EAAE,oBAAoB;QAC1C,kBAAkB,EAAE,kBAAkB;QACtC,+BAA+B,EAAE,SAAS;QAC1C,iCAAiC,EAAE,iBAAiB;QACpD;QACA,iBAAiB,EAAE,MAAM;QACzB,mBAAmB,EAAE,MAAM;QAC3B,wBAAwB,EAAE,MAAM;QAChC,yBAAyB,EAAE,MAAM;QACjC;QACA;QACA;QACA,SAAS,EAAE;MACf,CAAC;MAAEV,QAAQ,EAAE,gBAAgB;MAAEoG,aAAa,EAAE3M,iBAAiB,CAACiL,IAAI;MAAE2B,eAAe,EAAE3M,uBAAuB,CAAC4M,MAAM;MAAErG,UAAU,EAAE,IAAI;MAAEsG,OAAO,EAAE,CAACvM,SAAS,EAAEC,qBAAqB,CAAC;MAAEiL,QAAQ,EAAE,81CAA81C;MAAEiB,MAAM,EAAE,CAAC,y5UAAy5U;IAAE,CAAC;EACj9X,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEpH,IAAI,EAAE5D,aAAa;IAAEqL,UAAU,EAAE,CAAC;MACnDzH,IAAI,EAAEpF;IACV,CAAC,EAAE;MACCoF,IAAI,EAAEnF,MAAM;MACZ2G,IAAI,EAAE,CAAClF,eAAe;IAC1B,CAAC;EAAE,CAAC,EAAE;IAAE0D,IAAI,EAAEnG,EAAE,CAACW;EAAW,CAAC,EAAE;IAAEwF,IAAI,EAAEnG,EAAE,CAACgG;EAAkB,CAAC,EAAE;IAAEG,IAAI,EAAE3E,EAAE,CAACoJ;EAAa,CAAC,EAAE;IAAEzE,IAAI,EAAE1E,EAAE,CAACoJ;EAA0B,CAAC,EAAE;IAAE1E,IAAI,EAAE0H,SAAS;IAAED,UAAU,EAAE,CAAC;MAC9JzH,IAAI,EAAEpF;IACV,CAAC,EAAE;MACCoF,IAAI,EAAEnF,MAAM;MACZ2G,IAAI,EAAE,CAACjH,qBAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAEyF,IAAI,EAAE0H,SAAS;IAAED,UAAU,EAAE,CAAC;MAClCzH,IAAI,EAAEpF;IACV,CAAC,EAAE;MACCoF,IAAI,EAAEnF,MAAM;MACZ2G,IAAI,EAAE,CAACjF,yBAAyB;IACpC,CAAC;EAAE,CAAC,EAAE;IAAEyD,IAAI,EAAE0H,SAAS;IAAED,UAAU,EAAE,CAAC;MAClCzH,IAAI,EAAElF,SAAS;MACf0G,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEU,EAAE,EAAE,CAAC;MAC9BlC,IAAI,EAAE3F;IACV,CAAC,CAAC;IAAEuC,IAAI,EAAE,CAAC;MACPoD,IAAI,EAAE3F;IACV,CAAC,CAAC;IAAEqL,SAAS,EAAE,CAAC;MACZ1F,IAAI,EAAE3F,KAAK;MACXmH,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEoE,cAAc,EAAE,CAAC;MACjB5F,IAAI,EAAE3F,KAAK;MACXmH,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEqE,eAAe,EAAE,CAAC;MAClB7F,IAAI,EAAE3F,KAAK;MACXmH,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEoB,aAAa,EAAE,CAAC;MAChB5C,IAAI,EAAE3F,KAAK;MACXmH,IAAI,EAAE,CAAC;QAAEK,SAAS,EAAE5H;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0I,QAAQ,EAAE,CAAC;MACX3C,IAAI,EAAE3F,KAAK;MACXmH,IAAI,EAAE,CAAC;QACCK,SAAS,EAAG7F,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG1B,eAAe,CAAC0B,KAAK;MACpE,CAAC;IACT,CAAC,CAAC;IAAEwB,OAAO,EAAE,CAAC;MACVwC,IAAI,EAAE3F,KAAK;MACXmH,IAAI,EAAE,CAAC;QAAEK,SAAS,EAAE5H;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE+B,KAAK,EAAE,CAAC;MACRgE,IAAI,EAAE3F;IACV,CAAC,CAAC;IAAE0C,aAAa,EAAE,CAAC;MAChBiD,IAAI,EAAE3F;IACV,CAAC,CAAC;IAAEqD,QAAQ,EAAE,CAAC;MACXsC,IAAI,EAAE3F,KAAK;MACXmH,IAAI,EAAE,CAAC;QAAEK,SAAS,EAAE5H;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2D,QAAQ,EAAE,CAAC;MACXoC,IAAI,EAAE3F,KAAK;MACXmH,IAAI,EAAE,CAAC;QAAEK,SAAS,EAAE5H;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0C,KAAK,EAAE,CAAC;MACRqD,IAAI,EAAE3F;IACV,CAAC,CAAC;IAAE6D,MAAM,EAAE,CAAC;MACT8B,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAEgJ,aAAa,EAAE,CAAC;MAChBnD,IAAI,EAAEjF,SAAS;MACfyG,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAE0D,cAAc,EAAE,CAAC;MACjBlF,IAAI,EAAEjF,SAAS;MACfyG,IAAI,EAAE,CAAC,WAAW,EAAE;QAAEmG,IAAI,EAAEnN,UAAU;QAAEoN,MAAM,EAAE;MAAK,CAAC;IAC1D,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMC,cAAc,CAAC;EACjB;IAAS,IAAI,CAACpI,IAAI,YAAAqI,uBAAAnI,CAAA;MAAA,YAAAA,CAAA,IAAwFkI,cAAc;IAAA,CAAkD;EAAE;EAC5K;IAAS,IAAI,CAACE,IAAI,kBAhX8ElO,EAAE,CAAAmO,gBAAA;MAAAhI,IAAA,EAgXS6H;IAAc,EAAwJ;EAAE;EACnR;IAAS,IAAI,CAACI,IAAI,kBAjX8EpO,EAAE,CAAAqO,gBAAA;MAAAV,OAAA,GAiXmCrM,eAAe,EAAEK,YAAY,EAAEJ,eAAe,EAAEoF,cAAc,EAAErF,eAAe;IAAA,EAAI;EAAE;AAC9N;AACA;EAAA,QAAAmG,SAAA,oBAAAA,SAAA,KAnXoGzH,EAAE,CAAA0H,iBAAA,CAmXXsG,cAAc,EAAc,CAAC;IAC5G7H,IAAI,EAAEhF,QAAQ;IACdwG,IAAI,EAAE,CAAC;MACCgG,OAAO,EAAE,CAACrM,eAAe,EAAEK,YAAY,EAAEJ,eAAe,EAAEgB,aAAa,EAAEoE,cAAc,CAAC;MACxF2H,OAAO,EAAE,CAAChN,eAAe,EAAEiB,aAAa,EAAEoE,cAAc;IAC5D,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASjE,yBAAyB,EAAEG,iCAAiC,EAAEJ,eAAe,EAAEL,sCAAsC,EAAEuE,cAAc,EAAE3E,cAAc,EAAEO,aAAa,EAAEyL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}