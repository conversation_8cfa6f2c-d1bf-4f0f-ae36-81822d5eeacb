// Instagram-style Header
.instagram-header {
  ion-toolbar {
    --background: #ffffff;
    --border-color: #dbdbdb;
    --border-width: 0 0 1px 0;
    --min-height: 44px;
  }
  
  .instagram-logo {
    h1 {
      font-family: 'Billabong', cursive, sans-serif;
      font-size: 24px;
      font-weight: normal;
      margin: 0;
      color: #262626;
      letter-spacing: 1px;
    }
  }
  
  ion-button {
    --color: #262626;
    --padding-start: 8px;
    --padding-end: 8px;
    
    ion-icon {
      font-size: 24px;
    }
  }
}

// Instagram-style Main Content
.instagram-feed {
  background: #ffffff;
  
  ion-content {
    --background: #fafafa;
  }
}

// Loading State
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: 16px;
  
  ion-spinner {
    --color: #262626;
  }
  
  p {
    color: #8e8e8e;
    font-size: 14px;
  }
}

// Instagram-style Stories Section
.stories-section {
  background: #ffffff;
  border-bottom: 1px solid #dbdbdb;
  padding: 12px 0;
  
  .stories-slider {
    padding: 0 16px;
    
    .story-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      cursor: pointer;
      
      .story-avatar {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        position: relative;
        
        &.has-story {
          background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
          padding: 2px;
        }
        
        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          object-fit: cover;
          border: 2px solid #ffffff;
        }
      }
      
      .add-story-circle {
        width: 52px;
        height: 52px;
        border-radius: 50%;
        background: #fafafa;
        border: 1px dashed #dbdbdb;
        display: flex;
        align-items: center;
        justify-content: center;
        
        ion-icon {
          color: #262626;
          font-size: 24px;
        }
      }
      
      .story-username {
        font-size: 12px;
        color: #262626;
        text-align: center;
        max-width: 64px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

// Instagram-style Posts Feed
.posts-feed {
  background: #ffffff;
  
  .post-item {
    margin-bottom: 24px;
    border-bottom: 1px solid #efefef;
    
    .post-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      
      .user-info {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .user-avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          object-fit: cover;
        }
        
        .user-details {
          display: flex;
          flex-direction: column;
          
          .username {
            font-size: 14px;
            font-weight: 600;
            color: #262626;
          }
          
          .location {
            font-size: 12px;
            color: #8e8e8e;
          }
        }
      }
      
      ion-button {
        --color: #262626;
        --padding-start: 4px;
        --padding-end: 4px;
      }
    }
    
    .post-media {
      position: relative;
      width: 100%;
      
      .post-image {
        width: 100%;
        height: 375px;
        object-fit: cover;
        display: block;
      }
      
      .media-overlay {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        
        ion-icon {
          color: rgba(255, 255, 255, 0.8);
          font-size: 48px;
        }
      }
    }
    
    .post-actions {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 16px 0;
      
      .action-buttons {
        display: flex;
        gap: 8px;
        
        ion-button {
          --padding-start: 0;
          --padding-end: 8px;
          
          ion-icon {
            font-size: 24px;
          }
        }
      }
    }
    
    .post-info {
      padding: 0 16px 16px;
      
      .likes-count {
        font-size: 14px;
        color: #262626;
        margin-bottom: 4px;
      }
      
      .post-caption {
        font-size: 14px;
        color: #262626;
        margin-bottom: 4px;
        
        .username {
          font-weight: 600;
          margin-right: 4px;
        }
        
        .caption-text {
          color: #262626;
        }
      }
      
      .view-comments {
        font-size: 14px;
        color: #8e8e8e;
        margin-bottom: 4px;
        cursor: pointer;
      }
      
      .post-time {
        font-size: 10px;
        color: #8e8e8e;
        text-transform: uppercase;
        letter-spacing: 0.2px;
      }
    }
  }
}

// Instagram-style Suggested Section
.suggested-section {
  background: #ffffff;
  border-bottom: 1px solid #dbdbdb;
  padding: 16px;
  
  .section-title {
    margin-bottom: 12px;
    
    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      margin: 0;
    }
  }
  
  .suggested-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2px;
    
    .suggested-item {
      position: relative;
      aspect-ratio: 1;
      cursor: pointer;
      
      .suggested-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .suggested-overlay {
        position: absolute;
        bottom: 8px;
        left: 8px;
        
        .product-price {
          background: rgba(0, 0, 0, 0.7);
          color: white;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 600;
        }
      }
    }
  }
}

// Load More Section
.load-more-section {
  padding: 16px;
  text-align: center;
  
  ion-button {
    --color: #8e8e8e;
    font-size: 14px;
    
    ion-icon {
      margin-right: 8px;
    }
  }
}

// Instagram Bottom Spacing
.instagram-bottom-spacing {
  height: 60px;
  background: #fafafa;
}
