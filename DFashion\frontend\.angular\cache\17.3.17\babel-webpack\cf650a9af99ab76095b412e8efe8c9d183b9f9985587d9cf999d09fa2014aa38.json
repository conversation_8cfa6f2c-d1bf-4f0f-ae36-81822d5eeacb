{"ast": null, "code": "import { MatTableDataSource } from '@angular/material/table';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { Subject } from 'rxjs';\nimport { takeUntil, debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport { FormControl } from '@angular/forms';\nimport { UserDialogComponent } from './user-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/admin-api.service\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/table\";\nimport * as i10 from \"@angular/material/paginator\";\nimport * as i11 from \"@angular/material/sort\";\nimport * as i12 from \"@angular/material/input\";\nimport * as i13 from \"@angular/material/form-field\";\nimport * as i14 from \"@angular/material/select\";\nimport * as i15 from \"@angular/material/core\";\nimport * as i16 from \"@angular/material/progress-spinner\";\nimport * as i17 from \"@angular/material/tooltip\";\nimport * as i18 from \"../pipes/role.pipe\";\nconst _c0 = () => [5, 10, 25, 50];\nfunction UserManagementComponent_mat_option_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const role_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", role_r1.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", role_r1.label, \" \");\n  }\n}\nfunction UserManagementComponent_mat_option_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dept_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", dept_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", dept_r2.label, \" \");\n  }\n}\nfunction UserManagementComponent_mat_option_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r3.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", status_r3.label, \" \");\n  }\n}\nfunction UserManagementComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵelement(1, \"mat-spinner\", 21);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading users...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserManagementComponent_div_47_th_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 37);\n    i0.ɵɵtext(1, \"Name\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserManagementComponent_div_47_td_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 38)(1, \"div\", 39)(2, \"div\", 40);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 41)(5, \"div\", 42);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 43);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const user_r6 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", user_r6.fullName.charAt(0).toUpperCase(), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r6.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(\"@\" + user_r6.username);\n  }\n}\nfunction UserManagementComponent_div_47_th_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 37);\n    i0.ɵɵtext(1, \"Email\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserManagementComponent_div_47_td_7_mat_icon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 46);\n    i0.ɵɵtext(1, \"verified\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserManagementComponent_div_47_td_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 38)(1, \"div\", 44);\n    i0.ɵɵtext(2);\n    i0.ɵɵtemplate(3, UserManagementComponent_div_47_td_7_mat_icon_3_Template, 2, 0, \"mat-icon\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", user_r7.email, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", user_r7.isVerified);\n  }\n}\nfunction UserManagementComponent_div_47_th_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 37);\n    i0.ɵɵtext(1, \"Role\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserManagementComponent_div_47_td_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 38)(1, \"span\", 47);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"role\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r8 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", ctx_r4.getRoleColor(user_r8.role));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(3, 3, user_r8.role), \" \");\n  }\n}\nfunction UserManagementComponent_div_47_th_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 37);\n    i0.ɵɵtext(1, \"Department\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserManagementComponent_div_47_td_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 38)(1, \"span\", 48);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r9 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getDepartmentDisplay(user_r9.department), \" \");\n  }\n}\nfunction UserManagementComponent_div_47_th_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 49);\n    i0.ɵɵtext(1, \"Status\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserManagementComponent_div_47_td_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 38)(1, \"div\", 50);\n    i0.ɵɵelement(2, \"div\", 51);\n    i0.ɵɵelementStart(3, \"span\", 52);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const user_r10 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r4.getStatusColor(user_r10));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getStatusText(user_r10));\n  }\n}\nfunction UserManagementComponent_div_47_th_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 37);\n    i0.ɵɵtext(1, \"Last Login\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserManagementComponent_div_47_td_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 38)(1, \"span\", 53);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r11 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.formatDate(user_r11.lastLogin));\n  }\n}\nfunction UserManagementComponent_div_47_th_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 49);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserManagementComponent_div_47_td_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 38)(1, \"div\", 54)(2, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function UserManagementComponent_div_47_td_22_Template_button_click_2_listener() {\n      const user_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.openUserDialog(user_r13));\n    });\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function UserManagementComponent_div_47_td_22_Template_button_click_5_listener() {\n      const user_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.toggleUserStatus(user_r13));\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function UserManagementComponent_div_47_td_22_Template_button_click_8_listener() {\n      const user_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.deleteUser(user_r13));\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"delete\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const user_r13 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"matTooltip\", \"Edit user\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"matTooltip\", user_r13.isActive ? \"Deactivate user\" : \"Activate user\")(\"color\", user_r13.isActive ? \"warn\" : \"primary\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r13.isActive ? \"block\" : \"check_circle\");\n  }\n}\nfunction UserManagementComponent_div_47_tr_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 58);\n  }\n}\nfunction UserManagementComponent_div_47_tr_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 59);\n  }\n}\nfunction UserManagementComponent_div_47_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"people_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No users found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Try adjusting your search criteria or add a new user.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserManagementComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"table\", 23);\n    i0.ɵɵlistener(\"matSortChange\", function UserManagementComponent_div_47_Template_table_matSortChange_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSortChange());\n    });\n    i0.ɵɵelementContainerStart(2, 24);\n    i0.ɵɵtemplate(3, UserManagementComponent_div_47_th_3_Template, 2, 0, \"th\", 25)(4, UserManagementComponent_div_47_td_4_Template, 9, 3, \"td\", 26);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(5, 27);\n    i0.ɵɵtemplate(6, UserManagementComponent_div_47_th_6_Template, 2, 0, \"th\", 25)(7, UserManagementComponent_div_47_td_7_Template, 4, 2, \"td\", 26);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(8, 28);\n    i0.ɵɵtemplate(9, UserManagementComponent_div_47_th_9_Template, 2, 0, \"th\", 25)(10, UserManagementComponent_div_47_td_10_Template, 4, 5, \"td\", 26);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(11, 29);\n    i0.ɵɵtemplate(12, UserManagementComponent_div_47_th_12_Template, 2, 0, \"th\", 25)(13, UserManagementComponent_div_47_td_13_Template, 3, 1, \"td\", 26);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(14, 30);\n    i0.ɵɵtemplate(15, UserManagementComponent_div_47_th_15_Template, 2, 0, \"th\", 31)(16, UserManagementComponent_div_47_td_16_Template, 5, 3, \"td\", 26);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(17, 32);\n    i0.ɵɵtemplate(18, UserManagementComponent_div_47_th_18_Template, 2, 0, \"th\", 25)(19, UserManagementComponent_div_47_td_19_Template, 3, 1, \"td\", 26);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(20, 33);\n    i0.ɵɵtemplate(21, UserManagementComponent_div_47_th_21_Template, 2, 0, \"th\", 31)(22, UserManagementComponent_div_47_td_22_Template, 11, 4, \"td\", 26);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(23, UserManagementComponent_div_47_tr_23_Template, 1, 0, \"tr\", 34)(24, UserManagementComponent_div_47_tr_24_Template, 1, 0, \"tr\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, UserManagementComponent_div_47_div_25_Template, 7, 0, \"div\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"dataSource\", ctx_r4.dataSource);\n    i0.ɵɵadvance(22);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r4.displayedColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r4.displayedColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.dataSource.data.length === 0);\n  }\n}\nexport let UserManagementComponent = /*#__PURE__*/(() => {\n  class UserManagementComponent {\n    constructor(apiService, dialog, snackBar) {\n      this.apiService = apiService;\n      this.dialog = dialog;\n      this.snackBar = snackBar;\n      this.destroy$ = new Subject();\n      this.displayedColumns = ['fullName', 'email', 'role', 'department', 'status', 'lastLogin', 'actions'];\n      this.dataSource = new MatTableDataSource([]);\n      this.isLoading = false;\n      this.totalUsers = 0;\n      // Filters\n      this.searchControl = new FormControl('');\n      this.roleFilter = new FormControl('');\n      this.departmentFilter = new FormControl('');\n      this.statusFilter = new FormControl('');\n      this.roles = [{\n        value: '',\n        label: 'All Roles'\n      }, {\n        value: 'super_admin',\n        label: 'Super Admin'\n      }, {\n        value: 'admin',\n        label: 'Admin'\n      }, {\n        value: 'sales_manager',\n        label: 'Sales Manager'\n      }, {\n        value: 'marketing_manager',\n        label: 'Marketing Manager'\n      }, {\n        value: 'account_manager',\n        label: 'Account Manager'\n      }, {\n        value: 'support_manager',\n        label: 'Support Manager'\n      }, {\n        value: 'customer',\n        label: 'Customer'\n      }, {\n        value: 'vendor',\n        label: 'Vendor'\n      }];\n      this.departments = [{\n        value: '',\n        label: 'All Departments'\n      }, {\n        value: 'administration',\n        label: 'Administration'\n      }, {\n        value: 'sales',\n        label: 'Sales'\n      }, {\n        value: 'marketing',\n        label: 'Marketing'\n      }, {\n        value: 'accounting',\n        label: 'Accounting'\n      }, {\n        value: 'support',\n        label: 'Support'\n      }];\n      this.statuses = [{\n        value: '',\n        label: 'All Statuses'\n      }, {\n        value: 'true',\n        label: 'Active'\n      }, {\n        value: 'false',\n        label: 'Inactive'\n      }];\n    }\n    ngOnInit() {\n      this.setupFilters();\n      this.loadUsers();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    setupFilters() {\n      // Search filter\n      this.searchControl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(() => {\n        this.loadUsers();\n      });\n      // Other filters\n      [this.roleFilter, this.departmentFilter, this.statusFilter].forEach(control => {\n        control.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(() => {\n          this.loadUsers();\n        });\n      });\n    }\n    loadUsers() {\n      this.isLoading = true;\n      const params = {\n        page: this.paginator?.pageIndex ? this.paginator.pageIndex + 1 : 1,\n        limit: this.paginator?.pageSize || 10,\n        search: this.searchControl.value || '',\n        role: this.roleFilter.value || '',\n        department: this.departmentFilter.value || '',\n        isActive: this.statusFilter.value || '',\n        sortBy: this.sort?.active || 'createdAt',\n        sortOrder: this.sort?.direction || 'desc'\n      };\n      this.apiService.getUsers(params).pipe(takeUntil(this.destroy$)).subscribe({\n        next: response => {\n          this.dataSource.data = response.data.users || [];\n          this.totalUsers = response.data.pagination.totalUsers || 0;\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Failed to load users:', error);\n          this.isLoading = false;\n          this.snackBar.open('Failed to load users', 'Close', {\n            duration: 3000,\n            panelClass: ['error-snackbar']\n          });\n          // Initialize empty data on error\n          this.dataSource.data = [];\n          this.totalUsers = 0;\n        }\n      });\n    }\n    onPageChange() {\n      this.loadUsers();\n    }\n    onSortChange() {\n      this.loadUsers();\n    }\n    clearFilters() {\n      this.searchControl.setValue('');\n      this.roleFilter.setValue('');\n      this.departmentFilter.setValue('');\n      this.statusFilter.setValue('');\n    }\n    openUserDialog(user) {\n      const dialogRef = this.dialog.open(UserDialogComponent, {\n        width: '600px',\n        data: user ? {\n          ...user\n        } : null\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        if (result) {\n          this.loadUsers();\n        }\n      });\n    }\n    toggleUserStatus(user) {\n      const action = user.isActive ? 'deactivate' : 'activate';\n      if (user.isActive) {\n        this.apiService.deleteUser(user._id).subscribe({\n          next: () => {\n            this.snackBar.open('User deactivated successfully', 'Close', {\n              duration: 3000,\n              panelClass: ['success-snackbar']\n            });\n            this.loadUsers();\n          },\n          error: error => {\n            console.error('Failed to deactivate user:', error);\n            this.snackBar.open('Failed to deactivate user', 'Close', {\n              duration: 3000,\n              panelClass: ['error-snackbar']\n            });\n          }\n        });\n      } else {\n        this.apiService.activateUser(user._id).subscribe({\n          next: () => {\n            this.snackBar.open('User activated successfully', 'Close', {\n              duration: 3000,\n              panelClass: ['success-snackbar']\n            });\n            this.loadUsers();\n          },\n          error: error => {\n            console.error('Failed to activate user:', error);\n            this.snackBar.open('Failed to activate user', 'Close', {\n              duration: 3000,\n              panelClass: ['error-snackbar']\n            });\n          }\n        });\n      }\n    }\n    deleteUser(user) {\n      if (confirm(`Are you sure you want to delete user \"${user.fullName}\"?`)) {\n        this.apiService.deleteUser(user._id).subscribe({\n          next: () => {\n            this.snackBar.open('User deleted successfully', 'Close', {\n              duration: 3000,\n              panelClass: ['success-snackbar']\n            });\n            this.loadUsers();\n          },\n          error: error => {\n            console.error('Failed to delete user:', error);\n            this.snackBar.open('Failed to delete user', 'Close', {\n              duration: 3000,\n              panelClass: ['error-snackbar']\n            });\n          }\n        });\n      }\n    }\n    exportUsers() {\n      // Export functionality\n      this.snackBar.open('Export feature coming soon!', 'Close', {\n        duration: 3000\n      });\n    }\n    getStatusColor(user) {\n      if (!user.isActive) return '#f44336';\n      if (!user.isVerified) return '#ff9800';\n      return '#4caf50';\n    }\n    getStatusText(user) {\n      if (!user.isActive) return 'Inactive';\n      if (!user.isVerified) return 'Unverified';\n      return 'Active';\n    }\n    formatDate(dateString) {\n      if (!dateString) return 'Never';\n      return new Date(dateString).toLocaleDateString('en-IN', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n    getRoleColor(role) {\n      const roleColors = {\n        'super_admin': '#e91e63',\n        'admin': '#9c27b0',\n        'sales_manager': '#2196f3',\n        'sales_executive': '#03a9f4',\n        'marketing_manager': '#ff9800',\n        'marketing_executive': '#ffc107',\n        'account_manager': '#4caf50',\n        'accountant': '#8bc34a',\n        'support_manager': '#795548',\n        'support_agent': '#9e9e9e',\n        'customer': '#607d8b',\n        'vendor': '#ff5722'\n      };\n      return roleColors[role] || '#666666';\n    }\n    getDepartmentDisplay(department) {\n      if (!department) return 'N/A';\n      return department.charAt(0).toUpperCase() + department.slice(1).toLowerCase();\n    }\n    static {\n      this.ɵfac = function UserManagementComponent_Factory(t) {\n        return new (t || UserManagementComponent)(i0.ɵɵdirectiveInject(i1.AdminApiService), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: UserManagementComponent,\n        selectors: [[\"app-user-management\"]],\n        viewQuery: function UserManagementComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(MatPaginator, 5);\n            i0.ɵɵviewQuery(MatSort, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n          }\n        },\n        decls: 49,\n        vars: 13,\n        consts: [[1, \"user-management-container\"], [1, \"page-header\"], [1, \"header-content\"], [1, \"header-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-stroked-button\", \"\", 3, \"click\"], [1, \"filters-card\"], [1, \"filters-grid\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"placeholder\", \"Search by name, email, or username\", 3, \"formControl\"], [\"matSuffix\", \"\"], [\"appearance\", \"outline\"], [3, \"formControl\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"mat-stroked-button\", \"\", 1, \"clear-filters-btn\", 3, \"click\"], [1, \"table-card\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"table-container\", 4, \"ngIf\"], [\"showFirstLastButtons\", \"\", 3, \"page\", \"length\", \"pageSize\", \"pageSizeOptions\"], [3, \"value\"], [1, \"loading-container\"], [\"diameter\", \"40\"], [1, \"table-container\"], [\"mat-table\", \"\", \"matSort\", \"\", 3, \"matSortChange\", \"dataSource\"], [\"matColumnDef\", \"fullName\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"email\"], [\"matColumnDef\", \"role\"], [\"matColumnDef\", \"department\"], [\"matColumnDef\", \"status\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"matColumnDef\", \"lastLogin\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"class\", \"no-data\", 4, \"ngIf\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\"], [\"mat-cell\", \"\"], [1, \"user-info\"], [1, \"user-avatar\"], [1, \"user-details\"], [1, \"user-name\"], [1, \"user-username\"], [1, \"email-cell\"], [\"class\", \"verified-icon\", \"title\", \"Verified\", 4, \"ngIf\"], [\"title\", \"Verified\", 1, \"verified-icon\"], [1, \"role-chip\"], [1, \"department-text\"], [\"mat-header-cell\", \"\"], [1, \"status-indicator\"], [1, \"status-dot\"], [1, \"status-text\"], [1, \"last-login\"], [1, \"actions-cell\"], [\"mat-icon-button\", \"\", 3, \"click\", \"matTooltip\"], [\"mat-icon-button\", \"\", 3, \"click\", \"matTooltip\", \"color\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Delete user\", \"color\", \"warn\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [1, \"no-data\"]],\n        template: function UserManagementComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n            i0.ɵɵtext(4, \"User Management\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p\");\n            i0.ɵɵtext(6, \"Manage users, roles, and permissions\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"div\", 3)(8, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function UserManagementComponent_Template_button_click_8_listener() {\n              return ctx.openUserDialog();\n            });\n            i0.ɵɵelementStart(9, \"mat-icon\");\n            i0.ɵɵtext(10, \"person_add\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(11, \" Add User \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"button\", 5);\n            i0.ɵɵlistener(\"click\", function UserManagementComponent_Template_button_click_12_listener() {\n              return ctx.exportUsers();\n            });\n            i0.ɵɵelementStart(13, \"mat-icon\");\n            i0.ɵɵtext(14, \"download\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(15, \" Export \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(16, \"mat-card\", 6)(17, \"mat-card-content\")(18, \"div\", 7)(19, \"mat-form-field\", 8)(20, \"mat-label\");\n            i0.ɵɵtext(21, \"Search users\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(22, \"input\", 9);\n            i0.ɵɵelementStart(23, \"mat-icon\", 10);\n            i0.ɵɵtext(24, \"search\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(25, \"mat-form-field\", 11)(26, \"mat-label\");\n            i0.ɵɵtext(27, \"Role\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"mat-select\", 12);\n            i0.ɵɵtemplate(29, UserManagementComponent_mat_option_29_Template, 2, 2, \"mat-option\", 13);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(30, \"mat-form-field\", 11)(31, \"mat-label\");\n            i0.ɵɵtext(32, \"Department\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"mat-select\", 12);\n            i0.ɵɵtemplate(34, UserManagementComponent_mat_option_34_Template, 2, 2, \"mat-option\", 13);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(35, \"mat-form-field\", 11)(36, \"mat-label\");\n            i0.ɵɵtext(37, \"Status\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"mat-select\", 12);\n            i0.ɵɵtemplate(39, UserManagementComponent_mat_option_39_Template, 2, 2, \"mat-option\", 13);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(40, \"button\", 14);\n            i0.ɵɵlistener(\"click\", function UserManagementComponent_Template_button_click_40_listener() {\n              return ctx.clearFilters();\n            });\n            i0.ɵɵelementStart(41, \"mat-icon\");\n            i0.ɵɵtext(42, \"clear\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(43, \" Clear Filters \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(44, \"mat-card\", 15)(45, \"mat-card-content\");\n            i0.ɵɵtemplate(46, UserManagementComponent_div_46_Template, 4, 0, \"div\", 16)(47, UserManagementComponent_div_47_Template, 26, 4, \"div\", 17);\n            i0.ɵɵelementStart(48, \"mat-paginator\", 18);\n            i0.ɵɵlistener(\"page\", function UserManagementComponent_Template_mat_paginator_page_48_listener() {\n              return ctx.onPageChange();\n            });\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(22);\n            i0.ɵɵproperty(\"formControl\", ctx.searchControl);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"formControl\", ctx.roleFilter);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.roles);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"formControl\", ctx.departmentFilter);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.departments);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"formControl\", ctx.statusFilter);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngForOf\", ctx.statuses);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"length\", ctx.totalUsers)(\"pageSize\", 10)(\"pageSizeOptions\", i0.ɵɵpureFunction0(12, _c0));\n          }\n        },\n        dependencies: [i4.NgForOf, i4.NgIf, i5.DefaultValueAccessor, i5.NgControlStatus, i5.FormControlDirective, i6.MatIcon, i7.MatButton, i7.MatIconButton, i8.MatCard, i8.MatCardContent, i9.MatTable, i9.MatHeaderCellDef, i9.MatHeaderRowDef, i9.MatColumnDef, i9.MatCellDef, i9.MatRowDef, i9.MatHeaderCell, i9.MatCell, i9.MatHeaderRow, i9.MatRow, i10.MatPaginator, i11.MatSort, i11.MatSortHeader, i12.MatInput, i13.MatFormField, i13.MatLabel, i13.MatSuffix, i14.MatSelect, i15.MatOption, i16.MatProgressSpinner, i17.MatTooltip, i18.RolePipe],\n        styles: [\".user-management-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:1.5rem}.page-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:1rem}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0 0 .5rem;font-size:2rem;font-weight:600;color:#333}.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#666;font-size:1rem}.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]{display:flex;gap:1rem;align-items:center}.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.filters-card[_ngcontent-%COMP%]   .filters-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:2fr 1fr 1fr 1fr auto;gap:1rem;align-items:center}.filters-card[_ngcontent-%COMP%]   .filters-grid[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]{min-width:300px}.filters-card[_ngcontent-%COMP%]   .filters-grid[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%]{height:56px;display:flex;align-items:center;gap:.5rem}.table-card[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:3rem;gap:1rem}.table-card[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;margin:0}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]{overflow-x:auto}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]{width:100%;min-width:800px}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;display:flex;align-items:center;justify-content:center;font-weight:600;font-size:1rem}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%]{font-weight:500;color:#333;margin-bottom:.25rem}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   .user-username[_ngcontent-%COMP%]{color:#666;font-size:.85rem}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .email-cell[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .email-cell[_ngcontent-%COMP%]   .verified-icon[_ngcontent-%COMP%]{color:#4caf50;font-size:1.2rem;width:1.2rem;height:1.2rem}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .role-chip[_ngcontent-%COMP%]{color:#fff;font-size:.8rem;font-weight:500;padding:.25rem .75rem;border-radius:12px;min-height:auto;height:auto}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .department-text[_ngcontent-%COMP%]{color:#666;font-size:.9rem}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]   .status-dot[_ngcontent-%COMP%]{width:8px;height:8px;border-radius:50%}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{font-size:.9rem;font-weight:500}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .last-login[_ngcontent-%COMP%]{color:#666;font-size:.85rem}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .actions-cell[_ngcontent-%COMP%]{display:flex;gap:.25rem}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .actions-cell[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:36px;height:36px}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .actions-cell[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.2rem;width:1.2rem;height:1.2rem}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]{text-align:center;padding:3rem;color:#666}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:4rem;width:4rem;height:4rem;margin-bottom:1rem;opacity:.5}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 .5rem;font-weight:500}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:.9rem}.role-chip[style*=\\\"rgb(102, 126, 234)\\\"][_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2)!important}@media (max-width: 1200px){.filters-card[_ngcontent-%COMP%]   .filters-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:1rem}.filters-card[_ngcontent-%COMP%]   .filters-grid[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]{min-width:auto}.filters-card[_ngcontent-%COMP%]   .filters-grid[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%]{height:48px;justify-self:start}}@media (max-width: 768px){.page-header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:1rem}.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]{width:100%;justify-content:flex-start}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%]{width:32px;height:32px;font-size:.9rem}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%]{font-size:.9rem}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   .user-username[_ngcontent-%COMP%]{font-size:.8rem}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .actions-cell[_ngcontent-%COMP%]{flex-direction:column;gap:.125rem}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .actions-cell[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:32px;height:32px}.table-card[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .actions-cell[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1rem;width:1rem;height:1rem}}  .mat-form-field-appearance-outline .mat-form-field-outline{color:#e0e0e0}  .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick{color:#667eea}  .mat-form-field-label{color:#666}  .mat-form-field-appearance-outline.mat-focused .mat-form-field-label{color:#667eea}  .mat-table .mat-header-cell{font-weight:600;color:#333;border-bottom:2px solid #f0f0f0}  .mat-table .mat-cell{border-bottom:1px solid #f8f8f8}  .mat-table .mat-row:hover{background:#f8f9fa}  .mat-paginator{border-top:1px solid #f0f0f0;margin-top:1rem}  .success-snackbar{background-color:#4caf50!important;color:#fff!important}  .error-snackbar{background-color:#f44336!important;color:#fff!important}.user-info[_ngcontent-%COMP%], .actions-cell[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{transition:all .2s ease}.actions-cell[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{transform:scale(1.1)}\"]\n      });\n    }\n  }\n  return UserManagementComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}