{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction VendorOrdersComponent_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function VendorOrdersComponent_button_22_Template_button_click_0_listener() {\n      const filter_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.setActiveFilter(filter_r2.key));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r2.activeFilter === filter_r2.key);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", filter_r2.label, \" (\", ctx_r2.getOrdersByStatus(filter_r2.key).length, \") \");\n  }\n}\nfunction VendorOrdersComponent_div_23_div_1_div_25_div_8_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Size: \", item_r5.size, \"\");\n  }\n}\nfunction VendorOrdersComponent_div_23_div_1_div_25_div_8_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Color: \", item_r5.color, \"\");\n  }\n}\nfunction VendorOrdersComponent_div_23_div_1_div_25_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵtemplate(1, VendorOrdersComponent_div_23_div_1_div_25_div_8_span_1_Template, 2, 1, \"span\", 41)(2, VendorOrdersComponent_div_23_div_1_div_25_div_8_span_2_Template, 2, 1, \"span\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.size);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.color);\n  }\n}\nfunction VendorOrdersComponent_div_23_div_1_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"img\", 36);\n    i0.ɵɵelementStart(2, \"div\", 37)(3, \"h4\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, VendorOrdersComponent_div_23_div_1_div_25_div_8_Template, 3, 2, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 39);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r2.getImageUrl(item_r5.product.images[0]), i0.ɵɵsanitizeUrl)(\"alt\", item_r5.product.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r5.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"Qty: \", item_r5.quantity, \" \\u00D7 \\u20B9\", i0.ɵɵpipeBind2(7, 7, item_r5.price, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r5.size || item_r5.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind2(11, 10, item_r5.quantity * item_r5.price, \"1.0-0\"), \" \");\n  }\n}\nfunction VendorOrdersComponent_div_23_div_1_button_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function VendorOrdersComponent_div_23_div_1_button_32_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const order_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.updateOrderStatus(order_r7));\n    });\n    i0.ɵɵelement(1, \"i\", 43);\n    i0.ɵɵtext(2, \" Confirm Order \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VendorOrdersComponent_div_23_div_1_button_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function VendorOrdersComponent_div_23_div_1_button_33_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const order_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.updateOrderStatus(order_r7));\n    });\n    i0.ɵɵelement(1, \"i\", 45);\n    i0.ɵɵtext(2, \" Mark as Shipped \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VendorOrdersComponent_div_23_div_1_button_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function VendorOrdersComponent_div_23_div_1_button_34_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const order_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.updateOrderStatus(order_r7));\n    });\n    i0.ɵɵelement(1, \"i\", 47);\n    i0.ɵɵtext(2, \" Mark as Delivered \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VendorOrdersComponent_div_23_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14)(2, \"div\", 15)(3, \"h3\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 16);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 17)(9, \"span\", 18);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 19);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 20)(16, \"div\", 21);\n    i0.ɵɵelement(17, \"i\", 22);\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 23);\n    i0.ɵɵelement(21, \"i\", 24);\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 25);\n    i0.ɵɵtemplate(25, VendorOrdersComponent_div_23_div_1_div_25_Template, 12, 13, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 27)(27, \"h4\");\n    i0.ɵɵtext(28, \"Shipping Address:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"p\");\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 28);\n    i0.ɵɵtemplate(32, VendorOrdersComponent_div_23_div_1_button_32_Template, 3, 0, \"button\", 29)(33, VendorOrdersComponent_div_23_div_1_button_33_Template, 3, 0, \"button\", 30)(34, VendorOrdersComponent_div_23_div_1_button_34_Template, 3, 0, \"button\", 31);\n    i0.ɵɵelementStart(35, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function VendorOrdersComponent_div_23_div_1_Template_button_click_35_listener() {\n      const order_r7 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.viewOrderDetails(order_r7));\n    });\n    i0.ɵɵelement(36, \"i\", 33);\n    i0.ɵɵtext(37, \" View Details \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function VendorOrdersComponent_div_23_div_1_Template_button_click_38_listener() {\n      const order_r7 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.contactCustomer(order_r7));\n    });\n    i0.ɵɵelement(39, \"i\", 24);\n    i0.ɵɵtext(40, \" Contact Customer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const order_r7 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Order #\", order_r7.orderNumber, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(7, 16, order_r7.createdAt, \"medium\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(order_r7.status);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(11, 19, order_r7.status));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(14, 21, order_r7.total, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(order_r7.customer.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(order_r7.customer.phone);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", order_r7.items);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate4(\"\", order_r7.shippingAddress.addressLine1, \", \", order_r7.shippingAddress.city, \", \", order_r7.shippingAddress.state, \" - \", order_r7.shippingAddress.pincode, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", order_r7.status === \"pending\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", order_r7.status === \"confirmed\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", order_r7.status === \"shipped\");\n  }\n}\nfunction VendorOrdersComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵtemplate(1, VendorOrdersComponent_div_23_div_1_Template, 41, 24, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.filteredOrders);\n  }\n}\nfunction VendorOrdersComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49);\n    i0.ɵɵelement(2, \"i\", 50);\n    i0.ɵɵelementStart(3, \"h2\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"No \", ctx_r2.activeFilter === \"all\" ? \"\" : ctx_r2.activeFilter, \" orders\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getEmptyMessage());\n  }\n}\nexport let VendorOrdersComponent = /*#__PURE__*/(() => {\n  class VendorOrdersComponent {\n    constructor() {\n      this.orders = [];\n      this.activeFilter = 'all';\n      this.filteredOrders = [];\n      this.filters = [{\n        key: 'all',\n        label: 'All Orders'\n      }, {\n        key: 'pending',\n        label: 'Pending'\n      }, {\n        key: 'confirmed',\n        label: 'Confirmed'\n      }, {\n        key: 'shipped',\n        label: 'Shipped'\n      }, {\n        key: 'delivered',\n        label: 'Delivered'\n      }, {\n        key: 'cancelled',\n        label: 'Cancelled'\n      }];\n    }\n    ngOnInit() {\n      this.loadOrders();\n    }\n    loadOrders() {\n      // Load vendor orders from API\n      this.orders = [];\n      this.filterOrders();\n    }\n    setActiveFilter(filter) {\n      this.activeFilter = filter;\n      this.filterOrders();\n    }\n    filterOrders() {\n      if (this.activeFilter === 'all') {\n        this.filteredOrders = this.orders;\n      } else {\n        this.filteredOrders = this.orders.filter(order => order.status === this.activeFilter);\n      }\n    }\n    getOrdersByStatus(status) {\n      if (status === 'all') {\n        return this.orders;\n      }\n      return this.orders.filter(order => order.status === status);\n    }\n    getTotalOrders() {\n      return this.orders.length;\n    }\n    getPendingOrders() {\n      return this.orders.filter(order => order.status === 'pending').length;\n    }\n    getTotalRevenue() {\n      return this.orders.reduce((total, order) => total + order.total, 0);\n    }\n    getImageUrl(image) {\n      if (typeof image === 'string') {\n        return image;\n      }\n      return image?.url || '/assets/images/placeholder.jpg';\n    }\n    getEmptyMessage() {\n      switch (this.activeFilter) {\n        case 'pending':\n          return 'No pending orders at the moment.';\n        case 'confirmed':\n          return 'No confirmed orders to process.';\n        case 'shipped':\n          return 'No shipped orders currently.';\n        case 'delivered':\n          return 'No delivered orders yet.';\n        case 'cancelled':\n          return 'No cancelled orders.';\n        default:\n          return 'No orders received yet. Start promoting your products!';\n      }\n    }\n    updateOrderStatus(order) {\n      // TODO: Implement order status update API\n      const statusFlow = {\n        'pending': 'confirmed',\n        'confirmed': 'shipped',\n        'shipped': 'delivered'\n      };\n      const newStatus = statusFlow[order.status];\n      if (newStatus) {\n        order.status = newStatus;\n        this.filterOrders();\n        alert(`Order #${order.orderNumber} status updated to ${newStatus}`);\n      }\n    }\n    viewOrderDetails(order) {\n      // TODO: Navigate to order details page\n      console.log('View order details:', order);\n    }\n    contactCustomer(order) {\n      // TODO: Open contact options\n      console.log('Contact customer:', order.customer);\n    }\n    static {\n      this.ɵfac = function VendorOrdersComponent_Factory(t) {\n        return new (t || VendorOrdersComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: VendorOrdersComponent,\n        selectors: [[\"app-vendor-orders\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 25,\n        vars: 9,\n        consts: [[1, \"vendor-orders-container\"], [1, \"header\"], [1, \"order-stats\"], [1, \"stat-item\"], [1, \"stat-value\"], [1, \"stat-label\"], [1, \"filter-tabs\"], [\"class\", \"filter-tab\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"orders-list\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"filter-tab\", 3, \"click\"], [1, \"orders-list\"], [\"class\", \"order-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"order-card\"], [1, \"order-header\"], [1, \"order-info\"], [1, \"order-date\"], [1, \"order-status\"], [1, \"status-badge\"], [1, \"order-total\"], [1, \"order-customer\"], [1, \"customer-info\"], [1, \"fas\", \"fa-user\"], [1, \"customer-contact\"], [1, \"fas\", \"fa-phone\"], [1, \"order-items\"], [\"class\", \"item\", 4, \"ngFor\", \"ngForOf\"], [1, \"order-address\"], [1, \"order-actions\"], [\"class\", \"btn-primary\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn-secondary\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn-success\", 3, \"click\", 4, \"ngIf\"], [1, \"btn-view\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [1, \"btn-contact\", 3, \"click\"], [1, \"item\"], [3, \"src\", \"alt\"], [1, \"item-details\"], [\"class\", \"item-variants\", 4, \"ngIf\"], [1, \"item-total\"], [1, \"item-variants\"], [4, \"ngIf\"], [1, \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-check\"], [1, \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-truck\"], [1, \"btn-success\", 3, \"click\"], [1, \"fas\", \"fa-box\"], [1, \"empty-state\"], [1, \"empty-content\"], [1, \"fas\", \"fa-shopping-bag\"]],\n        template: function VendorOrdersComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n            i0.ɵɵtext(3, \"Orders\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 2)(5, \"div\", 3)(6, \"span\", 4);\n            i0.ɵɵtext(7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"span\", 5);\n            i0.ɵɵtext(9, \"Total Orders\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 3)(11, \"span\", 4);\n            i0.ɵɵtext(12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"span\", 5);\n            i0.ɵɵtext(14, \"Pending\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"div\", 3)(16, \"span\", 4);\n            i0.ɵɵtext(17);\n            i0.ɵɵpipe(18, \"number\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"span\", 5);\n            i0.ɵɵtext(20, \"Revenue\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(21, \"div\", 6);\n            i0.ɵɵtemplate(22, VendorOrdersComponent_button_22_Template, 2, 4, \"button\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(23, VendorOrdersComponent_div_23_Template, 2, 1, \"div\", 8)(24, VendorOrdersComponent_div_24_Template, 7, 2, \"div\", 9);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate(ctx.getTotalOrders());\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate(ctx.getPendingOrders());\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(18, 6, ctx.getTotalRevenue(), \"1.0-0\"), \"\");\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngForOf\", ctx.filters);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.filteredOrders.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.filteredOrders.length === 0);\n          }\n        },\n        dependencies: [CommonModule, i1.NgForOf, i1.NgIf, i1.DecimalPipe, i1.TitleCasePipe, i1.DatePipe],\n        styles: [\".vendor-orders-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:20px}.header[_ngcontent-%COMP%]{margin-bottom:30px}.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem;font-weight:600;margin-bottom:20px}.order-stats[_ngcontent-%COMP%]{display:flex;gap:30px}.stat-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center}.stat-value[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:700;color:#333}.stat-label[_ngcontent-%COMP%]{font-size:.85rem;color:#666;text-transform:uppercase;letter-spacing:.5px}.filter-tabs[_ngcontent-%COMP%]{display:flex;gap:8px;margin-bottom:24px;border-bottom:1px solid #eee}.filter-tab[_ngcontent-%COMP%]{padding:12px 20px;border:none;background:none;cursor:pointer;font-weight:500;color:#666;border-bottom:2px solid transparent;transition:all .2s}.filter-tab[_ngcontent-%COMP%]:hover{color:#007bff}.filter-tab.active[_ngcontent-%COMP%]{color:#007bff;border-bottom-color:#007bff}.orders-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:20px}.order-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;padding:24px;box-shadow:0 2px 8px #0000001a;border:1px solid #eee}.order-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:16px}.order-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:600;margin-bottom:4px}.order-date[_ngcontent-%COMP%]{color:#666;font-size:.9rem}.order-status[_ngcontent-%COMP%]{text-align:right}.status-badge[_ngcontent-%COMP%]{display:inline-block;padding:4px 12px;border-radius:12px;font-size:.8rem;font-weight:500;text-transform:uppercase;margin-bottom:4px}.status-badge.pending[_ngcontent-%COMP%]{background:#fff3cd;color:#856404}.status-badge.confirmed[_ngcontent-%COMP%]{background:#cce7ff;color:#06c}.status-badge.shipped[_ngcontent-%COMP%]{background:#e7f3ff;color:#007bff}.status-badge.delivered[_ngcontent-%COMP%]{background:#d4edda;color:#155724}.status-badge.cancelled[_ngcontent-%COMP%]{background:#f8d7da;color:#721c24}.order-total[_ngcontent-%COMP%]{display:block;font-size:1.1rem;font-weight:600;color:#333}.order-customer[_ngcontent-%COMP%]{display:flex;gap:24px;margin-bottom:16px;padding:12px;background:#f8f9fa;border-radius:8px}.customer-info[_ngcontent-%COMP%], .customer-contact[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:.9rem}.order-items[_ngcontent-%COMP%]{margin-bottom:16px}.item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;padding:12px 0;border-bottom:1px solid #f0f0f0}.item[_ngcontent-%COMP%]:last-child{border-bottom:none}.item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:60px;height:60px;object-fit:cover;border-radius:8px}.item-details[_ngcontent-%COMP%]{flex:1}.item-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:1rem;font-weight:500;margin-bottom:4px}.item-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:.9rem;margin-bottom:4px}.item-variants[_ngcontent-%COMP%]{display:flex;gap:12px;font-size:.8rem;color:#666}.item-total[_ngcontent-%COMP%]{font-weight:600;color:#333}.order-address[_ngcontent-%COMP%]{margin-bottom:20px;padding:12px;background:#f8f9fa;border-radius:8px}.order-address[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:.9rem;font-weight:600;margin-bottom:6px;color:#333}.order-address[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.9rem;color:#666;margin:0}.order-actions[_ngcontent-%COMP%]{display:flex;gap:12px;flex-wrap:wrap}.btn-primary[_ngcontent-%COMP%], .btn-secondary[_ngcontent-%COMP%], .btn-success[_ngcontent-%COMP%], .btn-view[_ngcontent-%COMP%], .btn-contact[_ngcontent-%COMP%]{padding:8px 16px;border:none;border-radius:6px;font-size:.85rem;font-weight:500;cursor:pointer;transition:all .2s;display:flex;align-items:center;gap:6px}.btn-primary[_ngcontent-%COMP%]{background:#007bff;color:#fff}.btn-primary[_ngcontent-%COMP%]:hover{background:#0056b3}.btn-secondary[_ngcontent-%COMP%]{background:#6c757d;color:#fff}.btn-secondary[_ngcontent-%COMP%]:hover{background:#545b62}.btn-success[_ngcontent-%COMP%]{background:#28a745;color:#fff}.btn-success[_ngcontent-%COMP%]:hover{background:#1e7e34}.btn-view[_ngcontent-%COMP%]{background:#f8f9fa;color:#495057;border:1px solid #dee2e6}.btn-view[_ngcontent-%COMP%]:hover{background:#e9ecef}.btn-contact[_ngcontent-%COMP%]{background:#17a2b8;color:#fff}.btn-contact[_ngcontent-%COMP%]:hover{background:#138496}.empty-state[_ngcontent-%COMP%]{text-align:center;padding:60px 20px}.empty-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:4rem;color:#ddd;margin-bottom:20px}.empty-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.5rem;margin-bottom:10px}.empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666}@media (max-width: 768px){.order-header[_ngcontent-%COMP%]{flex-direction:column;gap:12px}.order-customer[_ngcontent-%COMP%]{flex-direction:column;gap:8px}.order-actions[_ngcontent-%COMP%]{flex-direction:column}.filter-tabs[_ngcontent-%COMP%]{flex-wrap:wrap}}\"]\n      });\n    }\n  }\n  return VendorOrdersComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}