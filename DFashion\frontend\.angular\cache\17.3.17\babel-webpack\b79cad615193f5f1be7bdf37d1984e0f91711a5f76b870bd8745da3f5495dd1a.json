{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AdminLayoutComponent } from './layout/admin-layout.component';\nimport { AdminDashboardComponent } from './dashboard/admin-dashboard.component';\nimport { AdminLoginComponent } from './auth/admin-login.component';\nimport { UserManagementComponent } from './users/user-management.component';\nimport { ProductManagementComponent } from './products/product-management.component';\nimport { OrderManagementComponent } from './orders/order-management.component';\nimport { AnalyticsComponent } from './analytics/analytics.component';\nimport { SettingsComponent } from './settings/settings.component';\nimport { AdminAuthGuard } from './guards/admin-auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'login',\n  component: AdminLoginComponent\n}, {\n  path: '',\n  component: AdminLayoutComponent,\n  canActivate: [AdminAuthGuard],\n  children: [{\n    path: '',\n    redirectTo: 'dashboard',\n    pathMatch: 'full'\n  }, {\n    path: 'dashboard',\n    component: AdminDashboardComponent,\n    data: {\n      title: 'Dashboard',\n      permission: 'dashboard:view'\n    }\n  }, {\n    path: 'users',\n    component: UserManagementComponent,\n    data: {\n      title: 'User Management',\n      permission: 'users:view'\n    }\n  }, {\n    path: 'products',\n    component: ProductManagementComponent,\n    data: {\n      title: 'Product Management',\n      permission: 'products:view'\n    }\n  }, {\n    path: 'orders',\n    component: OrderManagementComponent,\n    data: {\n      title: 'Order Management',\n      permission: 'orders:view'\n    }\n  }, {\n    path: 'analytics',\n    component: AnalyticsComponent,\n    data: {\n      title: 'Analytics',\n      permission: 'analytics:view'\n    }\n  }, {\n    path: 'settings',\n    component: SettingsComponent,\n    data: {\n      title: 'Settings',\n      permission: 'settings:view'\n    }\n  }]\n}];\nexport class AdminRoutingModule {\n  static {\n    this.ɵfac = function AdminRoutingModule_Factory(t) {\n      return new (t || AdminRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AdminRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AdminRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AdminLayoutComponent", "AdminDashboardComponent", "AdminLoginComponent", "UserManagementComponent", "ProductManagementComponent", "OrderManagementComponent", "AnalyticsComponent", "SettingsComponent", "Ad<PERSON><PERSON><PERSON><PERSON><PERSON>", "routes", "path", "component", "canActivate", "children", "redirectTo", "pathMatch", "data", "title", "permission", "AdminRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\admin\\admin-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { AdminLayoutComponent } from './layout/admin-layout.component';\nimport { AdminDashboardComponent } from './dashboard/admin-dashboard.component';\nimport { AdminLoginComponent } from './auth/admin-login.component';\nimport { UserManagementComponent } from './users/user-management.component';\nimport { ProductManagementComponent } from './products/product-management.component';\nimport { OrderManagementComponent } from './orders/order-management.component';\nimport { AnalyticsComponent } from './analytics/analytics.component';\nimport { SettingsComponent } from './settings/settings.component';\nimport { AdminAuthGuard } from './guards/admin-auth.guard';\n\nconst routes: Routes = [\n  {\n    path: 'login',\n    component: AdminLoginComponent\n  },\n  {\n    path: '',\n    component: AdminLayoutComponent,\n    canActivate: [AdminAuthGuard],\n    children: [\n      {\n        path: '',\n        redirectTo: 'dashboard',\n        pathMatch: 'full'\n      },\n      {\n        path: 'dashboard',\n        component: AdminDashboardComponent,\n        data: { title: 'Dashboard', permission: 'dashboard:view' }\n      },\n      {\n        path: 'users',\n        component: UserManagementComponent,\n        data: { title: 'User Management', permission: 'users:view' }\n      },\n      {\n        path: 'products',\n        component: ProductManagementComponent,\n        data: { title: 'Product Management', permission: 'products:view' }\n      },\n      {\n        path: 'orders',\n        component: OrderManagementComponent,\n        data: { title: 'Order Management', permission: 'orders:view' }\n      },\n      {\n        path: 'analytics',\n        component: AnalyticsComponent,\n        data: { title: 'Analytics', permission: 'analytics:view' }\n      },\n      {\n        path: 'settings',\n        component: SettingsComponent,\n        data: { title: 'Settings', permission: 'settings:view' }\n      }\n    ]\n  }\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule]\n})\nexport class AdminRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SAASC,uBAAuB,QAAQ,uCAAuC;AAC/E,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,uBAAuB,QAAQ,mCAAmC;AAC3E,SAASC,0BAA0B,QAAQ,yCAAyC;AACpF,SAASC,wBAAwB,QAAQ,qCAAqC;AAC9E,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,cAAc,QAAQ,2BAA2B;;;AAE1D,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAET;CACZ,EACD;EACEQ,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEX,oBAAoB;EAC/BY,WAAW,EAAE,CAACJ,cAAc,CAAC;EAC7BK,QAAQ,EAAE,CACR;IACEH,IAAI,EAAE,EAAE;IACRI,UAAU,EAAE,WAAW;IACvBC,SAAS,EAAE;GACZ,EACD;IACEL,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEV,uBAAuB;IAClCe,IAAI,EAAE;MAAEC,KAAK,EAAE,WAAW;MAAEC,UAAU,EAAE;IAAgB;GACzD,EACD;IACER,IAAI,EAAE,OAAO;IACbC,SAAS,EAAER,uBAAuB;IAClCa,IAAI,EAAE;MAAEC,KAAK,EAAE,iBAAiB;MAAEC,UAAU,EAAE;IAAY;GAC3D,EACD;IACER,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEP,0BAA0B;IACrCY,IAAI,EAAE;MAAEC,KAAK,EAAE,oBAAoB;MAAEC,UAAU,EAAE;IAAe;GACjE,EACD;IACER,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAEN,wBAAwB;IACnCW,IAAI,EAAE;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,UAAU,EAAE;IAAa;GAC7D,EACD;IACER,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEL,kBAAkB;IAC7BU,IAAI,EAAE;MAAEC,KAAK,EAAE,WAAW;MAAEC,UAAU,EAAE;IAAgB;GACzD,EACD;IACER,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEJ,iBAAiB;IAC5BS,IAAI,EAAE;MAAEC,KAAK,EAAE,UAAU;MAAEC,UAAU,EAAE;IAAe;GACvD;CAEJ,CACF;AAMD,OAAM,MAAOC,kBAAkB;;;uBAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA;IAAkB;EAAA;;;gBAHnBpB,YAAY,CAACqB,QAAQ,CAACX,MAAM,CAAC,EAC7BV,YAAY;IAAA;EAAA;;;2EAEXoB,kBAAkB;IAAAE,OAAA,GAAAC,EAAA,CAAAvB,YAAA;IAAAwB,OAAA,GAFnBxB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}