{"ast": null, "code": "import { Breakpoints } from '@angular/cdk/layout';\nimport { Subject } from 'rxjs';\nimport { map, shareReplay, takeUntil } from 'rxjs/operators';\nimport { NavigationEnd } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/cdk/layout\";\nimport * as i2 from \"../services/admin-auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/toolbar\";\nimport * as i6 from \"@angular/material/sidenav\";\nimport * as i7 from \"@angular/material/list\";\nimport * as i8 from \"@angular/material/divider\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/button\";\nimport * as i11 from \"@angular/material/menu\";\nimport * as i12 from \"@angular/material/badge\";\nconst _c0 = [\"drawer\"];\nfunction AdminLayoutComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 30)(2, \"span\", 31);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 32)(5, \"div\", 33);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 34);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 35);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.getRoleColor());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getUserInitials());\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.currentUser.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getRoleDisplayName());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(11, 6, ctx_r1.currentUser.department));\n  }\n}\nfunction AdminLayoutComponent_mat_list_item_16_mat_icon_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 40);\n    i0.ɵɵtext(1, \"chevron_right\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminLayoutComponent_mat_list_item_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-list-item\", 36);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_mat_list_item_16_Template_mat_list_item_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onMenuItemClick());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\", 37);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 38);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, AdminLayoutComponent_mat_list_item_16_mat_icon_5_Template, 2, 0, \"mat-icon\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", item_r4.route);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isActiveRoute(item_r4.route));\n  }\n}\nfunction AdminLayoutComponent_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_button_25_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      i0.ɵɵnextContext();\n      const drawer_r6 = i0.ɵɵreference(2);\n      return i0.ɵɵresetView(drawer_r6.toggle());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\", 42);\n    i0.ɵɵtext(2, \"menu\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AdminLayoutComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 44)(2, \"span\", 45);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 46)(5, \"div\", 47);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 48);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 49);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.getRoleColor());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getUserInitials());\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.currentUser.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.currentUser.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getRoleDisplayName());\n  }\n}\nexport let AdminLayoutComponent = /*#__PURE__*/(() => {\n  class AdminLayoutComponent {\n    constructor(breakpointObserver, authService, router) {\n      this.breakpointObserver = breakpointObserver;\n      this.authService = authService;\n      this.router = router;\n      this.destroy$ = new Subject();\n      this.currentUser = null;\n      this.pageTitle = 'Dashboard';\n      this.isHandset$ = this.breakpointObserver.observe(Breakpoints.Handset).pipe(map(result => result.matches), shareReplay());\n      this.navigationItems = [{\n        title: 'Dashboard',\n        icon: 'dashboard',\n        route: '/admin/dashboard',\n        permission: 'dashboard:view'\n      }, {\n        title: 'User Management',\n        icon: 'people',\n        route: '/admin/users',\n        permission: 'users:view'\n      }, {\n        title: 'Product Management',\n        icon: 'inventory',\n        route: '/admin/products',\n        permission: 'products:view'\n      }, {\n        title: 'Order Management',\n        icon: 'shopping_cart',\n        route: '/admin/orders',\n        permission: 'orders:view'\n      }, {\n        title: 'Analytics',\n        icon: 'analytics',\n        route: '/admin/analytics',\n        permission: 'analytics:view'\n      }, {\n        title: 'Settings',\n        icon: 'settings',\n        route: '/admin/settings',\n        permission: 'settings:view'\n      }];\n      this.currentUser$ = this.authService.currentUser$;\n    }\n    ngOnInit() {\n      // Subscribe to current user\n      this.currentUser$.pipe(takeUntil(this.destroy$)).subscribe(user => {\n        this.currentUser = user;\n      });\n      // Listen to route changes to update page title\n      this.router.events.pipe(takeUntil(this.destroy$)).subscribe(event => {\n        if (event instanceof NavigationEnd) {\n          this.updatePageTitle();\n        }\n      });\n      // Initial page title update\n      this.updatePageTitle();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    hasPermission(permission) {\n      if (!permission || !this.currentUser) return true;\n      const [module, action] = permission.split(':');\n      return this.authService.hasPermission(module, action);\n    }\n    getVisibleNavigationItems() {\n      return this.navigationItems.filter(item => this.hasPermission(item.permission));\n    }\n    onLogout() {\n      this.authService.logout();\n    }\n    updatePageTitle() {\n      const url = this.router.url;\n      const routeTitleMap = {\n        '/admin/dashboard': 'Dashboard',\n        '/admin/users': 'User Management',\n        '/admin/products': 'Product Management',\n        '/admin/orders': 'Order Management',\n        '/admin/analytics': 'Analytics',\n        '/admin/settings': 'Settings'\n      };\n      this.pageTitle = routeTitleMap[url] || 'Admin Panel';\n    }\n    getUserInitials() {\n      if (!this.currentUser?.fullName) return 'AD';\n      const names = this.currentUser.fullName.split(' ');\n      if (names.length >= 2) {\n        return (names[0][0] + names[1][0]).toUpperCase();\n      }\n      return names[0][0].toUpperCase();\n    }\n    getRoleColor() {\n      if (!this.currentUser?.role) return '#666';\n      const roleColors = {\n        'super_admin': '#e91e63',\n        'admin': '#9c27b0',\n        'sales_manager': '#2196f3',\n        'marketing_manager': '#ff9800',\n        'account_manager': '#4caf50',\n        'support_manager': '#795548'\n      };\n      return roleColors[this.currentUser.role] || '#666';\n    }\n    getRoleDisplayName() {\n      if (!this.currentUser?.role) return '';\n      const roleDisplayNames = {\n        'super_admin': 'Super Admin',\n        'admin': 'Admin',\n        'sales_manager': 'Sales Manager',\n        'sales_executive': 'Sales Executive',\n        'marketing_manager': 'Marketing Manager',\n        'marketing_executive': 'Marketing Executive',\n        'account_manager': 'Account Manager',\n        'accountant': 'Accountant',\n        'support_manager': 'Support Manager',\n        'support_agent': 'Support Agent',\n        'content_manager': 'Content Manager',\n        'vendor_manager': 'Vendor Manager'\n      };\n      return roleDisplayNames[this.currentUser.role] || this.currentUser.role.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase());\n    }\n    isActiveRoute(route) {\n      return this.router.url === route;\n    }\n    onMenuItemClick() {\n      // Close drawer on mobile after navigation\n      this.isHandset$.pipe(takeUntil(this.destroy$)).subscribe(isHandset => {\n        if (isHandset && this.drawer) {\n          this.drawer.close();\n        }\n      });\n    }\n    static {\n      this.ɵfac = function AdminLayoutComponent_Factory(t) {\n        return new (t || AdminLayoutComponent)(i0.ɵɵdirectiveInject(i1.BreakpointObserver), i0.ɵɵdirectiveInject(i2.AdminAuthService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AdminLayoutComponent,\n        selectors: [[\"app-admin-layout\"]],\n        viewQuery: function AdminLayoutComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.drawer = _t.first);\n          }\n        },\n        decls: 64,\n        vars: 20,\n        consts: [[\"drawer\", \"\"], [\"userMenu\", \"matMenu\"], [1, \"sidenav-container\"], [\"fixedInViewport\", \"\", 1, \"sidenav\", 3, \"mode\", \"opened\"], [1, \"sidenav-header\"], [1, \"logo\"], [1, \"logo-icon\"], [1, \"logo-text\"], [1, \"admin-badge\"], [\"class\", \"user-profile\", 4, \"ngIf\"], [1, \"nav-list\"], [\"routerLinkActive\", \"active-nav-item\", \"class\", \"nav-item\", 3, \"routerLink\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"sidenav-footer\"], [\"mat-button\", \"\", 1, \"logout-button\", 3, \"click\"], [1, \"main-content\"], [\"color\", \"primary\", 1, \"toolbar\"], [\"type\", \"button\", \"aria-label\", \"Toggle sidenav\", \"mat-icon-button\", \"\", 3, \"click\", 4, \"ngIf\"], [1, \"page-title\"], [1, \"toolbar-spacer\"], [\"mat-icon-button\", \"\", 1, \"notification-button\"], [\"matBadge\", \"3\", \"matBadgeColor\", \"warn\"], [\"mat-icon-button\", \"\", 1, \"user-menu-button\", 3, \"matMenuTriggerFor\"], [1, \"toolbar-user-avatar\"], [1, \"toolbar-user-initials\"], [1, \"user-menu\"], [\"class\", \"user-menu-header\", 4, \"ngIf\"], [\"mat-menu-item\", \"\"], [\"mat-menu-item\", \"\", 1, \"logout-menu-item\", 3, \"click\"], [1, \"page-content\"], [1, \"user-profile\"], [1, \"user-avatar\"], [1, \"user-initials\"], [1, \"user-info\"], [1, \"user-name\"], [1, \"user-role\"], [1, \"user-department\"], [\"routerLinkActive\", \"active-nav-item\", 1, \"nav-item\", 3, \"click\", \"routerLink\"], [\"matListIcon\", \"\"], [\"matLine\", \"\"], [\"class\", \"nav-arrow\", 4, \"ngIf\"], [1, \"nav-arrow\"], [\"type\", \"button\", \"aria-label\", \"Toggle sidenav\", \"mat-icon-button\", \"\", 3, \"click\"], [\"aria-label\", \"Side nav toggle icon\"], [1, \"user-menu-header\"], [1, \"menu-user-avatar\"], [1, \"menu-user-initials\"], [1, \"menu-user-info\"], [1, \"menu-user-name\"], [1, \"menu-user-email\"], [1, \"menu-user-role\"]],\n        template: function AdminLayoutComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"mat-sidenav-container\", 2)(1, \"mat-sidenav\", 3, 0);\n            i0.ɵɵpipe(3, \"async\");\n            i0.ɵɵpipe(4, \"async\");\n            i0.ɵɵpipe(5, \"async\");\n            i0.ɵɵelementStart(6, \"div\", 4)(7, \"div\", 5)(8, \"mat-icon\", 6);\n            i0.ɵɵtext(9, \"shopping_bag\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"span\", 7);\n            i0.ɵɵtext(11, \"DFashion\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(12, \"div\", 8);\n            i0.ɵɵtext(13, \"Admin Panel\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(14, AdminLayoutComponent_div_14_Template, 12, 8, \"div\", 9);\n            i0.ɵɵelementStart(15, \"mat-nav-list\", 10);\n            i0.ɵɵtemplate(16, AdminLayoutComponent_mat_list_item_16_Template, 6, 4, \"mat-list-item\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"div\", 12)(18, \"button\", 13);\n            i0.ɵɵlistener(\"click\", function AdminLayoutComponent_Template_button_click_18_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onLogout());\n            });\n            i0.ɵɵelementStart(19, \"mat-icon\");\n            i0.ɵɵtext(20, \"logout\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"span\");\n            i0.ɵɵtext(22, \"Logout\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(23, \"mat-sidenav-content\", 14)(24, \"mat-toolbar\", 15);\n            i0.ɵɵtemplate(25, AdminLayoutComponent_button_25_Template, 3, 0, \"button\", 16);\n            i0.ɵɵpipe(26, \"async\");\n            i0.ɵɵelementStart(27, \"span\", 17);\n            i0.ɵɵtext(28);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(29, \"span\", 18);\n            i0.ɵɵelementStart(30, \"button\", 19)(31, \"mat-icon\", 20);\n            i0.ɵɵtext(32, \"notifications\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(33, \"button\", 21)(34, \"div\", 22)(35, \"span\", 23);\n            i0.ɵɵtext(36);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(37, \"mat-menu\", 24, 1);\n            i0.ɵɵtemplate(39, AdminLayoutComponent_div_39_Template, 11, 6, \"div\", 25);\n            i0.ɵɵelement(40, \"mat-divider\");\n            i0.ɵɵelementStart(41, \"button\", 26)(42, \"mat-icon\");\n            i0.ɵɵtext(43, \"person\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"span\");\n            i0.ɵɵtext(45, \"Profile\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(46, \"button\", 26)(47, \"mat-icon\");\n            i0.ɵɵtext(48, \"settings\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"span\");\n            i0.ɵɵtext(50, \"Account Settings\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(51, \"button\", 26)(52, \"mat-icon\");\n            i0.ɵɵtext(53, \"help\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(54, \"span\");\n            i0.ɵɵtext(55, \"Help & Support\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(56, \"mat-divider\");\n            i0.ɵɵelementStart(57, \"button\", 27);\n            i0.ɵɵlistener(\"click\", function AdminLayoutComponent_Template_button_click_57_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onLogout());\n            });\n            i0.ɵɵelementStart(58, \"mat-icon\");\n            i0.ɵɵtext(59, \"logout\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(60, \"span\");\n            i0.ɵɵtext(61, \"Logout\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(62, \"div\", 28);\n            i0.ɵɵelement(63, \"router-outlet\");\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            const userMenu_r7 = i0.ɵɵreference(38);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"mode\", i0.ɵɵpipeBind1(3, 12, ctx.isHandset$) ? \"over\" : \"side\")(\"opened\", i0.ɵɵpipeBind1(4, 14, ctx.isHandset$) === false);\n            i0.ɵɵattribute(\"role\", i0.ɵɵpipeBind1(5, 16, ctx.isHandset$) ? \"dialog\" : \"navigation\");\n            i0.ɵɵadvance(13);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.getVisibleNavigationItems());\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(26, 18, ctx.isHandset$));\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate(ctx.pageTitle);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"matMenuTriggerFor\", userMenu_r7);\n            i0.ɵɵadvance();\n            i0.ɵɵstyleProp(\"background-color\", ctx.getRoleColor());\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.getUserInitials());\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n          }\n        },\n        dependencies: [i4.NgForOf, i4.NgIf, i3.RouterOutlet, i3.RouterLink, i3.RouterLinkActive, i5.MatToolbar, i6.MatSidenav, i6.MatSidenavContainer, i6.MatSidenavContent, i7.MatNavList, i7.MatListItem, i8.MatDivider, i9.MatIcon, i10.MatButton, i10.MatIconButton, i11.MatMenu, i11.MatMenuItem, i11.MatMenuTrigger, i12.MatBadge, i4.AsyncPipe, i4.TitleCasePipe],\n        styles: [\".sidenav-container[_ngcontent-%COMP%]{height:100vh}.sidenav[_ngcontent-%COMP%]{width:280px;background:#1a1a2e;color:#fff;display:flex;flex-direction:column}.sidenav-header[_ngcontent-%COMP%]{padding:1.5rem 1rem 1rem;border-bottom:1px solid rgba(255,255,255,.1)}.sidenav-header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.75rem;margin-bottom:.5rem}.sidenav-header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   .logo-icon[_ngcontent-%COMP%]{font-size:2rem;width:2rem;height:2rem;color:#667eea}.sidenav-header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   .logo-text[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:600;background:linear-gradient(135deg,#667eea,#764ba2);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.sidenav-header[_ngcontent-%COMP%]   .admin-badge[_ngcontent-%COMP%]{background:#667eea33;color:#667eea;padding:.25rem .75rem;border-radius:12px;font-size:.75rem;font-weight:500;display:inline-block}.user-profile[_ngcontent-%COMP%]{padding:1rem;border-bottom:1px solid rgba(255,255,255,.1);display:flex;align-items:center;gap:1rem}.user-profile[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:50%;display:flex;align-items:center;justify-content:center;font-weight:600;color:#fff}.user-profile[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%]   .user-initials[_ngcontent-%COMP%]{font-size:1.1rem}.user-profile[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]{flex:1;min-width:0}.user-profile[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%]{font-weight:500;font-size:.95rem;margin-bottom:.25rem;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.user-profile[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .user-role[_ngcontent-%COMP%]{color:#667eea;font-size:.8rem;font-weight:500}.user-profile[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .user-department[_ngcontent-%COMP%]{color:#ffffffb3;font-size:.75rem}.nav-list[_ngcontent-%COMP%]{flex:1;padding-top:1rem}.nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]{margin:.25rem 1rem;border-radius:8px;transition:all .2s ease;color:#fffc}.nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]:hover{background:#ffffff1a;color:#fff}.nav-list[_ngcontent-%COMP%]   .nav-item.active-nav-item[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff}.nav-list[_ngcontent-%COMP%]   .nav-item.active-nav-item[_ngcontent-%COMP%]   .nav-arrow[_ngcontent-%COMP%]{color:#fff}.nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:inherit}.nav-list[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-arrow[_ngcontent-%COMP%]{margin-left:auto}.sidenav-footer[_ngcontent-%COMP%]{padding:1rem;border-top:1px solid rgba(255,255,255,.1)}.sidenav-footer[_ngcontent-%COMP%]   .logout-button[_ngcontent-%COMP%]{width:100%;color:#fffc;justify-content:flex-start;gap:.75rem}.sidenav-footer[_ngcontent-%COMP%]   .logout-button[_ngcontent-%COMP%]:hover{background:#ffffff1a;color:#fff}.main-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:100vh}.toolbar[_ngcontent-%COMP%]{position:sticky;top:0;z-index:1000;box-shadow:0 2px 4px #0000001a}.toolbar[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:500}.toolbar[_ngcontent-%COMP%]   .toolbar-spacer[_ngcontent-%COMP%]{flex:1 1 auto}.toolbar[_ngcontent-%COMP%]   .notification-button[_ngcontent-%COMP%]{margin-right:.5rem}.toolbar[_ngcontent-%COMP%]   .user-menu-button[_ngcontent-%COMP%]   .toolbar-user-avatar[_ngcontent-%COMP%]{width:36px;height:36px;border-radius:50%;display:flex;align-items:center;justify-content:center;color:#fff;font-weight:600}.toolbar[_ngcontent-%COMP%]   .user-menu-button[_ngcontent-%COMP%]   .toolbar-user-avatar[_ngcontent-%COMP%]   .toolbar-user-initials[_ngcontent-%COMP%]{font-size:.9rem}.page-content[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding:1.5rem;background:#f5f5f5}  .user-menu .mat-menu-content{padding:0}  .user-menu .user-menu-header{padding:1rem;background:#f8f9fa;display:flex;align-items:center;gap:1rem}  .user-menu .user-menu-header .menu-user-avatar{width:48px;height:48px;border-radius:50%;display:flex;align-items:center;justify-content:center;color:#fff;font-weight:600}  .user-menu .user-menu-header .menu-user-avatar .menu-user-initials{font-size:1.1rem}  .user-menu .user-menu-header .menu-user-info .menu-user-name{font-weight:500;color:#333;margin-bottom:.25rem}  .user-menu .user-menu-header .menu-user-info .menu-user-email{color:#666;font-size:.85rem;margin-bottom:.25rem}  .user-menu .user-menu-header .menu-user-info .menu-user-role{color:#667eea;font-size:.8rem;font-weight:500}  .user-menu .logout-menu-item{color:#f44336}  .user-menu .logout-menu-item mat-icon{color:#f44336}@media (max-width: 768px){.sidenav[_ngcontent-%COMP%]{width:100%;max-width:280px}.page-content[_ngcontent-%COMP%]{padding:1rem}.user-profile[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%]{font-size:.9rem}}  .mat-list-item{height:48px!important}  .mat-list-item-content{padding:0 16px!important}  .mat-toolbar{background:linear-gradient(135deg,#667eea,#764ba2)!important}  .mat-badge-content{background:#f44336!important;color:#fff!important}.nav-item[_ngcontent-%COMP%]{transition:all .2s cubic-bezier(.4,0,.2,1)}.user-menu-button[_ngcontent-%COMP%]{transition:transform .2s ease}.user-menu-button[_ngcontent-%COMP%]:hover{transform:scale(1.05)}\"]\n      });\n    }\n  }\n  return AdminLayoutComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}