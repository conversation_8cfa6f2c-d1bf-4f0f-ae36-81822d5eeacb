{"ast": null, "code": "import { of } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/admin-auth.service\";\nimport * as i2 from \"@angular/router\";\nexport let AdminAuthGuard = /*#__PURE__*/(() => {\n  class AdminAuthGuard {\n    constructor(authService, router) {\n      this.authService = authService;\n      this.router = router;\n    }\n    canActivate(route, state) {\n      return this.checkAuth(route, state);\n    }\n    canActivateChild(route, state) {\n      return this.checkAuth(route, state);\n    }\n    checkAuth(route, state) {\n      // Check if user is authenticated\n      if (!this.authService.isAuthenticated()) {\n        this.router.navigate(['/admin/login'], {\n          queryParams: {\n            returnUrl: state.url\n          }\n        });\n        return of(false);\n      }\n      // Check if user can access admin panel\n      if (!this.authService.canAccessAdmin()) {\n        this.router.navigate(['/admin/login']);\n        return of(false);\n      }\n      // Check specific permission if required\n      const requiredPermission = route.data?.['permission'];\n      if (requiredPermission) {\n        const [module, action] = requiredPermission.split(':');\n        if (!this.authService.hasPermission(module, action)) {\n          // Redirect to dashboard with error message\n          this.router.navigate(['/admin/dashboard'], {\n            queryParams: {\n              error: 'insufficient_permissions'\n            }\n          });\n          return of(false);\n        }\n      }\n      // Verify token with server\n      return this.authService.verifyToken().pipe(map(() => true), catchError(() => {\n        this.router.navigate(['/admin/login'], {\n          queryParams: {\n            returnUrl: state.url,\n            error: 'session_expired'\n          }\n        });\n        return of(false);\n      }));\n    }\n    static {\n      this.ɵfac = function AdminAuthGuard_Factory(t) {\n        return new (t || AdminAuthGuard)(i0.ɵɵinject(i1.AdminAuthService), i0.ɵɵinject(i2.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AdminAuthGuard,\n        factory: AdminAuthGuard.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AdminAuthGuard;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}