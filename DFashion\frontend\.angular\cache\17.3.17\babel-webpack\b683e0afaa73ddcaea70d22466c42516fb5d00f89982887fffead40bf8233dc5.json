{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3, 4];\nfunction ShopByCategoryComponent_div_8_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵelement(1, \"div\", 14);\n    i0.ɵɵelementStart(2, \"div\", 15);\n    i0.ɵɵelement(3, \"div\", 16)(4, \"div\", 17)(5, \"div\", 18);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ShopByCategoryComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11);\n    i0.ɵɵtemplate(2, ShopByCategoryComponent_div_8_div_2_Template, 6, 0, \"div\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction ShopByCategoryComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵelement(1, \"ion-icon\", 20);\n    i0.ɵɵelementStart(2, \"p\", 21);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ShopByCategoryComponent_div_9_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 23);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction ShopByCategoryComponent_div_10_div_7_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵelement(1, \"ion-icon\", 47);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Trending\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ShopByCategoryComponent_div_10_div_7_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", category_r5.discount, \"% OFF \");\n  }\n}\nfunction ShopByCategoryComponent_div_10_div_7_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const sub_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", sub_r6, \" \");\n  }\n}\nfunction ShopByCategoryComponent_div_10_div_7_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" +\", category_r5.subcategories.length - 3, \" \");\n  }\n}\nfunction ShopByCategoryComponent_div_10_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵlistener(\"click\", function ShopByCategoryComponent_div_10_div_7_Template_div_click_0_listener() {\n      const category_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onCategoryClick(category_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 33);\n    i0.ɵɵelement(2, \"img\", 34);\n    i0.ɵɵtemplate(3, ShopByCategoryComponent_div_10_div_7_div_3_Template, 4, 0, \"div\", 35)(4, ShopByCategoryComponent_div_10_div_7_div_4_Template, 2, 1, \"div\", 36);\n    i0.ɵɵelementStart(5, \"div\", 37);\n    i0.ɵɵelement(6, \"ion-icon\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 39)(8, \"h3\", 40);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 41);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 42);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 43);\n    i0.ɵɵtemplate(15, ShopByCategoryComponent_div_10_div_7_span_15_Template, 2, 1, \"span\", 44)(16, ShopByCategoryComponent_div_10_div_7_span_16_Template, 2, 1, \"span\", 45);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const category_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"trending\", category_r5.trending);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", category_r5.image, i0.ɵɵsanitizeUrl)(\"alt\", category_r5.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", category_r5.trending);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", category_r5.discount);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(category_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r5.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatProductCount(category_r5.productCount), \" Products\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", category_r5.subcategories.slice(0, 3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", category_r5.subcategories.length > 3);\n  }\n}\nfunction ShopByCategoryComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function ShopByCategoryComponent_div_10_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slidePrev());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function ShopByCategoryComponent_div_10_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slideNext());\n    });\n    i0.ɵɵelement(4, \"ion-icon\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 29);\n    i0.ɵɵlistener(\"mouseenter\", function ShopByCategoryComponent_div_10_Template_div_mouseenter_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pauseAutoSlide());\n    })(\"mouseleave\", function ShopByCategoryComponent_div_10_Template_div_mouseleave_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resumeAutoSlide());\n    });\n    i0.ɵɵelementStart(6, \"div\", 30);\n    i0.ɵɵtemplate(7, ShopByCategoryComponent_div_10_div_7_Template, 17, 11, \"div\", 31);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide >= ctx_r1.maxSlide);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx_r1.slideOffset + \"px)\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categories)(\"ngForTrackBy\", ctx_r1.trackByCategoryId);\n  }\n}\nfunction ShopByCategoryComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵelement(1, \"ion-icon\", 52);\n    i0.ɵɵelementStart(2, \"h3\", 53);\n    i0.ɵɵtext(3, \"No Categories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 54);\n    i0.ɵɵtext(5, \"Check back later for product categories\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let ShopByCategoryComponent = /*#__PURE__*/(() => {\n  class ShopByCategoryComponent {\n    constructor(router) {\n      this.router = router;\n      this.categories = [];\n      this.isLoading = true;\n      this.error = null;\n      this.subscription = new Subscription();\n      // Slider properties\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.cardWidth = 200; // Width of each category card including margin\n      this.visibleCards = 4; // Number of cards visible at once\n      this.maxSlide = 0;\n      this.autoSlideDelay = 4500; // 4.5 seconds for categories\n      this.isAutoSliding = true;\n      this.isPaused = false;\n    }\n    ngOnInit() {\n      this.loadCategories();\n      this.updateResponsiveSettings();\n      this.setupResizeListener();\n    }\n    ngOnDestroy() {\n      this.subscription.unsubscribe();\n      this.stopAutoSlide();\n    }\n    loadCategories() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          _this.isLoading = true;\n          _this.error = null;\n          // Mock data for shop categories\n          _this.categories = [{\n            id: '1',\n            name: 'Women\\'s Fashion',\n            image: 'https://images.unsplash.com/photo-1483985988355-763728e1935b?w=300&h=200&fit=crop',\n            productCount: 15420,\n            description: 'Trendy outfits for every occasion',\n            trending: true,\n            discount: 30,\n            subcategories: ['Dresses', 'Tops', 'Bottoms', 'Accessories']\n          }, {\n            id: '2',\n            name: 'Men\\'s Fashion',\n            image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300&h=200&fit=crop',\n            productCount: 12850,\n            description: 'Stylish clothing for modern men',\n            trending: true,\n            discount: 25,\n            subcategories: ['Shirts', 'Pants', 'Jackets', 'Shoes']\n          }, {\n            id: '3',\n            name: 'Footwear',\n            image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=300&h=200&fit=crop',\n            productCount: 8960,\n            description: 'Step up your shoe game',\n            trending: false,\n            discount: 20,\n            subcategories: ['Sneakers', 'Formal', 'Casual', 'Sports']\n          }, {\n            id: '4',\n            name: 'Accessories',\n            image: 'https://images.unsplash.com/photo-1506629905607-d9c36e0a3f90?w=300&h=200&fit=crop',\n            productCount: 6750,\n            description: 'Complete your look',\n            trending: true,\n            subcategories: ['Bags', 'Jewelry', 'Watches', 'Belts']\n          }, {\n            id: '5',\n            name: 'Kids Fashion',\n            image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=300&h=200&fit=crop',\n            productCount: 4320,\n            description: 'Adorable styles for little ones',\n            trending: false,\n            discount: 35,\n            subcategories: ['Boys', 'Girls', 'Baby', 'Toys']\n          }, {\n            id: '6',\n            name: 'Sports & Fitness',\n            image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop',\n            productCount: 5680,\n            description: 'Gear up for your workout',\n            trending: true,\n            subcategories: ['Activewear', 'Equipment', 'Shoes', 'Supplements']\n          }, {\n            id: '7',\n            name: 'Beauty & Personal Care',\n            image: 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=300&h=200&fit=crop',\n            productCount: 7890,\n            description: 'Look and feel your best',\n            trending: false,\n            discount: 15,\n            subcategories: ['Skincare', 'Makeup', 'Haircare', 'Fragrance']\n          }, {\n            id: '8',\n            name: 'Home & Living',\n            image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=200&fit=crop',\n            productCount: 3450,\n            description: 'Style your space',\n            trending: false,\n            subcategories: ['Decor', 'Furniture', 'Kitchen', 'Bedding']\n          }];\n          _this.isLoading = false;\n          _this.updateSliderOnCategoriesLoad();\n        } catch (error) {\n          console.error('Error loading categories:', error);\n          _this.error = 'Failed to load categories';\n          _this.isLoading = false;\n        }\n      })();\n    }\n    onCategoryClick(category) {\n      this.router.navigate(['/category', category.id]);\n    }\n    formatProductCount(count) {\n      if (count >= 1000) {\n        return (count / 1000).toFixed(1) + 'K';\n      }\n      return count.toString();\n    }\n    onRetry() {\n      this.loadCategories();\n    }\n    trackByCategoryId(index, category) {\n      return category.id;\n    }\n    // Auto-sliding methods\n    startAutoSlide() {\n      if (!this.isAutoSliding || this.isPaused) return;\n      this.stopAutoSlide();\n      this.autoSlideInterval = setInterval(() => {\n        if (!this.isPaused && this.categories.length > this.visibleCards) {\n          this.autoSlideNext();\n        }\n      }, this.autoSlideDelay);\n    }\n    stopAutoSlide() {\n      if (this.autoSlideInterval) {\n        clearInterval(this.autoSlideInterval);\n        this.autoSlideInterval = null;\n      }\n    }\n    autoSlideNext() {\n      if (this.currentSlide >= this.maxSlide) {\n        this.currentSlide = 0;\n      } else {\n        this.currentSlide++;\n      }\n      this.updateSlideOffset();\n    }\n    pauseAutoSlide() {\n      this.isPaused = true;\n      this.stopAutoSlide();\n    }\n    resumeAutoSlide() {\n      this.isPaused = false;\n      this.startAutoSlide();\n    }\n    // Responsive methods\n    updateResponsiveSettings() {\n      const width = window.innerWidth;\n      if (width <= 480) {\n        this.cardWidth = 160;\n        this.visibleCards = 1;\n      } else if (width <= 768) {\n        this.cardWidth = 180;\n        this.visibleCards = 2;\n      } else if (width <= 1200) {\n        this.cardWidth = 200;\n        this.visibleCards = 3;\n      } else {\n        this.cardWidth = 220;\n        this.visibleCards = 4;\n      }\n      this.updateSliderLimits();\n      this.updateSlideOffset();\n    }\n    setupResizeListener() {\n      window.addEventListener('resize', () => {\n        this.updateResponsiveSettings();\n      });\n    }\n    // Slider methods\n    updateSliderLimits() {\n      this.maxSlide = Math.max(0, this.categories.length - this.visibleCards);\n    }\n    slidePrev() {\n      if (this.currentSlide > 0) {\n        this.currentSlide--;\n        this.updateSlideOffset();\n        this.restartAutoSlideAfterInteraction();\n      }\n    }\n    slideNext() {\n      if (this.currentSlide < this.maxSlide) {\n        this.currentSlide++;\n        this.updateSlideOffset();\n        this.restartAutoSlideAfterInteraction();\n      }\n    }\n    updateSlideOffset() {\n      this.slideOffset = -this.currentSlide * this.cardWidth;\n    }\n    restartAutoSlideAfterInteraction() {\n      this.stopAutoSlide();\n      setTimeout(() => {\n        this.startAutoSlide();\n      }, 2000);\n    }\n    // Update slider when categories load\n    updateSliderOnCategoriesLoad() {\n      setTimeout(() => {\n        this.updateSliderLimits();\n        this.currentSlide = 0;\n        this.slideOffset = 0;\n        this.startAutoSlide();\n      }, 100);\n    }\n    static {\n      this.ɵfac = function ShopByCategoryComponent_Factory(t) {\n        return new (t || ShopByCategoryComponent)(i0.ɵɵdirectiveInject(i1.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ShopByCategoryComponent,\n        selectors: [[\"app-shop-by-category\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 12,\n        vars: 4,\n        consts: [[1, \"shop-categories-container\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"grid\", 1, \"title-icon\"], [1, \"section-subtitle\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"categories-slider-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-category-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-category-card\"], [1, \"loading-image\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"categories-slider-container\"], [1, \"slider-nav\", \"prev-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-back\"], [1, \"slider-nav\", \"next-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-forward\"], [1, \"categories-slider-wrapper\", 3, \"mouseenter\", \"mouseleave\"], [1, \"categories-slider\"], [\"class\", \"category-card\", 3, \"trending\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"category-card\", 3, \"click\"], [1, \"category-image-container\"], [\"loading\", \"lazy\", 1, \"category-image\", 3, \"src\", \"alt\"], [\"class\", \"trending-badge\", 4, \"ngIf\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"category-overlay\"], [\"name\", \"arrow-forward\", 1, \"explore-icon\"], [1, \"category-info\"], [1, \"category-name\"], [1, \"category-description\"], [1, \"product-count\"], [1, \"subcategories\"], [\"class\", \"subcategory-tag\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"more-subcategories\", 4, \"ngIf\"], [1, \"trending-badge\"], [\"name\", \"trending-up\"], [1, \"discount-badge\"], [1, \"subcategory-tag\"], [1, \"more-subcategories\"], [1, \"empty-container\"], [\"name\", \"grid-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"]],\n        template: function ShopByCategoryComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n            i0.ɵɵelement(4, \"ion-icon\", 4);\n            i0.ɵɵtext(5, \" Shop by Category \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"p\", 5);\n            i0.ɵɵtext(7, \"Explore our collections\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(8, ShopByCategoryComponent_div_8_Template, 3, 2, \"div\", 6)(9, ShopByCategoryComponent_div_9_Template, 7, 1, \"div\", 7)(10, ShopByCategoryComponent_div_10_Template, 8, 6, \"div\", 8)(11, ShopByCategoryComponent_div_11_Template, 6, 0, \"div\", 9);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.categories.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.categories.length === 0);\n          }\n        },\n        dependencies: [CommonModule, i2.NgForOf, i2.NgIf, IonicModule, i3.IonIcon, CarouselModule],\n        styles: [\".shop-categories-container[_ngcontent-%COMP%]{padding:20px;background:linear-gradient(135deg,#f8f9fa,#e9ecef);border-radius:16px;margin-bottom:24px}.section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:24px}.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{flex:1}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#1a1a1a;margin:0 0 8px;display:flex;align-items:center;gap:12px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%]{font-size:28px;color:#6c5ce7}.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%]{font-size:14px;color:#666;margin:0}.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]{display:flex;gap:20px;overflow-x:auto;padding-bottom:8px}.loading-container[_ngcontent-%COMP%]   .loading-category-card[_ngcontent-%COMP%]{flex:0 0 180px;background:#ffffffb3;border-radius:16px;overflow:hidden}.loading-container[_ngcontent-%COMP%]   .loading-category-card[_ngcontent-%COMP%]   .loading-image[_ngcontent-%COMP%]{width:100%;height:120px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite}.loading-container[_ngcontent-%COMP%]   .loading-category-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]{padding:16px}.loading-container[_ngcontent-%COMP%]   .loading-category-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%]{height:12px;border-radius:6px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite;margin-bottom:8px}.loading-container[_ngcontent-%COMP%]   .loading-category-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%]{width:60%}.loading-container[_ngcontent-%COMP%]   .loading-category-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%]{width:80%}.loading-container[_ngcontent-%COMP%]   .loading-category-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%]{width:100%}@keyframes _ngcontent-%COMP%_loading{0%{background-position:200% 0}to{background-position:-200% 0}}.error-container[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%]{font-size:48px;color:#e74c3c;margin-bottom:16px}.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{color:#666;margin-bottom:20px;font-size:16px}.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#e74c3c,#c0392b);color:#fff;border:none;padding:12px 24px;border-radius:25px;font-weight:600;display:flex;align-items:center;gap:8px;margin:0 auto;cursor:pointer;transition:all .3s ease}.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 25px #e74c3c4d}.categories-slider-container[_ngcontent-%COMP%]{position:relative;margin:0 -20px}.categories-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]{position:absolute;top:50%;transform:translateY(-50%);z-index:10;background:#000000b3;color:#fff;border:none;width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease}.categories-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:hover:not(:disabled){background:#000000e6;transform:translateY(-50%) scale(1.1)}.categories-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:disabled{opacity:.3;cursor:not-allowed}.categories-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.categories-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%]{left:-20px}.categories-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%]{right:-20px}.categories-slider-wrapper[_ngcontent-%COMP%]{overflow:hidden;padding:0 20px}.categories-slider[_ngcontent-%COMP%]{display:flex;transition:transform .4s cubic-bezier(.25,.46,.45,.94);gap:20px}.categories-slider[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]{flex:0 0 180px;width:180px}.category-card[_ngcontent-%COMP%]{background:#fff;border-radius:16px;overflow:hidden;box-shadow:0 4px 20px #00000014;transition:all .3s ease;cursor:pointer}.category-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);box-shadow:0 12px 40px #00000026}.category-card[_ngcontent-%COMP%]:hover   .category-overlay[_ngcontent-%COMP%]{opacity:1}.category-card[_ngcontent-%COMP%]:hover   .category-image[_ngcontent-%COMP%]{transform:scale(1.1)}.category-card.trending[_ngcontent-%COMP%]{border:2px solid #ff6b6b}.category-card.trending[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%]{color:#ff6b6b}.category-image-container[_ngcontent-%COMP%]{position:relative;height:120px;overflow:hidden}.category-image-container[_ngcontent-%COMP%]   .category-image[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;transition:transform .3s ease}.category-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]{position:absolute;top:8px;left:8px;background:linear-gradient(135deg,#ff6b6b,#ee5a52);color:#fff;padding:4px 8px;border-radius:12px;font-size:10px;font-weight:600;display:flex;align-items:center;gap:4px}.category-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:12px}.category-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%]{position:absolute;top:8px;right:8px;background:linear-gradient(135deg,#00b894,#00a085);color:#fff;padding:4px 8px;border-radius:12px;font-size:10px;font-weight:700}.category-image-container[_ngcontent-%COMP%]   .category-overlay[_ngcontent-%COMP%]{position:absolute;inset:0;background:#0006;display:flex;align-items:center;justify-content:center;opacity:0;transition:opacity .3s ease}.category-image-container[_ngcontent-%COMP%]   .category-overlay[_ngcontent-%COMP%]   .explore-icon[_ngcontent-%COMP%]{font-size:24px;color:#fff}.category-info[_ngcontent-%COMP%]{padding:16px}.category-info[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%]{font-size:16px;font-weight:700;color:#1a1a1a;margin:0 0 6px;line-height:1.2}.category-info[_ngcontent-%COMP%]   .category-description[_ngcontent-%COMP%]{font-size:12px;color:#666;margin:0 0 8px;line-height:1.3}.category-info[_ngcontent-%COMP%]   .product-count[_ngcontent-%COMP%]{font-size:11px;color:#6c5ce7;font-weight:600;margin:0 0 12px}.subcategories[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:4px}.subcategories[_ngcontent-%COMP%]   .subcategory-tag[_ngcontent-%COMP%]{font-size:9px;background:#6c5ce71a;color:#6c5ce7;padding:2px 6px;border-radius:8px;font-weight:500}.subcategories[_ngcontent-%COMP%]   .more-subcategories[_ngcontent-%COMP%]{font-size:9px;color:#999;font-weight:500}.empty-container[_ngcontent-%COMP%]{text-align:center;padding:60px 20px}.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]{font-size:64px;color:#ddd;margin-bottom:20px}.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#666;margin:0 0 8px}.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%]{color:#999;margin:0}@media (max-width: 1200px){.categories-slider[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]{flex:0 0 170px;width:170px}}@media (max-width: 768px){.categories-slider-container[_ngcontent-%COMP%]{margin:0 -10px}.categories-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]{width:35px;height:35px}.categories-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%]{left:-15px}.categories-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%]{right:-15px}.categories-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.categories-slider-wrapper[_ngcontent-%COMP%]{padding:0 10px}.categories-slider[_ngcontent-%COMP%]{gap:15px}.categories-slider[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]{flex:0 0 160px;width:160px}.category-image-container[_ngcontent-%COMP%]{height:100px}.category-info[_ngcontent-%COMP%]{padding:12px}}@media (max-width: 480px){.categories-slider[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]{flex:0 0 150px;width:150px}}\"]\n      });\n    }\n  }\n  return ShopByCategoryComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}