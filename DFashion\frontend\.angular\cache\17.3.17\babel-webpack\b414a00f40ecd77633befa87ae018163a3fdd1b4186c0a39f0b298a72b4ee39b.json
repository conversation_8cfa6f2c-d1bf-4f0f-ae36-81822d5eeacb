{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, w as writeTask, h, H as Host, f as getElement, i as forceUpdate } from './index-a1a47f01.js';\nimport { c as createLegacyFormController } from './form-controller-21dd62b1.js';\nimport { c as createNotchController } from './notch-controller-6bd3e0f9.js';\nimport { j as debounceEvent, i as inheritAriaAttributes, k as inheritAttributes, c as componentOnReady, h as findItemLabel } from './helpers-be245865.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { c as createSlotMutationController, g as getCounterText } from './input.utils-a445f677.js';\nimport { c as createColorClasses, h as hostContext } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nimport './index-a5d50daf.js';\nconst textareaIosCss = \".sc-ion-textarea-ios-h{--background:initial;--color:initial;--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:0.6;--padding-top:0;--padding-end:0;--padding-bottom:0;--padding-start:0;--border-radius:0;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #3880ff);--highlight-color-valid:var(--ion-color-success, #2dd36f);--highlight-color-invalid:var(--ion-color-danger, #eb445a);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2;-webkit-box-sizing:border-box;box-sizing:border-box}.sc-ion-textarea-ios-h:not(.legacy-textarea){min-height:44px}.textarea-label-placement-floating.sc-ion-textarea-ios-h,.textarea-label-placement-stacked.sc-ion-textarea-ios-h{--padding-top:0px;min-height:56px}[cols].sc-ion-textarea-ios-h:not([auto-grow]){width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.legacy-textarea.sc-ion-textarea-ios-h{-ms-flex:1;flex:1;background:var(--background);white-space:pre-wrap}.legacy-textarea.ion-color.sc-ion-textarea-ios-h{color:var(--ion-color-base)}.sc-ion-textarea-ios-h:not(.legacy-textarea){--padding-bottom:8px}.ion-color.sc-ion-textarea-ios-h{--highlight-color-focused:var(--ion-color-base);background:initial}ion-item.sc-ion-textarea-ios-h,ion-item .sc-ion-textarea-ios-h{-ms-flex-item-align:baseline;align-self:baseline}ion-item.sc-ion-textarea-ios-h:not(.item-label),ion-item:not(.item-label) .sc-ion-textarea-ios-h{--padding-start:0}ion-item[slot=start].sc-ion-textarea-ios-h,ion-item [slot=start].sc-ion-textarea-ios-h,ion-item[slot=end].sc-ion-textarea-ios-h,ion-item [slot=end].sc-ion-textarea-ios-h{width:auto}.native-textarea.sc-ion-textarea-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%;border:0;outline:none;background:transparent;white-space:pre-wrap;z-index:1;-webkit-box-sizing:border-box;box-sizing:border-box;resize:none;-webkit-appearance:none;-moz-appearance:none;appearance:none}.native-textarea.sc-ion-textarea-ios::-webkit-input-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-ios::-moz-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-ios:-ms-input-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-ios::-ms-input-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-ios::placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.legacy-textarea.sc-ion-textarea-ios-h .native-textarea.sc-ion-textarea-ios{white-space:inherit}.legacy-textarea.sc-ion-textarea-ios-h .native-textarea.sc-ion-textarea-ios,.legacy-textarea.sc-ion-textarea-ios-h .textarea-legacy-wrapper.sc-ion-textarea-ios::after{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius)}.native-textarea.sc-ion-textarea-ios{color:inherit;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-align:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;grid-area:1/1/2/2;word-break:break-word}.legacy-textarea.sc-ion-textarea-ios-h .textarea-legacy-wrapper.sc-ion-textarea-ios::after{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;grid-area:1/1/2/2;word-break:break-word}.cloned-input.sc-ion-textarea-ios{top:0;bottom:0;position:absolute;pointer-events:none}@supports (inset-inline-start: 0){.cloned-input.sc-ion-textarea-ios{inset-inline-start:0}}@supports not (inset-inline-start: 0){.cloned-input.sc-ion-textarea-ios{left:0}[dir=rtl].sc-ion-textarea-ios-h .cloned-input.sc-ion-textarea-ios,[dir=rtl] .sc-ion-textarea-ios-h .cloned-input.sc-ion-textarea-ios{left:unset;right:unset;right:0}[dir=rtl].sc-ion-textarea-ios .cloned-input.sc-ion-textarea-ios{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.cloned-input.sc-ion-textarea-ios:dir(rtl){left:unset;right:unset;right:0}}}.cloned-input.sc-ion-textarea-ios:disabled{opacity:1}.legacy-textarea[auto-grow].sc-ion-textarea-ios-h .cloned-input.sc-ion-textarea-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}[auto-grow].sc-ion-textarea-ios-h .cloned-input.sc-ion-textarea-ios{height:100%}[auto-grow].sc-ion-textarea-ios-h .native-textarea.sc-ion-textarea-ios{overflow:hidden}.item-label-floating.item-has-placeholder.sc-ion-textarea-ios-h:not(.item-has-value),.item-label-floating.item-has-placeholder:not(.item-has-value) .sc-ion-textarea-ios-h{opacity:0}.item-label-floating.item-has-placeholder.sc-ion-textarea-ios-h:not(.item-has-value).item-has-focus,.item-label-floating.item-has-placeholder:not(.item-has-value).item-has-focus .sc-ion-textarea-ios-h{-webkit-transition:opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1);opacity:1}.textarea-wrapper.sc-ion-textarea-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:0px;padding-bottom:0px;border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:start;align-items:flex-start;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-textarea-ios{position:relative;width:100%;height:100%}.has-focus.sc-ion-textarea-ios-h textarea.sc-ion-textarea-ios{caret-color:var(--highlight-color)}.native-wrapper.sc-ion-textarea-ios textarea.sc-ion-textarea-ios{-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom)}.native-wrapper.sc-ion-textarea-ios,.textarea-legacy-wrapper.sc-ion-textarea-ios{display:grid;min-width:inherit;max-width:inherit;min-height:inherit;max-height:inherit;grid-auto-rows:100%}.native-wrapper.sc-ion-textarea-ios::after,.textarea-legacy-wrapper.sc-ion-textarea-ios::after{white-space:pre-wrap;content:attr(data-replicated-value) \\\" \\\";visibility:hidden}.native-wrapper.sc-ion-textarea-ios::after{padding-left:0;padding-right:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;border-radius:var(--border-radius);color:inherit;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-align:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;grid-area:1/1/2/2;word-break:break-word}.textarea-wrapper-inner.sc-ion-textarea-ios{display:-ms-flexbox;display:flex;width:100%;min-height:inherit}.ion-touched.ion-invalid.sc-ion-textarea-ios-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-textarea-ios-h{--highlight-color:var(--highlight-color-valid)}.textarea-bottom.sc-ion-textarea-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem}.has-focus.ion-valid.sc-ion-textarea-ios-h,.ion-touched.ion-invalid.sc-ion-textarea-ios-h{--border-color:var(--highlight-color)}.textarea-bottom.sc-ion-textarea-ios .error-text.sc-ion-textarea-ios{display:none;color:var(--highlight-color-invalid)}.textarea-bottom.sc-ion-textarea-ios .helper-text.sc-ion-textarea-ios{display:block;color:var(--ion-color-step-550, #737373)}.ion-touched.ion-invalid.sc-ion-textarea-ios-h .textarea-bottom.sc-ion-textarea-ios .error-text.sc-ion-textarea-ios{display:block}.ion-touched.ion-invalid.sc-ion-textarea-ios-h .textarea-bottom.sc-ion-textarea-ios .helper-text.sc-ion-textarea-ios{display:none}.textarea-bottom.sc-ion-textarea-ios .counter.sc-ion-textarea-ios{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-550, #737373);white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.label-text-wrapper.sc-ion-textarea-ios{-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-textarea-ios,.sc-ion-textarea-ios-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-textarea-ios,.textarea-outline-notch-hidden.sc-ion-textarea-ios{display:none}.textarea-wrapper.sc-ion-textarea-ios textarea.sc-ion-textarea-ios{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.textarea-label-placement-start.sc-ion-textarea-ios-h .textarea-wrapper.sc-ion-textarea-ios{-ms-flex-direction:row;flex-direction:row}.textarea-label-placement-start.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.textarea-label-placement-end.sc-ion-textarea-ios-h .textarea-wrapper.sc-ion-textarea-ios{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.textarea-label-placement-end.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.textarea-label-placement-fixed.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.textarea-label-placement-fixed.sc-ion-textarea-ios-h .label-text.sc-ion-textarea-ios{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.textarea-label-placement-stacked.sc-ion-textarea-ios-h .textarea-wrapper.sc-ion-textarea-ios,.textarea-label-placement-floating.sc-ion-textarea-ios-h .textarea-wrapper.sc-ion-textarea-ios{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.textarea-label-placement-stacked.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,.textarea-label-placement-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-transform-origin:left top;transform-origin:left top;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;max-width:100%;z-index:2}[dir=rtl].sc-ion-textarea-ios-h -no-combinator.textarea-label-placement-stacked.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl] .sc-ion-textarea-ios-h -no-combinator.textarea-label-placement-stacked.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl].textarea-label-placement-stacked.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl] .textarea-label-placement-stacked.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl].sc-ion-textarea-ios-h -no-combinator.textarea-label-placement-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl] .sc-ion-textarea-ios-h -no-combinator.textarea-label-placement-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl].textarea-label-placement-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl] .textarea-label-placement-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.textarea-label-placement-stacked.sc-ion-textarea-ios-h:dir(rtl) .label-text-wrapper.sc-ion-textarea-ios,.textarea-label-placement-floating.sc-ion-textarea-ios-h:dir(rtl) .label-text-wrapper.sc-ion-textarea-ios{-webkit-transform-origin:right top;transform-origin:right top}}.textarea-label-placement-stacked.sc-ion-textarea-ios-h textarea.sc-ion-textarea-ios,.textarea-label-placement-floating.sc-ion-textarea-ios-h textarea.sc-ion-textarea-ios,.textarea-label-placement-stacked[auto-grow].sc-ion-textarea-ios-h .native-wrapper.sc-ion-textarea-ios::after,.textarea-label-placement-floating[auto-grow].sc-ion-textarea-ios-h .native-wrapper.sc-ion-textarea-ios::after{-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:8px;margin-bottom:0px}.sc-ion-textarea-ios-h.textarea-label-placement-stacked.sc-ion-textarea-ios-s>[slot=start],.sc-ion-textarea-ios-h.textarea-label-placement-stacked .sc-ion-textarea-ios-s>[slot=start],.sc-ion-textarea-ios-h.textarea-label-placement-stacked.sc-ion-textarea-ios-s>[slot=end],.sc-ion-textarea-ios-h.textarea-label-placement-stacked .sc-ion-textarea-ios-s>[slot=end],.sc-ion-textarea-ios-h.textarea-label-placement-floating.sc-ion-textarea-ios-s>[slot=start],.sc-ion-textarea-ios-h.textarea-label-placement-floating .sc-ion-textarea-ios-s>[slot=start],.sc-ion-textarea-ios-h.textarea-label-placement-floating.sc-ion-textarea-ios-s>[slot=end],.sc-ion-textarea-ios-h.textarea-label-placement-floating .sc-ion-textarea-ios-s>[slot=end]{margin-top:8px}.textarea-label-placement-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.textarea-label-placement-floating.sc-ion-textarea-ios-h textarea.sc-ion-textarea-ios{opacity:0}.has-focus.textarea-label-placement-floating.sc-ion-textarea-ios-h textarea.sc-ion-textarea-ios,.has-value.textarea-label-placement-floating.sc-ion-textarea-ios-h textarea.sc-ion-textarea-ios{opacity:1}.label-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.start-slot-wrapper.sc-ion-textarea-ios,.end-slot-wrapper.sc-ion-textarea-ios{padding-left:0;padding-right:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-negative:0;flex-shrink:0;-ms-flex-item-align:start;align-self:start}.sc-ion-textarea-ios-s>[slot=start],.sc-ion-textarea-ios-s>[slot=end]{margin-top:0}.sc-ion-textarea-ios-s>[slot=start]{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-textarea-ios-s>[slot=end]{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.sc-ion-textarea-ios-h{--border-width:0.55px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));--padding-top:10px;--padding-end:0px;--padding-bottom:8px;--padding-start:0px;font-size:inherit}.legacy-textarea.sc-ion-textarea-ios-h{--padding-top:10px;--padding-end:8px;--padding-bottom:10px;--padding-start:0}.item-label-stacked.sc-ion-textarea-ios-h,.item-label-stacked .sc-ion-textarea-ios-h,.item-label-floating.sc-ion-textarea-ios-h,.item-label-floating .sc-ion-textarea-ios-h{--padding-top:8px;--padding-bottom:8px;--padding-start:0px}.legacy-textarea.sc-ion-textarea-ios-h .native-textarea[disabled].sc-ion-textarea-ios,.textarea-disabled.sc-ion-textarea-ios-h{opacity:0.3}.sc-ion-textarea-ios-s>ion-button[slot=start].button-has-icon-only,.sc-ion-textarea-ios-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}\";\nconst IonTextareaIosStyle0 = textareaIosCss;\nconst textareaMdCss = \".sc-ion-textarea-md-h{--background:initial;--color:initial;--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:0.6;--padding-top:0;--padding-end:0;--padding-bottom:0;--padding-start:0;--border-radius:0;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #3880ff);--highlight-color-valid:var(--ion-color-success, #2dd36f);--highlight-color-invalid:var(--ion-color-danger, #eb445a);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2;-webkit-box-sizing:border-box;box-sizing:border-box}.sc-ion-textarea-md-h:not(.legacy-textarea){min-height:44px}.textarea-label-placement-floating.sc-ion-textarea-md-h,.textarea-label-placement-stacked.sc-ion-textarea-md-h{--padding-top:0px;min-height:56px}[cols].sc-ion-textarea-md-h:not([auto-grow]){width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.legacy-textarea.sc-ion-textarea-md-h{-ms-flex:1;flex:1;background:var(--background);white-space:pre-wrap}.legacy-textarea.ion-color.sc-ion-textarea-md-h{color:var(--ion-color-base)}.sc-ion-textarea-md-h:not(.legacy-textarea){--padding-bottom:8px}.ion-color.sc-ion-textarea-md-h{--highlight-color-focused:var(--ion-color-base);background:initial}ion-item.sc-ion-textarea-md-h,ion-item .sc-ion-textarea-md-h{-ms-flex-item-align:baseline;align-self:baseline}ion-item.sc-ion-textarea-md-h:not(.item-label),ion-item:not(.item-label) .sc-ion-textarea-md-h{--padding-start:0}ion-item[slot=start].sc-ion-textarea-md-h,ion-item [slot=start].sc-ion-textarea-md-h,ion-item[slot=end].sc-ion-textarea-md-h,ion-item [slot=end].sc-ion-textarea-md-h{width:auto}.native-textarea.sc-ion-textarea-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%;border:0;outline:none;background:transparent;white-space:pre-wrap;z-index:1;-webkit-box-sizing:border-box;box-sizing:border-box;resize:none;-webkit-appearance:none;-moz-appearance:none;appearance:none}.native-textarea.sc-ion-textarea-md::-webkit-input-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-md::-moz-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-md:-ms-input-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-md::-ms-input-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-md::placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.legacy-textarea.sc-ion-textarea-md-h .native-textarea.sc-ion-textarea-md{white-space:inherit}.legacy-textarea.sc-ion-textarea-md-h .native-textarea.sc-ion-textarea-md,.legacy-textarea.sc-ion-textarea-md-h .textarea-legacy-wrapper.sc-ion-textarea-md::after{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius)}.native-textarea.sc-ion-textarea-md{color:inherit;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-align:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;grid-area:1/1/2/2;word-break:break-word}.legacy-textarea.sc-ion-textarea-md-h .textarea-legacy-wrapper.sc-ion-textarea-md::after{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;grid-area:1/1/2/2;word-break:break-word}.cloned-input.sc-ion-textarea-md{top:0;bottom:0;position:absolute;pointer-events:none}@supports (inset-inline-start: 0){.cloned-input.sc-ion-textarea-md{inset-inline-start:0}}@supports not (inset-inline-start: 0){.cloned-input.sc-ion-textarea-md{left:0}[dir=rtl].sc-ion-textarea-md-h .cloned-input.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h .cloned-input.sc-ion-textarea-md{left:unset;right:unset;right:0}[dir=rtl].sc-ion-textarea-md .cloned-input.sc-ion-textarea-md{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.cloned-input.sc-ion-textarea-md:dir(rtl){left:unset;right:unset;right:0}}}.cloned-input.sc-ion-textarea-md:disabled{opacity:1}.legacy-textarea[auto-grow].sc-ion-textarea-md-h .cloned-input.sc-ion-textarea-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}[auto-grow].sc-ion-textarea-md-h .cloned-input.sc-ion-textarea-md{height:100%}[auto-grow].sc-ion-textarea-md-h .native-textarea.sc-ion-textarea-md{overflow:hidden}.item-label-floating.item-has-placeholder.sc-ion-textarea-md-h:not(.item-has-value),.item-label-floating.item-has-placeholder:not(.item-has-value) .sc-ion-textarea-md-h{opacity:0}.item-label-floating.item-has-placeholder.sc-ion-textarea-md-h:not(.item-has-value).item-has-focus,.item-label-floating.item-has-placeholder:not(.item-has-value).item-has-focus .sc-ion-textarea-md-h{-webkit-transition:opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1);opacity:1}.textarea-wrapper.sc-ion-textarea-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:0px;padding-bottom:0px;border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:start;align-items:flex-start;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-textarea-md{position:relative;width:100%;height:100%}.has-focus.sc-ion-textarea-md-h textarea.sc-ion-textarea-md{caret-color:var(--highlight-color)}.native-wrapper.sc-ion-textarea-md textarea.sc-ion-textarea-md{-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom)}.native-wrapper.sc-ion-textarea-md,.textarea-legacy-wrapper.sc-ion-textarea-md{display:grid;min-width:inherit;max-width:inherit;min-height:inherit;max-height:inherit;grid-auto-rows:100%}.native-wrapper.sc-ion-textarea-md::after,.textarea-legacy-wrapper.sc-ion-textarea-md::after{white-space:pre-wrap;content:attr(data-replicated-value) \\\" \\\";visibility:hidden}.native-wrapper.sc-ion-textarea-md::after{padding-left:0;padding-right:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;border-radius:var(--border-radius);color:inherit;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-align:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;grid-area:1/1/2/2;word-break:break-word}.textarea-wrapper-inner.sc-ion-textarea-md{display:-ms-flexbox;display:flex;width:100%;min-height:inherit}.ion-touched.ion-invalid.sc-ion-textarea-md-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-textarea-md-h{--highlight-color:var(--highlight-color-valid)}.textarea-bottom.sc-ion-textarea-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem}.has-focus.ion-valid.sc-ion-textarea-md-h,.ion-touched.ion-invalid.sc-ion-textarea-md-h{--border-color:var(--highlight-color)}.textarea-bottom.sc-ion-textarea-md .error-text.sc-ion-textarea-md{display:none;color:var(--highlight-color-invalid)}.textarea-bottom.sc-ion-textarea-md .helper-text.sc-ion-textarea-md{display:block;color:var(--ion-color-step-550, #737373)}.ion-touched.ion-invalid.sc-ion-textarea-md-h .textarea-bottom.sc-ion-textarea-md .error-text.sc-ion-textarea-md{display:block}.ion-touched.ion-invalid.sc-ion-textarea-md-h .textarea-bottom.sc-ion-textarea-md .helper-text.sc-ion-textarea-md{display:none}.textarea-bottom.sc-ion-textarea-md .counter.sc-ion-textarea-md{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-550, #737373);white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.label-text-wrapper.sc-ion-textarea-md{-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-textarea-md,.sc-ion-textarea-md-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-textarea-md,.textarea-outline-notch-hidden.sc-ion-textarea-md{display:none}.textarea-wrapper.sc-ion-textarea-md textarea.sc-ion-textarea-md{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.textarea-label-placement-start.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{-ms-flex-direction:row;flex-direction:row}.textarea-label-placement-start.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.textarea-label-placement-end.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.textarea-label-placement-end.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.textarea-label-placement-fixed.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.textarea-label-placement-fixed.sc-ion-textarea-md-h .label-text.sc-ion-textarea-md{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.textarea-label-placement-stacked.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md,.textarea-label-placement-floating.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform-origin:left top;transform-origin:left top;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;max-width:100%;z-index:2}[dir=rtl].sc-ion-textarea-md-h -no-combinator.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h -no-combinator.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl].textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl].sc-ion-textarea-md-h -no-combinator.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h -no-combinator.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl].textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.textarea-label-placement-stacked.sc-ion-textarea-md-h:dir(rtl) .label-text-wrapper.sc-ion-textarea-md,.textarea-label-placement-floating.sc-ion-textarea-md-h:dir(rtl) .label-text-wrapper.sc-ion-textarea-md{-webkit-transform-origin:right top;transform-origin:right top}}.textarea-label-placement-stacked.sc-ion-textarea-md-h textarea.sc-ion-textarea-md,.textarea-label-placement-floating.sc-ion-textarea-md-h textarea.sc-ion-textarea-md,.textarea-label-placement-stacked[auto-grow].sc-ion-textarea-md-h .native-wrapper.sc-ion-textarea-md::after,.textarea-label-placement-floating[auto-grow].sc-ion-textarea-md-h .native-wrapper.sc-ion-textarea-md::after{-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:8px;margin-bottom:0px}.sc-ion-textarea-md-h.textarea-label-placement-stacked.sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-label-placement-stacked .sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-label-placement-stacked.sc-ion-textarea-md-s>[slot=end],.sc-ion-textarea-md-h.textarea-label-placement-stacked .sc-ion-textarea-md-s>[slot=end],.sc-ion-textarea-md-h.textarea-label-placement-floating.sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-label-placement-floating .sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-label-placement-floating.sc-ion-textarea-md-s>[slot=end],.sc-ion-textarea-md-h.textarea-label-placement-floating .sc-ion-textarea-md-s>[slot=end]{margin-top:8px}.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.textarea-label-placement-floating.sc-ion-textarea-md-h textarea.sc-ion-textarea-md{opacity:0}.has-focus.textarea-label-placement-floating.sc-ion-textarea-md-h textarea.sc-ion-textarea-md,.has-value.textarea-label-placement-floating.sc-ion-textarea-md-h textarea.sc-ion-textarea-md{opacity:1}.label-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.start-slot-wrapper.sc-ion-textarea-md,.end-slot-wrapper.sc-ion-textarea-md{padding-left:0;padding-right:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-negative:0;flex-shrink:0;-ms-flex-item-align:start;align-self:start}.sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-s>[slot=end]{margin-top:0}.sc-ion-textarea-md-s>[slot=start]{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-textarea-md-s>[slot=end]{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.textarea-fill-solid.sc-ion-textarea-md-h{--background:var(--ion-color-step-50, #f2f2f2);--border-color:var(--ion-color-step-500, gray);--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.textarea-fill-solid.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{border-bottom:var(--border-width) var(--border-style) var(--border-color)}.has-focus.textarea-fill-solid.ion-valid.sc-ion-textarea-md-h,.textarea-fill-solid.ion-touched.ion-invalid.sc-ion-textarea-md-h{--border-color:var(--highlight-color)}.textarea-fill-solid.sc-ion-textarea-md-h .textarea-bottom.sc-ion-textarea-md{border-top:none}@media (any-hover: hover){.textarea-fill-solid.sc-ion-textarea-md-h:hover{--background:var(--ion-color-step-100, #e6e6e6);--border-color:var(--ion-color-step-750, #404040)}}.textarea-fill-solid.has-focus.sc-ion-textarea-md-h{--background:var(--ion-color-step-150, #d9d9d9);--border-color:var(--ion-color-step-750, #404040)}.textarea-fill-solid.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}[dir=rtl].sc-ion-textarea-md-h -no-combinator.textarea-fill-solid.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h -no-combinator.textarea-fill-solid.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md,[dir=rtl].textarea-fill-solid.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md,[dir=rtl] .textarea-fill-solid.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}@supports selector(:dir(rtl)){.textarea-fill-solid.sc-ion-textarea-md-h:dir(rtl) .textarea-wrapper.sc-ion-textarea-md{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}}.label-floating.textarea-fill-solid.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{max-width:calc(100% / 0.75)}.textarea-fill-outline.sc-ion-textarea-md-h{--border-color:var(--ion-color-step-300, #b3b3b3);--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.textarea-fill-outline.textarea-shape-round.sc-ion-textarea-md-h{--border-radius:28px;--padding-start:32px;--padding-end:32px}.has-focus.textarea-fill-outline.ion-valid.sc-ion-textarea-md-h,.textarea-fill-outline.ion-touched.ion-invalid.sc-ion-textarea-md-h{--border-color:var(--highlight-color)}@media (any-hover: hover){.textarea-fill-outline.sc-ion-textarea-md-h:hover{--border-color:var(--ion-color-step-750, #404040)}}.textarea-fill-outline.has-focus.sc-ion-textarea-md-h{--border-width:2px;--border-color:var(--highlight-color)}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-bottom.sc-ion-textarea-md{border-top:none}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{border-bottom:none}.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform-origin:left top;transform-origin:left top;position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}[dir=rtl].sc-ion-textarea-md-h -no-combinator.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h -no-combinator.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl].textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl].sc-ion-textarea-md-h -no-combinator.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h -no-combinator.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl].textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h:dir(rtl) .label-text-wrapper.sc-ion-textarea-md,.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h:dir(rtl) .label-text-wrapper.sc-ion-textarea-md{-webkit-transform-origin:right top;transform-origin:right top}}.textarea-fill-outline.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{position:relative}.label-floating.textarea-fill-outline.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc(\\n    (100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75\\n  )}.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h textarea.sc-ion-textarea-md,.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h textarea.sc-ion-textarea-md,.textarea-fill-outline.textarea-label-placement-stacked[auto-grow].sc-ion-textarea-md-h .native-wrapper.sc-ion-textarea-md::after,.textarea-fill-outline.textarea-label-placement-floating[auto-grow].sc-ion-textarea-md-h .native-wrapper.sc-ion-textarea-md::after{-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:12px;margin-bottom:0px}.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-stacked .sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-s>[slot=end],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-stacked .sc-ion-textarea-md-s>[slot=end],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-floating .sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-s>[slot=end],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-floating .sc-ion-textarea-md-s>[slot=end]{margin-top:12px}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-container.sc-ion-textarea-md{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-start.sc-ion-textarea-md,.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-end.sc-ion-textarea-md{pointer-events:none}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-start.sc-ion-textarea-md,.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-notch.sc-ion-textarea-md,.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-end.sc-ion-textarea-md{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color)}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-notch.sc-ion-textarea-md{max-width:calc(100% - var(--padding-start) - var(--padding-end))}.textarea-fill-outline.sc-ion-textarea-md-h .notch-spacer.sc-ion-textarea-md{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none;-webkit-box-sizing:content-box;box-sizing:content-box}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-start.sc-ion-textarea-md{border-top-left-radius:var(--border-radius);border-top-right-radius:0px;border-bottom-right-radius:0px;border-bottom-left-radius:var(--border-radius);-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color);width:calc(var(--padding-start) - 4px)}[dir=rtl].sc-ion-textarea-md-h -no-combinator.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-start.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h -no-combinator.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-start.sc-ion-textarea-md,[dir=rtl].textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-start.sc-ion-textarea-md,[dir=rtl] .textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-start.sc-ion-textarea-md{border-top-left-radius:0px;border-top-right-radius:var(--border-radius);border-bottom-right-radius:var(--border-radius);border-bottom-left-radius:0px}@supports selector(:dir(rtl)){.textarea-fill-outline.sc-ion-textarea-md-h:dir(rtl) .textarea-outline-start.sc-ion-textarea-md{border-top-left-radius:0px;border-top-right-radius:var(--border-radius);border-bottom-right-radius:var(--border-radius);border-bottom-left-radius:0px}}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-end.sc-ion-textarea-md{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color);border-top-left-radius:0px;border-top-right-radius:var(--border-radius);border-bottom-right-radius:var(--border-radius);border-bottom-left-radius:0px;-ms-flex-positive:1;flex-grow:1}[dir=rtl].sc-ion-textarea-md-h -no-combinator.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-end.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h -no-combinator.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-end.sc-ion-textarea-md,[dir=rtl].textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-end.sc-ion-textarea-md,[dir=rtl] .textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-end.sc-ion-textarea-md{border-top-left-radius:var(--border-radius);border-top-right-radius:0px;border-bottom-right-radius:0px;border-bottom-left-radius:var(--border-radius)}@supports selector(:dir(rtl)){.textarea-fill-outline.sc-ion-textarea-md-h:dir(rtl) .textarea-outline-end.sc-ion-textarea-md{border-top-left-radius:var(--border-radius);border-top-right-radius:0px;border-bottom-right-radius:0px;border-bottom-left-radius:var(--border-radius)}}.label-floating.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-notch.sc-ion-textarea-md{border-top:none}.sc-ion-textarea-md-h{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));--padding-top:18px;--padding-end:0px;--padding-bottom:8px;--padding-start:0px;font-size:inherit}.legacy-textarea.sc-ion-textarea-md-h{--padding-top:10px;--padding-end:0;--padding-bottom:11px;--padding-start:8px;margin-left:0;margin-right:0;margin-top:8px;margin-bottom:0}.item-label-stacked.sc-ion-textarea-md-h,.item-label-stacked .sc-ion-textarea-md-h,.item-label-floating.sc-ion-textarea-md-h,.item-label-floating .sc-ion-textarea-md-h{--padding-top:8px;--padding-bottom:8px;--padding-start:0}.textarea-bottom.sc-ion-textarea-md .counter.sc-ion-textarea-md{letter-spacing:0.0333333333em}.textarea-label-placement-floating.has-focus.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,.textarea-label-placement-stacked.has-focus.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{color:var(--highlight-color)}.has-focus.textarea-label-placement-floating.ion-valid.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,.textarea-label-placement-floating.ion-touched.ion-invalid.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,.has-focus.textarea-label-placement-stacked.ion-valid.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,.textarea-label-placement-stacked.ion-touched.ion-invalid.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{color:var(--highlight-color)}.legacy-textarea.sc-ion-textarea-md-h .native-textarea[disabled].sc-ion-textarea-md,.textarea-disabled.sc-ion-textarea-md-h{opacity:0.38}.textarea-highlight.sc-ion-textarea-md{bottom:-1px;position:absolute;width:100%;height:2px;-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}@supports (inset-inline-start: 0){.textarea-highlight.sc-ion-textarea-md{inset-inline-start:0}}@supports not (inset-inline-start: 0){.textarea-highlight.sc-ion-textarea-md{left:0}[dir=rtl].sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md{left:unset;right:unset;right:0}[dir=rtl].sc-ion-textarea-md .textarea-highlight.sc-ion-textarea-md{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.textarea-highlight.sc-ion-textarea-md:dir(rtl){left:unset;right:unset;right:0}}}.has-focus.sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md{-webkit-transform:scale(1);transform:scale(1)}.in-item.sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md{bottom:0}@supports (inset-inline-start: 0){.in-item.sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md{inset-inline-start:0}}@supports not (inset-inline-start: 0){.in-item.sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md{left:0}[dir=rtl].sc-ion-textarea-md-h -no-combinator.in-item.sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h -no-combinator.in-item.sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md,[dir=rtl].in-item.sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md,[dir=rtl] .in-item.sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.in-item.sc-ion-textarea-md-h:dir(rtl) .textarea-highlight.sc-ion-textarea-md{left:unset;right:unset;right:0}}}.textarea-shape-round.sc-ion-textarea-md-h{--border-radius:16px}.sc-ion-textarea-md-s>ion-button[slot=start].button-has-icon-only,.sc-ion-textarea-md-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}\";\nconst IonTextareaMdStyle0 = textareaMdCss;\nconst Textarea = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionInput = createEvent(this, \"ionInput\", 7);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.inputId = `ion-textarea-${textareaIds++}`;\n    /**\n     * `true` if the textarea was cleared as a result of the user typing\n     * with `clearOnEdit` enabled.\n     *\n     * Resets when the textarea loses focus.\n     */\n    this.didTextareaClearOnEdit = false;\n    this.inheritedAttributes = {};\n    // This flag ensures we log the deprecation warning at most once.\n    this.hasLoggedDeprecationWarning = false;\n    // `Event` type is used instead of `InputEvent`\n    // since the types from Stencil are not derived\n    // from the element (e.g. textarea and input\n    // should be InputEvent, but all other elements\n    // should be Event).\n    this.onInput = ev => {\n      const input = ev.target;\n      if (input) {\n        this.value = input.value || '';\n      }\n      this.emitInputChange(ev);\n    };\n    this.onChange = ev => {\n      this.emitValueChange(ev);\n    };\n    this.onFocus = ev => {\n      this.hasFocus = true;\n      this.focusedValue = this.value;\n      this.focusChange();\n      this.ionFocus.emit(ev);\n    };\n    this.onBlur = ev => {\n      this.hasFocus = false;\n      this.focusChange();\n      if (this.focusedValue !== this.value) {\n        /**\n         * Emits the `ionChange` event when the textarea value\n         * is different than the value when the textarea was focused.\n         */\n        this.emitValueChange(ev);\n      }\n      this.didTextareaClearOnEdit = false;\n      this.ionBlur.emit(ev);\n    };\n    this.onKeyDown = ev => {\n      this.checkClearOnEdit(ev);\n    };\n    this.hasFocus = false;\n    this.color = undefined;\n    this.autocapitalize = 'none';\n    this.autofocus = false;\n    this.clearOnEdit = false;\n    this.debounce = undefined;\n    this.disabled = false;\n    this.fill = undefined;\n    this.inputmode = undefined;\n    this.enterkeyhint = undefined;\n    this.maxlength = undefined;\n    this.minlength = undefined;\n    this.name = this.inputId;\n    this.placeholder = undefined;\n    this.readonly = false;\n    this.required = false;\n    this.spellcheck = false;\n    this.cols = undefined;\n    this.rows = undefined;\n    this.wrap = undefined;\n    this.autoGrow = false;\n    this.value = '';\n    this.counter = false;\n    this.counterFormatter = undefined;\n    this.errorText = undefined;\n    this.helperText = undefined;\n    this.label = undefined;\n    this.labelPlacement = 'start';\n    this.legacy = undefined;\n    this.shape = undefined;\n  }\n  debounceChanged() {\n    const {\n      ionInput,\n      debounce,\n      originalIonInput\n    } = this;\n    /**\n     * If debounce is undefined, we have to manually revert the ionInput emitter in case\n     * debounce used to be set to a number. Otherwise, the event would stay debounced.\n     */\n    this.ionInput = debounce === undefined ? originalIonInput !== null && originalIonInput !== void 0 ? originalIonInput : ionInput : debounceEvent(ionInput, debounce);\n  }\n  disabledChanged() {\n    this.emitStyle();\n  }\n  /**\n   * Update the native input element when the value changes\n   */\n  valueChanged() {\n    const nativeInput = this.nativeInput;\n    const value = this.getValue();\n    if (nativeInput && nativeInput.value !== value) {\n      nativeInput.value = value;\n    }\n    this.runAutoGrow();\n    this.emitStyle();\n  }\n  connectedCallback() {\n    const {\n      el\n    } = this;\n    this.legacyFormController = createLegacyFormController(el);\n    this.slotMutationController = createSlotMutationController(el, ['label', 'start', 'end'], () => forceUpdate(this));\n    this.notchController = createNotchController(el, () => this.notchSpacerEl, () => this.labelSlot);\n    this.emitStyle();\n    this.debounceChanged();\n    {\n      document.dispatchEvent(new CustomEvent('ionInputDidLoad', {\n        detail: el\n      }));\n    }\n  }\n  disconnectedCallback() {\n    {\n      document.dispatchEvent(new CustomEvent('ionInputDidUnload', {\n        detail: this.el\n      }));\n    }\n    if (this.slotMutationController) {\n      this.slotMutationController.destroy();\n      this.slotMutationController = undefined;\n    }\n    if (this.notchController) {\n      this.notchController.destroy();\n      this.notchController = undefined;\n    }\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = Object.assign(Object.assign({}, inheritAriaAttributes(this.el)), inheritAttributes(this.el, ['data-form-type', 'title', 'tabindex']));\n  }\n  componentDidLoad() {\n    this.originalIonInput = this.ionInput;\n    this.runAutoGrow();\n  }\n  componentDidRender() {\n    var _a;\n    (_a = this.notchController) === null || _a === void 0 ? void 0 : _a.calculateNotchWidth();\n  }\n  /**\n   * Sets focus on the native `textarea` in `ion-textarea`. Use this method instead of the global\n   * `textarea.focus()`.\n   *\n   * See [managing focus](/docs/developing/managing-focus) for more information.\n   */\n  setFocus() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.nativeInput) {\n        _this.nativeInput.focus();\n      }\n    })();\n  }\n  /**\n   * Returns the native `<textarea>` element used under the hood.\n   */\n  getInputElement() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      /**\n       * If this gets called in certain early lifecycle hooks (ex: Vue onMounted),\n       * nativeInput won't be defined yet with the custom elements build, so wait for it to load in.\n       */\n      if (!_this2.nativeInput) {\n        yield new Promise(resolve => componentOnReady(_this2.el, resolve));\n      }\n      return Promise.resolve(_this2.nativeInput);\n    })();\n  }\n  emitStyle() {\n    if (this.legacyFormController.hasLegacyControl()) {\n      this.ionStyle.emit({\n        interactive: true,\n        textarea: true,\n        input: true,\n        'interactive-disabled': this.disabled,\n        'has-placeholder': this.placeholder !== undefined,\n        'has-value': this.hasValue(),\n        'has-focus': this.hasFocus,\n        // TODO(FW-2876): remove this\n        legacy: !!this.legacy\n      });\n    }\n  }\n  /**\n   * Emits an `ionChange` event.\n   *\n   * This API should be called for user committed changes.\n   * This API should not be used for external value changes.\n   */\n  emitValueChange(event) {\n    const {\n      value\n    } = this;\n    // Checks for both null and undefined values\n    const newValue = value == null ? value : value.toString();\n    // Emitting a value change should update the internal state for tracking the focused value\n    this.focusedValue = newValue;\n    this.ionChange.emit({\n      value: newValue,\n      event\n    });\n  }\n  /**\n   * Emits an `ionInput` event.\n   */\n  emitInputChange(event) {\n    const {\n      value\n    } = this;\n    this.ionInput.emit({\n      value,\n      event\n    });\n  }\n  runAutoGrow() {\n    if (this.nativeInput && this.autoGrow) {\n      writeTask(() => {\n        var _a;\n        if (this.textareaWrapper) {\n          // Replicated value is an attribute to be used in the stylesheet\n          // to set the inner contents of a pseudo element.\n          this.textareaWrapper.dataset.replicatedValue = (_a = this.value) !== null && _a !== void 0 ? _a : '';\n        }\n      });\n    }\n  }\n  /**\n   * Check if we need to clear the text input if clearOnEdit is enabled\n   */\n  checkClearOnEdit(ev) {\n    if (!this.clearOnEdit) {\n      return;\n    }\n    /**\n     * The following keys do not modify the\n     * contents of the input. As a result, pressing\n     * them should not edit the textarea.\n     *\n     * We can't check to see if the value of the textarea\n     * was changed because we call checkClearOnEdit\n     * in a keydown listener, and the key has not yet\n     * been added to the textarea.\n     *\n     * Unlike ion-input, the \"Enter\" key does modify the\n     * textarea by adding a new line, so \"Enter\" is not\n     * included in the IGNORED_KEYS array.\n     */\n    const IGNORED_KEYS = ['Tab', 'Shift', 'Meta', 'Alt', 'Control'];\n    const pressedIgnoredKey = IGNORED_KEYS.includes(ev.key);\n    /**\n     * Clear the textarea if the control has not been previously cleared\n     * during focus.\n     */\n    if (!this.didTextareaClearOnEdit && this.hasValue() && !pressedIgnoredKey) {\n      this.value = '';\n      this.emitInputChange(ev);\n    }\n    /**\n     * Pressing an IGNORED_KEYS first and\n     * then an allowed key will cause the input to not\n     * be cleared.\n     */\n    if (!pressedIgnoredKey) {\n      this.didTextareaClearOnEdit = true;\n    }\n  }\n  focusChange() {\n    this.emitStyle();\n  }\n  hasValue() {\n    return this.getValue() !== '';\n  }\n  getValue() {\n    return this.value || '';\n  }\n  // TODO: FW-2876 - Remove this render function\n  renderLegacyTextarea() {\n    if (!this.hasLoggedDeprecationWarning) {\n      printIonWarning(`ion-textarea now requires providing a label with either the \"label\" property or the \"aria-label\" attribute. To migrate, remove any usage of \"ion-label\" and pass the label text to either the \"label\" property or the \"aria-label\" attribute.\n\nExample: <ion-textarea label=\"Comments\"></ion-textarea>\nExample with aria-label: <ion-textarea aria-label=\"Comments\"></ion-textarea>\n\nFor textareas that do not render the label immediately next to the input, developers may continue to use \"ion-label\" but must manually associate the label with the textarea by using \"aria-labelledby\".\n\nDevelopers can use the \"legacy\" property to continue using the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.`, this.el);\n      this.hasLoggedDeprecationWarning = true;\n    }\n    const mode = getIonMode(this);\n    const value = this.getValue();\n    const labelId = this.inputId + '-lbl';\n    const label = findItemLabel(this.el);\n    if (label) {\n      label.id = labelId;\n    }\n    return h(Host, {\n      \"aria-disabled\": this.disabled ? 'true' : null,\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'legacy-textarea': true\n      })\n    }, h(\"div\", {\n      class: \"textarea-legacy-wrapper\",\n      ref: el => this.textareaWrapper = el\n    }, h(\"textarea\", Object.assign({\n      class: \"native-textarea\",\n      \"aria-labelledby\": label ? label.id : null,\n      ref: el => this.nativeInput = el,\n      autoCapitalize: this.autocapitalize,\n      autoFocus: this.autofocus,\n      enterKeyHint: this.enterkeyhint,\n      inputMode: this.inputmode,\n      disabled: this.disabled,\n      maxLength: this.maxlength,\n      minLength: this.minlength,\n      name: this.name,\n      placeholder: this.placeholder || '',\n      readOnly: this.readonly,\n      required: this.required,\n      spellcheck: this.spellcheck,\n      cols: this.cols,\n      rows: this.rows,\n      wrap: this.wrap,\n      onInput: this.onInput,\n      onChange: this.onChange,\n      onBlur: this.onBlur,\n      onFocus: this.onFocus,\n      onKeyDown: this.onKeyDown\n    }, this.inheritedAttributes), value)));\n  }\n  renderLabel() {\n    const {\n      label\n    } = this;\n    return h(\"div\", {\n      class: {\n        'label-text-wrapper': true,\n        'label-text-wrapper-hidden': !this.hasLabel\n      }\n    }, label === undefined ? h(\"slot\", {\n      name: \"label\"\n    }) : h(\"div\", {\n      class: \"label-text\"\n    }, label));\n  }\n  /**\n   * Gets any content passed into the `label` slot,\n   * not the <slot> definition.\n   */\n  get labelSlot() {\n    return this.el.querySelector('[slot=\"label\"]');\n  }\n  /**\n   * Returns `true` if label content is provided\n   * either by a prop or a content. If you want\n   * to get the plaintext value of the label use\n   * the `labelText` getter instead.\n   */\n  get hasLabel() {\n    return this.label !== undefined || this.labelSlot !== null;\n  }\n  /**\n   * Renders the border container when fill=\"outline\".\n   */\n  renderLabelContainer() {\n    const mode = getIonMode(this);\n    const hasOutlineFill = mode === 'md' && this.fill === 'outline';\n    if (hasOutlineFill) {\n      /**\n       * The outline fill has a special outline\n       * that appears around the textarea and the label.\n       * Certain stacked and floating label placements cause the\n       * label to translate up and create a \"cut out\"\n       * inside of that border by using the notch-spacer element.\n       */\n      return [h(\"div\", {\n        class: \"textarea-outline-container\"\n      }, h(\"div\", {\n        class: \"textarea-outline-start\"\n      }), h(\"div\", {\n        class: {\n          'textarea-outline-notch': true,\n          'textarea-outline-notch-hidden': !this.hasLabel\n        }\n      }, h(\"div\", {\n        class: \"notch-spacer\",\n        \"aria-hidden\": \"true\",\n        ref: el => this.notchSpacerEl = el\n      }, this.label)), h(\"div\", {\n        class: \"textarea-outline-end\"\n      })), this.renderLabel()];\n    }\n    /**\n     * If not using the outline style,\n     * we can render just the label.\n     */\n    return this.renderLabel();\n  }\n  /**\n   * Renders the helper text or error text values\n   */\n  renderHintText() {\n    const {\n      helperText,\n      errorText\n    } = this;\n    return [h(\"div\", {\n      class: \"helper-text\"\n    }, helperText), h(\"div\", {\n      class: \"error-text\"\n    }, errorText)];\n  }\n  renderCounter() {\n    const {\n      counter,\n      maxlength,\n      counterFormatter,\n      value\n    } = this;\n    if (counter !== true || maxlength === undefined) {\n      return;\n    }\n    return h(\"div\", {\n      class: \"counter\"\n    }, getCounterText(value, maxlength, counterFormatter));\n  }\n  /**\n   * Responsible for rendering helper text,\n   * error text, and counter. This element should only\n   * be rendered if hint text is set or counter is enabled.\n   */\n  renderBottomContent() {\n    const {\n      counter,\n      helperText,\n      errorText,\n      maxlength\n    } = this;\n    /**\n     * undefined and empty string values should\n     * be treated as not having helper/error text.\n     */\n    const hasHintText = !!helperText || !!errorText;\n    const hasCounter = counter === true && maxlength !== undefined;\n    if (!hasHintText && !hasCounter) {\n      return;\n    }\n    return h(\"div\", {\n      class: \"textarea-bottom\"\n    }, this.renderHintText(), this.renderCounter());\n  }\n  renderTextarea() {\n    const {\n      inputId,\n      disabled,\n      fill,\n      shape,\n      labelPlacement,\n      el,\n      hasFocus\n    } = this;\n    const mode = getIonMode(this);\n    const value = this.getValue();\n    const inItem = hostContext('ion-item', this.el);\n    const shouldRenderHighlight = mode === 'md' && fill !== 'outline' && !inItem;\n    const hasValue = this.hasValue();\n    const hasStartEndSlots = el.querySelector('[slot=\"start\"], [slot=\"end\"]') !== null;\n    /**\n     * If the label is stacked, it should always sit above the textarea.\n     * For floating labels, the label should move above the textarea if\n     * the textarea has a value, is focused, or has anything in either\n     * the start or end slot.\n     *\n     * If there is content in the start slot, the label would overlap\n     * it if not forced to float. This is also applied to the end slot\n     * because with the default or solid fills, the textarea is not\n     * vertically centered in the container, but the label is. This\n     * causes the slots and label to appear vertically offset from each\n     * other when the label isn't floating above the input. This doesn't\n     * apply to the outline fill, but this was not accounted for to keep\n     * things consistent.\n     *\n     * TODO(FW-5592): Remove hasStartEndSlots condition\n     */\n    const labelShouldFloat = labelPlacement === 'stacked' || labelPlacement === 'floating' && (hasValue || hasFocus || hasStartEndSlots);\n    return h(Host, {\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'has-value': hasValue,\n        'has-focus': hasFocus,\n        'label-floating': labelShouldFloat,\n        [`textarea-fill-${fill}`]: fill !== undefined,\n        [`textarea-shape-${shape}`]: shape !== undefined,\n        [`textarea-label-placement-${labelPlacement}`]: true,\n        'textarea-disabled': disabled\n      })\n    }, h(\"label\", {\n      class: \"textarea-wrapper\",\n      htmlFor: inputId\n    }, this.renderLabelContainer(), h(\"div\", {\n      class: \"textarea-wrapper-inner\"\n    }, h(\"div\", {\n      class: \"start-slot-wrapper\"\n    }, h(\"slot\", {\n      name: \"start\"\n    })), h(\"div\", {\n      class: \"native-wrapper\",\n      ref: el => this.textareaWrapper = el\n    }, h(\"textarea\", Object.assign({\n      class: \"native-textarea\",\n      ref: el => this.nativeInput = el,\n      id: inputId,\n      disabled: disabled,\n      autoCapitalize: this.autocapitalize,\n      autoFocus: this.autofocus,\n      enterKeyHint: this.enterkeyhint,\n      inputMode: this.inputmode,\n      minLength: this.minlength,\n      maxLength: this.maxlength,\n      name: this.name,\n      placeholder: this.placeholder || '',\n      readOnly: this.readonly,\n      required: this.required,\n      spellcheck: this.spellcheck,\n      cols: this.cols,\n      rows: this.rows,\n      wrap: this.wrap,\n      onInput: this.onInput,\n      onChange: this.onChange,\n      onBlur: this.onBlur,\n      onFocus: this.onFocus,\n      onKeyDown: this.onKeyDown\n    }, this.inheritedAttributes), value)), h(\"div\", {\n      class: \"end-slot-wrapper\"\n    }, h(\"slot\", {\n      name: \"end\"\n    }))), shouldRenderHighlight && h(\"div\", {\n      class: \"textarea-highlight\"\n    })), this.renderBottomContent());\n  }\n  render() {\n    const {\n      legacyFormController\n    } = this;\n    return legacyFormController.hasLegacyControl() ? this.renderLegacyTextarea() : this.renderTextarea();\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"debounce\": [\"debounceChanged\"],\n      \"disabled\": [\"disabledChanged\"],\n      \"value\": [\"valueChanged\"]\n    };\n  }\n};\nlet textareaIds = 0;\nTextarea.style = {\n  ios: IonTextareaIosStyle0,\n  md: IonTextareaMdStyle0\n};\nexport { Textarea as ion_textarea };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "w", "writeTask", "h", "H", "Host", "f", "getElement", "i", "forceUpdate", "c", "createLegacyFormController", "createNotchController", "j", "debounceEvent", "inheritAriaAttributes", "k", "inheritAttributes", "componentOnReady", "findItemLabel", "p", "printIonWarning", "createSlotMutationController", "g", "getCounterText", "createColorClasses", "hostContext", "b", "getIonMode", "textareaIosCss", "IonTextareaIosStyle0", "textareaMdCss", "IonTextareaMdStyle0", "Textarea", "constructor", "hostRef", "ionChange", "ionInput", "ionStyle", "ionBlur", "ionFocus", "inputId", "textareaIds", "didTextareaClearOnEdit", "inheritedAttributes", "hasLoggedDeprecationWarning", "onInput", "ev", "input", "target", "value", "emitInputChange", "onChange", "emitValueChange", "onFocus", "hasFocus", "focusedValue", "focusChange", "emit", "onBlur", "onKeyDown", "checkClearOnEdit", "color", "undefined", "autocapitalize", "autofocus", "clearOnEdit", "debounce", "disabled", "fill", "inputmode", "enterkeyhint", "maxlength", "minlength", "name", "placeholder", "readonly", "required", "spellcheck", "cols", "rows", "wrap", "autoGrow", "counter", "counterFormatter", "errorText", "helperText", "label", "labelPlacement", "legacy", "shape", "debounce<PERSON><PERSON>ed", "originalIonInput", "disabled<PERSON><PERSON>ed", "emitStyle", "valueChanged", "nativeInput", "getValue", "runAutoGrow", "connectedCallback", "el", "legacyFormController", "slotMutationController", "notchController", "notchSpacerEl", "labelSlot", "document", "dispatchEvent", "CustomEvent", "detail", "disconnectedCallback", "destroy", "componentWillLoad", "Object", "assign", "componentDidLoad", "componentDidRender", "_a", "calculateNotchWidth", "setFocus", "_this", "_asyncToGenerator", "focus", "getInputElement", "_this2", "Promise", "resolve", "hasLegacyControl", "interactive", "textarea", "hasValue", "event", "newValue", "toString", "textareaWrapper", "dataset", "replicatedValue", "IGNORED_KEYS", "pressedIgnoredKey", "includes", "key", "renderLegacyTextarea", "mode", "labelId", "id", "class", "ref", "autoCapitalize", "autoFocus", "enterKeyHint", "inputMode", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "readOnly", "renderLabel", "<PERSON><PERSON><PERSON><PERSON>", "querySelector", "renderLabelContainer", "hasOutlineFill", "renderHintText", "renderCounter", "renderBottomContent", "hasHintText", "<PERSON><PERSON><PERSON><PERSON>", "renderTextarea", "inItem", "should<PERSON>ender<PERSON>ighlight", "hasStartEndSlots", "labelShouldFloat", "htmlFor", "render", "watchers", "style", "ios", "md", "ion_textarea"], "sources": ["E:/Fashion/DFashion/frontend/node_modules/@ionic/core/dist/esm/ion-textarea.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, w as writeTask, h, H as Host, f as getElement, i as forceUpdate } from './index-a1a47f01.js';\nimport { c as createLegacyFormController } from './form-controller-21dd62b1.js';\nimport { c as createNotchController } from './notch-controller-6bd3e0f9.js';\nimport { j as debounceEvent, i as inheritAriaAttributes, k as inheritAttributes, c as componentOnReady, h as findItemLabel } from './helpers-be245865.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { c as createSlotMutationController, g as getCounterText } from './input.utils-a445f677.js';\nimport { c as createColorClasses, h as hostContext } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nimport './index-a5d50daf.js';\n\nconst textareaIosCss = \".sc-ion-textarea-ios-h{--background:initial;--color:initial;--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:0.6;--padding-top:0;--padding-end:0;--padding-bottom:0;--padding-start:0;--border-radius:0;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #3880ff);--highlight-color-valid:var(--ion-color-success, #2dd36f);--highlight-color-invalid:var(--ion-color-danger, #eb445a);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2;-webkit-box-sizing:border-box;box-sizing:border-box}.sc-ion-textarea-ios-h:not(.legacy-textarea){min-height:44px}.textarea-label-placement-floating.sc-ion-textarea-ios-h,.textarea-label-placement-stacked.sc-ion-textarea-ios-h{--padding-top:0px;min-height:56px}[cols].sc-ion-textarea-ios-h:not([auto-grow]){width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.legacy-textarea.sc-ion-textarea-ios-h{-ms-flex:1;flex:1;background:var(--background);white-space:pre-wrap}.legacy-textarea.ion-color.sc-ion-textarea-ios-h{color:var(--ion-color-base)}.sc-ion-textarea-ios-h:not(.legacy-textarea){--padding-bottom:8px}.ion-color.sc-ion-textarea-ios-h{--highlight-color-focused:var(--ion-color-base);background:initial}ion-item.sc-ion-textarea-ios-h,ion-item .sc-ion-textarea-ios-h{-ms-flex-item-align:baseline;align-self:baseline}ion-item.sc-ion-textarea-ios-h:not(.item-label),ion-item:not(.item-label) .sc-ion-textarea-ios-h{--padding-start:0}ion-item[slot=start].sc-ion-textarea-ios-h,ion-item [slot=start].sc-ion-textarea-ios-h,ion-item[slot=end].sc-ion-textarea-ios-h,ion-item [slot=end].sc-ion-textarea-ios-h{width:auto}.native-textarea.sc-ion-textarea-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%;border:0;outline:none;background:transparent;white-space:pre-wrap;z-index:1;-webkit-box-sizing:border-box;box-sizing:border-box;resize:none;-webkit-appearance:none;-moz-appearance:none;appearance:none}.native-textarea.sc-ion-textarea-ios::-webkit-input-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-ios::-moz-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-ios:-ms-input-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-ios::-ms-input-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-ios::placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.legacy-textarea.sc-ion-textarea-ios-h .native-textarea.sc-ion-textarea-ios{white-space:inherit}.legacy-textarea.sc-ion-textarea-ios-h .native-textarea.sc-ion-textarea-ios,.legacy-textarea.sc-ion-textarea-ios-h .textarea-legacy-wrapper.sc-ion-textarea-ios::after{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius)}.native-textarea.sc-ion-textarea-ios{color:inherit;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-align:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;grid-area:1/1/2/2;word-break:break-word}.legacy-textarea.sc-ion-textarea-ios-h .textarea-legacy-wrapper.sc-ion-textarea-ios::after{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;grid-area:1/1/2/2;word-break:break-word}.cloned-input.sc-ion-textarea-ios{top:0;bottom:0;position:absolute;pointer-events:none}@supports (inset-inline-start: 0){.cloned-input.sc-ion-textarea-ios{inset-inline-start:0}}@supports not (inset-inline-start: 0){.cloned-input.sc-ion-textarea-ios{left:0}[dir=rtl].sc-ion-textarea-ios-h .cloned-input.sc-ion-textarea-ios,[dir=rtl] .sc-ion-textarea-ios-h .cloned-input.sc-ion-textarea-ios{left:unset;right:unset;right:0}[dir=rtl].sc-ion-textarea-ios .cloned-input.sc-ion-textarea-ios{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.cloned-input.sc-ion-textarea-ios:dir(rtl){left:unset;right:unset;right:0}}}.cloned-input.sc-ion-textarea-ios:disabled{opacity:1}.legacy-textarea[auto-grow].sc-ion-textarea-ios-h .cloned-input.sc-ion-textarea-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}[auto-grow].sc-ion-textarea-ios-h .cloned-input.sc-ion-textarea-ios{height:100%}[auto-grow].sc-ion-textarea-ios-h .native-textarea.sc-ion-textarea-ios{overflow:hidden}.item-label-floating.item-has-placeholder.sc-ion-textarea-ios-h:not(.item-has-value),.item-label-floating.item-has-placeholder:not(.item-has-value) .sc-ion-textarea-ios-h{opacity:0}.item-label-floating.item-has-placeholder.sc-ion-textarea-ios-h:not(.item-has-value).item-has-focus,.item-label-floating.item-has-placeholder:not(.item-has-value).item-has-focus .sc-ion-textarea-ios-h{-webkit-transition:opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1);opacity:1}.textarea-wrapper.sc-ion-textarea-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:0px;padding-bottom:0px;border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:start;align-items:flex-start;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-textarea-ios{position:relative;width:100%;height:100%}.has-focus.sc-ion-textarea-ios-h textarea.sc-ion-textarea-ios{caret-color:var(--highlight-color)}.native-wrapper.sc-ion-textarea-ios textarea.sc-ion-textarea-ios{-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom)}.native-wrapper.sc-ion-textarea-ios,.textarea-legacy-wrapper.sc-ion-textarea-ios{display:grid;min-width:inherit;max-width:inherit;min-height:inherit;max-height:inherit;grid-auto-rows:100%}.native-wrapper.sc-ion-textarea-ios::after,.textarea-legacy-wrapper.sc-ion-textarea-ios::after{white-space:pre-wrap;content:attr(data-replicated-value) \\\" \\\";visibility:hidden}.native-wrapper.sc-ion-textarea-ios::after{padding-left:0;padding-right:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;border-radius:var(--border-radius);color:inherit;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-align:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;grid-area:1/1/2/2;word-break:break-word}.textarea-wrapper-inner.sc-ion-textarea-ios{display:-ms-flexbox;display:flex;width:100%;min-height:inherit}.ion-touched.ion-invalid.sc-ion-textarea-ios-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-textarea-ios-h{--highlight-color:var(--highlight-color-valid)}.textarea-bottom.sc-ion-textarea-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem}.has-focus.ion-valid.sc-ion-textarea-ios-h,.ion-touched.ion-invalid.sc-ion-textarea-ios-h{--border-color:var(--highlight-color)}.textarea-bottom.sc-ion-textarea-ios .error-text.sc-ion-textarea-ios{display:none;color:var(--highlight-color-invalid)}.textarea-bottom.sc-ion-textarea-ios .helper-text.sc-ion-textarea-ios{display:block;color:var(--ion-color-step-550, #737373)}.ion-touched.ion-invalid.sc-ion-textarea-ios-h .textarea-bottom.sc-ion-textarea-ios .error-text.sc-ion-textarea-ios{display:block}.ion-touched.ion-invalid.sc-ion-textarea-ios-h .textarea-bottom.sc-ion-textarea-ios .helper-text.sc-ion-textarea-ios{display:none}.textarea-bottom.sc-ion-textarea-ios .counter.sc-ion-textarea-ios{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-550, #737373);white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.label-text-wrapper.sc-ion-textarea-ios{-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-textarea-ios,.sc-ion-textarea-ios-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-textarea-ios,.textarea-outline-notch-hidden.sc-ion-textarea-ios{display:none}.textarea-wrapper.sc-ion-textarea-ios textarea.sc-ion-textarea-ios{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.textarea-label-placement-start.sc-ion-textarea-ios-h .textarea-wrapper.sc-ion-textarea-ios{-ms-flex-direction:row;flex-direction:row}.textarea-label-placement-start.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.textarea-label-placement-end.sc-ion-textarea-ios-h .textarea-wrapper.sc-ion-textarea-ios{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.textarea-label-placement-end.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.textarea-label-placement-fixed.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.textarea-label-placement-fixed.sc-ion-textarea-ios-h .label-text.sc-ion-textarea-ios{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.textarea-label-placement-stacked.sc-ion-textarea-ios-h .textarea-wrapper.sc-ion-textarea-ios,.textarea-label-placement-floating.sc-ion-textarea-ios-h .textarea-wrapper.sc-ion-textarea-ios{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.textarea-label-placement-stacked.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,.textarea-label-placement-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-transform-origin:left top;transform-origin:left top;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;max-width:100%;z-index:2}[dir=rtl].sc-ion-textarea-ios-h -no-combinator.textarea-label-placement-stacked.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl] .sc-ion-textarea-ios-h -no-combinator.textarea-label-placement-stacked.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl].textarea-label-placement-stacked.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl] .textarea-label-placement-stacked.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl].sc-ion-textarea-ios-h -no-combinator.textarea-label-placement-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl] .sc-ion-textarea-ios-h -no-combinator.textarea-label-placement-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl].textarea-label-placement-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios,[dir=rtl] .textarea-label-placement-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.textarea-label-placement-stacked.sc-ion-textarea-ios-h:dir(rtl) .label-text-wrapper.sc-ion-textarea-ios,.textarea-label-placement-floating.sc-ion-textarea-ios-h:dir(rtl) .label-text-wrapper.sc-ion-textarea-ios{-webkit-transform-origin:right top;transform-origin:right top}}.textarea-label-placement-stacked.sc-ion-textarea-ios-h textarea.sc-ion-textarea-ios,.textarea-label-placement-floating.sc-ion-textarea-ios-h textarea.sc-ion-textarea-ios,.textarea-label-placement-stacked[auto-grow].sc-ion-textarea-ios-h .native-wrapper.sc-ion-textarea-ios::after,.textarea-label-placement-floating[auto-grow].sc-ion-textarea-ios-h .native-wrapper.sc-ion-textarea-ios::after{-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:8px;margin-bottom:0px}.sc-ion-textarea-ios-h.textarea-label-placement-stacked.sc-ion-textarea-ios-s>[slot=start],.sc-ion-textarea-ios-h.textarea-label-placement-stacked .sc-ion-textarea-ios-s>[slot=start],.sc-ion-textarea-ios-h.textarea-label-placement-stacked.sc-ion-textarea-ios-s>[slot=end],.sc-ion-textarea-ios-h.textarea-label-placement-stacked .sc-ion-textarea-ios-s>[slot=end],.sc-ion-textarea-ios-h.textarea-label-placement-floating.sc-ion-textarea-ios-s>[slot=start],.sc-ion-textarea-ios-h.textarea-label-placement-floating .sc-ion-textarea-ios-s>[slot=start],.sc-ion-textarea-ios-h.textarea-label-placement-floating.sc-ion-textarea-ios-s>[slot=end],.sc-ion-textarea-ios-h.textarea-label-placement-floating .sc-ion-textarea-ios-s>[slot=end]{margin-top:8px}.textarea-label-placement-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.textarea-label-placement-floating.sc-ion-textarea-ios-h textarea.sc-ion-textarea-ios{opacity:0}.has-focus.textarea-label-placement-floating.sc-ion-textarea-ios-h textarea.sc-ion-textarea-ios,.has-value.textarea-label-placement-floating.sc-ion-textarea-ios-h textarea.sc-ion-textarea-ios{opacity:1}.label-floating.sc-ion-textarea-ios-h .label-text-wrapper.sc-ion-textarea-ios{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.start-slot-wrapper.sc-ion-textarea-ios,.end-slot-wrapper.sc-ion-textarea-ios{padding-left:0;padding-right:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-negative:0;flex-shrink:0;-ms-flex-item-align:start;align-self:start}.sc-ion-textarea-ios-s>[slot=start],.sc-ion-textarea-ios-s>[slot=end]{margin-top:0}.sc-ion-textarea-ios-s>[slot=start]{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-textarea-ios-s>[slot=end]{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.sc-ion-textarea-ios-h{--border-width:0.55px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, #c8c7cc)));--padding-top:10px;--padding-end:0px;--padding-bottom:8px;--padding-start:0px;font-size:inherit}.legacy-textarea.sc-ion-textarea-ios-h{--padding-top:10px;--padding-end:8px;--padding-bottom:10px;--padding-start:0}.item-label-stacked.sc-ion-textarea-ios-h,.item-label-stacked .sc-ion-textarea-ios-h,.item-label-floating.sc-ion-textarea-ios-h,.item-label-floating .sc-ion-textarea-ios-h{--padding-top:8px;--padding-bottom:8px;--padding-start:0px}.legacy-textarea.sc-ion-textarea-ios-h .native-textarea[disabled].sc-ion-textarea-ios,.textarea-disabled.sc-ion-textarea-ios-h{opacity:0.3}.sc-ion-textarea-ios-s>ion-button[slot=start].button-has-icon-only,.sc-ion-textarea-ios-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}\";\nconst IonTextareaIosStyle0 = textareaIosCss;\n\nconst textareaMdCss = \".sc-ion-textarea-md-h{--background:initial;--color:initial;--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:0.6;--padding-top:0;--padding-end:0;--padding-bottom:0;--padding-start:0;--border-radius:0;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #3880ff);--highlight-color-valid:var(--ion-color-success, #2dd36f);--highlight-color-invalid:var(--ion-color-danger, #eb445a);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2;-webkit-box-sizing:border-box;box-sizing:border-box}.sc-ion-textarea-md-h:not(.legacy-textarea){min-height:44px}.textarea-label-placement-floating.sc-ion-textarea-md-h,.textarea-label-placement-stacked.sc-ion-textarea-md-h{--padding-top:0px;min-height:56px}[cols].sc-ion-textarea-md-h:not([auto-grow]){width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.legacy-textarea.sc-ion-textarea-md-h{-ms-flex:1;flex:1;background:var(--background);white-space:pre-wrap}.legacy-textarea.ion-color.sc-ion-textarea-md-h{color:var(--ion-color-base)}.sc-ion-textarea-md-h:not(.legacy-textarea){--padding-bottom:8px}.ion-color.sc-ion-textarea-md-h{--highlight-color-focused:var(--ion-color-base);background:initial}ion-item.sc-ion-textarea-md-h,ion-item .sc-ion-textarea-md-h{-ms-flex-item-align:baseline;align-self:baseline}ion-item.sc-ion-textarea-md-h:not(.item-label),ion-item:not(.item-label) .sc-ion-textarea-md-h{--padding-start:0}ion-item[slot=start].sc-ion-textarea-md-h,ion-item [slot=start].sc-ion-textarea-md-h,ion-item[slot=end].sc-ion-textarea-md-h,ion-item [slot=end].sc-ion-textarea-md-h{width:auto}.native-textarea.sc-ion-textarea-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;max-height:100%;border:0;outline:none;background:transparent;white-space:pre-wrap;z-index:1;-webkit-box-sizing:border-box;box-sizing:border-box;resize:none;-webkit-appearance:none;-moz-appearance:none;appearance:none}.native-textarea.sc-ion-textarea-md::-webkit-input-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-md::-moz-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-md:-ms-input-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-md::-ms-input-placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-textarea.sc-ion-textarea-md::placeholder{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.legacy-textarea.sc-ion-textarea-md-h .native-textarea.sc-ion-textarea-md{white-space:inherit}.legacy-textarea.sc-ion-textarea-md-h .native-textarea.sc-ion-textarea-md,.legacy-textarea.sc-ion-textarea-md-h .textarea-legacy-wrapper.sc-ion-textarea-md::after{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius)}.native-textarea.sc-ion-textarea-md{color:inherit;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-align:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;grid-area:1/1/2/2;word-break:break-word}.legacy-textarea.sc-ion-textarea-md-h .textarea-legacy-wrapper.sc-ion-textarea-md::after{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;grid-area:1/1/2/2;word-break:break-word}.cloned-input.sc-ion-textarea-md{top:0;bottom:0;position:absolute;pointer-events:none}@supports (inset-inline-start: 0){.cloned-input.sc-ion-textarea-md{inset-inline-start:0}}@supports not (inset-inline-start: 0){.cloned-input.sc-ion-textarea-md{left:0}[dir=rtl].sc-ion-textarea-md-h .cloned-input.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h .cloned-input.sc-ion-textarea-md{left:unset;right:unset;right:0}[dir=rtl].sc-ion-textarea-md .cloned-input.sc-ion-textarea-md{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.cloned-input.sc-ion-textarea-md:dir(rtl){left:unset;right:unset;right:0}}}.cloned-input.sc-ion-textarea-md:disabled{opacity:1}.legacy-textarea[auto-grow].sc-ion-textarea-md-h .cloned-input.sc-ion-textarea-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}[auto-grow].sc-ion-textarea-md-h .cloned-input.sc-ion-textarea-md{height:100%}[auto-grow].sc-ion-textarea-md-h .native-textarea.sc-ion-textarea-md{overflow:hidden}.item-label-floating.item-has-placeholder.sc-ion-textarea-md-h:not(.item-has-value),.item-label-floating.item-has-placeholder:not(.item-has-value) .sc-ion-textarea-md-h{opacity:0}.item-label-floating.item-has-placeholder.sc-ion-textarea-md-h:not(.item-has-value).item-has-focus,.item-label-floating.item-has-placeholder:not(.item-has-value).item-has-focus .sc-ion-textarea-md-h{-webkit-transition:opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1);opacity:1}.textarea-wrapper.sc-ion-textarea-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:0px;padding-bottom:0px;border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:start;align-items:flex-start;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-textarea-md{position:relative;width:100%;height:100%}.has-focus.sc-ion-textarea-md-h textarea.sc-ion-textarea-md{caret-color:var(--highlight-color)}.native-wrapper.sc-ion-textarea-md textarea.sc-ion-textarea-md{-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom)}.native-wrapper.sc-ion-textarea-md,.textarea-legacy-wrapper.sc-ion-textarea-md{display:grid;min-width:inherit;max-width:inherit;min-height:inherit;max-height:inherit;grid-auto-rows:100%}.native-wrapper.sc-ion-textarea-md::after,.textarea-legacy-wrapper.sc-ion-textarea-md::after{white-space:pre-wrap;content:attr(data-replicated-value) \\\" \\\";visibility:hidden}.native-wrapper.sc-ion-textarea-md::after{padding-left:0;padding-right:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;border-radius:var(--border-radius);color:inherit;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-align:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;grid-area:1/1/2/2;word-break:break-word}.textarea-wrapper-inner.sc-ion-textarea-md{display:-ms-flexbox;display:flex;width:100%;min-height:inherit}.ion-touched.ion-invalid.sc-ion-textarea-md-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-textarea-md-h{--highlight-color:var(--highlight-color-valid)}.textarea-bottom.sc-ion-textarea-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem}.has-focus.ion-valid.sc-ion-textarea-md-h,.ion-touched.ion-invalid.sc-ion-textarea-md-h{--border-color:var(--highlight-color)}.textarea-bottom.sc-ion-textarea-md .error-text.sc-ion-textarea-md{display:none;color:var(--highlight-color-invalid)}.textarea-bottom.sc-ion-textarea-md .helper-text.sc-ion-textarea-md{display:block;color:var(--ion-color-step-550, #737373)}.ion-touched.ion-invalid.sc-ion-textarea-md-h .textarea-bottom.sc-ion-textarea-md .error-text.sc-ion-textarea-md{display:block}.ion-touched.ion-invalid.sc-ion-textarea-md-h .textarea-bottom.sc-ion-textarea-md .helper-text.sc-ion-textarea-md{display:none}.textarea-bottom.sc-ion-textarea-md .counter.sc-ion-textarea-md{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-550, #737373);white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.label-text-wrapper.sc-ion-textarea-md{-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-textarea-md,.sc-ion-textarea-md-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-textarea-md,.textarea-outline-notch-hidden.sc-ion-textarea-md{display:none}.textarea-wrapper.sc-ion-textarea-md textarea.sc-ion-textarea-md{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.textarea-label-placement-start.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{-ms-flex-direction:row;flex-direction:row}.textarea-label-placement-start.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.textarea-label-placement-end.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.textarea-label-placement-end.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.textarea-label-placement-fixed.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.textarea-label-placement-fixed.sc-ion-textarea-md-h .label-text.sc-ion-textarea-md{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.textarea-label-placement-stacked.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md,.textarea-label-placement-floating.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform-origin:left top;transform-origin:left top;-webkit-padding-start:0px;padding-inline-start:0px;-webkit-padding-end:0px;padding-inline-end:0px;padding-top:0px;padding-bottom:0px;max-width:100%;z-index:2}[dir=rtl].sc-ion-textarea-md-h -no-combinator.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h -no-combinator.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl].textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl].sc-ion-textarea-md-h -no-combinator.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h -no-combinator.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl].textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.textarea-label-placement-stacked.sc-ion-textarea-md-h:dir(rtl) .label-text-wrapper.sc-ion-textarea-md,.textarea-label-placement-floating.sc-ion-textarea-md-h:dir(rtl) .label-text-wrapper.sc-ion-textarea-md{-webkit-transform-origin:right top;transform-origin:right top}}.textarea-label-placement-stacked.sc-ion-textarea-md-h textarea.sc-ion-textarea-md,.textarea-label-placement-floating.sc-ion-textarea-md-h textarea.sc-ion-textarea-md,.textarea-label-placement-stacked[auto-grow].sc-ion-textarea-md-h .native-wrapper.sc-ion-textarea-md::after,.textarea-label-placement-floating[auto-grow].sc-ion-textarea-md-h .native-wrapper.sc-ion-textarea-md::after{-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:8px;margin-bottom:0px}.sc-ion-textarea-md-h.textarea-label-placement-stacked.sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-label-placement-stacked .sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-label-placement-stacked.sc-ion-textarea-md-s>[slot=end],.sc-ion-textarea-md-h.textarea-label-placement-stacked .sc-ion-textarea-md-s>[slot=end],.sc-ion-textarea-md-h.textarea-label-placement-floating.sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-label-placement-floating .sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-label-placement-floating.sc-ion-textarea-md-s>[slot=end],.sc-ion-textarea-md-h.textarea-label-placement-floating .sc-ion-textarea-md-s>[slot=end]{margin-top:8px}.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.textarea-label-placement-floating.sc-ion-textarea-md-h textarea.sc-ion-textarea-md{opacity:0}.has-focus.textarea-label-placement-floating.sc-ion-textarea-md-h textarea.sc-ion-textarea-md,.has-value.textarea-label-placement-floating.sc-ion-textarea-md-h textarea.sc-ion-textarea-md{opacity:1}.label-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.start-slot-wrapper.sc-ion-textarea-md,.end-slot-wrapper.sc-ion-textarea-md{padding-left:0;padding-right:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-negative:0;flex-shrink:0;-ms-flex-item-align:start;align-self:start}.sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-s>[slot=end]{margin-top:0}.sc-ion-textarea-md-s>[slot=start]{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-textarea-md-s>[slot=end]{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.textarea-fill-solid.sc-ion-textarea-md-h{--background:var(--ion-color-step-50, #f2f2f2);--border-color:var(--ion-color-step-500, gray);--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.textarea-fill-solid.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{border-bottom:var(--border-width) var(--border-style) var(--border-color)}.has-focus.textarea-fill-solid.ion-valid.sc-ion-textarea-md-h,.textarea-fill-solid.ion-touched.ion-invalid.sc-ion-textarea-md-h{--border-color:var(--highlight-color)}.textarea-fill-solid.sc-ion-textarea-md-h .textarea-bottom.sc-ion-textarea-md{border-top:none}@media (any-hover: hover){.textarea-fill-solid.sc-ion-textarea-md-h:hover{--background:var(--ion-color-step-100, #e6e6e6);--border-color:var(--ion-color-step-750, #404040)}}.textarea-fill-solid.has-focus.sc-ion-textarea-md-h{--background:var(--ion-color-step-150, #d9d9d9);--border-color:var(--ion-color-step-750, #404040)}.textarea-fill-solid.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}[dir=rtl].sc-ion-textarea-md-h -no-combinator.textarea-fill-solid.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h -no-combinator.textarea-fill-solid.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md,[dir=rtl].textarea-fill-solid.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md,[dir=rtl] .textarea-fill-solid.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}@supports selector(:dir(rtl)){.textarea-fill-solid.sc-ion-textarea-md-h:dir(rtl) .textarea-wrapper.sc-ion-textarea-md{border-top-left-radius:var(--border-radius);border-top-right-radius:var(--border-radius);border-bottom-right-radius:0px;border-bottom-left-radius:0px}}.label-floating.textarea-fill-solid.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{max-width:calc(100% / 0.75)}.textarea-fill-outline.sc-ion-textarea-md-h{--border-color:var(--ion-color-step-300, #b3b3b3);--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.textarea-fill-outline.textarea-shape-round.sc-ion-textarea-md-h{--border-radius:28px;--padding-start:32px;--padding-end:32px}.has-focus.textarea-fill-outline.ion-valid.sc-ion-textarea-md-h,.textarea-fill-outline.ion-touched.ion-invalid.sc-ion-textarea-md-h{--border-color:var(--highlight-color)}@media (any-hover: hover){.textarea-fill-outline.sc-ion-textarea-md-h:hover{--border-color:var(--ion-color-step-750, #404040)}}.textarea-fill-outline.has-focus.sc-ion-textarea-md-h{--border-width:2px;--border-color:var(--highlight-color)}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-bottom.sc-ion-textarea-md{border-top:none}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-wrapper.sc-ion-textarea-md{border-bottom:none}.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform-origin:left top;transform-origin:left top;position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}[dir=rtl].sc-ion-textarea-md-h -no-combinator.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h -no-combinator.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl].textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl].sc-ion-textarea-md-h -no-combinator.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h -no-combinator.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl].textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,[dir=rtl] .textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h:dir(rtl) .label-text-wrapper.sc-ion-textarea-md,.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h:dir(rtl) .label-text-wrapper.sc-ion-textarea-md{-webkit-transform-origin:right top;transform-origin:right top}}.textarea-fill-outline.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{position:relative}.label-floating.textarea-fill-outline.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc(\\n    (100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75\\n  )}.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-h textarea.sc-ion-textarea-md,.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-h textarea.sc-ion-textarea-md,.textarea-fill-outline.textarea-label-placement-stacked[auto-grow].sc-ion-textarea-md-h .native-wrapper.sc-ion-textarea-md::after,.textarea-fill-outline.textarea-label-placement-floating[auto-grow].sc-ion-textarea-md-h .native-wrapper.sc-ion-textarea-md::after{-webkit-margin-start:0px;margin-inline-start:0px;-webkit-margin-end:0px;margin-inline-end:0px;margin-top:12px;margin-bottom:0px}.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-stacked .sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-stacked.sc-ion-textarea-md-s>[slot=end],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-stacked .sc-ion-textarea-md-s>[slot=end],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-floating .sc-ion-textarea-md-s>[slot=start],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-floating.sc-ion-textarea-md-s>[slot=end],.sc-ion-textarea-md-h.textarea-fill-outline.textarea-label-placement-floating .sc-ion-textarea-md-s>[slot=end]{margin-top:12px}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-container.sc-ion-textarea-md{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-start.sc-ion-textarea-md,.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-end.sc-ion-textarea-md{pointer-events:none}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-start.sc-ion-textarea-md,.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-notch.sc-ion-textarea-md,.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-end.sc-ion-textarea-md{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color)}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-notch.sc-ion-textarea-md{max-width:calc(100% - var(--padding-start) - var(--padding-end))}.textarea-fill-outline.sc-ion-textarea-md-h .notch-spacer.sc-ion-textarea-md{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none;-webkit-box-sizing:content-box;box-sizing:content-box}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-start.sc-ion-textarea-md{border-top-left-radius:var(--border-radius);border-top-right-radius:0px;border-bottom-right-radius:0px;border-bottom-left-radius:var(--border-radius);-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color);width:calc(var(--padding-start) - 4px)}[dir=rtl].sc-ion-textarea-md-h -no-combinator.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-start.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h -no-combinator.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-start.sc-ion-textarea-md,[dir=rtl].textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-start.sc-ion-textarea-md,[dir=rtl] .textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-start.sc-ion-textarea-md{border-top-left-radius:0px;border-top-right-radius:var(--border-radius);border-bottom-right-radius:var(--border-radius);border-bottom-left-radius:0px}@supports selector(:dir(rtl)){.textarea-fill-outline.sc-ion-textarea-md-h:dir(rtl) .textarea-outline-start.sc-ion-textarea-md{border-top-left-radius:0px;border-top-right-radius:var(--border-radius);border-bottom-right-radius:var(--border-radius);border-bottom-left-radius:0px}}.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-end.sc-ion-textarea-md{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color);border-top-left-radius:0px;border-top-right-radius:var(--border-radius);border-bottom-right-radius:var(--border-radius);border-bottom-left-radius:0px;-ms-flex-positive:1;flex-grow:1}[dir=rtl].sc-ion-textarea-md-h -no-combinator.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-end.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h -no-combinator.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-end.sc-ion-textarea-md,[dir=rtl].textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-end.sc-ion-textarea-md,[dir=rtl] .textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-end.sc-ion-textarea-md{border-top-left-radius:var(--border-radius);border-top-right-radius:0px;border-bottom-right-radius:0px;border-bottom-left-radius:var(--border-radius)}@supports selector(:dir(rtl)){.textarea-fill-outline.sc-ion-textarea-md-h:dir(rtl) .textarea-outline-end.sc-ion-textarea-md{border-top-left-radius:var(--border-radius);border-top-right-radius:0px;border-bottom-right-radius:0px;border-bottom-left-radius:var(--border-radius)}}.label-floating.textarea-fill-outline.sc-ion-textarea-md-h .textarea-outline-notch.sc-ion-textarea-md{border-top:none}.sc-ion-textarea-md-h{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, rgba(0, 0, 0, 0.13))));--padding-top:18px;--padding-end:0px;--padding-bottom:8px;--padding-start:0px;font-size:inherit}.legacy-textarea.sc-ion-textarea-md-h{--padding-top:10px;--padding-end:0;--padding-bottom:11px;--padding-start:8px;margin-left:0;margin-right:0;margin-top:8px;margin-bottom:0}.item-label-stacked.sc-ion-textarea-md-h,.item-label-stacked .sc-ion-textarea-md-h,.item-label-floating.sc-ion-textarea-md-h,.item-label-floating .sc-ion-textarea-md-h{--padding-top:8px;--padding-bottom:8px;--padding-start:0}.textarea-bottom.sc-ion-textarea-md .counter.sc-ion-textarea-md{letter-spacing:0.0333333333em}.textarea-label-placement-floating.has-focus.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,.textarea-label-placement-stacked.has-focus.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{color:var(--highlight-color)}.has-focus.textarea-label-placement-floating.ion-valid.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,.textarea-label-placement-floating.ion-touched.ion-invalid.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,.has-focus.textarea-label-placement-stacked.ion-valid.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md,.textarea-label-placement-stacked.ion-touched.ion-invalid.sc-ion-textarea-md-h .label-text-wrapper.sc-ion-textarea-md{color:var(--highlight-color)}.legacy-textarea.sc-ion-textarea-md-h .native-textarea[disabled].sc-ion-textarea-md,.textarea-disabled.sc-ion-textarea-md-h{opacity:0.38}.textarea-highlight.sc-ion-textarea-md{bottom:-1px;position:absolute;width:100%;height:2px;-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}@supports (inset-inline-start: 0){.textarea-highlight.sc-ion-textarea-md{inset-inline-start:0}}@supports not (inset-inline-start: 0){.textarea-highlight.sc-ion-textarea-md{left:0}[dir=rtl].sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md{left:unset;right:unset;right:0}[dir=rtl].sc-ion-textarea-md .textarea-highlight.sc-ion-textarea-md{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.textarea-highlight.sc-ion-textarea-md:dir(rtl){left:unset;right:unset;right:0}}}.has-focus.sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md{-webkit-transform:scale(1);transform:scale(1)}.in-item.sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md{bottom:0}@supports (inset-inline-start: 0){.in-item.sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md{inset-inline-start:0}}@supports not (inset-inline-start: 0){.in-item.sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md{left:0}[dir=rtl].sc-ion-textarea-md-h -no-combinator.in-item.sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md,[dir=rtl] .sc-ion-textarea-md-h -no-combinator.in-item.sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md,[dir=rtl].in-item.sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md,[dir=rtl] .in-item.sc-ion-textarea-md-h .textarea-highlight.sc-ion-textarea-md{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.in-item.sc-ion-textarea-md-h:dir(rtl) .textarea-highlight.sc-ion-textarea-md{left:unset;right:unset;right:0}}}.textarea-shape-round.sc-ion-textarea-md-h{--border-radius:16px}.sc-ion-textarea-md-s>ion-button[slot=start].button-has-icon-only,.sc-ion-textarea-md-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}\";\nconst IonTextareaMdStyle0 = textareaMdCss;\n\nconst Textarea = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionInput = createEvent(this, \"ionInput\", 7);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.inputId = `ion-textarea-${textareaIds++}`;\n        /**\n         * `true` if the textarea was cleared as a result of the user typing\n         * with `clearOnEdit` enabled.\n         *\n         * Resets when the textarea loses focus.\n         */\n        this.didTextareaClearOnEdit = false;\n        this.inheritedAttributes = {};\n        // This flag ensures we log the deprecation warning at most once.\n        this.hasLoggedDeprecationWarning = false;\n        // `Event` type is used instead of `InputEvent`\n        // since the types from Stencil are not derived\n        // from the element (e.g. textarea and input\n        // should be InputEvent, but all other elements\n        // should be Event).\n        this.onInput = (ev) => {\n            const input = ev.target;\n            if (input) {\n                this.value = input.value || '';\n            }\n            this.emitInputChange(ev);\n        };\n        this.onChange = (ev) => {\n            this.emitValueChange(ev);\n        };\n        this.onFocus = (ev) => {\n            this.hasFocus = true;\n            this.focusedValue = this.value;\n            this.focusChange();\n            this.ionFocus.emit(ev);\n        };\n        this.onBlur = (ev) => {\n            this.hasFocus = false;\n            this.focusChange();\n            if (this.focusedValue !== this.value) {\n                /**\n                 * Emits the `ionChange` event when the textarea value\n                 * is different than the value when the textarea was focused.\n                 */\n                this.emitValueChange(ev);\n            }\n            this.didTextareaClearOnEdit = false;\n            this.ionBlur.emit(ev);\n        };\n        this.onKeyDown = (ev) => {\n            this.checkClearOnEdit(ev);\n        };\n        this.hasFocus = false;\n        this.color = undefined;\n        this.autocapitalize = 'none';\n        this.autofocus = false;\n        this.clearOnEdit = false;\n        this.debounce = undefined;\n        this.disabled = false;\n        this.fill = undefined;\n        this.inputmode = undefined;\n        this.enterkeyhint = undefined;\n        this.maxlength = undefined;\n        this.minlength = undefined;\n        this.name = this.inputId;\n        this.placeholder = undefined;\n        this.readonly = false;\n        this.required = false;\n        this.spellcheck = false;\n        this.cols = undefined;\n        this.rows = undefined;\n        this.wrap = undefined;\n        this.autoGrow = false;\n        this.value = '';\n        this.counter = false;\n        this.counterFormatter = undefined;\n        this.errorText = undefined;\n        this.helperText = undefined;\n        this.label = undefined;\n        this.labelPlacement = 'start';\n        this.legacy = undefined;\n        this.shape = undefined;\n    }\n    debounceChanged() {\n        const { ionInput, debounce, originalIonInput } = this;\n        /**\n         * If debounce is undefined, we have to manually revert the ionInput emitter in case\n         * debounce used to be set to a number. Otherwise, the event would stay debounced.\n         */\n        this.ionInput = debounce === undefined ? originalIonInput !== null && originalIonInput !== void 0 ? originalIonInput : ionInput : debounceEvent(ionInput, debounce);\n    }\n    disabledChanged() {\n        this.emitStyle();\n    }\n    /**\n     * Update the native input element when the value changes\n     */\n    valueChanged() {\n        const nativeInput = this.nativeInput;\n        const value = this.getValue();\n        if (nativeInput && nativeInput.value !== value) {\n            nativeInput.value = value;\n        }\n        this.runAutoGrow();\n        this.emitStyle();\n    }\n    connectedCallback() {\n        const { el } = this;\n        this.legacyFormController = createLegacyFormController(el);\n        this.slotMutationController = createSlotMutationController(el, ['label', 'start', 'end'], () => forceUpdate(this));\n        this.notchController = createNotchController(el, () => this.notchSpacerEl, () => this.labelSlot);\n        this.emitStyle();\n        this.debounceChanged();\n        {\n            document.dispatchEvent(new CustomEvent('ionInputDidLoad', {\n                detail: el,\n            }));\n        }\n    }\n    disconnectedCallback() {\n        {\n            document.dispatchEvent(new CustomEvent('ionInputDidUnload', {\n                detail: this.el,\n            }));\n        }\n        if (this.slotMutationController) {\n            this.slotMutationController.destroy();\n            this.slotMutationController = undefined;\n        }\n        if (this.notchController) {\n            this.notchController.destroy();\n            this.notchController = undefined;\n        }\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = Object.assign(Object.assign({}, inheritAriaAttributes(this.el)), inheritAttributes(this.el, ['data-form-type', 'title', 'tabindex']));\n    }\n    componentDidLoad() {\n        this.originalIonInput = this.ionInput;\n        this.runAutoGrow();\n    }\n    componentDidRender() {\n        var _a;\n        (_a = this.notchController) === null || _a === void 0 ? void 0 : _a.calculateNotchWidth();\n    }\n    /**\n     * Sets focus on the native `textarea` in `ion-textarea`. Use this method instead of the global\n     * `textarea.focus()`.\n     *\n     * See [managing focus](/docs/developing/managing-focus) for more information.\n     */\n    async setFocus() {\n        if (this.nativeInput) {\n            this.nativeInput.focus();\n        }\n    }\n    /**\n     * Returns the native `<textarea>` element used under the hood.\n     */\n    async getInputElement() {\n        /**\n         * If this gets called in certain early lifecycle hooks (ex: Vue onMounted),\n         * nativeInput won't be defined yet with the custom elements build, so wait for it to load in.\n         */\n        if (!this.nativeInput) {\n            await new Promise((resolve) => componentOnReady(this.el, resolve));\n        }\n        return Promise.resolve(this.nativeInput);\n    }\n    emitStyle() {\n        if (this.legacyFormController.hasLegacyControl()) {\n            this.ionStyle.emit({\n                interactive: true,\n                textarea: true,\n                input: true,\n                'interactive-disabled': this.disabled,\n                'has-placeholder': this.placeholder !== undefined,\n                'has-value': this.hasValue(),\n                'has-focus': this.hasFocus,\n                // TODO(FW-2876): remove this\n                legacy: !!this.legacy,\n            });\n        }\n    }\n    /**\n     * Emits an `ionChange` event.\n     *\n     * This API should be called for user committed changes.\n     * This API should not be used for external value changes.\n     */\n    emitValueChange(event) {\n        const { value } = this;\n        // Checks for both null and undefined values\n        const newValue = value == null ? value : value.toString();\n        // Emitting a value change should update the internal state for tracking the focused value\n        this.focusedValue = newValue;\n        this.ionChange.emit({ value: newValue, event });\n    }\n    /**\n     * Emits an `ionInput` event.\n     */\n    emitInputChange(event) {\n        const { value } = this;\n        this.ionInput.emit({ value, event });\n    }\n    runAutoGrow() {\n        if (this.nativeInput && this.autoGrow) {\n            writeTask(() => {\n                var _a;\n                if (this.textareaWrapper) {\n                    // Replicated value is an attribute to be used in the stylesheet\n                    // to set the inner contents of a pseudo element.\n                    this.textareaWrapper.dataset.replicatedValue = (_a = this.value) !== null && _a !== void 0 ? _a : '';\n                }\n            });\n        }\n    }\n    /**\n     * Check if we need to clear the text input if clearOnEdit is enabled\n     */\n    checkClearOnEdit(ev) {\n        if (!this.clearOnEdit) {\n            return;\n        }\n        /**\n         * The following keys do not modify the\n         * contents of the input. As a result, pressing\n         * them should not edit the textarea.\n         *\n         * We can't check to see if the value of the textarea\n         * was changed because we call checkClearOnEdit\n         * in a keydown listener, and the key has not yet\n         * been added to the textarea.\n         *\n         * Unlike ion-input, the \"Enter\" key does modify the\n         * textarea by adding a new line, so \"Enter\" is not\n         * included in the IGNORED_KEYS array.\n         */\n        const IGNORED_KEYS = ['Tab', 'Shift', 'Meta', 'Alt', 'Control'];\n        const pressedIgnoredKey = IGNORED_KEYS.includes(ev.key);\n        /**\n         * Clear the textarea if the control has not been previously cleared\n         * during focus.\n         */\n        if (!this.didTextareaClearOnEdit && this.hasValue() && !pressedIgnoredKey) {\n            this.value = '';\n            this.emitInputChange(ev);\n        }\n        /**\n         * Pressing an IGNORED_KEYS first and\n         * then an allowed key will cause the input to not\n         * be cleared.\n         */\n        if (!pressedIgnoredKey) {\n            this.didTextareaClearOnEdit = true;\n        }\n    }\n    focusChange() {\n        this.emitStyle();\n    }\n    hasValue() {\n        return this.getValue() !== '';\n    }\n    getValue() {\n        return this.value || '';\n    }\n    // TODO: FW-2876 - Remove this render function\n    renderLegacyTextarea() {\n        if (!this.hasLoggedDeprecationWarning) {\n            printIonWarning(`ion-textarea now requires providing a label with either the \"label\" property or the \"aria-label\" attribute. To migrate, remove any usage of \"ion-label\" and pass the label text to either the \"label\" property or the \"aria-label\" attribute.\n\nExample: <ion-textarea label=\"Comments\"></ion-textarea>\nExample with aria-label: <ion-textarea aria-label=\"Comments\"></ion-textarea>\n\nFor textareas that do not render the label immediately next to the input, developers may continue to use \"ion-label\" but must manually associate the label with the textarea by using \"aria-labelledby\".\n\nDevelopers can use the \"legacy\" property to continue using the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.`, this.el);\n            this.hasLoggedDeprecationWarning = true;\n        }\n        const mode = getIonMode(this);\n        const value = this.getValue();\n        const labelId = this.inputId + '-lbl';\n        const label = findItemLabel(this.el);\n        if (label) {\n            label.id = labelId;\n        }\n        return (h(Host, { \"aria-disabled\": this.disabled ? 'true' : null, class: createColorClasses(this.color, {\n                [mode]: true,\n                'legacy-textarea': true,\n            }) }, h(\"div\", { class: \"textarea-legacy-wrapper\", ref: (el) => (this.textareaWrapper = el) }, h(\"textarea\", Object.assign({ class: \"native-textarea\", \"aria-labelledby\": label ? label.id : null, ref: (el) => (this.nativeInput = el), autoCapitalize: this.autocapitalize, autoFocus: this.autofocus, enterKeyHint: this.enterkeyhint, inputMode: this.inputmode, disabled: this.disabled, maxLength: this.maxlength, minLength: this.minlength, name: this.name, placeholder: this.placeholder || '', readOnly: this.readonly, required: this.required, spellcheck: this.spellcheck, cols: this.cols, rows: this.rows, wrap: this.wrap, onInput: this.onInput, onChange: this.onChange, onBlur: this.onBlur, onFocus: this.onFocus, onKeyDown: this.onKeyDown }, this.inheritedAttributes), value))));\n    }\n    renderLabel() {\n        const { label } = this;\n        return (h(\"div\", { class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !this.hasLabel,\n            } }, label === undefined ? h(\"slot\", { name: \"label\" }) : h(\"div\", { class: \"label-text\" }, label)));\n    }\n    /**\n     * Gets any content passed into the `label` slot,\n     * not the <slot> definition.\n     */\n    get labelSlot() {\n        return this.el.querySelector('[slot=\"label\"]');\n    }\n    /**\n     * Returns `true` if label content is provided\n     * either by a prop or a content. If you want\n     * to get the plaintext value of the label use\n     * the `labelText` getter instead.\n     */\n    get hasLabel() {\n        return this.label !== undefined || this.labelSlot !== null;\n    }\n    /**\n     * Renders the border container when fill=\"outline\".\n     */\n    renderLabelContainer() {\n        const mode = getIonMode(this);\n        const hasOutlineFill = mode === 'md' && this.fill === 'outline';\n        if (hasOutlineFill) {\n            /**\n             * The outline fill has a special outline\n             * that appears around the textarea and the label.\n             * Certain stacked and floating label placements cause the\n             * label to translate up and create a \"cut out\"\n             * inside of that border by using the notch-spacer element.\n             */\n            return [\n                h(\"div\", { class: \"textarea-outline-container\" }, h(\"div\", { class: \"textarea-outline-start\" }), h(\"div\", { class: {\n                        'textarea-outline-notch': true,\n                        'textarea-outline-notch-hidden': !this.hasLabel,\n                    } }, h(\"div\", { class: \"notch-spacer\", \"aria-hidden\": \"true\", ref: (el) => (this.notchSpacerEl = el) }, this.label)), h(\"div\", { class: \"textarea-outline-end\" })),\n                this.renderLabel(),\n            ];\n        }\n        /**\n         * If not using the outline style,\n         * we can render just the label.\n         */\n        return this.renderLabel();\n    }\n    /**\n     * Renders the helper text or error text values\n     */\n    renderHintText() {\n        const { helperText, errorText } = this;\n        return [h(\"div\", { class: \"helper-text\" }, helperText), h(\"div\", { class: \"error-text\" }, errorText)];\n    }\n    renderCounter() {\n        const { counter, maxlength, counterFormatter, value } = this;\n        if (counter !== true || maxlength === undefined) {\n            return;\n        }\n        return h(\"div\", { class: \"counter\" }, getCounterText(value, maxlength, counterFormatter));\n    }\n    /**\n     * Responsible for rendering helper text,\n     * error text, and counter. This element should only\n     * be rendered if hint text is set or counter is enabled.\n     */\n    renderBottomContent() {\n        const { counter, helperText, errorText, maxlength } = this;\n        /**\n         * undefined and empty string values should\n         * be treated as not having helper/error text.\n         */\n        const hasHintText = !!helperText || !!errorText;\n        const hasCounter = counter === true && maxlength !== undefined;\n        if (!hasHintText && !hasCounter) {\n            return;\n        }\n        return (h(\"div\", { class: \"textarea-bottom\" }, this.renderHintText(), this.renderCounter()));\n    }\n    renderTextarea() {\n        const { inputId, disabled, fill, shape, labelPlacement, el, hasFocus } = this;\n        const mode = getIonMode(this);\n        const value = this.getValue();\n        const inItem = hostContext('ion-item', this.el);\n        const shouldRenderHighlight = mode === 'md' && fill !== 'outline' && !inItem;\n        const hasValue = this.hasValue();\n        const hasStartEndSlots = el.querySelector('[slot=\"start\"], [slot=\"end\"]') !== null;\n        /**\n         * If the label is stacked, it should always sit above the textarea.\n         * For floating labels, the label should move above the textarea if\n         * the textarea has a value, is focused, or has anything in either\n         * the start or end slot.\n         *\n         * If there is content in the start slot, the label would overlap\n         * it if not forced to float. This is also applied to the end slot\n         * because with the default or solid fills, the textarea is not\n         * vertically centered in the container, but the label is. This\n         * causes the slots and label to appear vertically offset from each\n         * other when the label isn't floating above the input. This doesn't\n         * apply to the outline fill, but this was not accounted for to keep\n         * things consistent.\n         *\n         * TODO(FW-5592): Remove hasStartEndSlots condition\n         */\n        const labelShouldFloat = labelPlacement === 'stacked' || (labelPlacement === 'floating' && (hasValue || hasFocus || hasStartEndSlots));\n        return (h(Host, { class: createColorClasses(this.color, {\n                [mode]: true,\n                'has-value': hasValue,\n                'has-focus': hasFocus,\n                'label-floating': labelShouldFloat,\n                [`textarea-fill-${fill}`]: fill !== undefined,\n                [`textarea-shape-${shape}`]: shape !== undefined,\n                [`textarea-label-placement-${labelPlacement}`]: true,\n                'textarea-disabled': disabled,\n            }) }, h(\"label\", { class: \"textarea-wrapper\", htmlFor: inputId }, this.renderLabelContainer(), h(\"div\", { class: \"textarea-wrapper-inner\" }, h(\"div\", { class: \"start-slot-wrapper\" }, h(\"slot\", { name: \"start\" })), h(\"div\", { class: \"native-wrapper\", ref: (el) => (this.textareaWrapper = el) }, h(\"textarea\", Object.assign({ class: \"native-textarea\", ref: (el) => (this.nativeInput = el), id: inputId, disabled: disabled, autoCapitalize: this.autocapitalize, autoFocus: this.autofocus, enterKeyHint: this.enterkeyhint, inputMode: this.inputmode, minLength: this.minlength, maxLength: this.maxlength, name: this.name, placeholder: this.placeholder || '', readOnly: this.readonly, required: this.required, spellcheck: this.spellcheck, cols: this.cols, rows: this.rows, wrap: this.wrap, onInput: this.onInput, onChange: this.onChange, onBlur: this.onBlur, onFocus: this.onFocus, onKeyDown: this.onKeyDown }, this.inheritedAttributes), value)), h(\"div\", { class: \"end-slot-wrapper\" }, h(\"slot\", { name: \"end\" }))), shouldRenderHighlight && h(\"div\", { class: \"textarea-highlight\" })), this.renderBottomContent()));\n    }\n    render() {\n        const { legacyFormController } = this;\n        return legacyFormController.hasLegacyControl() ? this.renderLegacyTextarea() : this.renderTextarea();\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"debounce\": [\"debounceChanged\"],\n        \"disabled\": [\"disabledChanged\"],\n        \"value\": [\"valueChanged\"]\n    }; }\n};\nlet textareaIds = 0;\nTextarea.style = {\n    ios: IonTextareaIosStyle0,\n    md: IonTextareaMdStyle0\n};\n\nexport { Textarea as ion_textarea };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,WAAW,QAAQ,qBAAqB;AAC9I,SAASC,CAAC,IAAIC,0BAA0B,QAAQ,+BAA+B;AAC/E,SAASD,CAAC,IAAIE,qBAAqB,QAAQ,gCAAgC;AAC3E,SAASC,CAAC,IAAIC,aAAa,EAAEN,CAAC,IAAIO,qBAAqB,EAAEC,CAAC,IAAIC,iBAAiB,EAAEP,CAAC,IAAIQ,gBAAgB,EAAEf,CAAC,IAAIgB,aAAa,QAAQ,uBAAuB;AACzJ,SAASC,CAAC,IAAIC,eAAe,QAAQ,qBAAqB;AAC1D,SAASX,CAAC,IAAIY,4BAA4B,EAAEC,CAAC,IAAIC,cAAc,QAAQ,2BAA2B;AAClG,SAASd,CAAC,IAAIe,kBAAkB,EAAEtB,CAAC,IAAIuB,WAAW,QAAQ,qBAAqB;AAC/E,SAASC,CAAC,IAAIC,UAAU,QAAQ,4BAA4B;AAC5D,OAAO,qBAAqB;AAE5B,MAAMC,cAAc,GAAG,sniBAAsniB;AAC7oiB,MAAMC,oBAAoB,GAAGD,cAAc;AAE3C,MAAME,aAAa,GAAG,g08BAAg08B;AACt18B,MAAMC,mBAAmB,GAAGD,aAAa;AAEzC,MAAME,QAAQ,GAAG,MAAM;EACnBC,WAAWA,CAACC,OAAO,EAAE;IACjBrC,gBAAgB,CAAC,IAAI,EAAEqC,OAAO,CAAC;IAC/B,IAAI,CAACC,SAAS,GAAGpC,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACqC,QAAQ,GAAGrC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACsC,QAAQ,GAAGtC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACuC,OAAO,GAAGvC,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACwC,QAAQ,GAAGxC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACyC,OAAO,GAAG,gBAAgBC,WAAW,EAAE,EAAE;IAC9C;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B;IACA,IAAI,CAACC,2BAA2B,GAAG,KAAK;IACxC;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,OAAO,GAAIC,EAAE,IAAK;MACnB,MAAMC,KAAK,GAAGD,EAAE,CAACE,MAAM;MACvB,IAAID,KAAK,EAAE;QACP,IAAI,CAACE,KAAK,GAAGF,KAAK,CAACE,KAAK,IAAI,EAAE;MAClC;MACA,IAAI,CAACC,eAAe,CAACJ,EAAE,CAAC;IAC5B,CAAC;IACD,IAAI,CAACK,QAAQ,GAAIL,EAAE,IAAK;MACpB,IAAI,CAACM,eAAe,CAACN,EAAE,CAAC;IAC5B,CAAC;IACD,IAAI,CAACO,OAAO,GAAIP,EAAE,IAAK;MACnB,IAAI,CAACQ,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACC,YAAY,GAAG,IAAI,CAACN,KAAK;MAC9B,IAAI,CAACO,WAAW,CAAC,CAAC;MAClB,IAAI,CAACjB,QAAQ,CAACkB,IAAI,CAACX,EAAE,CAAC;IAC1B,CAAC;IACD,IAAI,CAACY,MAAM,GAAIZ,EAAE,IAAK;MAClB,IAAI,CAACQ,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACE,WAAW,CAAC,CAAC;MAClB,IAAI,IAAI,CAACD,YAAY,KAAK,IAAI,CAACN,KAAK,EAAE;QAClC;AAChB;AACA;AACA;QACgB,IAAI,CAACG,eAAe,CAACN,EAAE,CAAC;MAC5B;MACA,IAAI,CAACJ,sBAAsB,GAAG,KAAK;MACnC,IAAI,CAACJ,OAAO,CAACmB,IAAI,CAACX,EAAE,CAAC;IACzB,CAAC;IACD,IAAI,CAACa,SAAS,GAAIb,EAAE,IAAK;MACrB,IAAI,CAACc,gBAAgB,CAACd,EAAE,CAAC;IAC7B,CAAC;IACD,IAAI,CAACQ,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACO,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACC,cAAc,GAAG,MAAM;IAC5B,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,QAAQ,GAAGJ,SAAS;IACzB,IAAI,CAACK,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,IAAI,GAAGN,SAAS;IACrB,IAAI,CAACO,SAAS,GAAGP,SAAS;IAC1B,IAAI,CAACQ,YAAY,GAAGR,SAAS;IAC7B,IAAI,CAACS,SAAS,GAAGT,SAAS;IAC1B,IAAI,CAACU,SAAS,GAAGV,SAAS;IAC1B,IAAI,CAACW,IAAI,GAAG,IAAI,CAACjC,OAAO;IACxB,IAAI,CAACkC,WAAW,GAAGZ,SAAS;IAC5B,IAAI,CAACa,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,IAAI,GAAGhB,SAAS;IACrB,IAAI,CAACiB,IAAI,GAAGjB,SAAS;IACrB,IAAI,CAACkB,IAAI,GAAGlB,SAAS;IACrB,IAAI,CAACmB,QAAQ,GAAG,KAAK;IACrB,IAAI,CAAChC,KAAK,GAAG,EAAE;IACf,IAAI,CAACiC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,gBAAgB,GAAGrB,SAAS;IACjC,IAAI,CAACsB,SAAS,GAAGtB,SAAS;IAC1B,IAAI,CAACuB,UAAU,GAAGvB,SAAS;IAC3B,IAAI,CAACwB,KAAK,GAAGxB,SAAS;IACtB,IAAI,CAACyB,cAAc,GAAG,OAAO;IAC7B,IAAI,CAACC,MAAM,GAAG1B,SAAS;IACvB,IAAI,CAAC2B,KAAK,GAAG3B,SAAS;EAC1B;EACA4B,eAAeA,CAAA,EAAG;IACd,MAAM;MAAEtD,QAAQ;MAAE8B,QAAQ;MAAEyB;IAAiB,CAAC,GAAG,IAAI;IACrD;AACR;AACA;AACA;IACQ,IAAI,CAACvD,QAAQ,GAAG8B,QAAQ,KAAKJ,SAAS,GAAG6B,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAGvD,QAAQ,GAAGvB,aAAa,CAACuB,QAAQ,EAAE8B,QAAQ,CAAC;EACvK;EACA0B,eAAeA,CAAA,EAAG;IACd,IAAI,CAACC,SAAS,CAAC,CAAC;EACpB;EACA;AACJ;AACA;EACIC,YAAYA,CAAA,EAAG;IACX,MAAMC,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC,MAAM9C,KAAK,GAAG,IAAI,CAAC+C,QAAQ,CAAC,CAAC;IAC7B,IAAID,WAAW,IAAIA,WAAW,CAAC9C,KAAK,KAAKA,KAAK,EAAE;MAC5C8C,WAAW,CAAC9C,KAAK,GAAGA,KAAK;IAC7B;IACA,IAAI,CAACgD,WAAW,CAAC,CAAC;IAClB,IAAI,CAACJ,SAAS,CAAC,CAAC;EACpB;EACAK,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAEC;IAAG,CAAC,GAAG,IAAI;IACnB,IAAI,CAACC,oBAAoB,GAAG1F,0BAA0B,CAACyF,EAAE,CAAC;IAC1D,IAAI,CAACE,sBAAsB,GAAGhF,4BAA4B,CAAC8E,EAAE,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE,MAAM3F,WAAW,CAAC,IAAI,CAAC,CAAC;IAClH,IAAI,CAAC8F,eAAe,GAAG3F,qBAAqB,CAACwF,EAAE,EAAE,MAAM,IAAI,CAACI,aAAa,EAAE,MAAM,IAAI,CAACC,SAAS,CAAC;IAChG,IAAI,CAACX,SAAS,CAAC,CAAC;IAChB,IAAI,CAACH,eAAe,CAAC,CAAC;IACtB;MACIe,QAAQ,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,iBAAiB,EAAE;QACtDC,MAAM,EAAET;MACZ,CAAC,CAAC,CAAC;IACP;EACJ;EACAU,oBAAoBA,CAAA,EAAG;IACnB;MACIJ,QAAQ,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,mBAAmB,EAAE;QACxDC,MAAM,EAAE,IAAI,CAACT;MACjB,CAAC,CAAC,CAAC;IACP;IACA,IAAI,IAAI,CAACE,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAACS,OAAO,CAAC,CAAC;MACrC,IAAI,CAACT,sBAAsB,GAAGvC,SAAS;IAC3C;IACA,IAAI,IAAI,CAACwC,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACQ,OAAO,CAAC,CAAC;MAC9B,IAAI,CAACR,eAAe,GAAGxC,SAAS;IACpC;EACJ;EACAiD,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACpE,mBAAmB,GAAGqE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEnG,qBAAqB,CAAC,IAAI,CAACqF,EAAE,CAAC,CAAC,EAAEnF,iBAAiB,CAAC,IAAI,CAACmF,EAAE,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC;EACpK;EACAe,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACvB,gBAAgB,GAAG,IAAI,CAACvD,QAAQ;IACrC,IAAI,CAAC6D,WAAW,CAAC,CAAC;EACtB;EACAkB,kBAAkBA,CAAA,EAAG;IACjB,IAAIC,EAAE;IACN,CAACA,EAAE,GAAG,IAAI,CAACd,eAAe,MAAM,IAAI,IAAIc,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,mBAAmB,CAAC,CAAC;EAC7F;EACA;AACJ;AACA;AACA;AACA;AACA;EACUC,QAAQA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACb,IAAID,KAAI,CAACxB,WAAW,EAAE;QAClBwB,KAAI,CAACxB,WAAW,CAAC0B,KAAK,CAAC,CAAC;MAC5B;IAAC;EACL;EACA;AACJ;AACA;EACUC,eAAeA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAH,iBAAA;MACpB;AACR;AACA;AACA;MACQ,IAAI,CAACG,MAAI,CAAC5B,WAAW,EAAE;QACnB,MAAM,IAAI6B,OAAO,CAAEC,OAAO,IAAK5G,gBAAgB,CAAC0G,MAAI,CAACxB,EAAE,EAAE0B,OAAO,CAAC,CAAC;MACtE;MACA,OAAOD,OAAO,CAACC,OAAO,CAACF,MAAI,CAAC5B,WAAW,CAAC;IAAC;EAC7C;EACAF,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACO,oBAAoB,CAAC0B,gBAAgB,CAAC,CAAC,EAAE;MAC9C,IAAI,CAACzF,QAAQ,CAACoB,IAAI,CAAC;QACfsE,WAAW,EAAE,IAAI;QACjBC,QAAQ,EAAE,IAAI;QACdjF,KAAK,EAAE,IAAI;QACX,sBAAsB,EAAE,IAAI,CAACoB,QAAQ;QACrC,iBAAiB,EAAE,IAAI,CAACO,WAAW,KAAKZ,SAAS;QACjD,WAAW,EAAE,IAAI,CAACmE,QAAQ,CAAC,CAAC;QAC5B,WAAW,EAAE,IAAI,CAAC3E,QAAQ;QAC1B;QACAkC,MAAM,EAAE,CAAC,CAAC,IAAI,CAACA;MACnB,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIpC,eAAeA,CAAC8E,KAAK,EAAE;IACnB,MAAM;MAAEjF;IAAM,CAAC,GAAG,IAAI;IACtB;IACA,MAAMkF,QAAQ,GAAGlF,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGA,KAAK,CAACmF,QAAQ,CAAC,CAAC;IACzD;IACA,IAAI,CAAC7E,YAAY,GAAG4E,QAAQ;IAC5B,IAAI,CAAChG,SAAS,CAACsB,IAAI,CAAC;MAAER,KAAK,EAAEkF,QAAQ;MAAED;IAAM,CAAC,CAAC;EACnD;EACA;AACJ;AACA;EACIhF,eAAeA,CAACgF,KAAK,EAAE;IACnB,MAAM;MAAEjF;IAAM,CAAC,GAAG,IAAI;IACtB,IAAI,CAACb,QAAQ,CAACqB,IAAI,CAAC;MAAER,KAAK;MAAEiF;IAAM,CAAC,CAAC;EACxC;EACAjC,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACF,WAAW,IAAI,IAAI,CAACd,QAAQ,EAAE;MACnChF,SAAS,CAAC,MAAM;QACZ,IAAImH,EAAE;QACN,IAAI,IAAI,CAACiB,eAAe,EAAE;UACtB;UACA;UACA,IAAI,CAACA,eAAe,CAACC,OAAO,CAACC,eAAe,GAAG,CAACnB,EAAE,GAAG,IAAI,CAACnE,KAAK,MAAM,IAAI,IAAImE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;QACxG;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;EACIxD,gBAAgBA,CAACd,EAAE,EAAE;IACjB,IAAI,CAAC,IAAI,CAACmB,WAAW,EAAE;MACnB;IACJ;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMuE,YAAY,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC;IAC/D,MAAMC,iBAAiB,GAAGD,YAAY,CAACE,QAAQ,CAAC5F,EAAE,CAAC6F,GAAG,CAAC;IACvD;AACR;AACA;AACA;IACQ,IAAI,CAAC,IAAI,CAACjG,sBAAsB,IAAI,IAAI,CAACuF,QAAQ,CAAC,CAAC,IAAI,CAACQ,iBAAiB,EAAE;MACvE,IAAI,CAACxF,KAAK,GAAG,EAAE;MACf,IAAI,CAACC,eAAe,CAACJ,EAAE,CAAC;IAC5B;IACA;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC2F,iBAAiB,EAAE;MACpB,IAAI,CAAC/F,sBAAsB,GAAG,IAAI;IACtC;EACJ;EACAc,WAAWA,CAAA,EAAG;IACV,IAAI,CAACqC,SAAS,CAAC,CAAC;EACpB;EACAoC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACjC,QAAQ,CAAC,CAAC,KAAK,EAAE;EACjC;EACAA,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC/C,KAAK,IAAI,EAAE;EAC3B;EACA;EACA2F,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAAChG,2BAA2B,EAAE;MACnCxB,eAAe,CAAC;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,gNAAgN,EAAE,IAAI,CAAC+E,EAAE,CAAC;MAC9M,IAAI,CAACvD,2BAA2B,GAAG,IAAI;IAC3C;IACA,MAAMiG,IAAI,GAAGlH,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMsB,KAAK,GAAG,IAAI,CAAC+C,QAAQ,CAAC,CAAC;IAC7B,MAAM8C,OAAO,GAAG,IAAI,CAACtG,OAAO,GAAG,MAAM;IACrC,MAAM8C,KAAK,GAAGpE,aAAa,CAAC,IAAI,CAACiF,EAAE,CAAC;IACpC,IAAIb,KAAK,EAAE;MACPA,KAAK,CAACyD,EAAE,GAAGD,OAAO;IACtB;IACA,OAAQ5I,CAAC,CAACE,IAAI,EAAE;MAAE,eAAe,EAAE,IAAI,CAAC+D,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAE6E,KAAK,EAAExH,kBAAkB,CAAC,IAAI,CAACqC,KAAK,EAAE;QAChG,CAACgF,IAAI,GAAG,IAAI;QACZ,iBAAiB,EAAE;MACvB,CAAC;IAAE,CAAC,EAAE3I,CAAC,CAAC,KAAK,EAAE;MAAE8I,KAAK,EAAE,yBAAyB;MAAEC,GAAG,EAAG9C,EAAE,IAAM,IAAI,CAACkC,eAAe,GAAGlC;IAAI,CAAC,EAAEjG,CAAC,CAAC,UAAU,EAAE8G,MAAM,CAACC,MAAM,CAAC;MAAE+B,KAAK,EAAE,iBAAiB;MAAE,iBAAiB,EAAE1D,KAAK,GAAGA,KAAK,CAACyD,EAAE,GAAG,IAAI;MAAEE,GAAG,EAAG9C,EAAE,IAAM,IAAI,CAACJ,WAAW,GAAGI,EAAG;MAAE+C,cAAc,EAAE,IAAI,CAACnF,cAAc;MAAEoF,SAAS,EAAE,IAAI,CAACnF,SAAS;MAAEoF,YAAY,EAAE,IAAI,CAAC9E,YAAY;MAAE+E,SAAS,EAAE,IAAI,CAAChF,SAAS;MAAEF,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEmF,SAAS,EAAE,IAAI,CAAC/E,SAAS;MAAEgF,SAAS,EAAE,IAAI,CAAC/E,SAAS;MAAEC,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEC,WAAW,EAAE,IAAI,CAACA,WAAW,IAAI,EAAE;MAAE8E,QAAQ,EAAE,IAAI,CAAC7E,QAAQ;MAAEC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEC,UAAU,EAAE,IAAI,CAACA,UAAU;MAAEC,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEC,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEC,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEnC,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEM,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEO,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEL,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEM,SAAS,EAAE,IAAI,CAACA;IAAU,CAAC,EAAE,IAAI,CAAChB,mBAAmB,CAAC,EAAEM,KAAK,CAAC,CAAC,CAAC;EAChxB;EACAwG,WAAWA,CAAA,EAAG;IACV,MAAM;MAAEnE;IAAM,CAAC,GAAG,IAAI;IACtB,OAAQpF,CAAC,CAAC,KAAK,EAAE;MAAE8I,KAAK,EAAE;QAClB,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAE,CAAC,IAAI,CAACU;MACvC;IAAE,CAAC,EAAEpE,KAAK,KAAKxB,SAAS,GAAG5D,CAAC,CAAC,MAAM,EAAE;MAAEuE,IAAI,EAAE;IAAQ,CAAC,CAAC,GAAGvE,CAAC,CAAC,KAAK,EAAE;MAAE8I,KAAK,EAAE;IAAa,CAAC,EAAE1D,KAAK,CAAC,CAAC;EAC3G;EACA;AACJ;AACA;AACA;EACI,IAAIkB,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACL,EAAE,CAACwD,aAAa,CAAC,gBAAgB,CAAC;EAClD;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAID,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACpE,KAAK,KAAKxB,SAAS,IAAI,IAAI,CAAC0C,SAAS,KAAK,IAAI;EAC9D;EACA;AACJ;AACA;EACIoD,oBAAoBA,CAAA,EAAG;IACnB,MAAMf,IAAI,GAAGlH,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMkI,cAAc,GAAGhB,IAAI,KAAK,IAAI,IAAI,IAAI,CAACzE,IAAI,KAAK,SAAS;IAC/D,IAAIyF,cAAc,EAAE;MAChB;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,OAAO,CACH3J,CAAC,CAAC,KAAK,EAAE;QAAE8I,KAAK,EAAE;MAA6B,CAAC,EAAE9I,CAAC,CAAC,KAAK,EAAE;QAAE8I,KAAK,EAAE;MAAyB,CAAC,CAAC,EAAE9I,CAAC,CAAC,KAAK,EAAE;QAAE8I,KAAK,EAAE;UAC3G,wBAAwB,EAAE,IAAI;UAC9B,+BAA+B,EAAE,CAAC,IAAI,CAACU;QAC3C;MAAE,CAAC,EAAExJ,CAAC,CAAC,KAAK,EAAE;QAAE8I,KAAK,EAAE,cAAc;QAAE,aAAa,EAAE,MAAM;QAAEC,GAAG,EAAG9C,EAAE,IAAM,IAAI,CAACI,aAAa,GAAGJ;MAAI,CAAC,EAAE,IAAI,CAACb,KAAK,CAAC,CAAC,EAAEpF,CAAC,CAAC,KAAK,EAAE;QAAE8I,KAAK,EAAE;MAAuB,CAAC,CAAC,CAAC,EACtK,IAAI,CAACS,WAAW,CAAC,CAAC,CACrB;IACL;IACA;AACR;AACA;AACA;IACQ,OAAO,IAAI,CAACA,WAAW,CAAC,CAAC;EAC7B;EACA;AACJ;AACA;EACIK,cAAcA,CAAA,EAAG;IACb,MAAM;MAAEzE,UAAU;MAAED;IAAU,CAAC,GAAG,IAAI;IACtC,OAAO,CAAClF,CAAC,CAAC,KAAK,EAAE;MAAE8I,KAAK,EAAE;IAAc,CAAC,EAAE3D,UAAU,CAAC,EAAEnF,CAAC,CAAC,KAAK,EAAE;MAAE8I,KAAK,EAAE;IAAa,CAAC,EAAE5D,SAAS,CAAC,CAAC;EACzG;EACA2E,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAE7E,OAAO;MAAEX,SAAS;MAAEY,gBAAgB;MAAElC;IAAM,CAAC,GAAG,IAAI;IAC5D,IAAIiC,OAAO,KAAK,IAAI,IAAIX,SAAS,KAAKT,SAAS,EAAE;MAC7C;IACJ;IACA,OAAO5D,CAAC,CAAC,KAAK,EAAE;MAAE8I,KAAK,EAAE;IAAU,CAAC,EAAEzH,cAAc,CAAC0B,KAAK,EAAEsB,SAAS,EAAEY,gBAAgB,CAAC,CAAC;EAC7F;EACA;AACJ;AACA;AACA;AACA;EACI6E,mBAAmBA,CAAA,EAAG;IAClB,MAAM;MAAE9E,OAAO;MAAEG,UAAU;MAAED,SAAS;MAAEb;IAAU,CAAC,GAAG,IAAI;IAC1D;AACR;AACA;AACA;IACQ,MAAM0F,WAAW,GAAG,CAAC,CAAC5E,UAAU,IAAI,CAAC,CAACD,SAAS;IAC/C,MAAM8E,UAAU,GAAGhF,OAAO,KAAK,IAAI,IAAIX,SAAS,KAAKT,SAAS;IAC9D,IAAI,CAACmG,WAAW,IAAI,CAACC,UAAU,EAAE;MAC7B;IACJ;IACA,OAAQhK,CAAC,CAAC,KAAK,EAAE;MAAE8I,KAAK,EAAE;IAAkB,CAAC,EAAE,IAAI,CAACc,cAAc,CAAC,CAAC,EAAE,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC;EAC/F;EACAI,cAAcA,CAAA,EAAG;IACb,MAAM;MAAE3H,OAAO;MAAE2B,QAAQ;MAAEC,IAAI;MAAEqB,KAAK;MAAEF,cAAc;MAAEY,EAAE;MAAE7C;IAAS,CAAC,GAAG,IAAI;IAC7E,MAAMuF,IAAI,GAAGlH,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMsB,KAAK,GAAG,IAAI,CAAC+C,QAAQ,CAAC,CAAC;IAC7B,MAAMoE,MAAM,GAAG3I,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC0E,EAAE,CAAC;IAC/C,MAAMkE,qBAAqB,GAAGxB,IAAI,KAAK,IAAI,IAAIzE,IAAI,KAAK,SAAS,IAAI,CAACgG,MAAM;IAC5E,MAAMnC,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC;IAChC,MAAMqC,gBAAgB,GAAGnE,EAAE,CAACwD,aAAa,CAAC,8BAA8B,CAAC,KAAK,IAAI;IAClF;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMY,gBAAgB,GAAGhF,cAAc,KAAK,SAAS,IAAKA,cAAc,KAAK,UAAU,KAAK0C,QAAQ,IAAI3E,QAAQ,IAAIgH,gBAAgB,CAAE;IACtI,OAAQpK,CAAC,CAACE,IAAI,EAAE;MAAE4I,KAAK,EAAExH,kBAAkB,CAAC,IAAI,CAACqC,KAAK,EAAE;QAChD,CAACgF,IAAI,GAAG,IAAI;QACZ,WAAW,EAAEZ,QAAQ;QACrB,WAAW,EAAE3E,QAAQ;QACrB,gBAAgB,EAAEiH,gBAAgB;QAClC,CAAC,iBAAiBnG,IAAI,EAAE,GAAGA,IAAI,KAAKN,SAAS;QAC7C,CAAC,kBAAkB2B,KAAK,EAAE,GAAGA,KAAK,KAAK3B,SAAS;QAChD,CAAC,4BAA4ByB,cAAc,EAAE,GAAG,IAAI;QACpD,mBAAmB,EAAEpB;MACzB,CAAC;IAAE,CAAC,EAAEjE,CAAC,CAAC,OAAO,EAAE;MAAE8I,KAAK,EAAE,kBAAkB;MAAEwB,OAAO,EAAEhI;IAAQ,CAAC,EAAE,IAAI,CAACoH,oBAAoB,CAAC,CAAC,EAAE1J,CAAC,CAAC,KAAK,EAAE;MAAE8I,KAAK,EAAE;IAAyB,CAAC,EAAE9I,CAAC,CAAC,KAAK,EAAE;MAAE8I,KAAK,EAAE;IAAqB,CAAC,EAAE9I,CAAC,CAAC,MAAM,EAAE;MAAEuE,IAAI,EAAE;IAAQ,CAAC,CAAC,CAAC,EAAEvE,CAAC,CAAC,KAAK,EAAE;MAAE8I,KAAK,EAAE,gBAAgB;MAAEC,GAAG,EAAG9C,EAAE,IAAM,IAAI,CAACkC,eAAe,GAAGlC;IAAI,CAAC,EAAEjG,CAAC,CAAC,UAAU,EAAE8G,MAAM,CAACC,MAAM,CAAC;MAAE+B,KAAK,EAAE,iBAAiB;MAAEC,GAAG,EAAG9C,EAAE,IAAM,IAAI,CAACJ,WAAW,GAAGI,EAAG;MAAE4C,EAAE,EAAEvG,OAAO;MAAE2B,QAAQ,EAAEA,QAAQ;MAAE+E,cAAc,EAAE,IAAI,CAACnF,cAAc;MAAEoF,SAAS,EAAE,IAAI,CAACnF,SAAS;MAAEoF,YAAY,EAAE,IAAI,CAAC9E,YAAY;MAAE+E,SAAS,EAAE,IAAI,CAAChF,SAAS;MAAEkF,SAAS,EAAE,IAAI,CAAC/E,SAAS;MAAE8E,SAAS,EAAE,IAAI,CAAC/E,SAAS;MAAEE,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEC,WAAW,EAAE,IAAI,CAACA,WAAW,IAAI,EAAE;MAAE8E,QAAQ,EAAE,IAAI,CAAC7E,QAAQ;MAAEC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEC,UAAU,EAAE,IAAI,CAACA,UAAU;MAAEC,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEC,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEC,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEnC,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEM,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEO,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEL,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEM,SAAS,EAAE,IAAI,CAACA;IAAU,CAAC,EAAE,IAAI,CAAChB,mBAAmB,CAAC,EAAEM,KAAK,CAAC,CAAC,EAAE/C,CAAC,CAAC,KAAK,EAAE;MAAE8I,KAAK,EAAE;IAAmB,CAAC,EAAE9I,CAAC,CAAC,MAAM,EAAE;MAAEuE,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC,CAAC,EAAE4F,qBAAqB,IAAInK,CAAC,CAAC,KAAK,EAAE;MAAE8I,KAAK,EAAE;IAAqB,CAAC,CAAC,CAAC,EAAE,IAAI,CAACgB,mBAAmB,CAAC,CAAC,CAAC;EAC1lC;EACAS,MAAMA,CAAA,EAAG;IACL,MAAM;MAAErE;IAAqB,CAAC,GAAG,IAAI;IACrC,OAAOA,oBAAoB,CAAC0B,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAACc,oBAAoB,CAAC,CAAC,GAAG,IAAI,CAACuB,cAAc,CAAC,CAAC;EACxG;EACA,IAAIhE,EAAEA,CAAA,EAAG;IAAE,OAAO7F,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWoK,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,OAAO,EAAE,CAAC,cAAc;IAC5B,CAAC;EAAE;AACP,CAAC;AACD,IAAIjI,WAAW,GAAG,CAAC;AACnBT,QAAQ,CAAC2I,KAAK,GAAG;EACbC,GAAG,EAAE/I,oBAAoB;EACzBgJ,EAAE,EAAE9I;AACR,CAAC;AAED,SAASC,QAAQ,IAAI8I,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}