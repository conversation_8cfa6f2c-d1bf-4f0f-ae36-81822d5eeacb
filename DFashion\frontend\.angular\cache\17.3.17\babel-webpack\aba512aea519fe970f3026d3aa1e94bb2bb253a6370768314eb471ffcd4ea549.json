{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index5.js';\nimport { r as raf } from './helpers.js';\nlet animationPrefix;\n/**\n * Web Animations requires hyphenated CSS properties\n * to be written in camelCase when animating\n */\nconst processKeyframes = keyframes => {\n  keyframes.forEach(keyframe => {\n    for (const key in keyframe) {\n      // eslint-disable-next-line no-prototype-builtins\n      if (keyframe.hasOwnProperty(key)) {\n        const value = keyframe[key];\n        if (key === 'easing') {\n          const newKey = 'animation-timing-function';\n          keyframe[newKey] = value;\n          delete keyframe[key];\n        } else {\n          const newKey = convertCamelCaseToHypen(key);\n          if (newKey !== key) {\n            keyframe[newKey] = value;\n            delete keyframe[key];\n          }\n        }\n      }\n    }\n  });\n  return keyframes;\n};\nconst convertCamelCaseToHypen = str => {\n  return str.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n};\nconst getAnimationPrefix = el => {\n  if (animationPrefix === undefined) {\n    const supportsUnprefixed = el.style.animationName !== undefined;\n    const supportsWebkitPrefix = el.style.webkitAnimationName !== undefined;\n    animationPrefix = !supportsUnprefixed && supportsWebkitPrefix ? '-webkit-' : '';\n  }\n  return animationPrefix;\n};\nconst setStyleProperty = (element, propertyName, value) => {\n  const prefix = propertyName.startsWith('animation') ? getAnimationPrefix(element) : '';\n  element.style.setProperty(prefix + propertyName, value);\n};\nconst removeStyleProperty = (element, propertyName) => {\n  const prefix = propertyName.startsWith('animation') ? getAnimationPrefix(element) : '';\n  element.style.removeProperty(prefix + propertyName);\n};\nconst animationEnd = (el, callback) => {\n  let unRegTrans;\n  const opts = {\n    passive: true\n  };\n  const unregister = () => {\n    if (unRegTrans) {\n      unRegTrans();\n    }\n  };\n  const onTransitionEnd = ev => {\n    if (el === ev.target) {\n      unregister();\n      callback(ev);\n    }\n  };\n  if (el) {\n    el.addEventListener('webkitAnimationEnd', onTransitionEnd, opts);\n    el.addEventListener('animationend', onTransitionEnd, opts);\n    unRegTrans = () => {\n      el.removeEventListener('webkitAnimationEnd', onTransitionEnd, opts);\n      el.removeEventListener('animationend', onTransitionEnd, opts);\n    };\n  }\n  return unregister;\n};\n// TODO(FW-2832): type\nconst generateKeyframeRules = (keyframes = []) => {\n  return keyframes.map(keyframe => {\n    const offset = keyframe.offset;\n    const frameString = [];\n    for (const property in keyframe) {\n      // eslint-disable-next-line no-prototype-builtins\n      if (keyframe.hasOwnProperty(property) && property !== 'offset') {\n        frameString.push(`${property}: ${keyframe[property]};`);\n      }\n    }\n    return `${offset * 100}% { ${frameString.join(' ')} }`;\n  }).join(' ');\n};\nconst keyframeIds = [];\nconst generateKeyframeName = keyframeRules => {\n  let index = keyframeIds.indexOf(keyframeRules);\n  if (index < 0) {\n    index = keyframeIds.push(keyframeRules) - 1;\n  }\n  return `ion-animation-${index}`;\n};\nconst getStyleContainer = element => {\n  // getRootNode is not always available in SSR environments.\n  // TODO(FW-2832): types\n  const rootNode = element.getRootNode !== undefined ? element.getRootNode() : element;\n  return rootNode.head || rootNode;\n};\nconst createKeyframeStylesheet = (keyframeName, keyframeRules, element) => {\n  var _a;\n  const styleContainer = getStyleContainer(element);\n  const keyframePrefix = getAnimationPrefix(element);\n  const existingStylesheet = styleContainer.querySelector('#' + keyframeName);\n  if (existingStylesheet) {\n    return existingStylesheet;\n  }\n  const stylesheet = ((_a = element.ownerDocument) !== null && _a !== void 0 ? _a : document).createElement('style');\n  stylesheet.id = keyframeName;\n  stylesheet.textContent = `@${keyframePrefix}keyframes ${keyframeName} { ${keyframeRules} } @${keyframePrefix}keyframes ${keyframeName}-alt { ${keyframeRules} }`;\n  styleContainer.appendChild(stylesheet);\n  return stylesheet;\n};\nconst addClassToArray = (classes = [], className) => {\n  if (className !== undefined) {\n    const classNameToAppend = Array.isArray(className) ? className : [className];\n    return [...classes, ...classNameToAppend];\n  }\n  return classes;\n};\nconst createAnimation = animationId => {\n  let _delay;\n  let _duration;\n  let _easing;\n  let _iterations;\n  let _fill;\n  let _direction;\n  let _keyframes = [];\n  let beforeAddClasses = [];\n  let beforeRemoveClasses = [];\n  let initialized = false;\n  let parentAnimation;\n  let beforeStylesValue = {};\n  let afterAddClasses = [];\n  let afterRemoveClasses = [];\n  let afterStylesValue = {};\n  let numAnimationsRunning = 0;\n  let shouldForceLinearEasing = false;\n  let shouldForceSyncPlayback = false;\n  let cssAnimationsTimerFallback;\n  let forceDirectionValue;\n  let forceDurationValue;\n  let forceDelayValue;\n  let willComplete = true;\n  let finished = false;\n  let shouldCalculateNumAnimations = true;\n  let keyframeName;\n  let ani;\n  let paused = false;\n  const id = animationId;\n  const onFinishCallbacks = [];\n  const onFinishOneTimeCallbacks = [];\n  const onStopOneTimeCallbacks = [];\n  const elements = [];\n  const childAnimations = [];\n  const stylesheets = [];\n  const _beforeAddReadFunctions = [];\n  const _beforeAddWriteFunctions = [];\n  const _afterAddReadFunctions = [];\n  const _afterAddWriteFunctions = [];\n  const webAnimations = [];\n  const supportsAnimationEffect = typeof AnimationEffect === 'function' || win !== undefined && typeof win.AnimationEffect === 'function';\n  const supportsWebAnimations = typeof Element === 'function' && typeof Element.prototype.animate === 'function' && supportsAnimationEffect;\n  const ANIMATION_END_FALLBACK_PADDING_MS = 100;\n  const getWebAnimations = () => {\n    return webAnimations;\n  };\n  const destroy = clearStyleSheets => {\n    childAnimations.forEach(childAnimation => {\n      childAnimation.destroy(clearStyleSheets);\n    });\n    cleanUp(clearStyleSheets);\n    elements.length = 0;\n    childAnimations.length = 0;\n    _keyframes.length = 0;\n    clearOnFinish();\n    initialized = false;\n    shouldCalculateNumAnimations = true;\n    return ani;\n  };\n  /**\n   * Cancels any Web Animations, removes\n   * any animation properties from the\n   * animation's elements, and removes the\n   * animation's stylesheets from the DOM.\n   */\n  const cleanUp = clearStyleSheets => {\n    cleanUpElements();\n    if (clearStyleSheets) {\n      cleanUpStyleSheets();\n    }\n  };\n  const resetFlags = () => {\n    shouldForceLinearEasing = false;\n    shouldForceSyncPlayback = false;\n    shouldCalculateNumAnimations = true;\n    forceDirectionValue = undefined;\n    forceDurationValue = undefined;\n    forceDelayValue = undefined;\n    numAnimationsRunning = 0;\n    finished = false;\n    willComplete = true;\n    paused = false;\n  };\n  const isRunning = () => {\n    return numAnimationsRunning !== 0 && !paused;\n  };\n  /**\n   * @internal\n   * Remove a callback from a chosen callback array\n   * @param callbackToRemove: A reference to the callback that should be removed\n   * @param callbackObjects: An array of callbacks that callbackToRemove should be removed from.\n   */\n  const clearCallback = (callbackToRemove, callbackObjects) => {\n    const index = callbackObjects.findIndex(callbackObject => callbackObject.c === callbackToRemove);\n    if (index > -1) {\n      callbackObjects.splice(index, 1);\n    }\n  };\n  /**\n   * @internal\n   * Add a callback to be fired when an animation is stopped/cancelled.\n   * @param callback: A reference to the callback that should be fired\n   * @param opts: Any options associated with this particular callback\n   */\n  const onStop = (callback, opts) => {\n    onStopOneTimeCallbacks.push({\n      c: callback,\n      o: opts\n    });\n    return ani;\n  };\n  const onFinish = (callback, opts) => {\n    const callbacks = (opts === null || opts === void 0 ? void 0 : opts.oneTimeCallback) ? onFinishOneTimeCallbacks : onFinishCallbacks;\n    callbacks.push({\n      c: callback,\n      o: opts\n    });\n    return ani;\n  };\n  const clearOnFinish = () => {\n    onFinishCallbacks.length = 0;\n    onFinishOneTimeCallbacks.length = 0;\n    return ani;\n  };\n  /**\n   * Cancels any Web Animations and removes\n   * any animation properties from the\n   * the animation's elements.\n   */\n  const cleanUpElements = () => {\n    if (supportsWebAnimations) {\n      webAnimations.forEach(animation => {\n        animation.cancel();\n      });\n      webAnimations.length = 0;\n    } else {\n      const elementsArray = elements.slice();\n      raf(() => {\n        elementsArray.forEach(element => {\n          removeStyleProperty(element, 'animation-name');\n          removeStyleProperty(element, 'animation-duration');\n          removeStyleProperty(element, 'animation-timing-function');\n          removeStyleProperty(element, 'animation-iteration-count');\n          removeStyleProperty(element, 'animation-delay');\n          removeStyleProperty(element, 'animation-play-state');\n          removeStyleProperty(element, 'animation-fill-mode');\n          removeStyleProperty(element, 'animation-direction');\n        });\n      });\n    }\n  };\n  /**\n   * Removes the animation's stylesheets\n   * from the DOM.\n   */\n  const cleanUpStyleSheets = () => {\n    stylesheets.forEach(stylesheet => {\n      /**\n       * When sharing stylesheets, it's possible\n       * for another animation to have already\n       * cleaned up a particular stylesheet\n       */\n      if (stylesheet === null || stylesheet === void 0 ? void 0 : stylesheet.parentNode) {\n        stylesheet.parentNode.removeChild(stylesheet);\n      }\n    });\n    stylesheets.length = 0;\n  };\n  const beforeAddRead = readFn => {\n    _beforeAddReadFunctions.push(readFn);\n    return ani;\n  };\n  const beforeAddWrite = writeFn => {\n    _beforeAddWriteFunctions.push(writeFn);\n    return ani;\n  };\n  const afterAddRead = readFn => {\n    _afterAddReadFunctions.push(readFn);\n    return ani;\n  };\n  const afterAddWrite = writeFn => {\n    _afterAddWriteFunctions.push(writeFn);\n    return ani;\n  };\n  const beforeAddClass = className => {\n    beforeAddClasses = addClassToArray(beforeAddClasses, className);\n    return ani;\n  };\n  const beforeRemoveClass = className => {\n    beforeRemoveClasses = addClassToArray(beforeRemoveClasses, className);\n    return ani;\n  };\n  /**\n   * Set CSS inline styles to the animation's\n   * elements before the animation begins.\n   */\n  const beforeStyles = (styles = {}) => {\n    beforeStylesValue = styles;\n    return ani;\n  };\n  /**\n   * Clear CSS inline styles from the animation's\n   * elements before the animation begins.\n   */\n  const beforeClearStyles = (propertyNames = []) => {\n    for (const property of propertyNames) {\n      beforeStylesValue[property] = '';\n    }\n    return ani;\n  };\n  const afterAddClass = className => {\n    afterAddClasses = addClassToArray(afterAddClasses, className);\n    return ani;\n  };\n  const afterRemoveClass = className => {\n    afterRemoveClasses = addClassToArray(afterRemoveClasses, className);\n    return ani;\n  };\n  const afterStyles = (styles = {}) => {\n    afterStylesValue = styles;\n    return ani;\n  };\n  const afterClearStyles = (propertyNames = []) => {\n    for (const property of propertyNames) {\n      afterStylesValue[property] = '';\n    }\n    return ani;\n  };\n  const getFill = () => {\n    if (_fill !== undefined) {\n      return _fill;\n    }\n    if (parentAnimation) {\n      return parentAnimation.getFill();\n    }\n    return 'both';\n  };\n  const getDirection = () => {\n    if (forceDirectionValue !== undefined) {\n      return forceDirectionValue;\n    }\n    if (_direction !== undefined) {\n      return _direction;\n    }\n    if (parentAnimation) {\n      return parentAnimation.getDirection();\n    }\n    return 'normal';\n  };\n  const getEasing = () => {\n    if (shouldForceLinearEasing) {\n      return 'linear';\n    }\n    if (_easing !== undefined) {\n      return _easing;\n    }\n    if (parentAnimation) {\n      return parentAnimation.getEasing();\n    }\n    return 'linear';\n  };\n  const getDuration = () => {\n    if (shouldForceSyncPlayback) {\n      return 0;\n    }\n    if (forceDurationValue !== undefined) {\n      return forceDurationValue;\n    }\n    if (_duration !== undefined) {\n      return _duration;\n    }\n    if (parentAnimation) {\n      return parentAnimation.getDuration();\n    }\n    return 0;\n  };\n  const getIterations = () => {\n    if (_iterations !== undefined) {\n      return _iterations;\n    }\n    if (parentAnimation) {\n      return parentAnimation.getIterations();\n    }\n    return 1;\n  };\n  const getDelay = () => {\n    if (forceDelayValue !== undefined) {\n      return forceDelayValue;\n    }\n    if (_delay !== undefined) {\n      return _delay;\n    }\n    if (parentAnimation) {\n      return parentAnimation.getDelay();\n    }\n    return 0;\n  };\n  const getKeyframes = () => {\n    return _keyframes;\n  };\n  const direction = animationDirection => {\n    _direction = animationDirection;\n    update(true);\n    return ani;\n  };\n  const fill = animationFill => {\n    _fill = animationFill;\n    update(true);\n    return ani;\n  };\n  const delay = animationDelay => {\n    _delay = animationDelay;\n    update(true);\n    return ani;\n  };\n  const easing = animationEasing => {\n    _easing = animationEasing;\n    update(true);\n    return ani;\n  };\n  const duration = animationDuration => {\n    /**\n     * CSS Animation Durations of 0ms work fine on Chrome\n     * but do not run on Safari, so force it to 1ms to\n     * get it to run on both platforms.\n     */\n    if (!supportsWebAnimations && animationDuration === 0) {\n      animationDuration = 1;\n    }\n    _duration = animationDuration;\n    update(true);\n    return ani;\n  };\n  const iterations = animationIterations => {\n    _iterations = animationIterations;\n    update(true);\n    return ani;\n  };\n  const parent = animation => {\n    parentAnimation = animation;\n    return ani;\n  };\n  const addElement = el => {\n    if (el != null) {\n      if (el.nodeType === 1) {\n        elements.push(el);\n      } else if (el.length >= 0) {\n        for (let i = 0; i < el.length; i++) {\n          elements.push(el[i]);\n        }\n      } else {\n        console.error('Invalid addElement value');\n      }\n    }\n    return ani;\n  };\n  const addAnimation = animationToAdd => {\n    if (animationToAdd != null) {\n      if (Array.isArray(animationToAdd)) {\n        for (const animation of animationToAdd) {\n          animation.parent(ani);\n          childAnimations.push(animation);\n        }\n      } else {\n        animationToAdd.parent(ani);\n        childAnimations.push(animationToAdd);\n      }\n    }\n    return ani;\n  };\n  const keyframes = keyframeValues => {\n    const different = _keyframes !== keyframeValues;\n    _keyframes = keyframeValues;\n    if (different) {\n      updateKeyframes(_keyframes);\n    }\n    return ani;\n  };\n  const updateKeyframes = keyframeValues => {\n    if (supportsWebAnimations) {\n      getWebAnimations().forEach(animation => {\n        /**\n         * animation.effect's type is AnimationEffect.\n         * However, in this case we have a more specific\n         * type of AnimationEffect called KeyframeEffect which\n         * inherits from AnimationEffect. As a result,\n         * we cast animation.effect to KeyframeEffect.\n         */\n        const keyframeEffect = animation.effect;\n        /**\n         * setKeyframes is not supported in all browser\n         * versions that Ionic supports, so we need to\n         * check for support before using it.\n         */\n        // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions\n        if (keyframeEffect.setKeyframes) {\n          keyframeEffect.setKeyframes(keyframeValues);\n        } else {\n          const newEffect = new KeyframeEffect(keyframeEffect.target, keyframeValues, keyframeEffect.getTiming());\n          animation.effect = newEffect;\n        }\n      });\n    } else {\n      initializeCSSAnimation();\n    }\n  };\n  /**\n   * Run all \"before\" animation hooks.\n   */\n  const beforeAnimation = () => {\n    // Runs all before read callbacks\n    _beforeAddReadFunctions.forEach(callback => callback());\n    // Runs all before write callbacks\n    _beforeAddWriteFunctions.forEach(callback => callback());\n    // Updates styles and classes before animation runs\n    const addClasses = beforeAddClasses;\n    const removeClasses = beforeRemoveClasses;\n    const styles = beforeStylesValue;\n    elements.forEach(el => {\n      const elementClassList = el.classList;\n      addClasses.forEach(c => elementClassList.add(c));\n      removeClasses.forEach(c => elementClassList.remove(c));\n      for (const property in styles) {\n        // eslint-disable-next-line no-prototype-builtins\n        if (styles.hasOwnProperty(property)) {\n          setStyleProperty(el, property, styles[property]);\n        }\n      }\n    });\n  };\n  /**\n   * Run all \"after\" animation hooks.\n   */\n  const afterAnimation = () => {\n    clearCSSAnimationsTimeout();\n    // Runs all after read callbacks\n    _afterAddReadFunctions.forEach(callback => callback());\n    // Runs all after write callbacks\n    _afterAddWriteFunctions.forEach(callback => callback());\n    // Updates styles and classes before animation ends\n    const currentStep = willComplete ? 1 : 0;\n    const addClasses = afterAddClasses;\n    const removeClasses = afterRemoveClasses;\n    const styles = afterStylesValue;\n    elements.forEach(el => {\n      const elementClassList = el.classList;\n      addClasses.forEach(c => elementClassList.add(c));\n      removeClasses.forEach(c => elementClassList.remove(c));\n      for (const property in styles) {\n        // eslint-disable-next-line no-prototype-builtins\n        if (styles.hasOwnProperty(property)) {\n          setStyleProperty(el, property, styles[property]);\n        }\n      }\n    });\n    /**\n     * Clean up any value coercion before\n     * the user callbacks fire otherwise\n     * they may get stale values. For example,\n     * if someone calls progressStart(0) the\n     * animation may still be reversed.\n     */\n    forceDurationValue = undefined;\n    forceDirectionValue = undefined;\n    forceDelayValue = undefined;\n    onFinishCallbacks.forEach(onFinishCallback => {\n      return onFinishCallback.c(currentStep, ani);\n    });\n    onFinishOneTimeCallbacks.forEach(onFinishCallback => {\n      return onFinishCallback.c(currentStep, ani);\n    });\n    onFinishOneTimeCallbacks.length = 0;\n    shouldCalculateNumAnimations = true;\n    if (willComplete) {\n      finished = true;\n    }\n    willComplete = true;\n  };\n  const animationFinish = () => {\n    if (numAnimationsRunning === 0) {\n      return;\n    }\n    numAnimationsRunning--;\n    if (numAnimationsRunning === 0) {\n      afterAnimation();\n      if (parentAnimation) {\n        parentAnimation.animationFinish();\n      }\n    }\n  };\n  const initializeCSSAnimation = (toggleAnimationName = true) => {\n    cleanUpStyleSheets();\n    const processedKeyframes = processKeyframes(_keyframes);\n    elements.forEach(element => {\n      if (processedKeyframes.length > 0) {\n        const keyframeRules = generateKeyframeRules(processedKeyframes);\n        keyframeName = animationId !== undefined ? animationId : generateKeyframeName(keyframeRules);\n        const stylesheet = createKeyframeStylesheet(keyframeName, keyframeRules, element);\n        stylesheets.push(stylesheet);\n        setStyleProperty(element, 'animation-duration', `${getDuration()}ms`);\n        setStyleProperty(element, 'animation-timing-function', getEasing());\n        setStyleProperty(element, 'animation-delay', `${getDelay()}ms`);\n        setStyleProperty(element, 'animation-fill-mode', getFill());\n        setStyleProperty(element, 'animation-direction', getDirection());\n        const iterationsCount = getIterations() === Infinity ? 'infinite' : getIterations().toString();\n        setStyleProperty(element, 'animation-iteration-count', iterationsCount);\n        setStyleProperty(element, 'animation-play-state', 'paused');\n        if (toggleAnimationName) {\n          setStyleProperty(element, 'animation-name', `${stylesheet.id}-alt`);\n        }\n        raf(() => {\n          setStyleProperty(element, 'animation-name', stylesheet.id || null);\n        });\n      }\n    });\n  };\n  const initializeWebAnimation = () => {\n    elements.forEach(element => {\n      const animation = element.animate(_keyframes, {\n        id,\n        delay: getDelay(),\n        duration: getDuration(),\n        easing: getEasing(),\n        iterations: getIterations(),\n        fill: getFill(),\n        direction: getDirection()\n      });\n      animation.pause();\n      webAnimations.push(animation);\n    });\n    if (webAnimations.length > 0) {\n      webAnimations[0].onfinish = () => {\n        animationFinish();\n      };\n    }\n  };\n  const initializeAnimation = (toggleAnimationName = true) => {\n    beforeAnimation();\n    if (_keyframes.length > 0) {\n      if (supportsWebAnimations) {\n        initializeWebAnimation();\n      } else {\n        initializeCSSAnimation(toggleAnimationName);\n      }\n    }\n    initialized = true;\n  };\n  const setAnimationStep = step => {\n    step = Math.min(Math.max(step, 0), 0.9999);\n    if (supportsWebAnimations) {\n      webAnimations.forEach(animation => {\n        // When creating the animation the delay is guaranteed to be set to a number.\n        animation.currentTime = animation.effect.getComputedTiming().delay + getDuration() * step;\n        animation.pause();\n      });\n    } else {\n      const animationDuration = `-${getDuration() * step}ms`;\n      elements.forEach(element => {\n        if (_keyframes.length > 0) {\n          setStyleProperty(element, 'animation-delay', animationDuration);\n          setStyleProperty(element, 'animation-play-state', 'paused');\n        }\n      });\n    }\n  };\n  const updateWebAnimation = step => {\n    webAnimations.forEach(animation => {\n      animation.effect.updateTiming({\n        delay: getDelay(),\n        duration: getDuration(),\n        easing: getEasing(),\n        iterations: getIterations(),\n        fill: getFill(),\n        direction: getDirection()\n      });\n    });\n    if (step !== undefined) {\n      setAnimationStep(step);\n    }\n  };\n  const updateCSSAnimation = (toggleAnimationName = true, step) => {\n    raf(() => {\n      elements.forEach(element => {\n        setStyleProperty(element, 'animation-name', keyframeName || null);\n        setStyleProperty(element, 'animation-duration', `${getDuration()}ms`);\n        setStyleProperty(element, 'animation-timing-function', getEasing());\n        setStyleProperty(element, 'animation-delay', step !== undefined ? `-${step * getDuration()}ms` : `${getDelay()}ms`);\n        setStyleProperty(element, 'animation-fill-mode', getFill() || null);\n        setStyleProperty(element, 'animation-direction', getDirection() || null);\n        const iterationsCount = getIterations() === Infinity ? 'infinite' : getIterations().toString();\n        setStyleProperty(element, 'animation-iteration-count', iterationsCount);\n        if (toggleAnimationName) {\n          setStyleProperty(element, 'animation-name', `${keyframeName}-alt`);\n        }\n        raf(() => {\n          setStyleProperty(element, 'animation-name', keyframeName || null);\n        });\n      });\n    });\n  };\n  const update = (deep = false, toggleAnimationName = true, step) => {\n    if (deep) {\n      childAnimations.forEach(animation => {\n        animation.update(deep, toggleAnimationName, step);\n      });\n    }\n    if (supportsWebAnimations) {\n      updateWebAnimation(step);\n    } else {\n      updateCSSAnimation(toggleAnimationName, step);\n    }\n    return ani;\n  };\n  const progressStart = (forceLinearEasing = false, step) => {\n    childAnimations.forEach(animation => {\n      animation.progressStart(forceLinearEasing, step);\n    });\n    pauseAnimation();\n    shouldForceLinearEasing = forceLinearEasing;\n    if (!initialized) {\n      initializeAnimation();\n    }\n    update(false, true, step);\n    return ani;\n  };\n  const progressStep = step => {\n    childAnimations.forEach(animation => {\n      animation.progressStep(step);\n    });\n    setAnimationStep(step);\n    return ani;\n  };\n  const progressEnd = (playTo, step, dur) => {\n    shouldForceLinearEasing = false;\n    childAnimations.forEach(animation => {\n      animation.progressEnd(playTo, step, dur);\n    });\n    if (dur !== undefined) {\n      forceDurationValue = dur;\n    }\n    finished = false;\n    willComplete = true;\n    if (playTo === 0) {\n      forceDirectionValue = getDirection() === 'reverse' ? 'normal' : 'reverse';\n      if (forceDirectionValue === 'reverse') {\n        willComplete = false;\n      }\n      if (supportsWebAnimations) {\n        update();\n        setAnimationStep(1 - step);\n      } else {\n        forceDelayValue = (1 - step) * getDuration() * -1;\n        update(false, false);\n      }\n    } else if (playTo === 1) {\n      if (supportsWebAnimations) {\n        update();\n        setAnimationStep(step);\n      } else {\n        forceDelayValue = step * getDuration() * -1;\n        update(false, false);\n      }\n    }\n    if (playTo !== undefined && !parentAnimation) {\n      play();\n    }\n    return ani;\n  };\n  const pauseAnimation = () => {\n    if (initialized) {\n      if (supportsWebAnimations) {\n        webAnimations.forEach(animation => {\n          animation.pause();\n        });\n      } else {\n        elements.forEach(element => {\n          setStyleProperty(element, 'animation-play-state', 'paused');\n        });\n      }\n      paused = true;\n    }\n  };\n  const pause = () => {\n    childAnimations.forEach(animation => {\n      animation.pause();\n    });\n    pauseAnimation();\n    return ani;\n  };\n  const onAnimationEndFallback = () => {\n    cssAnimationsTimerFallback = undefined;\n    animationFinish();\n  };\n  const clearCSSAnimationsTimeout = () => {\n    if (cssAnimationsTimerFallback) {\n      clearTimeout(cssAnimationsTimerFallback);\n    }\n  };\n  const playCSSAnimations = () => {\n    clearCSSAnimationsTimeout();\n    raf(() => {\n      elements.forEach(element => {\n        if (_keyframes.length > 0) {\n          setStyleProperty(element, 'animation-play-state', 'running');\n        }\n      });\n    });\n    if (_keyframes.length === 0 || elements.length === 0) {\n      animationFinish();\n    } else {\n      /**\n       * This is a catchall in the event that a CSS Animation did not finish.\n       * The Web Animations API has mechanisms in place for preventing this.\n       * CSS Animations will not fire an `animationend` event\n       * for elements with `display: none`. The Web Animations API\n       * accounts for this, but using raw CSS Animations requires\n       * this workaround.\n       */\n      const animationDelay = getDelay() || 0;\n      const animationDuration = getDuration() || 0;\n      const animationIterations = getIterations() || 1;\n      // No need to set a timeout when animation has infinite iterations\n      if (isFinite(animationIterations)) {\n        cssAnimationsTimerFallback = setTimeout(onAnimationEndFallback, animationDelay + animationDuration * animationIterations + ANIMATION_END_FALLBACK_PADDING_MS);\n      }\n      animationEnd(elements[0], () => {\n        clearCSSAnimationsTimeout();\n        /**\n         * Ensure that clean up\n         * is always done a frame\n         * before the onFinish handlers\n         * are fired. Otherwise, there\n         * may be flickering if a new\n         * animation is started on the same\n         * element too quickly\n         */\n        raf(() => {\n          clearCSSAnimationPlayState();\n          raf(animationFinish);\n        });\n      });\n    }\n  };\n  const clearCSSAnimationPlayState = () => {\n    elements.forEach(element => {\n      removeStyleProperty(element, 'animation-duration');\n      removeStyleProperty(element, 'animation-delay');\n      removeStyleProperty(element, 'animation-play-state');\n    });\n  };\n  const playWebAnimations = () => {\n    webAnimations.forEach(animation => {\n      animation.play();\n    });\n    if (_keyframes.length === 0 || elements.length === 0) {\n      animationFinish();\n    }\n  };\n  const resetAnimation = () => {\n    if (supportsWebAnimations) {\n      setAnimationStep(0);\n      updateWebAnimation();\n    } else {\n      updateCSSAnimation();\n    }\n  };\n  const play = opts => {\n    return new Promise(resolve => {\n      if (opts === null || opts === void 0 ? void 0 : opts.sync) {\n        shouldForceSyncPlayback = true;\n        onFinish(() => shouldForceSyncPlayback = false, {\n          oneTimeCallback: true\n        });\n      }\n      if (!initialized) {\n        initializeAnimation();\n      }\n      if (finished) {\n        resetAnimation();\n        finished = false;\n      }\n      if (shouldCalculateNumAnimations) {\n        numAnimationsRunning = childAnimations.length + 1;\n        shouldCalculateNumAnimations = false;\n      }\n      /**\n       * When one of these callbacks fires we\n       * need to clear the other's callback otherwise\n       * you can potentially get these callbacks\n       * firing multiple times if the play method\n       * is subsequently called.\n       * Example:\n       * animation.play() (onStop and onFinish callbacks are registered)\n       * animation.stop() (onStop callback is fired, onFinish is not)\n       * animation.play() (onStop and onFinish callbacks are registered)\n       * Total onStop callbacks: 1\n       * Total onFinish callbacks: 2\n       */\n      const onStopCallback = () => {\n        clearCallback(onFinishCallback, onFinishOneTimeCallbacks);\n        resolve();\n      };\n      const onFinishCallback = () => {\n        clearCallback(onStopCallback, onStopOneTimeCallbacks);\n        resolve();\n      };\n      /**\n       * The play method resolves when an animation\n       * run either finishes or is cancelled.\n       */\n      onFinish(onFinishCallback, {\n        oneTimeCallback: true\n      });\n      onStop(onStopCallback, {\n        oneTimeCallback: true\n      });\n      childAnimations.forEach(animation => {\n        animation.play();\n      });\n      if (supportsWebAnimations) {\n        playWebAnimations();\n      } else {\n        playCSSAnimations();\n      }\n      paused = false;\n    });\n  };\n  /**\n   * Stops an animation and resets it state to the\n   * beginning. This does not fire any onFinish\n   * callbacks because the animation did not finish.\n   * However, since the animation was not destroyed\n   * (i.e. the animation could run again) we do not\n   * clear the onFinish callbacks.\n   */\n  const stop = () => {\n    childAnimations.forEach(animation => {\n      animation.stop();\n    });\n    if (initialized) {\n      cleanUpElements();\n      initialized = false;\n    }\n    resetFlags();\n    onStopOneTimeCallbacks.forEach(onStopCallback => onStopCallback.c(0, ani));\n    onStopOneTimeCallbacks.length = 0;\n  };\n  const from = (property, value) => {\n    const firstFrame = _keyframes[0];\n    if (firstFrame !== undefined && (firstFrame.offset === undefined || firstFrame.offset === 0)) {\n      firstFrame[property] = value;\n    } else {\n      _keyframes = [{\n        offset: 0,\n        [property]: value\n      }, ..._keyframes];\n    }\n    return ani;\n  };\n  const to = (property, value) => {\n    const lastFrame = _keyframes[_keyframes.length - 1];\n    if (lastFrame !== undefined && (lastFrame.offset === undefined || lastFrame.offset === 1)) {\n      lastFrame[property] = value;\n    } else {\n      _keyframes = [..._keyframes, {\n        offset: 1,\n        [property]: value\n      }];\n    }\n    return ani;\n  };\n  const fromTo = (property, fromValue, toValue) => {\n    return from(property, fromValue).to(property, toValue);\n  };\n  return ani = {\n    parentAnimation,\n    elements,\n    childAnimations,\n    id,\n    animationFinish,\n    from,\n    to,\n    fromTo,\n    parent,\n    play,\n    pause,\n    stop,\n    destroy,\n    keyframes,\n    addAnimation,\n    addElement,\n    update,\n    fill,\n    direction,\n    iterations,\n    duration,\n    easing,\n    delay,\n    getWebAnimations,\n    getKeyframes,\n    getFill,\n    getDirection,\n    getDelay,\n    getIterations,\n    getEasing,\n    getDuration,\n    afterAddRead,\n    afterAddWrite,\n    afterClearStyles,\n    afterStyles,\n    afterRemoveClass,\n    afterAddClass,\n    beforeAddRead,\n    beforeAddWrite,\n    beforeClearStyles,\n    beforeStyles,\n    beforeRemoveClass,\n    beforeAddClass,\n    onFinish,\n    isRunning,\n    progressStart,\n    progressStep,\n    progressEnd\n  };\n};\nexport { createAnimation as c };", "map": {"version": 3, "names": ["w", "win", "r", "raf", "animationPrefix", "processKeyframes", "keyframes", "for<PERSON>ach", "keyframe", "key", "hasOwnProperty", "value", "new<PERSON>ey", "convertCamelCaseToHypen", "str", "replace", "toLowerCase", "getAnimationPrefix", "el", "undefined", "supportsUnprefixed", "style", "animationName", "supportsWebkitPrefix", "webkitAnimationName", "setStyleProperty", "element", "propertyName", "prefix", "startsWith", "setProperty", "removeStyleProperty", "removeProperty", "animationEnd", "callback", "unRegTrans", "opts", "passive", "unregister", "onTransitionEnd", "ev", "target", "addEventListener", "removeEventListener", "generateKeyframeRules", "map", "offset", "frameString", "property", "push", "join", "keyframeIds", "generateKeyframeName", "keyframeRules", "index", "indexOf", "getStyleContainer", "rootNode", "getRootNode", "head", "createKeyframeStylesheet", "keyframeName", "_a", "styleContainer", "keyframePrefix", "existingStylesheet", "querySelector", "stylesheet", "ownerDocument", "document", "createElement", "id", "textContent", "append<PERSON><PERSON><PERSON>", "addClassToArray", "classes", "className", "classNameToAppend", "Array", "isArray", "createAnimation", "animationId", "_delay", "_duration", "_easing", "_iterations", "_fill", "_direction", "_keyframes", "beforeAddClasses", "beforeRemoveClasses", "initialized", "parentAnimation", "beforeStylesValue", "afterAddClasses", "afterRemoveClasses", "afterStylesValue", "numAnimationsRunning", "shouldForceLinearEasing", "shouldForceSyncPlayback", "cssAnimationsTimerFallback", "forceDirectionValue", "forceDurationValue", "forceDelayValue", "willComplete", "finished", "shouldCalculateNumAnimations", "ani", "paused", "onFinishCallbacks", "onFinishOneTimeCallbacks", "onStopOneTimeCallbacks", "elements", "childAnimations", "stylesheets", "_beforeAddReadFunctions", "_beforeAddWriteFunctions", "_afterAddReadFunctions", "_afterAddWriteFunctions", "webAnimations", "supportsAnimationEffect", "AnimationEffect", "supportsWebAnimations", "Element", "prototype", "animate", "ANIMATION_END_FALLBACK_PADDING_MS", "getWebAnimations", "destroy", "clearStyleSheets", "childAnimation", "cleanUp", "length", "clearOnFinish", "cleanUpElements", "cleanUpStyleSheets", "resetFlags", "isRunning", "clearCallback", "callback<PERSON><PERSON><PERSON><PERSON><PERSON>", "callbackObjects", "findIndex", "callbackObject", "c", "splice", "onStop", "o", "onFinish", "callbacks", "oneTimeCallback", "animation", "cancel", "elementsArray", "slice", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "beforeAddRead", "readFn", "beforeAddWrite", "writeFn", "afterAddRead", "afterAddWrite", "beforeAddClass", "beforeRemoveClass", "beforeStyles", "styles", "beforeClearStyles", "propertyNames", "afterAddClass", "afterRemoveClass", "afterStyles", "afterClearStyles", "getFill", "getDirection", "getEasing", "getDuration", "getIterations", "get<PERSON>elay", "getKeyframes", "direction", "animationDirection", "update", "fill", "animationFill", "delay", "animationDelay", "easing", "animationEasing", "duration", "animationDuration", "iterations", "animationIterations", "parent", "addElement", "nodeType", "i", "console", "error", "addAnimation", "animationToAdd", "keyframeValues", "different", "updateKeyframes", "keyframeEffect", "effect", "setKeyframes", "newEffect", "KeyframeEffect", "getTiming", "initializeCSSAnimation", "beforeAnimation", "addClasses", "removeClasses", "elementClassList", "classList", "add", "remove", "afterAnimation", "clearCSSAnimationsTimeout", "currentStep", "onFinishCallback", "animationFinish", "toggleAnimationName", "processedKeyframes", "iterationsCount", "Infinity", "toString", "initializeWebAnimation", "pause", "onfinish", "initializeAnimation", "setAnimationStep", "step", "Math", "min", "max", "currentTime", "getComputedTiming", "updateWebAnimation", "updateTiming", "updateCSSAnimation", "deep", "progressStart", "forceLinearEasing", "pauseAnimation", "progressStep", "progressEnd", "playTo", "dur", "play", "onAnimationEndFallback", "clearTimeout", "playCSSAnimations", "isFinite", "setTimeout", "clearCSSAnimationPlayState", "playWebAnimations", "resetAnimation", "Promise", "resolve", "sync", "onStopCallback", "stop", "from", "firstFrame", "to", "<PERSON><PERSON><PERSON><PERSON>", "fromTo", "fromValue", "toValue"], "sources": ["E:/Fashion/DFashion/frontend/node_modules/@ionic/core/components/animation.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index5.js';\nimport { r as raf } from './helpers.js';\n\nlet animationPrefix;\n/**\n * Web Animations requires hyphenated CSS properties\n * to be written in camelCase when animating\n */\nconst processKeyframes = (keyframes) => {\n    keyframes.forEach((keyframe) => {\n        for (const key in keyframe) {\n            // eslint-disable-next-line no-prototype-builtins\n            if (keyframe.hasOwnProperty(key)) {\n                const value = keyframe[key];\n                if (key === 'easing') {\n                    const newKey = 'animation-timing-function';\n                    keyframe[newKey] = value;\n                    delete keyframe[key];\n                }\n                else {\n                    const newKey = convertCamelCaseToHypen(key);\n                    if (newKey !== key) {\n                        keyframe[newKey] = value;\n                        delete keyframe[key];\n                    }\n                }\n            }\n        }\n    });\n    return keyframes;\n};\nconst convertCamelCaseToHypen = (str) => {\n    return str.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n};\nconst getAnimationPrefix = (el) => {\n    if (animationPrefix === undefined) {\n        const supportsUnprefixed = el.style.animationName !== undefined;\n        const supportsWebkitPrefix = el.style.webkitAnimationName !== undefined;\n        animationPrefix = !supportsUnprefixed && supportsWebkitPrefix ? '-webkit-' : '';\n    }\n    return animationPrefix;\n};\nconst setStyleProperty = (element, propertyName, value) => {\n    const prefix = propertyName.startsWith('animation') ? getAnimationPrefix(element) : '';\n    element.style.setProperty(prefix + propertyName, value);\n};\nconst removeStyleProperty = (element, propertyName) => {\n    const prefix = propertyName.startsWith('animation') ? getAnimationPrefix(element) : '';\n    element.style.removeProperty(prefix + propertyName);\n};\nconst animationEnd = (el, callback) => {\n    let unRegTrans;\n    const opts = { passive: true };\n    const unregister = () => {\n        if (unRegTrans) {\n            unRegTrans();\n        }\n    };\n    const onTransitionEnd = (ev) => {\n        if (el === ev.target) {\n            unregister();\n            callback(ev);\n        }\n    };\n    if (el) {\n        el.addEventListener('webkitAnimationEnd', onTransitionEnd, opts);\n        el.addEventListener('animationend', onTransitionEnd, opts);\n        unRegTrans = () => {\n            el.removeEventListener('webkitAnimationEnd', onTransitionEnd, opts);\n            el.removeEventListener('animationend', onTransitionEnd, opts);\n        };\n    }\n    return unregister;\n};\n// TODO(FW-2832): type\nconst generateKeyframeRules = (keyframes = []) => {\n    return keyframes\n        .map((keyframe) => {\n        const offset = keyframe.offset;\n        const frameString = [];\n        for (const property in keyframe) {\n            // eslint-disable-next-line no-prototype-builtins\n            if (keyframe.hasOwnProperty(property) && property !== 'offset') {\n                frameString.push(`${property}: ${keyframe[property]};`);\n            }\n        }\n        return `${offset * 100}% { ${frameString.join(' ')} }`;\n    })\n        .join(' ');\n};\nconst keyframeIds = [];\nconst generateKeyframeName = (keyframeRules) => {\n    let index = keyframeIds.indexOf(keyframeRules);\n    if (index < 0) {\n        index = keyframeIds.push(keyframeRules) - 1;\n    }\n    return `ion-animation-${index}`;\n};\nconst getStyleContainer = (element) => {\n    // getRootNode is not always available in SSR environments.\n    // TODO(FW-2832): types\n    const rootNode = element.getRootNode !== undefined ? element.getRootNode() : element;\n    return rootNode.head || rootNode;\n};\nconst createKeyframeStylesheet = (keyframeName, keyframeRules, element) => {\n    var _a;\n    const styleContainer = getStyleContainer(element);\n    const keyframePrefix = getAnimationPrefix(element);\n    const existingStylesheet = styleContainer.querySelector('#' + keyframeName);\n    if (existingStylesheet) {\n        return existingStylesheet;\n    }\n    const stylesheet = ((_a = element.ownerDocument) !== null && _a !== void 0 ? _a : document).createElement('style');\n    stylesheet.id = keyframeName;\n    stylesheet.textContent = `@${keyframePrefix}keyframes ${keyframeName} { ${keyframeRules} } @${keyframePrefix}keyframes ${keyframeName}-alt { ${keyframeRules} }`;\n    styleContainer.appendChild(stylesheet);\n    return stylesheet;\n};\nconst addClassToArray = (classes = [], className) => {\n    if (className !== undefined) {\n        const classNameToAppend = Array.isArray(className) ? className : [className];\n        return [...classes, ...classNameToAppend];\n    }\n    return classes;\n};\n\nconst createAnimation = (animationId) => {\n    let _delay;\n    let _duration;\n    let _easing;\n    let _iterations;\n    let _fill;\n    let _direction;\n    let _keyframes = [];\n    let beforeAddClasses = [];\n    let beforeRemoveClasses = [];\n    let initialized = false;\n    let parentAnimation;\n    let beforeStylesValue = {};\n    let afterAddClasses = [];\n    let afterRemoveClasses = [];\n    let afterStylesValue = {};\n    let numAnimationsRunning = 0;\n    let shouldForceLinearEasing = false;\n    let shouldForceSyncPlayback = false;\n    let cssAnimationsTimerFallback;\n    let forceDirectionValue;\n    let forceDurationValue;\n    let forceDelayValue;\n    let willComplete = true;\n    let finished = false;\n    let shouldCalculateNumAnimations = true;\n    let keyframeName;\n    let ani;\n    let paused = false;\n    const id = animationId;\n    const onFinishCallbacks = [];\n    const onFinishOneTimeCallbacks = [];\n    const onStopOneTimeCallbacks = [];\n    const elements = [];\n    const childAnimations = [];\n    const stylesheets = [];\n    const _beforeAddReadFunctions = [];\n    const _beforeAddWriteFunctions = [];\n    const _afterAddReadFunctions = [];\n    const _afterAddWriteFunctions = [];\n    const webAnimations = [];\n    const supportsAnimationEffect = typeof AnimationEffect === 'function' ||\n        (win !== undefined && typeof win.AnimationEffect === 'function');\n    const supportsWebAnimations = typeof Element === 'function' &&\n        typeof Element.prototype.animate === 'function' &&\n        supportsAnimationEffect;\n    const ANIMATION_END_FALLBACK_PADDING_MS = 100;\n    const getWebAnimations = () => {\n        return webAnimations;\n    };\n    const destroy = (clearStyleSheets) => {\n        childAnimations.forEach((childAnimation) => {\n            childAnimation.destroy(clearStyleSheets);\n        });\n        cleanUp(clearStyleSheets);\n        elements.length = 0;\n        childAnimations.length = 0;\n        _keyframes.length = 0;\n        clearOnFinish();\n        initialized = false;\n        shouldCalculateNumAnimations = true;\n        return ani;\n    };\n    /**\n     * Cancels any Web Animations, removes\n     * any animation properties from the\n     * animation's elements, and removes the\n     * animation's stylesheets from the DOM.\n     */\n    const cleanUp = (clearStyleSheets) => {\n        cleanUpElements();\n        if (clearStyleSheets) {\n            cleanUpStyleSheets();\n        }\n    };\n    const resetFlags = () => {\n        shouldForceLinearEasing = false;\n        shouldForceSyncPlayback = false;\n        shouldCalculateNumAnimations = true;\n        forceDirectionValue = undefined;\n        forceDurationValue = undefined;\n        forceDelayValue = undefined;\n        numAnimationsRunning = 0;\n        finished = false;\n        willComplete = true;\n        paused = false;\n    };\n    const isRunning = () => {\n        return numAnimationsRunning !== 0 && !paused;\n    };\n    /**\n     * @internal\n     * Remove a callback from a chosen callback array\n     * @param callbackToRemove: A reference to the callback that should be removed\n     * @param callbackObjects: An array of callbacks that callbackToRemove should be removed from.\n     */\n    const clearCallback = (callbackToRemove, callbackObjects) => {\n        const index = callbackObjects.findIndex((callbackObject) => callbackObject.c === callbackToRemove);\n        if (index > -1) {\n            callbackObjects.splice(index, 1);\n        }\n    };\n    /**\n     * @internal\n     * Add a callback to be fired when an animation is stopped/cancelled.\n     * @param callback: A reference to the callback that should be fired\n     * @param opts: Any options associated with this particular callback\n     */\n    const onStop = (callback, opts) => {\n        onStopOneTimeCallbacks.push({ c: callback, o: opts });\n        return ani;\n    };\n    const onFinish = (callback, opts) => {\n        const callbacks = (opts === null || opts === void 0 ? void 0 : opts.oneTimeCallback) ? onFinishOneTimeCallbacks : onFinishCallbacks;\n        callbacks.push({ c: callback, o: opts });\n        return ani;\n    };\n    const clearOnFinish = () => {\n        onFinishCallbacks.length = 0;\n        onFinishOneTimeCallbacks.length = 0;\n        return ani;\n    };\n    /**\n     * Cancels any Web Animations and removes\n     * any animation properties from the\n     * the animation's elements.\n     */\n    const cleanUpElements = () => {\n        if (supportsWebAnimations) {\n            webAnimations.forEach((animation) => {\n                animation.cancel();\n            });\n            webAnimations.length = 0;\n        }\n        else {\n            const elementsArray = elements.slice();\n            raf(() => {\n                elementsArray.forEach((element) => {\n                    removeStyleProperty(element, 'animation-name');\n                    removeStyleProperty(element, 'animation-duration');\n                    removeStyleProperty(element, 'animation-timing-function');\n                    removeStyleProperty(element, 'animation-iteration-count');\n                    removeStyleProperty(element, 'animation-delay');\n                    removeStyleProperty(element, 'animation-play-state');\n                    removeStyleProperty(element, 'animation-fill-mode');\n                    removeStyleProperty(element, 'animation-direction');\n                });\n            });\n        }\n    };\n    /**\n     * Removes the animation's stylesheets\n     * from the DOM.\n     */\n    const cleanUpStyleSheets = () => {\n        stylesheets.forEach((stylesheet) => {\n            /**\n             * When sharing stylesheets, it's possible\n             * for another animation to have already\n             * cleaned up a particular stylesheet\n             */\n            if (stylesheet === null || stylesheet === void 0 ? void 0 : stylesheet.parentNode) {\n                stylesheet.parentNode.removeChild(stylesheet);\n            }\n        });\n        stylesheets.length = 0;\n    };\n    const beforeAddRead = (readFn) => {\n        _beforeAddReadFunctions.push(readFn);\n        return ani;\n    };\n    const beforeAddWrite = (writeFn) => {\n        _beforeAddWriteFunctions.push(writeFn);\n        return ani;\n    };\n    const afterAddRead = (readFn) => {\n        _afterAddReadFunctions.push(readFn);\n        return ani;\n    };\n    const afterAddWrite = (writeFn) => {\n        _afterAddWriteFunctions.push(writeFn);\n        return ani;\n    };\n    const beforeAddClass = (className) => {\n        beforeAddClasses = addClassToArray(beforeAddClasses, className);\n        return ani;\n    };\n    const beforeRemoveClass = (className) => {\n        beforeRemoveClasses = addClassToArray(beforeRemoveClasses, className);\n        return ani;\n    };\n    /**\n     * Set CSS inline styles to the animation's\n     * elements before the animation begins.\n     */\n    const beforeStyles = (styles = {}) => {\n        beforeStylesValue = styles;\n        return ani;\n    };\n    /**\n     * Clear CSS inline styles from the animation's\n     * elements before the animation begins.\n     */\n    const beforeClearStyles = (propertyNames = []) => {\n        for (const property of propertyNames) {\n            beforeStylesValue[property] = '';\n        }\n        return ani;\n    };\n    const afterAddClass = (className) => {\n        afterAddClasses = addClassToArray(afterAddClasses, className);\n        return ani;\n    };\n    const afterRemoveClass = (className) => {\n        afterRemoveClasses = addClassToArray(afterRemoveClasses, className);\n        return ani;\n    };\n    const afterStyles = (styles = {}) => {\n        afterStylesValue = styles;\n        return ani;\n    };\n    const afterClearStyles = (propertyNames = []) => {\n        for (const property of propertyNames) {\n            afterStylesValue[property] = '';\n        }\n        return ani;\n    };\n    const getFill = () => {\n        if (_fill !== undefined) {\n            return _fill;\n        }\n        if (parentAnimation) {\n            return parentAnimation.getFill();\n        }\n        return 'both';\n    };\n    const getDirection = () => {\n        if (forceDirectionValue !== undefined) {\n            return forceDirectionValue;\n        }\n        if (_direction !== undefined) {\n            return _direction;\n        }\n        if (parentAnimation) {\n            return parentAnimation.getDirection();\n        }\n        return 'normal';\n    };\n    const getEasing = () => {\n        if (shouldForceLinearEasing) {\n            return 'linear';\n        }\n        if (_easing !== undefined) {\n            return _easing;\n        }\n        if (parentAnimation) {\n            return parentAnimation.getEasing();\n        }\n        return 'linear';\n    };\n    const getDuration = () => {\n        if (shouldForceSyncPlayback) {\n            return 0;\n        }\n        if (forceDurationValue !== undefined) {\n            return forceDurationValue;\n        }\n        if (_duration !== undefined) {\n            return _duration;\n        }\n        if (parentAnimation) {\n            return parentAnimation.getDuration();\n        }\n        return 0;\n    };\n    const getIterations = () => {\n        if (_iterations !== undefined) {\n            return _iterations;\n        }\n        if (parentAnimation) {\n            return parentAnimation.getIterations();\n        }\n        return 1;\n    };\n    const getDelay = () => {\n        if (forceDelayValue !== undefined) {\n            return forceDelayValue;\n        }\n        if (_delay !== undefined) {\n            return _delay;\n        }\n        if (parentAnimation) {\n            return parentAnimation.getDelay();\n        }\n        return 0;\n    };\n    const getKeyframes = () => {\n        return _keyframes;\n    };\n    const direction = (animationDirection) => {\n        _direction = animationDirection;\n        update(true);\n        return ani;\n    };\n    const fill = (animationFill) => {\n        _fill = animationFill;\n        update(true);\n        return ani;\n    };\n    const delay = (animationDelay) => {\n        _delay = animationDelay;\n        update(true);\n        return ani;\n    };\n    const easing = (animationEasing) => {\n        _easing = animationEasing;\n        update(true);\n        return ani;\n    };\n    const duration = (animationDuration) => {\n        /**\n         * CSS Animation Durations of 0ms work fine on Chrome\n         * but do not run on Safari, so force it to 1ms to\n         * get it to run on both platforms.\n         */\n        if (!supportsWebAnimations && animationDuration === 0) {\n            animationDuration = 1;\n        }\n        _duration = animationDuration;\n        update(true);\n        return ani;\n    };\n    const iterations = (animationIterations) => {\n        _iterations = animationIterations;\n        update(true);\n        return ani;\n    };\n    const parent = (animation) => {\n        parentAnimation = animation;\n        return ani;\n    };\n    const addElement = (el) => {\n        if (el != null) {\n            if (el.nodeType === 1) {\n                elements.push(el);\n            }\n            else if (el.length >= 0) {\n                for (let i = 0; i < el.length; i++) {\n                    elements.push(el[i]);\n                }\n            }\n            else {\n                console.error('Invalid addElement value');\n            }\n        }\n        return ani;\n    };\n    const addAnimation = (animationToAdd) => {\n        if (animationToAdd != null) {\n            if (Array.isArray(animationToAdd)) {\n                for (const animation of animationToAdd) {\n                    animation.parent(ani);\n                    childAnimations.push(animation);\n                }\n            }\n            else {\n                animationToAdd.parent(ani);\n                childAnimations.push(animationToAdd);\n            }\n        }\n        return ani;\n    };\n    const keyframes = (keyframeValues) => {\n        const different = _keyframes !== keyframeValues;\n        _keyframes = keyframeValues;\n        if (different) {\n            updateKeyframes(_keyframes);\n        }\n        return ani;\n    };\n    const updateKeyframes = (keyframeValues) => {\n        if (supportsWebAnimations) {\n            getWebAnimations().forEach((animation) => {\n                /**\n                 * animation.effect's type is AnimationEffect.\n                 * However, in this case we have a more specific\n                 * type of AnimationEffect called KeyframeEffect which\n                 * inherits from AnimationEffect. As a result,\n                 * we cast animation.effect to KeyframeEffect.\n                 */\n                const keyframeEffect = animation.effect;\n                /**\n                 * setKeyframes is not supported in all browser\n                 * versions that Ionic supports, so we need to\n                 * check for support before using it.\n                 */\n                // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions\n                if (keyframeEffect.setKeyframes) {\n                    keyframeEffect.setKeyframes(keyframeValues);\n                }\n                else {\n                    const newEffect = new KeyframeEffect(keyframeEffect.target, keyframeValues, keyframeEffect.getTiming());\n                    animation.effect = newEffect;\n                }\n            });\n        }\n        else {\n            initializeCSSAnimation();\n        }\n    };\n    /**\n     * Run all \"before\" animation hooks.\n     */\n    const beforeAnimation = () => {\n        // Runs all before read callbacks\n        _beforeAddReadFunctions.forEach((callback) => callback());\n        // Runs all before write callbacks\n        _beforeAddWriteFunctions.forEach((callback) => callback());\n        // Updates styles and classes before animation runs\n        const addClasses = beforeAddClasses;\n        const removeClasses = beforeRemoveClasses;\n        const styles = beforeStylesValue;\n        elements.forEach((el) => {\n            const elementClassList = el.classList;\n            addClasses.forEach((c) => elementClassList.add(c));\n            removeClasses.forEach((c) => elementClassList.remove(c));\n            for (const property in styles) {\n                // eslint-disable-next-line no-prototype-builtins\n                if (styles.hasOwnProperty(property)) {\n                    setStyleProperty(el, property, styles[property]);\n                }\n            }\n        });\n    };\n    /**\n     * Run all \"after\" animation hooks.\n     */\n    const afterAnimation = () => {\n        clearCSSAnimationsTimeout();\n        // Runs all after read callbacks\n        _afterAddReadFunctions.forEach((callback) => callback());\n        // Runs all after write callbacks\n        _afterAddWriteFunctions.forEach((callback) => callback());\n        // Updates styles and classes before animation ends\n        const currentStep = willComplete ? 1 : 0;\n        const addClasses = afterAddClasses;\n        const removeClasses = afterRemoveClasses;\n        const styles = afterStylesValue;\n        elements.forEach((el) => {\n            const elementClassList = el.classList;\n            addClasses.forEach((c) => elementClassList.add(c));\n            removeClasses.forEach((c) => elementClassList.remove(c));\n            for (const property in styles) {\n                // eslint-disable-next-line no-prototype-builtins\n                if (styles.hasOwnProperty(property)) {\n                    setStyleProperty(el, property, styles[property]);\n                }\n            }\n        });\n        /**\n         * Clean up any value coercion before\n         * the user callbacks fire otherwise\n         * they may get stale values. For example,\n         * if someone calls progressStart(0) the\n         * animation may still be reversed.\n         */\n        forceDurationValue = undefined;\n        forceDirectionValue = undefined;\n        forceDelayValue = undefined;\n        onFinishCallbacks.forEach((onFinishCallback) => {\n            return onFinishCallback.c(currentStep, ani);\n        });\n        onFinishOneTimeCallbacks.forEach((onFinishCallback) => {\n            return onFinishCallback.c(currentStep, ani);\n        });\n        onFinishOneTimeCallbacks.length = 0;\n        shouldCalculateNumAnimations = true;\n        if (willComplete) {\n            finished = true;\n        }\n        willComplete = true;\n    };\n    const animationFinish = () => {\n        if (numAnimationsRunning === 0) {\n            return;\n        }\n        numAnimationsRunning--;\n        if (numAnimationsRunning === 0) {\n            afterAnimation();\n            if (parentAnimation) {\n                parentAnimation.animationFinish();\n            }\n        }\n    };\n    const initializeCSSAnimation = (toggleAnimationName = true) => {\n        cleanUpStyleSheets();\n        const processedKeyframes = processKeyframes(_keyframes);\n        elements.forEach((element) => {\n            if (processedKeyframes.length > 0) {\n                const keyframeRules = generateKeyframeRules(processedKeyframes);\n                keyframeName = animationId !== undefined ? animationId : generateKeyframeName(keyframeRules);\n                const stylesheet = createKeyframeStylesheet(keyframeName, keyframeRules, element);\n                stylesheets.push(stylesheet);\n                setStyleProperty(element, 'animation-duration', `${getDuration()}ms`);\n                setStyleProperty(element, 'animation-timing-function', getEasing());\n                setStyleProperty(element, 'animation-delay', `${getDelay()}ms`);\n                setStyleProperty(element, 'animation-fill-mode', getFill());\n                setStyleProperty(element, 'animation-direction', getDirection());\n                const iterationsCount = getIterations() === Infinity ? 'infinite' : getIterations().toString();\n                setStyleProperty(element, 'animation-iteration-count', iterationsCount);\n                setStyleProperty(element, 'animation-play-state', 'paused');\n                if (toggleAnimationName) {\n                    setStyleProperty(element, 'animation-name', `${stylesheet.id}-alt`);\n                }\n                raf(() => {\n                    setStyleProperty(element, 'animation-name', stylesheet.id || null);\n                });\n            }\n        });\n    };\n    const initializeWebAnimation = () => {\n        elements.forEach((element) => {\n            const animation = element.animate(_keyframes, {\n                id,\n                delay: getDelay(),\n                duration: getDuration(),\n                easing: getEasing(),\n                iterations: getIterations(),\n                fill: getFill(),\n                direction: getDirection(),\n            });\n            animation.pause();\n            webAnimations.push(animation);\n        });\n        if (webAnimations.length > 0) {\n            webAnimations[0].onfinish = () => {\n                animationFinish();\n            };\n        }\n    };\n    const initializeAnimation = (toggleAnimationName = true) => {\n        beforeAnimation();\n        if (_keyframes.length > 0) {\n            if (supportsWebAnimations) {\n                initializeWebAnimation();\n            }\n            else {\n                initializeCSSAnimation(toggleAnimationName);\n            }\n        }\n        initialized = true;\n    };\n    const setAnimationStep = (step) => {\n        step = Math.min(Math.max(step, 0), 0.9999);\n        if (supportsWebAnimations) {\n            webAnimations.forEach((animation) => {\n                // When creating the animation the delay is guaranteed to be set to a number.\n                animation.currentTime = animation.effect.getComputedTiming().delay + getDuration() * step;\n                animation.pause();\n            });\n        }\n        else {\n            const animationDuration = `-${getDuration() * step}ms`;\n            elements.forEach((element) => {\n                if (_keyframes.length > 0) {\n                    setStyleProperty(element, 'animation-delay', animationDuration);\n                    setStyleProperty(element, 'animation-play-state', 'paused');\n                }\n            });\n        }\n    };\n    const updateWebAnimation = (step) => {\n        webAnimations.forEach((animation) => {\n            animation.effect.updateTiming({\n                delay: getDelay(),\n                duration: getDuration(),\n                easing: getEasing(),\n                iterations: getIterations(),\n                fill: getFill(),\n                direction: getDirection(),\n            });\n        });\n        if (step !== undefined) {\n            setAnimationStep(step);\n        }\n    };\n    const updateCSSAnimation = (toggleAnimationName = true, step) => {\n        raf(() => {\n            elements.forEach((element) => {\n                setStyleProperty(element, 'animation-name', keyframeName || null);\n                setStyleProperty(element, 'animation-duration', `${getDuration()}ms`);\n                setStyleProperty(element, 'animation-timing-function', getEasing());\n                setStyleProperty(element, 'animation-delay', step !== undefined ? `-${step * getDuration()}ms` : `${getDelay()}ms`);\n                setStyleProperty(element, 'animation-fill-mode', getFill() || null);\n                setStyleProperty(element, 'animation-direction', getDirection() || null);\n                const iterationsCount = getIterations() === Infinity ? 'infinite' : getIterations().toString();\n                setStyleProperty(element, 'animation-iteration-count', iterationsCount);\n                if (toggleAnimationName) {\n                    setStyleProperty(element, 'animation-name', `${keyframeName}-alt`);\n                }\n                raf(() => {\n                    setStyleProperty(element, 'animation-name', keyframeName || null);\n                });\n            });\n        });\n    };\n    const update = (deep = false, toggleAnimationName = true, step) => {\n        if (deep) {\n            childAnimations.forEach((animation) => {\n                animation.update(deep, toggleAnimationName, step);\n            });\n        }\n        if (supportsWebAnimations) {\n            updateWebAnimation(step);\n        }\n        else {\n            updateCSSAnimation(toggleAnimationName, step);\n        }\n        return ani;\n    };\n    const progressStart = (forceLinearEasing = false, step) => {\n        childAnimations.forEach((animation) => {\n            animation.progressStart(forceLinearEasing, step);\n        });\n        pauseAnimation();\n        shouldForceLinearEasing = forceLinearEasing;\n        if (!initialized) {\n            initializeAnimation();\n        }\n        update(false, true, step);\n        return ani;\n    };\n    const progressStep = (step) => {\n        childAnimations.forEach((animation) => {\n            animation.progressStep(step);\n        });\n        setAnimationStep(step);\n        return ani;\n    };\n    const progressEnd = (playTo, step, dur) => {\n        shouldForceLinearEasing = false;\n        childAnimations.forEach((animation) => {\n            animation.progressEnd(playTo, step, dur);\n        });\n        if (dur !== undefined) {\n            forceDurationValue = dur;\n        }\n        finished = false;\n        willComplete = true;\n        if (playTo === 0) {\n            forceDirectionValue = getDirection() === 'reverse' ? 'normal' : 'reverse';\n            if (forceDirectionValue === 'reverse') {\n                willComplete = false;\n            }\n            if (supportsWebAnimations) {\n                update();\n                setAnimationStep(1 - step);\n            }\n            else {\n                forceDelayValue = (1 - step) * getDuration() * -1;\n                update(false, false);\n            }\n        }\n        else if (playTo === 1) {\n            if (supportsWebAnimations) {\n                update();\n                setAnimationStep(step);\n            }\n            else {\n                forceDelayValue = step * getDuration() * -1;\n                update(false, false);\n            }\n        }\n        if (playTo !== undefined && !parentAnimation) {\n            play();\n        }\n        return ani;\n    };\n    const pauseAnimation = () => {\n        if (initialized) {\n            if (supportsWebAnimations) {\n                webAnimations.forEach((animation) => {\n                    animation.pause();\n                });\n            }\n            else {\n                elements.forEach((element) => {\n                    setStyleProperty(element, 'animation-play-state', 'paused');\n                });\n            }\n            paused = true;\n        }\n    };\n    const pause = () => {\n        childAnimations.forEach((animation) => {\n            animation.pause();\n        });\n        pauseAnimation();\n        return ani;\n    };\n    const onAnimationEndFallback = () => {\n        cssAnimationsTimerFallback = undefined;\n        animationFinish();\n    };\n    const clearCSSAnimationsTimeout = () => {\n        if (cssAnimationsTimerFallback) {\n            clearTimeout(cssAnimationsTimerFallback);\n        }\n    };\n    const playCSSAnimations = () => {\n        clearCSSAnimationsTimeout();\n        raf(() => {\n            elements.forEach((element) => {\n                if (_keyframes.length > 0) {\n                    setStyleProperty(element, 'animation-play-state', 'running');\n                }\n            });\n        });\n        if (_keyframes.length === 0 || elements.length === 0) {\n            animationFinish();\n        }\n        else {\n            /**\n             * This is a catchall in the event that a CSS Animation did not finish.\n             * The Web Animations API has mechanisms in place for preventing this.\n             * CSS Animations will not fire an `animationend` event\n             * for elements with `display: none`. The Web Animations API\n             * accounts for this, but using raw CSS Animations requires\n             * this workaround.\n             */\n            const animationDelay = getDelay() || 0;\n            const animationDuration = getDuration() || 0;\n            const animationIterations = getIterations() || 1;\n            // No need to set a timeout when animation has infinite iterations\n            if (isFinite(animationIterations)) {\n                cssAnimationsTimerFallback = setTimeout(onAnimationEndFallback, animationDelay + animationDuration * animationIterations + ANIMATION_END_FALLBACK_PADDING_MS);\n            }\n            animationEnd(elements[0], () => {\n                clearCSSAnimationsTimeout();\n                /**\n                 * Ensure that clean up\n                 * is always done a frame\n                 * before the onFinish handlers\n                 * are fired. Otherwise, there\n                 * may be flickering if a new\n                 * animation is started on the same\n                 * element too quickly\n                 */\n                raf(() => {\n                    clearCSSAnimationPlayState();\n                    raf(animationFinish);\n                });\n            });\n        }\n    };\n    const clearCSSAnimationPlayState = () => {\n        elements.forEach((element) => {\n            removeStyleProperty(element, 'animation-duration');\n            removeStyleProperty(element, 'animation-delay');\n            removeStyleProperty(element, 'animation-play-state');\n        });\n    };\n    const playWebAnimations = () => {\n        webAnimations.forEach((animation) => {\n            animation.play();\n        });\n        if (_keyframes.length === 0 || elements.length === 0) {\n            animationFinish();\n        }\n    };\n    const resetAnimation = () => {\n        if (supportsWebAnimations) {\n            setAnimationStep(0);\n            updateWebAnimation();\n        }\n        else {\n            updateCSSAnimation();\n        }\n    };\n    const play = (opts) => {\n        return new Promise((resolve) => {\n            if (opts === null || opts === void 0 ? void 0 : opts.sync) {\n                shouldForceSyncPlayback = true;\n                onFinish(() => (shouldForceSyncPlayback = false), { oneTimeCallback: true });\n            }\n            if (!initialized) {\n                initializeAnimation();\n            }\n            if (finished) {\n                resetAnimation();\n                finished = false;\n            }\n            if (shouldCalculateNumAnimations) {\n                numAnimationsRunning = childAnimations.length + 1;\n                shouldCalculateNumAnimations = false;\n            }\n            /**\n             * When one of these callbacks fires we\n             * need to clear the other's callback otherwise\n             * you can potentially get these callbacks\n             * firing multiple times if the play method\n             * is subsequently called.\n             * Example:\n             * animation.play() (onStop and onFinish callbacks are registered)\n             * animation.stop() (onStop callback is fired, onFinish is not)\n             * animation.play() (onStop and onFinish callbacks are registered)\n             * Total onStop callbacks: 1\n             * Total onFinish callbacks: 2\n             */\n            const onStopCallback = () => {\n                clearCallback(onFinishCallback, onFinishOneTimeCallbacks);\n                resolve();\n            };\n            const onFinishCallback = () => {\n                clearCallback(onStopCallback, onStopOneTimeCallbacks);\n                resolve();\n            };\n            /**\n             * The play method resolves when an animation\n             * run either finishes or is cancelled.\n             */\n            onFinish(onFinishCallback, { oneTimeCallback: true });\n            onStop(onStopCallback, { oneTimeCallback: true });\n            childAnimations.forEach((animation) => {\n                animation.play();\n            });\n            if (supportsWebAnimations) {\n                playWebAnimations();\n            }\n            else {\n                playCSSAnimations();\n            }\n            paused = false;\n        });\n    };\n    /**\n     * Stops an animation and resets it state to the\n     * beginning. This does not fire any onFinish\n     * callbacks because the animation did not finish.\n     * However, since the animation was not destroyed\n     * (i.e. the animation could run again) we do not\n     * clear the onFinish callbacks.\n     */\n    const stop = () => {\n        childAnimations.forEach((animation) => {\n            animation.stop();\n        });\n        if (initialized) {\n            cleanUpElements();\n            initialized = false;\n        }\n        resetFlags();\n        onStopOneTimeCallbacks.forEach((onStopCallback) => onStopCallback.c(0, ani));\n        onStopOneTimeCallbacks.length = 0;\n    };\n    const from = (property, value) => {\n        const firstFrame = _keyframes[0];\n        if (firstFrame !== undefined && (firstFrame.offset === undefined || firstFrame.offset === 0)) {\n            firstFrame[property] = value;\n        }\n        else {\n            _keyframes = [{ offset: 0, [property]: value }, ..._keyframes];\n        }\n        return ani;\n    };\n    const to = (property, value) => {\n        const lastFrame = _keyframes[_keyframes.length - 1];\n        if (lastFrame !== undefined && (lastFrame.offset === undefined || lastFrame.offset === 1)) {\n            lastFrame[property] = value;\n        }\n        else {\n            _keyframes = [..._keyframes, { offset: 1, [property]: value }];\n        }\n        return ani;\n    };\n    const fromTo = (property, fromValue, toValue) => {\n        return from(property, fromValue).to(property, toValue);\n    };\n    return (ani = {\n        parentAnimation,\n        elements,\n        childAnimations,\n        id,\n        animationFinish,\n        from,\n        to,\n        fromTo,\n        parent,\n        play,\n        pause,\n        stop,\n        destroy,\n        keyframes,\n        addAnimation,\n        addElement,\n        update,\n        fill,\n        direction,\n        iterations,\n        duration,\n        easing,\n        delay,\n        getWebAnimations,\n        getKeyframes,\n        getFill,\n        getDirection,\n        getDelay,\n        getIterations,\n        getEasing,\n        getDuration,\n        afterAddRead,\n        afterAddWrite,\n        afterClearStyles,\n        afterStyles,\n        afterRemoveClass,\n        afterAddClass,\n        beforeAddRead,\n        beforeAddWrite,\n        beforeClearStyles,\n        beforeStyles,\n        beforeRemoveClass,\n        beforeAddClass,\n        onFinish,\n        isRunning,\n        progressStart,\n        progressStep,\n        progressEnd,\n    });\n};\n\nexport { createAnimation as c };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,GAAG,QAAQ,aAAa;AACtC,SAASC,CAAC,IAAIC,GAAG,QAAQ,cAAc;AAEvC,IAAIC,eAAe;AACnB;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAIC,SAAS,IAAK;EACpCA,SAAS,CAACC,OAAO,CAAEC,QAAQ,IAAK;IAC5B,KAAK,MAAMC,GAAG,IAAID,QAAQ,EAAE;MACxB;MACA,IAAIA,QAAQ,CAACE,cAAc,CAACD,GAAG,CAAC,EAAE;QAC9B,MAAME,KAAK,GAAGH,QAAQ,CAACC,GAAG,CAAC;QAC3B,IAAIA,GAAG,KAAK,QAAQ,EAAE;UAClB,MAAMG,MAAM,GAAG,2BAA2B;UAC1CJ,QAAQ,CAACI,MAAM,CAAC,GAAGD,KAAK;UACxB,OAAOH,QAAQ,CAACC,GAAG,CAAC;QACxB,CAAC,MACI;UACD,MAAMG,MAAM,GAAGC,uBAAuB,CAACJ,GAAG,CAAC;UAC3C,IAAIG,MAAM,KAAKH,GAAG,EAAE;YAChBD,QAAQ,CAACI,MAAM,CAAC,GAAGD,KAAK;YACxB,OAAOH,QAAQ,CAACC,GAAG,CAAC;UACxB;QACJ;MACJ;IACJ;EACJ,CAAC,CAAC;EACF,OAAOH,SAAS;AACpB,CAAC;AACD,MAAMO,uBAAuB,GAAIC,GAAG,IAAK;EACrC,OAAOA,GAAG,CAACC,OAAO,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAACC,WAAW,CAAC,CAAC;AACnE,CAAC;AACD,MAAMC,kBAAkB,GAAIC,EAAE,IAAK;EAC/B,IAAId,eAAe,KAAKe,SAAS,EAAE;IAC/B,MAAMC,kBAAkB,GAAGF,EAAE,CAACG,KAAK,CAACC,aAAa,KAAKH,SAAS;IAC/D,MAAMI,oBAAoB,GAAGL,EAAE,CAACG,KAAK,CAACG,mBAAmB,KAAKL,SAAS;IACvEf,eAAe,GAAG,CAACgB,kBAAkB,IAAIG,oBAAoB,GAAG,UAAU,GAAG,EAAE;EACnF;EACA,OAAOnB,eAAe;AAC1B,CAAC;AACD,MAAMqB,gBAAgB,GAAGA,CAACC,OAAO,EAAEC,YAAY,EAAEhB,KAAK,KAAK;EACvD,MAAMiB,MAAM,GAAGD,YAAY,CAACE,UAAU,CAAC,WAAW,CAAC,GAAGZ,kBAAkB,CAACS,OAAO,CAAC,GAAG,EAAE;EACtFA,OAAO,CAACL,KAAK,CAACS,WAAW,CAACF,MAAM,GAAGD,YAAY,EAAEhB,KAAK,CAAC;AAC3D,CAAC;AACD,MAAMoB,mBAAmB,GAAGA,CAACL,OAAO,EAAEC,YAAY,KAAK;EACnD,MAAMC,MAAM,GAAGD,YAAY,CAACE,UAAU,CAAC,WAAW,CAAC,GAAGZ,kBAAkB,CAACS,OAAO,CAAC,GAAG,EAAE;EACtFA,OAAO,CAACL,KAAK,CAACW,cAAc,CAACJ,MAAM,GAAGD,YAAY,CAAC;AACvD,CAAC;AACD,MAAMM,YAAY,GAAGA,CAACf,EAAE,EAAEgB,QAAQ,KAAK;EACnC,IAAIC,UAAU;EACd,MAAMC,IAAI,GAAG;IAAEC,OAAO,EAAE;EAAK,CAAC;EAC9B,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACrB,IAAIH,UAAU,EAAE;MACZA,UAAU,CAAC,CAAC;IAChB;EACJ,CAAC;EACD,MAAMI,eAAe,GAAIC,EAAE,IAAK;IAC5B,IAAItB,EAAE,KAAKsB,EAAE,CAACC,MAAM,EAAE;MAClBH,UAAU,CAAC,CAAC;MACZJ,QAAQ,CAACM,EAAE,CAAC;IAChB;EACJ,CAAC;EACD,IAAItB,EAAE,EAAE;IACJA,EAAE,CAACwB,gBAAgB,CAAC,oBAAoB,EAAEH,eAAe,EAAEH,IAAI,CAAC;IAChElB,EAAE,CAACwB,gBAAgB,CAAC,cAAc,EAAEH,eAAe,EAAEH,IAAI,CAAC;IAC1DD,UAAU,GAAGA,CAAA,KAAM;MACfjB,EAAE,CAACyB,mBAAmB,CAAC,oBAAoB,EAAEJ,eAAe,EAAEH,IAAI,CAAC;MACnElB,EAAE,CAACyB,mBAAmB,CAAC,cAAc,EAAEJ,eAAe,EAAEH,IAAI,CAAC;IACjE,CAAC;EACL;EACA,OAAOE,UAAU;AACrB,CAAC;AACD;AACA,MAAMM,qBAAqB,GAAGA,CAACtC,SAAS,GAAG,EAAE,KAAK;EAC9C,OAAOA,SAAS,CACXuC,GAAG,CAAErC,QAAQ,IAAK;IACnB,MAAMsC,MAAM,GAAGtC,QAAQ,CAACsC,MAAM;IAC9B,MAAMC,WAAW,GAAG,EAAE;IACtB,KAAK,MAAMC,QAAQ,IAAIxC,QAAQ,EAAE;MAC7B;MACA,IAAIA,QAAQ,CAACE,cAAc,CAACsC,QAAQ,CAAC,IAAIA,QAAQ,KAAK,QAAQ,EAAE;QAC5DD,WAAW,CAACE,IAAI,CAAC,GAAGD,QAAQ,KAAKxC,QAAQ,CAACwC,QAAQ,CAAC,GAAG,CAAC;MAC3D;IACJ;IACA,OAAO,GAAGF,MAAM,GAAG,GAAG,OAAOC,WAAW,CAACG,IAAI,CAAC,GAAG,CAAC,IAAI;EAC1D,CAAC,CAAC,CACGA,IAAI,CAAC,GAAG,CAAC;AAClB,CAAC;AACD,MAAMC,WAAW,GAAG,EAAE;AACtB,MAAMC,oBAAoB,GAAIC,aAAa,IAAK;EAC5C,IAAIC,KAAK,GAAGH,WAAW,CAACI,OAAO,CAACF,aAAa,CAAC;EAC9C,IAAIC,KAAK,GAAG,CAAC,EAAE;IACXA,KAAK,GAAGH,WAAW,CAACF,IAAI,CAACI,aAAa,CAAC,GAAG,CAAC;EAC/C;EACA,OAAO,iBAAiBC,KAAK,EAAE;AACnC,CAAC;AACD,MAAME,iBAAiB,GAAI9B,OAAO,IAAK;EACnC;EACA;EACA,MAAM+B,QAAQ,GAAG/B,OAAO,CAACgC,WAAW,KAAKvC,SAAS,GAAGO,OAAO,CAACgC,WAAW,CAAC,CAAC,GAAGhC,OAAO;EACpF,OAAO+B,QAAQ,CAACE,IAAI,IAAIF,QAAQ;AACpC,CAAC;AACD,MAAMG,wBAAwB,GAAGA,CAACC,YAAY,EAAER,aAAa,EAAE3B,OAAO,KAAK;EACvE,IAAIoC,EAAE;EACN,MAAMC,cAAc,GAAGP,iBAAiB,CAAC9B,OAAO,CAAC;EACjD,MAAMsC,cAAc,GAAG/C,kBAAkB,CAACS,OAAO,CAAC;EAClD,MAAMuC,kBAAkB,GAAGF,cAAc,CAACG,aAAa,CAAC,GAAG,GAAGL,YAAY,CAAC;EAC3E,IAAII,kBAAkB,EAAE;IACpB,OAAOA,kBAAkB;EAC7B;EACA,MAAME,UAAU,GAAG,CAAC,CAACL,EAAE,GAAGpC,OAAO,CAAC0C,aAAa,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGO,QAAQ,EAAEC,aAAa,CAAC,OAAO,CAAC;EAClHH,UAAU,CAACI,EAAE,GAAGV,YAAY;EAC5BM,UAAU,CAACK,WAAW,GAAG,IAAIR,cAAc,aAAaH,YAAY,MAAMR,aAAa,OAAOW,cAAc,aAAaH,YAAY,UAAUR,aAAa,IAAI;EAChKU,cAAc,CAACU,WAAW,CAACN,UAAU,CAAC;EACtC,OAAOA,UAAU;AACrB,CAAC;AACD,MAAMO,eAAe,GAAGA,CAACC,OAAO,GAAG,EAAE,EAAEC,SAAS,KAAK;EACjD,IAAIA,SAAS,KAAKzD,SAAS,EAAE;IACzB,MAAM0D,iBAAiB,GAAGC,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;IAC5E,OAAO,CAAC,GAAGD,OAAO,EAAE,GAAGE,iBAAiB,CAAC;EAC7C;EACA,OAAOF,OAAO;AAClB,CAAC;AAED,MAAMK,eAAe,GAAIC,WAAW,IAAK;EACrC,IAAIC,MAAM;EACV,IAAIC,SAAS;EACb,IAAIC,OAAO;EACX,IAAIC,WAAW;EACf,IAAIC,KAAK;EACT,IAAIC,UAAU;EACd,IAAIC,UAAU,GAAG,EAAE;EACnB,IAAIC,gBAAgB,GAAG,EAAE;EACzB,IAAIC,mBAAmB,GAAG,EAAE;EAC5B,IAAIC,WAAW,GAAG,KAAK;EACvB,IAAIC,eAAe;EACnB,IAAIC,iBAAiB,GAAG,CAAC,CAAC;EAC1B,IAAIC,eAAe,GAAG,EAAE;EACxB,IAAIC,kBAAkB,GAAG,EAAE;EAC3B,IAAIC,gBAAgB,GAAG,CAAC,CAAC;EACzB,IAAIC,oBAAoB,GAAG,CAAC;EAC5B,IAAIC,uBAAuB,GAAG,KAAK;EACnC,IAAIC,uBAAuB,GAAG,KAAK;EACnC,IAAIC,0BAA0B;EAC9B,IAAIC,mBAAmB;EACvB,IAAIC,kBAAkB;EACtB,IAAIC,eAAe;EACnB,IAAIC,YAAY,GAAG,IAAI;EACvB,IAAIC,QAAQ,GAAG,KAAK;EACpB,IAAIC,4BAA4B,GAAG,IAAI;EACvC,IAAI7C,YAAY;EAChB,IAAI8C,GAAG;EACP,IAAIC,MAAM,GAAG,KAAK;EAClB,MAAMrC,EAAE,GAAGU,WAAW;EACtB,MAAM4B,iBAAiB,GAAG,EAAE;EAC5B,MAAMC,wBAAwB,GAAG,EAAE;EACnC,MAAMC,sBAAsB,GAAG,EAAE;EACjC,MAAMC,QAAQ,GAAG,EAAE;EACnB,MAAMC,eAAe,GAAG,EAAE;EAC1B,MAAMC,WAAW,GAAG,EAAE;EACtB,MAAMC,uBAAuB,GAAG,EAAE;EAClC,MAAMC,wBAAwB,GAAG,EAAE;EACnC,MAAMC,sBAAsB,GAAG,EAAE;EACjC,MAAMC,uBAAuB,GAAG,EAAE;EAClC,MAAMC,aAAa,GAAG,EAAE;EACxB,MAAMC,uBAAuB,GAAG,OAAOC,eAAe,KAAK,UAAU,IAChExH,GAAG,KAAKkB,SAAS,IAAI,OAAOlB,GAAG,CAACwH,eAAe,KAAK,UAAW;EACpE,MAAMC,qBAAqB,GAAG,OAAOC,OAAO,KAAK,UAAU,IACvD,OAAOA,OAAO,CAACC,SAAS,CAACC,OAAO,KAAK,UAAU,IAC/CL,uBAAuB;EAC3B,MAAMM,iCAAiC,GAAG,GAAG;EAC7C,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,OAAOR,aAAa;EACxB,CAAC;EACD,MAAMS,OAAO,GAAIC,gBAAgB,IAAK;IAClChB,eAAe,CAAC1G,OAAO,CAAE2H,cAAc,IAAK;MACxCA,cAAc,CAACF,OAAO,CAACC,gBAAgB,CAAC;IAC5C,CAAC,CAAC;IACFE,OAAO,CAACF,gBAAgB,CAAC;IACzBjB,QAAQ,CAACoB,MAAM,GAAG,CAAC;IACnBnB,eAAe,CAACmB,MAAM,GAAG,CAAC;IAC1B5C,UAAU,CAAC4C,MAAM,GAAG,CAAC;IACrBC,aAAa,CAAC,CAAC;IACf1C,WAAW,GAAG,KAAK;IACnBe,4BAA4B,GAAG,IAAI;IACnC,OAAOC,GAAG;EACd,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMwB,OAAO,GAAIF,gBAAgB,IAAK;IAClCK,eAAe,CAAC,CAAC;IACjB,IAAIL,gBAAgB,EAAE;MAClBM,kBAAkB,CAAC,CAAC;IACxB;EACJ,CAAC;EACD,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACrBtC,uBAAuB,GAAG,KAAK;IAC/BC,uBAAuB,GAAG,KAAK;IAC/BO,4BAA4B,GAAG,IAAI;IACnCL,mBAAmB,GAAGlF,SAAS;IAC/BmF,kBAAkB,GAAGnF,SAAS;IAC9BoF,eAAe,GAAGpF,SAAS;IAC3B8E,oBAAoB,GAAG,CAAC;IACxBQ,QAAQ,GAAG,KAAK;IAChBD,YAAY,GAAG,IAAI;IACnBI,MAAM,GAAG,KAAK;EAClB,CAAC;EACD,MAAM6B,SAAS,GAAGA,CAAA,KAAM;IACpB,OAAOxC,oBAAoB,KAAK,CAAC,IAAI,CAACW,MAAM;EAChD,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACI,MAAM8B,aAAa,GAAGA,CAACC,gBAAgB,EAAEC,eAAe,KAAK;IACzD,MAAMtF,KAAK,GAAGsF,eAAe,CAACC,SAAS,CAAEC,cAAc,IAAKA,cAAc,CAACC,CAAC,KAAKJ,gBAAgB,CAAC;IAClG,IAAIrF,KAAK,GAAG,CAAC,CAAC,EAAE;MACZsF,eAAe,CAACI,MAAM,CAAC1F,KAAK,EAAE,CAAC,CAAC;IACpC;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACI,MAAM2F,MAAM,GAAGA,CAAC/G,QAAQ,EAAEE,IAAI,KAAK;IAC/B2E,sBAAsB,CAAC9D,IAAI,CAAC;MAAE8F,CAAC,EAAE7G,QAAQ;MAAEgH,CAAC,EAAE9G;IAAK,CAAC,CAAC;IACrD,OAAOuE,GAAG;EACd,CAAC;EACD,MAAMwC,QAAQ,GAAGA,CAACjH,QAAQ,EAAEE,IAAI,KAAK;IACjC,MAAMgH,SAAS,GAAG,CAAChH,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACiH,eAAe,IAAIvC,wBAAwB,GAAGD,iBAAiB;IACnIuC,SAAS,CAACnG,IAAI,CAAC;MAAE8F,CAAC,EAAE7G,QAAQ;MAAEgH,CAAC,EAAE9G;IAAK,CAAC,CAAC;IACxC,OAAOuE,GAAG;EACd,CAAC;EACD,MAAM0B,aAAa,GAAGA,CAAA,KAAM;IACxBxB,iBAAiB,CAACuB,MAAM,GAAG,CAAC;IAC5BtB,wBAAwB,CAACsB,MAAM,GAAG,CAAC;IACnC,OAAOzB,GAAG;EACd,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI,MAAM2B,eAAe,GAAGA,CAAA,KAAM;IAC1B,IAAIZ,qBAAqB,EAAE;MACvBH,aAAa,CAAChH,OAAO,CAAE+I,SAAS,IAAK;QACjCA,SAAS,CAACC,MAAM,CAAC,CAAC;MACtB,CAAC,CAAC;MACFhC,aAAa,CAACa,MAAM,GAAG,CAAC;IAC5B,CAAC,MACI;MACD,MAAMoB,aAAa,GAAGxC,QAAQ,CAACyC,KAAK,CAAC,CAAC;MACtCtJ,GAAG,CAAC,MAAM;QACNqJ,aAAa,CAACjJ,OAAO,CAAEmB,OAAO,IAAK;UAC/BK,mBAAmB,CAACL,OAAO,EAAE,gBAAgB,CAAC;UAC9CK,mBAAmB,CAACL,OAAO,EAAE,oBAAoB,CAAC;UAClDK,mBAAmB,CAACL,OAAO,EAAE,2BAA2B,CAAC;UACzDK,mBAAmB,CAACL,OAAO,EAAE,2BAA2B,CAAC;UACzDK,mBAAmB,CAACL,OAAO,EAAE,iBAAiB,CAAC;UAC/CK,mBAAmB,CAACL,OAAO,EAAE,sBAAsB,CAAC;UACpDK,mBAAmB,CAACL,OAAO,EAAE,qBAAqB,CAAC;UACnDK,mBAAmB,CAACL,OAAO,EAAE,qBAAqB,CAAC;QACvD,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ,CAAC;EACD;AACJ;AACA;AACA;EACI,MAAM6G,kBAAkB,GAAGA,CAAA,KAAM;IAC7BrB,WAAW,CAAC3G,OAAO,CAAE4D,UAAU,IAAK;MAChC;AACZ;AACA;AACA;AACA;MACY,IAAIA,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACuF,UAAU,EAAE;QAC/EvF,UAAU,CAACuF,UAAU,CAACC,WAAW,CAACxF,UAAU,CAAC;MACjD;IACJ,CAAC,CAAC;IACF+C,WAAW,CAACkB,MAAM,GAAG,CAAC;EAC1B,CAAC;EACD,MAAMwB,aAAa,GAAIC,MAAM,IAAK;IAC9B1C,uBAAuB,CAAClE,IAAI,CAAC4G,MAAM,CAAC;IACpC,OAAOlD,GAAG;EACd,CAAC;EACD,MAAMmD,cAAc,GAAIC,OAAO,IAAK;IAChC3C,wBAAwB,CAACnE,IAAI,CAAC8G,OAAO,CAAC;IACtC,OAAOpD,GAAG;EACd,CAAC;EACD,MAAMqD,YAAY,GAAIH,MAAM,IAAK;IAC7BxC,sBAAsB,CAACpE,IAAI,CAAC4G,MAAM,CAAC;IACnC,OAAOlD,GAAG;EACd,CAAC;EACD,MAAMsD,aAAa,GAAIF,OAAO,IAAK;IAC/BzC,uBAAuB,CAACrE,IAAI,CAAC8G,OAAO,CAAC;IACrC,OAAOpD,GAAG;EACd,CAAC;EACD,MAAMuD,cAAc,GAAItF,SAAS,IAAK;IAClCa,gBAAgB,GAAGf,eAAe,CAACe,gBAAgB,EAAEb,SAAS,CAAC;IAC/D,OAAO+B,GAAG;EACd,CAAC;EACD,MAAMwD,iBAAiB,GAAIvF,SAAS,IAAK;IACrCc,mBAAmB,GAAGhB,eAAe,CAACgB,mBAAmB,EAAEd,SAAS,CAAC;IACrE,OAAO+B,GAAG;EACd,CAAC;EACD;AACJ;AACA;AACA;EACI,MAAMyD,YAAY,GAAGA,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK;IAClCxE,iBAAiB,GAAGwE,MAAM;IAC1B,OAAO1D,GAAG;EACd,CAAC;EACD;AACJ;AACA;AACA;EACI,MAAM2D,iBAAiB,GAAGA,CAACC,aAAa,GAAG,EAAE,KAAK;IAC9C,KAAK,MAAMvH,QAAQ,IAAIuH,aAAa,EAAE;MAClC1E,iBAAiB,CAAC7C,QAAQ,CAAC,GAAG,EAAE;IACpC;IACA,OAAO2D,GAAG;EACd,CAAC;EACD,MAAM6D,aAAa,GAAI5F,SAAS,IAAK;IACjCkB,eAAe,GAAGpB,eAAe,CAACoB,eAAe,EAAElB,SAAS,CAAC;IAC7D,OAAO+B,GAAG;EACd,CAAC;EACD,MAAM8D,gBAAgB,GAAI7F,SAAS,IAAK;IACpCmB,kBAAkB,GAAGrB,eAAe,CAACqB,kBAAkB,EAAEnB,SAAS,CAAC;IACnE,OAAO+B,GAAG;EACd,CAAC;EACD,MAAM+D,WAAW,GAAGA,CAACL,MAAM,GAAG,CAAC,CAAC,KAAK;IACjCrE,gBAAgB,GAAGqE,MAAM;IACzB,OAAO1D,GAAG;EACd,CAAC;EACD,MAAMgE,gBAAgB,GAAGA,CAACJ,aAAa,GAAG,EAAE,KAAK;IAC7C,KAAK,MAAMvH,QAAQ,IAAIuH,aAAa,EAAE;MAClCvE,gBAAgB,CAAChD,QAAQ,CAAC,GAAG,EAAE;IACnC;IACA,OAAO2D,GAAG;EACd,CAAC;EACD,MAAMiE,OAAO,GAAGA,CAAA,KAAM;IAClB,IAAItF,KAAK,KAAKnE,SAAS,EAAE;MACrB,OAAOmE,KAAK;IAChB;IACA,IAAIM,eAAe,EAAE;MACjB,OAAOA,eAAe,CAACgF,OAAO,CAAC,CAAC;IACpC;IACA,OAAO,MAAM;EACjB,CAAC;EACD,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACvB,IAAIxE,mBAAmB,KAAKlF,SAAS,EAAE;MACnC,OAAOkF,mBAAmB;IAC9B;IACA,IAAId,UAAU,KAAKpE,SAAS,EAAE;MAC1B,OAAOoE,UAAU;IACrB;IACA,IAAIK,eAAe,EAAE;MACjB,OAAOA,eAAe,CAACiF,YAAY,CAAC,CAAC;IACzC;IACA,OAAO,QAAQ;EACnB,CAAC;EACD,MAAMC,SAAS,GAAGA,CAAA,KAAM;IACpB,IAAI5E,uBAAuB,EAAE;MACzB,OAAO,QAAQ;IACnB;IACA,IAAId,OAAO,KAAKjE,SAAS,EAAE;MACvB,OAAOiE,OAAO;IAClB;IACA,IAAIQ,eAAe,EAAE;MACjB,OAAOA,eAAe,CAACkF,SAAS,CAAC,CAAC;IACtC;IACA,OAAO,QAAQ;EACnB,CAAC;EACD,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACtB,IAAI5E,uBAAuB,EAAE;MACzB,OAAO,CAAC;IACZ;IACA,IAAIG,kBAAkB,KAAKnF,SAAS,EAAE;MAClC,OAAOmF,kBAAkB;IAC7B;IACA,IAAInB,SAAS,KAAKhE,SAAS,EAAE;MACzB,OAAOgE,SAAS;IACpB;IACA,IAAIS,eAAe,EAAE;MACjB,OAAOA,eAAe,CAACmF,WAAW,CAAC,CAAC;IACxC;IACA,OAAO,CAAC;EACZ,CAAC;EACD,MAAMC,aAAa,GAAGA,CAAA,KAAM;IACxB,IAAI3F,WAAW,KAAKlE,SAAS,EAAE;MAC3B,OAAOkE,WAAW;IACtB;IACA,IAAIO,eAAe,EAAE;MACjB,OAAOA,eAAe,CAACoF,aAAa,CAAC,CAAC;IAC1C;IACA,OAAO,CAAC;EACZ,CAAC;EACD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACnB,IAAI1E,eAAe,KAAKpF,SAAS,EAAE;MAC/B,OAAOoF,eAAe;IAC1B;IACA,IAAIrB,MAAM,KAAK/D,SAAS,EAAE;MACtB,OAAO+D,MAAM;IACjB;IACA,IAAIU,eAAe,EAAE;MACjB,OAAOA,eAAe,CAACqF,QAAQ,CAAC,CAAC;IACrC;IACA,OAAO,CAAC;EACZ,CAAC;EACD,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACvB,OAAO1F,UAAU;EACrB,CAAC;EACD,MAAM2F,SAAS,GAAIC,kBAAkB,IAAK;IACtC7F,UAAU,GAAG6F,kBAAkB;IAC/BC,MAAM,CAAC,IAAI,CAAC;IACZ,OAAO1E,GAAG;EACd,CAAC;EACD,MAAM2E,IAAI,GAAIC,aAAa,IAAK;IAC5BjG,KAAK,GAAGiG,aAAa;IACrBF,MAAM,CAAC,IAAI,CAAC;IACZ,OAAO1E,GAAG;EACd,CAAC;EACD,MAAM6E,KAAK,GAAIC,cAAc,IAAK;IAC9BvG,MAAM,GAAGuG,cAAc;IACvBJ,MAAM,CAAC,IAAI,CAAC;IACZ,OAAO1E,GAAG;EACd,CAAC;EACD,MAAM+E,MAAM,GAAIC,eAAe,IAAK;IAChCvG,OAAO,GAAGuG,eAAe;IACzBN,MAAM,CAAC,IAAI,CAAC;IACZ,OAAO1E,GAAG;EACd,CAAC;EACD,MAAMiF,QAAQ,GAAIC,iBAAiB,IAAK;IACpC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACnE,qBAAqB,IAAImE,iBAAiB,KAAK,CAAC,EAAE;MACnDA,iBAAiB,GAAG,CAAC;IACzB;IACA1G,SAAS,GAAG0G,iBAAiB;IAC7BR,MAAM,CAAC,IAAI,CAAC;IACZ,OAAO1E,GAAG;EACd,CAAC;EACD,MAAMmF,UAAU,GAAIC,mBAAmB,IAAK;IACxC1G,WAAW,GAAG0G,mBAAmB;IACjCV,MAAM,CAAC,IAAI,CAAC;IACZ,OAAO1E,GAAG;EACd,CAAC;EACD,MAAMqF,MAAM,GAAI1C,SAAS,IAAK;IAC1B1D,eAAe,GAAG0D,SAAS;IAC3B,OAAO3C,GAAG;EACd,CAAC;EACD,MAAMsF,UAAU,GAAI/K,EAAE,IAAK;IACvB,IAAIA,EAAE,IAAI,IAAI,EAAE;MACZ,IAAIA,EAAE,CAACgL,QAAQ,KAAK,CAAC,EAAE;QACnBlF,QAAQ,CAAC/D,IAAI,CAAC/B,EAAE,CAAC;MACrB,CAAC,MACI,IAAIA,EAAE,CAACkH,MAAM,IAAI,CAAC,EAAE;QACrB,KAAK,IAAI+D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjL,EAAE,CAACkH,MAAM,EAAE+D,CAAC,EAAE,EAAE;UAChCnF,QAAQ,CAAC/D,IAAI,CAAC/B,EAAE,CAACiL,CAAC,CAAC,CAAC;QACxB;MACJ,CAAC,MACI;QACDC,OAAO,CAACC,KAAK,CAAC,0BAA0B,CAAC;MAC7C;IACJ;IACA,OAAO1F,GAAG;EACd,CAAC;EACD,MAAM2F,YAAY,GAAIC,cAAc,IAAK;IACrC,IAAIA,cAAc,IAAI,IAAI,EAAE;MACxB,IAAIzH,KAAK,CAACC,OAAO,CAACwH,cAAc,CAAC,EAAE;QAC/B,KAAK,MAAMjD,SAAS,IAAIiD,cAAc,EAAE;UACpCjD,SAAS,CAAC0C,MAAM,CAACrF,GAAG,CAAC;UACrBM,eAAe,CAAChE,IAAI,CAACqG,SAAS,CAAC;QACnC;MACJ,CAAC,MACI;QACDiD,cAAc,CAACP,MAAM,CAACrF,GAAG,CAAC;QAC1BM,eAAe,CAAChE,IAAI,CAACsJ,cAAc,CAAC;MACxC;IACJ;IACA,OAAO5F,GAAG;EACd,CAAC;EACD,MAAMrG,SAAS,GAAIkM,cAAc,IAAK;IAClC,MAAMC,SAAS,GAAGjH,UAAU,KAAKgH,cAAc;IAC/ChH,UAAU,GAAGgH,cAAc;IAC3B,IAAIC,SAAS,EAAE;MACXC,eAAe,CAAClH,UAAU,CAAC;IAC/B;IACA,OAAOmB,GAAG;EACd,CAAC;EACD,MAAM+F,eAAe,GAAIF,cAAc,IAAK;IACxC,IAAI9E,qBAAqB,EAAE;MACvBK,gBAAgB,CAAC,CAAC,CAACxH,OAAO,CAAE+I,SAAS,IAAK;QACtC;AAChB;AACA;AACA;AACA;AACA;AACA;QACgB,MAAMqD,cAAc,GAAGrD,SAAS,CAACsD,MAAM;QACvC;AAChB;AACA;AACA;AACA;QACgB;QACA,IAAID,cAAc,CAACE,YAAY,EAAE;UAC7BF,cAAc,CAACE,YAAY,CAACL,cAAc,CAAC;QAC/C,CAAC,MACI;UACD,MAAMM,SAAS,GAAG,IAAIC,cAAc,CAACJ,cAAc,CAAClK,MAAM,EAAE+J,cAAc,EAAEG,cAAc,CAACK,SAAS,CAAC,CAAC,CAAC;UACvG1D,SAAS,CAACsD,MAAM,GAAGE,SAAS;QAChC;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACDG,sBAAsB,CAAC,CAAC;IAC5B;EACJ,CAAC;EACD;AACJ;AACA;EACI,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC1B;IACA/F,uBAAuB,CAAC5G,OAAO,CAAE2B,QAAQ,IAAKA,QAAQ,CAAC,CAAC,CAAC;IACzD;IACAkF,wBAAwB,CAAC7G,OAAO,CAAE2B,QAAQ,IAAKA,QAAQ,CAAC,CAAC,CAAC;IAC1D;IACA,MAAMiL,UAAU,GAAG1H,gBAAgB;IACnC,MAAM2H,aAAa,GAAG1H,mBAAmB;IACzC,MAAM2E,MAAM,GAAGxE,iBAAiB;IAChCmB,QAAQ,CAACzG,OAAO,CAAEW,EAAE,IAAK;MACrB,MAAMmM,gBAAgB,GAAGnM,EAAE,CAACoM,SAAS;MACrCH,UAAU,CAAC5M,OAAO,CAAEwI,CAAC,IAAKsE,gBAAgB,CAACE,GAAG,CAACxE,CAAC,CAAC,CAAC;MAClDqE,aAAa,CAAC7M,OAAO,CAAEwI,CAAC,IAAKsE,gBAAgB,CAACG,MAAM,CAACzE,CAAC,CAAC,CAAC;MACxD,KAAK,MAAM/F,QAAQ,IAAIqH,MAAM,EAAE;QAC3B;QACA,IAAIA,MAAM,CAAC3J,cAAc,CAACsC,QAAQ,CAAC,EAAE;UACjCvB,gBAAgB,CAACP,EAAE,EAAE8B,QAAQ,EAAEqH,MAAM,CAACrH,QAAQ,CAAC,CAAC;QACpD;MACJ;IACJ,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;EACI,MAAMyK,cAAc,GAAGA,CAAA,KAAM;IACzBC,yBAAyB,CAAC,CAAC;IAC3B;IACArG,sBAAsB,CAAC9G,OAAO,CAAE2B,QAAQ,IAAKA,QAAQ,CAAC,CAAC,CAAC;IACxD;IACAoF,uBAAuB,CAAC/G,OAAO,CAAE2B,QAAQ,IAAKA,QAAQ,CAAC,CAAC,CAAC;IACzD;IACA,MAAMyL,WAAW,GAAGnH,YAAY,GAAG,CAAC,GAAG,CAAC;IACxC,MAAM2G,UAAU,GAAGrH,eAAe;IAClC,MAAMsH,aAAa,GAAGrH,kBAAkB;IACxC,MAAMsE,MAAM,GAAGrE,gBAAgB;IAC/BgB,QAAQ,CAACzG,OAAO,CAAEW,EAAE,IAAK;MACrB,MAAMmM,gBAAgB,GAAGnM,EAAE,CAACoM,SAAS;MACrCH,UAAU,CAAC5M,OAAO,CAAEwI,CAAC,IAAKsE,gBAAgB,CAACE,GAAG,CAACxE,CAAC,CAAC,CAAC;MAClDqE,aAAa,CAAC7M,OAAO,CAAEwI,CAAC,IAAKsE,gBAAgB,CAACG,MAAM,CAACzE,CAAC,CAAC,CAAC;MACxD,KAAK,MAAM/F,QAAQ,IAAIqH,MAAM,EAAE;QAC3B;QACA,IAAIA,MAAM,CAAC3J,cAAc,CAACsC,QAAQ,CAAC,EAAE;UACjCvB,gBAAgB,CAACP,EAAE,EAAE8B,QAAQ,EAAEqH,MAAM,CAACrH,QAAQ,CAAC,CAAC;QACpD;MACJ;IACJ,CAAC,CAAC;IACF;AACR;AACA;AACA;AACA;AACA;AACA;IACQsD,kBAAkB,GAAGnF,SAAS;IAC9BkF,mBAAmB,GAAGlF,SAAS;IAC/BoF,eAAe,GAAGpF,SAAS;IAC3B0F,iBAAiB,CAACtG,OAAO,CAAEqN,gBAAgB,IAAK;MAC5C,OAAOA,gBAAgB,CAAC7E,CAAC,CAAC4E,WAAW,EAAEhH,GAAG,CAAC;IAC/C,CAAC,CAAC;IACFG,wBAAwB,CAACvG,OAAO,CAAEqN,gBAAgB,IAAK;MACnD,OAAOA,gBAAgB,CAAC7E,CAAC,CAAC4E,WAAW,EAAEhH,GAAG,CAAC;IAC/C,CAAC,CAAC;IACFG,wBAAwB,CAACsB,MAAM,GAAG,CAAC;IACnC1B,4BAA4B,GAAG,IAAI;IACnC,IAAIF,YAAY,EAAE;MACdC,QAAQ,GAAG,IAAI;IACnB;IACAD,YAAY,GAAG,IAAI;EACvB,CAAC;EACD,MAAMqH,eAAe,GAAGA,CAAA,KAAM;IAC1B,IAAI5H,oBAAoB,KAAK,CAAC,EAAE;MAC5B;IACJ;IACAA,oBAAoB,EAAE;IACtB,IAAIA,oBAAoB,KAAK,CAAC,EAAE;MAC5BwH,cAAc,CAAC,CAAC;MAChB,IAAI7H,eAAe,EAAE;QACjBA,eAAe,CAACiI,eAAe,CAAC,CAAC;MACrC;IACJ;EACJ,CAAC;EACD,MAAMZ,sBAAsB,GAAGA,CAACa,mBAAmB,GAAG,IAAI,KAAK;IAC3DvF,kBAAkB,CAAC,CAAC;IACpB,MAAMwF,kBAAkB,GAAG1N,gBAAgB,CAACmF,UAAU,CAAC;IACvDwB,QAAQ,CAACzG,OAAO,CAAEmB,OAAO,IAAK;MAC1B,IAAIqM,kBAAkB,CAAC3F,MAAM,GAAG,CAAC,EAAE;QAC/B,MAAM/E,aAAa,GAAGT,qBAAqB,CAACmL,kBAAkB,CAAC;QAC/DlK,YAAY,GAAGoB,WAAW,KAAK9D,SAAS,GAAG8D,WAAW,GAAG7B,oBAAoB,CAACC,aAAa,CAAC;QAC5F,MAAMc,UAAU,GAAGP,wBAAwB,CAACC,YAAY,EAAER,aAAa,EAAE3B,OAAO,CAAC;QACjFwF,WAAW,CAACjE,IAAI,CAACkB,UAAU,CAAC;QAC5B1C,gBAAgB,CAACC,OAAO,EAAE,oBAAoB,EAAE,GAAGqJ,WAAW,CAAC,CAAC,IAAI,CAAC;QACrEtJ,gBAAgB,CAACC,OAAO,EAAE,2BAA2B,EAAEoJ,SAAS,CAAC,CAAC,CAAC;QACnErJ,gBAAgB,CAACC,OAAO,EAAE,iBAAiB,EAAE,GAAGuJ,QAAQ,CAAC,CAAC,IAAI,CAAC;QAC/DxJ,gBAAgB,CAACC,OAAO,EAAE,qBAAqB,EAAEkJ,OAAO,CAAC,CAAC,CAAC;QAC3DnJ,gBAAgB,CAACC,OAAO,EAAE,qBAAqB,EAAEmJ,YAAY,CAAC,CAAC,CAAC;QAChE,MAAMmD,eAAe,GAAGhD,aAAa,CAAC,CAAC,KAAKiD,QAAQ,GAAG,UAAU,GAAGjD,aAAa,CAAC,CAAC,CAACkD,QAAQ,CAAC,CAAC;QAC9FzM,gBAAgB,CAACC,OAAO,EAAE,2BAA2B,EAAEsM,eAAe,CAAC;QACvEvM,gBAAgB,CAACC,OAAO,EAAE,sBAAsB,EAAE,QAAQ,CAAC;QAC3D,IAAIoM,mBAAmB,EAAE;UACrBrM,gBAAgB,CAACC,OAAO,EAAE,gBAAgB,EAAE,GAAGyC,UAAU,CAACI,EAAE,MAAM,CAAC;QACvE;QACApE,GAAG,CAAC,MAAM;UACNsB,gBAAgB,CAACC,OAAO,EAAE,gBAAgB,EAAEyC,UAAU,CAACI,EAAE,IAAI,IAAI,CAAC;QACtE,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN,CAAC;EACD,MAAM4J,sBAAsB,GAAGA,CAAA,KAAM;IACjCnH,QAAQ,CAACzG,OAAO,CAAEmB,OAAO,IAAK;MAC1B,MAAM4H,SAAS,GAAG5H,OAAO,CAACmG,OAAO,CAACrC,UAAU,EAAE;QAC1CjB,EAAE;QACFiH,KAAK,EAAEP,QAAQ,CAAC,CAAC;QACjBW,QAAQ,EAAEb,WAAW,CAAC,CAAC;QACvBW,MAAM,EAAEZ,SAAS,CAAC,CAAC;QACnBgB,UAAU,EAAEd,aAAa,CAAC,CAAC;QAC3BM,IAAI,EAAEV,OAAO,CAAC,CAAC;QACfO,SAAS,EAAEN,YAAY,CAAC;MAC5B,CAAC,CAAC;MACFvB,SAAS,CAAC8E,KAAK,CAAC,CAAC;MACjB7G,aAAa,CAACtE,IAAI,CAACqG,SAAS,CAAC;IACjC,CAAC,CAAC;IACF,IAAI/B,aAAa,CAACa,MAAM,GAAG,CAAC,EAAE;MAC1Bb,aAAa,CAAC,CAAC,CAAC,CAAC8G,QAAQ,GAAG,MAAM;QAC9BR,eAAe,CAAC,CAAC;MACrB,CAAC;IACL;EACJ,CAAC;EACD,MAAMS,mBAAmB,GAAGA,CAACR,mBAAmB,GAAG,IAAI,KAAK;IACxDZ,eAAe,CAAC,CAAC;IACjB,IAAI1H,UAAU,CAAC4C,MAAM,GAAG,CAAC,EAAE;MACvB,IAAIV,qBAAqB,EAAE;QACvByG,sBAAsB,CAAC,CAAC;MAC5B,CAAC,MACI;QACDlB,sBAAsB,CAACa,mBAAmB,CAAC;MAC/C;IACJ;IACAnI,WAAW,GAAG,IAAI;EACtB,CAAC;EACD,MAAM4I,gBAAgB,GAAIC,IAAI,IAAK;IAC/BA,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACH,IAAI,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;IAC1C,IAAI9G,qBAAqB,EAAE;MACvBH,aAAa,CAAChH,OAAO,CAAE+I,SAAS,IAAK;QACjC;QACAA,SAAS,CAACsF,WAAW,GAAGtF,SAAS,CAACsD,MAAM,CAACiC,iBAAiB,CAAC,CAAC,CAACrD,KAAK,GAAGT,WAAW,CAAC,CAAC,GAAGyD,IAAI;QACzFlF,SAAS,CAAC8E,KAAK,CAAC,CAAC;MACrB,CAAC,CAAC;IACN,CAAC,MACI;MACD,MAAMvC,iBAAiB,GAAG,IAAId,WAAW,CAAC,CAAC,GAAGyD,IAAI,IAAI;MACtDxH,QAAQ,CAACzG,OAAO,CAAEmB,OAAO,IAAK;QAC1B,IAAI8D,UAAU,CAAC4C,MAAM,GAAG,CAAC,EAAE;UACvB3G,gBAAgB,CAACC,OAAO,EAAE,iBAAiB,EAAEmK,iBAAiB,CAAC;UAC/DpK,gBAAgB,CAACC,OAAO,EAAE,sBAAsB,EAAE,QAAQ,CAAC;QAC/D;MACJ,CAAC,CAAC;IACN;EACJ,CAAC;EACD,MAAMoN,kBAAkB,GAAIN,IAAI,IAAK;IACjCjH,aAAa,CAAChH,OAAO,CAAE+I,SAAS,IAAK;MACjCA,SAAS,CAACsD,MAAM,CAACmC,YAAY,CAAC;QAC1BvD,KAAK,EAAEP,QAAQ,CAAC,CAAC;QACjBW,QAAQ,EAAEb,WAAW,CAAC,CAAC;QACvBW,MAAM,EAAEZ,SAAS,CAAC,CAAC;QACnBgB,UAAU,EAAEd,aAAa,CAAC,CAAC;QAC3BM,IAAI,EAAEV,OAAO,CAAC,CAAC;QACfO,SAAS,EAAEN,YAAY,CAAC;MAC5B,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAI2D,IAAI,KAAKrN,SAAS,EAAE;MACpBoN,gBAAgB,CAACC,IAAI,CAAC;IAC1B;EACJ,CAAC;EACD,MAAMQ,kBAAkB,GAAGA,CAAClB,mBAAmB,GAAG,IAAI,EAAEU,IAAI,KAAK;IAC7DrO,GAAG,CAAC,MAAM;MACN6G,QAAQ,CAACzG,OAAO,CAAEmB,OAAO,IAAK;QAC1BD,gBAAgB,CAACC,OAAO,EAAE,gBAAgB,EAAEmC,YAAY,IAAI,IAAI,CAAC;QACjEpC,gBAAgB,CAACC,OAAO,EAAE,oBAAoB,EAAE,GAAGqJ,WAAW,CAAC,CAAC,IAAI,CAAC;QACrEtJ,gBAAgB,CAACC,OAAO,EAAE,2BAA2B,EAAEoJ,SAAS,CAAC,CAAC,CAAC;QACnErJ,gBAAgB,CAACC,OAAO,EAAE,iBAAiB,EAAE8M,IAAI,KAAKrN,SAAS,GAAG,IAAIqN,IAAI,GAAGzD,WAAW,CAAC,CAAC,IAAI,GAAG,GAAGE,QAAQ,CAAC,CAAC,IAAI,CAAC;QACnHxJ,gBAAgB,CAACC,OAAO,EAAE,qBAAqB,EAAEkJ,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC;QACnEnJ,gBAAgB,CAACC,OAAO,EAAE,qBAAqB,EAAEmJ,YAAY,CAAC,CAAC,IAAI,IAAI,CAAC;QACxE,MAAMmD,eAAe,GAAGhD,aAAa,CAAC,CAAC,KAAKiD,QAAQ,GAAG,UAAU,GAAGjD,aAAa,CAAC,CAAC,CAACkD,QAAQ,CAAC,CAAC;QAC9FzM,gBAAgB,CAACC,OAAO,EAAE,2BAA2B,EAAEsM,eAAe,CAAC;QACvE,IAAIF,mBAAmB,EAAE;UACrBrM,gBAAgB,CAACC,OAAO,EAAE,gBAAgB,EAAE,GAAGmC,YAAY,MAAM,CAAC;QACtE;QACA1D,GAAG,CAAC,MAAM;UACNsB,gBAAgB,CAACC,OAAO,EAAE,gBAAgB,EAAEmC,YAAY,IAAI,IAAI,CAAC;QACrE,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACD,MAAMwH,MAAM,GAAGA,CAAC4D,IAAI,GAAG,KAAK,EAAEnB,mBAAmB,GAAG,IAAI,EAAEU,IAAI,KAAK;IAC/D,IAAIS,IAAI,EAAE;MACNhI,eAAe,CAAC1G,OAAO,CAAE+I,SAAS,IAAK;QACnCA,SAAS,CAAC+B,MAAM,CAAC4D,IAAI,EAAEnB,mBAAmB,EAAEU,IAAI,CAAC;MACrD,CAAC,CAAC;IACN;IACA,IAAI9G,qBAAqB,EAAE;MACvBoH,kBAAkB,CAACN,IAAI,CAAC;IAC5B,CAAC,MACI;MACDQ,kBAAkB,CAAClB,mBAAmB,EAAEU,IAAI,CAAC;IACjD;IACA,OAAO7H,GAAG;EACd,CAAC;EACD,MAAMuI,aAAa,GAAGA,CAACC,iBAAiB,GAAG,KAAK,EAAEX,IAAI,KAAK;IACvDvH,eAAe,CAAC1G,OAAO,CAAE+I,SAAS,IAAK;MACnCA,SAAS,CAAC4F,aAAa,CAACC,iBAAiB,EAAEX,IAAI,CAAC;IACpD,CAAC,CAAC;IACFY,cAAc,CAAC,CAAC;IAChBlJ,uBAAuB,GAAGiJ,iBAAiB;IAC3C,IAAI,CAACxJ,WAAW,EAAE;MACd2I,mBAAmB,CAAC,CAAC;IACzB;IACAjD,MAAM,CAAC,KAAK,EAAE,IAAI,EAAEmD,IAAI,CAAC;IACzB,OAAO7H,GAAG;EACd,CAAC;EACD,MAAM0I,YAAY,GAAIb,IAAI,IAAK;IAC3BvH,eAAe,CAAC1G,OAAO,CAAE+I,SAAS,IAAK;MACnCA,SAAS,CAAC+F,YAAY,CAACb,IAAI,CAAC;IAChC,CAAC,CAAC;IACFD,gBAAgB,CAACC,IAAI,CAAC;IACtB,OAAO7H,GAAG;EACd,CAAC;EACD,MAAM2I,WAAW,GAAGA,CAACC,MAAM,EAAEf,IAAI,EAAEgB,GAAG,KAAK;IACvCtJ,uBAAuB,GAAG,KAAK;IAC/Be,eAAe,CAAC1G,OAAO,CAAE+I,SAAS,IAAK;MACnCA,SAAS,CAACgG,WAAW,CAACC,MAAM,EAAEf,IAAI,EAAEgB,GAAG,CAAC;IAC5C,CAAC,CAAC;IACF,IAAIA,GAAG,KAAKrO,SAAS,EAAE;MACnBmF,kBAAkB,GAAGkJ,GAAG;IAC5B;IACA/I,QAAQ,GAAG,KAAK;IAChBD,YAAY,GAAG,IAAI;IACnB,IAAI+I,MAAM,KAAK,CAAC,EAAE;MACdlJ,mBAAmB,GAAGwE,YAAY,CAAC,CAAC,KAAK,SAAS,GAAG,QAAQ,GAAG,SAAS;MACzE,IAAIxE,mBAAmB,KAAK,SAAS,EAAE;QACnCG,YAAY,GAAG,KAAK;MACxB;MACA,IAAIkB,qBAAqB,EAAE;QACvB2D,MAAM,CAAC,CAAC;QACRkD,gBAAgB,CAAC,CAAC,GAAGC,IAAI,CAAC;MAC9B,CAAC,MACI;QACDjI,eAAe,GAAG,CAAC,CAAC,GAAGiI,IAAI,IAAIzD,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;QACjDM,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC;MACxB;IACJ,CAAC,MACI,IAAIkE,MAAM,KAAK,CAAC,EAAE;MACnB,IAAI7H,qBAAqB,EAAE;QACvB2D,MAAM,CAAC,CAAC;QACRkD,gBAAgB,CAACC,IAAI,CAAC;MAC1B,CAAC,MACI;QACDjI,eAAe,GAAGiI,IAAI,GAAGzD,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3CM,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC;MACxB;IACJ;IACA,IAAIkE,MAAM,KAAKpO,SAAS,IAAI,CAACyE,eAAe,EAAE;MAC1C6J,IAAI,CAAC,CAAC;IACV;IACA,OAAO9I,GAAG;EACd,CAAC;EACD,MAAMyI,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAIzJ,WAAW,EAAE;MACb,IAAI+B,qBAAqB,EAAE;QACvBH,aAAa,CAAChH,OAAO,CAAE+I,SAAS,IAAK;UACjCA,SAAS,CAAC8E,KAAK,CAAC,CAAC;QACrB,CAAC,CAAC;MACN,CAAC,MACI;QACDpH,QAAQ,CAACzG,OAAO,CAAEmB,OAAO,IAAK;UAC1BD,gBAAgB,CAACC,OAAO,EAAE,sBAAsB,EAAE,QAAQ,CAAC;QAC/D,CAAC,CAAC;MACN;MACAkF,MAAM,GAAG,IAAI;IACjB;EACJ,CAAC;EACD,MAAMwH,KAAK,GAAGA,CAAA,KAAM;IAChBnH,eAAe,CAAC1G,OAAO,CAAE+I,SAAS,IAAK;MACnCA,SAAS,CAAC8E,KAAK,CAAC,CAAC;IACrB,CAAC,CAAC;IACFgB,cAAc,CAAC,CAAC;IAChB,OAAOzI,GAAG;EACd,CAAC;EACD,MAAM+I,sBAAsB,GAAGA,CAAA,KAAM;IACjCtJ,0BAA0B,GAAGjF,SAAS;IACtC0M,eAAe,CAAC,CAAC;EACrB,CAAC;EACD,MAAMH,yBAAyB,GAAGA,CAAA,KAAM;IACpC,IAAItH,0BAA0B,EAAE;MAC5BuJ,YAAY,CAACvJ,0BAA0B,CAAC;IAC5C;EACJ,CAAC;EACD,MAAMwJ,iBAAiB,GAAGA,CAAA,KAAM;IAC5BlC,yBAAyB,CAAC,CAAC;IAC3BvN,GAAG,CAAC,MAAM;MACN6G,QAAQ,CAACzG,OAAO,CAAEmB,OAAO,IAAK;QAC1B,IAAI8D,UAAU,CAAC4C,MAAM,GAAG,CAAC,EAAE;UACvB3G,gBAAgB,CAACC,OAAO,EAAE,sBAAsB,EAAE,SAAS,CAAC;QAChE;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAI8D,UAAU,CAAC4C,MAAM,KAAK,CAAC,IAAIpB,QAAQ,CAACoB,MAAM,KAAK,CAAC,EAAE;MAClDyF,eAAe,CAAC,CAAC;IACrB,CAAC,MACI;MACD;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAMpC,cAAc,GAAGR,QAAQ,CAAC,CAAC,IAAI,CAAC;MACtC,MAAMY,iBAAiB,GAAGd,WAAW,CAAC,CAAC,IAAI,CAAC;MAC5C,MAAMgB,mBAAmB,GAAGf,aAAa,CAAC,CAAC,IAAI,CAAC;MAChD;MACA,IAAI6E,QAAQ,CAAC9D,mBAAmB,CAAC,EAAE;QAC/B3F,0BAA0B,GAAG0J,UAAU,CAACJ,sBAAsB,EAAEjE,cAAc,GAAGI,iBAAiB,GAAGE,mBAAmB,GAAGjE,iCAAiC,CAAC;MACjK;MACA7F,YAAY,CAAC+E,QAAQ,CAAC,CAAC,CAAC,EAAE,MAAM;QAC5B0G,yBAAyB,CAAC,CAAC;QAC3B;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACgBvN,GAAG,CAAC,MAAM;UACN4P,0BAA0B,CAAC,CAAC;UAC5B5P,GAAG,CAAC0N,eAAe,CAAC;QACxB,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ,CAAC;EACD,MAAMkC,0BAA0B,GAAGA,CAAA,KAAM;IACrC/I,QAAQ,CAACzG,OAAO,CAAEmB,OAAO,IAAK;MAC1BK,mBAAmB,CAACL,OAAO,EAAE,oBAAoB,CAAC;MAClDK,mBAAmB,CAACL,OAAO,EAAE,iBAAiB,CAAC;MAC/CK,mBAAmB,CAACL,OAAO,EAAE,sBAAsB,CAAC;IACxD,CAAC,CAAC;EACN,CAAC;EACD,MAAMsO,iBAAiB,GAAGA,CAAA,KAAM;IAC5BzI,aAAa,CAAChH,OAAO,CAAE+I,SAAS,IAAK;MACjCA,SAAS,CAACmG,IAAI,CAAC,CAAC;IACpB,CAAC,CAAC;IACF,IAAIjK,UAAU,CAAC4C,MAAM,KAAK,CAAC,IAAIpB,QAAQ,CAACoB,MAAM,KAAK,CAAC,EAAE;MAClDyF,eAAe,CAAC,CAAC;IACrB;EACJ,CAAC;EACD,MAAMoC,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAIvI,qBAAqB,EAAE;MACvB6G,gBAAgB,CAAC,CAAC,CAAC;MACnBO,kBAAkB,CAAC,CAAC;IACxB,CAAC,MACI;MACDE,kBAAkB,CAAC,CAAC;IACxB;EACJ,CAAC;EACD,MAAMS,IAAI,GAAIrN,IAAI,IAAK;IACnB,OAAO,IAAI8N,OAAO,CAAEC,OAAO,IAAK;MAC5B,IAAI/N,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACgO,IAAI,EAAE;QACvDjK,uBAAuB,GAAG,IAAI;QAC9BgD,QAAQ,CAAC,MAAOhD,uBAAuB,GAAG,KAAM,EAAE;UAAEkD,eAAe,EAAE;QAAK,CAAC,CAAC;MAChF;MACA,IAAI,CAAC1D,WAAW,EAAE;QACd2I,mBAAmB,CAAC,CAAC;MACzB;MACA,IAAI7H,QAAQ,EAAE;QACVwJ,cAAc,CAAC,CAAC;QAChBxJ,QAAQ,GAAG,KAAK;MACpB;MACA,IAAIC,4BAA4B,EAAE;QAC9BT,oBAAoB,GAAGgB,eAAe,CAACmB,MAAM,GAAG,CAAC;QACjD1B,4BAA4B,GAAG,KAAK;MACxC;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAM2J,cAAc,GAAGA,CAAA,KAAM;QACzB3H,aAAa,CAACkF,gBAAgB,EAAE9G,wBAAwB,CAAC;QACzDqJ,OAAO,CAAC,CAAC;MACb,CAAC;MACD,MAAMvC,gBAAgB,GAAGA,CAAA,KAAM;QAC3BlF,aAAa,CAAC2H,cAAc,EAAEtJ,sBAAsB,CAAC;QACrDoJ,OAAO,CAAC,CAAC;MACb,CAAC;MACD;AACZ;AACA;AACA;MACYhH,QAAQ,CAACyE,gBAAgB,EAAE;QAAEvE,eAAe,EAAE;MAAK,CAAC,CAAC;MACrDJ,MAAM,CAACoH,cAAc,EAAE;QAAEhH,eAAe,EAAE;MAAK,CAAC,CAAC;MACjDpC,eAAe,CAAC1G,OAAO,CAAE+I,SAAS,IAAK;QACnCA,SAAS,CAACmG,IAAI,CAAC,CAAC;MACpB,CAAC,CAAC;MACF,IAAI/H,qBAAqB,EAAE;QACvBsI,iBAAiB,CAAC,CAAC;MACvB,CAAC,MACI;QACDJ,iBAAiB,CAAC,CAAC;MACvB;MACAhJ,MAAM,GAAG,KAAK;IAClB,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAM0J,IAAI,GAAGA,CAAA,KAAM;IACfrJ,eAAe,CAAC1G,OAAO,CAAE+I,SAAS,IAAK;MACnCA,SAAS,CAACgH,IAAI,CAAC,CAAC;IACpB,CAAC,CAAC;IACF,IAAI3K,WAAW,EAAE;MACb2C,eAAe,CAAC,CAAC;MACjB3C,WAAW,GAAG,KAAK;IACvB;IACA6C,UAAU,CAAC,CAAC;IACZzB,sBAAsB,CAACxG,OAAO,CAAE8P,cAAc,IAAKA,cAAc,CAACtH,CAAC,CAAC,CAAC,EAAEpC,GAAG,CAAC,CAAC;IAC5EI,sBAAsB,CAACqB,MAAM,GAAG,CAAC;EACrC,CAAC;EACD,MAAMmI,IAAI,GAAGA,CAACvN,QAAQ,EAAErC,KAAK,KAAK;IAC9B,MAAM6P,UAAU,GAAGhL,UAAU,CAAC,CAAC,CAAC;IAChC,IAAIgL,UAAU,KAAKrP,SAAS,KAAKqP,UAAU,CAAC1N,MAAM,KAAK3B,SAAS,IAAIqP,UAAU,CAAC1N,MAAM,KAAK,CAAC,CAAC,EAAE;MAC1F0N,UAAU,CAACxN,QAAQ,CAAC,GAAGrC,KAAK;IAChC,CAAC,MACI;MACD6E,UAAU,GAAG,CAAC;QAAE1C,MAAM,EAAE,CAAC;QAAE,CAACE,QAAQ,GAAGrC;MAAM,CAAC,EAAE,GAAG6E,UAAU,CAAC;IAClE;IACA,OAAOmB,GAAG;EACd,CAAC;EACD,MAAM8J,EAAE,GAAGA,CAACzN,QAAQ,EAAErC,KAAK,KAAK;IAC5B,MAAM+P,SAAS,GAAGlL,UAAU,CAACA,UAAU,CAAC4C,MAAM,GAAG,CAAC,CAAC;IACnD,IAAIsI,SAAS,KAAKvP,SAAS,KAAKuP,SAAS,CAAC5N,MAAM,KAAK3B,SAAS,IAAIuP,SAAS,CAAC5N,MAAM,KAAK,CAAC,CAAC,EAAE;MACvF4N,SAAS,CAAC1N,QAAQ,CAAC,GAAGrC,KAAK;IAC/B,CAAC,MACI;MACD6E,UAAU,GAAG,CAAC,GAAGA,UAAU,EAAE;QAAE1C,MAAM,EAAE,CAAC;QAAE,CAACE,QAAQ,GAAGrC;MAAM,CAAC,CAAC;IAClE;IACA,OAAOgG,GAAG;EACd,CAAC;EACD,MAAMgK,MAAM,GAAGA,CAAC3N,QAAQ,EAAE4N,SAAS,EAAEC,OAAO,KAAK;IAC7C,OAAON,IAAI,CAACvN,QAAQ,EAAE4N,SAAS,CAAC,CAACH,EAAE,CAACzN,QAAQ,EAAE6N,OAAO,CAAC;EAC1D,CAAC;EACD,OAAQlK,GAAG,GAAG;IACVf,eAAe;IACfoB,QAAQ;IACRC,eAAe;IACf1C,EAAE;IACFsJ,eAAe;IACf0C,IAAI;IACJE,EAAE;IACFE,MAAM;IACN3E,MAAM;IACNyD,IAAI;IACJrB,KAAK;IACLkC,IAAI;IACJtI,OAAO;IACP1H,SAAS;IACTgM,YAAY;IACZL,UAAU;IACVZ,MAAM;IACNC,IAAI;IACJH,SAAS;IACTW,UAAU;IACVF,QAAQ;IACRF,MAAM;IACNF,KAAK;IACLzD,gBAAgB;IAChBmD,YAAY;IACZN,OAAO;IACPC,YAAY;IACZI,QAAQ;IACRD,aAAa;IACbF,SAAS;IACTC,WAAW;IACXf,YAAY;IACZC,aAAa;IACbU,gBAAgB;IAChBD,WAAW;IACXD,gBAAgB;IAChBD,aAAa;IACbZ,aAAa;IACbE,cAAc;IACdQ,iBAAiB;IACjBF,YAAY;IACZD,iBAAiB;IACjBD,cAAc;IACdf,QAAQ;IACRV,SAAS;IACTyG,aAAa;IACbG,YAAY;IACZC;EACJ,CAAC;AACL,CAAC;AAED,SAAStK,eAAe,IAAI+D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}