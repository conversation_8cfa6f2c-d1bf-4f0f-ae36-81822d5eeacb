{"name": "dfashion-frontend", "version": "1.0.0", "description": "DFashion Social E-commerce Frontend", "scripts": {"ng": "ng", "start": "ng serve --host 0.0.0.0 --port 4200", "build": "ng build", "watch": "ng build --watch --configuration development", "serve": "ng serve", "ionic:build": "ng build", "ionic:serve": "ng serve", "build:mobile": "ng build --configuration production", "cap:add": "npx cap add", "cap:copy": "npx cap copy", "cap:sync": "npx cap sync", "cap:open": "npx cap open", "cap:run": "npx cap run", "cap:build": "npm run build:mobile && npx cap copy && npx cap sync"}, "dependencies": {"@angular/animations": "^17.0.0", "@angular/cdk": "^17.0.0", "@angular/common": "^17.0.0", "@angular/compiler": "^17.0.0", "@angular/core": "^17.0.0", "@angular/forms": "^17.0.0", "@angular/material": "^17.0.0", "@angular/platform-browser": "^17.0.0", "@angular/platform-browser-dynamic": "^17.0.0", "@angular/router": "^17.0.0", "@capacitor/action-sheet": "^5.0.7", "@capacitor/android": "^5.7.8", "@capacitor/angular": "^2.0.3", "@capacitor/app": "^5.0.0", "@capacitor/browser": "^5.2.1", "@capacitor/camera": "^7.0.1", "@capacitor/clipboard": "^5.0.8", "@capacitor/core": "^5.7.8", "@capacitor/device": "^7.0.1", "@capacitor/filesystem": "^7.1.1", "@capacitor/geolocation": "^7.1.2", "@capacitor/haptics": "^5.0.0", "@capacitor/ios": "^5.7.8", "@capacitor/keyboard": "^5.0.0", "@capacitor/local-notifications": "^7.0.1", "@capacitor/network": "^7.0.1", "@capacitor/preferences": "^7.0.1", "@capacitor/push-notifications": "^7.0.1", "@capacitor/screen-reader": "^5.0.9", "@capacitor/share": "^7.0.1", "@capacitor/status-bar": "^5.0.0", "@capacitor/toast": "^7.0.1", "@ionic/angular": "^7.0.0", "@ionic/pwa-elements": "^3.3.0", "@ionic/storage-angular": "^4.0.0", "add": "^2.0.6", "bootstrap": "^5.3.0", "ionicons": "^7.0.0", "ngx-infinite-scroll": "^17.0.0", "ngx-owl-carousel-o": "^18.0.0", "rxjs": "~7.8.0", "socket.io-client": "^4.8.1", "swiper": "^11.2.8", "tslib": "^2.3.0", "zone.js": "~0.14.0"}, "devDependencies": {"@angular-devkit/build-angular": "^17.3.17", "@angular/cli": "^17.0.0", "@angular/compiler-cli": "^17.0.0", "@capacitor/cli": "^5.7.8", "@ionic/angular-toolkit": "^9.0.0", "@types/node": "^18.18.0", "css-loader": "^7.1.2", "style-loader": "^4.0.0", "typescript": "~5.2.0", "webpack": "^5.99.9"}}