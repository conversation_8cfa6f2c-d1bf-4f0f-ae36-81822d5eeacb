{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { BehaviorSubject } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let AdminProductService = /*#__PURE__*/(() => {\n  class AdminProductService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = `${environment.apiUrl}/admin`;\n      this.productsSubject = new BehaviorSubject([]);\n      this.products$ = this.productsSubject.asObservable();\n    }\n    // Get all products with filters (Admin API)\n    getProducts(filters = {}) {\n      let params = new HttpParams();\n      Object.keys(filters).forEach(key => {\n        const value = filters[key];\n        if (value !== undefined && value !== null && value !== '') {\n          params = params.set(key, value.toString());\n        }\n      });\n      return this.http.get(`${this.apiUrl}/products`, {\n        params\n      });\n    }\n    // Get product by ID (Admin API)\n    getProductById(id) {\n      return this.http.get(`${this.apiUrl}/products/${id}`);\n    }\n    // Update product status (Admin API)\n    updateProductStatus(id, isActive) {\n      return this.http.put(`${this.apiUrl}/products/${id}/status`, {\n        isActive\n      });\n    }\n    // Toggle featured status (Admin API)\n    updateFeaturedStatus(id, isFeatured) {\n      return this.http.put(`${this.apiUrl}/products/${id}/featured`, {\n        isFeatured\n      });\n    }\n    // Delete product (Admin API)\n    deleteProduct(id) {\n      return this.http.delete(`${this.apiUrl}/products/${id}`);\n    }\n    // Approve product (for vendor products)\n    approveProduct(id) {\n      return this.http.patch(`${this.apiUrl}/${id}/approve`, {});\n    }\n    // Get product categories\n    getCategories() {\n      return this.http.get(`${this.apiUrl}/categories`);\n    }\n    // Get subcategories by category\n    getSubcategories(category) {\n      return this.http.get(`${this.apiUrl}/categories/${category}/subcategories`);\n    }\n    // Get product brands\n    getBrands() {\n      return this.http.get(`${this.apiUrl}/brands`);\n    }\n    // Upload product images\n    uploadProductImages(productId, files) {\n      const formData = new FormData();\n      files.forEach(file => {\n        formData.append('images', file);\n      });\n      return this.http.post(`${this.apiUrl}/${productId}/images`, formData);\n    }\n    // Delete product image\n    deleteProductImage(productId, imageId) {\n      return this.http.delete(`${this.apiUrl}/${productId}/images/${imageId}`);\n    }\n    // Update product inventory\n    updateInventory(productId, inventory) {\n      return this.http.patch(`${this.apiUrl}/${productId}/inventory`, inventory);\n    }\n    // Get product analytics\n    getProductAnalytics(productId, period = '30d') {\n      return this.http.get(`${this.apiUrl}/${productId}/analytics?period=${period}`);\n    }\n    // Bulk operations\n    bulkUpdateProducts(productIds, updates) {\n      return this.http.patch(`${this.apiUrl}/bulk-update`, {\n        productIds,\n        updates\n      });\n    }\n    bulkDeleteProducts(productIds) {\n      return this.http.delete(`${this.apiUrl}/bulk-delete`, {\n        body: {\n          productIds\n        }\n      });\n    }\n    // Search products\n    searchProducts(query, filters = {}) {\n      const searchFilters = {\n        ...filters,\n        search: query\n      };\n      return this.getProducts(searchFilters);\n    }\n    // Get featured products\n    getFeaturedProducts(limit = 10) {\n      return this.http.get(`${this.apiUrl}/featured?limit=${limit}`);\n    }\n    // Get products by vendor\n    getProductsByVendor(vendorId, filters = {}) {\n      const vendorFilters = {\n        ...filters,\n        vendor: vendorId\n      };\n      return this.getProducts(vendorFilters);\n    }\n    // Update products subject\n    updateProductsSubject(products) {\n      this.productsSubject.next(products);\n    }\n    // Get current products\n    getCurrentProducts() {\n      return this.productsSubject.value;\n    }\n    static {\n      this.ɵfac = function AdminProductService_Factory(t) {\n        return new (t || AdminProductService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AdminProductService,\n        factory: AdminProductService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AdminProductService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}