{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"../services/admin-api.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/select\";\nimport * as i10 from \"@angular/material/core\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nimport * as i12 from \"@angular/material/slide-toggle\";\nfunction UserDialogComponent_mat_error_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Full name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserDialogComponent_mat_error_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Email is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserDialogComponent_mat_error_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please enter a valid email \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserDialogComponent_mat_error_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Username is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserDialogComponent_div_23_mat_error_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserDialogComponent_div_23_mat_error_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password must be at least 6 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserDialogComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"mat-form-field\", 4)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 19);\n    i0.ɵɵtemplate(5, UserDialogComponent_div_23_mat_error_5_Template, 2, 0, \"mat-error\", 6)(6, UserDialogComponent_div_23_mat_error_6_Template, 2, 0, \"mat-error\", 6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r0.userForm.get(\"password\")) == null ? null : tmp_1_0.hasError(\"required\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.userForm.get(\"password\")) == null ? null : tmp_2_0.hasError(\"minlength\"));\n  }\n}\nfunction UserDialogComponent_mat_option_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const role_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", role_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", role_r2.label, \" \");\n  }\n}\nfunction UserDialogComponent_mat_error_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Role is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserDialogComponent_mat_option_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dept_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", dept_r3.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", dept_r3.label, \" \");\n  }\n}\nfunction UserDialogComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"mat-form-field\", 4)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Employee ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 21);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserDialogComponent_mat_spinner_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 22);\n  }\n}\nexport let UserDialogComponent = /*#__PURE__*/(() => {\n  class UserDialogComponent {\n    constructor(fb, dialogRef, data, apiService, snackBar) {\n      this.fb = fb;\n      this.dialogRef = dialogRef;\n      this.data = data;\n      this.apiService = apiService;\n      this.snackBar = snackBar;\n      this.isEditMode = false;\n      this.isLoading = false;\n      this.roles = [{\n        value: 'super_admin',\n        label: 'Super Admin'\n      }, {\n        value: 'admin',\n        label: 'Admin'\n      }, {\n        value: 'sales_manager',\n        label: 'Sales Manager'\n      }, {\n        value: 'marketing_manager',\n        label: 'Marketing Manager'\n      }, {\n        value: 'account_manager',\n        label: 'Account Manager'\n      }, {\n        value: 'support_manager',\n        label: 'Support Manager'\n      }, {\n        value: 'sales_executive',\n        label: 'Sales Executive'\n      }, {\n        value: 'marketing_executive',\n        label: 'Marketing Executive'\n      }, {\n        value: 'account_executive',\n        label: 'Account Executive'\n      }, {\n        value: 'support_executive',\n        label: 'Support Executive'\n      }];\n      this.departments = [{\n        value: 'administration',\n        label: 'Administration'\n      }, {\n        value: 'sales',\n        label: 'Sales'\n      }, {\n        value: 'marketing',\n        label: 'Marketing'\n      }, {\n        value: 'accounting',\n        label: 'Accounting'\n      }, {\n        value: 'support',\n        label: 'Support'\n      }];\n      this.isEditMode = !!data;\n      this.userForm = this.createForm();\n    }\n    ngOnInit() {\n      if (this.isEditMode && this.data) {\n        this.userForm.patchValue(this.data);\n      }\n    }\n    createForm() {\n      const formConfig = {\n        fullName: ['', [Validators.required]],\n        email: ['', [Validators.required, Validators.email]],\n        username: ['', [Validators.required]],\n        role: ['', [Validators.required]],\n        department: [''],\n        employeeId: [''],\n        isActive: [true]\n      };\n      if (!this.isEditMode) {\n        formConfig.password = ['', [Validators.required, Validators.minLength(6)]];\n      }\n      return this.fb.group(formConfig);\n    }\n    get showEmployeeFields() {\n      const role = this.userForm.get('role')?.value;\n      return role && role !== 'customer' && role !== 'vendor';\n    }\n    onSave() {\n      if (this.userForm.valid) {\n        this.isLoading = true;\n        const formData = this.userForm.value;\n        const apiCall = this.isEditMode ? this.apiService.updateUser(this.data._id, formData) : this.apiService.createUser(formData);\n        apiCall.subscribe({\n          next: response => {\n            this.isLoading = false;\n            this.snackBar.open(`User ${this.isEditMode ? 'updated' : 'created'} successfully`, 'Close', {\n              duration: 3000,\n              panelClass: ['success-snackbar']\n            });\n            this.dialogRef.close(true);\n          },\n          error: error => {\n            this.isLoading = false;\n            const errorMessage = error.error?.message || `Failed to ${this.isEditMode ? 'update' : 'create'} user`;\n            this.snackBar.open(errorMessage, 'Close', {\n              duration: 5000,\n              panelClass: ['error-snackbar']\n            });\n          }\n        });\n      }\n    }\n    onCancel() {\n      this.dialogRef.close(false);\n    }\n    static {\n      this.ɵfac = function UserDialogComponent_Factory(t) {\n        return new (t || UserDialogComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i3.AdminApiService), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: UserDialogComponent,\n        selectors: [[\"app-user-dialog\"]],\n        decls: 46,\n        vars: 14,\n        consts: [[1, \"user-dialog\"], [\"mat-dialog-title\", \"\"], [1, \"user-form\", 3, \"formGroup\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"fullName\", \"placeholder\", \"Enter full name\"], [4, \"ngIf\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter email\"], [\"matInput\", \"\", \"formControlName\", \"username\", \"placeholder\", \"Enter username\"], [\"class\", \"form-row\", 4, \"ngIf\"], [\"formControlName\", \"role\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"department\"], [\"formControlName\", \"isActive\", \"color\", \"primary\"], [\"align\", \"end\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", \"disabled\"], [\"diameter\", \"20\", \"class\", \"save-spinner\", 4, \"ngIf\"], [\"matInput\", \"\", \"type\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Enter password\"], [3, \"value\"], [\"matInput\", \"\", \"formControlName\", \"employeeId\", \"placeholder\", \"Enter employee ID\"], [\"diameter\", \"20\", 1, \"save-spinner\"]],\n        template: function UserDialogComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\", 1);\n            i0.ɵɵtext(2);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(3, \"mat-dialog-content\")(4, \"form\", 2)(5, \"div\", 3)(6, \"mat-form-field\", 4)(7, \"mat-label\");\n            i0.ɵɵtext(8, \"Full Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(9, \"input\", 5);\n            i0.ɵɵtemplate(10, UserDialogComponent_mat_error_10_Template, 2, 0, \"mat-error\", 6);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(11, \"div\", 3)(12, \"mat-form-field\", 7)(13, \"mat-label\");\n            i0.ɵɵtext(14, \"Email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(15, \"input\", 8);\n            i0.ɵɵtemplate(16, UserDialogComponent_mat_error_16_Template, 2, 0, \"mat-error\", 6)(17, UserDialogComponent_mat_error_17_Template, 2, 0, \"mat-error\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"mat-form-field\", 7)(19, \"mat-label\");\n            i0.ɵɵtext(20, \"Username\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(21, \"input\", 9);\n            i0.ɵɵtemplate(22, UserDialogComponent_mat_error_22_Template, 2, 0, \"mat-error\", 6);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(23, UserDialogComponent_div_23_Template, 7, 2, \"div\", 10);\n            i0.ɵɵelementStart(24, \"div\", 3)(25, \"mat-form-field\", 7)(26, \"mat-label\");\n            i0.ɵɵtext(27, \"Role\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"mat-select\", 11);\n            i0.ɵɵtemplate(29, UserDialogComponent_mat_option_29_Template, 2, 2, \"mat-option\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(30, UserDialogComponent_mat_error_30_Template, 2, 0, \"mat-error\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"mat-form-field\", 7)(32, \"mat-label\");\n            i0.ɵɵtext(33, \"Department\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"mat-select\", 13);\n            i0.ɵɵtemplate(35, UserDialogComponent_mat_option_35_Template, 2, 2, \"mat-option\", 12);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(36, UserDialogComponent_div_36_Template, 5, 0, \"div\", 10);\n            i0.ɵɵelementStart(37, \"div\", 3)(38, \"mat-slide-toggle\", 14);\n            i0.ɵɵtext(39, \" Active User \");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(40, \"mat-dialog-actions\", 15)(41, \"button\", 16);\n            i0.ɵɵlistener(\"click\", function UserDialogComponent_Template_button_click_41_listener() {\n              return ctx.onCancel();\n            });\n            i0.ɵɵtext(42, \"Cancel\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function UserDialogComponent_Template_button_click_43_listener() {\n              return ctx.onSave();\n            });\n            i0.ɵɵtemplate(44, UserDialogComponent_mat_spinner_44_Template, 1, 0, \"mat-spinner\", 18);\n            i0.ɵɵtext(45);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            let tmp_2_0;\n            let tmp_3_0;\n            let tmp_4_0;\n            let tmp_5_0;\n            let tmp_8_0;\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate(ctx.isEditMode ? \"Edit User\" : \"Add New User\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"formGroup\", ctx.userForm);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx.userForm.get(\"fullName\")) == null ? null : tmp_2_0.hasError(\"required\"));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx.userForm.get(\"email\")) == null ? null : tmp_3_0.hasError(\"required\"));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx.userForm.get(\"email\")) == null ? null : tmp_4_0.hasError(\"email\"));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx.userForm.get(\"username\")) == null ? null : tmp_5_0.hasError(\"required\"));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngForOf\", ctx.roles);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", (tmp_8_0 = ctx.userForm.get(\"role\")) == null ? null : tmp_8_0.hasError(\"required\"));\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngForOf\", ctx.departments);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showEmployeeFields);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"disabled\", ctx.userForm.invalid || ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Update\" : \"Create\", \" \");\n          }\n        },\n        dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.MatButton, i7.MatInput, i8.MatFormField, i8.MatLabel, i8.MatError, i9.MatSelect, i10.MatOption, i2.MatDialogTitle, i2.MatDialogActions, i2.MatDialogContent, i11.MatProgressSpinner, i12.MatSlideToggle],\n        styles: [\".user-dialog[_ngcontent-%COMP%]{min-width:500px}.user-form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:1rem;margin:1rem 0}.form-row[_ngcontent-%COMP%]{display:flex;gap:1rem}.full-width[_ngcontent-%COMP%]{width:100%}.half-width[_ngcontent-%COMP%]{flex:1}.save-spinner[_ngcontent-%COMP%]{margin-right:.5rem}  .save-spinner circle{stroke:#fff}@media (max-width: 600px){.user-dialog[_ngcontent-%COMP%]{min-width:auto;width:100%}.form-row[_ngcontent-%COMP%]{flex-direction:column}.half-width[_ngcontent-%COMP%]{width:100%}}\"]\n      });\n    }\n  }\n  return UserDialogComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}