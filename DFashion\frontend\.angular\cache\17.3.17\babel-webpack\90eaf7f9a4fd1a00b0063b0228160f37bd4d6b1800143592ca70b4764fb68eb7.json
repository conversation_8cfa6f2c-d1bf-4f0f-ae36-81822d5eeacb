{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3, 4];\nfunction SuggestedForYouComponent_div_8_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵelement(1, \"div\", 14);\n    i0.ɵɵelementStart(2, \"div\", 15);\n    i0.ɵɵelement(3, \"div\", 16)(4, \"div\", 17)(5, \"div\", 18);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SuggestedForYouComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11);\n    i0.ɵɵtemplate(2, SuggestedForYouComponent_div_8_div_2_Template, 6, 0, \"div\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction SuggestedForYouComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵelement(1, \"ion-icon\", 20);\n    i0.ɵɵelementStart(2, \"p\", 21);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_9_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 23);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction SuggestedForYouComponent_div_10_div_7_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵelement(1, \"ion-icon\", 45);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuggestedForYouComponent_div_10_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_10_div_7_Template_div_click_0_listener() {\n      const user_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onUserClick(user_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 33);\n    i0.ɵɵelement(2, \"img\", 34);\n    i0.ɵɵtemplate(3, SuggestedForYouComponent_div_10_div_7_div_3_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 36)(5, \"h3\", 37);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 38);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 39);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\", 40);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\", 41);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_10_div_7_Template_button_click_15_listener($event) {\n      const user_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onFollowUser(user_r5, $event));\n    });\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"ion-icon\", 43);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", user_r5.avatar, i0.ɵɵsanitizeUrl)(\"alt\", user_r5.fullName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", user_r5.isInfluencer);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r5.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"@\", user_r5.username, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatFollowerCount(user_r5.followerCount), \" followers\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r5.category);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r5.followedBy);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"following\", user_r5.isFollowing);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r5.isFollowing ? \"Following\" : \"Follow\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", user_r5.isFollowing ? \"checkmark\" : \"add\");\n  }\n}\nfunction SuggestedForYouComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_10_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slidePrev());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_10_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slideNext());\n    });\n    i0.ɵɵelement(4, \"ion-icon\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 29);\n    i0.ɵɵlistener(\"mouseenter\", function SuggestedForYouComponent_div_10_Template_div_mouseenter_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pauseAutoSlide());\n    })(\"mouseleave\", function SuggestedForYouComponent_div_10_Template_div_mouseleave_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resumeAutoSlide());\n    });\n    i0.ɵɵelementStart(6, \"div\", 30);\n    i0.ɵɵtemplate(7, SuggestedForYouComponent_div_10_div_7_Template, 19, 12, \"div\", 31);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide >= ctx_r1.maxSlide);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx_r1.slideOffset + \"px)\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.suggestedUsers)(\"ngForTrackBy\", ctx_r1.trackByUserId);\n  }\n}\nfunction SuggestedForYouComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵelement(1, \"ion-icon\", 47);\n    i0.ɵɵelementStart(2, \"h3\", 48);\n    i0.ɵɵtext(3, \"No Suggestions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 49);\n    i0.ɵɵtext(5, \"Check back later for user suggestions\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class SuggestedForYouComponent {\n  constructor(router) {\n    this.router = router;\n    this.suggestedUsers = [];\n    this.isLoading = true;\n    this.error = null;\n    this.subscription = new Subscription();\n    // Slider properties\n    this.currentSlide = 0;\n    this.slideOffset = 0;\n    this.cardWidth = 200; // Width of each user card including margin\n    this.visibleCards = 4; // Number of cards visible at once\n    this.maxSlide = 0;\n    this.autoSlideDelay = 5000; // 5 seconds for users\n    this.isAutoSliding = true;\n    this.isPaused = false;\n  }\n  ngOnInit() {\n    this.loadSuggestedUsers();\n    this.updateResponsiveSettings();\n    this.setupResizeListener();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n  loadSuggestedUsers() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        // Mock data for suggested users\n        _this.suggestedUsers = [{\n          id: '1',\n          username: 'fashionista_maya',\n          fullName: 'Maya Patel',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by john_doe and 12 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 45000,\n          category: 'Fashion'\n        }, {\n          id: '2',\n          username: 'style_guru_raj',\n          fullName: 'Raj Kumar',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by sarah_k and 8 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 32000,\n          category: 'Menswear'\n        }, {\n          id: '3',\n          username: 'trendy_sara',\n          fullName: 'Sara Johnson',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by alex_m and 15 others',\n          isFollowing: false,\n          isInfluencer: false,\n          followerCount: 8500,\n          category: 'Casual'\n        }, {\n          id: '4',\n          username: 'luxury_lover',\n          fullName: 'Emma Wilson',\n          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by mike_t and 20 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 67000,\n          category: 'Luxury'\n        }, {\n          id: '5',\n          username: 'street_style_alex',\n          fullName: 'Alex Chen',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by lisa_p and 5 others',\n          isFollowing: false,\n          isInfluencer: false,\n          followerCount: 12000,\n          category: 'Streetwear'\n        }, {\n          id: '6',\n          username: 'boho_bella',\n          fullName: 'Isabella Rodriguez',\n          avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by tom_h and 18 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 28000,\n          category: 'Boho'\n        }];\n        _this.isLoading = false;\n        _this.updateSliderOnUsersLoad();\n      } catch (error) {\n        console.error('Error loading suggested users:', error);\n        _this.error = 'Failed to load suggested users';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onUserClick(user) {\n    this.router.navigate(['/profile', user.username]);\n  }\n  onFollowUser(user, event) {\n    event.stopPropagation();\n    user.isFollowing = !user.isFollowing;\n    if (user.isFollowing) {\n      user.followerCount++;\n    } else {\n      user.followerCount--;\n    }\n  }\n  formatFollowerCount(count) {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  onRetry() {\n    this.loadSuggestedUsers();\n  }\n  trackByUserId(index, user) {\n    return user.id;\n  }\n  // Auto-sliding methods\n  startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.suggestedUsers.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n  autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n  // Responsive methods\n  updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 480) {\n      this.cardWidth = 180;\n      this.visibleCards = 1;\n    } else if (width <= 768) {\n      this.cardWidth = 200;\n      this.visibleCards = 2;\n    } else if (width <= 1200) {\n      this.cardWidth = 220;\n      this.visibleCards = 3;\n    } else {\n      this.cardWidth = 220;\n      this.visibleCards = 4;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n  setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.suggestedUsers.length - this.visibleCards);\n  }\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n  restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000);\n  }\n  // Update slider when users load\n  updateSliderOnUsersLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n  static {\n    this.ɵfac = function SuggestedForYouComponent_Factory(t) {\n      return new (t || SuggestedForYouComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SuggestedForYouComponent,\n      selectors: [[\"app-suggested-for-you\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 12,\n      vars: 4,\n      consts: [[1, \"suggested-users-container\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"people\", 1, \"title-icon\"], [1, \"section-subtitle\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"users-slider-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-user-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-user-card\"], [1, \"loading-avatar\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"users-slider-container\"], [1, \"slider-nav\", \"prev-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-back\"], [1, \"slider-nav\", \"next-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-forward\"], [1, \"users-slider-wrapper\", 3, \"mouseenter\", \"mouseleave\"], [1, \"users-slider\"], [\"class\", \"user-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"user-card\", 3, \"click\"], [1, \"user-avatar-container\"], [\"loading\", \"lazy\", 1, \"user-avatar\", 3, \"src\", \"alt\"], [\"class\", \"influencer-badge\", 4, \"ngIf\"], [1, \"user-info\"], [1, \"user-name\"], [1, \"username\"], [1, \"follower-count\"], [1, \"category-tag\"], [1, \"followed-by\"], [1, \"follow-btn\", 3, \"click\"], [3, \"name\"], [1, \"influencer-badge\"], [\"name\", \"star\"], [1, \"empty-container\"], [\"name\", \"people-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"]],\n      template: function SuggestedForYouComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n          i0.ɵɵelement(4, \"ion-icon\", 4);\n          i0.ɵɵtext(5, \" Suggested for you \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \"Discover amazing fashion creators\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(8, SuggestedForYouComponent_div_8_Template, 3, 2, \"div\", 6)(9, SuggestedForYouComponent_div_9_Template, 7, 1, \"div\", 7)(10, SuggestedForYouComponent_div_10_Template, 8, 6, \"div\", 8)(11, SuggestedForYouComponent_div_11_Template, 6, 0, \"div\", 9);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.suggestedUsers.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.suggestedUsers.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, IonicModule, i3.IonIcon, CarouselModule],\n      styles: [\".suggested-users-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #6c5ce7;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  margin: 0;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  overflow-x: auto;\\n  padding-bottom: 8px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%] {\\n  flex: 0 0 180px;\\n  background: rgba(255, 255, 255, 0.7);\\n  border-radius: 16px;\\n  padding: 20px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-avatar[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 50%;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  margin: 0 auto 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  border-radius: 6px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  margin-bottom: 8px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0% {\\n    background-position: 200% 0;\\n  }\\n  100% {\\n    background-position: -200% 0;\\n  }\\n}\\n.error-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #e74c3c;\\n  margin-bottom: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 20px;\\n  font-size: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);\\n  color: white;\\n  border: none;\\n  padding: 12px 24px;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 auto;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);\\n}\\n\\n.users-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin: 0 -20px;\\n}\\n.users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  z-index: 10;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  border: none;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: rgba(0, 0, 0, 0.9);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n}\\n.users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n.users-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%] {\\n  left: -20px;\\n}\\n.users-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%] {\\n  right: -20px;\\n}\\n\\n.users-slider-wrapper[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  padding: 0 20px;\\n}\\n\\n.users-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n  gap: 20px;\\n}\\n.users-slider[_ngcontent-%COMP%]   .user-card[_ngcontent-%COMP%] {\\n  flex: 0 0 180px;\\n  width: 180px;\\n}\\n\\n.user-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 20px;\\n  text-align: center;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.user-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n}\\n\\n.user-avatar-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 16px;\\n}\\n.user-avatar-container[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid #6c5ce7;\\n  margin: 0 auto;\\n  display: block;\\n}\\n.user-avatar-container[_ngcontent-%COMP%]   .influencer-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -5px;\\n  right: calc(50% - 45px);\\n  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);\\n  color: white;\\n  width: 24px;\\n  height: 24px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 12px;\\n  border: 2px solid white;\\n}\\n.user-avatar-container[_ngcontent-%COMP%]   .influencer-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.user-info[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1a1a1a;\\n  margin: 0 0 4px 0;\\n}\\n.user-info[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #6c5ce7;\\n  margin: 0 0 8px 0;\\n  font-weight: 500;\\n}\\n.user-info[_ngcontent-%COMP%]   .follower-count[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  margin: 0 0 4px 0;\\n  font-weight: 600;\\n}\\n.user-info[_ngcontent-%COMP%]   .category-tag[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #6c5ce7;\\n  background: rgba(108, 92, 231, 0.1);\\n  padding: 2px 8px;\\n  border-radius: 12px;\\n  display: inline-block;\\n  margin: 0 0 8px 0;\\n}\\n.user-info[_ngcontent-%COMP%]   .followed-by[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #999;\\n  margin: 0;\\n  line-height: 1.3;\\n}\\n\\n.follow-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #6c5ce7 0%, #5a4fcf 100%);\\n  color: white;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  margin: 0 auto;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.follow-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(108, 92, 231, 0.3);\\n}\\n.follow-btn.following[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);\\n}\\n.follow-btn.following[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 6px 20px rgba(0, 184, 148, 0.3);\\n}\\n.follow-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n\\n.empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: #ddd;\\n  margin-bottom: 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #666;\\n  margin: 0 0 8px 0;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  color: #999;\\n  margin: 0;\\n}\\n\\n@media (max-width: 1200px) {\\n  .users-slider[_ngcontent-%COMP%]   .user-card[_ngcontent-%COMP%] {\\n    flex: 0 0 170px;\\n    width: 170px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .users-slider-container[_ngcontent-%COMP%] {\\n    margin: 0 -10px;\\n  }\\n  .users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .users-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%] {\\n    left: -15px;\\n  }\\n  .users-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%] {\\n    right: -15px;\\n  }\\n  .users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .users-slider-wrapper[_ngcontent-%COMP%] {\\n    padding: 0 10px;\\n  }\\n  .users-slider[_ngcontent-%COMP%] {\\n    gap: 15px;\\n  }\\n  .users-slider[_ngcontent-%COMP%]   .user-card[_ngcontent-%COMP%] {\\n    flex: 0 0 160px;\\n    width: 160px;\\n    padding: 16px;\\n  }\\n  .user-avatar-container[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n    width: 70px;\\n    height: 70px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .users-slider[_ngcontent-%COMP%]   .user-card[_ngcontent-%COMP%] {\\n    flex: 0 0 150px;\\n    width: 150px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subscription", "IonicModule", "CarouselModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "SuggestedForYouComponent_div_8_div_2_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵtext", "ɵɵlistener", "SuggestedForYouComponent_div_9_Template_button_click_4_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onRetry", "ɵɵtextInterpolate", "error", "SuggestedForYouComponent_div_10_div_7_Template_div_click_0_listener", "user_r5", "_r4", "$implicit", "onUserClick", "SuggestedForYouComponent_div_10_div_7_div_3_Template", "SuggestedForYouComponent_div_10_div_7_Template_button_click_15_listener", "$event", "onFollowUser", "avatar", "ɵɵsanitizeUrl", "fullName", "isInfluencer", "ɵɵtextInterpolate1", "username", "formatFollowerCount", "followerCount", "category", "<PERSON><PERSON><PERSON>", "ɵɵclassProp", "isFollowing", "SuggestedForYouComponent_div_10_Template_button_click_1_listener", "_r3", "slidePrev", "SuggestedForYouComponent_div_10_Template_button_click_3_listener", "slideNext", "SuggestedForYouComponent_div_10_Template_div_mouseenter_5_listener", "pauseAutoSlide", "SuggestedForYouComponent_div_10_Template_div_mouseleave_5_listener", "resumeAutoSlide", "SuggestedForYouComponent_div_10_div_7_Template", "currentSlide", "maxSlide", "ɵɵstyleProp", "slideOffset", "suggestedUsers", "trackByUserId", "SuggestedForYouComponent", "constructor", "router", "isLoading", "subscription", "<PERSON><PERSON><PERSON><PERSON>", "visibleCards", "autoSlideDelay", "isAutoSliding", "isPaused", "ngOnInit", "loadSuggestedUsers", "updateResponsiveSettings", "setupResizeListener", "ngOnDestroy", "unsubscribe", "stopAutoSlide", "_this", "_asyncToGenerator", "id", "updateSliderOnUsersLoad", "console", "user", "navigate", "event", "stopPropagation", "count", "toFixed", "toString", "index", "startAutoSlide", "autoSlideInterval", "setInterval", "length", "autoSlideNext", "clearInterval", "updateSlideOffset", "width", "window", "innerWidth", "updateSliderLimits", "addEventListener", "Math", "max", "restartAutoSlideAfterInteraction", "setTimeout", "ɵɵdirectiveInject", "i1", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SuggestedForYouComponent_Template", "rf", "ctx", "SuggestedForYouComponent_div_8_Template", "SuggestedForYouComponent_div_9_Template", "SuggestedForYouComponent_div_10_Template", "SuggestedForYouComponent_div_11_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "IonIcon", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\suggested-for-you\\suggested-for-you.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\suggested-for-you\\suggested-for-you.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\n\ninterface SuggestedUser {\n  id: string;\n  username: string;\n  fullName: string;\n  avatar: string;\n  followedBy: string;\n  isFollowing: boolean;\n  isInfluencer: boolean;\n  followerCount: number;\n  category: string;\n}\n\n@Component({\n  selector: 'app-suggested-for-you',\n  standalone: true,\n  imports: [CommonModule, IonicModule, CarouselModule],\n  templateUrl: './suggested-for-you.component.html',\n  styleUrls: ['./suggested-for-you.component.scss']\n})\nexport class SuggestedForYouComponent implements OnInit, OnDestroy {\n  suggestedUsers: SuggestedUser[] = [];\n  isLoading = true;\n  error: string | null = null;\n  private subscription: Subscription = new Subscription();\n\n  // Slider properties\n  currentSlide = 0;\n  slideOffset = 0;\n  cardWidth = 200; // Width of each user card including margin\n  visibleCards = 4; // Number of cards visible at once\n  maxSlide = 0;\n  \n  // Auto-sliding properties\n  autoSlideInterval: any;\n  autoSlideDelay = 5000; // 5 seconds for users\n  isAutoSliding = true;\n  isPaused = false;\n\n  constructor(private router: Router) {}\n\n  ngOnInit() {\n    this.loadSuggestedUsers();\n    this.updateResponsiveSettings();\n    this.setupResizeListener();\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n\n  private async loadSuggestedUsers() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      \n      // Mock data for suggested users\n      this.suggestedUsers = [\n        {\n          id: '1',\n          username: 'fashionista_maya',\n          fullName: 'Maya Patel',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by john_doe and 12 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 45000,\n          category: 'Fashion'\n        },\n        {\n          id: '2',\n          username: 'style_guru_raj',\n          fullName: 'Raj Kumar',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by sarah_k and 8 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 32000,\n          category: 'Menswear'\n        },\n        {\n          id: '3',\n          username: 'trendy_sara',\n          fullName: 'Sara Johnson',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by alex_m and 15 others',\n          isFollowing: false,\n          isInfluencer: false,\n          followerCount: 8500,\n          category: 'Casual'\n        },\n        {\n          id: '4',\n          username: 'luxury_lover',\n          fullName: 'Emma Wilson',\n          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by mike_t and 20 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 67000,\n          category: 'Luxury'\n        },\n        {\n          id: '5',\n          username: 'street_style_alex',\n          fullName: 'Alex Chen',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by lisa_p and 5 others',\n          isFollowing: false,\n          isInfluencer: false,\n          followerCount: 12000,\n          category: 'Streetwear'\n        },\n        {\n          id: '6',\n          username: 'boho_bella',\n          fullName: 'Isabella Rodriguez',\n          avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by tom_h and 18 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 28000,\n          category: 'Boho'\n        }\n      ];\n      \n      this.isLoading = false;\n      this.updateSliderOnUsersLoad();\n    } catch (error) {\n      console.error('Error loading suggested users:', error);\n      this.error = 'Failed to load suggested users';\n      this.isLoading = false;\n    }\n  }\n\n  onUserClick(user: SuggestedUser) {\n    this.router.navigate(['/profile', user.username]);\n  }\n\n  onFollowUser(user: SuggestedUser, event: Event) {\n    event.stopPropagation();\n    user.isFollowing = !user.isFollowing;\n    \n    if (user.isFollowing) {\n      user.followerCount++;\n    } else {\n      user.followerCount--;\n    }\n  }\n\n  formatFollowerCount(count: number): string {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n\n  onRetry() {\n    this.loadSuggestedUsers();\n  }\n\n  trackByUserId(index: number, user: SuggestedUser): string {\n    return user.id;\n  }\n\n  // Auto-sliding methods\n  private startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n    \n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.suggestedUsers.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n\n  private stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n\n  private autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n\n  // Responsive methods\n  private updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 480) {\n      this.cardWidth = 180;\n      this.visibleCards = 1;\n    } else if (width <= 768) {\n      this.cardWidth = 200;\n      this.visibleCards = 2;\n    } else if (width <= 1200) {\n      this.cardWidth = 220;\n      this.visibleCards = 3;\n    } else {\n      this.cardWidth = 220;\n      this.visibleCards = 4;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n\n  private setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.suggestedUsers.length - this.visibleCards);\n  }\n\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n\n  private updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n\n  private restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000);\n  }\n\n  // Update slider when users load\n  private updateSliderOnUsersLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n}\n", "<div class=\"suggested-users-container\">\n  <!-- Header -->\n  <div class=\"section-header\">\n    <div class=\"header-content\">\n      <h2 class=\"section-title\">\n        <ion-icon name=\"people\" class=\"title-icon\"></ion-icon>\n        Suggested for you\n      </h2>\n      <p class=\"section-subtitle\">Discover amazing fashion creators</p>\n    </div>\n  </div>\n\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <div class=\"loading-grid\">\n      <div *ngFor=\"let item of [1,2,3,4]\" class=\"loading-user-card\">\n        <div class=\"loading-avatar\"></div>\n        <div class=\"loading-content\">\n          <div class=\"loading-line short\"></div>\n          <div class=\"loading-line medium\"></div>\n          <div class=\"loading-line long\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error && !isLoading\" class=\"error-container\">\n    <ion-icon name=\"alert-circle\" class=\"error-icon\"></ion-icon>\n    <p class=\"error-message\">{{ error }}</p>\n    <button class=\"retry-btn\" (click)=\"onRetry()\">\n      <ion-icon name=\"refresh\"></ion-icon>\n      Try Again\n    </button>\n  </div>\n\n  <!-- Users Slider -->\n  <div *ngIf=\"!isLoading && !error && suggestedUsers.length > 0\" class=\"users-slider-container\">\n    <!-- Navigation Buttons -->\n    <button class=\"slider-nav prev-btn\" (click)=\"slidePrev()\" [disabled]=\"currentSlide === 0\">\n      <ion-icon name=\"chevron-back\"></ion-icon>\n    </button>\n    <button class=\"slider-nav next-btn\" (click)=\"slideNext()\" [disabled]=\"currentSlide >= maxSlide\">\n      <ion-icon name=\"chevron-forward\"></ion-icon>\n    </button>\n    \n    <!-- Slider Wrapper -->\n    <div class=\"users-slider-wrapper\" (mouseenter)=\"pauseAutoSlide()\" (mouseleave)=\"resumeAutoSlide()\">\n      <div class=\"users-slider\" [style.transform]=\"'translateX(' + slideOffset + 'px)'\">\n        <div \n          *ngFor=\"let user of suggestedUsers; trackBy: trackByUserId\" \n          class=\"user-card\"\n          (click)=\"onUserClick(user)\"\n        >\n          <!-- User Avatar -->\n          <div class=\"user-avatar-container\">\n            <img \n              [src]=\"user.avatar\"\n              [alt]=\"user.fullName\"\n              class=\"user-avatar\"\n              loading=\"lazy\"\n            />\n            <div *ngIf=\"user.isInfluencer\" class=\"influencer-badge\">\n              <ion-icon name=\"star\"></ion-icon>\n            </div>\n          </div>\n\n          <!-- User Info -->\n          <div class=\"user-info\">\n            <h3 class=\"user-name\">{{ user.fullName }}</h3>\n            <p class=\"username\">&#64;{{ user.username }}</p>\n            <p class=\"follower-count\">{{ formatFollowerCount(user.followerCount) }} followers</p>\n            <p class=\"category-tag\">{{ user.category }}</p>\n            <p class=\"followed-by\">{{ user.followedBy }}</p>\n          </div>\n\n          <!-- Follow Button -->\n          <button \n            class=\"follow-btn\"\n            [class.following]=\"user.isFollowing\"\n            (click)=\"onFollowUser(user, $event)\"\n          >\n            <span>{{ user.isFollowing ? 'Following' : 'Follow' }}</span>\n            <ion-icon [name]=\"user.isFollowing ? 'checkmark' : 'add'\"></ion-icon>\n          </button>\n        </div>\n      </div>\n    </div> <!-- End users-slider-wrapper -->\n  </div> <!-- End users-slider-container -->\n\n  <!-- Empty State -->\n  <div *ngIf=\"!isLoading && !error && suggestedUsers.length === 0\" class=\"empty-container\">\n    <ion-icon name=\"people-outline\" class=\"empty-icon\"></ion-icon>\n    <h3 class=\"empty-title\">No Suggestions</h3>\n    <p class=\"empty-message\">Check back later for user suggestions</p>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AACnC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,oBAAoB;;;;;;;;ICU7CC,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAE,SAAA,cAAkC;IAClCF,EAAA,CAAAC,cAAA,cAA6B;IAG3BD,EAFA,CAAAE,SAAA,cAAsC,cACC,cACF;IAEzCF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IARRH,EADF,CAAAC,cAAA,cAAiD,cACrB;IACxBD,EAAA,CAAAI,UAAA,IAAAC,6CAAA,kBAA8D;IASlEL,EADE,CAAAG,YAAA,EAAM,EACF;;;IAToBH,EAAA,CAAAM,SAAA,GAAY;IAAZN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAY;;;;;;IAYtCT,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,SAAA,mBAA4D;IAC5DF,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAU,MAAA,GAAW;IAAAV,EAAA,CAAAG,YAAA,EAAI;IACxCH,EAAA,CAAAC,cAAA,iBAA8C;IAApBD,EAAA,CAAAW,UAAA,mBAAAC,gEAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAG,OAAA,EAAS;IAAA,EAAC;IAC3ClB,EAAA,CAAAE,SAAA,mBAAoC;IACpCF,EAAA,CAAAU,MAAA,kBACF;IACFV,EADE,CAAAG,YAAA,EAAS,EACL;;;;IALqBH,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAmB,iBAAA,CAAAJ,MAAA,CAAAK,KAAA,CAAW;;;;;IAiC5BpB,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAE,SAAA,mBAAiC;IACnCF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAfVH,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAW,UAAA,mBAAAU,oEAAA;MAAA,MAAAC,OAAA,GAAAtB,EAAA,CAAAa,aAAA,CAAAU,GAAA,EAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAU,WAAA,CAAAH,OAAA,CAAiB;IAAA,EAAC;IAG3BtB,EAAA,CAAAC,cAAA,cAAmC;IACjCD,EAAA,CAAAE,SAAA,cAKE;IACFF,EAAA,CAAAI,UAAA,IAAAsB,oDAAA,kBAAwD;IAG1D1B,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,cAAuB,aACC;IAAAD,EAAA,CAAAU,MAAA,GAAmB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,YAAoB;IAAAD,EAAA,CAAAU,MAAA,GAAwB;IAAAV,EAAA,CAAAG,YAAA,EAAI;IAChDH,EAAA,CAAAC,cAAA,YAA0B;IAAAD,EAAA,CAAAU,MAAA,IAAuD;IAAAV,EAAA,CAAAG,YAAA,EAAI;IACrFH,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAU,MAAA,IAAmB;IAAAV,EAAA,CAAAG,YAAA,EAAI;IAC/CH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAU,MAAA,IAAqB;IAC9CV,EAD8C,CAAAG,YAAA,EAAI,EAC5C;IAGNH,EAAA,CAAAC,cAAA,kBAIC;IADCD,EAAA,CAAAW,UAAA,mBAAAgB,wEAAAC,MAAA;MAAA,MAAAN,OAAA,GAAAtB,EAAA,CAAAa,aAAA,CAAAU,GAAA,EAAAC,SAAA;MAAA,MAAAT,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAc,YAAA,CAAAP,OAAA,EAAAM,MAAA,CAA0B;IAAA,EAAC;IAEpC5B,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,IAA+C;IAAAV,EAAA,CAAAG,YAAA,EAAO;IAC5DH,EAAA,CAAAE,SAAA,oBAAqE;IAEzEF,EADE,CAAAG,YAAA,EAAS,EACL;;;;;IA5BAH,EAAA,CAAAM,SAAA,GAAmB;IACnBN,EADA,CAAAO,UAAA,QAAAe,OAAA,CAAAQ,MAAA,EAAA9B,EAAA,CAAA+B,aAAA,CAAmB,QAAAT,OAAA,CAAAU,QAAA,CACE;IAIjBhC,EAAA,CAAAM,SAAA,EAAuB;IAAvBN,EAAA,CAAAO,UAAA,SAAAe,OAAA,CAAAW,YAAA,CAAuB;IAOPjC,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAmB,iBAAA,CAAAG,OAAA,CAAAU,QAAA,CAAmB;IACrBhC,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAkC,kBAAA,MAAAZ,OAAA,CAAAa,QAAA,KAAwB;IAClBnC,EAAA,CAAAM,SAAA,GAAuD;IAAvDN,EAAA,CAAAkC,kBAAA,KAAAnB,MAAA,CAAAqB,mBAAA,CAAAd,OAAA,CAAAe,aAAA,gBAAuD;IACzDrC,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAmB,iBAAA,CAAAG,OAAA,CAAAgB,QAAA,CAAmB;IACpBtC,EAAA,CAAAM,SAAA,GAAqB;IAArBN,EAAA,CAAAmB,iBAAA,CAAAG,OAAA,CAAAiB,UAAA,CAAqB;IAM5CvC,EAAA,CAAAM,SAAA,EAAoC;IAApCN,EAAA,CAAAwC,WAAA,cAAAlB,OAAA,CAAAmB,WAAA,CAAoC;IAG9BzC,EAAA,CAAAM,SAAA,GAA+C;IAA/CN,EAAA,CAAAmB,iBAAA,CAAAG,OAAA,CAAAmB,WAAA,0BAA+C;IAC3CzC,EAAA,CAAAM,SAAA,EAA+C;IAA/CN,EAAA,CAAAO,UAAA,SAAAe,OAAA,CAAAmB,WAAA,uBAA+C;;;;;;IA5CjEzC,EAFF,CAAAC,cAAA,cAA8F,iBAEF;IAAtDD,EAAA,CAAAW,UAAA,mBAAA+B,iEAAA;MAAA1C,EAAA,CAAAa,aAAA,CAAA8B,GAAA;MAAA,MAAA5B,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAA6B,SAAA,EAAW;IAAA,EAAC;IACvD5C,EAAA,CAAAE,SAAA,mBAAyC;IAC3CF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAgG;IAA5DD,EAAA,CAAAW,UAAA,mBAAAkC,iEAAA;MAAA7C,EAAA,CAAAa,aAAA,CAAA8B,GAAA;MAAA,MAAA5B,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAA+B,SAAA,EAAW;IAAA,EAAC;IACvD9C,EAAA,CAAAE,SAAA,mBAA4C;IAC9CF,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,cAAmG;IAAjCD,EAAhC,CAAAW,UAAA,wBAAAoC,mEAAA;MAAA/C,EAAA,CAAAa,aAAA,CAAA8B,GAAA;MAAA,MAAA5B,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAcF,MAAA,CAAAiC,cAAA,EAAgB;IAAA,EAAC,wBAAAC,mEAAA;MAAAjD,EAAA,CAAAa,aAAA,CAAA8B,GAAA;MAAA,MAAA5B,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAeF,MAAA,CAAAmC,eAAA,EAAiB;IAAA,EAAC;IAChGlD,EAAA,CAAAC,cAAA,cAAkF;IAChFD,EAAA,CAAAI,UAAA,IAAA+C,8CAAA,oBAIC;IAmCPnD,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAjDsDH,EAAA,CAAAM,SAAA,EAA+B;IAA/BN,EAAA,CAAAO,UAAA,aAAAQ,MAAA,CAAAqC,YAAA,OAA+B;IAG/BpD,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAAO,UAAA,aAAAQ,MAAA,CAAAqC,YAAA,IAAArC,MAAA,CAAAsC,QAAA,CAAqC;IAMnErD,EAAA,CAAAM,SAAA,GAAuD;IAAvDN,EAAA,CAAAsD,WAAA,8BAAAvC,MAAA,CAAAwC,WAAA,SAAuD;IAE5DvD,EAAA,CAAAM,SAAA,EAAmB;IAAAN,EAAnB,CAAAO,UAAA,YAAAQ,MAAA,CAAAyC,cAAA,CAAmB,iBAAAzC,MAAA,CAAA0C,aAAA,CAAsB;;;;;IAyClEzD,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAE,SAAA,mBAA8D;IAC9DF,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAU,MAAA,qBAAc;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAU,MAAA,4CAAqC;IAChEV,EADgE,CAAAG,YAAA,EAAI,EAC9D;;;ADrER,OAAM,MAAOuD,wBAAwB;EAmBnCC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAlB1B,KAAAJ,cAAc,GAAoB,EAAE;IACpC,KAAAK,SAAS,GAAG,IAAI;IAChB,KAAAzC,KAAK,GAAkB,IAAI;IACnB,KAAA0C,YAAY,GAAiB,IAAIjE,YAAY,EAAE;IAEvD;IACA,KAAAuD,YAAY,GAAG,CAAC;IAChB,KAAAG,WAAW,GAAG,CAAC;IACf,KAAAQ,SAAS,GAAG,GAAG,CAAC,CAAC;IACjB,KAAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAClB,KAAAX,QAAQ,GAAG,CAAC;IAIZ,KAAAY,cAAc,GAAG,IAAI,CAAC,CAAC;IACvB,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,QAAQ,GAAG,KAAK;EAEqB;EAErCC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,wBAAwB,EAAE;IAC/B,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACV,YAAY,CAACW,WAAW,EAAE;IAC/B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEcL,kBAAkBA,CAAA;IAAA,IAAAM,KAAA;IAAA,OAAAC,iBAAA;MAC9B,IAAI;QACFD,KAAI,CAACd,SAAS,GAAG,IAAI;QACrBc,KAAI,CAACvD,KAAK,GAAG,IAAI;QAEjB;QACAuD,KAAI,CAACnB,cAAc,GAAG,CACpB;UACEqB,EAAE,EAAE,GAAG;UACP1C,QAAQ,EAAE,kBAAkB;UAC5BH,QAAQ,EAAE,YAAY;UACtBF,MAAM,EAAE,6FAA6F;UACrGS,UAAU,EAAE,oCAAoC;UAChDE,WAAW,EAAE,KAAK;UAClBR,YAAY,EAAE,IAAI;UAClBI,aAAa,EAAE,KAAK;UACpBC,QAAQ,EAAE;SACX,EACD;UACEuC,EAAE,EAAE,GAAG;UACP1C,QAAQ,EAAE,gBAAgB;UAC1BH,QAAQ,EAAE,WAAW;UACrBF,MAAM,EAAE,6FAA6F;UACrGS,UAAU,EAAE,kCAAkC;UAC9CE,WAAW,EAAE,KAAK;UAClBR,YAAY,EAAE,IAAI;UAClBI,aAAa,EAAE,KAAK;UACpBC,QAAQ,EAAE;SACX,EACD;UACEuC,EAAE,EAAE,GAAG;UACP1C,QAAQ,EAAE,aAAa;UACvBH,QAAQ,EAAE,cAAc;UACxBF,MAAM,EAAE,6FAA6F;UACrGS,UAAU,EAAE,kCAAkC;UAC9CE,WAAW,EAAE,KAAK;UAClBR,YAAY,EAAE,KAAK;UACnBI,aAAa,EAAE,IAAI;UACnBC,QAAQ,EAAE;SACX,EACD;UACEuC,EAAE,EAAE,GAAG;UACP1C,QAAQ,EAAE,cAAc;UACxBH,QAAQ,EAAE,aAAa;UACvBF,MAAM,EAAE,0FAA0F;UAClGS,UAAU,EAAE,kCAAkC;UAC9CE,WAAW,EAAE,KAAK;UAClBR,YAAY,EAAE,IAAI;UAClBI,aAAa,EAAE,KAAK;UACpBC,QAAQ,EAAE;SACX,EACD;UACEuC,EAAE,EAAE,GAAG;UACP1C,QAAQ,EAAE,mBAAmB;UAC7BH,QAAQ,EAAE,WAAW;UACrBF,MAAM,EAAE,6FAA6F;UACrGS,UAAU,EAAE,iCAAiC;UAC7CE,WAAW,EAAE,KAAK;UAClBR,YAAY,EAAE,KAAK;UACnBI,aAAa,EAAE,KAAK;UACpBC,QAAQ,EAAE;SACX,EACD;UACEuC,EAAE,EAAE,GAAG;UACP1C,QAAQ,EAAE,YAAY;UACtBH,QAAQ,EAAE,oBAAoB;UAC9BF,MAAM,EAAE,6FAA6F;UACrGS,UAAU,EAAE,iCAAiC;UAC7CE,WAAW,EAAE,KAAK;UAClBR,YAAY,EAAE,IAAI;UAClBI,aAAa,EAAE,KAAK;UACpBC,QAAQ,EAAE;SACX,CACF;QAEDqC,KAAI,CAACd,SAAS,GAAG,KAAK;QACtBc,KAAI,CAACG,uBAAuB,EAAE;OAC/B,CAAC,OAAO1D,KAAK,EAAE;QACd2D,OAAO,CAAC3D,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtDuD,KAAI,CAACvD,KAAK,GAAG,gCAAgC;QAC7CuD,KAAI,CAACd,SAAS,GAAG,KAAK;;IACvB;EACH;EAEApC,WAAWA,CAACuD,IAAmB;IAC7B,IAAI,CAACpB,MAAM,CAACqB,QAAQ,CAAC,CAAC,UAAU,EAAED,IAAI,CAAC7C,QAAQ,CAAC,CAAC;EACnD;EAEAN,YAAYA,CAACmD,IAAmB,EAAEE,KAAY;IAC5CA,KAAK,CAACC,eAAe,EAAE;IACvBH,IAAI,CAACvC,WAAW,GAAG,CAACuC,IAAI,CAACvC,WAAW;IAEpC,IAAIuC,IAAI,CAACvC,WAAW,EAAE;MACpBuC,IAAI,CAAC3C,aAAa,EAAE;KACrB,MAAM;MACL2C,IAAI,CAAC3C,aAAa,EAAE;;EAExB;EAEAD,mBAAmBA,CAACgD,KAAa;IAC/B,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEApE,OAAOA,CAAA;IACL,IAAI,CAACmD,kBAAkB,EAAE;EAC3B;EAEAZ,aAAaA,CAAC8B,KAAa,EAAEP,IAAmB;IAC9C,OAAOA,IAAI,CAACH,EAAE;EAChB;EAEA;EACQW,cAAcA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACtB,aAAa,IAAI,IAAI,CAACC,QAAQ,EAAE;IAE1C,IAAI,CAACO,aAAa,EAAE;IACpB,IAAI,CAACe,iBAAiB,GAAGC,WAAW,CAAC,MAAK;MACxC,IAAI,CAAC,IAAI,CAACvB,QAAQ,IAAI,IAAI,CAACX,cAAc,CAACmC,MAAM,GAAG,IAAI,CAAC3B,YAAY,EAAE;QACpE,IAAI,CAAC4B,aAAa,EAAE;;IAExB,CAAC,EAAE,IAAI,CAAC3B,cAAc,CAAC;EACzB;EAEQS,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACe,iBAAiB,EAAE;MAC1BI,aAAa,CAAC,IAAI,CAACJ,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;EAEjC;EAEQG,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACxC,YAAY,IAAI,IAAI,CAACC,QAAQ,EAAE;MACtC,IAAI,CAACD,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;IAErB,IAAI,CAAC0C,iBAAiB,EAAE;EAC1B;EAEA9C,cAAcA,CAAA;IACZ,IAAI,CAACmB,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACO,aAAa,EAAE;EACtB;EAEAxB,eAAeA,CAAA;IACb,IAAI,CAACiB,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACqB,cAAc,EAAE;EACvB;EAEA;EACQlB,wBAAwBA,CAAA;IAC9B,MAAMyB,KAAK,GAAGC,MAAM,CAACC,UAAU;IAC/B,IAAIF,KAAK,IAAI,GAAG,EAAE;MAChB,IAAI,CAAChC,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAI+B,KAAK,IAAI,GAAG,EAAE;MACvB,IAAI,CAAChC,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAI+B,KAAK,IAAI,IAAI,EAAE;MACxB,IAAI,CAAChC,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACD,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;;IAEvB,IAAI,CAACkC,kBAAkB,EAAE;IACzB,IAAI,CAACJ,iBAAiB,EAAE;EAC1B;EAEQvB,mBAAmBA,CAAA;IACzByB,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MACrC,IAAI,CAAC7B,wBAAwB,EAAE;IACjC,CAAC,CAAC;EACJ;EAEA;EACA4B,kBAAkBA,CAAA;IAChB,IAAI,CAAC7C,QAAQ,GAAG+C,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC7C,cAAc,CAACmC,MAAM,GAAG,IAAI,CAAC3B,YAAY,CAAC;EAC7E;EAEApB,SAASA,CAAA;IACP,IAAI,IAAI,CAACQ,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,IAAI,CAAC0C,iBAAiB,EAAE;MACxB,IAAI,CAACQ,gCAAgC,EAAE;;EAE3C;EAEAxD,SAASA,CAAA;IACP,IAAI,IAAI,CAACM,YAAY,GAAG,IAAI,CAACC,QAAQ,EAAE;MACrC,IAAI,CAACD,YAAY,EAAE;MACnB,IAAI,CAAC0C,iBAAiB,EAAE;MACxB,IAAI,CAACQ,gCAAgC,EAAE;;EAE3C;EAEQR,iBAAiBA,CAAA;IACvB,IAAI,CAACvC,WAAW,GAAG,CAAC,IAAI,CAACH,YAAY,GAAG,IAAI,CAACW,SAAS;EACxD;EAEQuC,gCAAgCA,CAAA;IACtC,IAAI,CAAC5B,aAAa,EAAE;IACpB6B,UAAU,CAAC,MAAK;MACd,IAAI,CAACf,cAAc,EAAE;IACvB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACQV,uBAAuBA,CAAA;IAC7ByB,UAAU,CAAC,MAAK;MACd,IAAI,CAACL,kBAAkB,EAAE;MACzB,IAAI,CAAC9C,YAAY,GAAG,CAAC;MACrB,IAAI,CAACG,WAAW,GAAG,CAAC;MACpB,IAAI,CAACiC,cAAc,EAAE;IACvB,CAAC,EAAE,GAAG,CAAC;EACT;;;uBA5PW9B,wBAAwB,EAAA1D,EAAA,CAAAwG,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAxBhD,wBAAwB;MAAAiD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA7G,EAAA,CAAA8G,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCtB/BpH,EAJN,CAAAC,cAAA,aAAuC,aAET,aACE,YACA;UACxBD,EAAA,CAAAE,SAAA,kBAAsD;UACtDF,EAAA,CAAAU,MAAA,0BACF;UAAAV,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAU,MAAA,wCAAiC;UAEjEV,EAFiE,CAAAG,YAAA,EAAI,EAC7D,EACF;UAiFNH,EA9EA,CAAAI,UAAA,IAAAkH,uCAAA,iBAAiD,IAAAC,uCAAA,iBAcQ,KAAAC,wCAAA,iBAUqC,KAAAC,wCAAA,iBAsDL;UAK3FzH,EAAA,CAAAG,YAAA,EAAM;;;UAnFEH,EAAA,CAAAM,SAAA,GAAe;UAAfN,EAAA,CAAAO,UAAA,SAAA8G,GAAA,CAAAxD,SAAA,CAAe;UAcf7D,EAAA,CAAAM,SAAA,EAAyB;UAAzBN,EAAA,CAAAO,UAAA,SAAA8G,GAAA,CAAAjG,KAAA,KAAAiG,GAAA,CAAAxD,SAAA,CAAyB;UAUzB7D,EAAA,CAAAM,SAAA,EAAuD;UAAvDN,EAAA,CAAAO,UAAA,UAAA8G,GAAA,CAAAxD,SAAA,KAAAwD,GAAA,CAAAjG,KAAA,IAAAiG,GAAA,CAAA7D,cAAA,CAAAmC,MAAA,KAAuD;UAsDvD3F,EAAA,CAAAM,SAAA,EAAyD;UAAzDN,EAAA,CAAAO,UAAA,UAAA8G,GAAA,CAAAxD,SAAA,KAAAwD,GAAA,CAAAjG,KAAA,IAAAiG,GAAA,CAAA7D,cAAA,CAAAmC,MAAA,OAAyD;;;qBDrErD/F,YAAY,EAAA8H,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE9H,WAAW,EAAA+H,EAAA,CAAAC,OAAA,EAAE/H,cAAc;MAAAgI,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}