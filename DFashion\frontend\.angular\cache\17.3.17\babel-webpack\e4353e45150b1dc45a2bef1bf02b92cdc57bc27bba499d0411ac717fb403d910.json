{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport class ProfileComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.currentUser = null;\n    this.isLoading = true;\n  }\n  ngOnInit() {\n    this.loadUserProfile();\n  }\n  loadUserProfile() {\n    this.currentUser = this.authService.currentUserValue;\n    this.isLoading = false;\n  }\n  // Role-based feature access\n  canAccessVendorDashboard() {\n    return this.authService.isVendor() || this.authService.isAdmin();\n  }\n  canAccessAdminPanel() {\n    return this.authService.isAdmin();\n  }\n  canManageProducts() {\n    return this.authService.isVendor() || this.authService.isAdmin();\n  }\n  canViewAnalytics() {\n    return this.authService.isVendor() || this.authService.isAdmin();\n  }\n  // Navigation methods\n  navigateToVendorDashboard() {\n    if (this.canAccessVendorDashboard()) {\n      this.router.navigate(['/vendor/dashboard']);\n    }\n  }\n  navigateToAdminPanel() {\n    if (this.canAccessAdminPanel()) {\n      this.router.navigate(['/admin/dashboard']);\n    }\n  }\n  navigateToOrders() {\n    this.router.navigate(['/account/orders']);\n  }\n  navigateToWishlist() {\n    this.router.navigate(['/wishlist']);\n  }\n  navigateToCart() {\n    this.router.navigate(['/cart']);\n  }\n  navigateToSettings() {\n    this.router.navigate(['/account/settings']);\n  }\n  editProfile() {\n    this.router.navigate(['/account/edit-profile']);\n  }\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/auth/login']);\n  }\n  getRoleDisplayName() {\n    switch (this.currentUser?.role) {\n      case 'customer':\n        return 'Customer';\n      case 'vendor':\n        return 'Vendor';\n      case 'admin':\n        return 'Administrator';\n      default:\n        return 'User';\n    }\n  }\n  getRoleFeatures() {\n    const baseFeatures = [{\n      icon: 'person-outline',\n      title: 'Edit Profile',\n      action: () => this.editProfile()\n    }, {\n      icon: 'bag-outline',\n      title: 'My Orders',\n      action: () => this.navigateToOrders()\n    }, {\n      icon: 'heart-outline',\n      title: 'Wishlist',\n      action: () => this.navigateToWishlist()\n    }, {\n      icon: 'cart-outline',\n      title: 'Shopping Cart',\n      action: () => this.navigateToCart()\n    }, {\n      icon: 'settings-outline',\n      title: 'Settings',\n      action: () => this.navigateToSettings()\n    }];\n    if (this.canAccessVendorDashboard()) {\n      baseFeatures.push({\n        icon: 'storefront-outline',\n        title: 'Vendor Dashboard',\n        action: () => this.navigateToVendorDashboard()\n      }, {\n        icon: 'cube-outline',\n        title: 'Manage Products',\n        action: () => this.router.navigate(['/vendor/products'])\n      }, {\n        icon: 'analytics-outline',\n        title: 'Sales Analytics',\n        action: () => this.router.navigate(['/vendor/analytics'])\n      });\n    }\n    if (this.canAccessAdminPanel()) {\n      baseFeatures.push({\n        icon: 'shield-outline',\n        title: 'Admin Panel',\n        action: () => this.navigateToAdminPanel()\n      }, {\n        icon: 'people-outline',\n        title: 'User Management',\n        action: () => this.router.navigate(['/admin/users'])\n      }, {\n        icon: 'bar-chart-outline',\n        title: 'System Analytics',\n        action: () => this.router.navigate(['/admin/analytics'])\n      });\n    }\n    return baseFeatures;\n  }\n  static {\n    this.ɵfac = function ProfileComponent_Factory(t) {\n      return new (t || ProfileComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileComponent,\n      selectors: [[\"app-profile\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 0,\n      consts: [[1, \"profile-container\"]],\n      template: function ProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\");\n          i0.ɵɵtext(2, \"Profile Page\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"p\");\n          i0.ɵɵtext(4, \"Profile functionality coming soon...\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      dependencies: [CommonModule, FormsModule, IonicModule],\n      styles: [\".profile-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n\\n.profile-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 20px;\\n  margin-bottom: 30px;\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 12px;\\n  color: white;\\n}\\n\\n.profile-avatar[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 50%;\\n  border: 3px solid white;\\n}\\n\\n.profile-info[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.5rem;\\n}\\n\\n.profile-role[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  padding: 4px 12px;\\n  border-radius: 20px;\\n  font-size: 0.9rem;\\n  margin-top: 8px;\\n  display: inline-block;\\n}\\n\\n.profile-sections[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 20px;\\n}\\n\\n.profile-section[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  padding: 20px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  margin-bottom: 15px;\\n  color: #333;\\n}\\n\\n.role-features[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 15px;\\n}\\n\\n.feature-card[_ngcontent-%COMP%] {\\n  padding: 15px;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  text-align: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.feature-card[_ngcontent-%COMP%]:hover {\\n  border-color: #667eea;\\n  transform: translateY(-2px);\\n}\\n\\n.feature-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  margin-bottom: 10px;\\n  color: #667eea;\\n}\\n\\n.settings-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n}\\n\\n.settings-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 12px 0;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n\\n.settings-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: #667eea;\\n  color: white;\\n  border: none;\\n  padding: 10px 20px;\\n  border-radius: 6px;\\n  cursor: pointer;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  color: #333;\\n  border: 1px solid #ddd;\\n  padding: 10px 20px;\\n  border-radius: 6px;\\n  cursor: pointer;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "IonicModule", "ProfileComponent", "constructor", "authService", "router", "currentUser", "isLoading", "ngOnInit", "loadUserProfile", "currentUserValue", "canAccessVendorDashboard", "isVendor", "isAdmin", "canAccessAdminPanel", "canManageProducts", "canViewAnalytics", "navigateToVendorDashboard", "navigate", "navigateToAdminPanel", "navigateToOrders", "navigateToWishlist", "navigateToCart", "navigateToSettings", "editProfile", "logout", "getRoleDisplayName", "role", "getRoleFeatures", "baseFeatures", "icon", "title", "action", "push", "i0", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ProfileComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\profile\\pages\\profile\\profile.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\profile\\pages\\profile\\profile.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\n\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { User } from '../../../../core/models/user.model';\n\n@Component({\n  selector: 'app-profile',\n  standalone: true,\n  imports: [CommonModule, FormsModule, IonicModule],\n  templateUrl: './profile.component.html',\n  styles: [`\n    .profile-container {\n      padding: 20px;\n      max-width: 800px;\n      margin: 0 auto;\n    }\n\n    .profile-header {\n      display: flex;\n      align-items: center;\n      gap: 20px;\n      margin-bottom: 30px;\n      padding: 20px;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      border-radius: 12px;\n      color: white;\n    }\n\n    .profile-avatar {\n      width: 80px;\n      height: 80px;\n      border-radius: 50%;\n      border: 3px solid white;\n    }\n\n    .profile-info h2 {\n      margin: 0;\n      font-size: 1.5rem;\n    }\n\n    .profile-role {\n      background: rgba(255, 255, 255, 0.2);\n      padding: 4px 12px;\n      border-radius: 20px;\n      font-size: 0.9rem;\n      margin-top: 8px;\n      display: inline-block;\n    }\n\n    .profile-sections {\n      display: grid;\n      gap: 20px;\n    }\n\n    .profile-section {\n      background: white;\n      border-radius: 12px;\n      padding: 20px;\n      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n    }\n\n    .section-title {\n      font-size: 1.2rem;\n      font-weight: 600;\n      margin-bottom: 15px;\n      color: #333;\n    }\n\n    .role-features {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 15px;\n    }\n\n    .feature-card {\n      padding: 15px;\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n      text-align: center;\n      cursor: pointer;\n      transition: all 0.3s ease;\n    }\n\n    .feature-card:hover {\n      border-color: #667eea;\n      transform: translateY(-2px);\n    }\n\n    .feature-icon {\n      font-size: 2rem;\n      margin-bottom: 10px;\n      color: #667eea;\n    }\n\n    .settings-list {\n      list-style: none;\n      padding: 0;\n    }\n\n    .settings-item {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 12px 0;\n      border-bottom: 1px solid #f0f0f0;\n    }\n\n    .settings-item:last-child {\n      border-bottom: none;\n    }\n\n    .btn-primary {\n      background: #667eea;\n      color: white;\n      border: none;\n      padding: 10px 20px;\n      border-radius: 6px;\n      cursor: pointer;\n    }\n\n    .btn-secondary {\n      background: #f8f9fa;\n      color: #333;\n      border: 1px solid #ddd;\n      padding: 10px 20px;\n      border-radius: 6px;\n      cursor: pointer;\n    }\n  `]\n})\nexport class ProfileComponent implements OnInit {\n  currentUser: User | null = null;\n  isLoading = true;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadUserProfile();\n  }\n\n  loadUserProfile() {\n    this.currentUser = this.authService.currentUserValue;\n    this.isLoading = false;\n  }\n\n  // Role-based feature access\n  canAccessVendorDashboard(): boolean {\n    return this.authService.isVendor() || this.authService.isAdmin();\n  }\n\n  canAccessAdminPanel(): boolean {\n    return this.authService.isAdmin();\n  }\n\n  canManageProducts(): boolean {\n    return this.authService.isVendor() || this.authService.isAdmin();\n  }\n\n  canViewAnalytics(): boolean {\n    return this.authService.isVendor() || this.authService.isAdmin();\n  }\n\n  // Navigation methods\n  navigateToVendorDashboard() {\n    if (this.canAccessVendorDashboard()) {\n      this.router.navigate(['/vendor/dashboard']);\n    }\n  }\n\n  navigateToAdminPanel() {\n    if (this.canAccessAdminPanel()) {\n      this.router.navigate(['/admin/dashboard']);\n    }\n  }\n\n  navigateToOrders() {\n    this.router.navigate(['/account/orders']);\n  }\n\n  navigateToWishlist() {\n    this.router.navigate(['/wishlist']);\n  }\n\n  navigateToCart() {\n    this.router.navigate(['/cart']);\n  }\n\n  navigateToSettings() {\n    this.router.navigate(['/account/settings']);\n  }\n\n  editProfile() {\n    this.router.navigate(['/account/edit-profile']);\n  }\n\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/auth/login']);\n  }\n\n  getRoleDisplayName(): string {\n    switch (this.currentUser?.role) {\n      case 'customer':\n        return 'Customer';\n      case 'vendor':\n        return 'Vendor';\n      case 'admin':\n        return 'Administrator';\n      default:\n        return 'User';\n    }\n  }\n\n  getRoleFeatures(): any[] {\n    const baseFeatures = [\n      { icon: 'person-outline', title: 'Edit Profile', action: () => this.editProfile() },\n      { icon: 'bag-outline', title: 'My Orders', action: () => this.navigateToOrders() },\n      { icon: 'heart-outline', title: 'Wishlist', action: () => this.navigateToWishlist() },\n      { icon: 'cart-outline', title: 'Shopping Cart', action: () => this.navigateToCart() },\n      { icon: 'settings-outline', title: 'Settings', action: () => this.navigateToSettings() }\n    ];\n\n    if (this.canAccessVendorDashboard()) {\n      baseFeatures.push(\n        { icon: 'storefront-outline', title: 'Vendor Dashboard', action: () => this.navigateToVendorDashboard() },\n        { icon: 'cube-outline', title: 'Manage Products', action: () => this.router.navigate(['/vendor/products']) },\n        { icon: 'analytics-outline', title: 'Sales Analytics', action: () => this.router.navigate(['/vendor/analytics']) }\n      );\n    }\n\n    if (this.canAccessAdminPanel()) {\n      baseFeatures.push(\n        { icon: 'shield-outline', title: 'Admin Panel', action: () => this.navigateToAdminPanel() },\n        { icon: 'people-outline', title: 'User Management', action: () => this.router.navigate(['/admin/users']) },\n        { icon: 'bar-chart-outline', title: 'System Analytics', action: () => this.router.navigate(['/admin/analytics']) }\n      );\n    }\n\n    return baseFeatures;\n  }\n}\n", "<div class=\"profile-container\">\n  <h2>Profile Page</h2>\n  <p>Profile functionality coming soon...</p>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,WAAW,QAAQ,gBAAgB;;;;AAkI5C,OAAM,MAAOC,gBAAgB;EAI3BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IALhB,KAAAC,WAAW,GAAgB,IAAI;IAC/B,KAAAC,SAAS,GAAG,IAAI;EAKb;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACb,IAAI,CAACH,WAAW,GAAG,IAAI,CAACF,WAAW,CAACM,gBAAgB;IACpD,IAAI,CAACH,SAAS,GAAG,KAAK;EACxB;EAEA;EACAI,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAACP,WAAW,CAACQ,QAAQ,EAAE,IAAI,IAAI,CAACR,WAAW,CAACS,OAAO,EAAE;EAClE;EAEAC,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAACV,WAAW,CAACS,OAAO,EAAE;EACnC;EAEAE,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACX,WAAW,CAACQ,QAAQ,EAAE,IAAI,IAAI,CAACR,WAAW,CAACS,OAAO,EAAE;EAClE;EAEAG,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACZ,WAAW,CAACQ,QAAQ,EAAE,IAAI,IAAI,CAACR,WAAW,CAACS,OAAO,EAAE;EAClE;EAEA;EACAI,yBAAyBA,CAAA;IACvB,IAAI,IAAI,CAACN,wBAAwB,EAAE,EAAE;MACnC,IAAI,CAACN,MAAM,CAACa,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;;EAE/C;EAEAC,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACL,mBAAmB,EAAE,EAAE;MAC9B,IAAI,CAACT,MAAM,CAACa,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;;EAE9C;EAEAE,gBAAgBA,CAAA;IACd,IAAI,CAACf,MAAM,CAACa,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAEAG,kBAAkBA,CAAA;IAChB,IAAI,CAAChB,MAAM,CAACa,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACrC;EAEAI,cAAcA,CAAA;IACZ,IAAI,CAACjB,MAAM,CAACa,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;EAEAK,kBAAkBA,CAAA;IAChB,IAAI,CAAClB,MAAM,CAACa,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;EAC7C;EAEAM,WAAWA,CAAA;IACT,IAAI,CAACnB,MAAM,CAACa,QAAQ,CAAC,CAAC,uBAAuB,CAAC,CAAC;EACjD;EAEAO,MAAMA,CAAA;IACJ,IAAI,CAACrB,WAAW,CAACqB,MAAM,EAAE;IACzB,IAAI,CAACpB,MAAM,CAACa,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEAQ,kBAAkBA,CAAA;IAChB,QAAQ,IAAI,CAACpB,WAAW,EAAEqB,IAAI;MAC5B,KAAK,UAAU;QACb,OAAO,UAAU;MACnB,KAAK,QAAQ;QACX,OAAO,QAAQ;MACjB,KAAK,OAAO;QACV,OAAO,eAAe;MACxB;QACE,OAAO,MAAM;;EAEnB;EAEAC,eAAeA,CAAA;IACb,MAAMC,YAAY,GAAG,CACnB;MAAEC,IAAI,EAAE,gBAAgB;MAAEC,KAAK,EAAE,cAAc;MAAEC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACR,WAAW;IAAE,CAAE,EACnF;MAAEM,IAAI,EAAE,aAAa;MAAEC,KAAK,EAAE,WAAW;MAAEC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACZ,gBAAgB;IAAE,CAAE,EAClF;MAAEU,IAAI,EAAE,eAAe;MAAEC,KAAK,EAAE,UAAU;MAAEC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACX,kBAAkB;IAAE,CAAE,EACrF;MAAES,IAAI,EAAE,cAAc;MAAEC,KAAK,EAAE,eAAe;MAAEC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACV,cAAc;IAAE,CAAE,EACrF;MAAEQ,IAAI,EAAE,kBAAkB;MAAEC,KAAK,EAAE,UAAU;MAAEC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACT,kBAAkB;IAAE,CAAE,CACzF;IAED,IAAI,IAAI,CAACZ,wBAAwB,EAAE,EAAE;MACnCkB,YAAY,CAACI,IAAI,CACf;QAAEH,IAAI,EAAE,oBAAoB;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACf,yBAAyB;MAAE,CAAE,EACzG;QAAEa,IAAI,EAAE,cAAc;QAAEC,KAAK,EAAE,iBAAiB;QAAEC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAAC3B,MAAM,CAACa,QAAQ,CAAC,CAAC,kBAAkB,CAAC;MAAC,CAAE,EAC5G;QAAEY,IAAI,EAAE,mBAAmB;QAAEC,KAAK,EAAE,iBAAiB;QAAEC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAAC3B,MAAM,CAACa,QAAQ,CAAC,CAAC,mBAAmB,CAAC;MAAC,CAAE,CACnH;;IAGH,IAAI,IAAI,CAACJ,mBAAmB,EAAE,EAAE;MAC9Be,YAAY,CAACI,IAAI,CACf;QAAEH,IAAI,EAAE,gBAAgB;QAAEC,KAAK,EAAE,aAAa;QAAEC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACb,oBAAoB;MAAE,CAAE,EAC3F;QAAEW,IAAI,EAAE,gBAAgB;QAAEC,KAAK,EAAE,iBAAiB;QAAEC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAAC3B,MAAM,CAACa,QAAQ,CAAC,CAAC,cAAc,CAAC;MAAC,CAAE,EAC1G;QAAEY,IAAI,EAAE,mBAAmB;QAAEC,KAAK,EAAE,kBAAkB;QAAEC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAAC3B,MAAM,CAACa,QAAQ,CAAC,CAAC,kBAAkB,CAAC;MAAC,CAAE,CACnH;;IAGH,OAAOW,YAAY;EACrB;;;uBAhHW3B,gBAAgB,EAAAgC,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAhBrC,gBAAgB;MAAAsC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAR,EAAA,CAAAS,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrI3Bf,EADF,CAAAiB,cAAA,aAA+B,SACzB;UAAAjB,EAAA,CAAAkB,MAAA,mBAAY;UAAAlB,EAAA,CAAAmB,YAAA,EAAK;UACrBnB,EAAA,CAAAiB,cAAA,QAAG;UAAAjB,EAAA,CAAAkB,MAAA,2CAAoC;UACzClB,EADyC,CAAAmB,YAAA,EAAI,EACvC;;;qBDSMtD,YAAY,EAAEC,WAAW,EAAEC,WAAW;MAAAqD,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}