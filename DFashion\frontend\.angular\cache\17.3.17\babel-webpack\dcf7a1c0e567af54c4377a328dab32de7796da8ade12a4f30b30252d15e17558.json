{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { TrendingProductsComponent } from '../trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../new-arrivals/new-arrivals.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/product.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3];\nconst _c1 = a0 => [\"/category\", a0];\nfunction SidebarComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelement(1, \"img\", 11);\n    i0.ɵɵelementStart(2, \"div\", 12)(3, \"h5\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_7_Template_button_click_7_listener() {\n      const user_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.followUser(user_r2.id));\n    });\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", user_r2.avatar, i0.ɵɵsanitizeUrl)(\"alt\", user_r2.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r2.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r2.followedBy);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", user_r2.isFollowing ? \"Following\" : \"Follow\", \" \");\n  }\n}\nfunction SidebarComponent_div_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17);\n    i0.ɵɵelement(2, \"div\", 18)(3, \"div\", 19)(4, \"div\", 20);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SidebarComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtemplate(1, SidebarComponent_div_11_div_1_Template, 5, 0, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction SidebarComponent_div_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵelement(1, \"img\", 11);\n    i0.ɵɵelementStart(2, \"div\", 23)(3, \"h5\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 24)(8, \"span\", 25);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 26);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_12_div_1_Template_button_click_12_listener() {\n      const influencer_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.followInfluencer(influencer_r5.id));\n    });\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const influencer_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", influencer_r5.avatar, i0.ɵɵsanitizeUrl)(\"alt\", influencer_r5.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(influencer_r5.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.formatFollowerCount(influencer_r5.followersCount), \" followers\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", influencer_r5.postsCount, \" posts\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", influencer_r5.engagement, \"% engagement\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", influencer_r5.isFollowing ? \"Following\" : \"Follow\", \" \");\n  }\n}\nfunction SidebarComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, SidebarComponent_div_12_div_1_Template, 14, 7, \"div\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.topInfluencers);\n  }\n}\nfunction SidebarComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"ion-icon\", 28);\n    i0.ɵɵelementStart(2, \"h3\", 29);\n    i0.ɵɵtext(3, \"No Influencers Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 30);\n    i0.ɵɵtext(5, \"Check back later for top fashion influencers\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SidebarComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelement(1, \"img\", 11);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c1, category_r6.slug));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", category_r6.image, i0.ɵɵsanitizeUrl)(\"alt\", category_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r6.name);\n  }\n}\nexport class SidebarComponent {\n  constructor(productService, router) {\n    this.productService = productService;\n    this.router = router;\n    this.suggestedUsers = [];\n    this.trendingProducts = [];\n    this.topInfluencers = [];\n    this.categories = [];\n    this.isLoadingInfluencers = false;\n  }\n  ngOnInit() {\n    this.loadSuggestedUsers();\n    this.loadTrendingProducts();\n    this.loadTopInfluencers();\n    this.loadCategories();\n  }\n  loadSuggestedUsers() {\n    // Load from API - empty for now\n    this.suggestedUsers = [];\n  }\n  loadTrendingProducts() {\n    // Load from API - empty for now\n    this.trendingProducts = [];\n  }\n  loadTopInfluencers() {\n    this.isLoadingInfluencers = true;\n    // Simulate API call\n    setTimeout(() => {\n      this.topInfluencers = [{\n        id: '1',\n        username: '@fashionista_maya',\n        fullName: 'Maya Sharma',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=100',\n        followersCount: 125000,\n        postsCount: 342,\n        engagement: 8.5,\n        isFollowing: false\n      }, {\n        id: '2',\n        username: '@style_guru_raj',\n        fullName: 'Raj Patel',\n        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100',\n        followersCount: 89000,\n        postsCount: 198,\n        engagement: 7.2,\n        isFollowing: true\n      }, {\n        id: '3',\n        username: '@trendy_priya',\n        fullName: 'Priya Singh',\n        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100',\n        followersCount: 67000,\n        postsCount: 156,\n        engagement: 9.1,\n        isFollowing: false\n      }];\n      this.isLoadingInfluencers = false;\n    }, 1500);\n  }\n  loadCategories() {\n    this.categories = [{\n      name: 'Women',\n      slug: 'women',\n      image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=100'\n    }, {\n      name: 'Men',\n      slug: 'men',\n      image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=100'\n    }, {\n      name: 'Kids',\n      slug: 'children',\n      image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=100'\n    }, {\n      name: 'Ethnic',\n      slug: 'ethnic',\n      image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=100'\n    }];\n  }\n  formatFollowerCount(count) {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'k';\n    }\n    return count.toString();\n  }\n  followUser(userId) {\n    const user = this.suggestedUsers.find(u => u.id === userId);\n    if (user) {\n      user.isFollowing = !user.isFollowing;\n    }\n  }\n  followInfluencer(influencerId) {\n    const influencer = this.topInfluencers.find(i => i.id === influencerId);\n    if (influencer) {\n      influencer.isFollowing = !influencer.isFollowing;\n    }\n  }\n  quickBuy(productId) {\n    console.log('Quick buy product:', productId);\n    // TODO: Implement quick buy functionality\n  }\n  browseCategory(categorySlug) {\n    console.log('Browse category:', categorySlug);\n    this.router.navigate(['/category', categorySlug]);\n  }\n  static {\n    this.ɵfac = function SidebarComponent_Factory(t) {\n      return new (t || SidebarComponent)(i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SidebarComponent,\n      selectors: [[\"app-sidebar\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 19,\n      vars: 5,\n      consts: [[1, \"sidebar\"], [1, \"suggestions\"], [\"class\", \"suggestion-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"influencers\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"categories\"], [1, \"category-grid\"], [\"class\", \"category-item\", \"routerLinkActive\", \"active\", \"tabindex\", \"0\", 3, \"routerLink\", 4, \"ngFor\", \"ngForOf\"], [1, \"suggestion-item\"], [3, \"src\", \"alt\"], [1, \"suggestion-info\"], [1, \"follow-btn\", 3, \"click\"], [1, \"loading-container\"], [\"class\", \"loading-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-card\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [\"class\", \"influencer-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"influencer-item\"], [1, \"influencer-info\"], [1, \"influencer-stats\"], [1, \"posts-count\"], [1, \"engagement\"], [1, \"empty-container\"], [\"name\", \"people-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"], [\"routerLinkActive\", \"active\", \"tabindex\", \"0\", 1, \"category-item\", 3, \"routerLink\"]],\n      template: function SidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"aside\", 0);\n          i0.ɵɵelement(1, \"app-trending-products\")(2, \"app-featured-brands\")(3, \"app-new-arrivals\");\n          i0.ɵɵelementStart(4, \"div\", 1)(5, \"h3\");\n          i0.ɵɵtext(6, \"Suggested for you\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, SidebarComponent_div_7_Template, 9, 5, \"div\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 3)(9, \"h3\");\n          i0.ɵɵtext(10, \"Top Fashion Influencers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(11, SidebarComponent_div_11_Template, 2, 2, \"div\", 4)(12, SidebarComponent_div_12_Template, 2, 1, \"div\", 5)(13, SidebarComponent_div_13_Template, 6, 0, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 7)(15, \"h3\");\n          i0.ɵɵtext(16, \"Shop by Category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 8);\n          i0.ɵɵtemplate(18, SidebarComponent_div_18_Template, 4, 6, \"div\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.suggestedUsers);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingInfluencers);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingInfluencers && ctx.topInfluencers.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingInfluencers && ctx.topInfluencers.length === 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, RouterModule, i2.RouterLink, i2.RouterLinkActive, IonicModule, i4.IonIcon, i4.RouterLinkDelegate, TrendingProductsComponent, FeaturedBrandsComponent, NewArrivalsComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.sidebar[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 80px;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n  max-height: calc(100vh - 100px);\\n  overflow-y: auto;\\n  padding-right: 8px;\\n}\\n\\napp-view-add-stories[_ngcontent-%COMP%], app-trending-products[_ngcontent-%COMP%], app-featured-brands[_ngcontent-%COMP%], app-new-arrivals[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: block;\\n  margin-bottom: 24px;\\n}\\n\\n.suggestions[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n  box-shadow: 0 8px 32px rgba(255, 107, 107, 0.2);\\n}\\n\\n.categories[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n  box-shadow: 0 8px 32px rgba(0, 210, 211, 0.2);\\n}\\n\\n.trending[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  padding: 16px;\\n}\\n\\n.influencers[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDCA1\\\";\\n  font-size: 28px;\\n}\\n\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDECD\\uFE0F\\\";\\n  font-size: 28px;\\n}\\n\\n.trending[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin-bottom: 16px;\\n  color: #8e8e8e;\\n}\\n\\n.influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDC51\\\";\\n  font-size: 28px;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 16px;\\n  margin-bottom: 16px;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 16px;\\n  margin-bottom: 16px;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);\\n}\\n\\n.influencer-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n\\n.suggestion-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.suggestion-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin-bottom: 2px;\\n}\\n\\n.suggestion-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #8e8e8e;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 16px;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid rgba(255, 255, 255, 0.3);\\n  transition: transform 0.3s ease;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.influencer-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.influencer-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 4px;\\n}\\n\\n.influencer-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.7);\\n  margin-bottom: 8px;\\n}\\n\\n.influencer-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n  margin-bottom: 12px;\\n}\\n\\n.influencer-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: rgba(255, 255, 255, 0.6);\\n  background: rgba(255, 255, 255, 0.1);\\n  padding: 4px 8px;\\n  border-radius: 8px;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  -webkit-backdrop-filter: blur(5px);\\n          backdrop-filter: blur(5px);\\n}\\n\\n.follow-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 8px 16px;\\n  border-radius: 8px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  align-self: flex-start;\\n}\\n\\n.follow-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n  transform: translateY(-2px);\\n}\\n\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  padding: 16px;\\n  margin-bottom: 16px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  background: rgba(255, 255, 255, 0.2);\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  border-radius: 6px;\\n  margin-bottom: 8px;\\n}\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0%, 100% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n.influencers[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.influencers[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: rgba(255, 255, 255, 0.4);\\n  margin-bottom: 16px;\\n}\\n.influencers[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 8px;\\n}\\n.influencers[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.trending-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n  padding-bottom: 16px;\\n  border-bottom: 1px solid #f1f5f9;\\n}\\n\\n.trending-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n  margin-bottom: 0;\\n  padding-bottom: 0;\\n}\\n\\n.trending-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 8px;\\n  object-fit: cover;\\n}\\n\\n.trending-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.trending-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin-bottom: 4px;\\n}\\n\\n.trending-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: var(--primary-color);\\n  margin-bottom: 8px;\\n}\\n\\n.original-price[_ngcontent-%COMP%] {\\n  text-decoration: line-through;\\n  color: #8e8e8e;\\n  font-weight: 400;\\n  margin-left: 4px;\\n}\\n\\n.trending-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n\\n.trending-badge[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 600;\\n  background: #fef3c7;\\n  color: #92400e;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n\\n.views[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #8e8e8e;\\n}\\n\\n.quick-buy-btn[_ngcontent-%COMP%] {\\n  background: #f1f5f9;\\n  color: #64748b;\\n  border: 1px solid #e2e8f0;\\n  padding: 6px 12px;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n  align-self: flex-start;\\n}\\n\\n.quick-buy-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-color);\\n  color: #fff;\\n  border-color: var(--primary-color);\\n}\\n\\n.categories[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1f2937;\\n  margin-bottom: 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDECD\\uFE0F\\\";\\n  font-size: 18px;\\n}\\n\\n.category-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 12px;\\n}\\n\\n.category-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 12px;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  text-decoration: none;\\n  color: inherit;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.category-item[_ngcontent-%COMP%]:hover {\\n  background: #f8fafc;\\n  border-color: var(--primary-color);\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n.category-item.active[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  border-color: var(--primary-color);\\n  color: white;\\n}\\n\\n.category-item.active[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n.category-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 8px;\\n  object-fit: cover;\\n  margin-bottom: 8px;\\n  transition: transform 0.3s ease;\\n}\\n\\n.category-item[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.category-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 500;\\n  text-align: center;\\n  transition: color 0.3s ease;\\n}\\n\\n.category-item[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.5s ease;\\n}\\n\\n.category-item[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n\\n@media (max-width: 1024px) {\\n  .sidebar[_ngcontent-%COMP%] {\\n    order: -1;\\n    position: static;\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n    gap: 20px;\\n    max-height: none;\\n    overflow-y: visible;\\n  }\\n  app-view-add-stories[_ngcontent-%COMP%], app-trending-products[_ngcontent-%COMP%], app-featured-brands[_ngcontent-%COMP%], app-new-arrivals[_ngcontent-%COMP%] {\\n    margin-bottom: 16px;\\n  }\\n  .influencers[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .influencer-item[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .sidebar[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  app-view-add-stories[_ngcontent-%COMP%], app-trending-products[_ngcontent-%COMP%], app-featured-brands[_ngcontent-%COMP%], app-new-arrivals[_ngcontent-%COMP%] {\\n    margin-bottom: 12px;\\n  }\\n  .influencers[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .influencer-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n    padding: 16px;\\n  }\\n  .influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n    margin-bottom: 12px;\\n  }\\n  .influencer-stats[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    justify-content: center;\\n    gap: 8px;\\n  }\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "IonicModule", "TrendingProductsComponent", "FeaturedBrandsComponent", "NewArrivalsComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "SidebarComponent_div_7_Template_button_click_7_listener", "user_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "followUser", "id", "ɵɵadvance", "ɵɵproperty", "avatar", "ɵɵsanitizeUrl", "fullName", "ɵɵtextInterpolate", "username", "<PERSON><PERSON><PERSON>", "ɵɵtextInterpolate1", "isFollowing", "ɵɵtemplate", "SidebarComponent_div_11_div_1_Template", "ɵɵpureFunction0", "_c0", "SidebarComponent_div_12_div_1_Template_button_click_12_listener", "influencer_r5", "_r4", "followInfluencer", "formatFollowerCount", "followersCount", "postsCount", "engagement", "SidebarComponent_div_12_div_1_Template", "topInfluencers", "ɵɵpureFunction1", "_c1", "category_r6", "slug", "image", "name", "SidebarComponent", "constructor", "productService", "router", "suggestedUsers", "trendingProducts", "categories", "isLoadingInfluencers", "ngOnInit", "loadSuggestedUsers", "loadTrendingProducts", "loadTopInfluencers", "loadCategories", "setTimeout", "count", "toFixed", "toString", "userId", "user", "find", "u", "influencerId", "influencer", "i", "quickBuy", "productId", "console", "log", "browseCategory", "categorySlug", "navigate", "ɵɵdirectiveInject", "i1", "ProductService", "i2", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SidebarComponent_Template", "rf", "ctx", "SidebarComponent_div_7_Template", "SidebarComponent_div_11_Template", "SidebarComponent_div_12_Template", "SidebarComponent_div_13_Template", "SidebarComponent_div_18_Template", "length", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "RouterLink", "RouterLinkActive", "i4", "IonIcon", "RouterLinkDelegate", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\sidebar\\sidebar.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\sidebar\\sidebar.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\n\nimport { ProductService } from '../../../../core/services/product.service';\nimport { Product } from '../../../../core/models/product.model';\nimport { TrendingProductsComponent } from '../trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../new-arrivals/new-arrivals.component';\n\n@Component({\n  selector: 'app-sidebar',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    IonicModule,\n    TrendingProductsComponent,\n    FeaturedBrandsComponent,\n    NewArrivalsComponent\n  ],\n  templateUrl: './sidebar.component.html',\n  styleUrls: ['./sidebar.component.scss']\n})\nexport class SidebarComponent implements OnInit {\n  suggestedUsers: any[] = [];\n  trendingProducts: Product[] = [];\n  topInfluencers: any[] = [];\n  categories: any[] = [];\n  isLoadingInfluencers: boolean = false;\n\n  constructor(\n    private productService: ProductService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadSuggestedUsers();\n    this.loadTrendingProducts();\n    this.loadTopInfluencers();\n    this.loadCategories();\n  }\n\n  loadSuggestedUsers() {\n    // Load from API - empty for now\n    this.suggestedUsers = [];\n  }\n\n  loadTrendingProducts() {\n    // Load from API - empty for now\n    this.trendingProducts = [];\n  }\n\n  loadTopInfluencers() {\n    this.isLoadingInfluencers = true;\n\n    // Simulate API call\n    setTimeout(() => {\n      this.topInfluencers = [\n        {\n          id: '1',\n          username: '@fashionista_maya',\n          fullName: 'Maya Sharma',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=100',\n          followersCount: 125000,\n          postsCount: 342,\n          engagement: 8.5,\n          isFollowing: false\n        },\n        {\n          id: '2',\n          username: '@style_guru_raj',\n          fullName: 'Raj Patel',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100',\n          followersCount: 89000,\n          postsCount: 198,\n          engagement: 7.2,\n          isFollowing: true\n        },\n        {\n          id: '3',\n          username: '@trendy_priya',\n          fullName: 'Priya Singh',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100',\n          followersCount: 67000,\n          postsCount: 156,\n          engagement: 9.1,\n          isFollowing: false\n        }\n      ];\n      this.isLoadingInfluencers = false;\n    }, 1500);\n  }\n\n  loadCategories() {\n    this.categories = [\n      {\n        name: 'Women',\n        slug: 'women',\n        image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=100'\n      },\n      {\n        name: 'Men',\n        slug: 'men',\n        image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=100'\n      },\n      {\n        name: 'Kids',\n        slug: 'children',\n        image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=100'\n      },\n      {\n        name: 'Ethnic',\n        slug: 'ethnic',\n        image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=100'\n      }\n    ];\n  }\n\n  formatFollowerCount(count: number): string {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'k';\n    }\n    return count.toString();\n  }\n\n  followUser(userId: string) {\n    const user = this.suggestedUsers.find(u => u.id === userId);\n    if (user) {\n      user.isFollowing = !user.isFollowing;\n    }\n  }\n\n  followInfluencer(influencerId: string) {\n    const influencer = this.topInfluencers.find(i => i.id === influencerId);\n    if (influencer) {\n      influencer.isFollowing = !influencer.isFollowing;\n    }\n  }\n\n  quickBuy(productId: string) {\n    console.log('Quick buy product:', productId);\n    // TODO: Implement quick buy functionality\n  }\n\n  browseCategory(categorySlug: string) {\n    console.log('Browse category:', categorySlug);\n    this.router.navigate(['/category', categorySlug]);\n  }\n}\n", "<aside class=\"sidebar\">\n  <!-- Instagram-style Stories -->\n \n\n  <!-- Trending Products Section -->\n  <app-trending-products></app-trending-products>\n\n  <!-- Featured Brands Section -->\n  <app-featured-brands></app-featured-brands>\n\n  <!-- New Arrivals Section -->\n  <app-new-arrivals></app-new-arrivals>\n\n  <!-- Suggested for you -->\n  <div class=\"suggestions\">\n    <h3>Suggested for you</h3>\n    <div *ngFor=\"let user of suggestedUsers\" class=\"suggestion-item\">\n      <img [src]=\"user.avatar\" [alt]=\"user.fullName\">\n      <div class=\"suggestion-info\">\n        <h5>{{ user.username }}</h5>\n        <p>{{ user.followedBy }}</p>\n      </div>\n      <button class=\"follow-btn\" (click)=\"followUser(user.id)\">\n        {{ user.isFollowing ? 'Following' : 'Follow' }}\n      </button>\n    </div>\n  </div>\n\n  <!-- Top Fashion Influencers -->\n  <div class=\"influencers\">\n    <h3>Top Fashion Influencers</h3>\n\n    <!-- Loading State -->\n    <div *ngIf=\"isLoadingInfluencers\" class=\"loading-container\">\n      <div class=\"loading-card\" *ngFor=\"let item of [1,2,3]\">\n        <div class=\"loading-content\">\n          <div class=\"loading-line short\"></div>\n          <div class=\"loading-line medium\"></div>\n          <div class=\"loading-line long\"></div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Influencers List -->\n    <div *ngIf=\"!isLoadingInfluencers && topInfluencers.length > 0\">\n      <div *ngFor=\"let influencer of topInfluencers\" class=\"influencer-item\">\n        <img [src]=\"influencer.avatar\" [alt]=\"influencer.fullName\">\n        <div class=\"influencer-info\">\n          <h5>{{ influencer.username }}</h5>\n          <p>{{ formatFollowerCount(influencer.followersCount) }} followers</p>\n          <div class=\"influencer-stats\">\n            <span class=\"posts-count\">{{ influencer.postsCount }} posts</span>\n            <span class=\"engagement\">{{ influencer.engagement }}% engagement</span>\n          </div>\n          <button class=\"follow-btn\" (click)=\"followInfluencer(influencer.id)\">\n            {{ influencer.isFollowing ? 'Following' : 'Follow' }}\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Empty State -->\n    <div *ngIf=\"!isLoadingInfluencers && topInfluencers.length === 0\" class=\"empty-container\">\n      <ion-icon name=\"people-outline\" class=\"empty-icon\"></ion-icon>\n      <h3 class=\"empty-title\">No Influencers Found</h3>\n      <p class=\"empty-message\">Check back later for top fashion influencers</p>\n    </div>\n  </div>\n\n  <!-- Shop by Category -->\n  <div class=\"categories\">\n    <h3>Shop by Category</h3>\n    <div class=\"category-grid\">\n      <div\n        *ngFor=\"let category of categories\"\n        class=\"category-item\"\n        [routerLink]=\"['/category', category.slug]\"\n        routerLinkActive=\"active\"\n        tabindex=\"0\"\n      >\n        <img [src]=\"category.image\" [alt]=\"category.name\">\n        <span>{{ category.name }}</span>\n      </div>\n    </div>\n  </div>\n\n\n</aside>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,WAAW,QAAQ,gBAAgB;AAI5C,SAASC,yBAAyB,QAAQ,kDAAkD;AAC5F,SAASC,uBAAuB,QAAQ,8CAA8C;AACtF,SAASC,oBAAoB,QAAQ,wCAAwC;;;;;;;;;;;ICOzEC,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAE,SAAA,cAA+C;IAE7CF,EADF,CAAAC,cAAA,cAA6B,SACvB;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5BJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAqB;IAC1BH,EAD0B,CAAAI,YAAA,EAAI,EACxB;IACNJ,EAAA,CAAAC,cAAA,iBAAyD;IAA9BD,EAAA,CAAAK,UAAA,mBAAAC,wDAAA;MAAA,MAAAC,OAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,UAAA,CAAAP,OAAA,CAAAQ,EAAA,CAAmB;IAAA,EAAC;IACtDf,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;;;;IARCJ,EAAA,CAAAgB,SAAA,EAAmB;IAAChB,EAApB,CAAAiB,UAAA,QAAAV,OAAA,CAAAW,MAAA,EAAAlB,EAAA,CAAAmB,aAAA,CAAmB,QAAAZ,OAAA,CAAAa,QAAA,CAAsB;IAExCpB,EAAA,CAAAgB,SAAA,GAAmB;IAAnBhB,EAAA,CAAAqB,iBAAA,CAAAd,OAAA,CAAAe,QAAA,CAAmB;IACpBtB,EAAA,CAAAgB,SAAA,GAAqB;IAArBhB,EAAA,CAAAqB,iBAAA,CAAAd,OAAA,CAAAgB,UAAA,CAAqB;IAGxBvB,EAAA,CAAAgB,SAAA,GACF;IADEhB,EAAA,CAAAwB,kBAAA,MAAAjB,OAAA,CAAAkB,WAAA,+BACF;;;;;IAWEzB,EADF,CAAAC,cAAA,cAAuD,cACxB;IAG3BD,EAFA,CAAAE,SAAA,cAAsC,cACC,cACF;IAEzCF,EADE,CAAAI,YAAA,EAAM,EACF;;;;;IAPRJ,EAAA,CAAAC,cAAA,cAA4D;IAC1DD,EAAA,CAAA0B,UAAA,IAAAC,sCAAA,kBAAuD;IAOzD3B,EAAA,CAAAI,YAAA,EAAM;;;IAPuCJ,EAAA,CAAAgB,SAAA,EAAU;IAAVhB,EAAA,CAAAiB,UAAA,YAAAjB,EAAA,CAAA4B,eAAA,IAAAC,GAAA,EAAU;;;;;;IAWrD7B,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAE,SAAA,cAA2D;IAEzDF,EADF,CAAAC,cAAA,cAA6B,SACvB;IAAAD,EAAA,CAAAG,MAAA,GAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAA8D;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEnEJ,EADF,CAAAC,cAAA,cAA8B,eACF;IAAAD,EAAA,CAAAG,MAAA,GAAiC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAClEJ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAG,MAAA,IAAuC;IAClEH,EADkE,CAAAI,YAAA,EAAO,EACnE;IACNJ,EAAA,CAAAC,cAAA,kBAAqE;IAA1CD,EAAA,CAAAK,UAAA,mBAAAyB,gEAAA;MAAA,MAAAC,aAAA,GAAA/B,EAAA,CAAAQ,aAAA,CAAAwB,GAAA,EAAAtB,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAsB,gBAAA,CAAAF,aAAA,CAAAhB,EAAA,CAA+B;IAAA,EAAC;IAClEf,EAAA,CAAAG,MAAA,IACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;;;;;IAZCJ,EAAA,CAAAgB,SAAA,EAAyB;IAAChB,EAA1B,CAAAiB,UAAA,QAAAc,aAAA,CAAAb,MAAA,EAAAlB,EAAA,CAAAmB,aAAA,CAAyB,QAAAY,aAAA,CAAAX,QAAA,CAA4B;IAEpDpB,EAAA,CAAAgB,SAAA,GAAyB;IAAzBhB,EAAA,CAAAqB,iBAAA,CAAAU,aAAA,CAAAT,QAAA,CAAyB;IAC1BtB,EAAA,CAAAgB,SAAA,GAA8D;IAA9DhB,EAAA,CAAAwB,kBAAA,KAAAb,MAAA,CAAAuB,mBAAA,CAAAH,aAAA,CAAAI,cAAA,gBAA8D;IAErCnC,EAAA,CAAAgB,SAAA,GAAiC;IAAjChB,EAAA,CAAAwB,kBAAA,KAAAO,aAAA,CAAAK,UAAA,WAAiC;IAClCpC,EAAA,CAAAgB,SAAA,GAAuC;IAAvChB,EAAA,CAAAwB,kBAAA,KAAAO,aAAA,CAAAM,UAAA,iBAAuC;IAGhErC,EAAA,CAAAgB,SAAA,GACF;IADEhB,EAAA,CAAAwB,kBAAA,MAAAO,aAAA,CAAAN,WAAA,+BACF;;;;;IAZNzB,EAAA,CAAAC,cAAA,UAAgE;IAC9DD,EAAA,CAAA0B,UAAA,IAAAY,sCAAA,mBAAuE;IAczEtC,EAAA,CAAAI,YAAA,EAAM;;;;IAdwBJ,EAAA,CAAAgB,SAAA,EAAiB;IAAjBhB,EAAA,CAAAiB,UAAA,YAAAN,MAAA,CAAA4B,cAAA,CAAiB;;;;;IAiB/CvC,EAAA,CAAAC,cAAA,cAA0F;IACxFD,EAAA,CAAAE,SAAA,mBAA8D;IAC9DF,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAG,MAAA,2BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACjDJ,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAG,MAAA,mDAA4C;IACvEH,EADuE,CAAAI,YAAA,EAAI,EACrE;;;;;IAOJJ,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAE,SAAA,cAAkD;IAClDF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAC3BH,EAD2B,CAAAI,YAAA,EAAO,EAC5B;;;;IANJJ,EAAA,CAAAiB,UAAA,eAAAjB,EAAA,CAAAwC,eAAA,IAAAC,GAAA,EAAAC,WAAA,CAAAC,IAAA,EAA2C;IAItC3C,EAAA,CAAAgB,SAAA,EAAsB;IAAChB,EAAvB,CAAAiB,UAAA,QAAAyB,WAAA,CAAAE,KAAA,EAAA5C,EAAA,CAAAmB,aAAA,CAAsB,QAAAuB,WAAA,CAAAG,IAAA,CAAsB;IAC3C7C,EAAA,CAAAgB,SAAA,GAAmB;IAAnBhB,EAAA,CAAAqB,iBAAA,CAAAqB,WAAA,CAAAG,IAAA,CAAmB;;;ADxDjC,OAAM,MAAOC,gBAAgB;EAO3BC,YACUC,cAA8B,EAC9BC,MAAc;IADd,KAAAD,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IARhB,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,gBAAgB,GAAc,EAAE;IAChC,KAAAZ,cAAc,GAAU,EAAE;IAC1B,KAAAa,UAAU,GAAU,EAAE;IACtB,KAAAC,oBAAoB,GAAY,KAAK;EAKlC;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAH,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACL,cAAc,GAAG,EAAE;EAC1B;EAEAM,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAACL,gBAAgB,GAAG,EAAE;EAC5B;EAEAM,kBAAkBA,CAAA;IAChB,IAAI,CAACJ,oBAAoB,GAAG,IAAI;IAEhC;IACAM,UAAU,CAAC,MAAK;MACd,IAAI,CAACpB,cAAc,GAAG,CACpB;QACExB,EAAE,EAAE,GAAG;QACPO,QAAQ,EAAE,mBAAmB;QAC7BF,QAAQ,EAAE,aAAa;QACvBF,MAAM,EAAE,oEAAoE;QAC5EiB,cAAc,EAAE,MAAM;QACtBC,UAAU,EAAE,GAAG;QACfC,UAAU,EAAE,GAAG;QACfZ,WAAW,EAAE;OACd,EACD;QACEV,EAAE,EAAE,GAAG;QACPO,QAAQ,EAAE,iBAAiB;QAC3BF,QAAQ,EAAE,WAAW;QACrBF,MAAM,EAAE,oEAAoE;QAC5EiB,cAAc,EAAE,KAAK;QACrBC,UAAU,EAAE,GAAG;QACfC,UAAU,EAAE,GAAG;QACfZ,WAAW,EAAE;OACd,EACD;QACEV,EAAE,EAAE,GAAG;QACPO,QAAQ,EAAE,eAAe;QACzBF,QAAQ,EAAE,aAAa;QACvBF,MAAM,EAAE,oEAAoE;QAC5EiB,cAAc,EAAE,KAAK;QACrBC,UAAU,EAAE,GAAG;QACfC,UAAU,EAAE,GAAG;QACfZ,WAAW,EAAE;OACd,CACF;MACD,IAAI,CAAC4B,oBAAoB,GAAG,KAAK;IACnC,CAAC,EAAE,IAAI,CAAC;EACV;EAEAK,cAAcA,CAAA;IACZ,IAAI,CAACN,UAAU,GAAG,CAChB;MACEP,IAAI,EAAE,OAAO;MACbF,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;KACR,EACD;MACEC,IAAI,EAAE,KAAK;MACXF,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE;KACR,EACD;MACEC,IAAI,EAAE,MAAM;MACZF,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE;KACR,EACD;MACEC,IAAI,EAAE,QAAQ;MACdF,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;KACR,CACF;EACH;EAEAV,mBAAmBA,CAAC0B,KAAa;IAC/B,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEAhD,UAAUA,CAACiD,MAAc;IACvB,MAAMC,IAAI,GAAG,IAAI,CAACd,cAAc,CAACe,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnD,EAAE,KAAKgD,MAAM,CAAC;IAC3D,IAAIC,IAAI,EAAE;MACRA,IAAI,CAACvC,WAAW,GAAG,CAACuC,IAAI,CAACvC,WAAW;;EAExC;EAEAQ,gBAAgBA,CAACkC,YAAoB;IACnC,MAAMC,UAAU,GAAG,IAAI,CAAC7B,cAAc,CAAC0B,IAAI,CAACI,CAAC,IAAIA,CAAC,CAACtD,EAAE,KAAKoD,YAAY,CAAC;IACvE,IAAIC,UAAU,EAAE;MACdA,UAAU,CAAC3C,WAAW,GAAG,CAAC2C,UAAU,CAAC3C,WAAW;;EAEpD;EAEA6C,QAAQA,CAACC,SAAiB;IACxBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,SAAS,CAAC;IAC5C;EACF;EAEAG,cAAcA,CAACC,YAAoB;IACjCH,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEE,YAAY,CAAC;IAC7C,IAAI,CAAC1B,MAAM,CAAC2B,QAAQ,CAAC,CAAC,WAAW,EAAED,YAAY,CAAC,CAAC;EACnD;;;uBA5HW7B,gBAAgB,EAAA9C,EAAA,CAAA6E,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA/E,EAAA,CAAA6E,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAhBnC,gBAAgB;MAAAoC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAApF,EAAA,CAAAqF,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCzB7B3F,EAAA,CAAAC,cAAA,eAAuB;UAWrBD,EANA,CAAAE,SAAA,4BAA+C,0BAGJ,uBAGN;UAInCF,EADF,CAAAC,cAAA,aAAyB,SACnB;UAAAD,EAAA,CAAAG,MAAA,wBAAiB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC1BJ,EAAA,CAAA0B,UAAA,IAAAmE,+BAAA,iBAAiE;UAUnE7F,EAAA,CAAAI,YAAA,EAAM;UAIJJ,EADF,CAAAC,cAAA,aAAyB,SACnB;UAAAD,EAAA,CAAAG,MAAA,+BAAuB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAgChCJ,EA7BA,CAAA0B,UAAA,KAAAoE,gCAAA,iBAA4D,KAAAC,gCAAA,iBAWI,KAAAC,gCAAA,iBAkB0B;UAK5FhG,EAAA,CAAAI,YAAA,EAAM;UAIJJ,EADF,CAAAC,cAAA,cAAwB,UAClB;UAAAD,EAAA,CAAAG,MAAA,wBAAgB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACzBJ,EAAA,CAAAC,cAAA,cAA2B;UACzBD,EAAA,CAAA0B,UAAA,KAAAuE,gCAAA,iBAMC;UAQPjG,EAJI,CAAAI,YAAA,EAAM,EACF,EAGA;;;UAvEkBJ,EAAA,CAAAgB,SAAA,GAAiB;UAAjBhB,EAAA,CAAAiB,UAAA,YAAA2E,GAAA,CAAA1C,cAAA,CAAiB;UAiBjClD,EAAA,CAAAgB,SAAA,GAA0B;UAA1BhB,EAAA,CAAAiB,UAAA,SAAA2E,GAAA,CAAAvC,oBAAA,CAA0B;UAW1BrD,EAAA,CAAAgB,SAAA,EAAwD;UAAxDhB,EAAA,CAAAiB,UAAA,UAAA2E,GAAA,CAAAvC,oBAAA,IAAAuC,GAAA,CAAArD,cAAA,CAAA2D,MAAA,KAAwD;UAkBxDlG,EAAA,CAAAgB,SAAA,EAA0D;UAA1DhB,EAAA,CAAAiB,UAAA,UAAA2E,GAAA,CAAAvC,oBAAA,IAAAuC,GAAA,CAAArD,cAAA,CAAA2D,MAAA,OAA0D;UAYvClG,EAAA,CAAAgB,SAAA,GAAa;UAAbhB,EAAA,CAAAiB,UAAA,YAAA2E,GAAA,CAAAxC,UAAA,CAAa;;;qBD3DtC1D,YAAY,EAAAyG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ1G,YAAY,EAAAqF,EAAA,CAAAsB,UAAA,EAAAtB,EAAA,CAAAuB,gBAAA,EACZ3G,WAAW,EAAA4G,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,kBAAA,EACX7G,yBAAyB,EACzBC,uBAAuB,EACvBC,oBAAoB;MAAA4G,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}