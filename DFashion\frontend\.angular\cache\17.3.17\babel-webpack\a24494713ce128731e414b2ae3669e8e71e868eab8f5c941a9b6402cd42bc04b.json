{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/cart.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction CheckoutComponent_div_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"span\");\n    i0.ɵɵtext(2, \"Discount\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 36);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"-\\u20B9\", i0.ɵɵpipeBind1(5, 1, ctx_r0.cartSummary.discount), \"\");\n  }\n}\nexport let CheckoutComponent = /*#__PURE__*/(() => {\n  class CheckoutComponent {\n    constructor(cartService, router) {\n      this.cartService = cartService;\n      this.router = router;\n      this.cartSummary = null;\n      this.tax = 0;\n      this.paymentMethod = 'card';\n      this.shippingAddress = {\n        firstName: '',\n        lastName: '',\n        email: '',\n        phone: '',\n        address: '',\n        city: '',\n        state: '',\n        pinCode: ''\n      };\n    }\n    ngOnInit() {\n      this.cartService.cartSummary$.subscribe(summary => {\n        this.cartSummary = summary;\n        this.tax = summary ? Math.round(summary.total * 0.18) : 0; // 18% GST\n      });\n    }\n    getTotal() {\n      return this.cartSummary ? this.cartSummary.total + this.tax : 0;\n    }\n    isFormValid() {\n      return !!(this.shippingAddress.firstName && this.shippingAddress.lastName && this.shippingAddress.email && this.shippingAddress.phone && this.shippingAddress.address && this.shippingAddress.city && this.shippingAddress.state && this.shippingAddress.pinCode && this.paymentMethod);\n    }\n    placeOrder() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        if (_this.isFormValid()) {\n          // Mock order placement\n          alert('Order placed successfully! (Demo)');\n          _this.cartService.clearCartAPI().subscribe({\n            next: () => {\n              _this.router.navigate(['/']);\n            },\n            error: error => {\n              console.error('Error clearing cart:', error);\n              _this.router.navigate(['/']);\n            }\n          });\n        }\n      })();\n    }\n    backToCart() {\n      this.router.navigate(['/shop/cart']);\n    }\n    static {\n      this.ɵfac = function CheckoutComponent_Factory(t) {\n        return new (t || CheckoutComponent)(i0.ɵɵdirectiveInject(i1.CartService), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CheckoutComponent,\n        selectors: [[\"app-checkout\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 112,\n        vars: 22,\n        consts: [[1, \"checkout-page\"], [1, \"checkout-header\"], [1, \"steps\"], [1, \"step\", \"active\"], [1, \"step-number\"], [1, \"step-label\"], [1, \"step\"], [1, \"checkout-content\"], [1, \"checkout-form\"], [1, \"form-section\"], [1, \"form-row\"], [1, \"form-group\"], [\"type\", \"text\", \"name\", \"firstName\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"name\", \"lastName\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"email\", \"name\", \"email\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"tel\", \"name\", \"phone\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"name\", \"address\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"name\", \"city\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"name\", \"state\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"name\", \"pinCode\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [1, \"payment-options\"], [1, \"payment-option\"], [\"type\", \"radio\", \"name\", \"paymentMethod\", \"value\", \"card\", 3, \"ngModelChange\", \"ngModel\"], [1, \"option-content\"], [1, \"fas\", \"fa-credit-card\"], [\"type\", \"radio\", \"name\", \"paymentMethod\", \"value\", \"upi\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-mobile-alt\"], [\"type\", \"radio\", \"name\", \"paymentMethod\", \"value\", \"cod\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-money-bill-wave\"], [1, \"order-summary\"], [1, \"summary-card\"], [1, \"summary-row\"], [\"class\", \"summary-row\", 4, \"ngIf\"], [1, \"summary-row\", \"total\"], [1, \"place-order-btn\", 3, \"click\", \"disabled\"], [1, \"back-to-cart-btn\", 3, \"click\"], [1, \"discount\"]],\n        template: function CheckoutComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n            i0.ɵɵtext(3, \"Checkout\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 2)(5, \"div\", 3)(6, \"span\", 4);\n            i0.ɵɵtext(7, \"1\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"span\", 5);\n            i0.ɵɵtext(9, \"Shipping\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 6)(11, \"span\", 4);\n            i0.ɵɵtext(12, \"2\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"span\", 5);\n            i0.ɵɵtext(14, \"Payment\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"div\", 6)(16, \"span\", 4);\n            i0.ɵɵtext(17, \"3\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"span\", 5);\n            i0.ɵɵtext(19, \"Review\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(20, \"div\", 7)(21, \"div\", 8)(22, \"div\", 9)(23, \"h3\");\n            i0.ɵɵtext(24, \"Shipping Address\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"form\")(26, \"div\", 10)(27, \"div\", 11)(28, \"label\");\n            i0.ɵɵtext(29, \"First Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"input\", 12);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CheckoutComponent_Template_input_ngModelChange_30_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.shippingAddress.firstName, $event) || (ctx.shippingAddress.firstName = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(31, \"div\", 11)(32, \"label\");\n            i0.ɵɵtext(33, \"Last Name\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"input\", 13);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CheckoutComponent_Template_input_ngModelChange_34_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.shippingAddress.lastName, $event) || (ctx.shippingAddress.lastName = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(35, \"div\", 11)(36, \"label\");\n            i0.ɵɵtext(37, \"Email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"input\", 14);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CheckoutComponent_Template_input_ngModelChange_38_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.shippingAddress.email, $event) || (ctx.shippingAddress.email = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(39, \"div\", 11)(40, \"label\");\n            i0.ɵɵtext(41, \"Phone\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"input\", 15);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CheckoutComponent_Template_input_ngModelChange_42_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.shippingAddress.phone, $event) || (ctx.shippingAddress.phone = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(43, \"div\", 11)(44, \"label\");\n            i0.ɵɵtext(45, \"Address\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"input\", 16);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CheckoutComponent_Template_input_ngModelChange_46_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.shippingAddress.address, $event) || (ctx.shippingAddress.address = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(47, \"div\", 10)(48, \"div\", 11)(49, \"label\");\n            i0.ɵɵtext(50, \"City\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"input\", 17);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CheckoutComponent_Template_input_ngModelChange_51_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.shippingAddress.city, $event) || (ctx.shippingAddress.city = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(52, \"div\", 11)(53, \"label\");\n            i0.ɵɵtext(54, \"State\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"input\", 18);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CheckoutComponent_Template_input_ngModelChange_55_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.shippingAddress.state, $event) || (ctx.shippingAddress.state = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(56, \"div\", 11)(57, \"label\");\n            i0.ɵɵtext(58, \"PIN Code\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(59, \"input\", 19);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CheckoutComponent_Template_input_ngModelChange_59_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.shippingAddress.pinCode, $event) || (ctx.shippingAddress.pinCode = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(60, \"div\", 9)(61, \"h3\");\n            i0.ɵɵtext(62, \"Payment Method\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(63, \"div\", 20)(64, \"label\", 21)(65, \"input\", 22);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CheckoutComponent_Template_input_ngModelChange_65_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.paymentMethod, $event) || (ctx.paymentMethod = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(66, \"span\", 23);\n            i0.ɵɵelement(67, \"i\", 24);\n            i0.ɵɵtext(68, \" Credit/Debit Card \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(69, \"label\", 21)(70, \"input\", 25);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CheckoutComponent_Template_input_ngModelChange_70_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.paymentMethod, $event) || (ctx.paymentMethod = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(71, \"span\", 23);\n            i0.ɵɵelement(72, \"i\", 26);\n            i0.ɵɵtext(73, \" UPI \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(74, \"label\", 21)(75, \"input\", 27);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function CheckoutComponent_Template_input_ngModelChange_75_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.paymentMethod, $event) || (ctx.paymentMethod = $event);\n              return $event;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(76, \"span\", 23);\n            i0.ɵɵelement(77, \"i\", 28);\n            i0.ɵɵtext(78, \" Cash on Delivery \");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(79, \"div\", 29)(80, \"div\", 30)(81, \"h3\");\n            i0.ɵɵtext(82, \"Order Summary\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(83, \"div\", 31)(84, \"span\");\n            i0.ɵɵtext(85, \"Subtotal\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(86, \"span\");\n            i0.ɵɵtext(87);\n            i0.ɵɵpipe(88, \"number\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(89, CheckoutComponent_div_89_Template, 6, 3, \"div\", 32);\n            i0.ɵɵelementStart(90, \"div\", 31)(91, \"span\");\n            i0.ɵɵtext(92, \"Shipping\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(93, \"span\");\n            i0.ɵɵtext(94, \"Free\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(95, \"div\", 31)(96, \"span\");\n            i0.ɵɵtext(97, \"Tax\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(98, \"span\");\n            i0.ɵɵtext(99);\n            i0.ɵɵpipe(100, \"number\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelement(101, \"hr\");\n            i0.ɵɵelementStart(102, \"div\", 33)(103, \"span\");\n            i0.ɵɵtext(104, \"Total\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(105, \"span\");\n            i0.ɵɵtext(106);\n            i0.ɵɵpipe(107, \"number\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(108, \"button\", 34);\n            i0.ɵɵlistener(\"click\", function CheckoutComponent_Template_button_click_108_listener() {\n              return ctx.placeOrder();\n            });\n            i0.ɵɵtext(109, \" Place Order \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(110, \"button\", 35);\n            i0.ɵɵlistener(\"click\", function CheckoutComponent_Template_button_click_110_listener() {\n              return ctx.backToCart();\n            });\n            i0.ɵɵtext(111, \" Back to Cart \");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(30);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.shippingAddress.firstName);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.shippingAddress.lastName);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.shippingAddress.email);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.shippingAddress.phone);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.shippingAddress.address);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.shippingAddress.city);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.shippingAddress.state);\n            i0.ɵɵadvance(4);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.shippingAddress.pinCode);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.paymentMethod);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.paymentMethod);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.paymentMethod);\n            i0.ɵɵadvance(12);\n            i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(88, 16, ctx.cartSummary == null ? null : ctx.cartSummary.subtotal), \"\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.cartSummary && ctx.cartSummary.discount && ctx.cartSummary.discount > 0);\n            i0.ɵɵadvance(10);\n            i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(100, 18, ctx.tax), \"\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(107, 20, ctx.getTotal()), \"\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", !ctx.isFormValid());\n          }\n        },\n        dependencies: [CommonModule, i3.NgIf, i3.DecimalPipe, FormsModule, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.RadioControlValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.NgModel, i4.NgForm],\n        styles: [\".checkout-page[_ngcontent-%COMP%]{padding:2rem;max-width:1200px;margin:0 auto}.checkout-header[_ngcontent-%COMP%]{margin-bottom:2rem}.checkout-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;margin-bottom:1rem;color:#333}.steps[_ngcontent-%COMP%]{display:flex;gap:2rem}.step[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;color:#999}.step.active[_ngcontent-%COMP%]{color:#007bff}.step-number[_ngcontent-%COMP%]{width:30px;height:30px;border-radius:50%;background:#f0f0f0;display:flex;align-items:center;justify-content:center;font-weight:600}.step.active[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%]{background:#007bff;color:#fff}.checkout-content[_ngcontent-%COMP%]{display:grid;grid-template-columns:2fr 1fr;gap:2rem}.form-section[_ngcontent-%COMP%]{background:#fff;padding:1.5rem;border-radius:8px;border:1px solid #eee;margin-bottom:1rem}.form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin-bottom:1rem;color:#333}.form-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:1rem}.form-group[_ngcontent-%COMP%]{margin-bottom:1rem}.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;margin-bottom:.5rem;font-weight:600;color:#333}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{width:100%;padding:.75rem;border:1px solid #ddd;border-radius:4px;font-size:1rem}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus{outline:none;border-color:#007bff}.payment-options[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:1rem}.payment-option[_ngcontent-%COMP%]{display:flex;align-items:center;padding:1rem;border:2px solid #eee;border-radius:8px;cursor:pointer;transition:border-color .2s}.payment-option[_ngcontent-%COMP%]:hover{border-color:#007bff}.payment-option[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]{margin-right:1rem}.option-content[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;font-weight:600}.option-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.2rem;color:#666}.summary-card[_ngcontent-%COMP%]{background:#f8f9fa;padding:1.5rem;border-radius:8px;position:sticky;top:2rem}.summary-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin-bottom:1rem;color:#333}.summary-row[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-bottom:.75rem}.summary-row.total[_ngcontent-%COMP%]{font-weight:700;font-size:1.2rem;color:#333}.discount[_ngcontent-%COMP%]{color:#28a745}.place-order-btn[_ngcontent-%COMP%]{width:100%;background:#28a745;color:#fff;border:none;padding:1rem;border-radius:8px;font-size:1.1rem;font-weight:600;cursor:pointer;margin-bottom:1rem;transition:background .2s}.place-order-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:#218838}.place-order-btn[_ngcontent-%COMP%]:disabled{background:#ccc;cursor:not-allowed}.back-to-cart-btn[_ngcontent-%COMP%]{width:100%;background:transparent;color:#007bff;border:2px solid #007bff;padding:1rem;border-radius:8px;font-size:1rem;font-weight:600;cursor:pointer;transition:all .2s}.back-to-cart-btn[_ngcontent-%COMP%]:hover{background:#007bff;color:#fff}@media (max-width: 768px){.checkout-content[_ngcontent-%COMP%], .form-row[_ngcontent-%COMP%]{grid-template-columns:1fr}.steps[_ngcontent-%COMP%]{flex-direction:column;gap:1rem}}\"]\n      });\n    }\n  }\n  return CheckoutComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}