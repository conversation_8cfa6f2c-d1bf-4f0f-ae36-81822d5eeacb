{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let TrendingService = /*#__PURE__*/(() => {\n  class TrendingService {\n    constructor(http) {\n      this.http = http;\n      this.API_URL = environment.apiUrl;\n      // BehaviorSubjects for caching\n      this.trendingProductsSubject = new BehaviorSubject([]);\n      this.suggestedProductsSubject = new BehaviorSubject([]);\n      this.newArrivalsSubject = new BehaviorSubject([]);\n      this.featuredBrandsSubject = new BehaviorSubject([]);\n      this.influencersSubject = new BehaviorSubject([]);\n      // Public observables\n      this.trendingProducts$ = this.trendingProductsSubject.asObservable();\n      this.suggestedProducts$ = this.suggestedProductsSubject.asObservable();\n      this.newArrivals$ = this.newArrivalsSubject.asObservable();\n      this.featuredBrands$ = this.featuredBrandsSubject.asObservable();\n      this.influencers$ = this.influencersSubject.asObservable();\n    }\n    // Get trending products\n    getTrendingProducts(page = 1, limit = 12) {\n      return this.http.get(`${this.API_URL}/v1/products/trending`, {\n        params: {\n          page: page.toString(),\n          limit: limit.toString()\n        }\n      });\n    }\n    // Get suggested products\n    getSuggestedProducts(page = 1, limit = 12) {\n      return this.http.get(`${this.API_URL}/v1/products/suggested`, {\n        params: {\n          page: page.toString(),\n          limit: limit.toString()\n        }\n      });\n    }\n    // Get new arrivals\n    getNewArrivals(page = 1, limit = 12) {\n      return this.http.get(`${this.API_URL}/v1/products/new-arrivals`, {\n        params: {\n          page: page.toString(),\n          limit: limit.toString()\n        }\n      });\n    }\n    // Get featured brands\n    getFeaturedBrands() {\n      return this.http.get(`${this.API_URL}/v1/products/featured-brands`);\n    }\n    // Get influencers\n    getInfluencers(page = 1, limit = 10) {\n      return this.http.get(`${this.API_URL}/v1/users/influencers`, {\n        params: {\n          page: page.toString(),\n          limit: limit.toString()\n        }\n      });\n    }\n    // Load and cache trending products\n    loadTrendingProducts() {\n      var _this = this;\n      return _asyncToGenerator(function* (page = 1, limit = 12) {\n        try {\n          const response = yield _this.getTrendingProducts(page, limit).toPromise();\n          if (response?.success && response?.products) {\n            _this.trendingProductsSubject.next(response.products);\n          }\n        } catch (error) {\n          console.error('Error loading trending products:', error);\n        }\n      }).apply(this, arguments);\n    }\n    // Load and cache suggested products\n    loadSuggestedProducts() {\n      var _this2 = this;\n      return _asyncToGenerator(function* (page = 1, limit = 12) {\n        try {\n          const response = yield _this2.getSuggestedProducts(page, limit).toPromise();\n          if (response?.success && response?.products) {\n            _this2.suggestedProductsSubject.next(response.products);\n          }\n        } catch (error) {\n          console.error('Error loading suggested products:', error);\n        }\n      }).apply(this, arguments);\n    }\n    // Load and cache new arrivals\n    loadNewArrivals() {\n      var _this3 = this;\n      return _asyncToGenerator(function* (page = 1, limit = 12) {\n        try {\n          const response = yield _this3.getNewArrivals(page, limit).toPromise();\n          if (response?.success && response?.products) {\n            _this3.newArrivalsSubject.next(response.products);\n          }\n        } catch (error) {\n          console.error('Error loading new arrivals:', error);\n        }\n      }).apply(this, arguments);\n    }\n    // Load and cache featured brands\n    loadFeaturedBrands() {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const response = yield _this4.getFeaturedBrands().toPromise();\n          if (response?.success && response?.brands) {\n            _this4.featuredBrandsSubject.next(response.brands);\n          }\n        } catch (error) {\n          console.error('Error loading featured brands:', error);\n        }\n      })();\n    }\n    // Load and cache influencers\n    loadInfluencers() {\n      var _this5 = this;\n      return _asyncToGenerator(function* (page = 1, limit = 10) {\n        try {\n          const response = yield _this5.getInfluencers(page, limit).toPromise();\n          if (response?.success && response?.influencers) {\n            _this5.influencersSubject.next(response.influencers);\n          }\n        } catch (error) {\n          console.error('Error loading influencers:', error);\n        }\n      }).apply(this, arguments);\n    }\n    // Clear all cached data\n    clearCache() {\n      this.trendingProductsSubject.next([]);\n      this.suggestedProductsSubject.next([]);\n      this.newArrivalsSubject.next([]);\n      this.featuredBrandsSubject.next([]);\n      this.influencersSubject.next([]);\n    }\n    // Get current cached data\n    getCurrentTrendingProducts() {\n      return this.trendingProductsSubject.value;\n    }\n    getCurrentSuggestedProducts() {\n      return this.suggestedProductsSubject.value;\n    }\n    getCurrentNewArrivals() {\n      return this.newArrivalsSubject.value;\n    }\n    getCurrentFeaturedBrands() {\n      return this.featuredBrandsSubject.value;\n    }\n    getCurrentInfluencers() {\n      return this.influencersSubject.value;\n    }\n    static {\n      this.ɵfac = function TrendingService_Factory(t) {\n        return new (t || TrendingService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: TrendingService,\n        factory: TrendingService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return TrendingService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}