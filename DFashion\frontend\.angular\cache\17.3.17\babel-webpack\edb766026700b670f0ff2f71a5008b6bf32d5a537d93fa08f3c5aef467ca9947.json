{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { isPlatformServer } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, PLATFORM_ID, NgModule } from '@angular/core';\nimport { Storage } from '@ionic/storage';\nexport { Storage } from '@ionic/storage';\nconst StorageConfigToken = new InjectionToken('STORAGE_CONFIG_TOKEN');\nclass NoopStorage extends Storage {\n  constructor() {\n    super();\n  }\n  create() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      return _this;\n    })();\n  }\n  defineDriver() {\n    return _asyncToGenerator(function* () {})();\n  }\n  get driver() {\n    return 'noop';\n  }\n  get(key) {\n    return _asyncToGenerator(function* () {\n      return null;\n    })();\n  }\n  set(key, value) {\n    return _asyncToGenerator(function* () {})();\n  }\n  remove(key) {\n    return _asyncToGenerator(function* () {})();\n  }\n  clear() {\n    return _asyncToGenerator(function* () {})();\n  }\n  length() {\n    return _asyncToGenerator(function* () {\n      return 0;\n    })();\n  }\n  keys() {\n    return _asyncToGenerator(function* () {\n      return [];\n    })();\n  }\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  forEach(iteratorCallback) {\n    return _asyncToGenerator(function* () {})();\n  }\n  setEncryptionKey(key) {}\n}\nfunction provideStorage(platformId, storageConfig) {\n  if (isPlatformServer(platformId)) {\n    // When running in a server context return the NoopStorage\n    return new NoopStorage();\n  }\n  return new Storage(storageConfig);\n}\nlet IonicStorageModule = /*#__PURE__*/(() => {\n  class IonicStorageModule {\n    static forRoot(storageConfig = null) {\n      return {\n        ngModule: IonicStorageModule,\n        providers: [{\n          provide: StorageConfigToken,\n          useValue: storageConfig\n        }, {\n          provide: Storage,\n          useFactory: provideStorage,\n          deps: [PLATFORM_ID, StorageConfigToken]\n        }]\n      };\n    }\n  }\n  IonicStorageModule.ɵfac = function IonicStorageModule_Factory(t) {\n    return new (t || IonicStorageModule)();\n  };\n  IonicStorageModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: IonicStorageModule\n  });\n  IonicStorageModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  return IonicStorageModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { IonicStorageModule, StorageConfigToken, provideStorage };\n//# sourceMappingURL=ionic-storage-angular.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}