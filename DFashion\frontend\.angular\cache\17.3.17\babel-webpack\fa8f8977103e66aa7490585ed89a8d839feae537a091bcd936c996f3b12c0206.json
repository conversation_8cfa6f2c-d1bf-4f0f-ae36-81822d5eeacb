{"ast": null, "code": "import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nexport function race(...sources) {\n  sources = argsOrArgArray(sources);\n  return sources.length === 1 ? innerFrom(sources[0]) : new Observable(raceInit(sources));\n}\nexport function raceInit(sources) {\n  return subscriber => {\n    let subscriptions = [];\n    for (let i = 0; subscriptions && !subscriber.closed && i < sources.length; i++) {\n      subscriptions.push(innerFrom(sources[i]).subscribe(createOperatorSubscriber(subscriber, value => {\n        if (subscriptions) {\n          for (let s = 0; s < subscriptions.length; s++) {\n            s !== i && subscriptions[s].unsubscribe();\n          }\n          subscriptions = null;\n        }\n        subscriber.next(value);\n      })));\n    }\n  };\n}\n//# sourceMappingURL=race.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}