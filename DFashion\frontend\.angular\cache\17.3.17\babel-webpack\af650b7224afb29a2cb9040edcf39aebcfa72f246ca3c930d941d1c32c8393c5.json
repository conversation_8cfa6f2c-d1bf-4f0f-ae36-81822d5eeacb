{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { OptimizedImageComponent } from '../../../../shared/components/optimized-image/optimized-image.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@ionic/angular\";\nfunction ProfileComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"ion-spinner\", 5);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading profile...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProfileComponent_div_2_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_2_div_15_Template_div_click_0_listener() {\n      const feature_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      return i0.ɵɵresetView(feature_r3.action());\n    });\n    i0.ɵɵelement(1, \"ion-icon\", 26);\n    i0.ɵɵelementStart(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const feature_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", feature_r3.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(feature_r3.title);\n  }\n}\nfunction ProfileComponent_div_2_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"h3\", 13);\n    i0.ɵɵtext(2, \"Vendor Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Access your vendor dashboard to manage products, view sales analytics, and handle orders.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_2_div_45_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.navigateToVendorDashboard());\n    });\n    i0.ɵɵelement(6, \"ion-icon\", 27);\n    i0.ɵɵtext(7, \" Go to Vendor Dashboard \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProfileComponent_div_2_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"h3\", 13);\n    i0.ɵɵtext(2, \"Administrator Access\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Access the admin panel to manage users, products, and system settings.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_2_div_46_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.navigateToAdminPanel());\n    });\n    i0.ɵɵelement(6, \"ion-icon\", 28);\n    i0.ɵɵtext(7, \" Go to Admin Panel \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProfileComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7);\n    i0.ɵɵelement(2, \"app-optimized-image\", 8);\n    i0.ɵɵelementStart(3, \"div\", 9)(4, \"h2\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 10);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 11)(11, \"div\", 12)(12, \"h3\", 13);\n    i0.ɵɵtext(13, \"Available Features\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 14);\n    i0.ɵɵtemplate(15, ProfileComponent_div_2_div_15_Template, 4, 2, \"div\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 12)(17, \"h3\", 13);\n    i0.ɵɵtext(18, \"Account Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"ul\", 16)(20, \"li\", 17)(21, \"span\");\n    i0.ɵɵtext(22, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\");\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"li\", 17)(26, \"span\");\n    i0.ɵɵtext(27, \"Username\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"li\", 17)(31, \"span\");\n    i0.ɵɵtext(32, \"Account Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"span\");\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"li\", 17)(36, \"span\");\n    i0.ɵɵtext(37, \"Account Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"span\");\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"li\", 17)(41, \"span\");\n    i0.ɵɵtext(42, \"Verification Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"span\");\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(45, ProfileComponent_div_2_div_45_Template, 8, 0, \"div\", 18)(46, ProfileComponent_div_2_div_46_Template, 8, 0, \"div\", 18);\n    i0.ɵɵelementStart(47, \"div\", 12)(48, \"h3\", 13);\n    i0.ɵɵtext(49, \"Quick Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 19)(51, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_2_Template_button_click_51_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.editProfile());\n    });\n    i0.ɵɵelement(52, \"ion-icon\", 21);\n    i0.ɵɵtext(53, \" Edit Profile \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_2_Template_button_click_54_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.navigateToSettings());\n    });\n    i0.ɵɵelement(55, \"ion-icon\", 23);\n    i0.ɵɵtext(56, \" Settings \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_2_Template_button_click_57_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.logout());\n    });\n    i0.ɵɵelement(58, \"ion-icon\", 24);\n    i0.ɵɵtext(59, \" Logout \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r4.currentUser.avatar)(\"alt\", ctx_r4.currentUser.fullName)(\"width\", 80)(\"height\", 80);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.currentUser.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(\"@\" + ctx_r4.currentUser.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getRoleDisplayName());\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.getRoleFeatures());\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r4.currentUser.email);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r4.currentUser.username);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r4.getRoleDisplayName());\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r4.currentUser.isActive ? \"text-success\" : \"text-danger\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.currentUser.isActive ? \"Active\" : \"Inactive\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r4.currentUser.isVerified ? \"text-success\" : \"text-warning\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.currentUser.isVerified ? \"Verified\" : \"Pending Verification\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.canAccessVendorDashboard());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.canAccessAdminPanel());\n  }\n}\nfunction ProfileComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵelement(1, \"ion-icon\", 30);\n    i0.ɵɵelementStart(2, \"h3\");\n    i0.ɵɵtext(3, \"No User Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Please log in to view your profile.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_3_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.navigateToLogin());\n    });\n    i0.ɵɵtext(7, \" Login \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ProfileComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.currentUser = null;\n    this.isLoading = true;\n  }\n  ngOnInit() {\n    this.loadUserProfile();\n  }\n  loadUserProfile() {\n    this.currentUser = this.authService.currentUserValue;\n    this.isLoading = false;\n  }\n  // Role-based feature access\n  canAccessVendorDashboard() {\n    return this.authService.isVendor() || this.authService.isAdmin();\n  }\n  canAccessAdminPanel() {\n    return this.authService.isAdmin();\n  }\n  canManageProducts() {\n    return this.authService.isVendor() || this.authService.isAdmin();\n  }\n  canViewAnalytics() {\n    return this.authService.isVendor() || this.authService.isAdmin();\n  }\n  // Navigation methods\n  navigateToVendorDashboard() {\n    if (this.canAccessVendorDashboard()) {\n      this.router.navigate(['/vendor/dashboard']);\n    }\n  }\n  navigateToAdminPanel() {\n    if (this.canAccessAdminPanel()) {\n      this.router.navigate(['/admin/dashboard']);\n    }\n  }\n  navigateToOrders() {\n    this.router.navigate(['/account/orders']);\n  }\n  navigateToWishlist() {\n    this.router.navigate(['/wishlist']);\n  }\n  navigateToCart() {\n    this.router.navigate(['/cart']);\n  }\n  navigateToSettings() {\n    this.router.navigate(['/account/settings']);\n  }\n  editProfile() {\n    this.router.navigate(['/account/edit-profile']);\n  }\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/auth/login']);\n  }\n  getRoleDisplayName() {\n    switch (this.currentUser?.role) {\n      case 'customer':\n        return 'Customer';\n      case 'vendor':\n        return 'Vendor';\n      case 'admin':\n        return 'Administrator';\n      default:\n        return 'User';\n    }\n  }\n  getRoleFeatures() {\n    const baseFeatures = [{\n      icon: 'person-outline',\n      title: 'Edit Profile',\n      action: () => this.editProfile()\n    }, {\n      icon: 'bag-outline',\n      title: 'My Orders',\n      action: () => this.navigateToOrders()\n    }, {\n      icon: 'heart-outline',\n      title: 'Wishlist',\n      action: () => this.navigateToWishlist()\n    }, {\n      icon: 'cart-outline',\n      title: 'Shopping Cart',\n      action: () => this.navigateToCart()\n    }, {\n      icon: 'settings-outline',\n      title: 'Settings',\n      action: () => this.navigateToSettings()\n    }];\n    if (this.canAccessVendorDashboard()) {\n      baseFeatures.push({\n        icon: 'storefront-outline',\n        title: 'Vendor Dashboard',\n        action: () => this.navigateToVendorDashboard()\n      }, {\n        icon: 'cube-outline',\n        title: 'Manage Products',\n        action: () => this.router.navigate(['/vendor/products'])\n      }, {\n        icon: 'analytics-outline',\n        title: 'Sales Analytics',\n        action: () => this.router.navigate(['/vendor/analytics'])\n      });\n    }\n    if (this.canAccessAdminPanel()) {\n      baseFeatures.push({\n        icon: 'shield-outline',\n        title: 'Admin Panel',\n        action: () => this.navigateToAdminPanel()\n      }, {\n        icon: 'people-outline',\n        title: 'User Management',\n        action: () => this.router.navigate(['/admin/users'])\n      }, {\n        icon: 'bar-chart-outline',\n        title: 'System Analytics',\n        action: () => this.router.navigate(['/admin/analytics'])\n      });\n    }\n    return baseFeatures;\n  }\n  static {\n    this.ɵfac = function ProfileComponent_Factory(t) {\n      return new (t || ProfileComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileComponent,\n      selectors: [[\"app-profile\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 3,\n      consts: [[1, \"profile-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"profile-content\", 4, \"ngIf\"], [\"class\", \"no-user-state\", 4, \"ngIf\"], [1, \"loading-container\"], [\"name\", \"crescent\"], [1, \"profile-content\"], [1, \"profile-header\"], [\"fallbackType\", \"user\", \"containerClass\", \"profile-avatar\", \"imageClass\", \"avatar-img\", \"objectFit\", \"cover\", 3, \"src\", \"alt\", \"width\", \"height\"], [1, \"profile-info\"], [1, \"profile-role\"], [1, \"profile-sections\"], [1, \"profile-section\"], [1, \"section-title\"], [1, \"role-features\"], [\"class\", \"feature-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"settings-list\"], [1, \"settings-item\"], [\"class\", \"profile-section\", 4, \"ngIf\"], [1, \"action-buttons\"], [1, \"btn-primary\", 3, \"click\"], [\"name\", \"create-outline\"], [1, \"btn-secondary\", 3, \"click\"], [\"name\", \"settings-outline\"], [\"name\", \"log-out-outline\"], [1, \"feature-card\", 3, \"click\"], [1, \"feature-icon\", 3, \"name\"], [\"name\", \"storefront-outline\"], [\"name\", \"shield-outline\"], [1, \"no-user-state\"], [\"name\", \"person-outline\", 1, \"large-icon\"]],\n      template: function ProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, ProfileComponent_div_1_Template, 4, 0, \"div\", 1)(2, ProfileComponent_div_2_Template, 60, 19, \"div\", 2)(3, ProfileComponent_div_3_Template, 8, 0, \"div\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.currentUser);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.currentUser);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, FormsModule, IonicModule, i4.IonIcon, i4.IonSpinner, OptimizedImageComponent],\n      styles: [\".profile-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n\\n.profile-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 20px;\\n  margin-bottom: 30px;\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 12px;\\n  color: white;\\n}\\n\\n.profile-avatar[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 50%;\\n  border: 3px solid white;\\n  overflow: hidden;\\n  flex-shrink: 0;\\n}\\n\\n.avatar-img[_ngcontent-%COMP%] {\\n  border-radius: 50%;\\n}\\n\\n.profile-info[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.5rem;\\n}\\n\\n.profile-role[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  padding: 4px 12px;\\n  border-radius: 20px;\\n  font-size: 0.9rem;\\n  margin-top: 8px;\\n  display: inline-block;\\n}\\n\\n.profile-sections[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 20px;\\n}\\n\\n.profile-section[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  padding: 20px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  margin-bottom: 15px;\\n  color: #333;\\n}\\n\\n.role-features[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 15px;\\n}\\n\\n.feature-card[_ngcontent-%COMP%] {\\n  padding: 15px;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  text-align: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.feature-card[_ngcontent-%COMP%]:hover {\\n  border-color: #667eea;\\n  transform: translateY(-2px);\\n}\\n\\n.feature-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  margin-bottom: 10px;\\n  color: #667eea;\\n}\\n\\n.settings-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n}\\n\\n.settings-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 12px 0;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n\\n.settings-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: #667eea;\\n  color: white;\\n  border: none;\\n  padding: 10px 20px;\\n  border-radius: 6px;\\n  cursor: pointer;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  color: #333;\\n  border: 1px solid #ddd;\\n  padding: 10px 20px;\\n  border-radius: 6px;\\n  cursor: pointer;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  flex-wrap: wrap;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px;\\n}\\n\\n.no-user-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px;\\n}\\n\\n.large-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  color: #ccc;\\n  margin-bottom: 20px;\\n}\\n\\n.text-success[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n\\n.text-danger[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n\\n.text-warning[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n\\n@media (max-width: 768px) {\\n  .profile-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n  .role-features[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\\n  }\\n  .action-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "IonicModule", "OptimizedImageComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ProfileComponent_div_2_div_15_Template_div_click_0_listener", "feature_r3", "ɵɵrestoreView", "_r2", "$implicit", "ɵɵresetView", "action", "ɵɵadvance", "ɵɵproperty", "icon", "ɵɵtextInterpolate", "title", "ProfileComponent_div_2_div_45_Template_button_click_5_listener", "_r4", "ctx_r4", "ɵɵnextContext", "navigateToVendorDashboard", "ProfileComponent_div_2_div_46_Template_button_click_5_listener", "_r6", "navigateToAdminPanel", "ɵɵtemplate", "ProfileComponent_div_2_div_15_Template", "ProfileComponent_div_2_div_45_Template", "ProfileComponent_div_2_div_46_Template", "ProfileComponent_div_2_Template_button_click_51_listener", "_r1", "editProfile", "ProfileComponent_div_2_Template_button_click_54_listener", "navigateToSettings", "ProfileComponent_div_2_Template_button_click_57_listener", "logout", "currentUser", "avatar", "fullName", "username", "getRoleDisplayName", "getRoleFeatures", "email", "ɵɵclassMap", "isActive", "ɵɵtextInterpolate1", "isVerified", "canAccessVendorDashboard", "canAccessAdminPanel", "ProfileComponent_div_3_Template_button_click_6_listener", "_r7", "navigateToLogin", "ProfileComponent", "constructor", "authService", "router", "isLoading", "ngOnInit", "loadUserProfile", "currentUserValue", "isVendor", "isAdmin", "canManageProducts", "canViewAnalytics", "navigate", "navigateToOrders", "navigateToWishlist", "navigateToCart", "role", "baseFeatures", "push", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ProfileComponent_Template", "rf", "ctx", "ProfileComponent_div_1_Template", "ProfileComponent_div_2_Template", "ProfileComponent_div_3_Template", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i4", "IonIcon", "Ion<PERSON><PERSON><PERSON>", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\profile\\pages\\profile\\profile.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\profile\\pages\\profile\\profile.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\n\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { User } from '../../../../core/models/user.model';\nimport { OptimizedImageComponent } from '../../../../shared/components/optimized-image/optimized-image.component';\nimport { OptimizedImageComponent } from '../../../../shared/components/optimized-image/optimized-image.component';\n\n@Component({\n  selector: 'app-profile',\n  standalone: true,\n  imports: [CommonModule, FormsModule, IonicModule, OptimizedImageComponent],\n  templateUrl: './profile.component.html',\n  styles: [`\n    .profile-container {\n      padding: 20px;\n      max-width: 800px;\n      margin: 0 auto;\n    }\n\n    .profile-header {\n      display: flex;\n      align-items: center;\n      gap: 20px;\n      margin-bottom: 30px;\n      padding: 20px;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      border-radius: 12px;\n      color: white;\n    }\n\n    .profile-avatar {\n      width: 80px;\n      height: 80px;\n      border-radius: 50%;\n      border: 3px solid white;\n      overflow: hidden;\n      flex-shrink: 0;\n    }\n\n    .avatar-img {\n      border-radius: 50%;\n    }\n\n    .profile-info h2 {\n      margin: 0;\n      font-size: 1.5rem;\n    }\n\n    .profile-role {\n      background: rgba(255, 255, 255, 0.2);\n      padding: 4px 12px;\n      border-radius: 20px;\n      font-size: 0.9rem;\n      margin-top: 8px;\n      display: inline-block;\n    }\n\n    .profile-sections {\n      display: grid;\n      gap: 20px;\n    }\n\n    .profile-section {\n      background: white;\n      border-radius: 12px;\n      padding: 20px;\n      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n    }\n\n    .section-title {\n      font-size: 1.2rem;\n      font-weight: 600;\n      margin-bottom: 15px;\n      color: #333;\n    }\n\n    .role-features {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 15px;\n    }\n\n    .feature-card {\n      padding: 15px;\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n      text-align: center;\n      cursor: pointer;\n      transition: all 0.3s ease;\n    }\n\n    .feature-card:hover {\n      border-color: #667eea;\n      transform: translateY(-2px);\n    }\n\n    .feature-icon {\n      font-size: 2rem;\n      margin-bottom: 10px;\n      color: #667eea;\n    }\n\n    .settings-list {\n      list-style: none;\n      padding: 0;\n    }\n\n    .settings-item {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 12px 0;\n      border-bottom: 1px solid #f0f0f0;\n    }\n\n    .settings-item:last-child {\n      border-bottom: none;\n    }\n\n    .btn-primary {\n      background: #667eea;\n      color: white;\n      border: none;\n      padding: 10px 20px;\n      border-radius: 6px;\n      cursor: pointer;\n    }\n\n    .btn-secondary {\n      background: #f8f9fa;\n      color: #333;\n      border: 1px solid #ddd;\n      padding: 10px 20px;\n      border-radius: 6px;\n      cursor: pointer;\n    }\n\n    .action-buttons {\n      display: flex;\n      gap: 10px;\n      flex-wrap: wrap;\n    }\n\n    .action-buttons button {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .loading-container {\n      text-align: center;\n      padding: 40px;\n    }\n\n    .no-user-state {\n      text-align: center;\n      padding: 40px;\n    }\n\n    .large-icon {\n      font-size: 4rem;\n      color: #ccc;\n      margin-bottom: 20px;\n    }\n\n    .text-success {\n      color: #28a745;\n    }\n\n    .text-danger {\n      color: #dc3545;\n    }\n\n    .text-warning {\n      color: #ffc107;\n    }\n\n    @media (max-width: 768px) {\n      .profile-header {\n        flex-direction: column;\n        text-align: center;\n      }\n\n      .role-features {\n        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n      }\n\n      .action-buttons {\n        flex-direction: column;\n      }\n    }\n  `]\n})\nexport class ProfileComponent implements OnInit {\n  currentUser: User | null = null;\n  isLoading = true;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadUserProfile();\n  }\n\n  loadUserProfile() {\n    this.currentUser = this.authService.currentUserValue;\n    this.isLoading = false;\n  }\n\n  // Role-based feature access\n  canAccessVendorDashboard(): boolean {\n    return this.authService.isVendor() || this.authService.isAdmin();\n  }\n\n  canAccessAdminPanel(): boolean {\n    return this.authService.isAdmin();\n  }\n\n  canManageProducts(): boolean {\n    return this.authService.isVendor() || this.authService.isAdmin();\n  }\n\n  canViewAnalytics(): boolean {\n    return this.authService.isVendor() || this.authService.isAdmin();\n  }\n\n  // Navigation methods\n  navigateToVendorDashboard() {\n    if (this.canAccessVendorDashboard()) {\n      this.router.navigate(['/vendor/dashboard']);\n    }\n  }\n\n  navigateToAdminPanel() {\n    if (this.canAccessAdminPanel()) {\n      this.router.navigate(['/admin/dashboard']);\n    }\n  }\n\n  navigateToOrders() {\n    this.router.navigate(['/account/orders']);\n  }\n\n  navigateToWishlist() {\n    this.router.navigate(['/wishlist']);\n  }\n\n  navigateToCart() {\n    this.router.navigate(['/cart']);\n  }\n\n  navigateToSettings() {\n    this.router.navigate(['/account/settings']);\n  }\n\n  editProfile() {\n    this.router.navigate(['/account/edit-profile']);\n  }\n\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/auth/login']);\n  }\n\n  getRoleDisplayName(): string {\n    switch (this.currentUser?.role) {\n      case 'customer':\n        return 'Customer';\n      case 'vendor':\n        return 'Vendor';\n      case 'admin':\n        return 'Administrator';\n      default:\n        return 'User';\n    }\n  }\n\n  getRoleFeatures(): any[] {\n    const baseFeatures = [\n      { icon: 'person-outline', title: 'Edit Profile', action: () => this.editProfile() },\n      { icon: 'bag-outline', title: 'My Orders', action: () => this.navigateToOrders() },\n      { icon: 'heart-outline', title: 'Wishlist', action: () => this.navigateToWishlist() },\n      { icon: 'cart-outline', title: 'Shopping Cart', action: () => this.navigateToCart() },\n      { icon: 'settings-outline', title: 'Settings', action: () => this.navigateToSettings() }\n    ];\n\n    if (this.canAccessVendorDashboard()) {\n      baseFeatures.push(\n        { icon: 'storefront-outline', title: 'Vendor Dashboard', action: () => this.navigateToVendorDashboard() },\n        { icon: 'cube-outline', title: 'Manage Products', action: () => this.router.navigate(['/vendor/products']) },\n        { icon: 'analytics-outline', title: 'Sales Analytics', action: () => this.router.navigate(['/vendor/analytics']) }\n      );\n    }\n\n    if (this.canAccessAdminPanel()) {\n      baseFeatures.push(\n        { icon: 'shield-outline', title: 'Admin Panel', action: () => this.navigateToAdminPanel() },\n        { icon: 'people-outline', title: 'User Management', action: () => this.router.navigate(['/admin/users']) },\n        { icon: 'bar-chart-outline', title: 'System Analytics', action: () => this.router.navigate(['/admin/analytics']) }\n      );\n    }\n\n    return baseFeatures;\n  }\n}\n", "<div class=\"profile-container\">\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <ion-spinner name=\"crescent\"></ion-spinner>\n    <p>Loading profile...</p>\n  </div>\n\n  <!-- Profile Content -->\n  <div *ngIf=\"!isLoading && currentUser\" class=\"profile-content\">\n    <!-- Profile Header -->\n    <div class=\"profile-header\">\n      <app-optimized-image\n        [src]=\"currentUser.avatar\"\n        [alt]=\"currentUser.fullName\"\n        fallbackType=\"user\"\n        [width]=\"80\"\n        [height]=\"80\"\n        containerClass=\"profile-avatar\"\n        imageClass=\"avatar-img\"\n        objectFit=\"cover\">\n      </app-optimized-image>\n      <div class=\"profile-info\">\n        <h2>{{ currentUser.fullName }}</h2>\n        <p>{{ '@' + currentUser.username }}</p>\n        <span class=\"profile-role\">{{ getRoleDisplayName() }}</span>\n      </div>\n    </div>\n\n    <!-- Profile Sections -->\n    <div class=\"profile-sections\">\n      <!-- Role-based Features -->\n      <div class=\"profile-section\">\n        <h3 class=\"section-title\">Available Features</h3>\n        <div class=\"role-features\">\n          <div *ngFor=\"let feature of getRoleFeatures()\"\n               class=\"feature-card\"\n               (click)=\"feature.action()\">\n            <ion-icon [name]=\"feature.icon\" class=\"feature-icon\"></ion-icon>\n            <h4>{{ feature.title }}</h4>\n          </div>\n        </div>\n      </div>\n\n      <!-- Account Information -->\n      <div class=\"profile-section\">\n        <h3 class=\"section-title\">Account Information</h3>\n        <ul class=\"settings-list\">\n          <li class=\"settings-item\">\n            <span>Email</span>\n            <span>{{ currentUser.email }}</span>\n          </li>\n          <li class=\"settings-item\">\n            <span>Username</span>\n            <span>{{ currentUser.username }}</span>\n          </li>\n          <li class=\"settings-item\">\n            <span>Account Type</span>\n            <span>{{ getRoleDisplayName() }}</span>\n          </li>\n          <li class=\"settings-item\">\n            <span>Account Status</span>\n            <span [class]=\"currentUser.isActive ? 'text-success' : 'text-danger'\">\n              {{ currentUser.isActive ? 'Active' : 'Inactive' }}\n            </span>\n          </li>\n          <li class=\"settings-item\">\n            <span>Verification Status</span>\n            <span [class]=\"currentUser.isVerified ? 'text-success' : 'text-warning'\">\n              {{ currentUser.isVerified ? 'Verified' : 'Pending Verification' }}\n            </span>\n          </li>\n        </ul>\n      </div>\n\n      <!-- Role-specific Information -->\n      <div *ngIf=\"canAccessVendorDashboard()\" class=\"profile-section\">\n        <h3 class=\"section-title\">Vendor Information</h3>\n        <p>Access your vendor dashboard to manage products, view sales analytics, and handle orders.</p>\n        <button class=\"btn-primary\" (click)=\"navigateToVendorDashboard()\">\n          <ion-icon name=\"storefront-outline\"></ion-icon>\n          Go to Vendor Dashboard\n        </button>\n      </div>\n\n      <div *ngIf=\"canAccessAdminPanel()\" class=\"profile-section\">\n        <h3 class=\"section-title\">Administrator Access</h3>\n        <p>Access the admin panel to manage users, products, and system settings.</p>\n        <button class=\"btn-primary\" (click)=\"navigateToAdminPanel()\">\n          <ion-icon name=\"shield-outline\"></ion-icon>\n          Go to Admin Panel\n        </button>\n      </div>\n\n      <!-- Quick Actions -->\n      <div class=\"profile-section\">\n        <h3 class=\"section-title\">Quick Actions</h3>\n        <div class=\"action-buttons\">\n          <button class=\"btn-primary\" (click)=\"editProfile()\">\n            <ion-icon name=\"create-outline\"></ion-icon>\n            Edit Profile\n          </button>\n          <button class=\"btn-secondary\" (click)=\"navigateToSettings()\">\n            <ion-icon name=\"settings-outline\"></ion-icon>\n            Settings\n          </button>\n          <button class=\"btn-secondary\" (click)=\"logout()\">\n            <ion-icon name=\"log-out-outline\"></ion-icon>\n            Logout\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- No User State -->\n  <div *ngIf=\"!isLoading && !currentUser\" class=\"no-user-state\">\n    <ion-icon name=\"person-outline\" class=\"large-icon\"></ion-icon>\n    <h3>No User Found</h3>\n    <p>Please log in to view your profile.</p>\n    <button class=\"btn-primary\" (click)=\"navigateToLogin()\">\n      Login\n    </button>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,WAAW,QAAQ,gBAAgB;AAI5C,SAASC,uBAAuB,QAAQ,yEAAyE;;;;;;;;ICN/GC,EAAA,CAAAC,cAAA,aAAiD;IAC/CD,EAAA,CAAAE,SAAA,qBAA2C;IAC3CF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,yBAAkB;IACvBH,EADuB,CAAAI,YAAA,EAAI,EACrB;;;;;;IA6BEJ,EAAA,CAAAC,cAAA,cAEgC;IAA3BD,EAAA,CAAAK,UAAA,mBAAAC,4DAAA;MAAA,MAAAC,UAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASJ,UAAA,CAAAK,MAAA,EAAgB;IAAA,EAAC;IAC7BZ,EAAA,CAAAE,SAAA,mBAAgE;IAChEF,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IACzBH,EADyB,CAAAI,YAAA,EAAK,EACxB;;;;IAFMJ,EAAA,CAAAa,SAAA,EAAqB;IAArBb,EAAA,CAAAc,UAAA,SAAAP,UAAA,CAAAQ,IAAA,CAAqB;IAC3Bf,EAAA,CAAAa,SAAA,GAAmB;IAAnBb,EAAA,CAAAgB,iBAAA,CAAAT,UAAA,CAAAU,KAAA,CAAmB;;;;;;IAsC3BjB,EADF,CAAAC,cAAA,cAAgE,aACpC;IAAAD,EAAA,CAAAG,MAAA,yBAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACjDJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,gGAAyF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAChGJ,EAAA,CAAAC,cAAA,iBAAkE;IAAtCD,EAAA,CAAAK,UAAA,mBAAAa,+DAAA;MAAAlB,EAAA,CAAAQ,aAAA,CAAAW,GAAA;MAAA,MAAAC,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAW,WAAA,CAASS,MAAA,CAAAE,yBAAA,EAA2B;IAAA,EAAC;IAC/DtB,EAAA,CAAAE,SAAA,mBAA+C;IAC/CF,EAAA,CAAAG,MAAA,+BACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;;;;;;IAGJJ,EADF,CAAAC,cAAA,cAA2D,aAC/B;IAAAD,EAAA,CAAAG,MAAA,2BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACnDJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,6EAAsE;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC7EJ,EAAA,CAAAC,cAAA,iBAA6D;IAAjCD,EAAA,CAAAK,UAAA,mBAAAkB,+DAAA;MAAAvB,EAAA,CAAAQ,aAAA,CAAAgB,GAAA;MAAA,MAAAJ,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAW,WAAA,CAASS,MAAA,CAAAK,oBAAA,EAAsB;IAAA,EAAC;IAC1DzB,EAAA,CAAAE,SAAA,mBAA2C;IAC3CF,EAAA,CAAAG,MAAA,0BACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;;;;;;IAjFRJ,EAFF,CAAAC,cAAA,aAA+D,aAEjC;IAC1BD,EAAA,CAAAE,SAAA,6BASsB;IAEpBF,EADF,CAAAC,cAAA,aAA0B,SACpB;IAAAD,EAAA,CAAAG,MAAA,GAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACnCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACvCJ,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAG,MAAA,GAA0B;IAEzDH,EAFyD,CAAAI,YAAA,EAAO,EACxD,EACF;IAMFJ,EAHJ,CAAAC,cAAA,eAA8B,eAEC,cACD;IAAAD,EAAA,CAAAG,MAAA,0BAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACjDJ,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAA0B,UAAA,KAAAC,sCAAA,kBAEgC;IAKpC3B,EADE,CAAAI,YAAA,EAAM,EACF;IAIJJ,EADF,CAAAC,cAAA,eAA6B,cACD;IAAAD,EAAA,CAAAG,MAAA,2BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAG9CJ,EAFJ,CAAAC,cAAA,cAA0B,cACE,YAClB;IAAAD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAClBJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAAuB;IAC/BH,EAD+B,CAAAI,YAAA,EAAO,EACjC;IAEHJ,EADF,CAAAC,cAAA,cAA0B,YAClB;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACrBJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAA0B;IAClCH,EADkC,CAAAI,YAAA,EAAO,EACpC;IAEHJ,EADF,CAAAC,cAAA,cAA0B,YAClB;IAAAD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACzBJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAA0B;IAClCH,EADkC,CAAAI,YAAA,EAAO,EACpC;IAEHJ,EADF,CAAAC,cAAA,cAA0B,YAClB;IAAAD,EAAA,CAAAG,MAAA,sBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC3BJ,EAAA,CAAAC,cAAA,YAAsE;IACpED,EAAA,CAAAG,MAAA,IACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACJ;IAEHJ,EADF,CAAAC,cAAA,cAA0B,YAClB;IAAAD,EAAA,CAAAG,MAAA,2BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAChCJ,EAAA,CAAAC,cAAA,YAAyE;IACvED,EAAA,CAAAG,MAAA,IACF;IAGNH,EAHM,CAAAI,YAAA,EAAO,EACJ,EACF,EACD;IAYNJ,EATA,CAAA0B,UAAA,KAAAE,sCAAA,kBAAgE,KAAAC,sCAAA,kBASL;IAWzD7B,EADF,CAAAC,cAAA,eAA6B,cACD;IAAAD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAE1CJ,EADF,CAAAC,cAAA,eAA4B,kBAC0B;IAAxBD,EAAA,CAAAK,UAAA,mBAAAyB,yDAAA;MAAA9B,EAAA,CAAAQ,aAAA,CAAAuB,GAAA;MAAA,MAAAX,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAW,WAAA,CAASS,MAAA,CAAAY,WAAA,EAAa;IAAA,EAAC;IACjDhC,EAAA,CAAAE,SAAA,oBAA2C;IAC3CF,EAAA,CAAAG,MAAA,sBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAA6D;IAA/BD,EAAA,CAAAK,UAAA,mBAAA4B,yDAAA;MAAAjC,EAAA,CAAAQ,aAAA,CAAAuB,GAAA;MAAA,MAAAX,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAW,WAAA,CAASS,MAAA,CAAAc,kBAAA,EAAoB;IAAA,EAAC;IAC1DlC,EAAA,CAAAE,SAAA,oBAA6C;IAC7CF,EAAA,CAAAG,MAAA,kBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAiD;IAAnBD,EAAA,CAAAK,UAAA,mBAAA8B,yDAAA;MAAAnC,EAAA,CAAAQ,aAAA,CAAAuB,GAAA;MAAA,MAAAX,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAW,WAAA,CAASS,MAAA,CAAAgB,MAAA,EAAQ;IAAA,EAAC;IAC9CpC,EAAA,CAAAE,SAAA,oBAA4C;IAC5CF,EAAA,CAAAG,MAAA,gBACF;IAIRH,EAJQ,CAAAI,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;IApGAJ,EAAA,CAAAa,SAAA,GAA0B;IAI1Bb,EAJA,CAAAc,UAAA,QAAAM,MAAA,CAAAiB,WAAA,CAAAC,MAAA,CAA0B,QAAAlB,MAAA,CAAAiB,WAAA,CAAAE,QAAA,CACE,aAEhB,cACC;IAMTvC,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAgB,iBAAA,CAAAI,MAAA,CAAAiB,WAAA,CAAAE,QAAA,CAA0B;IAC3BvC,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAgB,iBAAA,OAAAI,MAAA,CAAAiB,WAAA,CAAAG,QAAA,CAAgC;IACRxC,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAgB,iBAAA,CAAAI,MAAA,CAAAqB,kBAAA,GAA0B;IAU1BzC,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAAc,UAAA,YAAAM,MAAA,CAAAsB,eAAA,GAAoB;IAerC1C,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAgB,iBAAA,CAAAI,MAAA,CAAAiB,WAAA,CAAAM,KAAA,CAAuB;IAIvB3C,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAgB,iBAAA,CAAAI,MAAA,CAAAiB,WAAA,CAAAG,QAAA,CAA0B;IAI1BxC,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAgB,iBAAA,CAAAI,MAAA,CAAAqB,kBAAA,GAA0B;IAI1BzC,EAAA,CAAAa,SAAA,GAA+D;IAA/Db,EAAA,CAAA4C,UAAA,CAAAxB,MAAA,CAAAiB,WAAA,CAAAQ,QAAA,kCAA+D;IACnE7C,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAA8C,kBAAA,MAAA1B,MAAA,CAAAiB,WAAA,CAAAQ,QAAA,8BACF;IAIM7C,EAAA,CAAAa,SAAA,GAAkE;IAAlEb,EAAA,CAAA4C,UAAA,CAAAxB,MAAA,CAAAiB,WAAA,CAAAU,UAAA,mCAAkE;IACtE/C,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAA8C,kBAAA,MAAA1B,MAAA,CAAAiB,WAAA,CAAAU,UAAA,4CACF;IAMA/C,EAAA,CAAAa,SAAA,EAAgC;IAAhCb,EAAA,CAAAc,UAAA,SAAAM,MAAA,CAAA4B,wBAAA,GAAgC;IAShChD,EAAA,CAAAa,SAAA,EAA2B;IAA3Bb,EAAA,CAAAc,UAAA,SAAAM,MAAA,CAAA6B,mBAAA,GAA2B;;;;;;IA+BrCjD,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAE,SAAA,mBAA8D;IAC9DF,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACtBJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,0CAAmC;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC1CJ,EAAA,CAAAC,cAAA,iBAAwD;IAA5BD,EAAA,CAAAK,UAAA,mBAAA6C,wDAAA;MAAAlD,EAAA,CAAAQ,aAAA,CAAA2C,GAAA;MAAA,MAAA/B,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAW,WAAA,CAASS,MAAA,CAAAgC,eAAA,EAAiB;IAAA,EAAC;IACrDpD,EAAA,CAAAG,MAAA,cACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;;;AD2ER,OAAM,MAAOiD,gBAAgB;EAI3BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IALhB,KAAAnB,WAAW,GAAgB,IAAI;IAC/B,KAAAoB,SAAS,GAAG,IAAI;EAKb;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACb,IAAI,CAACtB,WAAW,GAAG,IAAI,CAACkB,WAAW,CAACK,gBAAgB;IACpD,IAAI,CAACH,SAAS,GAAG,KAAK;EACxB;EAEA;EACAT,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAACO,WAAW,CAACM,QAAQ,EAAE,IAAI,IAAI,CAACN,WAAW,CAACO,OAAO,EAAE;EAClE;EAEAb,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAACM,WAAW,CAACO,OAAO,EAAE;EACnC;EAEAC,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACR,WAAW,CAACM,QAAQ,EAAE,IAAI,IAAI,CAACN,WAAW,CAACO,OAAO,EAAE;EAClE;EAEAE,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACT,WAAW,CAACM,QAAQ,EAAE,IAAI,IAAI,CAACN,WAAW,CAACO,OAAO,EAAE;EAClE;EAEA;EACAxC,yBAAyBA,CAAA;IACvB,IAAI,IAAI,CAAC0B,wBAAwB,EAAE,EAAE;MACnC,IAAI,CAACQ,MAAM,CAACS,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;;EAE/C;EAEAxC,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACwB,mBAAmB,EAAE,EAAE;MAC9B,IAAI,CAACO,MAAM,CAACS,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;;EAE9C;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAACV,MAAM,CAACS,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAEAE,kBAAkBA,CAAA;IAChB,IAAI,CAACX,MAAM,CAACS,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACrC;EAEAG,cAAcA,CAAA;IACZ,IAAI,CAACZ,MAAM,CAACS,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;EAEA/B,kBAAkBA,CAAA;IAChB,IAAI,CAACsB,MAAM,CAACS,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;EAC7C;EAEAjC,WAAWA,CAAA;IACT,IAAI,CAACwB,MAAM,CAACS,QAAQ,CAAC,CAAC,uBAAuB,CAAC,CAAC;EACjD;EAEA7B,MAAMA,CAAA;IACJ,IAAI,CAACmB,WAAW,CAACnB,MAAM,EAAE;IACzB,IAAI,CAACoB,MAAM,CAACS,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEAxB,kBAAkBA,CAAA;IAChB,QAAQ,IAAI,CAACJ,WAAW,EAAEgC,IAAI;MAC5B,KAAK,UAAU;QACb,OAAO,UAAU;MACnB,KAAK,QAAQ;QACX,OAAO,QAAQ;MACjB,KAAK,OAAO;QACV,OAAO,eAAe;MACxB;QACE,OAAO,MAAM;;EAEnB;EAEA3B,eAAeA,CAAA;IACb,MAAM4B,YAAY,GAAG,CACnB;MAAEvD,IAAI,EAAE,gBAAgB;MAAEE,KAAK,EAAE,cAAc;MAAEL,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACoB,WAAW;IAAE,CAAE,EACnF;MAAEjB,IAAI,EAAE,aAAa;MAAEE,KAAK,EAAE,WAAW;MAAEL,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACsD,gBAAgB;IAAE,CAAE,EAClF;MAAEnD,IAAI,EAAE,eAAe;MAAEE,KAAK,EAAE,UAAU;MAAEL,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACuD,kBAAkB;IAAE,CAAE,EACrF;MAAEpD,IAAI,EAAE,cAAc;MAAEE,KAAK,EAAE,eAAe;MAAEL,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACwD,cAAc;IAAE,CAAE,EACrF;MAAErD,IAAI,EAAE,kBAAkB;MAAEE,KAAK,EAAE,UAAU;MAAEL,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACsB,kBAAkB;IAAE,CAAE,CACzF;IAED,IAAI,IAAI,CAACc,wBAAwB,EAAE,EAAE;MACnCsB,YAAY,CAACC,IAAI,CACf;QAAExD,IAAI,EAAE,oBAAoB;QAAEE,KAAK,EAAE,kBAAkB;QAAEL,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACU,yBAAyB;MAAE,CAAE,EACzG;QAAEP,IAAI,EAAE,cAAc;QAAEE,KAAK,EAAE,iBAAiB;QAAEL,MAAM,EAAEA,CAAA,KAAM,IAAI,CAAC4C,MAAM,CAACS,QAAQ,CAAC,CAAC,kBAAkB,CAAC;MAAC,CAAE,EAC5G;QAAElD,IAAI,EAAE,mBAAmB;QAAEE,KAAK,EAAE,iBAAiB;QAAEL,MAAM,EAAEA,CAAA,KAAM,IAAI,CAAC4C,MAAM,CAACS,QAAQ,CAAC,CAAC,mBAAmB,CAAC;MAAC,CAAE,CACnH;;IAGH,IAAI,IAAI,CAAChB,mBAAmB,EAAE,EAAE;MAC9BqB,YAAY,CAACC,IAAI,CACf;QAAExD,IAAI,EAAE,gBAAgB;QAAEE,KAAK,EAAE,aAAa;QAAEL,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACa,oBAAoB;MAAE,CAAE,EAC3F;QAAEV,IAAI,EAAE,gBAAgB;QAAEE,KAAK,EAAE,iBAAiB;QAAEL,MAAM,EAAEA,CAAA,KAAM,IAAI,CAAC4C,MAAM,CAACS,QAAQ,CAAC,CAAC,cAAc,CAAC;MAAC,CAAE,EAC1G;QAAElD,IAAI,EAAE,mBAAmB;QAAEE,KAAK,EAAE,kBAAkB;QAAEL,MAAM,EAAEA,CAAA,KAAM,IAAI,CAAC4C,MAAM,CAACS,QAAQ,CAAC,CAAC,kBAAkB,CAAC;MAAC,CAAE,CACnH;;IAGH,OAAOK,YAAY;EACrB;;;uBAhHWjB,gBAAgB,EAAArD,EAAA,CAAAwE,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1E,EAAA,CAAAwE,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAhBvB,gBAAgB;MAAAwB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA/E,EAAA,CAAAgF,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrM7BtF,EAAA,CAAAC,cAAA,aAA+B;UAmH7BD,EAjHA,CAAA0B,UAAA,IAAA8D,+BAAA,iBAAiD,IAAAC,+BAAA,mBAMc,IAAAC,+BAAA,iBA2GD;UAQhE1F,EAAA,CAAAI,YAAA,EAAM;;;UAzHEJ,EAAA,CAAAa,SAAA,EAAe;UAAfb,EAAA,CAAAc,UAAA,SAAAyE,GAAA,CAAA9B,SAAA,CAAe;UAMfzD,EAAA,CAAAa,SAAA,EAA+B;UAA/Bb,EAAA,CAAAc,UAAA,UAAAyE,GAAA,CAAA9B,SAAA,IAAA8B,GAAA,CAAAlD,WAAA,CAA+B;UA2G/BrC,EAAA,CAAAa,SAAA,EAAgC;UAAhCb,EAAA,CAAAc,UAAA,UAAAyE,GAAA,CAAA9B,SAAA,KAAA8B,GAAA,CAAAlD,WAAA,CAAgC;;;qBDrG5BzC,YAAY,EAAA+F,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEhG,WAAW,EAAEC,WAAW,EAAAgG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,UAAA,EAAEjG,uBAAuB;MAAAkG,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}