{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { c as createAnimation } from './animation-6a0c5338.js';\nimport { g as getIonPageElement } from './index-fae1515c.js';\nimport './index-a5d50daf.js';\nimport './helpers-be245865.js';\nimport './index-a1a47f01.js';\nconst mdTransitionAnimation = (_, opts) => {\n  var _a, _b, _c;\n  const OFF_BOTTOM = '40px';\n  const CENTER = '0px';\n  const backDirection = opts.direction === 'back';\n  const enteringEl = opts.enteringEl;\n  const leavingEl = opts.leavingEl;\n  const ionPageElement = getIonPageElement(enteringEl);\n  const enteringToolbarEle = ionPageElement.querySelector('ion-toolbar');\n  const rootTransition = createAnimation();\n  rootTransition.addElement(ionPageElement).fill('both').beforeRemoveClass('ion-page-invisible');\n  // animate the component itself\n  if (backDirection) {\n    rootTransition.duration(((_a = opts.duration) !== null && _a !== void 0 ? _a : 0) || 200).easing('cubic-bezier(0.47,0,0.745,0.715)');\n  } else {\n    rootTransition.duration(((_b = opts.duration) !== null && _b !== void 0 ? _b : 0) || 280).easing('cubic-bezier(0.36,0.66,0.04,1)').fromTo('transform', `translateY(${OFF_BOTTOM})`, `translateY(${CENTER})`).fromTo('opacity', 0.01, 1);\n  }\n  // Animate toolbar if it's there\n  if (enteringToolbarEle) {\n    const enteringToolBar = createAnimation();\n    enteringToolBar.addElement(enteringToolbarEle);\n    rootTransition.addAnimation(enteringToolBar);\n  }\n  // setup leaving view\n  if (leavingEl && backDirection) {\n    // leaving content\n    rootTransition.duration(((_c = opts.duration) !== null && _c !== void 0 ? _c : 0) || 200).easing('cubic-bezier(0.47,0,0.745,0.715)');\n    const leavingPage = createAnimation();\n    leavingPage.addElement(getIonPageElement(leavingEl)).onFinish(currentStep => {\n      if (currentStep === 1 && leavingPage.elements.length > 0) {\n        leavingPage.elements[0].style.setProperty('display', 'none');\n      }\n    }).fromTo('transform', `translateY(${CENTER})`, `translateY(${OFF_BOTTOM})`).fromTo('opacity', 1, 0);\n    rootTransition.addAnimation(leavingPage);\n  }\n  return rootTransition;\n};\nexport { mdTransitionAnimation };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}