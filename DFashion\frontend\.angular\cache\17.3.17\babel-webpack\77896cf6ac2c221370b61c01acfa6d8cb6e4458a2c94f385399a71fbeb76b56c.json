{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nlet TrendingProductsComponent = class TrendingProductsComponent {\n  constructor(trendingService, socialService, cartService, wishlistService, router) {\n    this.trendingService = trendingService;\n    this.socialService = socialService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.router = router;\n    this.trendingProducts = [];\n    this.isLoading = true;\n    this.error = null;\n    this.likedProducts = new Set();\n    this.subscription = new Subscription();\n  }\n  ngOnInit() {\n    this.loadTrendingProducts();\n    this.subscribeTrendingProducts();\n    this.subscribeLikedProducts();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n  subscribeTrendingProducts() {\n    this.subscription.add(this.trendingService.trendingProducts$.subscribe(products => {\n      this.trendingProducts = products;\n      this.isLoading = false;\n    }));\n  }\n  subscribeLikedProducts() {\n    this.subscription.add(this.socialService.likedProducts$.subscribe(likedProducts => {\n      this.likedProducts = likedProducts;\n    }));\n  }\n  loadTrendingProducts() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        yield _this.trendingService.loadTrendingProducts(1, 8);\n      } catch (error) {\n        console.error('Error loading trending products:', error);\n        _this.error = 'Failed to load trending products';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onProductClick(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  onLikeProduct(product, event) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const result = yield _this2.socialService.likeProduct(product._id);\n        if (result.success) {\n          console.log(result.message);\n        } else {\n          console.error('Failed to like product:', result.message);\n        }\n      } catch (error) {\n        console.error('Error liking product:', error);\n      }\n    })();\n  }\n  onShareProduct(product, event) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        // For now, copy link to clipboard\n        const productUrl = `${window.location.origin}/product/${product._id}`;\n        yield navigator.clipboard.writeText(productUrl);\n        // Track the share\n        yield _this3.socialService.shareProduct(product._id, {\n          platform: 'copy_link',\n          message: `Check out this amazing ${product.name} from ${product.brand}!`\n        });\n        console.log('Product link copied to clipboard!');\n      } catch (error) {\n        console.error('Error sharing product:', error);\n      }\n    })();\n  }\n  onAddToCart(product, event) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        yield _this4.cartService.addToCart(product._id, 1);\n        console.log('Product added to cart!');\n      } catch (error) {\n        console.error('Error adding to cart:', error);\n      }\n    })();\n  }\n  onAddToWishlist(product, event) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        yield _this5.wishlistService.addToWishlist(product._id);\n        console.log('Product added to wishlist!');\n      } catch (error) {\n        console.error('Error adding to wishlist:', error);\n      }\n    })();\n  }\n  getDiscountPercentage(product) {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n    }\n    return 0;\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  onRetry() {\n    this.loadTrendingProducts();\n  }\n  onViewAll() {\n    this.router.navigate(['/products'], {\n      queryParams: {\n        filter: 'trending'\n      }\n    });\n  }\n  isProductLiked(productId) {\n    return this.likedProducts.has(productId);\n  }\n  trackByProductId(index, product) {\n    return product._id;\n  }\n};\nTrendingProductsComponent = __decorate([Component({\n  selector: 'app-trending-products',\n  standalone: true,\n  imports: [CommonModule, IonicModule, OwlCarouselOModule],\n  templateUrl: './trending-products.component.html',\n  styleUrls: ['./trending-products.component.scss']\n})], TrendingProductsComponent);\nexport { TrendingProductsComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "Subscription", "IonicModule", "TrendingProductsComponent", "constructor", "trendingService", "socialService", "cartService", "wishlistService", "router", "trendingProducts", "isLoading", "error", "likedProducts", "Set", "subscription", "ngOnInit", "loadTrendingProducts", "subscribeTrendingProducts", "subscribeLikedProducts", "ngOnDestroy", "unsubscribe", "add", "trendingProducts$", "subscribe", "products", "likedProducts$", "_this", "_asyncToGenerator", "console", "onProductClick", "product", "navigate", "_id", "onLikeProduct", "event", "_this2", "stopPropagation", "result", "likeProduct", "success", "log", "message", "onShareProduct", "_this3", "productUrl", "window", "location", "origin", "navigator", "clipboard", "writeText", "shareProduct", "platform", "name", "brand", "onAddToCart", "_this4", "addToCart", "onAddToWishlist", "_this5", "addToWishlist", "getDiscountPercentage", "originalPrice", "price", "Math", "round", "formatPrice", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "onRetry", "onViewAll", "queryParams", "filter", "isProductLiked", "productId", "has", "trackByProductId", "index", "__decorate", "selector", "standalone", "imports", "OwlCarouselOModule", "templateUrl", "styleUrls"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\trending-products\\trending-products.component.ts"], "sourcesContent": ["import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { TrendingService } from '../../../../core/services/trending.service';\nimport { Product } from '../../../../core/models/product.model';\nimport { SocialInteractionsService } from '../../../../core/services/social-interactions.service';\nimport { CartService } from '../../../../core/services/cart.service';\nimport { WishlistService } from '../../../../core/services/wishlist.service';\nimport { IonicModule } from '@ionic/angular';\nimport { OwlOptions } from 'ngx-owl-carousel-o';\n\n@Component({\n  selector: 'app-trending-products',\n  standalone: true,\n  imports: [CommonModule, IonicModule, OwlCarouselOModule],\n  templateUrl: './trending-products.component.html',\n  styleUrls: ['./trending-products.component.scss']\n})\nexport class TrendingProductsComponent implements OnInit, OnDestroy {\n  trendingProducts: Product[] = [];\n  isLoading = true;\n  error: string | null = null;\n  likedProducts = new Set<string>();\n  private subscription: Subscription = new Subscription();\n\n  constructor(\n    private trendingService: TrendingService,\n    private socialService: SocialInteractionsService,\n    private cartService: CartService,\n    private wishlistService: WishlistService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadTrendingProducts();\n    this.subscribeTrendingProducts();\n    this.subscribeLikedProducts();\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n\n  private subscribeTrendingProducts() {\n    this.subscription.add(\n      this.trendingService.trendingProducts$.subscribe(products => {\n        this.trendingProducts = products;\n        this.isLoading = false;\n      })\n    );\n  }\n\n  private subscribeLikedProducts() {\n    this.subscription.add(\n      this.socialService.likedProducts$.subscribe(likedProducts => {\n        this.likedProducts = likedProducts;\n      })\n    );\n  }\n\n  private async loadTrendingProducts() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      await this.trendingService.loadTrendingProducts(1, 8);\n    } catch (error) {\n      console.error('Error loading trending products:', error);\n      this.error = 'Failed to load trending products';\n      this.isLoading = false;\n    }\n  }\n\n  onProductClick(product: Product) {\n    this.router.navigate(['/product', product._id]);\n  }\n\n  async onLikeProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      const result = await this.socialService.likeProduct(product._id);\n      if (result.success) {\n        console.log(result.message);\n      } else {\n        console.error('Failed to like product:', result.message);\n      }\n    } catch (error) {\n      console.error('Error liking product:', error);\n    }\n  }\n\n  async onShareProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      // For now, copy link to clipboard\n      const productUrl = `${window.location.origin}/product/${product._id}`;\n      await navigator.clipboard.writeText(productUrl);\n\n      // Track the share\n      await this.socialService.shareProduct(product._id, {\n        platform: 'copy_link',\n        message: `Check out this amazing ${product.name} from ${product.brand}!`\n      });\n\n      console.log('Product link copied to clipboard!');\n    } catch (error) {\n      console.error('Error sharing product:', error);\n    }\n  }\n\n  async onAddToCart(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      await this.cartService.addToCart(product._id, 1);\n      console.log('Product added to cart!');\n    } catch (error) {\n      console.error('Error adding to cart:', error);\n    }\n  }\n\n  async onAddToWishlist(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      await this.wishlistService.addToWishlist(product._id);\n      console.log('Product added to wishlist!');\n    } catch (error) {\n      console.error('Error adding to wishlist:', error);\n    }\n  }\n\n  getDiscountPercentage(product: Product): number {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);\n    }\n    return 0;\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n\n  onRetry() {\n    this.loadTrendingProducts();\n  }\n\n  onViewAll() {\n    this.router.navigate(['/products'], {\n      queryParams: { filter: 'trending' }\n    });\n  }\n\n  isProductLiked(productId: string): boolean {\n    return this.likedProducts.has(productId);\n  }\n\n  trackByProductId(index: number, product: Product): string {\n    return product._id;\n  }\n}\n"], "mappings": ";;AAAA,SAASA,SAAS,QAA2B,eAAe;AAC5D,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AAMnC,SAASC,WAAW,QAAQ,gBAAgB;AAUrC,IAAMC,yBAAyB,GAA/B,MAAMA,yBAAyB;EAOpCC,YACUC,eAAgC,EAChCC,aAAwC,EACxCC,WAAwB,EACxBC,eAAgC,EAChCC,MAAc;IAJd,KAAAJ,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IAXhB,KAAAC,gBAAgB,GAAc,EAAE;IAChC,KAAAC,SAAS,GAAG,IAAI;IAChB,KAAAC,KAAK,GAAkB,IAAI;IAC3B,KAAAC,aAAa,GAAG,IAAIC,GAAG,EAAU;IACzB,KAAAC,YAAY,GAAiB,IAAId,YAAY,EAAE;EAQpD;EAEHe,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,yBAAyB,EAAE;IAChC,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACL,YAAY,CAACM,WAAW,EAAE;EACjC;EAEQH,yBAAyBA,CAAA;IAC/B,IAAI,CAACH,YAAY,CAACO,GAAG,CACnB,IAAI,CAACjB,eAAe,CAACkB,iBAAiB,CAACC,SAAS,CAACC,QAAQ,IAAG;MAC1D,IAAI,CAACf,gBAAgB,GAAGe,QAAQ;MAChC,IAAI,CAACd,SAAS,GAAG,KAAK;IACxB,CAAC,CAAC,CACH;EACH;EAEQQ,sBAAsBA,CAAA;IAC5B,IAAI,CAACJ,YAAY,CAACO,GAAG,CACnB,IAAI,CAAChB,aAAa,CAACoB,cAAc,CAACF,SAAS,CAACX,aAAa,IAAG;MAC1D,IAAI,CAACA,aAAa,GAAGA,aAAa;IACpC,CAAC,CAAC,CACH;EACH;EAEcI,oBAAoBA,CAAA;IAAA,IAAAU,KAAA;IAAA,OAAAC,iBAAA;MAChC,IAAI;QACFD,KAAI,CAAChB,SAAS,GAAG,IAAI;QACrBgB,KAAI,CAACf,KAAK,GAAG,IAAI;QACjB,MAAMe,KAAI,CAACtB,eAAe,CAACY,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC;OACtD,CAAC,OAAOL,KAAK,EAAE;QACdiB,OAAO,CAACjB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxDe,KAAI,CAACf,KAAK,GAAG,kCAAkC;QAC/Ce,KAAI,CAAChB,SAAS,GAAG,KAAK;;IACvB;EACH;EAEAmB,cAAcA,CAACC,OAAgB;IAC7B,IAAI,CAACtB,MAAM,CAACuB,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAACE,GAAG,CAAC,CAAC;EACjD;EAEMC,aAAaA,CAACH,OAAgB,EAAEI,KAAY;IAAA,IAAAC,MAAA;IAAA,OAAAR,iBAAA;MAChDO,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMC,MAAM,SAASF,MAAI,CAAC9B,aAAa,CAACiC,WAAW,CAACR,OAAO,CAACE,GAAG,CAAC;QAChE,IAAIK,MAAM,CAACE,OAAO,EAAE;UAClBX,OAAO,CAACY,GAAG,CAACH,MAAM,CAACI,OAAO,CAAC;SAC5B,MAAM;UACLb,OAAO,CAACjB,KAAK,CAAC,yBAAyB,EAAE0B,MAAM,CAACI,OAAO,CAAC;;OAE3D,CAAC,OAAO9B,KAAK,EAAE;QACdiB,OAAO,CAACjB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;IAC9C;EACH;EAEM+B,cAAcA,CAACZ,OAAgB,EAAEI,KAAY;IAAA,IAAAS,MAAA;IAAA,OAAAhB,iBAAA;MACjDO,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF;QACA,MAAMQ,UAAU,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,YAAYjB,OAAO,CAACE,GAAG,EAAE;QACrE,MAAMgB,SAAS,CAACC,SAAS,CAACC,SAAS,CAACN,UAAU,CAAC;QAE/C;QACA,MAAMD,MAAI,CAACtC,aAAa,CAAC8C,YAAY,CAACrB,OAAO,CAACE,GAAG,EAAE;UACjDoB,QAAQ,EAAE,WAAW;UACrBX,OAAO,EAAE,0BAA0BX,OAAO,CAACuB,IAAI,SAASvB,OAAO,CAACwB,KAAK;SACtE,CAAC;QAEF1B,OAAO,CAACY,GAAG,CAAC,mCAAmC,CAAC;OACjD,CAAC,OAAO7B,KAAK,EAAE;QACdiB,OAAO,CAACjB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;IAC/C;EACH;EAEM4C,WAAWA,CAACzB,OAAgB,EAAEI,KAAY;IAAA,IAAAsB,MAAA;IAAA,OAAA7B,iBAAA;MAC9CO,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMoB,MAAI,CAAClD,WAAW,CAACmD,SAAS,CAAC3B,OAAO,CAACE,GAAG,EAAE,CAAC,CAAC;QAChDJ,OAAO,CAACY,GAAG,CAAC,wBAAwB,CAAC;OACtC,CAAC,OAAO7B,KAAK,EAAE;QACdiB,OAAO,CAACjB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;IAC9C;EACH;EAEM+C,eAAeA,CAAC5B,OAAgB,EAAEI,KAAY;IAAA,IAAAyB,MAAA;IAAA,OAAAhC,iBAAA;MAClDO,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMuB,MAAI,CAACpD,eAAe,CAACqD,aAAa,CAAC9B,OAAO,CAACE,GAAG,CAAC;QACrDJ,OAAO,CAACY,GAAG,CAAC,4BAA4B,CAAC;OAC1C,CAAC,OAAO7B,KAAK,EAAE;QACdiB,OAAO,CAACjB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;;IAClD;EACH;EAEAkD,qBAAqBA,CAAC/B,OAAgB;IACpC,IAAIA,OAAO,CAACgC,aAAa,IAAIhC,OAAO,CAACgC,aAAa,GAAGhC,OAAO,CAACiC,KAAK,EAAE;MAClE,OAAOC,IAAI,CAACC,KAAK,CAAE,CAACnC,OAAO,CAACgC,aAAa,GAAGhC,OAAO,CAACiC,KAAK,IAAIjC,OAAO,CAACgC,aAAa,GAAI,GAAG,CAAC;;IAE5F,OAAO,CAAC;EACV;EAEAI,WAAWA,CAACH,KAAa;IACvB,OAAO,IAAII,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACT,KAAK,CAAC;EAClB;EAEAU,OAAOA,CAAA;IACL,IAAI,CAACzD,oBAAoB,EAAE;EAC7B;EAEA0D,SAASA,CAAA;IACP,IAAI,CAAClE,MAAM,CAACuB,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;MAClC4C,WAAW,EAAE;QAAEC,MAAM,EAAE;MAAU;KAClC,CAAC;EACJ;EAEAC,cAAcA,CAACC,SAAiB;IAC9B,OAAO,IAAI,CAAClE,aAAa,CAACmE,GAAG,CAACD,SAAS,CAAC;EAC1C;EAEAE,gBAAgBA,CAACC,KAAa,EAAEnD,OAAgB;IAC9C,OAAOA,OAAO,CAACE,GAAG;EACpB;CACD;AA/IY9B,yBAAyB,GAAAgF,UAAA,EAPrCpF,SAAS,CAAC;EACTqF,QAAQ,EAAE,uBAAuB;EACjCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACtF,YAAY,EAAEE,WAAW,EAAEqF,kBAAkB,CAAC;EACxDC,WAAW,EAAE,oCAAoC;EACjDC,SAAS,EAAE,CAAC,oCAAoC;CACjD,CAAC,C,EACWtF,yBAAyB,CA+IrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}