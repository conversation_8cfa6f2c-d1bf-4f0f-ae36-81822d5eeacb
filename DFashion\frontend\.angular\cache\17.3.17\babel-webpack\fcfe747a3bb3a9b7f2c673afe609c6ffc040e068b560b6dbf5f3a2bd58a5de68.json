{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../../../core/services/upload.service\";\nimport * as i4 from \"@angular/common\";\nfunction CreateProductComponent_div_61_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵelement(1, \"img\", 44);\n    i0.ɵɵelementStart(2, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function CreateProductComponent_div_61_div_1_Template_button_click_2_listener() {\n      const i_r4 = i0.ɵɵrestoreView(_r3).index;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.removeImage(i_r4));\n    });\n    i0.ɵɵelement(3, \"i\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const image_r6 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", image_r6.preview, i0.ɵɵsanitizeUrl)(\"alt\", \"Image \" + (i_r4 + 1));\n  }\n}\nfunction CreateProductComponent_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtemplate(1, CreateProductComponent_div_61_div_1_Template, 4, 2, \"div\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.selectedImages);\n  }\n}\nfunction CreateProductComponent_label_70_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\")(1, \"input\", 47);\n    i0.ɵɵlistener(\"change\", function CreateProductComponent_label_70_Template_input_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSizeChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const size_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", size_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", size_r8, \" \");\n  }\n}\nfunction CreateProductComponent_label_75_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 48)(1, \"input\", 47);\n    i0.ɵɵlistener(\"change\", function CreateProductComponent_label_75_Template_input_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onColorChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(2, \"span\", 49);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const color_r10 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", color_r10.value);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", color_r10.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", color_r10.name, \" \");\n  }\n}\nfunction CreateProductComponent_span_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Creating...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateProductComponent_span_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Create Product\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let CreateProductComponent = /*#__PURE__*/(() => {\n  class CreateProductComponent {\n    constructor(fb, router, uploadService) {\n      this.fb = fb;\n      this.router = router;\n      this.uploadService = uploadService;\n      this.selectedImages = [];\n      this.selectedSizes = [];\n      this.selectedColors = [];\n      this.uploading = false;\n      this.uploadProgress = null;\n      this.isUploading = false;\n      this.availableSizes = ['XS', 'S', 'M', 'L', 'XL', 'XXL'];\n      this.availableColors = [{\n        name: 'Black',\n        value: '#000000'\n      }, {\n        name: 'White',\n        value: '#FFFFFF'\n      }, {\n        name: 'Red',\n        value: '#FF0000'\n      }, {\n        name: 'Blue',\n        value: '#0000FF'\n      }, {\n        name: 'Green',\n        value: '#008000'\n      }, {\n        name: 'Yellow',\n        value: '#FFFF00'\n      }];\n      this.productForm = this.fb.group({\n        name: ['', Validators.required],\n        brand: [''],\n        category: ['', Validators.required],\n        description: ['', Validators.required],\n        price: ['', [Validators.required, Validators.min(1)]],\n        originalPrice: [''],\n        stock: ['', [Validators.required, Validators.min(0)]],\n        sku: ['']\n      });\n    }\n    ngOnInit() {\n      // Subscribe to upload progress\n      this.uploadService.getUploadProgress().subscribe(progress => {\n        this.uploadProgress = progress;\n        this.isUploading = progress?.status === 'uploading';\n      });\n    }\n    onFileSelect(event) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        const files = Array.from(event.target.files);\n        // Validate files\n        const validation = _this.uploadService.validateFiles(files, 'image', 5);\n        if (!validation.isValid) {\n          alert(validation.errors.join('\\n'));\n          return;\n        }\n        // Add files to selection with previews\n        for (const file of files) {\n          if (_this.selectedImages.length < 5) {\n            try {\n              const preview = yield _this.uploadService.createFilePreview(file);\n              _this.selectedImages.push({\n                file,\n                preview,\n                name: file.name,\n                size: _this.uploadService.formatFileSize(file.size),\n                uploaded: false,\n                url: null\n              });\n            } catch (error) {\n              console.error('Error creating preview:', error);\n            }\n          }\n        }\n      })();\n    }\n    removeImage(index) {\n      this.selectedImages.splice(index, 1);\n    }\n    // Upload selected images\n    uploadImages() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        if (_this2.selectedImages.length === 0) {\n          return [];\n        }\n        const filesToUpload = _this2.selectedImages.filter(img => !img.uploaded).map(img => img.file);\n        if (filesToUpload.length === 0) {\n          // All images already uploaded\n          return _this2.selectedImages.map(img => img.url).filter(url => url);\n        }\n        try {\n          _this2.isUploading = true;\n          const response = yield _this2.uploadService.uploadProductImages(filesToUpload).toPromise();\n          if (response?.success && response.data.images) {\n            // Update selected images with upload results\n            response.data.images.forEach((uploadedImage, index) => {\n              const imageIndex = _this2.selectedImages.findIndex(img => !img.uploaded);\n              if (imageIndex !== -1) {\n                _this2.selectedImages[imageIndex].uploaded = true;\n                _this2.selectedImages[imageIndex].url = uploadedImage.url;\n              }\n            });\n            return response.data.images.map(img => img.url);\n          }\n          throw new Error(response?.message || 'Upload failed');\n        } catch (error) {\n          console.error('Upload error:', error);\n          throw error;\n        } finally {\n          _this2.isUploading = false;\n        }\n      })();\n    }\n    onSizeChange(event) {\n      const size = event.target.value;\n      if (event.target.checked) {\n        this.selectedSizes.push(size);\n      } else {\n        this.selectedSizes = this.selectedSizes.filter(s => s !== size);\n      }\n    }\n    onColorChange(event) {\n      const color = event.target.value;\n      if (event.target.checked) {\n        this.selectedColors.push(color);\n      } else {\n        this.selectedColors = this.selectedColors.filter(c => c !== color);\n      }\n    }\n    saveDraft() {\n      console.log('Saving as draft...');\n      alert('Draft saved successfully!');\n    }\n    onSubmit() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this3.productForm.valid) {\n          alert('Please fill in all required fields');\n          return;\n        }\n        if (_this3.selectedImages.length === 0) {\n          alert('Please select at least one product image');\n          return;\n        }\n        try {\n          _this3.uploading = true;\n          // Upload images first\n          const imageUrls = yield _this3.uploadImages();\n          const productData = {\n            ..._this3.productForm.value,\n            images: imageUrls.map(url => ({\n              url\n            })),\n            sizes: _this3.selectedSizes,\n            colors: _this3.selectedColors\n          };\n          // TODO: Implement actual product creation API\n          console.log('Creating product:', productData);\n          // Simulate API call\n          yield new Promise(resolve => setTimeout(resolve, 1000));\n          alert('Product created successfully!');\n          _this3.router.navigate(['/vendor/products']);\n        } catch (error) {\n          console.error('Error creating product:', error);\n          alert('Failed to create product. Please try again.');\n        } finally {\n          _this3.uploading = false;\n        }\n      })();\n    }\n    static {\n      this.ɵfac = function CreateProductComponent_Factory(t) {\n        return new (t || CreateProductComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.UploadService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CreateProductComponent,\n        selectors: [[\"app-create-product\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 94,\n        vars: 7,\n        consts: [[\"fileInput\", \"\"], [1, \"create-product-container\"], [1, \"header\"], [1, \"product-form\", 3, \"ngSubmit\", \"formGroup\"], [1, \"form-section\"], [1, \"form-group\"], [\"for\", \"name\"], [\"type\", \"text\", \"id\", \"name\", \"formControlName\", \"name\", \"placeholder\", \"Enter product name\"], [1, \"form-row\"], [\"for\", \"brand\"], [\"type\", \"text\", \"id\", \"brand\", \"formControlName\", \"brand\", \"placeholder\", \"Brand name\"], [\"for\", \"category\"], [\"id\", \"category\", \"formControlName\", \"category\"], [\"value\", \"\"], [\"value\", \"clothing\"], [\"value\", \"shoes\"], [\"value\", \"accessories\"], [\"value\", \"bags\"], [\"for\", \"description\"], [\"id\", \"description\", \"formControlName\", \"description\", \"rows\", \"4\", \"placeholder\", \"Describe your product...\"], [\"for\", \"price\"], [\"type\", \"number\", \"id\", \"price\", \"formControlName\", \"price\", \"placeholder\", \"0\"], [\"for\", \"originalPrice\"], [\"type\", \"number\", \"id\", \"originalPrice\", \"formControlName\", \"originalPrice\", \"placeholder\", \"0\"], [1, \"image-upload\"], [1, \"upload-area\", 3, \"click\"], [\"type\", \"file\", \"multiple\", \"\", \"accept\", \"image/*\", 2, \"display\", \"none\", 3, \"change\"], [1, \"fas\", \"fa-cloud-upload-alt\"], [\"class\", \"image-preview\", 4, \"ngIf\"], [1, \"checkbox-group\"], [4, \"ngFor\", \"ngForOf\"], [1, \"color-group\"], [\"class\", \"color-option\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"stock\"], [\"type\", \"number\", \"id\", \"stock\", \"formControlName\", \"stock\", \"placeholder\", \"0\"], [\"for\", \"sku\"], [\"type\", \"text\", \"id\", \"sku\", \"formControlName\", \"sku\", \"placeholder\", \"Product SKU\"], [1, \"form-actions\"], [\"type\", \"button\", 1, \"btn-secondary\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn-primary\", 3, \"disabled\"], [4, \"ngIf\"], [1, \"image-preview\"], [\"class\", \"image-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"image-item\"], [3, \"src\", \"alt\"], [\"type\", \"button\", 1, \"remove-image\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [\"type\", \"checkbox\", 3, \"change\", \"value\"], [1, \"color-option\"], [1, \"color-swatch\"]],\n        template: function CreateProductComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"h1\");\n            i0.ɵɵtext(3, \"Create New Product\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p\");\n            i0.ɵɵtext(5, \"Add a new product to your catalog\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"form\", 3);\n            i0.ɵɵlistener(\"ngSubmit\", function CreateProductComponent_Template_form_ngSubmit_6_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSubmit());\n            });\n            i0.ɵɵelementStart(7, \"div\", 4)(8, \"h3\");\n            i0.ɵɵtext(9, \"Basic Information\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"div\", 5)(11, \"label\", 6);\n            i0.ɵɵtext(12, \"Product Name *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(13, \"input\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"div\", 8)(15, \"div\", 5)(16, \"label\", 9);\n            i0.ɵɵtext(17, \"Brand\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(18, \"input\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"div\", 5)(20, \"label\", 11);\n            i0.ɵɵtext(21, \"Category *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"select\", 12)(23, \"option\", 13);\n            i0.ɵɵtext(24, \"Select category\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"option\", 14);\n            i0.ɵɵtext(26, \"Clothing\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(27, \"option\", 15);\n            i0.ɵɵtext(28, \"Shoes\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"option\", 16);\n            i0.ɵɵtext(30, \"Accessories\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"option\", 17);\n            i0.ɵɵtext(32, \"Bags\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(33, \"div\", 5)(34, \"label\", 18);\n            i0.ɵɵtext(35, \"Description *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(36, \"textarea\", 19);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(37, \"div\", 4)(38, \"h3\");\n            i0.ɵɵtext(39, \"Pricing\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(40, \"div\", 8)(41, \"div\", 5)(42, \"label\", 20);\n            i0.ɵɵtext(43, \"Selling Price *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(44, \"input\", 21);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(45, \"div\", 5)(46, \"label\", 22);\n            i0.ɵɵtext(47, \"Original Price\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(48, \"input\", 23);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(49, \"div\", 4)(50, \"h3\");\n            i0.ɵɵtext(51, \"Product Images\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(52, \"div\", 24)(53, \"div\", 25);\n            i0.ɵɵlistener(\"click\", function CreateProductComponent_Template_div_click_53_listener() {\n              i0.ɵɵrestoreView(_r1);\n              const fileInput_r2 = i0.ɵɵreference(55);\n              return i0.ɵɵresetView(fileInput_r2.click());\n            });\n            i0.ɵɵelementStart(54, \"input\", 26, 0);\n            i0.ɵɵlistener(\"change\", function CreateProductComponent_Template_input_change_54_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onFileSelect($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(56, \"i\", 27);\n            i0.ɵɵelementStart(57, \"p\");\n            i0.ɵɵtext(58, \"Click to upload images\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(59, \"span\");\n            i0.ɵɵtext(60, \"Support: JPG, PNG (Max 5 images)\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(61, CreateProductComponent_div_61_Template, 2, 1, \"div\", 28);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(62, \"div\", 4)(63, \"h3\");\n            i0.ɵɵtext(64, \"Product Variants\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(65, \"div\", 8)(66, \"div\", 5)(67, \"label\");\n            i0.ɵɵtext(68, \"Available Sizes\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(69, \"div\", 29);\n            i0.ɵɵtemplate(70, CreateProductComponent_label_70_Template, 3, 2, \"label\", 30);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(71, \"div\", 5)(72, \"label\");\n            i0.ɵɵtext(73, \"Available Colors\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(74, \"div\", 31);\n            i0.ɵɵtemplate(75, CreateProductComponent_label_75_Template, 4, 4, \"label\", 32);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(76, \"div\", 4)(77, \"h3\");\n            i0.ɵɵtext(78, \"Inventory\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(79, \"div\", 8)(80, \"div\", 5)(81, \"label\", 33);\n            i0.ɵɵtext(82, \"Stock Quantity *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(83, \"input\", 34);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(84, \"div\", 5)(85, \"label\", 35);\n            i0.ɵɵtext(86, \"SKU\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(87, \"input\", 36);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(88, \"div\", 37)(89, \"button\", 38);\n            i0.ɵɵlistener(\"click\", function CreateProductComponent_Template_button_click_89_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.saveDraft());\n            });\n            i0.ɵɵtext(90, \"Save as Draft\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(91, \"button\", 39);\n            i0.ɵɵtemplate(92, CreateProductComponent_span_92_Template, 2, 0, \"span\", 40)(93, CreateProductComponent_span_93_Template, 2, 0, \"span\", 40);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"formGroup\", ctx.productForm);\n            i0.ɵɵadvance(55);\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedImages.length > 0);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngForOf\", ctx.availableSizes);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngForOf\", ctx.availableColors);\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"disabled\", !ctx.productForm.valid || ctx.uploading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.uploading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.uploading);\n          }\n        },\n        dependencies: [CommonModule, i4.NgForOf, i4.NgIf, FormsModule, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, ReactiveFormsModule, i1.FormGroupDirective, i1.FormControlName],\n        styles: [\".create-product-container[_ngcontent-%COMP%]{max-width:800px;margin:0 auto;padding:20px}.header[_ngcontent-%COMP%]{margin-bottom:30px}.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem;font-weight:600;margin-bottom:8px}.header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666}.product-form[_ngcontent-%COMP%]{background:#fff;border-radius:8px;padding:30px;border:1px solid #eee}.form-section[_ngcontent-%COMP%]{margin-bottom:30px}.form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:600;margin-bottom:20px;color:#333}.form-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:20px}.form-group[_ngcontent-%COMP%]{margin-bottom:20px}.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;margin-bottom:8px;font-weight:500;color:#333}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%], .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{width:100%;padding:12px;border:1px solid #ddd;border-radius:6px;font-size:1rem}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:focus, .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus{outline:none;border-color:#007bff;box-shadow:0 0 0 3px #007bff1a}.upload-area[_ngcontent-%COMP%]{border:2px dashed #ddd;border-radius:8px;padding:40px;text-align:center;cursor:pointer;transition:all .2s;margin-bottom:20px}.upload-area[_ngcontent-%COMP%]:hover{border-color:#007bff;background:#f8f9ff}.upload-area[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:3rem;color:#ddd;margin-bottom:15px}.upload-area[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.1rem;margin-bottom:5px}.upload-area[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#666;font-size:.9rem}.image-preview[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(120px,1fr));gap:15px}.image-item[_ngcontent-%COMP%]{position:relative;border-radius:8px;overflow:hidden}.image-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:120px;object-fit:cover}.remove-image[_ngcontent-%COMP%]{position:absolute;top:8px;right:8px;background:#000000b3;color:#fff;border:none;border-radius:50%;width:24px;height:24px;cursor:pointer}.checkbox-group[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:15px}.checkbox-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;cursor:pointer}.color-group[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:15px}.color-option[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;cursor:pointer}.color-swatch[_ngcontent-%COMP%]{width:20px;height:20px;border-radius:50%;border:2px solid #ddd}.form-actions[_ngcontent-%COMP%]{display:flex;gap:15px;justify-content:flex-end;margin-top:30px;padding-top:20px;border-top:1px solid #eee}.btn-primary[_ngcontent-%COMP%], .btn-secondary[_ngcontent-%COMP%]{padding:12px 24px;border-radius:6px;font-weight:500;cursor:pointer;border:none;transition:all .2s}.btn-primary[_ngcontent-%COMP%]{background:#007bff;color:#fff}.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled){background:#0056b3}.btn-primary[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.btn-secondary[_ngcontent-%COMP%]{background:#f8f9fa;color:#6c757d;border:1px solid #dee2e6}.btn-secondary[_ngcontent-%COMP%]:hover{background:#e9ecef}@media (max-width: 768px){.form-row[_ngcontent-%COMP%]{grid-template-columns:1fr}.form-actions[_ngcontent-%COMP%]{flex-direction:column}}\"]\n      });\n    }\n  }\n  return CreateProductComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}