{"ast": null, "code": "import { Observable } from '../../Observable';\nimport { performanceTimestampProvider } from '../../scheduler/performanceTimestampProvider';\nimport { animationFrameProvider } from '../../scheduler/animationFrameProvider';\nexport function animationFrames(timestampProvider) {\n  return timestampProvider ? animationFramesFactory(timestampProvider) : DEFAULT_ANIMATION_FRAMES;\n}\nfunction animationFramesFactory(timestampProvider) {\n  return new Observable(subscriber => {\n    const provider = timestampProvider || performanceTimestampProvider;\n    const start = provider.now();\n    let id = 0;\n    const run = () => {\n      if (!subscriber.closed) {\n        id = animationFrameProvider.requestAnimationFrame(timestamp => {\n          id = 0;\n          const now = provider.now();\n          subscriber.next({\n            timestamp: timestampProvider ? now : timestamp,\n            elapsed: now - start\n          });\n          run();\n        });\n      }\n    };\n    run();\n    return () => {\n      if (id) {\n        animationFrameProvider.cancelAnimationFrame(id);\n      }\n    };\n  });\n}\nconst DEFAULT_ANIMATION_FRAMES = animationFramesFactory();\n//# sourceMappingURL=animationFrames.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}