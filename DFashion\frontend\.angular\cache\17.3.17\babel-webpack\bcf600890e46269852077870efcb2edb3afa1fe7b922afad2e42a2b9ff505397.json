{"ast": null, "code": "import { BehaviorSubject, tap, catchError, throwError } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport let AuthService = /*#__PURE__*/(() => {\n  class AuthService {\n    constructor(http, router) {\n      this.http = http;\n      this.router = router;\n      this.API_URL = environment.apiUrl;\n      this.currentUserSubject = new BehaviorSubject(null);\n      this.isAuthenticatedSubject = new BehaviorSubject(false);\n      this.currentUser$ = this.currentUserSubject.asObservable();\n      this.isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n    }\n    initializeAuth() {\n      const token = this.getToken();\n      if (token) {\n        this.getCurrentUser().subscribe({\n          next: response => {\n            this.currentUserSubject.next(response.user);\n            this.isAuthenticatedSubject.next(true);\n          },\n          error: () => {\n            // Clear invalid token without redirecting\n            this.clearAuth();\n          }\n        });\n      }\n    }\n    login(credentials) {\n      return this.http.post(`${this.API_URL}/auth/login`, credentials).pipe(tap(response => {\n        this.setToken(response.token);\n        this.currentUserSubject.next(response.user);\n        this.isAuthenticatedSubject.next(true);\n        // Trigger cart and wishlist refresh after successful login\n        this.refreshUserDataOnLogin();\n      }), catchError(error => {\n        console.error('Login error:', error);\n        return throwError(() => error);\n      }));\n    }\n    register(userData) {\n      return this.http.post(`${this.API_URL}/auth/register`, userData).pipe(tap(response => {\n        this.setToken(response.token);\n        this.currentUserSubject.next(response.user);\n        this.isAuthenticatedSubject.next(true);\n      }));\n    }\n    logout() {\n      this.clearAuth();\n      this.router.navigate(['/auth/login']);\n    }\n    clearAuth() {\n      localStorage.removeItem('token');\n      this.currentUserSubject.next(null);\n      this.isAuthenticatedSubject.next(false);\n      // Clear cart and wishlist data on logout\n      this.clearUserDataOnLogout();\n    }\n    // Method to refresh user data (cart, wishlist) after login\n    refreshUserDataOnLogin() {\n      // Use setTimeout to avoid circular dependency issues\n      setTimeout(() => {\n        try {\n          // Import services dynamically to avoid circular dependency\n          import('./cart.service').then(({\n            CartService\n          }) => {\n            const cartService = new CartService(this.http, null, null);\n            cartService.loadCartCountOnLogin();\n          });\n          import('./wishlist.service').then(({\n            WishlistService\n          }) => {\n            const wishlistService = new WishlistService(this.http);\n            wishlistService.loadWishlistCountOnLogin();\n          });\n        } catch (error) {\n          console.error('Error refreshing user data on login:', error);\n        }\n      }, 100);\n    }\n    // Method to clear user data on logout\n    clearUserDataOnLogout() {\n      setTimeout(() => {\n        try {\n          // Import services dynamically to avoid circular dependency\n          import('./cart.service').then(({\n            CartService\n          }) => {\n            const cartService = new CartService(this.http, null, null);\n            cartService.clearCartData();\n          });\n          import('./wishlist.service').then(({\n            WishlistService\n          }) => {\n            const wishlistService = new WishlistService(this.http);\n            wishlistService.clearWishlistData();\n          });\n        } catch (error) {\n          console.error('Error clearing user data on logout:', error);\n        }\n      }, 100);\n    }\n    getCurrentUser() {\n      return this.http.get(`${this.API_URL}/auth/me`);\n    }\n    getToken() {\n      return localStorage.getItem('token');\n    }\n    setToken(token) {\n      localStorage.setItem('token', token);\n    }\n    get currentUserValue() {\n      return this.currentUserSubject.value;\n    }\n    get isAuthenticated() {\n      return this.isAuthenticatedSubject.value;\n    }\n    isAdmin() {\n      const user = this.currentUserValue;\n      return user?.role === 'admin';\n    }\n    isVendor() {\n      const user = this.currentUserValue;\n      return user?.role === 'vendor';\n    }\n    isCustomer() {\n      const user = this.currentUserValue;\n      return user?.role === 'customer';\n    }\n    // Helper methods for checking authentication before actions\n    requireAuth(action = 'perform this action') {\n      if (!this.isAuthenticated) {\n        this.showLoginPrompt(action);\n        return false;\n      }\n      return true;\n    }\n    requireSuperAdminAuth(action = 'perform this action') {\n      if (!this.isAuthenticated) {\n        this.showLoginPrompt(action);\n        return false;\n      }\n      if (!this.isAdmin()) {\n        this.showRoleError('super admin', action);\n        return false;\n      }\n      return true;\n    }\n    requireCustomerAuth(action = 'perform this action') {\n      if (!this.isAuthenticated) {\n        this.showLoginPrompt(action);\n        return false;\n      }\n      if (!this.isCustomer()) {\n        this.showRoleError('customer', action);\n        return false;\n      }\n      return true;\n    }\n    showLoginPrompt(action) {\n      const message = `Please login to ${action}`;\n      if (confirm(`${message}. Would you like to login now?`)) {\n        this.router.navigate(['/auth/login'], {\n          queryParams: {\n            returnUrl: this.router.url\n          }\n        });\n      }\n    }\n    showRoleError(requiredRole, action) {\n      alert(`Only ${requiredRole}s can ${action}. Please login with a ${requiredRole} account.`);\n    }\n    // Social interaction methods with authentication checks\n    canLike() {\n      return this.requireCustomerAuth('like posts');\n    }\n    canComment() {\n      return this.requireCustomerAuth('comment on posts');\n    }\n    canAddToCart() {\n      return this.requireCustomerAuth('add items to cart');\n    }\n    canAddToWishlist() {\n      return this.requireCustomerAuth('add items to wishlist');\n    }\n    canBuy() {\n      return this.requireCustomerAuth('purchase items');\n    }\n    // Get auth headers for API calls\n    getAuthHeaders() {\n      const token = this.getToken();\n      return token ? {\n        'Authorization': `Bearer ${token}`\n      } : {};\n    }\n    static {\n      this.ɵfac = function AuthService_Factory(t) {\n        return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthService,\n        factory: AuthService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AuthService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}