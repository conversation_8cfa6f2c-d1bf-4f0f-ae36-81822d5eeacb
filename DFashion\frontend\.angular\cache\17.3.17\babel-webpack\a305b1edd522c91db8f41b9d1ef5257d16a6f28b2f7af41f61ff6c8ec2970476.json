{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/product.service\";\nimport * as i2 from \"../../../../core/services/auth.service\";\nimport * as i3 from \"../../../../core/services/cart.service\";\nimport * as i4 from \"../../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nfunction ShopComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelement(1, \"div\", 4);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading shop data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ShopComponent_div_2_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_10_Template_div_click_0_listener() {\n      const category_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.navigateToCategory(category_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 24);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 25);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r4.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r4.name);\n  }\n}\nfunction ShopComponent_div_2_div_15_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 30);\n    i0.ɵɵtext(1, \"Popular\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ShopComponent_div_2_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"img\", 27);\n    i0.ɵɵelementStart(2, \"h3\", 28);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ShopComponent_div_2_div_15_span_4_Template, 2, 0, \"span\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const brand_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", brand_r5.logo, i0.ɵɵsanitizeUrl)(\"alt\", brand_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(brand_r5.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", brand_r5.isPopular);\n  }\n}\nfunction ShopComponent_div_2_div_19_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDiscountPercentage(product_r7), \"% OFF \");\n  }\n}\nfunction ShopComponent_div_2_div_19_div_1_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(product_r7.likesCount);\n  }\n}\nfunction ShopComponent_div_2_div_19_div_1_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(product_r7.sharesCount);\n  }\n}\nfunction ShopComponent_div_2_div_19_div_1_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(product_r7.commentsCount);\n  }\n}\nfunction ShopComponent_div_2_div_19_div_1_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 54);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r7.originalPrice, \"\");\n  }\n}\nfunction ShopComponent_div_2_div_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_19_div_1_Template_div_click_0_listener() {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(product_r7));\n    });\n    i0.ɵɵelementStart(1, \"div\", 32);\n    i0.ɵɵelement(2, \"img\", 33);\n    i0.ɵɵtemplate(3, ShopComponent_div_2_div_19_div_1_div_3_Template, 2, 1, \"div\", 34);\n    i0.ɵɵelementStart(4, \"div\", 35)(5, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_19_div_1_Template_button_click_5_listener($event) {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.likeProduct(product_r7, $event));\n    });\n    i0.ɵɵelement(6, \"i\", 37);\n    i0.ɵɵtemplate(7, ShopComponent_div_2_div_19_div_1_span_7_Template, 2, 1, \"span\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_19_div_1_Template_button_click_8_listener($event) {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.shareProduct(product_r7, $event));\n    });\n    i0.ɵɵelement(9, \"i\", 40);\n    i0.ɵɵtemplate(10, ShopComponent_div_2_div_19_div_1_span_10_Template, 2, 1, \"span\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_19_div_1_Template_button_click_11_listener($event) {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.commentOnProduct(product_r7, $event));\n    });\n    i0.ɵɵelement(12, \"i\", 42);\n    i0.ɵɵtemplate(13, ShopComponent_div_2_div_19_div_1_span_13_Template, 2, 1, \"span\", 38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 43)(15, \"h3\", 44);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\", 45);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 46)(20, \"span\", 47);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, ShopComponent_div_2_div_19_div_1_span_22_Template, 2, 1, \"span\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 49)(24, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_19_div_1_Template_button_click_24_listener($event) {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.addToWishlist(product_r7, $event));\n    });\n    i0.ɵɵelement(25, \"i\", 37);\n    i0.ɵɵtext(26, \" Wishlist \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_19_div_1_Template_button_click_27_listener($event) {\n      const product_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.addToCart(product_r7, $event));\n    });\n    i0.ɵɵelement(28, \"i\", 52);\n    i0.ɵɵtext(29, \" Add to Cart \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.getProductImage(product_r7), i0.ɵɵsanitizeUrl)(\"alt\", product_r7.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDiscountPercentage(product_r7) > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", product_r7.isLiked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r7.likesCount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", product_r7.sharesCount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", product_r7.commentsCount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r7.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r7.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r7.price, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r7.originalPrice && product_r7.originalPrice > product_r7.price);\n  }\n}\nfunction ShopComponent_div_2_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtemplate(1, ShopComponent_div_2_div_19_div_1_Template, 30, 12, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.trendingProducts);\n  }\n}\nfunction ShopComponent_div_2_div_24_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(product_r9.likesCount);\n  }\n}\nfunction ShopComponent_div_2_div_24_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(product_r9.sharesCount);\n  }\n}\nfunction ShopComponent_div_2_div_24_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(product_r9.commentsCount);\n  }\n}\nfunction ShopComponent_div_2_div_24_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 54);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r9.originalPrice, \"\");\n  }\n}\nfunction ShopComponent_div_2_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_24_Template_div_click_0_listener() {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(product_r9));\n    });\n    i0.ɵɵelementStart(1, \"div\", 32);\n    i0.ɵɵelement(2, \"img\", 33);\n    i0.ɵɵelementStart(3, \"div\", 55);\n    i0.ɵɵtext(4, \"NEW\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 35)(6, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_24_Template_button_click_6_listener($event) {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.likeProduct(product_r9, $event));\n    });\n    i0.ɵɵelement(7, \"i\", 37);\n    i0.ɵɵtemplate(8, ShopComponent_div_2_div_24_span_8_Template, 2, 1, \"span\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_24_Template_button_click_9_listener($event) {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.shareProduct(product_r9, $event));\n    });\n    i0.ɵɵelement(10, \"i\", 40);\n    i0.ɵɵtemplate(11, ShopComponent_div_2_div_24_span_11_Template, 2, 1, \"span\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_24_Template_button_click_12_listener($event) {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.commentOnProduct(product_r9, $event));\n    });\n    i0.ɵɵelement(13, \"i\", 42);\n    i0.ɵɵtemplate(14, ShopComponent_div_2_div_24_span_14_Template, 2, 1, \"span\", 38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 43)(16, \"h3\", 44);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\", 45);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 46)(21, \"span\", 47);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, ShopComponent_div_2_div_24_span_23_Template, 2, 1, \"span\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 49)(25, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_24_Template_button_click_25_listener($event) {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToWishlist(product_r9, $event));\n    });\n    i0.ɵɵelement(26, \"i\", 37);\n    i0.ɵɵtext(27, \" Wishlist \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_div_24_Template_button_click_28_listener($event) {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToCart(product_r9, $event));\n    });\n    i0.ɵɵelement(29, \"i\", 52);\n    i0.ɵɵtext(30, \" Add to Cart \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r9 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.getProductImage(product_r9), i0.ɵɵsanitizeUrl)(\"alt\", product_r9.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"liked\", product_r9.isLiked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", product_r9.likesCount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", product_r9.sharesCount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", product_r9.commentsCount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r9.brand);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", product_r9.price, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r9.originalPrice && product_r9.originalPrice > product_r9.price);\n  }\n}\nfunction ShopComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"div\", 7)(3, \"input\", 8);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ShopComponent_div_2_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchQuery, $event) || (ctx_r1.searchQuery = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function ShopComponent_div_2_Template_input_keyup_enter_3_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.search());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function ShopComponent_div_2_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.search());\n    });\n    i0.ɵɵelement(5, \"i\", 10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"section\", 11)(7, \"h2\", 12);\n    i0.ɵɵtext(8, \"Shop by Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 13);\n    i0.ɵɵtemplate(10, ShopComponent_div_2_div_10_Template, 5, 2, \"div\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"section\", 15)(12, \"h2\", 12);\n    i0.ɵɵtext(13, \"Featured Brands\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 16);\n    i0.ɵɵtemplate(15, ShopComponent_div_2_div_15_Template, 5, 4, \"div\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"section\", 18)(17, \"h2\", 12);\n    i0.ɵɵtext(18, \"Trending Now\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, ShopComponent_div_2_div_19_Template, 2, 1, \"div\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"section\", 20)(21, \"h2\", 12);\n    i0.ɵɵtext(22, \"New Arrivals\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 21);\n    i0.ɵɵtemplate(24, ShopComponent_div_2_div_24_Template, 31, 11, \"div\", 22);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchQuery);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categories);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.featuredBrands);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.trendingProducts.length > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.newArrivals);\n  }\n}\nexport class ShopComponent {\n  constructor(productService, authService, cartService, wishlistService, router) {\n    this.productService = productService;\n    this.authService = authService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.router = router;\n    this.featuredBrands = [];\n    this.trendingProducts = [];\n    this.newArrivals = [];\n    this.categories = [];\n    this.searchQuery = '';\n    this.loading = true;\n  }\n  ngOnInit() {\n    this.loadShopData();\n  }\n  loadShopData() {\n    this.loading = true;\n    Promise.all([this.loadFeaturedBrands(), this.loadTrendingProducts(), this.loadNewArrivals(), this.loadCategories()]).finally(() => {\n      this.loading = false;\n    });\n  }\n  loadFeaturedBrands() {\n    return this.productService.getFeaturedBrands().toPromise().then(response => {\n      this.featuredBrands = response?.data || [];\n    }).catch(error => {\n      console.error('Error loading featured brands:', error);\n      this.featuredBrands = [];\n    });\n  }\n  loadTrendingProducts() {\n    return this.productService.getTrendingProducts().toPromise().then(response => {\n      this.trendingProducts = response?.data || [];\n    }).catch(error => {\n      console.error('Error loading trending products:', error);\n      this.trendingProducts = [];\n    });\n  }\n  loadNewArrivals() {\n    return this.productService.getNewArrivals().toPromise().then(response => {\n      this.newArrivals = response?.data || [];\n    }).catch(error => {\n      console.error('Error loading new arrivals:', error);\n      this.newArrivals = [];\n    });\n  }\n  loadCategories() {\n    return this.productService.getCategories().toPromise().then(response => {\n      this.categories = response?.data || [];\n    }).catch(error => {\n      console.error('Error loading categories:', error);\n      this.categories = [];\n    });\n  }\n  // Product interaction methods\n  likeProduct(product, event) {\n    event.stopPropagation();\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/login']);\n      return;\n    }\n    product.isLiked = !product.isLiked;\n    product.likesCount = product.isLiked ? (product.likesCount || 0) + 1 : (product.likesCount || 1) - 1;\n    this.productService.toggleProductLike(product._id).subscribe({\n      next: response => {\n        console.log('Product like updated:', response);\n      },\n      error: error => {\n        console.error('Error updating product like:', error);\n        product.isLiked = !product.isLiked;\n        product.likesCount = product.isLiked ? (product.likesCount || 0) + 1 : (product.likesCount || 1) - 1;\n      }\n    });\n  }\n  shareProduct(product, event) {\n    event.stopPropagation();\n    const shareData = {\n      title: product.name,\n      text: `Check out this amazing product: ${product.name}`,\n      url: `${window.location.origin}/product/${product._id}`\n    };\n    if (navigator.share) {\n      navigator.share(shareData);\n    } else {\n      navigator.clipboard.writeText(shareData.url).then(() => {\n        alert('Product link copied to clipboard!');\n      });\n    }\n    this.productService.shareProduct(product._id).subscribe({\n      next: response => {\n        product.sharesCount = (product.sharesCount || 0) + 1;\n      },\n      error: error => {\n        console.error('Error tracking product share:', error);\n      }\n    });\n  }\n  commentOnProduct(product, event) {\n    event.stopPropagation();\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/login']);\n      return;\n    }\n    this.router.navigate(['/product', product._id], {\n      queryParams: {\n        action: 'comment'\n      }\n    });\n  }\n  addToWishlist(product, event) {\n    event.stopPropagation();\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/login']);\n      return;\n    }\n    this.wishlistService.addToWishlist(product._id).subscribe({\n      next: response => {\n        product.isInWishlist = true;\n        console.log('Product added to wishlist:', response);\n      },\n      error: error => {\n        console.error('Error adding to wishlist:', error);\n      }\n    });\n  }\n  addToCart(product, event) {\n    event.stopPropagation();\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/login']);\n      return;\n    }\n    this.cartService.addToCart(product._id, 1).subscribe({\n      next: response => {\n        console.log('Product added to cart:', response);\n        alert('Product added to cart successfully!');\n      },\n      error: error => {\n        console.error('Error adding to cart:', error);\n      }\n    });\n  }\n  // Navigation methods\n  viewProduct(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  navigateToCategory(category) {\n    this.router.navigate(['/category', category.slug]);\n  }\n  search() {\n    if (this.searchQuery.trim()) {\n      this.router.navigate(['/search'], {\n        queryParams: {\n          q: this.searchQuery\n        }\n      });\n    }\n  }\n  getProductImage(product) {\n    return product.images?.[0]?.url || '/assets/images/placeholder.jpg';\n  }\n  getDiscountPercentage(product) {\n    if (!product.originalPrice || product.originalPrice <= product.price) return 0;\n    return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n  }\n  static {\n    this.ɵfac = function ShopComponent_Factory(t) {\n      return new (t || ShopComponent)(i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService), i0.ɵɵdirectiveInject(i5.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ShopComponent,\n      selectors: [[\"app-shop\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[1, \"shop-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"shop-content\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-spinner\"], [1, \"shop-content\"], [1, \"search-section\"], [1, \"search-bar\"], [\"type\", \"text\", \"placeholder\", \"Search products, brands, categories...\", 1, \"search-input\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"search-btn\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"category-section\"], [1, \"section-title\"], [1, \"categories-grid\"], [\"class\", \"category-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"featured-brands-section\"], [1, \"brands-slider\"], [\"class\", \"brand-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"trending-section\"], [\"class\", \"products-grid\", 4, \"ngIf\"], [1, \"new-arrivals-section\"], [1, \"products-grid\"], [\"class\", \"product-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"category-card\", 3, \"click\"], [1, \"category-icon\"], [1, \"category-name\"], [1, \"brand-card\"], [1, \"brand-logo\", 3, \"src\", \"alt\"], [1, \"brand-name\"], [\"class\", \"popular-badge\", 4, \"ngIf\"], [1, \"popular-badge\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image-container\"], [1, \"product-image\", 3, \"src\", \"alt\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"product-actions\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [1, \"fas\", \"fa-heart\"], [4, \"ngIf\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [1, \"fas\", \"fa-share\"], [1, \"action-btn\", \"comment-btn\", 3, \"click\"], [1, \"fas\", \"fa-comment\"], [1, \"product-info\"], [1, \"product-name\"], [1, \"product-brand\"], [1, \"product-pricing\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"product-buttons\"], [1, \"btn-wishlist\", 3, \"click\"], [1, \"btn-cart\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"discount-badge\"], [1, \"original-price\"], [1, \"new-badge\"]],\n      template: function ShopComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, ShopComponent_div_1_Template, 4, 0, \"div\", 1)(2, ShopComponent_div_2_Template, 25, 5, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        }\n      },\n      dependencies: [CommonModule, i6.NgForOf, i6.NgIf, FormsModule, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel],\n      styles: [\".shop-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 20px;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  min-height: 400px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: 4px solid #f3f3f3;\\n  border-top: 4px solid #007bff;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  margin-bottom: 20px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.search-section[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n}\\n.search-section[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%] {\\n  display: flex;\\n  max-width: 600px;\\n  margin: 0 auto;\\n  border: 2px solid #e0e0e0;\\n  border-radius: 25px;\\n  overflow: hidden;\\n}\\n.search-section[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 12px 20px;\\n  border: none;\\n  outline: none;\\n  font-size: 16px;\\n}\\n.search-section[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-btn[_ngcontent-%COMP%] {\\n  padding: 12px 20px;\\n  background: #007bff;\\n  color: white;\\n  border: none;\\n  cursor: pointer;\\n}\\n.search-section[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-btn[_ngcontent-%COMP%]:hover {\\n  background: #0056b3;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: 600;\\n  margin-bottom: 30px;\\n  text-align: center;\\n  color: #333;\\n}\\n\\n.category-section[_ngcontent-%COMP%] {\\n  margin-bottom: 60px;\\n}\\n.category-section[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\\n  gap: 20px;\\n}\\n.category-section[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 15px;\\n  padding: 30px 20px;\\n  text-align: center;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.category-section[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n.category-section[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  margin-bottom: 15px;\\n}\\n.category-section[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #333;\\n  margin: 0;\\n}\\n\\n.featured-brands-section[_ngcontent-%COMP%] {\\n  margin-bottom: 60px;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brands-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  overflow-x: auto;\\n  padding: 20px 0;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%] {\\n  min-width: 200px;\\n  background: white;\\n  border-radius: 15px;\\n  padding: 20px;\\n  text-align: center;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\\n  position: relative;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-logo[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  margin-bottom: 15px;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .brand-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #333;\\n  margin: 0;\\n}\\n.featured-brands-section[_ngcontent-%COMP%]   .brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]   .popular-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 10px;\\n  right: 10px;\\n  background: #ff6b6b;\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 600;\\n}\\n\\n.products-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\\n  gap: 30px;\\n}\\n\\n.product-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 15px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.product-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 250px;\\n  object-fit: cover;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%], .product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 10px;\\n  left: 10px;\\n  padding: 6px 12px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: white;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%] {\\n  background: #ff6b6b;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%] {\\n  background: #4ecdc4;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 10px;\\n  right: 10px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #666;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 14px;\\n  transition: all 0.3s ease;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  color: #333;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.liked[_ngcontent-%COMP%] {\\n  color: #ff6b6b;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  margin-left: 2px;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #333;\\n  margin: 0 0 8px 0;\\n  line-height: 1.3;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin: 0 0 12px 0;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-pricing[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #999;\\n  text-decoration: line-through;\\n  margin-left: 8px;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 10px;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 5px;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-buttons[_ngcontent-%COMP%]   .btn-wishlist[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  color: #666;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-buttons[_ngcontent-%COMP%]   .btn-wishlist[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  color: #333;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-buttons[_ngcontent-%COMP%]   .btn-cart[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  color: white;\\n}\\n.product-card[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-buttons[_ngcontent-%COMP%]   .btn-cart[_ngcontent-%COMP%]:hover {\\n  background: #0056b3;\\n}\\n\\n@media (max-width: 768px) {\\n  .shop-container[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 24px;\\n  }\\n  .categories-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\\n    gap: 15px;\\n  }\\n  .products-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\\n    gap: 20px;\\n  }\\n  .brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%] {\\n    min-width: 150px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ShopComponent_div_2_div_10_Template_div_click_0_listener", "category_r4", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "navigateToCategory", "ɵɵadvance", "ɵɵtextInterpolate", "icon", "name", "ɵɵtemplate", "ShopComponent_div_2_div_15_span_4_Template", "ɵɵproperty", "brand_r5", "logo", "ɵɵsanitizeUrl", "isPopular", "ɵɵtextInterpolate1", "getDiscountPercentage", "product_r7", "likesCount", "sharesCount", "commentsCount", "originalPrice", "ShopComponent_div_2_div_19_div_1_Template_div_click_0_listener", "_r6", "viewProduct", "ShopComponent_div_2_div_19_div_1_div_3_Template", "ShopComponent_div_2_div_19_div_1_Template_button_click_5_listener", "$event", "likeProduct", "ShopComponent_div_2_div_19_div_1_span_7_Template", "ShopComponent_div_2_div_19_div_1_Template_button_click_8_listener", "shareProduct", "ShopComponent_div_2_div_19_div_1_span_10_Template", "ShopComponent_div_2_div_19_div_1_Template_button_click_11_listener", "commentOnProduct", "ShopComponent_div_2_div_19_div_1_span_13_Template", "ShopComponent_div_2_div_19_div_1_span_22_Template", "ShopComponent_div_2_div_19_div_1_Template_button_click_24_listener", "addToWishlist", "ShopComponent_div_2_div_19_div_1_Template_button_click_27_listener", "addToCart", "getProductImage", "ɵɵclassProp", "isLiked", "brand", "price", "ShopComponent_div_2_div_19_div_1_Template", "trendingProducts", "product_r9", "ShopComponent_div_2_div_24_Template_div_click_0_listener", "_r8", "ShopComponent_div_2_div_24_Template_button_click_6_listener", "ShopComponent_div_2_div_24_span_8_Template", "ShopComponent_div_2_div_24_Template_button_click_9_listener", "ShopComponent_div_2_div_24_span_11_Template", "ShopComponent_div_2_div_24_Template_button_click_12_listener", "ShopComponent_div_2_div_24_span_14_Template", "ShopComponent_div_2_div_24_span_23_Template", "ShopComponent_div_2_div_24_Template_button_click_25_listener", "ShopComponent_div_2_div_24_Template_button_click_28_listener", "ɵɵtwoWayListener", "ShopComponent_div_2_Template_input_ngModelChange_3_listener", "_r1", "ɵɵtwoWayBindingSet", "searchQuery", "ShopComponent_div_2_Template_input_keyup_enter_3_listener", "search", "ShopComponent_div_2_Template_button_click_4_listener", "ShopComponent_div_2_div_10_Template", "ShopComponent_div_2_div_15_Template", "ShopComponent_div_2_div_19_Template", "ShopComponent_div_2_div_24_Template", "ɵɵtwoWayProperty", "categories", "featuredB<PERSON>s", "length", "newArrivals", "ShopComponent", "constructor", "productService", "authService", "cartService", "wishlistService", "router", "loading", "ngOnInit", "loadShopData", "Promise", "all", "loadFeaturedBrands", "loadTrendingProducts", "loadNewArrivals", "loadCategories", "finally", "getFeaturedBrands", "to<PERSON>romise", "then", "response", "data", "catch", "error", "console", "getTrendingProducts", "getNewArrivals", "getCategories", "product", "event", "stopPropagation", "isAuthenticated", "navigate", "toggleProductLike", "_id", "subscribe", "next", "log", "shareData", "title", "text", "url", "window", "location", "origin", "navigator", "share", "clipboard", "writeText", "alert", "queryParams", "action", "isInWishlist", "category", "slug", "trim", "q", "images", "Math", "round", "ɵɵdirectiveInject", "i1", "ProductService", "i2", "AuthService", "i3", "CartService", "i4", "WishlistService", "i5", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ShopComponent_Template", "rf", "ctx", "ShopComponent_div_1_Template", "ShopComponent_div_2_Template", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i7", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\shop\\pages\\shop\\shop.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\shop\\pages\\shop\\shop.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport { ProductService } from '../../../../core/services/product.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { CartService } from '../../../../core/services/cart.service';\nimport { WishlistService } from '../../../../core/services/wishlist.service';\n\n@Component({\n  selector: 'app-shop',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  templateUrl: './shop.component.html',\n  styleUrls: ['./shop.component.scss']\n})\nexport class ShopComponent implements OnInit {\n  featuredBrands: any[] = [];\n  trendingProducts: any[] = [];\n  newArrivals: any[] = [];\n  categories: any[] = [];\n  searchQuery: string = '';\n  loading = true;\n\n  constructor(\n    private productService: ProductService,\n    private authService: AuthService,\n    private cartService: CartService,\n    private wishlistService: WishlistService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadShopData();\n  }\n\n  loadShopData() {\n    this.loading = true;\n    Promise.all([\n      this.loadFeaturedBrands(),\n      this.loadTrendingProducts(),\n      this.loadNewArrivals(),\n      this.loadCategories()\n    ]).finally(() => {\n      this.loading = false;\n    });\n  }\n\n  loadFeaturedBrands() {\n    return this.productService.getFeaturedBrands().toPromise().then(\n      (response) => {\n        this.featuredBrands = response?.data || [];\n      }\n    ).catch(error => {\n      console.error('Error loading featured brands:', error);\n      this.featuredBrands = [];\n    });\n  }\n\n  loadTrendingProducts() {\n    return this.productService.getTrendingProducts().toPromise().then(\n      (response) => {\n        this.trendingProducts = response?.data || [];\n      }\n    ).catch(error => {\n      console.error('Error loading trending products:', error);\n      this.trendingProducts = [];\n    });\n  }\n\n  loadNewArrivals() {\n    return this.productService.getNewArrivals().toPromise().then(\n      (response) => {\n        this.newArrivals = response?.data || [];\n      }\n    ).catch(error => {\n      console.error('Error loading new arrivals:', error);\n      this.newArrivals = [];\n    });\n  }\n\n  loadCategories() {\n    return this.productService.getCategories().toPromise().then(\n      (response) => {\n        this.categories = response?.data || [];\n      }\n    ).catch(error => {\n      console.error('Error loading categories:', error);\n      this.categories = [];\n    });\n  }\n\n  // Product interaction methods\n  likeProduct(product: any, event: Event) {\n    event.stopPropagation();\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/login']);\n      return;\n    }\n\n    product.isLiked = !product.isLiked;\n    product.likesCount = product.isLiked ? (product.likesCount || 0) + 1 : (product.likesCount || 1) - 1;\n\n    this.productService.toggleProductLike(product._id).subscribe({\n      next: (response) => {\n        console.log('Product like updated:', response);\n      },\n      error: (error) => {\n        console.error('Error updating product like:', error);\n        product.isLiked = !product.isLiked;\n        product.likesCount = product.isLiked ? (product.likesCount || 0) + 1 : (product.likesCount || 1) - 1;\n      }\n    });\n  }\n\n  shareProduct(product: any, event: Event) {\n    event.stopPropagation();\n    const shareData = {\n      title: product.name,\n      text: `Check out this amazing product: ${product.name}`,\n      url: `${window.location.origin}/product/${product._id}`\n    };\n\n    if (navigator.share) {\n      navigator.share(shareData);\n    } else {\n      navigator.clipboard.writeText(shareData.url).then(() => {\n        alert('Product link copied to clipboard!');\n      });\n    }\n\n    this.productService.shareProduct(product._id).subscribe({\n      next: (response) => {\n        product.sharesCount = (product.sharesCount || 0) + 1;\n      },\n      error: (error) => {\n        console.error('Error tracking product share:', error);\n      }\n    });\n  }\n\n  commentOnProduct(product: any, event: Event) {\n    event.stopPropagation();\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/login']);\n      return;\n    }\n\n    this.router.navigate(['/product', product._id], {\n      queryParams: { action: 'comment' }\n    });\n  }\n\n  addToWishlist(product: any, event: Event) {\n    event.stopPropagation();\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/login']);\n      return;\n    }\n\n    this.wishlistService.addToWishlist(product._id).subscribe({\n      next: (response) => {\n        product.isInWishlist = true;\n        console.log('Product added to wishlist:', response);\n      },\n      error: (error) => {\n        console.error('Error adding to wishlist:', error);\n      }\n    });\n  }\n\n  addToCart(product: any, event: Event) {\n    event.stopPropagation();\n    if (!this.authService.isAuthenticated) {\n      this.router.navigate(['/login']);\n      return;\n    }\n\n    this.cartService.addToCart(product._id, 1).subscribe({\n      next: (response) => {\n        console.log('Product added to cart:', response);\n        alert('Product added to cart successfully!');\n      },\n      error: (error) => {\n        console.error('Error adding to cart:', error);\n      }\n    });\n  }\n\n  // Navigation methods\n  viewProduct(product: any) {\n    this.router.navigate(['/product', product._id]);\n  }\n\n  navigateToCategory(category: any) {\n    this.router.navigate(['/category', category.slug]);\n  }\n\n  search() {\n    if (this.searchQuery.trim()) {\n      this.router.navigate(['/search'], {\n        queryParams: { q: this.searchQuery }\n      });\n    }\n  }\n\n  getProductImage(product: any): string {\n    return product.images?.[0]?.url || '/assets/images/placeholder.jpg';\n  }\n\n  getDiscountPercentage(product: any): number {\n    if (!product.originalPrice || product.originalPrice <= product.price) return 0;\n    return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);\n  }\n}\n", "<div class=\"shop-container\">\n  <!-- Loading State -->\n  <div *ngIf=\"loading\" class=\"loading-container\">\n    <div class=\"loading-spinner\"></div>\n    <p>Loading shop data...</p>\n  </div>\n\n  <!-- Shop Content -->\n  <div *ngIf=\"!loading\" class=\"shop-content\">\n    <!-- Global Search Bar -->\n    <div class=\"search-section\">\n      <div class=\"search-bar\">\n        <input \n          type=\"text\" \n          [(ngModel)]=\"searchQuery\" \n          placeholder=\"Search products, brands, categories...\"\n          (keyup.enter)=\"search()\"\n          class=\"search-input\">\n        <button (click)=\"search()\" class=\"search-btn\">\n          <i class=\"fas fa-search\"></i>\n        </button>\n      </div>\n    </div>\n\n    <!-- Shop by Category Section -->\n    <section class=\"category-section\">\n      <h2 class=\"section-title\">Shop by Category</h2>\n      <div class=\"categories-grid\">\n        <div \n          *ngFor=\"let category of categories\" \n          class=\"category-card\"\n          (click)=\"navigateToCategory(category)\">\n          <div class=\"category-icon\">{{ category.icon }}</div>\n          <h3 class=\"category-name\">{{ category.name }}</h3>\n        </div>\n      </div>\n    </section>\n\n    <!-- Featured Brands Section -->\n    <section class=\"featured-brands-section\">\n      <h2 class=\"section-title\">Featured Brands</h2>\n      <div class=\"brands-slider\">\n        <div *ngFor=\"let brand of featuredBrands\" class=\"brand-card\">\n          <img [src]=\"brand.logo\" [alt]=\"brand.name\" class=\"brand-logo\">\n          <h3 class=\"brand-name\">{{ brand.name }}</h3>\n          <span *ngIf=\"brand.isPopular\" class=\"popular-badge\">Popular</span>\n        </div>\n      </div>\n    </section>\n\n    <!-- Trending Now Section -->\n    <section class=\"trending-section\">\n      <h2 class=\"section-title\">Trending Now</h2>\n      <div *ngIf=\"trendingProducts.length > 0\" class=\"products-grid\">\n        <div *ngFor=\"let product of trendingProducts\" class=\"product-card\" (click)=\"viewProduct(product)\">\n          <div class=\"product-image-container\">\n            <img [src]=\"getProductImage(product)\" [alt]=\"product.name\" class=\"product-image\">\n            <div *ngIf=\"getDiscountPercentage(product) > 0\" class=\"discount-badge\">\n              {{ getDiscountPercentage(product) }}% OFF\n            </div>\n            <div class=\"product-actions\">\n              <button (click)=\"likeProduct(product, $event)\" class=\"action-btn like-btn\" \n                      [class.liked]=\"product.isLiked\">\n                <i class=\"fas fa-heart\"></i>\n                <span *ngIf=\"product.likesCount\">{{ product.likesCount }}</span>\n              </button>\n              <button (click)=\"shareProduct(product, $event)\" class=\"action-btn share-btn\">\n                <i class=\"fas fa-share\"></i>\n                <span *ngIf=\"product.sharesCount\">{{ product.sharesCount }}</span>\n              </button>\n              <button (click)=\"commentOnProduct(product, $event)\" class=\"action-btn comment-btn\">\n                <i class=\"fas fa-comment\"></i>\n                <span *ngIf=\"product.commentsCount\">{{ product.commentsCount }}</span>\n              </button>\n            </div>\n          </div>\n          <div class=\"product-info\">\n            <h3 class=\"product-name\">{{ product.name }}</h3>\n            <p class=\"product-brand\">{{ product.brand }}</p>\n            <div class=\"product-pricing\">\n              <span class=\"current-price\">₹{{ product.price }}</span>\n              <span *ngIf=\"product.originalPrice && product.originalPrice > product.price\" \n                    class=\"original-price\">₹{{ product.originalPrice }}</span>\n            </div>\n            <div class=\"product-buttons\">\n              <button (click)=\"addToWishlist(product, $event)\" class=\"btn-wishlist\">\n                <i class=\"fas fa-heart\"></i> Wishlist\n              </button>\n              <button (click)=\"addToCart(product, $event)\" class=\"btn-cart\">\n                <i class=\"fas fa-shopping-cart\"></i> Add to Cart\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- New Arrivals Section -->\n    <section class=\"new-arrivals-section\">\n      <h2 class=\"section-title\">New Arrivals</h2>\n      <div class=\"products-grid\">\n        <div *ngFor=\"let product of newArrivals\" class=\"product-card\" (click)=\"viewProduct(product)\">\n          <div class=\"product-image-container\">\n            <img [src]=\"getProductImage(product)\" [alt]=\"product.name\" class=\"product-image\">\n            <div class=\"new-badge\">NEW</div>\n            <div class=\"product-actions\">\n              <button (click)=\"likeProduct(product, $event)\" class=\"action-btn like-btn\" \n                      [class.liked]=\"product.isLiked\">\n                <i class=\"fas fa-heart\"></i>\n                <span *ngIf=\"product.likesCount\">{{ product.likesCount }}</span>\n              </button>\n              <button (click)=\"shareProduct(product, $event)\" class=\"action-btn share-btn\">\n                <i class=\"fas fa-share\"></i>\n                <span *ngIf=\"product.sharesCount\">{{ product.sharesCount }}</span>\n              </button>\n              <button (click)=\"commentOnProduct(product, $event)\" class=\"action-btn comment-btn\">\n                <i class=\"fas fa-comment\"></i>\n                <span *ngIf=\"product.commentsCount\">{{ product.commentsCount }}</span>\n              </button>\n            </div>\n          </div>\n          <div class=\"product-info\">\n            <h3 class=\"product-name\">{{ product.name }}</h3>\n            <p class=\"product-brand\">{{ product.brand }}</p>\n            <div class=\"product-pricing\">\n              <span class=\"current-price\">₹{{ product.price }}</span>\n              <span *ngIf=\"product.originalPrice && product.originalPrice > product.price\" \n                    class=\"original-price\">₹{{ product.originalPrice }}</span>\n            </div>\n            <div class=\"product-buttons\">\n              <button (click)=\"addToWishlist(product, $event)\" class=\"btn-wishlist\">\n                <i class=\"fas fa-heart\"></i> Wishlist\n              </button>\n              <button (click)=\"addToCart(product, $event)\" class=\"btn-cart\">\n                <i class=\"fas fa-shopping-cart\"></i> Add to Cart\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  </div>\n</div>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;ICD1CC,EAAA,CAAAC,cAAA,aAA+C;IAC7CD,EAAA,CAAAE,SAAA,aAAmC;IACnCF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,2BAAoB;IACzBH,EADyB,CAAAI,YAAA,EAAI,EACvB;;;;;;IAuBAJ,EAAA,CAAAC,cAAA,cAGyC;IAAvCD,EAAA,CAAAK,UAAA,mBAAAC,yDAAA;MAAA,MAAAC,WAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,kBAAA,CAAAP,WAAA,CAA4B;IAAA,EAAC;IACtCP,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACpDJ,EAAA,CAAAC,cAAA,aAA0B;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAC/CH,EAD+C,CAAAI,YAAA,EAAK,EAC9C;;;;IAFuBJ,EAAA,CAAAe,SAAA,GAAmB;IAAnBf,EAAA,CAAAgB,iBAAA,CAAAT,WAAA,CAAAU,IAAA,CAAmB;IACpBjB,EAAA,CAAAe,SAAA,GAAmB;IAAnBf,EAAA,CAAAgB,iBAAA,CAAAT,WAAA,CAAAW,IAAA,CAAmB;;;;;IAY7ClB,EAAA,CAAAC,cAAA,eAAoD;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAHpEJ,EAAA,CAAAC,cAAA,cAA6D;IAC3DD,EAAA,CAAAE,SAAA,cAA8D;IAC9DF,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAG,MAAA,GAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5CJ,EAAA,CAAAmB,UAAA,IAAAC,0CAAA,mBAAoD;IACtDpB,EAAA,CAAAI,YAAA,EAAM;;;;IAHCJ,EAAA,CAAAe,SAAA,EAAkB;IAACf,EAAnB,CAAAqB,UAAA,QAAAC,QAAA,CAAAC,IAAA,EAAAvB,EAAA,CAAAwB,aAAA,CAAkB,QAAAF,QAAA,CAAAJ,IAAA,CAAmB;IACnBlB,EAAA,CAAAe,SAAA,GAAgB;IAAhBf,EAAA,CAAAgB,iBAAA,CAAAM,QAAA,CAAAJ,IAAA,CAAgB;IAChClB,EAAA,CAAAe,SAAA,EAAqB;IAArBf,EAAA,CAAAqB,UAAA,SAAAC,QAAA,CAAAG,SAAA,CAAqB;;;;;IAY1BzB,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IADJJ,EAAA,CAAAe,SAAA,EACF;IADEf,EAAA,CAAA0B,kBAAA,MAAAf,MAAA,CAAAgB,qBAAA,CAAAC,UAAA,YACF;;;;;IAKI5B,EAAA,CAAAC,cAAA,WAAiC;IAAAD,EAAA,CAAAG,MAAA,GAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAA/BJ,EAAA,CAAAe,SAAA,EAAwB;IAAxBf,EAAA,CAAAgB,iBAAA,CAAAY,UAAA,CAAAC,UAAA,CAAwB;;;;;IAIzD7B,EAAA,CAAAC,cAAA,WAAkC;IAAAD,EAAA,CAAAG,MAAA,GAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAhCJ,EAAA,CAAAe,SAAA,EAAyB;IAAzBf,EAAA,CAAAgB,iBAAA,CAAAY,UAAA,CAAAE,WAAA,CAAyB;;;;;IAI3D9B,EAAA,CAAAC,cAAA,WAAoC;IAAAD,EAAA,CAAAG,MAAA,GAA2B;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAlCJ,EAAA,CAAAe,SAAA,EAA2B;IAA3Bf,EAAA,CAAAgB,iBAAA,CAAAY,UAAA,CAAAG,aAAA,CAA2B;;;;;IASjE/B,EAAA,CAAAC,cAAA,eAC6B;IAAAD,EAAA,CAAAG,MAAA,GAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAnCJ,EAAA,CAAAe,SAAA,EAA4B;IAA5Bf,EAAA,CAAA0B,kBAAA,WAAAE,UAAA,CAAAI,aAAA,KAA4B;;;;;;IA5B/DhC,EAAA,CAAAC,cAAA,cAAkG;IAA/BD,EAAA,CAAAK,UAAA,mBAAA4B,+DAAA;MAAA,MAAAL,UAAA,GAAA5B,EAAA,CAAAQ,aAAA,CAAA0B,GAAA,EAAAxB,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAwB,WAAA,CAAAP,UAAA,CAAoB;IAAA,EAAC;IAC/F5B,EAAA,CAAAC,cAAA,cAAqC;IACnCD,EAAA,CAAAE,SAAA,cAAiF;IACjFF,EAAA,CAAAmB,UAAA,IAAAiB,+CAAA,kBAAuE;IAIrEpC,EADF,CAAAC,cAAA,cAA6B,iBAEa;IADhCD,EAAA,CAAAK,UAAA,mBAAAgC,kEAAAC,MAAA;MAAA,MAAAV,UAAA,GAAA5B,EAAA,CAAAQ,aAAA,CAAA0B,GAAA,EAAAxB,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAA4B,WAAA,CAAAX,UAAA,EAAAU,MAAA,CAA4B;IAAA,EAAC;IAE5CtC,EAAA,CAAAE,SAAA,YAA4B;IAC5BF,EAAA,CAAAmB,UAAA,IAAAqB,gDAAA,mBAAiC;IACnCxC,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,iBAA6E;IAArED,EAAA,CAAAK,UAAA,mBAAAoC,kEAAAH,MAAA;MAAA,MAAAV,UAAA,GAAA5B,EAAA,CAAAQ,aAAA,CAAA0B,GAAA,EAAAxB,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAA+B,YAAA,CAAAd,UAAA,EAAAU,MAAA,CAA6B;IAAA,EAAC;IAC7CtC,EAAA,CAAAE,SAAA,YAA4B;IAC5BF,EAAA,CAAAmB,UAAA,KAAAwB,iDAAA,mBAAkC;IACpC3C,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAmF;IAA3ED,EAAA,CAAAK,UAAA,mBAAAuC,mEAAAN,MAAA;MAAA,MAAAV,UAAA,GAAA5B,EAAA,CAAAQ,aAAA,CAAA0B,GAAA,EAAAxB,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAkC,gBAAA,CAAAjB,UAAA,EAAAU,MAAA,CAAiC;IAAA,EAAC;IACjDtC,EAAA,CAAAE,SAAA,aAA8B;IAC9BF,EAAA,CAAAmB,UAAA,KAAA2B,iDAAA,mBAAoC;IAG1C9C,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;IAEJJ,EADF,CAAAC,cAAA,eAA0B,cACC;IAAAD,EAAA,CAAAG,MAAA,IAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChDJ,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAG,MAAA,IAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAE9CJ,EADF,CAAAC,cAAA,eAA6B,gBACC;IAAAD,EAAA,CAAAG,MAAA,IAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAAmB,UAAA,KAAA4B,iDAAA,mBAC6B;IAC/B/C,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAC,cAAA,eAA6B,kBAC2C;IAA9DD,EAAA,CAAAK,UAAA,mBAAA2C,mEAAAV,MAAA;MAAA,MAAAV,UAAA,GAAA5B,EAAA,CAAAQ,aAAA,CAAA0B,GAAA,EAAAxB,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAsC,aAAA,CAAArB,UAAA,EAAAU,MAAA,CAA8B;IAAA,EAAC;IAC9CtC,EAAA,CAAAE,SAAA,aAA4B;IAACF,EAAA,CAAAG,MAAA,kBAC/B;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAA8D;IAAtDD,EAAA,CAAAK,UAAA,mBAAA6C,mEAAAZ,MAAA;MAAA,MAAAV,UAAA,GAAA5B,EAAA,CAAAQ,aAAA,CAAA0B,GAAA,EAAAxB,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAwC,SAAA,CAAAvB,UAAA,EAAAU,MAAA,CAA0B;IAAA,EAAC;IAC1CtC,EAAA,CAAAE,SAAA,aAAoC;IAACF,EAAA,CAAAG,MAAA,qBACvC;IAGNH,EAHM,CAAAI,YAAA,EAAS,EACL,EACF,EACF;;;;;IArCGJ,EAAA,CAAAe,SAAA,GAAgC;IAACf,EAAjC,CAAAqB,UAAA,QAAAV,MAAA,CAAAyC,eAAA,CAAAxB,UAAA,GAAA5B,EAAA,CAAAwB,aAAA,CAAgC,QAAAI,UAAA,CAAAV,IAAA,CAAqB;IACpDlB,EAAA,CAAAe,SAAA,EAAwC;IAAxCf,EAAA,CAAAqB,UAAA,SAAAV,MAAA,CAAAgB,qBAAA,CAAAC,UAAA,MAAwC;IAKpC5B,EAAA,CAAAe,SAAA,GAA+B;IAA/Bf,EAAA,CAAAqD,WAAA,UAAAzB,UAAA,CAAA0B,OAAA,CAA+B;IAE9BtD,EAAA,CAAAe,SAAA,GAAwB;IAAxBf,EAAA,CAAAqB,UAAA,SAAAO,UAAA,CAAAC,UAAA,CAAwB;IAIxB7B,EAAA,CAAAe,SAAA,GAAyB;IAAzBf,EAAA,CAAAqB,UAAA,SAAAO,UAAA,CAAAE,WAAA,CAAyB;IAIzB9B,EAAA,CAAAe,SAAA,GAA2B;IAA3Bf,EAAA,CAAAqB,UAAA,SAAAO,UAAA,CAAAG,aAAA,CAA2B;IAKb/B,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAgB,iBAAA,CAAAY,UAAA,CAAAV,IAAA,CAAkB;IAClBlB,EAAA,CAAAe,SAAA,GAAmB;IAAnBf,EAAA,CAAAgB,iBAAA,CAAAY,UAAA,CAAA2B,KAAA,CAAmB;IAEdvD,EAAA,CAAAe,SAAA,GAAoB;IAApBf,EAAA,CAAA0B,kBAAA,WAAAE,UAAA,CAAA4B,KAAA,KAAoB;IACzCxD,EAAA,CAAAe,SAAA,EAAoE;IAApEf,EAAA,CAAAqB,UAAA,SAAAO,UAAA,CAAAI,aAAA,IAAAJ,UAAA,CAAAI,aAAA,GAAAJ,UAAA,CAAA4B,KAAA,CAAoE;;;;;IA5BnFxD,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAmB,UAAA,IAAAsC,yCAAA,oBAAkG;IAwCpGzD,EAAA,CAAAI,YAAA,EAAM;;;;IAxCqBJ,EAAA,CAAAe,SAAA,EAAmB;IAAnBf,EAAA,CAAAqB,UAAA,YAAAV,MAAA,CAAA+C,gBAAA,CAAmB;;;;;IAuDpC1D,EAAA,CAAAC,cAAA,WAAiC;IAAAD,EAAA,CAAAG,MAAA,GAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAA/BJ,EAAA,CAAAe,SAAA,EAAwB;IAAxBf,EAAA,CAAAgB,iBAAA,CAAA2C,UAAA,CAAA9B,UAAA,CAAwB;;;;;IAIzD7B,EAAA,CAAAC,cAAA,WAAkC;IAAAD,EAAA,CAAAG,MAAA,GAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAhCJ,EAAA,CAAAe,SAAA,EAAyB;IAAzBf,EAAA,CAAAgB,iBAAA,CAAA2C,UAAA,CAAA7B,WAAA,CAAyB;;;;;IAI3D9B,EAAA,CAAAC,cAAA,WAAoC;IAAAD,EAAA,CAAAG,MAAA,GAA2B;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAlCJ,EAAA,CAAAe,SAAA,EAA2B;IAA3Bf,EAAA,CAAAgB,iBAAA,CAAA2C,UAAA,CAAA5B,aAAA,CAA2B;;;;;IASjE/B,EAAA,CAAAC,cAAA,eAC6B;IAAAD,EAAA,CAAAG,MAAA,GAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAnCJ,EAAA,CAAAe,SAAA,EAA4B;IAA5Bf,EAAA,CAAA0B,kBAAA,WAAAiC,UAAA,CAAA3B,aAAA,KAA4B;;;;;;IA1B/DhC,EAAA,CAAAC,cAAA,cAA6F;IAA/BD,EAAA,CAAAK,UAAA,mBAAAuD,yDAAA;MAAA,MAAAD,UAAA,GAAA3D,EAAA,CAAAQ,aAAA,CAAAqD,GAAA,EAAAnD,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAwB,WAAA,CAAAwB,UAAA,CAAoB;IAAA,EAAC;IAC1F3D,EAAA,CAAAC,cAAA,cAAqC;IACnCD,EAAA,CAAAE,SAAA,cAAiF;IACjFF,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAG,MAAA,UAAG;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAE9BJ,EADF,CAAAC,cAAA,cAA6B,iBAEa;IADhCD,EAAA,CAAAK,UAAA,mBAAAyD,4DAAAxB,MAAA;MAAA,MAAAqB,UAAA,GAAA3D,EAAA,CAAAQ,aAAA,CAAAqD,GAAA,EAAAnD,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAA4B,WAAA,CAAAoB,UAAA,EAAArB,MAAA,CAA4B;IAAA,EAAC;IAE5CtC,EAAA,CAAAE,SAAA,YAA4B;IAC5BF,EAAA,CAAAmB,UAAA,IAAA4C,0CAAA,mBAAiC;IACnC/D,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,iBAA6E;IAArED,EAAA,CAAAK,UAAA,mBAAA2D,4DAAA1B,MAAA;MAAA,MAAAqB,UAAA,GAAA3D,EAAA,CAAAQ,aAAA,CAAAqD,GAAA,EAAAnD,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAA+B,YAAA,CAAAiB,UAAA,EAAArB,MAAA,CAA6B;IAAA,EAAC;IAC7CtC,EAAA,CAAAE,SAAA,aAA4B;IAC5BF,EAAA,CAAAmB,UAAA,KAAA8C,2CAAA,mBAAkC;IACpCjE,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAAmF;IAA3ED,EAAA,CAAAK,UAAA,mBAAA6D,6DAAA5B,MAAA;MAAA,MAAAqB,UAAA,GAAA3D,EAAA,CAAAQ,aAAA,CAAAqD,GAAA,EAAAnD,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAkC,gBAAA,CAAAc,UAAA,EAAArB,MAAA,CAAiC;IAAA,EAAC;IACjDtC,EAAA,CAAAE,SAAA,aAA8B;IAC9BF,EAAA,CAAAmB,UAAA,KAAAgD,2CAAA,mBAAoC;IAG1CnE,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;IAEJJ,EADF,CAAAC,cAAA,eAA0B,cACC;IAAAD,EAAA,CAAAG,MAAA,IAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChDJ,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAG,MAAA,IAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAE9CJ,EADF,CAAAC,cAAA,eAA6B,gBACC;IAAAD,EAAA,CAAAG,MAAA,IAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAAmB,UAAA,KAAAiD,2CAAA,mBAC6B;IAC/BpE,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAC,cAAA,eAA6B,kBAC2C;IAA9DD,EAAA,CAAAK,UAAA,mBAAAgE,6DAAA/B,MAAA;MAAA,MAAAqB,UAAA,GAAA3D,EAAA,CAAAQ,aAAA,CAAAqD,GAAA,EAAAnD,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAsC,aAAA,CAAAU,UAAA,EAAArB,MAAA,CAA8B;IAAA,EAAC;IAC9CtC,EAAA,CAAAE,SAAA,aAA4B;IAACF,EAAA,CAAAG,MAAA,kBAC/B;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,kBAA8D;IAAtDD,EAAA,CAAAK,UAAA,mBAAAiE,6DAAAhC,MAAA;MAAA,MAAAqB,UAAA,GAAA3D,EAAA,CAAAQ,aAAA,CAAAqD,GAAA,EAAAnD,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAwC,SAAA,CAAAQ,UAAA,EAAArB,MAAA,CAA0B;IAAA,EAAC;IAC1CtC,EAAA,CAAAE,SAAA,aAAoC;IAACF,EAAA,CAAAG,MAAA,qBACvC;IAGNH,EAHM,CAAAI,YAAA,EAAS,EACL,EACF,EACF;;;;;IAnCGJ,EAAA,CAAAe,SAAA,GAAgC;IAACf,EAAjC,CAAAqB,UAAA,QAAAV,MAAA,CAAAyC,eAAA,CAAAO,UAAA,GAAA3D,EAAA,CAAAwB,aAAA,CAAgC,QAAAmC,UAAA,CAAAzC,IAAA,CAAqB;IAIhDlB,EAAA,CAAAe,SAAA,GAA+B;IAA/Bf,EAAA,CAAAqD,WAAA,UAAAM,UAAA,CAAAL,OAAA,CAA+B;IAE9BtD,EAAA,CAAAe,SAAA,GAAwB;IAAxBf,EAAA,CAAAqB,UAAA,SAAAsC,UAAA,CAAA9B,UAAA,CAAwB;IAIxB7B,EAAA,CAAAe,SAAA,GAAyB;IAAzBf,EAAA,CAAAqB,UAAA,SAAAsC,UAAA,CAAA7B,WAAA,CAAyB;IAIzB9B,EAAA,CAAAe,SAAA,GAA2B;IAA3Bf,EAAA,CAAAqB,UAAA,SAAAsC,UAAA,CAAA5B,aAAA,CAA2B;IAKb/B,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAgB,iBAAA,CAAA2C,UAAA,CAAAzC,IAAA,CAAkB;IAClBlB,EAAA,CAAAe,SAAA,GAAmB;IAAnBf,EAAA,CAAAgB,iBAAA,CAAA2C,UAAA,CAAAJ,KAAA,CAAmB;IAEdvD,EAAA,CAAAe,SAAA,GAAoB;IAApBf,EAAA,CAAA0B,kBAAA,WAAAiC,UAAA,CAAAH,KAAA,KAAoB;IACzCxD,EAAA,CAAAe,SAAA,EAAoE;IAApEf,EAAA,CAAAqB,UAAA,SAAAsC,UAAA,CAAA3B,aAAA,IAAA2B,UAAA,CAAA3B,aAAA,GAAA2B,UAAA,CAAAH,KAAA,CAAoE;;;;;;IAlHjFxD,EAJN,CAAAC,cAAA,aAA2C,aAEb,aACF,eAMC;IAHrBD,EAAA,CAAAuE,gBAAA,2BAAAC,4DAAAlC,MAAA;MAAAtC,EAAA,CAAAQ,aAAA,CAAAiE,GAAA;MAAA,MAAA9D,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAAZ,EAAA,CAAA0E,kBAAA,CAAA/D,MAAA,CAAAgE,WAAA,EAAArC,MAAA,MAAA3B,MAAA,CAAAgE,WAAA,GAAArC,MAAA;MAAA,OAAAtC,EAAA,CAAAa,WAAA,CAAAyB,MAAA;IAAA,EAAyB;IAEzBtC,EAAA,CAAAK,UAAA,yBAAAuE,0DAAA;MAAA5E,EAAA,CAAAQ,aAAA,CAAAiE,GAAA;MAAA,MAAA9D,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAeF,MAAA,CAAAkE,MAAA,EAAQ;IAAA,EAAC;IAJ1B7E,EAAA,CAAAI,YAAA,EAKuB;IACvBJ,EAAA,CAAAC,cAAA,gBAA8C;IAAtCD,EAAA,CAAAK,UAAA,mBAAAyE,qDAAA;MAAA9E,EAAA,CAAAQ,aAAA,CAAAiE,GAAA;MAAA,MAAA9D,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAkE,MAAA,EAAQ;IAAA,EAAC;IACxB7E,EAAA,CAAAE,SAAA,YAA6B;IAGnCF,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;IAIJJ,EADF,CAAAC,cAAA,kBAAkC,aACN;IAAAD,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC/CJ,EAAA,CAAAC,cAAA,cAA6B;IAC3BD,EAAA,CAAAmB,UAAA,KAAA4D,mCAAA,kBAGyC;IAK7C/E,EADE,CAAAI,YAAA,EAAM,EACE;IAIRJ,EADF,CAAAC,cAAA,mBAAyC,cACb;IAAAD,EAAA,CAAAG,MAAA,uBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC9CJ,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAmB,UAAA,KAAA6D,mCAAA,kBAA6D;IAMjEhF,EADE,CAAAI,YAAA,EAAM,EACE;IAIRJ,EADF,CAAAC,cAAA,mBAAkC,cACN;IAAAD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC3CJ,EAAA,CAAAmB,UAAA,KAAA8D,mCAAA,kBAA+D;IA0CjEjF,EAAA,CAAAI,YAAA,EAAU;IAIRJ,EADF,CAAAC,cAAA,mBAAsC,cACV;IAAAD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC3CJ,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAmB,UAAA,KAAA+D,mCAAA,oBAA6F;IAwCnGlF,EAFI,CAAAI,YAAA,EAAM,EACE,EACN;;;;IA/HEJ,EAAA,CAAAe,SAAA,GAAyB;IAAzBf,EAAA,CAAAmF,gBAAA,YAAAxE,MAAA,CAAAgE,WAAA,CAAyB;IAeJ3E,EAAA,CAAAe,SAAA,GAAa;IAAbf,EAAA,CAAAqB,UAAA,YAAAV,MAAA,CAAAyE,UAAA,CAAa;IAabpF,EAAA,CAAAe,SAAA,GAAiB;IAAjBf,EAAA,CAAAqB,UAAA,YAAAV,MAAA,CAAA0E,cAAA,CAAiB;IAWpCrF,EAAA,CAAAe,SAAA,GAAiC;IAAjCf,EAAA,CAAAqB,UAAA,SAAAV,MAAA,CAAA+C,gBAAA,CAAA4B,MAAA,KAAiC;IAgDZtF,EAAA,CAAAe,SAAA,GAAc;IAAdf,EAAA,CAAAqB,UAAA,YAAAV,MAAA,CAAA4E,WAAA,CAAc;;;ADrF/C,OAAM,MAAOC,aAAa;EAQxBC,YACUC,cAA8B,EAC9BC,WAAwB,EACxBC,WAAwB,EACxBC,eAAgC,EAChCC,MAAc;IAJd,KAAAJ,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IAZhB,KAAAT,cAAc,GAAU,EAAE;IAC1B,KAAA3B,gBAAgB,GAAU,EAAE;IAC5B,KAAA6B,WAAW,GAAU,EAAE;IACvB,KAAAH,UAAU,GAAU,EAAE;IACtB,KAAAT,WAAW,GAAW,EAAE;IACxB,KAAAoB,OAAO,GAAG,IAAI;EAQX;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,IAAI,CAACF,OAAO,GAAG,IAAI;IACnBG,OAAO,CAACC,GAAG,CAAC,CACV,IAAI,CAACC,kBAAkB,EAAE,EACzB,IAAI,CAACC,oBAAoB,EAAE,EAC3B,IAAI,CAACC,eAAe,EAAE,EACtB,IAAI,CAACC,cAAc,EAAE,CACtB,CAAC,CAACC,OAAO,CAAC,MAAK;MACd,IAAI,CAACT,OAAO,GAAG,KAAK;IACtB,CAAC,CAAC;EACJ;EAEAK,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACV,cAAc,CAACe,iBAAiB,EAAE,CAACC,SAAS,EAAE,CAACC,IAAI,CAC5DC,QAAQ,IAAI;MACX,IAAI,CAACvB,cAAc,GAAGuB,QAAQ,EAAEC,IAAI,IAAI,EAAE;IAC5C,CAAC,CACF,CAACC,KAAK,CAACC,KAAK,IAAG;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,IAAI,CAAC1B,cAAc,GAAG,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAgB,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACX,cAAc,CAACuB,mBAAmB,EAAE,CAACP,SAAS,EAAE,CAACC,IAAI,CAC9DC,QAAQ,IAAI;MACX,IAAI,CAAClD,gBAAgB,GAAGkD,QAAQ,EAAEC,IAAI,IAAI,EAAE;IAC9C,CAAC,CACF,CAACC,KAAK,CAACC,KAAK,IAAG;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,IAAI,CAACrD,gBAAgB,GAAG,EAAE;IAC5B,CAAC,CAAC;EACJ;EAEA4C,eAAeA,CAAA;IACb,OAAO,IAAI,CAACZ,cAAc,CAACwB,cAAc,EAAE,CAACR,SAAS,EAAE,CAACC,IAAI,CACzDC,QAAQ,IAAI;MACX,IAAI,CAACrB,WAAW,GAAGqB,QAAQ,EAAEC,IAAI,IAAI,EAAE;IACzC,CAAC,CACF,CAACC,KAAK,CAACC,KAAK,IAAG;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,IAAI,CAACxB,WAAW,GAAG,EAAE;IACvB,CAAC,CAAC;EACJ;EAEAgB,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACb,cAAc,CAACyB,aAAa,EAAE,CAACT,SAAS,EAAE,CAACC,IAAI,CACxDC,QAAQ,IAAI;MACX,IAAI,CAACxB,UAAU,GAAGwB,QAAQ,EAAEC,IAAI,IAAI,EAAE;IACxC,CAAC,CACF,CAACC,KAAK,CAACC,KAAK,IAAG;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,IAAI,CAAC3B,UAAU,GAAG,EAAE;IACtB,CAAC,CAAC;EACJ;EAEA;EACA7C,WAAWA,CAAC6E,OAAY,EAAEC,KAAY;IACpCA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,CAAC,IAAI,CAAC3B,WAAW,CAAC4B,eAAe,EAAE;MACrC,IAAI,CAACzB,MAAM,CAAC0B,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC;;IAGFJ,OAAO,CAAC9D,OAAO,GAAG,CAAC8D,OAAO,CAAC9D,OAAO;IAClC8D,OAAO,CAACvF,UAAU,GAAGuF,OAAO,CAAC9D,OAAO,GAAG,CAAC8D,OAAO,CAACvF,UAAU,IAAI,CAAC,IAAI,CAAC,GAAG,CAACuF,OAAO,CAACvF,UAAU,IAAI,CAAC,IAAI,CAAC;IAEpG,IAAI,CAAC6D,cAAc,CAAC+B,iBAAiB,CAACL,OAAO,CAACM,GAAG,CAAC,CAACC,SAAS,CAAC;MAC3DC,IAAI,EAAGhB,QAAQ,IAAI;QACjBI,OAAO,CAACa,GAAG,CAAC,uBAAuB,EAAEjB,QAAQ,CAAC;MAChD,CAAC;MACDG,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpDK,OAAO,CAAC9D,OAAO,GAAG,CAAC8D,OAAO,CAAC9D,OAAO;QAClC8D,OAAO,CAACvF,UAAU,GAAGuF,OAAO,CAAC9D,OAAO,GAAG,CAAC8D,OAAO,CAACvF,UAAU,IAAI,CAAC,IAAI,CAAC,GAAG,CAACuF,OAAO,CAACvF,UAAU,IAAI,CAAC,IAAI,CAAC;MACtG;KACD,CAAC;EACJ;EAEAa,YAAYA,CAAC0E,OAAY,EAAEC,KAAY;IACrCA,KAAK,CAACC,eAAe,EAAE;IACvB,MAAMQ,SAAS,GAAG;MAChBC,KAAK,EAAEX,OAAO,CAAClG,IAAI;MACnB8G,IAAI,EAAE,mCAAmCZ,OAAO,CAAClG,IAAI,EAAE;MACvD+G,GAAG,EAAE,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,YAAYhB,OAAO,CAACM,GAAG;KACtD;IAED,IAAIW,SAAS,CAACC,KAAK,EAAE;MACnBD,SAAS,CAACC,KAAK,CAACR,SAAS,CAAC;KAC3B,MAAM;MACLO,SAAS,CAACE,SAAS,CAACC,SAAS,CAACV,SAAS,CAACG,GAAG,CAAC,CAACtB,IAAI,CAAC,MAAK;QACrD8B,KAAK,CAAC,mCAAmC,CAAC;MAC5C,CAAC,CAAC;;IAGJ,IAAI,CAAC/C,cAAc,CAAChD,YAAY,CAAC0E,OAAO,CAACM,GAAG,CAAC,CAACC,SAAS,CAAC;MACtDC,IAAI,EAAGhB,QAAQ,IAAI;QACjBQ,OAAO,CAACtF,WAAW,GAAG,CAACsF,OAAO,CAACtF,WAAW,IAAI,CAAC,IAAI,CAAC;MACtD,CAAC;MACDiF,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;KACD,CAAC;EACJ;EAEAlE,gBAAgBA,CAACuE,OAAY,EAAEC,KAAY;IACzCA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,CAAC,IAAI,CAAC3B,WAAW,CAAC4B,eAAe,EAAE;MACrC,IAAI,CAACzB,MAAM,CAAC0B,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC;;IAGF,IAAI,CAAC1B,MAAM,CAAC0B,QAAQ,CAAC,CAAC,UAAU,EAAEJ,OAAO,CAACM,GAAG,CAAC,EAAE;MAC9CgB,WAAW,EAAE;QAAEC,MAAM,EAAE;MAAS;KACjC,CAAC;EACJ;EAEA1F,aAAaA,CAACmE,OAAY,EAAEC,KAAY;IACtCA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,CAAC,IAAI,CAAC3B,WAAW,CAAC4B,eAAe,EAAE;MACrC,IAAI,CAACzB,MAAM,CAAC0B,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC;;IAGF,IAAI,CAAC3B,eAAe,CAAC5C,aAAa,CAACmE,OAAO,CAACM,GAAG,CAAC,CAACC,SAAS,CAAC;MACxDC,IAAI,EAAGhB,QAAQ,IAAI;QACjBQ,OAAO,CAACwB,YAAY,GAAG,IAAI;QAC3B5B,OAAO,CAACa,GAAG,CAAC,4BAA4B,EAAEjB,QAAQ,CAAC;MACrD,CAAC;MACDG,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;KACD,CAAC;EACJ;EAEA5D,SAASA,CAACiE,OAAY,EAAEC,KAAY;IAClCA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,CAAC,IAAI,CAAC3B,WAAW,CAAC4B,eAAe,EAAE;MACrC,IAAI,CAACzB,MAAM,CAAC0B,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC;;IAGF,IAAI,CAAC5B,WAAW,CAACzC,SAAS,CAACiE,OAAO,CAACM,GAAG,EAAE,CAAC,CAAC,CAACC,SAAS,CAAC;MACnDC,IAAI,EAAGhB,QAAQ,IAAI;QACjBI,OAAO,CAACa,GAAG,CAAC,wBAAwB,EAAEjB,QAAQ,CAAC;QAC/C6B,KAAK,CAAC,qCAAqC,CAAC;MAC9C,CAAC;MACD1B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC/C;KACD,CAAC;EACJ;EAEA;EACA5E,WAAWA,CAACiF,OAAY;IACtB,IAAI,CAACtB,MAAM,CAAC0B,QAAQ,CAAC,CAAC,UAAU,EAAEJ,OAAO,CAACM,GAAG,CAAC,CAAC;EACjD;EAEA5G,kBAAkBA,CAAC+H,QAAa;IAC9B,IAAI,CAAC/C,MAAM,CAAC0B,QAAQ,CAAC,CAAC,WAAW,EAAEqB,QAAQ,CAACC,IAAI,CAAC,CAAC;EACpD;EAEAjE,MAAMA,CAAA;IACJ,IAAI,IAAI,CAACF,WAAW,CAACoE,IAAI,EAAE,EAAE;MAC3B,IAAI,CAACjD,MAAM,CAAC0B,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE;QAChCkB,WAAW,EAAE;UAAEM,CAAC,EAAE,IAAI,CAACrE;QAAW;OACnC,CAAC;;EAEN;EAEAvB,eAAeA,CAACgE,OAAY;IAC1B,OAAOA,OAAO,CAAC6B,MAAM,GAAG,CAAC,CAAC,EAAEhB,GAAG,IAAI,gCAAgC;EACrE;EAEAtG,qBAAqBA,CAACyF,OAAY;IAChC,IAAI,CAACA,OAAO,CAACpF,aAAa,IAAIoF,OAAO,CAACpF,aAAa,IAAIoF,OAAO,CAAC5D,KAAK,EAAE,OAAO,CAAC;IAC9E,OAAO0F,IAAI,CAACC,KAAK,CAAE,CAAC/B,OAAO,CAACpF,aAAa,GAAGoF,OAAO,CAAC5D,KAAK,IAAI4D,OAAO,CAACpF,aAAa,GAAI,GAAG,CAAC;EAC5F;;;uBArMWwD,aAAa,EAAAxF,EAAA,CAAAoJ,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAtJ,EAAA,CAAAoJ,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAxJ,EAAA,CAAAoJ,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA1J,EAAA,CAAAoJ,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAA5J,EAAA,CAAAoJ,iBAAA,CAAAS,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAbtE,aAAa;MAAAuE,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAjK,EAAA,CAAAkK,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChB1BxK,EAAA,CAAAC,cAAA,aAA4B;UAQ1BD,EANA,CAAAmB,UAAA,IAAAuJ,4BAAA,iBAA+C,IAAAC,4BAAA,kBAMJ;UAsI7C3K,EAAA,CAAAI,YAAA,EAAM;;;UA5IEJ,EAAA,CAAAe,SAAA,EAAa;UAAbf,EAAA,CAAAqB,UAAA,SAAAoJ,GAAA,CAAA1E,OAAA,CAAa;UAMb/F,EAAA,CAAAe,SAAA,EAAc;UAAdf,EAAA,CAAAqB,UAAA,UAAAoJ,GAAA,CAAA1E,OAAA,CAAc;;;qBDIVjG,YAAY,EAAA8K,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE/K,WAAW,EAAAgL,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}