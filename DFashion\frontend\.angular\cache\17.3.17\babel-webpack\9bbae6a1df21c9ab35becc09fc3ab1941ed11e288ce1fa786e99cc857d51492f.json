{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/cart.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction CheckoutComponent_div_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"span\");\n    i0.ɵɵtext(2, \"Discount\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 36);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"-\\u20B9\", i0.ɵɵpipeBind1(5, 1, ctx_r0.cartSummary.discount), \"\");\n  }\n}\nexport class CheckoutComponent {\n  constructor(cartService, router) {\n    this.cartService = cartService;\n    this.router = router;\n    this.cartSummary = null;\n    this.tax = 0;\n    this.paymentMethod = 'card';\n    this.shippingAddress = {\n      firstName: '',\n      lastName: '',\n      email: '',\n      phone: '',\n      address: '',\n      city: '',\n      state: '',\n      pinCode: ''\n    };\n  }\n  ngOnInit() {\n    this.cartService.cartSummary$.subscribe(summary => {\n      this.cartSummary = summary;\n      this.tax = summary ? Math.round(summary.total * 0.18) : 0; // 18% GST\n    });\n  }\n  getTotal() {\n    return this.cartSummary ? this.cartSummary.total + this.tax : 0;\n  }\n  isFormValid() {\n    return !!(this.shippingAddress.firstName && this.shippingAddress.lastName && this.shippingAddress.email && this.shippingAddress.phone && this.shippingAddress.address && this.shippingAddress.city && this.shippingAddress.state && this.shippingAddress.pinCode && this.paymentMethod);\n  }\n  placeOrder() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.isFormValid()) {\n        // Mock order placement\n        alert('Order placed successfully! (Demo)');\n        _this.cartService.clearCartAPI().subscribe({\n          next: () => {\n            _this.router.navigate(['/']);\n          },\n          error: error => {\n            console.error('Error clearing cart:', error);\n            _this.router.navigate(['/']);\n          }\n        });\n      }\n    })();\n  }\n  backToCart() {\n    this.router.navigate(['/shop/cart']);\n  }\n  static {\n    this.ɵfac = function CheckoutComponent_Factory(t) {\n      return new (t || CheckoutComponent)(i0.ɵɵdirectiveInject(i1.CartService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CheckoutComponent,\n      selectors: [[\"app-checkout\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 112,\n      vars: 22,\n      consts: [[1, \"checkout-page\"], [1, \"checkout-header\"], [1, \"steps\"], [1, \"step\", \"active\"], [1, \"step-number\"], [1, \"step-label\"], [1, \"step\"], [1, \"checkout-content\"], [1, \"checkout-form\"], [1, \"form-section\"], [1, \"form-row\"], [1, \"form-group\"], [\"type\", \"text\", \"name\", \"firstName\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"name\", \"lastName\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"email\", \"name\", \"email\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"tel\", \"name\", \"phone\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"name\", \"address\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"name\", \"city\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"name\", \"state\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"name\", \"pinCode\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [1, \"payment-options\"], [1, \"payment-option\"], [\"type\", \"radio\", \"name\", \"paymentMethod\", \"value\", \"card\", 3, \"ngModelChange\", \"ngModel\"], [1, \"option-content\"], [1, \"fas\", \"fa-credit-card\"], [\"type\", \"radio\", \"name\", \"paymentMethod\", \"value\", \"upi\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-mobile-alt\"], [\"type\", \"radio\", \"name\", \"paymentMethod\", \"value\", \"cod\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-money-bill-wave\"], [1, \"order-summary\"], [1, \"summary-card\"], [1, \"summary-row\"], [\"class\", \"summary-row\", 4, \"ngIf\"], [1, \"summary-row\", \"total\"], [1, \"place-order-btn\", 3, \"click\", \"disabled\"], [1, \"back-to-cart-btn\", 3, \"click\"], [1, \"discount\"]],\n      template: function CheckoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n          i0.ɵɵtext(3, \"Checkout\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"div\", 3)(6, \"span\", 4);\n          i0.ɵɵtext(7, \"1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"span\", 5);\n          i0.ɵɵtext(9, \"Shipping\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 6)(11, \"span\", 4);\n          i0.ɵɵtext(12, \"2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"span\", 5);\n          i0.ɵɵtext(14, \"Payment\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 6)(16, \"span\", 4);\n          i0.ɵɵtext(17, \"3\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"span\", 5);\n          i0.ɵɵtext(19, \"Review\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(20, \"div\", 7)(21, \"div\", 8)(22, \"div\", 9)(23, \"h3\");\n          i0.ɵɵtext(24, \"Shipping Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"form\")(26, \"div\", 10)(27, \"div\", 11)(28, \"label\");\n          i0.ɵɵtext(29, \"First Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"input\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CheckoutComponent_Template_input_ngModelChange_30_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.shippingAddress.firstName, $event) || (ctx.shippingAddress.firstName = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 11)(32, \"label\");\n          i0.ɵɵtext(33, \"Last Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"input\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CheckoutComponent_Template_input_ngModelChange_34_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.shippingAddress.lastName, $event) || (ctx.shippingAddress.lastName = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(35, \"div\", 11)(36, \"label\");\n          i0.ɵɵtext(37, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"input\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CheckoutComponent_Template_input_ngModelChange_38_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.shippingAddress.email, $event) || (ctx.shippingAddress.email = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"div\", 11)(40, \"label\");\n          i0.ɵɵtext(41, \"Phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"input\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CheckoutComponent_Template_input_ngModelChange_42_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.shippingAddress.phone, $event) || (ctx.shippingAddress.phone = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"div\", 11)(44, \"label\");\n          i0.ɵɵtext(45, \"Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"input\", 16);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CheckoutComponent_Template_input_ngModelChange_46_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.shippingAddress.address, $event) || (ctx.shippingAddress.address = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"div\", 10)(48, \"div\", 11)(49, \"label\");\n          i0.ɵɵtext(50, \"City\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"input\", 17);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CheckoutComponent_Template_input_ngModelChange_51_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.shippingAddress.city, $event) || (ctx.shippingAddress.city = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(52, \"div\", 11)(53, \"label\");\n          i0.ɵɵtext(54, \"State\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"input\", 18);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CheckoutComponent_Template_input_ngModelChange_55_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.shippingAddress.state, $event) || (ctx.shippingAddress.state = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(56, \"div\", 11)(57, \"label\");\n          i0.ɵɵtext(58, \"PIN Code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"input\", 19);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CheckoutComponent_Template_input_ngModelChange_59_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.shippingAddress.pinCode, $event) || (ctx.shippingAddress.pinCode = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(60, \"div\", 9)(61, \"h3\");\n          i0.ɵɵtext(62, \"Payment Method\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"div\", 20)(64, \"label\", 21)(65, \"input\", 22);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CheckoutComponent_Template_input_ngModelChange_65_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.paymentMethod, $event) || (ctx.paymentMethod = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"span\", 23);\n          i0.ɵɵelement(67, \"i\", 24);\n          i0.ɵɵtext(68, \" Credit/Debit Card \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(69, \"label\", 21)(70, \"input\", 25);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CheckoutComponent_Template_input_ngModelChange_70_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.paymentMethod, $event) || (ctx.paymentMethod = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"span\", 23);\n          i0.ɵɵelement(72, \"i\", 26);\n          i0.ɵɵtext(73, \" UPI \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(74, \"label\", 21)(75, \"input\", 27);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function CheckoutComponent_Template_input_ngModelChange_75_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.paymentMethod, $event) || (ctx.paymentMethod = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"span\", 23);\n          i0.ɵɵelement(77, \"i\", 28);\n          i0.ɵɵtext(78, \" Cash on Delivery \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(79, \"div\", 29)(80, \"div\", 30)(81, \"h3\");\n          i0.ɵɵtext(82, \"Order Summary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"div\", 31)(84, \"span\");\n          i0.ɵɵtext(85, \"Subtotal\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"span\");\n          i0.ɵɵtext(87);\n          i0.ɵɵpipe(88, \"number\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(89, CheckoutComponent_div_89_Template, 6, 3, \"div\", 32);\n          i0.ɵɵelementStart(90, \"div\", 31)(91, \"span\");\n          i0.ɵɵtext(92, \"Shipping\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"span\");\n          i0.ɵɵtext(94, \"Free\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(95, \"div\", 31)(96, \"span\");\n          i0.ɵɵtext(97, \"Tax\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"span\");\n          i0.ɵɵtext(99);\n          i0.ɵɵpipe(100, \"number\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(101, \"hr\");\n          i0.ɵɵelementStart(102, \"div\", 33)(103, \"span\");\n          i0.ɵɵtext(104, \"Total\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"span\");\n          i0.ɵɵtext(106);\n          i0.ɵɵpipe(107, \"number\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(108, \"button\", 34);\n          i0.ɵɵlistener(\"click\", function CheckoutComponent_Template_button_click_108_listener() {\n            return ctx.placeOrder();\n          });\n          i0.ɵɵtext(109, \" Place Order \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(110, \"button\", 35);\n          i0.ɵɵlistener(\"click\", function CheckoutComponent_Template_button_click_110_listener() {\n            return ctx.backToCart();\n          });\n          i0.ɵɵtext(111, \" Back to Cart \");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(30);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.shippingAddress.firstName);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.shippingAddress.lastName);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.shippingAddress.email);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.shippingAddress.phone);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.shippingAddress.address);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.shippingAddress.city);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.shippingAddress.state);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.shippingAddress.pinCode);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.paymentMethod);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.paymentMethod);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.paymentMethod);\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(88, 16, ctx.cartSummary == null ? null : ctx.cartSummary.subtotal), \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.cartSummary && ctx.cartSummary.discount && ctx.cartSummary.discount > 0);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(100, 18, ctx.tax), \"\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(107, 20, ctx.getTotal()), \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", !ctx.isFormValid());\n        }\n      },\n      dependencies: [CommonModule, i3.NgIf, i3.DecimalPipe, FormsModule, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.RadioControlValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.NgModel, i4.NgForm],\n      styles: [\".checkout-page[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.checkout-header[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n\\n.checkout-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 700;\\n  margin-bottom: 1rem;\\n  color: #333;\\n}\\n\\n.steps[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2rem;\\n}\\n\\n.step[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  color: #999;\\n}\\n\\n.step.active[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.step-number[_ngcontent-%COMP%] {\\n  width: 30px;\\n  height: 30px;\\n  border-radius: 50%;\\n  background: #f0f0f0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 600;\\n}\\n\\n.step.active[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  color: white;\\n}\\n\\n.checkout-content[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 2fr 1fr;\\n  gap: 2rem;\\n}\\n\\n.form-section[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 1.5rem;\\n  border-radius: 8px;\\n  border: 1px solid #eee;\\n  margin-bottom: 1rem;\\n}\\n\\n.form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  color: #333;\\n}\\n\\n.form-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 1rem;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 0.5rem;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0.75rem;\\n  border: 1px solid #ddd;\\n  border-radius: 4px;\\n  font-size: 1rem;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #007bff;\\n}\\n\\n.payment-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1rem;\\n}\\n\\n.payment-option[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 1rem;\\n  border: 2px solid #eee;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  transition: border-color 0.2s;\\n}\\n\\n.payment-option[_ngcontent-%COMP%]:hover {\\n  border-color: #007bff;\\n}\\n\\n.payment-option[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%] {\\n  margin-right: 1rem;\\n}\\n\\n.option-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  font-weight: 600;\\n}\\n\\n.option-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  color: #666;\\n}\\n\\n.summary-card[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  padding: 1.5rem;\\n  border-radius: 8px;\\n  position: sticky;\\n  top: 2rem;\\n}\\n\\n.summary-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  color: #333;\\n}\\n\\n.summary-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-bottom: 0.75rem;\\n}\\n\\n.summary-row.total[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  font-size: 1.2rem;\\n  color: #333;\\n}\\n\\n.discount[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n\\n.place-order-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background: #28a745;\\n  color: white;\\n  border: none;\\n  padding: 1rem;\\n  border-radius: 8px;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  margin-bottom: 1rem;\\n  transition: background 0.2s;\\n}\\n\\n.place-order-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #218838;\\n}\\n\\n.place-order-btn[_ngcontent-%COMP%]:disabled {\\n  background: #ccc;\\n  cursor: not-allowed;\\n}\\n\\n.back-to-cart-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background: transparent;\\n  color: #007bff;\\n  border: 2px solid #007bff;\\n  padding: 1rem;\\n  border-radius: 8px;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n}\\n\\n.back-to-cart-btn[_ngcontent-%COMP%]:hover {\\n  background: #007bff;\\n  color: white;\\n}\\n\\n@media (max-width: 768px) {\\n  .checkout-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .form-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .steps[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ctx_r0", "cartSummary", "discount", "CheckoutComponent", "constructor", "cartService", "router", "tax", "paymentMethod", "shippingAddress", "firstName", "lastName", "email", "phone", "address", "city", "state", "pinCode", "ngOnInit", "cartSummary$", "subscribe", "summary", "Math", "round", "total", "getTotal", "isFormValid", "placeOrder", "_this", "_asyncToGenerator", "alert", "clearCartAPI", "next", "navigate", "error", "console", "backToCart", "ɵɵdirectiveInject", "i1", "CartService", "i2", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "CheckoutComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "CheckoutComponent_Template_input_ngModelChange_30_listener", "$event", "ɵɵtwoWayBindingSet", "CheckoutComponent_Template_input_ngModelChange_34_listener", "CheckoutComponent_Template_input_ngModelChange_38_listener", "CheckoutComponent_Template_input_ngModelChange_42_listener", "CheckoutComponent_Template_input_ngModelChange_46_listener", "CheckoutComponent_Template_input_ngModelChange_51_listener", "CheckoutComponent_Template_input_ngModelChange_55_listener", "CheckoutComponent_Template_input_ngModelChange_59_listener", "CheckoutComponent_Template_input_ngModelChange_65_listener", "ɵɵelement", "CheckoutComponent_Template_input_ngModelChange_70_listener", "CheckoutComponent_Template_input_ngModelChange_75_listener", "ɵɵtemplate", "CheckoutComponent_div_89_Template", "ɵɵlistener", "CheckoutComponent_Template_button_click_108_listener", "CheckoutComponent_Template_button_click_110_listener", "ɵɵtwoWayProperty", "subtotal", "ɵɵproperty", "i3", "NgIf", "DecimalPipe", "i4", "ɵNgNoValidate", "DefaultValueAccessor", "RadioControlValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "NgModel", "NgForm", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\shop\\pages\\checkout\\checkout.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router } from '@angular/router';\n\nimport { CartService, CartSummary } from '../../../../core/services/cart.service';\n\n@Component({\n  selector: 'app-checkout',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <div class=\"checkout-page\">\n      <div class=\"checkout-header\">\n        <h1>Checkout</h1>\n        <div class=\"steps\">\n          <div class=\"step active\">\n            <span class=\"step-number\">1</span>\n            <span class=\"step-label\">Shipping</span>\n          </div>\n          <div class=\"step\">\n            <span class=\"step-number\">2</span>\n            <span class=\"step-label\">Payment</span>\n          </div>\n          <div class=\"step\">\n            <span class=\"step-number\">3</span>\n            <span class=\"step-label\">Review</span>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"checkout-content\">\n        <div class=\"checkout-form\">\n          <div class=\"form-section\">\n            <h3>Shipping Address</h3>\n            <form>\n              <div class=\"form-row\">\n                <div class=\"form-group\">\n                  <label>First Name</label>\n                  <input type=\"text\" [(ngModel)]=\"shippingAddress.firstName\" name=\"firstName\" required>\n                </div>\n                <div class=\"form-group\">\n                  <label>Last Name</label>\n                  <input type=\"text\" [(ngModel)]=\"shippingAddress.lastName\" name=\"lastName\" required>\n                </div>\n              </div>\n              <div class=\"form-group\">\n                <label>Email</label>\n                <input type=\"email\" [(ngModel)]=\"shippingAddress.email\" name=\"email\" required>\n              </div>\n              <div class=\"form-group\">\n                <label>Phone</label>\n                <input type=\"tel\" [(ngModel)]=\"shippingAddress.phone\" name=\"phone\" required>\n              </div>\n              <div class=\"form-group\">\n                <label>Address</label>\n                <input type=\"text\" [(ngModel)]=\"shippingAddress.address\" name=\"address\" required>\n              </div>\n              <div class=\"form-row\">\n                <div class=\"form-group\">\n                  <label>City</label>\n                  <input type=\"text\" [(ngModel)]=\"shippingAddress.city\" name=\"city\" required>\n                </div>\n                <div class=\"form-group\">\n                  <label>State</label>\n                  <input type=\"text\" [(ngModel)]=\"shippingAddress.state\" name=\"state\" required>\n                </div>\n                <div class=\"form-group\">\n                  <label>PIN Code</label>\n                  <input type=\"text\" [(ngModel)]=\"shippingAddress.pinCode\" name=\"pinCode\" required>\n                </div>\n              </div>\n            </form>\n          </div>\n\n          <div class=\"form-section\">\n            <h3>Payment Method</h3>\n            <div class=\"payment-options\">\n              <label class=\"payment-option\">\n                <input type=\"radio\" name=\"paymentMethod\" value=\"card\" [(ngModel)]=\"paymentMethod\">\n                <span class=\"option-content\">\n                  <i class=\"fas fa-credit-card\"></i>\n                  Credit/Debit Card\n                </span>\n              </label>\n              <label class=\"payment-option\">\n                <input type=\"radio\" name=\"paymentMethod\" value=\"upi\" [(ngModel)]=\"paymentMethod\">\n                <span class=\"option-content\">\n                  <i class=\"fas fa-mobile-alt\"></i>\n                  UPI\n                </span>\n              </label>\n              <label class=\"payment-option\">\n                <input type=\"radio\" name=\"paymentMethod\" value=\"cod\" [(ngModel)]=\"paymentMethod\">\n                <span class=\"option-content\">\n                  <i class=\"fas fa-money-bill-wave\"></i>\n                  Cash on Delivery\n                </span>\n              </label>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"order-summary\">\n          <div class=\"summary-card\">\n            <h3>Order Summary</h3>\n            <div class=\"summary-row\">\n              <span>Subtotal</span>\n              <span>₹{{ cartSummary?.subtotal | number }}</span>\n            </div>\n            <div class=\"summary-row\" *ngIf=\"cartSummary && cartSummary.discount && cartSummary.discount > 0\">\n              <span>Discount</span>\n              <span class=\"discount\">-₹{{ cartSummary.discount | number }}</span>\n            </div>\n            <div class=\"summary-row\">\n              <span>Shipping</span>\n              <span>Free</span>\n            </div>\n            <div class=\"summary-row\">\n              <span>Tax</span>\n              <span>₹{{ tax | number }}</span>\n            </div>\n            <hr>\n            <div class=\"summary-row total\">\n              <span>Total</span>\n              <span>₹{{ getTotal() | number }}</span>\n            </div>\n            <button class=\"place-order-btn\" (click)=\"placeOrder()\" [disabled]=\"!isFormValid()\">\n              Place Order\n            </button>\n            <button class=\"back-to-cart-btn\" (click)=\"backToCart()\">\n              Back to Cart\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .checkout-page {\n      padding: 2rem;\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    .checkout-header {\n      margin-bottom: 2rem;\n    }\n\n    .checkout-header h1 {\n      font-size: 2rem;\n      font-weight: 700;\n      margin-bottom: 1rem;\n      color: #333;\n    }\n\n    .steps {\n      display: flex;\n      gap: 2rem;\n    }\n\n    .step {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      color: #999;\n    }\n\n    .step.active {\n      color: #007bff;\n    }\n\n    .step-number {\n      width: 30px;\n      height: 30px;\n      border-radius: 50%;\n      background: #f0f0f0;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-weight: 600;\n    }\n\n    .step.active .step-number {\n      background: #007bff;\n      color: white;\n    }\n\n    .checkout-content {\n      display: grid;\n      grid-template-columns: 2fr 1fr;\n      gap: 2rem;\n    }\n\n    .form-section {\n      background: white;\n      padding: 1.5rem;\n      border-radius: 8px;\n      border: 1px solid #eee;\n      margin-bottom: 1rem;\n    }\n\n    .form-section h3 {\n      margin-bottom: 1rem;\n      color: #333;\n    }\n\n    .form-row {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: 1rem;\n    }\n\n    .form-group {\n      margin-bottom: 1rem;\n    }\n\n    .form-group label {\n      display: block;\n      margin-bottom: 0.5rem;\n      font-weight: 600;\n      color: #333;\n    }\n\n    .form-group input {\n      width: 100%;\n      padding: 0.75rem;\n      border: 1px solid #ddd;\n      border-radius: 4px;\n      font-size: 1rem;\n    }\n\n    .form-group input:focus {\n      outline: none;\n      border-color: #007bff;\n    }\n\n    .payment-options {\n      display: flex;\n      flex-direction: column;\n      gap: 1rem;\n    }\n\n    .payment-option {\n      display: flex;\n      align-items: center;\n      padding: 1rem;\n      border: 2px solid #eee;\n      border-radius: 8px;\n      cursor: pointer;\n      transition: border-color 0.2s;\n    }\n\n    .payment-option:hover {\n      border-color: #007bff;\n    }\n\n    .payment-option input[type=\"radio\"] {\n      margin-right: 1rem;\n    }\n\n    .option-content {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      font-weight: 600;\n    }\n\n    .option-content i {\n      font-size: 1.2rem;\n      color: #666;\n    }\n\n    .summary-card {\n      background: #f8f9fa;\n      padding: 1.5rem;\n      border-radius: 8px;\n      position: sticky;\n      top: 2rem;\n    }\n\n    .summary-card h3 {\n      margin-bottom: 1rem;\n      color: #333;\n    }\n\n    .summary-row {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 0.75rem;\n    }\n\n    .summary-row.total {\n      font-weight: 700;\n      font-size: 1.2rem;\n      color: #333;\n    }\n\n    .discount {\n      color: #28a745;\n    }\n\n    .place-order-btn {\n      width: 100%;\n      background: #28a745;\n      color: white;\n      border: none;\n      padding: 1rem;\n      border-radius: 8px;\n      font-size: 1.1rem;\n      font-weight: 600;\n      cursor: pointer;\n      margin-bottom: 1rem;\n      transition: background 0.2s;\n    }\n\n    .place-order-btn:hover:not(:disabled) {\n      background: #218838;\n    }\n\n    .place-order-btn:disabled {\n      background: #ccc;\n      cursor: not-allowed;\n    }\n\n    .back-to-cart-btn {\n      width: 100%;\n      background: transparent;\n      color: #007bff;\n      border: 2px solid #007bff;\n      padding: 1rem;\n      border-radius: 8px;\n      font-size: 1rem;\n      font-weight: 600;\n      cursor: pointer;\n      transition: all 0.2s;\n    }\n\n    .back-to-cart-btn:hover {\n      background: #007bff;\n      color: white;\n    }\n\n    @media (max-width: 768px) {\n      .checkout-content {\n        grid-template-columns: 1fr;\n      }\n\n      .form-row {\n        grid-template-columns: 1fr;\n      }\n\n      .steps {\n        flex-direction: column;\n        gap: 1rem;\n      }\n    }\n  `]\n})\nexport class CheckoutComponent implements OnInit {\n  cartSummary: CartSummary | null = null;\n  tax = 0;\n  paymentMethod = 'card';\n\n  shippingAddress = {\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    address: '',\n    city: '',\n    state: '',\n    pinCode: ''\n  };\n\n  constructor(\n    private cartService: CartService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.cartService.cartSummary$.subscribe(summary => {\n      this.cartSummary = summary;\n      this.tax = summary ? Math.round(summary.total * 0.18) : 0; // 18% GST\n    });\n  }\n\n  getTotal(): number {\n    return this.cartSummary ? this.cartSummary.total + this.tax : 0;\n  }\n\n  isFormValid(): boolean {\n    return !!(\n      this.shippingAddress.firstName &&\n      this.shippingAddress.lastName &&\n      this.shippingAddress.email &&\n      this.shippingAddress.phone &&\n      this.shippingAddress.address &&\n      this.shippingAddress.city &&\n      this.shippingAddress.state &&\n      this.shippingAddress.pinCode &&\n      this.paymentMethod\n    );\n  }\n\n  async placeOrder() {\n    if (this.isFormValid()) {\n      // Mock order placement\n      alert('Order placed successfully! (Demo)');\n      this.cartService.clearCartAPI().subscribe({\n        next: () => {\n          this.router.navigate(['/']);\n        },\n        error: (error) => {\n          console.error('Error clearing cart:', error);\n          this.router.navigate(['/']);\n        }\n      });\n    }\n  }\n\n  backToCart() {\n    this.router.navigate(['/shop/cart']);\n  }\n}\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;IA6G9BC,EADF,CAAAC,cAAA,cAAiG,WACzF;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrBH,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAE,MAAA,GAAqC;;IAC9DF,EAD8D,CAAAG,YAAA,EAAO,EAC/D;;;;IADmBH,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAK,kBAAA,YAAAL,EAAA,CAAAM,WAAA,OAAAC,MAAA,CAAAC,WAAA,CAAAC,QAAA,MAAqC;;;AAuP1E,OAAM,MAAOC,iBAAiB;EAgB5BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAjBhB,KAAAL,WAAW,GAAuB,IAAI;IACtC,KAAAM,GAAG,GAAG,CAAC;IACP,KAAAC,aAAa,GAAG,MAAM;IAEtB,KAAAC,eAAe,GAAG;MAChBC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE;KACV;EAKE;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACb,WAAW,CAACc,YAAY,CAACC,SAAS,CAACC,OAAO,IAAG;MAChD,IAAI,CAACpB,WAAW,GAAGoB,OAAO;MAC1B,IAAI,CAACd,GAAG,GAAGc,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACF,OAAO,CAACG,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7D,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,OAAO,IAAI,CAACxB,WAAW,GAAG,IAAI,CAACA,WAAW,CAACuB,KAAK,GAAG,IAAI,CAACjB,GAAG,GAAG,CAAC;EACjE;EAEAmB,WAAWA,CAAA;IACT,OAAO,CAAC,EACN,IAAI,CAACjB,eAAe,CAACC,SAAS,IAC9B,IAAI,CAACD,eAAe,CAACE,QAAQ,IAC7B,IAAI,CAACF,eAAe,CAACG,KAAK,IAC1B,IAAI,CAACH,eAAe,CAACI,KAAK,IAC1B,IAAI,CAACJ,eAAe,CAACK,OAAO,IAC5B,IAAI,CAACL,eAAe,CAACM,IAAI,IACzB,IAAI,CAACN,eAAe,CAACO,KAAK,IAC1B,IAAI,CAACP,eAAe,CAACQ,OAAO,IAC5B,IAAI,CAACT,aAAa,CACnB;EACH;EAEMmB,UAAUA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACd,IAAID,KAAI,CAACF,WAAW,EAAE,EAAE;QACtB;QACAI,KAAK,CAAC,mCAAmC,CAAC;QAC1CF,KAAI,CAACvB,WAAW,CAAC0B,YAAY,EAAE,CAACX,SAAS,CAAC;UACxCY,IAAI,EAAEA,CAAA,KAAK;YACTJ,KAAI,CAACtB,MAAM,CAAC2B,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;UAC7B,CAAC;UACDC,KAAK,EAAGA,KAAK,IAAI;YACfC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;YAC5CN,KAAI,CAACtB,MAAM,CAAC2B,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;UAC7B;SACD,CAAC;;IACH;EACH;EAEAG,UAAUA,CAAA;IACR,IAAI,CAAC9B,MAAM,CAAC2B,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;EACtC;;;uBAhEW9B,iBAAiB,EAAAV,EAAA,CAAA4C,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9C,EAAA,CAAA4C,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAjBtC,iBAAiB;MAAAuC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAnD,EAAA,CAAAoD,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzVtB1D,EAFJ,CAAAC,cAAA,aAA2B,aACI,SACvB;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGbH,EAFJ,CAAAC,cAAA,aAAmB,aACQ,cACG;UAAAD,EAAA,CAAAE,MAAA,QAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClCH,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UACnCF,EADmC,CAAAG,YAAA,EAAO,EACpC;UAEJH,EADF,CAAAC,cAAA,cAAkB,eACU;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClCH,EAAA,CAAAC,cAAA,eAAyB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAClCF,EADkC,CAAAG,YAAA,EAAO,EACnC;UAEJH,EADF,CAAAC,cAAA,cAAkB,eACU;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClCH,EAAA,CAAAC,cAAA,eAAyB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAGrCF,EAHqC,CAAAG,YAAA,EAAO,EAClC,EACF,EACF;UAKAH,EAHN,CAAAC,cAAA,cAA8B,cACD,cACC,UACpB;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAInBH,EAHN,CAAAC,cAAA,YAAM,eACkB,eACI,aACf;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzBH,EAAA,CAAAC,cAAA,iBAAqF;UAAlED,EAAA,CAAA4D,gBAAA,2BAAAC,2DAAAC,MAAA;YAAA9D,EAAA,CAAA+D,kBAAA,CAAAJ,GAAA,CAAA3C,eAAA,CAAAC,SAAA,EAAA6C,MAAA,MAAAH,GAAA,CAAA3C,eAAA,CAAAC,SAAA,GAAA6C,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAuC;UAC5D9D,EADE,CAAAG,YAAA,EAAqF,EACjF;UAEJH,EADF,CAAAC,cAAA,eAAwB,aACf;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxBH,EAAA,CAAAC,cAAA,iBAAmF;UAAhED,EAAA,CAAA4D,gBAAA,2BAAAI,2DAAAF,MAAA;YAAA9D,EAAA,CAAA+D,kBAAA,CAAAJ,GAAA,CAAA3C,eAAA,CAAAE,QAAA,EAAA4C,MAAA,MAAAH,GAAA,CAAA3C,eAAA,CAAAE,QAAA,GAAA4C,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAsC;UAE7D9D,EAFI,CAAAG,YAAA,EAAmF,EAC/E,EACF;UAEJH,EADF,CAAAC,cAAA,eAAwB,aACf;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpBH,EAAA,CAAAC,cAAA,iBAA8E;UAA1DD,EAAA,CAAA4D,gBAAA,2BAAAK,2DAAAH,MAAA;YAAA9D,EAAA,CAAA+D,kBAAA,CAAAJ,GAAA,CAAA3C,eAAA,CAAAG,KAAA,EAAA2C,MAAA,MAAAH,GAAA,CAAA3C,eAAA,CAAAG,KAAA,GAAA2C,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAmC;UACzD9D,EADE,CAAAG,YAAA,EAA8E,EAC1E;UAEJH,EADF,CAAAC,cAAA,eAAwB,aACf;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpBH,EAAA,CAAAC,cAAA,iBAA4E;UAA1DD,EAAA,CAAA4D,gBAAA,2BAAAM,2DAAAJ,MAAA;YAAA9D,EAAA,CAAA+D,kBAAA,CAAAJ,GAAA,CAAA3C,eAAA,CAAAI,KAAA,EAAA0C,MAAA,MAAAH,GAAA,CAAA3C,eAAA,CAAAI,KAAA,GAAA0C,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAmC;UACvD9D,EADE,CAAAG,YAAA,EAA4E,EACxE;UAEJH,EADF,CAAAC,cAAA,eAAwB,aACf;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtBH,EAAA,CAAAC,cAAA,iBAAiF;UAA9DD,EAAA,CAAA4D,gBAAA,2BAAAO,2DAAAL,MAAA;YAAA9D,EAAA,CAAA+D,kBAAA,CAAAJ,GAAA,CAAA3C,eAAA,CAAAK,OAAA,EAAAyC,MAAA,MAAAH,GAAA,CAAA3C,eAAA,CAAAK,OAAA,GAAAyC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAqC;UAC1D9D,EADE,CAAAG,YAAA,EAAiF,EAC7E;UAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACI,aACf;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnBH,EAAA,CAAAC,cAAA,iBAA2E;UAAxDD,EAAA,CAAA4D,gBAAA,2BAAAQ,2DAAAN,MAAA;YAAA9D,EAAA,CAAA+D,kBAAA,CAAAJ,GAAA,CAAA3C,eAAA,CAAAM,IAAA,EAAAwC,MAAA,MAAAH,GAAA,CAAA3C,eAAA,CAAAM,IAAA,GAAAwC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAkC;UACvD9D,EADE,CAAAG,YAAA,EAA2E,EACvE;UAEJH,EADF,CAAAC,cAAA,eAAwB,aACf;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpBH,EAAA,CAAAC,cAAA,iBAA6E;UAA1DD,EAAA,CAAA4D,gBAAA,2BAAAS,2DAAAP,MAAA;YAAA9D,EAAA,CAAA+D,kBAAA,CAAAJ,GAAA,CAAA3C,eAAA,CAAAO,KAAA,EAAAuC,MAAA,MAAAH,GAAA,CAAA3C,eAAA,CAAAO,KAAA,GAAAuC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAmC;UACxD9D,EADE,CAAAG,YAAA,EAA6E,EACzE;UAEJH,EADF,CAAAC,cAAA,eAAwB,aACf;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvBH,EAAA,CAAAC,cAAA,iBAAiF;UAA9DD,EAAA,CAAA4D,gBAAA,2BAAAU,2DAAAR,MAAA;YAAA9D,EAAA,CAAA+D,kBAAA,CAAAJ,GAAA,CAAA3C,eAAA,CAAAQ,OAAA,EAAAsC,MAAA,MAAAH,GAAA,CAAA3C,eAAA,CAAAQ,OAAA,GAAAsC,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAqC;UAIhE9D,EAJQ,CAAAG,YAAA,EAAiF,EAC7E,EACF,EACD,EACH;UAGJH,EADF,CAAAC,cAAA,cAA0B,UACpB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGnBH,EAFJ,CAAAC,cAAA,eAA6B,iBACG,iBACsD;UAA5BD,EAAA,CAAA4D,gBAAA,2BAAAW,2DAAAT,MAAA;YAAA9D,EAAA,CAAA+D,kBAAA,CAAAJ,GAAA,CAAA5C,aAAA,EAAA+C,MAAA,MAAAH,GAAA,CAAA5C,aAAA,GAAA+C,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UAAjF9D,EAAA,CAAAG,YAAA,EAAkF;UAClFH,EAAA,CAAAC,cAAA,gBAA6B;UAC3BD,EAAA,CAAAwE,SAAA,aAAkC;UAClCxE,EAAA,CAAAE,MAAA,2BACF;UACFF,EADE,CAAAG,YAAA,EAAO,EACD;UAENH,EADF,CAAAC,cAAA,iBAA8B,iBACqD;UAA5BD,EAAA,CAAA4D,gBAAA,2BAAAa,2DAAAX,MAAA;YAAA9D,EAAA,CAAA+D,kBAAA,CAAAJ,GAAA,CAAA5C,aAAA,EAAA+C,MAAA,MAAAH,GAAA,CAAA5C,aAAA,GAAA+C,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UAAhF9D,EAAA,CAAAG,YAAA,EAAiF;UACjFH,EAAA,CAAAC,cAAA,gBAA6B;UAC3BD,EAAA,CAAAwE,SAAA,aAAiC;UACjCxE,EAAA,CAAAE,MAAA,aACF;UACFF,EADE,CAAAG,YAAA,EAAO,EACD;UAENH,EADF,CAAAC,cAAA,iBAA8B,iBACqD;UAA5BD,EAAA,CAAA4D,gBAAA,2BAAAc,2DAAAZ,MAAA;YAAA9D,EAAA,CAAA+D,kBAAA,CAAAJ,GAAA,CAAA5C,aAAA,EAAA+C,MAAA,MAAAH,GAAA,CAAA5C,aAAA,GAAA+C,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA2B;UAAhF9D,EAAA,CAAAG,YAAA,EAAiF;UACjFH,EAAA,CAAAC,cAAA,gBAA6B;UAC3BD,EAAA,CAAAwE,SAAA,aAAsC;UACtCxE,EAAA,CAAAE,MAAA,0BACF;UAIRF,EAJQ,CAAAG,YAAA,EAAO,EACD,EACJ,EACF,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAA2B,eACC,UACpB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEpBH,EADF,CAAAC,cAAA,eAAyB,YACjB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACrBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAAqC;;UAC7CF,EAD6C,CAAAG,YAAA,EAAO,EAC9C;UACNH,EAAA,CAAA2E,UAAA,KAAAC,iCAAA,kBAAiG;UAK/F5E,EADF,CAAAC,cAAA,eAAyB,YACjB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACrBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,YAAI;UACZF,EADY,CAAAG,YAAA,EAAO,EACb;UAEJH,EADF,CAAAC,cAAA,eAAyB,YACjB;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAAmB;;UAC3BF,EAD2B,CAAAG,YAAA,EAAO,EAC5B;UACNH,EAAA,CAAAwE,SAAA,WAAI;UAEFxE,EADF,CAAAC,cAAA,gBAA+B,aACvB;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,KAA0B;;UAClCF,EADkC,CAAAG,YAAA,EAAO,EACnC;UACNH,EAAA,CAAAC,cAAA,mBAAmF;UAAnDD,EAAA,CAAA6E,UAAA,mBAAAC,qDAAA;YAAA,OAASnB,GAAA,CAAAzB,UAAA,EAAY;UAAA,EAAC;UACpDlC,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,mBAAwD;UAAvBD,EAAA,CAAA6E,UAAA,mBAAAE,qDAAA;YAAA,OAASpB,GAAA,CAAAhB,UAAA,EAAY;UAAA,EAAC;UACrD3C,EAAA,CAAAE,MAAA,uBACF;UAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;;;UAjG2BH,EAAA,CAAAI,SAAA,IAAuC;UAAvCJ,EAAA,CAAAgF,gBAAA,YAAArB,GAAA,CAAA3C,eAAA,CAAAC,SAAA,CAAuC;UAIvCjB,EAAA,CAAAI,SAAA,GAAsC;UAAtCJ,EAAA,CAAAgF,gBAAA,YAAArB,GAAA,CAAA3C,eAAA,CAAAE,QAAA,CAAsC;UAKvClB,EAAA,CAAAI,SAAA,GAAmC;UAAnCJ,EAAA,CAAAgF,gBAAA,YAAArB,GAAA,CAAA3C,eAAA,CAAAG,KAAA,CAAmC;UAIrCnB,EAAA,CAAAI,SAAA,GAAmC;UAAnCJ,EAAA,CAAAgF,gBAAA,YAAArB,GAAA,CAAA3C,eAAA,CAAAI,KAAA,CAAmC;UAIlCpB,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAgF,gBAAA,YAAArB,GAAA,CAAA3C,eAAA,CAAAK,OAAA,CAAqC;UAKnCrB,EAAA,CAAAI,SAAA,GAAkC;UAAlCJ,EAAA,CAAAgF,gBAAA,YAAArB,GAAA,CAAA3C,eAAA,CAAAM,IAAA,CAAkC;UAIlCtB,EAAA,CAAAI,SAAA,GAAmC;UAAnCJ,EAAA,CAAAgF,gBAAA,YAAArB,GAAA,CAAA3C,eAAA,CAAAO,KAAA,CAAmC;UAInCvB,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAgF,gBAAA,YAAArB,GAAA,CAAA3C,eAAA,CAAAQ,OAAA,CAAqC;UAUJxB,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAAgF,gBAAA,YAAArB,GAAA,CAAA5C,aAAA,CAA2B;UAO5Bf,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAAgF,gBAAA,YAAArB,GAAA,CAAA5C,aAAA,CAA2B;UAO3Bf,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAAgF,gBAAA,YAAArB,GAAA,CAAA5C,aAAA,CAA2B;UAe5Ef,EAAA,CAAAI,SAAA,IAAqC;UAArCJ,EAAA,CAAAK,kBAAA,WAAAL,EAAA,CAAAM,WAAA,SAAAqD,GAAA,CAAAnD,WAAA,kBAAAmD,GAAA,CAAAnD,WAAA,CAAAyE,QAAA,MAAqC;UAEnBjF,EAAA,CAAAI,SAAA,GAAqE;UAArEJ,EAAA,CAAAkF,UAAA,SAAAvB,GAAA,CAAAnD,WAAA,IAAAmD,GAAA,CAAAnD,WAAA,CAAAC,QAAA,IAAAkD,GAAA,CAAAnD,WAAA,CAAAC,QAAA,KAAqE;UAUvFT,EAAA,CAAAI,SAAA,IAAmB;UAAnBJ,EAAA,CAAAK,kBAAA,WAAAL,EAAA,CAAAM,WAAA,UAAAqD,GAAA,CAAA7C,GAAA,MAAmB;UAKnBd,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAK,kBAAA,WAAAL,EAAA,CAAAM,WAAA,UAAAqD,GAAA,CAAA3B,QAAA,QAA0B;UAEqBhC,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAAkF,UAAA,cAAAvB,GAAA,CAAA1B,WAAA,GAA2B;;;qBArHlFnC,YAAY,EAAAqF,EAAA,CAAAC,IAAA,EAAAD,EAAA,CAAAE,WAAA,EAAEtF,WAAW,EAAAuF,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,yBAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,oBAAA,EAAAL,EAAA,CAAAM,iBAAA,EAAAN,EAAA,CAAAO,OAAA,EAAAP,EAAA,CAAAQ,MAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}