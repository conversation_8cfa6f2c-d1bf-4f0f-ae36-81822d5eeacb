{"ast": null, "code": "export function applyPolyfills() {\n  var promises = [];\n  if (typeof window !== 'undefined') {\n    var win = window;\n    if (!win.customElements || win.Element && (!win.Element.prototype.closest || !win.Element.prototype.matches || !win.Element.prototype.remove || !win.Element.prototype.getRootNode)) {\n      promises.push(import(/* webpackChunkName: \"polyfills-dom\" */'./dom.js'));\n    }\n    var checkIfURLIsSupported = function () {\n      try {\n        var u = new URL('b', 'http://a');\n        u.pathname = 'c%20d';\n        return u.href === 'http://a/c%20d' && u.searchParams;\n      } catch (e) {\n        return false;\n      }\n    };\n    if ('function' !== typeof Object.assign || !Object.entries || !Array.prototype.find || !Array.prototype.includes || !String.prototype.startsWith || !String.prototype.endsWith || win.NodeList && !win.NodeList.prototype.forEach || !win.fetch || !checkIfURLIsSupported() || typeof WeakMap == 'undefined') {\n      promises.push(import(/* webpackChunkName: \"polyfills-core-js\" */'./core-js.js'));\n    }\n  }\n  return Promise.all(promises);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}