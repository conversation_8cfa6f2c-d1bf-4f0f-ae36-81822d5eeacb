{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/mobile-optimization.service\";\nimport * as i2 from \"../../../core/services/auth.service\";\nimport * as i3 from \"../../../core/services/cart.service\";\nimport * as i4 from \"../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@angular/forms\";\nconst _c0 = [\"*\"];\nfunction MobileLayoutComponent_header_1_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 51);\n  }\n}\nfunction MobileLayoutComponent_header_1_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 50);\n  }\n}\nfunction MobileLayoutComponent_header_1_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.cartCount));\n  }\n}\nfunction MobileLayoutComponent_header_1_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.wishlistCount));\n  }\n}\nfunction MobileLayoutComponent_header_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"header\", 29)(1, \"div\", 30)(2, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_header_1_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleMenu());\n    });\n    i0.ɵɵtemplate(3, MobileLayoutComponent_header_1_i_3_Template, 1, 0, \"i\", 32)(4, MobileLayoutComponent_header_1_i_4_Template, 1, 0, \"i\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 34);\n    i0.ɵɵelement(6, \"img\", 35);\n    i0.ɵɵelementStart(7, \"span\", 36);\n    i0.ɵɵtext(8, \"DFashion\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 37)(10, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_header_1_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSearch());\n    });\n    i0.ɵɵelement(11, \"i\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 40);\n    i0.ɵɵelement(13, \"i\", 41);\n    i0.ɵɵtemplate(14, MobileLayoutComponent_header_1_span_14_Template, 2, 1, \"span\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 43);\n    i0.ɵɵelement(16, \"i\", 44);\n    i0.ɵɵtemplate(17, MobileLayoutComponent_header_1_span_17_Template, 2, 1, \"span\", 42);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 45)(19, \"div\", 46)(20, \"input\", 47);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function MobileLayoutComponent_header_1_Template_input_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.searchQuery, $event) || (ctx_r1.searchQuery = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function MobileLayoutComponent_header_1_Template_input_keyup_enter_20_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearchSubmit());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_header_1_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSearchSubmit());\n    });\n    i0.ɵɵelement(22, \"i\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_header_1_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSearch());\n    });\n    i0.ɵɵelement(24, \"i\", 50);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isMenuOpen);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isMenuOpen);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cartCount > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.wishlistCount > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSearchOpen);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.searchQuery);\n  }\n}\nfunction MobileLayoutComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 54);\n    i0.ɵɵelement(2, \"img\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 56)(4, \"h3\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.currentUser.avatar || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.currentUser.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.currentUser.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.currentUser.email);\n  }\n}\nfunction MobileLayoutComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"div\", 58);\n    i0.ɵɵelement(2, \"i\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 60)(4, \"h3\");\n    i0.ɵɵtext(5, \"Welcome to DFashion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Sign in for personalized experience\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 61)(9, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_div_6_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeMenu());\n    });\n    i0.ɵɵtext(10, \"Sign In\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_div_6_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeMenu());\n    });\n    i0.ɵɵtext(12, \"Sign Up\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MobileLayoutComponent_div_28_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 76);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.wishlistCount));\n  }\n}\nfunction MobileLayoutComponent_div_28_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 76);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.cartCount));\n  }\n}\nfunction MobileLayoutComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵelement(1, \"div\", 65);\n    i0.ɵɵelementStart(2, \"a\", 66);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_div_28_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeMenu());\n    });\n    i0.ɵɵelement(3, \"i\", 59);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"My Profile\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"a\", 67);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_div_28_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeMenu());\n    });\n    i0.ɵɵelement(7, \"i\", 68);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9, \"My Orders\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"a\", 69);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_div_28_Template_a_click_10_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeMenu());\n    });\n    i0.ɵɵelement(11, \"i\", 44);\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13, \"Wishlist\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, MobileLayoutComponent_div_28_span_14_Template, 2, 1, \"span\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"a\", 71);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_div_28_Template_a_click_15_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeMenu());\n    });\n    i0.ɵɵelement(16, \"i\", 41);\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18, \"Cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, MobileLayoutComponent_div_28_span_19_Template, 2, 1, \"span\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"a\", 72);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_div_28_Template_a_click_20_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeMenu());\n    });\n    i0.ɵɵelement(21, \"i\", 73);\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23, \"Settings\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(24, \"div\", 65);\n    i0.ɵɵelementStart(25, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function MobileLayoutComponent_div_28_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.logout());\n    });\n    i0.ɵɵelement(26, \"i\", 75);\n    i0.ɵɵelementStart(27, \"span\");\n    i0.ɵɵtext(28, \"Logout\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.wishlistCount > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cartCount > 0);\n  }\n}\nfunction MobileLayoutComponent_nav_44_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 84);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.wishlistCount));\n  }\n}\nfunction MobileLayoutComponent_nav_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nav\", 77)(1, \"a\", 78);\n    i0.ɵɵelement(2, \"i\", 9);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Home\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"a\", 79);\n    i0.ɵɵelement(6, \"i\", 11);\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Shop\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"a\", 80);\n    i0.ɵɵelement(10, \"i\", 39);\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"Search\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"a\", 81);\n    i0.ɵɵelement(14, \"i\", 44);\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16, \"Wishlist\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, MobileLayoutComponent_nav_44_span_17_Template, 2, 1, \"span\", 82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"a\", 83);\n    i0.ɵɵelement(19, \"i\", 59);\n    i0.ɵɵelementStart(20, \"span\");\n    i0.ɵɵtext(21, \"Profile\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isCurrentRoute(\"/\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"active\", ctx_r1.isCurrentRoute(\"/categories\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"active\", ctx_r1.isCurrentRoute(\"/search\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"active\", ctx_r1.isCurrentRoute(\"/wishlist\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.wishlistCount > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isCurrentRoute(\"/profile\"));\n  }\n}\nfunction MobileLayoutComponent_footer_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"footer\", 85)(1, \"div\", 86)(2, \"div\", 87)(3, \"a\", 88);\n    i0.ɵɵtext(4, \"About\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"a\", 89);\n    i0.ɵɵtext(6, \"Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"a\", 90);\n    i0.ɵɵtext(8, \"Privacy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"a\", 91);\n    i0.ɵɵtext(10, \"Terms\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 92)(12, \"p\");\n    i0.ɵɵtext(13, \"\\u00A9 2024 DFashion. All rights reserved.\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nexport class MobileLayoutComponent {\n  constructor(mobileService, authService, cartService, wishlistService) {\n    this.mobileService = mobileService;\n    this.authService = authService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.showHeader = true;\n    this.showFooter = true;\n    this.showBottomNav = true;\n    this.menuToggle = new EventEmitter();\n    this.deviceInfo = null;\n    this.breakpoints = null;\n    this.isKeyboardOpen = false;\n    this.currentUser = null;\n    this.cartCount = 0;\n    this.wishlistCount = 0;\n    this.isMenuOpen = false;\n    this.isSearchOpen = false;\n    this.searchQuery = '';\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    // Subscribe to device info\n    this.subscriptions.push(this.mobileService.getDeviceInfo$().subscribe(info => {\n      this.deviceInfo = info;\n      this.updateLayoutForDevice();\n    }));\n    // Subscribe to viewport breakpoints\n    this.subscriptions.push(this.mobileService.getViewportBreakpoints$().subscribe(breakpoints => {\n      this.breakpoints = breakpoints;\n      this.updateLayoutForBreakpoint();\n    }));\n    // Subscribe to keyboard state\n    this.subscriptions.push(this.mobileService.getIsKeyboardOpen$().subscribe(isOpen => {\n      this.isKeyboardOpen = isOpen;\n      this.handleKeyboardState(isOpen);\n    }));\n    // Subscribe to auth state\n    this.subscriptions.push(this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      if (user) {\n        this.loadUserCounts();\n      } else {\n        this.cartCount = 0;\n        this.wishlistCount = 0;\n      }\n    }));\n    // Load initial counts\n    this.loadUserCounts();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  updateLayoutForDevice() {\n    if (!this.deviceInfo) return;\n    // Apply device-specific optimizations\n    if (this.deviceInfo.isMobile) {\n      this.enableMobileOptimizations();\n    } else {\n      this.disableMobileOptimizations();\n    }\n    // Handle orientation changes\n    if (this.deviceInfo.orientation === 'landscape' && this.deviceInfo.isMobile) {\n      this.handleLandscapeMode();\n    } else {\n      this.handlePortraitMode();\n    }\n  }\n  updateLayoutForBreakpoint() {\n    if (!this.breakpoints) return;\n    // Adjust layout based on breakpoints\n    if (this.breakpoints.xs || this.breakpoints.sm) {\n      this.showBottomNav = true;\n      this.enableCompactMode();\n    } else {\n      this.showBottomNav = false;\n      this.disableCompactMode();\n    }\n  }\n  handleKeyboardState(isOpen) {\n    if (isOpen) {\n      // Hide bottom navigation when keyboard is open\n      document.body.classList.add('keyboard-open');\n    } else {\n      document.body.classList.remove('keyboard-open');\n    }\n  }\n  enableMobileOptimizations() {\n    // Enable touch-friendly interactions\n    document.body.classList.add('mobile-device');\n    // Disable hover effects on mobile\n    if (!this.mobileService.supportsHover()) {\n      document.body.classList.add('no-hover');\n    }\n    // Enable GPU acceleration for smooth scrolling\n    const scrollElements = document.querySelectorAll('.scroll-container');\n    scrollElements.forEach(element => {\n      this.mobileService.enableGPUAcceleration(element);\n    });\n  }\n  disableMobileOptimizations() {\n    document.body.classList.remove('mobile-device', 'no-hover');\n  }\n  handleLandscapeMode() {\n    document.body.classList.add('landscape-mode');\n  }\n  handlePortraitMode() {\n    document.body.classList.remove('landscape-mode');\n  }\n  enableCompactMode() {\n    document.body.classList.add('compact-mode');\n  }\n  disableCompactMode() {\n    document.body.classList.remove('compact-mode');\n  }\n  loadUserCounts() {\n    if (!this.currentUser) return;\n    // Set default counts for now\n    this.cartCount = 0;\n    this.wishlistCount = 0;\n  }\n  // Menu Methods\n  toggleMenu() {\n    this.isMenuOpen = !this.isMenuOpen;\n    this.menuToggle.emit(this.isMenuOpen);\n    if (this.isMenuOpen) {\n      this.mobileService.disableBodyScroll();\n    } else {\n      this.mobileService.enableBodyScroll();\n    }\n  }\n  closeMenu() {\n    this.isMenuOpen = false;\n    this.menuToggle.emit(false);\n    this.mobileService.enableBodyScroll();\n  }\n  // Search Methods\n  toggleSearch() {\n    this.isSearchOpen = !this.isSearchOpen;\n    if (this.isSearchOpen) {\n      setTimeout(() => {\n        const searchInput = document.querySelector('.mobile-search-input');\n        if (searchInput) {\n          searchInput.focus();\n        }\n      }, 100);\n    }\n  }\n  onSearchSubmit() {\n    if (this.searchQuery.trim()) {\n      // Navigate to search results\n      console.log('Searching for:', this.searchQuery);\n      this.isSearchOpen = false;\n      this.searchQuery = '';\n    }\n  }\n  // Navigation Methods\n  navigateToProfile() {\n    this.closeMenu();\n    // Navigation logic\n  }\n  navigateToOrders() {\n    this.closeMenu();\n    // Navigation logic\n  }\n  navigateToSettings() {\n    this.closeMenu();\n    // Navigation logic\n  }\n  logout() {\n    // Simple logout without subscription\n    this.closeMenu();\n  }\n  // Utility Methods\n  getTotalCount() {\n    return this.cartCount + this.wishlistCount;\n  }\n  formatCount(count) {\n    if (count > 99) return '99+';\n    return count.toString();\n  }\n  isCurrentRoute(route) {\n    return window.location.pathname === route;\n  }\n  // Touch Event Handlers\n  onTouchStart(event) {\n    // Handle touch start for custom gestures\n  }\n  onTouchMove(event) {\n    // Handle touch move for custom gestures\n  }\n  onTouchEnd(event) {\n    // Handle touch end for custom gestures\n  }\n  // Performance Optimization\n  trackByIndex(index) {\n    return index;\n  }\n  static {\n    this.ɵfac = function MobileLayoutComponent_Factory(t) {\n      return new (t || MobileLayoutComponent)(i0.ɵɵdirectiveInject(i1.MobileOptimizationService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MobileLayoutComponent,\n      selectors: [[\"app-mobile-layout\"]],\n      inputs: {\n        showHeader: \"showHeader\",\n        showFooter: \"showFooter\",\n        showBottomNav: \"showBottomNav\"\n      },\n      outputs: {\n        menuToggle: \"menuToggle\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 46,\n      vars: 24,\n      consts: [[1, \"mobile-layout\"], [\"class\", \"mobile-header\", 4, \"ngIf\"], [1, \"mobile-menu\"], [1, \"menu-overlay\", 3, \"click\"], [1, \"menu-content\"], [\"class\", \"menu-profile\", 4, \"ngIf\"], [\"class\", \"menu-guest\", 4, \"ngIf\"], [1, \"menu-nav\"], [\"routerLink\", \"/\", 1, \"menu-item\", 3, \"click\"], [1, \"fas\", \"fa-home\"], [\"routerLink\", \"/categories\", 1, \"menu-item\", 3, \"click\"], [1, \"fas\", \"fa-th-large\"], [\"routerLink\", \"/trending\", 1, \"menu-item\", 3, \"click\"], [1, \"fas\", \"fa-fire\"], [\"routerLink\", \"/brands\", 1, \"menu-item\", 3, \"click\"], [1, \"fas\", \"fa-tags\"], [\"routerLink\", \"/offers\", 1, \"menu-item\", 3, \"click\"], [1, \"fas\", \"fa-percent\"], [\"class\", \"menu-section\", 4, \"ngIf\"], [1, \"menu-footer\"], [1, \"app-info\"], [1, \"social-links\"], [\"href\", \"#\", 1, \"social-link\"], [1, \"fab\", \"fa-instagram\"], [1, \"fab\", \"fa-facebook\"], [1, \"fab\", \"fa-twitter\"], [1, \"mobile-main\"], [\"class\", \"mobile-bottom-nav\", 4, \"ngIf\"], [\"class\", \"mobile-footer\", 4, \"ngIf\"], [1, \"mobile-header\"], [1, \"header-content\"], [1, \"header-btn\", \"menu-btn\", 3, \"click\"], [\"class\", \"fas fa-bars\", 4, \"ngIf\"], [\"class\", \"fas fa-times\", 4, \"ngIf\"], [\"routerLink\", \"/\", 1, \"header-logo\"], [\"src\", \"assets/images/logo.png\", \"alt\", \"DFashion\", 1, \"logo-image\"], [1, \"logo-text\"], [1, \"header-actions\"], [1, \"header-btn\", \"search-btn\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [\"routerLink\", \"/cart\", 1, \"header-btn\", \"cart-btn\"], [1, \"fas\", \"fa-shopping-cart\"], [\"class\", \"badge\", 4, \"ngIf\"], [\"routerLink\", \"/wishlist\", 1, \"header-btn\", \"wishlist-btn\"], [1, \"fas\", \"fa-heart\"], [1, \"mobile-search\"], [1, \"search-container\"], [\"type\", \"text\", \"placeholder\", \"Search for products, brands...\", 1, \"mobile-search-input\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"search-submit-btn\", 3, \"click\"], [1, \"search-close-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"fas\", \"fa-bars\"], [1, \"badge\"], [1, \"menu-profile\"], [1, \"profile-avatar\"], [3, \"src\", \"alt\"], [1, \"profile-info\"], [1, \"menu-guest\"], [1, \"guest-avatar\"], [1, \"fas\", \"fa-user\"], [1, \"guest-info\"], [1, \"guest-actions\"], [\"routerLink\", \"/login\", 1, \"btn-primary\", 3, \"click\"], [\"routerLink\", \"/register\", 1, \"btn-secondary\", 3, \"click\"], [1, \"menu-section\"], [1, \"menu-divider\"], [\"routerLink\", \"/profile\", 1, \"menu-item\", 3, \"click\"], [\"routerLink\", \"/orders\", 1, \"menu-item\", 3, \"click\"], [1, \"fas\", \"fa-box\"], [\"routerLink\", \"/wishlist\", 1, \"menu-item\", 3, \"click\"], [\"class\", \"menu-badge\", 4, \"ngIf\"], [\"routerLink\", \"/cart\", 1, \"menu-item\", 3, \"click\"], [\"routerLink\", \"/settings\", 1, \"menu-item\", 3, \"click\"], [1, \"fas\", \"fa-cog\"], [1, \"menu-item\", \"logout-btn\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\"], [1, \"menu-badge\"], [1, \"mobile-bottom-nav\"], [\"routerLink\", \"/\", 1, \"nav-item\"], [\"routerLink\", \"/categories\", 1, \"nav-item\"], [\"routerLink\", \"/search\", 1, \"nav-item\", \"search-nav\"], [\"routerLink\", \"/wishlist\", 1, \"nav-item\"], [\"class\", \"nav-badge\", 4, \"ngIf\"], [\"routerLink\", \"/profile\", 1, \"nav-item\"], [1, \"nav-badge\"], [1, \"mobile-footer\"], [1, \"footer-content\"], [1, \"footer-links\"], [\"href\", \"/about\"], [\"href\", \"/contact\"], [\"href\", \"/privacy\"], [\"href\", \"/terms\"], [1, \"footer-copyright\"]],\n      template: function MobileLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, MobileLayoutComponent_header_1_Template, 25, 7, \"header\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_div_click_3_listener() {\n            return ctx.closeMenu();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵtemplate(5, MobileLayoutComponent_div_5_Template, 8, 4, \"div\", 5)(6, MobileLayoutComponent_div_6_Template, 13, 0, \"div\", 6);\n          i0.ɵɵelementStart(7, \"nav\", 7)(8, \"a\", 8);\n          i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_a_click_8_listener() {\n            return ctx.closeMenu();\n          });\n          i0.ɵɵelement(9, \"i\", 9);\n          i0.ɵɵelementStart(10, \"span\");\n          i0.ɵɵtext(11, \"Home\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"a\", 10);\n          i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_a_click_12_listener() {\n            return ctx.closeMenu();\n          });\n          i0.ɵɵelement(13, \"i\", 11);\n          i0.ɵɵelementStart(14, \"span\");\n          i0.ɵɵtext(15, \"Categories\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"a\", 12);\n          i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_a_click_16_listener() {\n            return ctx.closeMenu();\n          });\n          i0.ɵɵelement(17, \"i\", 13);\n          i0.ɵɵelementStart(18, \"span\");\n          i0.ɵɵtext(19, \"Trending\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"a\", 14);\n          i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_a_click_20_listener() {\n            return ctx.closeMenu();\n          });\n          i0.ɵɵelement(21, \"i\", 15);\n          i0.ɵɵelementStart(22, \"span\");\n          i0.ɵɵtext(23, \"Brands\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"a\", 16);\n          i0.ɵɵlistener(\"click\", function MobileLayoutComponent_Template_a_click_24_listener() {\n            return ctx.closeMenu();\n          });\n          i0.ɵɵelement(25, \"i\", 17);\n          i0.ɵɵelementStart(26, \"span\");\n          i0.ɵɵtext(27, \"Offers\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(28, MobileLayoutComponent_div_28_Template, 29, 2, \"div\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 19)(30, \"div\", 20)(31, \"p\");\n          i0.ɵɵtext(32, \"DFashion v1.0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"p\");\n          i0.ɵɵtext(34, \"\\u00A9 2024 All rights reserved\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"div\", 21)(36, \"a\", 22);\n          i0.ɵɵelement(37, \"i\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"a\", 22);\n          i0.ɵɵelement(39, \"i\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"a\", 22);\n          i0.ɵɵelement(41, \"i\", 25);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(42, \"main\", 26);\n          i0.ɵɵprojection(43);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(44, MobileLayoutComponent_nav_44_Template, 22, 11, \"nav\", 27)(45, MobileLayoutComponent_footer_45_Template, 14, 0, \"footer\", 28);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"menu-open\", ctx.isMenuOpen)(\"search-open\", ctx.isSearchOpen)(\"keyboard-open\", ctx.isKeyboardOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showHeader);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.isMenuOpen);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.currentUser);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.isCurrentRoute(\"/\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"active\", ctx.isCurrentRoute(\"/categories\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"active\", ctx.isCurrentRoute(\"/trending\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"active\", ctx.isCurrentRoute(\"/brands\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"active\", ctx.isCurrentRoute(\"/offers\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngIf\", ctx.showBottomNav && !ctx.isKeyboardOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showFooter && !ctx.showBottomNav);\n        }\n      },\n      dependencies: [CommonModule, i5.NgIf, RouterModule, i6.RouterLink, FormsModule, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel],\n      styles: [\".mobile-layout[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 100vh;\\n  position: relative;\\n  overflow-x: hidden;\\n}\\n\\n.mobile-header[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  background: white;\\n  border-bottom: 1px solid #e0e0e0;\\n  z-index: 1000;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n  padding-top: env(safe-area-inset-top);\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 12px 16px;\\n  height: 56px;\\n}\\n\\n.header-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: none;\\n  background: none;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.header-btn[_ngcontent-%COMP%]:hover {\\n  background: #f0f0f0;\\n}\\n.header-btn[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n.header-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  color: #333;\\n}\\n\\n.header-logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  text-decoration: none;\\n}\\n.header-logo[_ngcontent-%COMP%]   .logo-image[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 6px;\\n}\\n.header-logo[_ngcontent-%COMP%]   .logo-text[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 700;\\n  color: #333;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -2px;\\n  right: -2px;\\n  background: #ff6b6b;\\n  color: white;\\n  font-size: 10px;\\n  font-weight: 600;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  min-width: 16px;\\n  text-align: center;\\n  line-height: 1.2;\\n}\\n\\n.mobile-search[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  left: 0;\\n  right: 0;\\n  background: white;\\n  border-bottom: 1px solid #e0e0e0;\\n  transform: translateY(-100%);\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: all 0.3s ease;\\n}\\n.mobile-search.active[_ngcontent-%COMP%] {\\n  transform: translateY(0);\\n  opacity: 1;\\n  visibility: visible;\\n}\\n\\n.search-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 12px 16px;\\n  gap: 8px;\\n}\\n\\n.mobile-search-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 12px 16px;\\n  border: 1px solid #ddd;\\n  border-radius: 25px;\\n  font-size: 16px;\\n  outline: none;\\n}\\n.mobile-search-input[_ngcontent-%COMP%]:focus {\\n  border-color: #667eea;\\n}\\n\\n.search-submit-btn[_ngcontent-%COMP%], .search-close-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: none;\\n  background: #667eea;\\n  color: white;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.search-submit-btn[_ngcontent-%COMP%]:active, .search-close-btn[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n\\n.search-close-btn[_ngcontent-%COMP%] {\\n  background: #f0f0f0;\\n  color: #666;\\n}\\n\\n.mobile-menu[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  z-index: 2000;\\n  visibility: hidden;\\n}\\n.mobile-menu.active[_ngcontent-%COMP%] {\\n  visibility: visible;\\n}\\n.mobile-menu.active[_ngcontent-%COMP%]   .menu-overlay[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.mobile-menu.active[_ngcontent-%COMP%]   .menu-content[_ngcontent-%COMP%] {\\n  transform: translateX(0);\\n}\\n\\n.menu-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0, 0, 0, 0.5);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.menu-content[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  bottom: 0;\\n  width: 280px;\\n  background: white;\\n  transform: translateX(-100%);\\n  transition: transform 0.3s ease;\\n  overflow-y: auto;\\n  display: flex;\\n  flex-direction: column;\\n  padding-top: env(safe-area-inset-top);\\n  padding-bottom: env(safe-area-inset-bottom);\\n}\\n\\n.menu-profile[_ngcontent-%COMP%] {\\n  padding: 24px 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n.profile-avatar[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  border: 3px solid rgba(255, 255, 255, 0.3);\\n}\\n.profile-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.profile-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.profile-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 4px 0;\\n}\\n.profile-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  opacity: 0.8;\\n  margin: 0;\\n}\\n\\n.menu-guest[_ngcontent-%COMP%] {\\n  padding: 24px 20px;\\n  background: #f8f9fa;\\n  text-align: center;\\n}\\n\\n.guest-avatar[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  background: #ddd;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 auto 16px;\\n}\\n.guest-avatar[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #666;\\n}\\n\\n.guest-info[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.guest-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: #333;\\n  margin: 0 0 8px 0;\\n}\\n.guest-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  margin: 0;\\n}\\n\\n.guest-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.guest-actions[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%], .guest-actions[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 10px 16px;\\n  border-radius: 20px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  text-decoration: none;\\n  text-align: center;\\n}\\n.guest-actions[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\\n  background: #667eea;\\n  color: white;\\n  border: none;\\n}\\n.guest-actions[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover {\\n  background: #5a6fd8;\\n}\\n.guest-actions[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\\n  background: white;\\n  color: #667eea;\\n  border: 1px solid #667eea;\\n}\\n.guest-actions[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\\n  background: #f0f2ff;\\n}\\n\\n.menu-nav[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 8px 0;\\n}\\n\\n.menu-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  padding: 16px 20px;\\n  color: #333;\\n  text-decoration: none;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  border: none;\\n  background: none;\\n  width: 100%;\\n  cursor: pointer;\\n}\\n.menu-item[_ngcontent-%COMP%]:hover {\\n  background: #f8f9fa;\\n}\\n.menu-item.active[_ngcontent-%COMP%] {\\n  background: #e7f3ff;\\n  color: #667eea;\\n}\\n.menu-item.active[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  left: 0;\\n  top: 0;\\n  bottom: 0;\\n  width: 4px;\\n  background: #667eea;\\n}\\n.menu-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 20px;\\n  text-align: center;\\n}\\n.menu-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n}\\n.menu-item.logout-btn[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.menu-item.logout-btn[_ngcontent-%COMP%]:hover {\\n  background: #fff5f5;\\n}\\n\\n.menu-badge[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  background: #ff6b6b;\\n  color: white;\\n  font-size: 10px;\\n  font-weight: 600;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  min-width: 16px;\\n  text-align: center;\\n}\\n\\n.menu-section[_ngcontent-%COMP%]   .menu-divider[_ngcontent-%COMP%] {\\n  height: 1px;\\n  background: #e0e0e0;\\n  margin: 8px 20px;\\n}\\n\\n.menu-footer[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  border-top: 1px solid #e0e0e0;\\n  background: #f8f9fa;\\n}\\n\\n.app-info[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 16px;\\n}\\n.app-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  margin: 4px 0;\\n}\\n\\n.social-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 16px;\\n}\\n\\n.social-link[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  background: #e0e0e0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #666;\\n  text-decoration: none;\\n  transition: all 0.3s ease;\\n}\\n.social-link[_ngcontent-%COMP%]:hover {\\n  background: #667eea;\\n  color: white;\\n}\\n.social-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.mobile-main[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding-top: calc(56px + env(safe-area-inset-top));\\n  padding-bottom: 80px;\\n}\\n.keyboard-open[_ngcontent-%COMP%]   .mobile-main[_ngcontent-%COMP%] {\\n  padding-bottom: 0;\\n}\\n\\n.mobile-bottom-nav[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  background: white;\\n  border-top: 1px solid #e0e0e0;\\n  display: flex;\\n  z-index: 1000;\\n  padding-bottom: env(safe-area-inset-bottom);\\n}\\n\\n.nav-item[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 8px 4px;\\n  color: #666;\\n  text-decoration: none;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  min-height: 60px;\\n}\\n.nav-item[_ngcontent-%COMP%]:hover {\\n  background: #f8f9fa;\\n}\\n.nav-item.active[_ngcontent-%COMP%] {\\n  color: #667eea;\\n}\\n.nav-item.active[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.nav-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  margin-bottom: 4px;\\n  transition: transform 0.3s ease;\\n}\\n.nav-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  font-weight: 600;\\n  text-align: center;\\n}\\n\\n.nav-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 4px;\\n  right: 50%;\\n  transform: translateX(50%);\\n  background: #ff6b6b;\\n  color: white;\\n  font-size: 8px;\\n  font-weight: 600;\\n  padding: 2px 4px;\\n  border-radius: 8px;\\n  min-width: 14px;\\n  text-align: center;\\n  line-height: 1;\\n}\\n\\n.mobile-footer[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border-top: 1px solid #e0e0e0;\\n  padding: 20px 16px;\\n  padding-bottom: calc(20px + env(safe-area-inset-bottom));\\n}\\n\\n.footer-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.footer-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 20px;\\n  margin-bottom: 16px;\\n}\\n.footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #666;\\n  text-decoration: none;\\n  font-size: 14px;\\n}\\n.footer-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #667eea;\\n}\\n\\n.footer-copyright[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #999;\\n  margin: 0;\\n}\\n\\n@media (max-width: 480px) {\\n  .header-content[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n  }\\n  .menu-content[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-width: 320px;\\n  }\\n  .nav-item[_ngcontent-%COMP%] {\\n    padding: 6px 2px;\\n    min-height: 56px;\\n  }\\n  .nav-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .nav-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 9px;\\n  }\\n}\\n@media (orientation: landscape) and (max-height: 500px) {\\n  .mobile-main[_ngcontent-%COMP%] {\\n    padding-top: 48px;\\n    padding-bottom: 0;\\n  }\\n  .mobile-bottom-nav[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .menu-profile[_ngcontent-%COMP%], .menu-guest[_ngcontent-%COMP%] {\\n    padding: 16px 20px;\\n  }\\n  .profile-avatar[_ngcontent-%COMP%], .guest-avatar[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .mobile-header[_ngcontent-%COMP%], .mobile-search[_ngcontent-%COMP%], .menu-content[_ngcontent-%COMP%], .mobile-bottom-nav[_ngcontent-%COMP%], .mobile-footer[_ngcontent-%COMP%] {\\n    background: #1a1a1a;\\n    border-color: #333;\\n  }\\n  .header-btn[_ngcontent-%COMP%], .menu-item[_ngcontent-%COMP%] {\\n    color: #fff;\\n  }\\n  .header-btn[_ngcontent-%COMP%]:hover, .menu-item[_ngcontent-%COMP%]:hover {\\n    background: #333;\\n  }\\n  .mobile-search-input[_ngcontent-%COMP%] {\\n    background: #333;\\n    border-color: #555;\\n    color: #fff;\\n  }\\n}\\n@media (prefers-reduced-motion: reduce) {\\n  *[_ngcontent-%COMP%] {\\n    transition: none !important;\\n    animation: none !important;\\n  }\\n}\\n@media (prefers-contrast: high) {\\n  .mobile-header[_ngcontent-%COMP%], .mobile-bottom-nav[_ngcontent-%COMP%] {\\n    border-width: 2px;\\n  }\\n  .badge[_ngcontent-%COMP%], .nav-badge[_ngcontent-%COMP%], .menu-badge[_ngcontent-%COMP%] {\\n    border: 1px solid #000;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "RouterModule", "FormsModule", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "formatCount", "cartCount", "wishlistCount", "ɵɵlistener", "MobileLayoutComponent_header_1_Template_button_click_2_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "toggleMenu", "ɵɵtemplate", "MobileLayoutComponent_header_1_i_3_Template", "MobileLayoutComponent_header_1_i_4_Template", "MobileLayoutComponent_header_1_Template_button_click_10_listener", "toggleSearch", "MobileLayoutComponent_header_1_span_14_Template", "MobileLayoutComponent_header_1_span_17_Template", "ɵɵtwoWayListener", "MobileLayoutComponent_header_1_Template_input_ngModelChange_20_listener", "$event", "ɵɵtwoWayBindingSet", "searchQuery", "MobileLayoutComponent_header_1_Template_input_keyup_enter_20_listener", "onSearchSubmit", "MobileLayoutComponent_header_1_Template_button_click_21_listener", "MobileLayoutComponent_header_1_Template_button_click_23_listener", "ɵɵproperty", "isMenuOpen", "ɵɵclassProp", "isSearchOpen", "ɵɵtwoWayProperty", "currentUser", "avatar", "ɵɵsanitizeUrl", "fullName", "email", "MobileLayoutComponent_div_6_Template_button_click_9_listener", "_r3", "closeMenu", "MobileLayoutComponent_div_6_Template_button_click_11_listener", "MobileLayoutComponent_div_28_Template_a_click_2_listener", "_r4", "MobileLayoutComponent_div_28_Template_a_click_6_listener", "MobileLayoutComponent_div_28_Template_a_click_10_listener", "MobileLayoutComponent_div_28_span_14_Template", "MobileLayoutComponent_div_28_Template_a_click_15_listener", "MobileLayoutComponent_div_28_span_19_Template", "MobileLayoutComponent_div_28_Template_a_click_20_listener", "MobileLayoutComponent_div_28_Template_button_click_25_listener", "logout", "MobileLayoutComponent_nav_44_span_17_Template", "isCurrentRoute", "MobileLayoutComponent", "constructor", "mobileService", "authService", "cartService", "wishlistService", "showHeader", "showFooter", "showBottomNav", "menuToggle", "deviceInfo", "breakpoints", "isKeyboardOpen", "subscriptions", "ngOnInit", "push", "getDeviceInfo$", "subscribe", "info", "updateLayoutForDevice", "getViewportBreakpoints$", "updateLayoutForBreakpoint", "getIsKeyboardOpen$", "isOpen", "handleKeyboardState", "currentUser$", "user", "loadUserCounts", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "isMobile", "enableMobileOptimizations", "disableMobileOptimizations", "orientation", "handleLandscapeMode", "handlePortraitMode", "xs", "sm", "enableCompactMode", "disableCompactMode", "document", "body", "classList", "add", "remove", "supportsHover", "scrollElements", "querySelectorAll", "element", "enableGPUAcceleration", "emit", "disableBodyScroll", "enableBodyScroll", "setTimeout", "searchInput", "querySelector", "focus", "trim", "console", "log", "navigateToProfile", "navigateToOrders", "navigateToSettings", "getTotalCount", "count", "toString", "route", "window", "location", "pathname", "onTouchStart", "event", "onTouchMove", "onTouchEnd", "trackByIndex", "index", "ɵɵdirectiveInject", "i1", "MobileOptimizationService", "i2", "AuthService", "i3", "CartService", "i4", "WishlistService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "ngContentSelectors", "_c0", "decls", "vars", "consts", "template", "MobileLayoutComponent_Template", "rf", "ctx", "MobileLayoutComponent_header_1_Template", "MobileLayoutComponent_Template_div_click_3_listener", "MobileLayoutComponent_div_5_Template", "MobileLayoutComponent_div_6_Template", "MobileLayoutComponent_Template_a_click_8_listener", "MobileLayoutComponent_Template_a_click_12_listener", "MobileLayoutComponent_Template_a_click_16_listener", "MobileLayoutComponent_Template_a_click_20_listener", "MobileLayoutComponent_Template_a_click_24_listener", "MobileLayoutComponent_div_28_Template", "ɵɵprojection", "MobileLayoutComponent_nav_44_Template", "MobileLayoutComponent_footer_45_Template", "i5", "NgIf", "i6", "RouterLink", "i7", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\shared\\components\\mobile-layout\\mobile-layout.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\shared\\components\\mobile-layout\\mobile-layout.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport { Subscription } from 'rxjs';\n\nimport { MobileOptimizationService, DeviceInfo, ViewportBreakpoints } from '../../../core/services/mobile-optimization.service';\nimport { AuthService } from '../../../core/services/auth.service';\nimport { CartService } from '../../../core/services/cart.service';\nimport { WishlistService } from '../../../core/services/wishlist.service';\n\n@Component({\n  selector: 'app-mobile-layout',\n  standalone: true,\n  imports: [CommonModule, RouterModule, FormsModule],\n  templateUrl: './mobile-layout.component.html',\n  styleUrls: ['./mobile-layout.component.scss']\n})\nexport class MobileLayoutComponent implements OnInit, OnDestroy {\n  @Input() showHeader = true;\n  @Input() showFooter = true;\n  @Input() showBottomNav = true;\n  @Output() menuToggle = new EventEmitter<boolean>();\n\n  deviceInfo: DeviceInfo | null = null;\n  breakpoints: ViewportBreakpoints | null = null;\n  isKeyboardOpen = false;\n  currentUser: any = null;\n  \n  cartCount = 0;\n  wishlistCount = 0;\n  \n  isMenuOpen = false;\n  isSearchOpen = false;\n  searchQuery = '';\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private mobileService: MobileOptimizationService,\n    private authService: AuthService,\n    private cartService: CartService,\n    private wishlistService: WishlistService\n  ) {}\n\n  ngOnInit() {\n    // Subscribe to device info\n    this.subscriptions.push(\n      this.mobileService.getDeviceInfo$().subscribe(info => {\n        this.deviceInfo = info;\n        this.updateLayoutForDevice();\n      })\n    );\n\n    // Subscribe to viewport breakpoints\n    this.subscriptions.push(\n      this.mobileService.getViewportBreakpoints$().subscribe(breakpoints => {\n        this.breakpoints = breakpoints;\n        this.updateLayoutForBreakpoint();\n      })\n    );\n\n    // Subscribe to keyboard state\n    this.subscriptions.push(\n      this.mobileService.getIsKeyboardOpen$().subscribe(isOpen => {\n        this.isKeyboardOpen = isOpen;\n        this.handleKeyboardState(isOpen);\n      })\n    );\n\n    // Subscribe to auth state\n    this.subscriptions.push(\n      this.authService.currentUser$.subscribe(user => {\n        this.currentUser = user;\n        if (user) {\n          this.loadUserCounts();\n        } else {\n          this.cartCount = 0;\n          this.wishlistCount = 0;\n        }\n      })\n    );\n\n    // Load initial counts\n    this.loadUserCounts();\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  private updateLayoutForDevice() {\n    if (!this.deviceInfo) return;\n\n    // Apply device-specific optimizations\n    if (this.deviceInfo.isMobile) {\n      this.enableMobileOptimizations();\n    } else {\n      this.disableMobileOptimizations();\n    }\n\n    // Handle orientation changes\n    if (this.deviceInfo.orientation === 'landscape' && this.deviceInfo.isMobile) {\n      this.handleLandscapeMode();\n    } else {\n      this.handlePortraitMode();\n    }\n  }\n\n  private updateLayoutForBreakpoint() {\n    if (!this.breakpoints) return;\n\n    // Adjust layout based on breakpoints\n    if (this.breakpoints.xs || this.breakpoints.sm) {\n      this.showBottomNav = true;\n      this.enableCompactMode();\n    } else {\n      this.showBottomNav = false;\n      this.disableCompactMode();\n    }\n  }\n\n  private handleKeyboardState(isOpen: boolean) {\n    if (isOpen) {\n      // Hide bottom navigation when keyboard is open\n      document.body.classList.add('keyboard-open');\n    } else {\n      document.body.classList.remove('keyboard-open');\n    }\n  }\n\n  private enableMobileOptimizations() {\n    // Enable touch-friendly interactions\n    document.body.classList.add('mobile-device');\n    \n    // Disable hover effects on mobile\n    if (!this.mobileService.supportsHover()) {\n      document.body.classList.add('no-hover');\n    }\n\n    // Enable GPU acceleration for smooth scrolling\n    const scrollElements = document.querySelectorAll('.scroll-container');\n    scrollElements.forEach(element => {\n      this.mobileService.enableGPUAcceleration(element as HTMLElement);\n    });\n  }\n\n  private disableMobileOptimizations() {\n    document.body.classList.remove('mobile-device', 'no-hover');\n  }\n\n  private handleLandscapeMode() {\n    document.body.classList.add('landscape-mode');\n  }\n\n  private handlePortraitMode() {\n    document.body.classList.remove('landscape-mode');\n  }\n\n  private enableCompactMode() {\n    document.body.classList.add('compact-mode');\n  }\n\n  private disableCompactMode() {\n    document.body.classList.remove('compact-mode');\n  }\n\n  private loadUserCounts() {\n    if (!this.currentUser) return;\n\n    // Set default counts for now\n    this.cartCount = 0;\n    this.wishlistCount = 0;\n  }\n\n  // Menu Methods\n  toggleMenu() {\n    this.isMenuOpen = !this.isMenuOpen;\n    this.menuToggle.emit(this.isMenuOpen);\n    \n    if (this.isMenuOpen) {\n      this.mobileService.disableBodyScroll();\n    } else {\n      this.mobileService.enableBodyScroll();\n    }\n  }\n\n  closeMenu() {\n    this.isMenuOpen = false;\n    this.menuToggle.emit(false);\n    this.mobileService.enableBodyScroll();\n  }\n\n  // Search Methods\n  toggleSearch() {\n    this.isSearchOpen = !this.isSearchOpen;\n    \n    if (this.isSearchOpen) {\n      setTimeout(() => {\n        const searchInput = document.querySelector('.mobile-search-input') as HTMLInputElement;\n        if (searchInput) {\n          searchInput.focus();\n        }\n      }, 100);\n    }\n  }\n\n  onSearchSubmit() {\n    if (this.searchQuery.trim()) {\n      // Navigate to search results\n      console.log('Searching for:', this.searchQuery);\n      this.isSearchOpen = false;\n      this.searchQuery = '';\n    }\n  }\n\n  // Navigation Methods\n  navigateToProfile() {\n    this.closeMenu();\n    // Navigation logic\n  }\n\n  navigateToOrders() {\n    this.closeMenu();\n    // Navigation logic\n  }\n\n  navigateToSettings() {\n    this.closeMenu();\n    // Navigation logic\n  }\n\n  logout() {\n    // Simple logout without subscription\n    this.closeMenu();\n  }\n\n  // Utility Methods\n  getTotalCount(): number {\n    return this.cartCount + this.wishlistCount;\n  }\n\n  formatCount(count: number): string {\n    if (count > 99) return '99+';\n    return count.toString();\n  }\n\n  isCurrentRoute(route: string): boolean {\n    return window.location.pathname === route;\n  }\n\n  // Touch Event Handlers\n  onTouchStart(event: TouchEvent) {\n    // Handle touch start for custom gestures\n  }\n\n  onTouchMove(event: TouchEvent) {\n    // Handle touch move for custom gestures\n  }\n\n  onTouchEnd(event: TouchEvent) {\n    // Handle touch end for custom gestures\n  }\n\n  // Performance Optimization\n  trackByIndex(index: number): number {\n    return index;\n  }\n}\n", "<div class=\"mobile-layout\" \n     [class.menu-open]=\"isMenuOpen\"\n     [class.search-open]=\"isSearchOpen\"\n     [class.keyboard-open]=\"isKeyboardOpen\">\n\n  <!-- Mobile Header -->\n  <header *ngIf=\"showHeader\" class=\"mobile-header\">\n    <div class=\"header-content\">\n      <!-- <PERSON>u <PERSON> -->\n      <button class=\"header-btn menu-btn\" (click)=\"toggleMenu()\">\n        <i class=\"fas fa-bars\" *ngIf=\"!isMenuOpen\"></i>\n        <i class=\"fas fa-times\" *ngIf=\"isMenuOpen\"></i>\n      </button>\n\n      <!-- Logo -->\n      <div class=\"header-logo\" routerLink=\"/\">\n        <img src=\"assets/images/logo.png\" alt=\"DFashion\" class=\"logo-image\">\n        <span class=\"logo-text\">DFashion</span>\n      </div>\n\n      <!-- Header Actions -->\n      <div class=\"header-actions\">\n        <!-- Search Button -->\n        <button class=\"header-btn search-btn\" (click)=\"toggleSearch()\">\n          <i class=\"fas fa-search\"></i>\n        </button>\n\n        <!-- Cart Button -->\n        <button class=\"header-btn cart-btn\" routerLink=\"/cart\">\n          <i class=\"fas fa-shopping-cart\"></i>\n          <span *ngIf=\"cartCount > 0\" class=\"badge\">{{ formatCount(cartCount) }}</span>\n        </button>\n\n        <!-- Wishlist Button -->\n        <button class=\"header-btn wishlist-btn\" routerLink=\"/wishlist\">\n          <i class=\"fas fa-heart\"></i>\n          <span *ngIf=\"wishlistCount > 0\" class=\"badge\">{{ formatCount(wishlistCount) }}</span>\n        </button>\n      </div>\n    </div>\n\n    <!-- Mobile Search Bar -->\n    <div class=\"mobile-search\" [class.active]=\"isSearchOpen\">\n      <div class=\"search-container\">\n        <input \n          type=\"text\" \n          class=\"mobile-search-input\"\n          [(ngModel)]=\"searchQuery\"\n          (keyup.enter)=\"onSearchSubmit()\"\n          placeholder=\"Search for products, brands...\">\n        <button class=\"search-submit-btn\" (click)=\"onSearchSubmit()\">\n          <i class=\"fas fa-search\"></i>\n        </button>\n        <button class=\"search-close-btn\" (click)=\"toggleSearch()\">\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n    </div>\n  </header>\n\n  <!-- Mobile Side Menu -->\n  <div class=\"mobile-menu\" [class.active]=\"isMenuOpen\">\n    <div class=\"menu-overlay\" (click)=\"closeMenu()\"></div>\n    <div class=\"menu-content\">\n      <!-- User Profile Section -->\n      <div class=\"menu-profile\" *ngIf=\"currentUser\">\n        <div class=\"profile-avatar\">\n          <img [src]=\"currentUser.avatar || 'assets/images/default-avatar.png'\" \n               [alt]=\"currentUser.fullName\">\n        </div>\n        <div class=\"profile-info\">\n          <h3>{{ currentUser.fullName }}</h3>\n          <p>{{ currentUser.email }}</p>\n        </div>\n      </div>\n\n      <!-- Guest Section -->\n      <div class=\"menu-guest\" *ngIf=\"!currentUser\">\n        <div class=\"guest-avatar\">\n          <i class=\"fas fa-user\"></i>\n        </div>\n        <div class=\"guest-info\">\n          <h3>Welcome to DFashion</h3>\n          <p>Sign in for personalized experience</p>\n        </div>\n        <div class=\"guest-actions\">\n          <button class=\"btn-primary\" routerLink=\"/login\" (click)=\"closeMenu()\">Sign In</button>\n          <button class=\"btn-secondary\" routerLink=\"/register\" (click)=\"closeMenu()\">Sign Up</button>\n        </div>\n      </div>\n\n      <!-- Menu Items -->\n      <nav class=\"menu-nav\">\n        <a routerLink=\"/\" (click)=\"closeMenu()\" class=\"menu-item\" [class.active]=\"isCurrentRoute('/')\">\n          <i class=\"fas fa-home\"></i>\n          <span>Home</span>\n        </a>\n        \n        <a routerLink=\"/categories\" (click)=\"closeMenu()\" class=\"menu-item\" [class.active]=\"isCurrentRoute('/categories')\">\n          <i class=\"fas fa-th-large\"></i>\n          <span>Categories</span>\n        </a>\n        \n        <a routerLink=\"/trending\" (click)=\"closeMenu()\" class=\"menu-item\" [class.active]=\"isCurrentRoute('/trending')\">\n          <i class=\"fas fa-fire\"></i>\n          <span>Trending</span>\n        </a>\n        \n        <a routerLink=\"/brands\" (click)=\"closeMenu()\" class=\"menu-item\" [class.active]=\"isCurrentRoute('/brands')\">\n          <i class=\"fas fa-tags\"></i>\n          <span>Brands</span>\n        </a>\n        \n        <a routerLink=\"/offers\" (click)=\"closeMenu()\" class=\"menu-item\" [class.active]=\"isCurrentRoute('/offers')\">\n          <i class=\"fas fa-percent\"></i>\n          <span>Offers</span>\n        </a>\n\n        <!-- Authenticated User Menu Items -->\n        <div *ngIf=\"currentUser\" class=\"menu-section\">\n          <div class=\"menu-divider\"></div>\n          \n          <a routerLink=\"/profile\" (click)=\"closeMenu()\" class=\"menu-item\">\n            <i class=\"fas fa-user\"></i>\n            <span>My Profile</span>\n          </a>\n          \n          <a routerLink=\"/orders\" (click)=\"closeMenu()\" class=\"menu-item\">\n            <i class=\"fas fa-box\"></i>\n            <span>My Orders</span>\n          </a>\n          \n          <a routerLink=\"/wishlist\" (click)=\"closeMenu()\" class=\"menu-item\">\n            <i class=\"fas fa-heart\"></i>\n            <span>Wishlist</span>\n            <span *ngIf=\"wishlistCount > 0\" class=\"menu-badge\">{{ formatCount(wishlistCount) }}</span>\n          </a>\n          \n          <a routerLink=\"/cart\" (click)=\"closeMenu()\" class=\"menu-item\">\n            <i class=\"fas fa-shopping-cart\"></i>\n            <span>Cart</span>\n            <span *ngIf=\"cartCount > 0\" class=\"menu-badge\">{{ formatCount(cartCount) }}</span>\n          </a>\n          \n          <a routerLink=\"/settings\" (click)=\"closeMenu()\" class=\"menu-item\">\n            <i class=\"fas fa-cog\"></i>\n            <span>Settings</span>\n          </a>\n          \n          <div class=\"menu-divider\"></div>\n          \n          <button class=\"menu-item logout-btn\" (click)=\"logout()\">\n            <i class=\"fas fa-sign-out-alt\"></i>\n            <span>Logout</span>\n          </button>\n        </div>\n      </nav>\n\n      <!-- App Info -->\n      <div class=\"menu-footer\">\n        <div class=\"app-info\">\n          <p>DFashion v1.0</p>\n          <p>© 2024 All rights reserved</p>\n        </div>\n        <div class=\"social-links\">\n          <a href=\"#\" class=\"social-link\">\n            <i class=\"fab fa-instagram\"></i>\n          </a>\n          <a href=\"#\" class=\"social-link\">\n            <i class=\"fab fa-facebook\"></i>\n          </a>\n          <a href=\"#\" class=\"social-link\">\n            <i class=\"fab fa-twitter\"></i>\n          </a>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Main Content -->\n  <main class=\"mobile-main\">\n    <ng-content></ng-content>\n  </main>\n\n  <!-- Mobile Bottom Navigation -->\n  <nav *ngIf=\"showBottomNav && !isKeyboardOpen\" class=\"mobile-bottom-nav\">\n    <a routerLink=\"/\" class=\"nav-item\" [class.active]=\"isCurrentRoute('/')\">\n      <i class=\"fas fa-home\"></i>\n      <span>Home</span>\n    </a>\n    \n    <a routerLink=\"/categories\" class=\"nav-item\" [class.active]=\"isCurrentRoute('/categories')\">\n      <i class=\"fas fa-th-large\"></i>\n      <span>Shop</span>\n    </a>\n    \n    <a routerLink=\"/search\" class=\"nav-item search-nav\" [class.active]=\"isCurrentRoute('/search')\">\n      <i class=\"fas fa-search\"></i>\n      <span>Search</span>\n    </a>\n    \n    <a routerLink=\"/wishlist\" class=\"nav-item\" [class.active]=\"isCurrentRoute('/wishlist')\">\n      <i class=\"fas fa-heart\"></i>\n      <span>Wishlist</span>\n      <span *ngIf=\"wishlistCount > 0\" class=\"nav-badge\">{{ formatCount(wishlistCount) }}</span>\n    </a>\n    \n    <a routerLink=\"/profile\" class=\"nav-item\" [class.active]=\"isCurrentRoute('/profile')\">\n      <i class=\"fas fa-user\"></i>\n      <span>Profile</span>\n    </a>\n  </nav>\n\n  <!-- Mobile Footer -->\n  <footer *ngIf=\"showFooter && !showBottomNav\" class=\"mobile-footer\">\n    <div class=\"footer-content\">\n      <div class=\"footer-links\">\n        <a href=\"/about\">About</a>\n        <a href=\"/contact\">Contact</a>\n        <a href=\"/privacy\">Privacy</a>\n        <a href=\"/terms\">Terms</a>\n      </div>\n      <div class=\"footer-copyright\">\n        <p>© 2024 DFashion. All rights reserved.</p>\n      </div>\n    </div>\n  </footer>\n</div>\n"], "mappings": "AAAA,SAAsDA,YAAY,QAAQ,eAAe;AACzF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;;ICOpCC,EAAA,CAAAC,SAAA,YAA+C;;;;;IAC/CD,EAAA,CAAAC,SAAA,YAA+C;;;;;IAmB7CD,EAAA,CAAAE,cAAA,eAA0C;IAAAF,EAAA,CAAAG,MAAA,GAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAnCJ,EAAA,CAAAK,SAAA,EAA4B;IAA5BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAD,MAAA,CAAAE,SAAA,EAA4B;;;;;IAMtET,EAAA,CAAAE,cAAA,eAA8C;IAAAF,EAAA,CAAAG,MAAA,GAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAvCJ,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAD,MAAA,CAAAG,aAAA,EAAgC;;;;;;IA3BlFV,EAHJ,CAAAE,cAAA,iBAAiD,cACnB,iBAEiC;IAAvBF,EAAA,CAAAW,UAAA,mBAAAC,gEAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAU,UAAA,EAAY;IAAA,EAAC;IAExDjB,EADA,CAAAkB,UAAA,IAAAC,2CAAA,gBAA2C,IAAAC,2CAAA,gBACA;IAC7CpB,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAE,cAAA,cAAwC;IACtCF,EAAA,CAAAC,SAAA,cAAoE;IACpED,EAAA,CAAAE,cAAA,eAAwB;IAAAF,EAAA,CAAAG,MAAA,eAAQ;IAClCH,EADkC,CAAAI,YAAA,EAAO,EACnC;IAKJJ,EAFF,CAAAE,cAAA,cAA4B,kBAEqC;IAAzBF,EAAA,CAAAW,UAAA,mBAAAU,iEAAA;MAAArB,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAe,YAAA,EAAc;IAAA,EAAC;IAC5DtB,EAAA,CAAAC,SAAA,aAA6B;IAC/BD,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAE,cAAA,kBAAuD;IACrDF,EAAA,CAAAC,SAAA,aAAoC;IACpCD,EAAA,CAAAkB,UAAA,KAAAK,+CAAA,mBAA0C;IAC5CvB,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAE,cAAA,kBAA+D;IAC7DF,EAAA,CAAAC,SAAA,aAA4B;IAC5BD,EAAA,CAAAkB,UAAA,KAAAM,+CAAA,mBAA8C;IAGpDxB,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;IAKFJ,EAFJ,CAAAE,cAAA,eAAyD,eACzB,iBAMmB;IAF7CF,EAAA,CAAAyB,gBAAA,2BAAAC,wEAAAC,MAAA;MAAA3B,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAA4B,kBAAA,CAAArB,MAAA,CAAAsB,WAAA,EAAAF,MAAA,MAAApB,MAAA,CAAAsB,WAAA,GAAAF,MAAA;MAAA,OAAA3B,EAAA,CAAAgB,WAAA,CAAAW,MAAA;IAAA,EAAyB;IACzB3B,EAAA,CAAAW,UAAA,yBAAAmB,sEAAA;MAAA9B,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAeT,MAAA,CAAAwB,cAAA,EAAgB;IAAA,EAAC;IAJlC/B,EAAA,CAAAI,YAAA,EAK+C;IAC/CJ,EAAA,CAAAE,cAAA,kBAA6D;IAA3BF,EAAA,CAAAW,UAAA,mBAAAqB,iEAAA;MAAAhC,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAwB,cAAA,EAAgB;IAAA,EAAC;IAC1D/B,EAAA,CAAAC,SAAA,aAA6B;IAC/BD,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,kBAA0D;IAAzBF,EAAA,CAAAW,UAAA,mBAAAsB,iEAAA;MAAAjC,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAe,YAAA,EAAc;IAAA,EAAC;IACvDtB,EAAA,CAAAC,SAAA,aAA4B;IAIpCD,EAHM,CAAAI,YAAA,EAAS,EACL,EACF,EACC;;;;IAhDqBJ,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAkC,UAAA,UAAA3B,MAAA,CAAA4B,UAAA,CAAiB;IAChBnC,EAAA,CAAAK,SAAA,EAAgB;IAAhBL,EAAA,CAAAkC,UAAA,SAAA3B,MAAA,CAAA4B,UAAA,CAAgB;IAmBhCnC,EAAA,CAAAK,SAAA,IAAmB;IAAnBL,EAAA,CAAAkC,UAAA,SAAA3B,MAAA,CAAAE,SAAA,KAAmB;IAMnBT,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAkC,UAAA,SAAA3B,MAAA,CAAAG,aAAA,KAAuB;IAMTV,EAAA,CAAAK,SAAA,EAA6B;IAA7BL,EAAA,CAAAoC,WAAA,WAAA7B,MAAA,CAAA8B,YAAA,CAA6B;IAKlDrC,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAsC,gBAAA,YAAA/B,MAAA,CAAAsB,WAAA,CAAyB;;;;;IAmB3B7B,EADF,CAAAE,cAAA,cAA8C,cAChB;IAC1BF,EAAA,CAAAC,SAAA,cACkC;IACpCD,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAE,cAAA,cAA0B,SACpB;IAAAF,EAAA,CAAAG,MAAA,GAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACnCJ,EAAA,CAAAE,cAAA,QAAG;IAAAF,EAAA,CAAAG,MAAA,GAAuB;IAE9BH,EAF8B,CAAAI,YAAA,EAAI,EAC1B,EACF;;;;IAPGJ,EAAA,CAAAK,SAAA,GAAgE;IAChEL,EADA,CAAAkC,UAAA,QAAA3B,MAAA,CAAAgC,WAAA,CAAAC,MAAA,wCAAAxC,EAAA,CAAAyC,aAAA,CAAgE,QAAAlC,MAAA,CAAAgC,WAAA,CAAAG,QAAA,CACpC;IAG7B1C,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAgC,WAAA,CAAAG,QAAA,CAA0B;IAC3B1C,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAgC,WAAA,CAAAI,KAAA,CAAuB;;;;;;IAM5B3C,EADF,CAAAE,cAAA,cAA6C,cACjB;IACxBF,EAAA,CAAAC,SAAA,YAA2B;IAC7BD,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAE,cAAA,cAAwB,SAClB;IAAAF,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5BJ,EAAA,CAAAE,cAAA,QAAG;IAAAF,EAAA,CAAAG,MAAA,0CAAmC;IACxCH,EADwC,CAAAI,YAAA,EAAI,EACtC;IAEJJ,EADF,CAAAE,cAAA,cAA2B,iBAC6C;IAAtBF,EAAA,CAAAW,UAAA,mBAAAiC,6DAAA;MAAA5C,EAAA,CAAAa,aAAA,CAAAgC,GAAA;MAAA,MAAAtC,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAuC,SAAA,EAAW;IAAA,EAAC;IAAC9C,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACtFJ,EAAA,CAAAE,cAAA,kBAA2E;IAAtBF,EAAA,CAAAW,UAAA,mBAAAoC,8DAAA;MAAA/C,EAAA,CAAAa,aAAA,CAAAgC,GAAA;MAAA,MAAAtC,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAuC,SAAA,EAAW;IAAA,EAAC;IAAC9C,EAAA,CAAAG,MAAA,eAAO;IAEtFH,EAFsF,CAAAI,YAAA,EAAS,EACvF,EACF;;;;;IA8CAJ,EAAA,CAAAE,cAAA,eAAmD;IAAAF,EAAA,CAAAG,MAAA,GAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAvCJ,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAD,MAAA,CAAAG,aAAA,EAAgC;;;;;IAMnFV,EAAA,CAAAE,cAAA,eAA+C;IAAAF,EAAA,CAAAG,MAAA,GAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAnCJ,EAAA,CAAAK,SAAA,EAA4B;IAA5BL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAD,MAAA,CAAAE,SAAA,EAA4B;;;;;;IAtB/ET,EAAA,CAAAE,cAAA,cAA8C;IAC5CF,EAAA,CAAAC,SAAA,cAAgC;IAEhCD,EAAA,CAAAE,cAAA,YAAiE;IAAxCF,EAAA,CAAAW,UAAA,mBAAAqC,yDAAA;MAAAhD,EAAA,CAAAa,aAAA,CAAAoC,GAAA;MAAA,MAAA1C,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAuC,SAAA,EAAW;IAAA,EAAC;IAC5C9C,EAAA,CAAAC,SAAA,YAA2B;IAC3BD,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,iBAAU;IAClBH,EADkB,CAAAI,YAAA,EAAO,EACrB;IAEJJ,EAAA,CAAAE,cAAA,YAAgE;IAAxCF,EAAA,CAAAW,UAAA,mBAAAuC,yDAAA;MAAAlD,EAAA,CAAAa,aAAA,CAAAoC,GAAA;MAAA,MAAA1C,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAuC,SAAA,EAAW;IAAA,EAAC;IAC3C9C,EAAA,CAAAC,SAAA,YAA0B;IAC1BD,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,gBAAS;IACjBH,EADiB,CAAAI,YAAA,EAAO,EACpB;IAEJJ,EAAA,CAAAE,cAAA,aAAkE;IAAxCF,EAAA,CAAAW,UAAA,mBAAAwC,0DAAA;MAAAnD,EAAA,CAAAa,aAAA,CAAAoC,GAAA;MAAA,MAAA1C,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAuC,SAAA,EAAW;IAAA,EAAC;IAC7C9C,EAAA,CAAAC,SAAA,aAA4B;IAC5BD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACrBJ,EAAA,CAAAkB,UAAA,KAAAkC,6CAAA,mBAAmD;IACrDpD,EAAA,CAAAI,YAAA,EAAI;IAEJJ,EAAA,CAAAE,cAAA,aAA8D;IAAxCF,EAAA,CAAAW,UAAA,mBAAA0C,0DAAA;MAAArD,EAAA,CAAAa,aAAA,CAAAoC,GAAA;MAAA,MAAA1C,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAuC,SAAA,EAAW;IAAA,EAAC;IACzC9C,EAAA,CAAAC,SAAA,aAAoC;IACpCD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACjBJ,EAAA,CAAAkB,UAAA,KAAAoC,6CAAA,mBAA+C;IACjDtD,EAAA,CAAAI,YAAA,EAAI;IAEJJ,EAAA,CAAAE,cAAA,aAAkE;IAAxCF,EAAA,CAAAW,UAAA,mBAAA4C,0DAAA;MAAAvD,EAAA,CAAAa,aAAA,CAAAoC,GAAA;MAAA,MAAA1C,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAuC,SAAA,EAAW;IAAA,EAAC;IAC7C9C,EAAA,CAAAC,SAAA,aAA0B;IAC1BD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,gBAAQ;IAChBH,EADgB,CAAAI,YAAA,EAAO,EACnB;IAEJJ,EAAA,CAAAC,SAAA,eAAgC;IAEhCD,EAAA,CAAAE,cAAA,kBAAwD;IAAnBF,EAAA,CAAAW,UAAA,mBAAA6C,+DAAA;MAAAxD,EAAA,CAAAa,aAAA,CAAAoC,GAAA;MAAA,MAAA1C,MAAA,GAAAP,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAST,MAAA,CAAAkD,MAAA,EAAQ;IAAA,EAAC;IACrDzD,EAAA,CAAAC,SAAA,aAAmC;IACnCD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,cAAM;IAEhBH,EAFgB,CAAAI,YAAA,EAAO,EACZ,EACL;;;;IApBKJ,EAAA,CAAAK,SAAA,IAAuB;IAAvBL,EAAA,CAAAkC,UAAA,SAAA3B,MAAA,CAAAG,aAAA,KAAuB;IAMvBV,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAkC,UAAA,SAAA3B,MAAA,CAAAE,SAAA,KAAmB;;;;;IA+DhCT,EAAA,CAAAE,cAAA,eAAkD;IAAAF,EAAA,CAAAG,MAAA,GAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAvCJ,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAD,MAAA,CAAAG,aAAA,EAAgC;;;;;IAlBpFV,EADF,CAAAE,cAAA,cAAwE,YACE;IACtEF,EAAA,CAAAC,SAAA,WAA2B;IAC3BD,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,WAAI;IACZH,EADY,CAAAI,YAAA,EAAO,EACf;IAEJJ,EAAA,CAAAE,cAAA,YAA4F;IAC1FF,EAAA,CAAAC,SAAA,YAA+B;IAC/BD,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,WAAI;IACZH,EADY,CAAAI,YAAA,EAAO,EACf;IAEJJ,EAAA,CAAAE,cAAA,YAA+F;IAC7FF,EAAA,CAAAC,SAAA,aAA6B;IAC7BD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,cAAM;IACdH,EADc,CAAAI,YAAA,EAAO,EACjB;IAEJJ,EAAA,CAAAE,cAAA,aAAwF;IACtFF,EAAA,CAAAC,SAAA,aAA4B;IAC5BD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACrBJ,EAAA,CAAAkB,UAAA,KAAAwC,6CAAA,mBAAkD;IACpD1D,EAAA,CAAAI,YAAA,EAAI;IAEJJ,EAAA,CAAAE,cAAA,aAAsF;IACpFF,EAAA,CAAAC,SAAA,aAA2B;IAC3BD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,eAAO;IAEjBH,EAFiB,CAAAI,YAAA,EAAO,EAClB,EACA;;;;IAzB+BJ,EAAA,CAAAK,SAAA,EAAoC;IAApCL,EAAA,CAAAoC,WAAA,WAAA7B,MAAA,CAAAoD,cAAA,MAAoC;IAK1B3D,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAoC,WAAA,WAAA7B,MAAA,CAAAoD,cAAA,gBAA8C;IAKvC3D,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAoC,WAAA,WAAA7B,MAAA,CAAAoD,cAAA,YAA0C;IAKnD3D,EAAA,CAAAK,SAAA,GAA4C;IAA5CL,EAAA,CAAAoC,WAAA,WAAA7B,MAAA,CAAAoD,cAAA,cAA4C;IAG9E3D,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAkC,UAAA,SAAA3B,MAAA,CAAAG,aAAA,KAAuB;IAGUV,EAAA,CAAAK,SAAA,EAA2C;IAA3CL,EAAA,CAAAoC,WAAA,WAAA7B,MAAA,CAAAoD,cAAA,aAA2C;;;;;IAUjF3D,EAHN,CAAAE,cAAA,iBAAmE,cACrC,cACA,YACP;IAAAF,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC1BJ,EAAA,CAAAE,cAAA,YAAmB;IAAAF,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC9BJ,EAAA,CAAAE,cAAA,YAAmB;IAAAF,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC9BJ,EAAA,CAAAE,cAAA,YAAiB;IAAAF,EAAA,CAAAG,MAAA,aAAK;IACxBH,EADwB,CAAAI,YAAA,EAAI,EACtB;IAEJJ,EADF,CAAAE,cAAA,eAA8B,SACzB;IAAAF,EAAA,CAAAG,MAAA,kDAAqC;IAG9CH,EAH8C,CAAAI,YAAA,EAAI,EACxC,EACF,EACC;;;ADhNX,OAAM,MAAOwD,qBAAqB;EAoBhCC,YACUC,aAAwC,EACxCC,WAAwB,EACxBC,WAAwB,EACxBC,eAAgC;IAHhC,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IAvBhB,KAAAC,UAAU,GAAG,IAAI;IACjB,KAAAC,UAAU,GAAG,IAAI;IACjB,KAAAC,aAAa,GAAG,IAAI;IACnB,KAAAC,UAAU,GAAG,IAAIzE,YAAY,EAAW;IAElD,KAAA0E,UAAU,GAAsB,IAAI;IACpC,KAAAC,WAAW,GAA+B,IAAI;IAC9C,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAjC,WAAW,GAAQ,IAAI;IAEvB,KAAA9B,SAAS,GAAG,CAAC;IACb,KAAAC,aAAa,GAAG,CAAC;IAEjB,KAAAyB,UAAU,GAAG,KAAK;IAClB,KAAAE,YAAY,GAAG,KAAK;IACpB,KAAAR,WAAW,GAAG,EAAE;IAER,KAAA4C,aAAa,GAAmB,EAAE;EAOvC;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACD,aAAa,CAACE,IAAI,CACrB,IAAI,CAACb,aAAa,CAACc,cAAc,EAAE,CAACC,SAAS,CAACC,IAAI,IAAG;MACnD,IAAI,CAACR,UAAU,GAAGQ,IAAI;MACtB,IAAI,CAACC,qBAAqB,EAAE;IAC9B,CAAC,CAAC,CACH;IAED;IACA,IAAI,CAACN,aAAa,CAACE,IAAI,CACrB,IAAI,CAACb,aAAa,CAACkB,uBAAuB,EAAE,CAACH,SAAS,CAACN,WAAW,IAAG;MACnE,IAAI,CAACA,WAAW,GAAGA,WAAW;MAC9B,IAAI,CAACU,yBAAyB,EAAE;IAClC,CAAC,CAAC,CACH;IAED;IACA,IAAI,CAACR,aAAa,CAACE,IAAI,CACrB,IAAI,CAACb,aAAa,CAACoB,kBAAkB,EAAE,CAACL,SAAS,CAACM,MAAM,IAAG;MACzD,IAAI,CAACX,cAAc,GAAGW,MAAM;MAC5B,IAAI,CAACC,mBAAmB,CAACD,MAAM,CAAC;IAClC,CAAC,CAAC,CACH;IAED;IACA,IAAI,CAACV,aAAa,CAACE,IAAI,CACrB,IAAI,CAACZ,WAAW,CAACsB,YAAY,CAACR,SAAS,CAACS,IAAI,IAAG;MAC7C,IAAI,CAAC/C,WAAW,GAAG+C,IAAI;MACvB,IAAIA,IAAI,EAAE;QACR,IAAI,CAACC,cAAc,EAAE;OACtB,MAAM;QACL,IAAI,CAAC9E,SAAS,GAAG,CAAC;QAClB,IAAI,CAACC,aAAa,GAAG,CAAC;;IAE1B,CAAC,CAAC,CACH;IAED;IACA,IAAI,CAAC6E,cAAc,EAAE;EACvB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACf,aAAa,CAACgB,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;EACtD;EAEQZ,qBAAqBA,CAAA;IAC3B,IAAI,CAAC,IAAI,CAACT,UAAU,EAAE;IAEtB;IACA,IAAI,IAAI,CAACA,UAAU,CAACsB,QAAQ,EAAE;MAC5B,IAAI,CAACC,yBAAyB,EAAE;KACjC,MAAM;MACL,IAAI,CAACC,0BAA0B,EAAE;;IAGnC;IACA,IAAI,IAAI,CAACxB,UAAU,CAACyB,WAAW,KAAK,WAAW,IAAI,IAAI,CAACzB,UAAU,CAACsB,QAAQ,EAAE;MAC3E,IAAI,CAACI,mBAAmB,EAAE;KAC3B,MAAM;MACL,IAAI,CAACC,kBAAkB,EAAE;;EAE7B;EAEQhB,yBAAyBA,CAAA;IAC/B,IAAI,CAAC,IAAI,CAACV,WAAW,EAAE;IAEvB;IACA,IAAI,IAAI,CAACA,WAAW,CAAC2B,EAAE,IAAI,IAAI,CAAC3B,WAAW,CAAC4B,EAAE,EAAE;MAC9C,IAAI,CAAC/B,aAAa,GAAG,IAAI;MACzB,IAAI,CAACgC,iBAAiB,EAAE;KACzB,MAAM;MACL,IAAI,CAAChC,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACiC,kBAAkB,EAAE;;EAE7B;EAEQjB,mBAAmBA,CAACD,MAAe;IACzC,IAAIA,MAAM,EAAE;MACV;MACAmB,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;KAC7C,MAAM;MACLH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,eAAe,CAAC;;EAEnD;EAEQb,yBAAyBA,CAAA;IAC/B;IACAS,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;IAE5C;IACA,IAAI,CAAC,IAAI,CAAC3C,aAAa,CAAC6C,aAAa,EAAE,EAAE;MACvCL,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;;IAGzC;IACA,MAAMG,cAAc,GAAGN,QAAQ,CAACO,gBAAgB,CAAC,mBAAmB,CAAC;IACrED,cAAc,CAACnB,OAAO,CAACqB,OAAO,IAAG;MAC/B,IAAI,CAAChD,aAAa,CAACiD,qBAAqB,CAACD,OAAsB,CAAC;IAClE,CAAC,CAAC;EACJ;EAEQhB,0BAA0BA,CAAA;IAChCQ,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,eAAe,EAAE,UAAU,CAAC;EAC7D;EAEQV,mBAAmBA,CAAA;IACzBM,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,gBAAgB,CAAC;EAC/C;EAEQR,kBAAkBA,CAAA;IACxBK,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,gBAAgB,CAAC;EAClD;EAEQN,iBAAiBA,CAAA;IACvBE,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,cAAc,CAAC;EAC7C;EAEQJ,kBAAkBA,CAAA;IACxBC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,cAAc,CAAC;EAChD;EAEQnB,cAAcA,CAAA;IACpB,IAAI,CAAC,IAAI,CAAChD,WAAW,EAAE;IAEvB;IACA,IAAI,CAAC9B,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,aAAa,GAAG,CAAC;EACxB;EAEA;EACAO,UAAUA,CAAA;IACR,IAAI,CAACkB,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAACkC,UAAU,CAAC2C,IAAI,CAAC,IAAI,CAAC7E,UAAU,CAAC;IAErC,IAAI,IAAI,CAACA,UAAU,EAAE;MACnB,IAAI,CAAC2B,aAAa,CAACmD,iBAAiB,EAAE;KACvC,MAAM;MACL,IAAI,CAACnD,aAAa,CAACoD,gBAAgB,EAAE;;EAEzC;EAEApE,SAASA,CAAA;IACP,IAAI,CAACX,UAAU,GAAG,KAAK;IACvB,IAAI,CAACkC,UAAU,CAAC2C,IAAI,CAAC,KAAK,CAAC;IAC3B,IAAI,CAAClD,aAAa,CAACoD,gBAAgB,EAAE;EACvC;EAEA;EACA5F,YAAYA,CAAA;IACV,IAAI,CAACe,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IAEtC,IAAI,IAAI,CAACA,YAAY,EAAE;MACrB8E,UAAU,CAAC,MAAK;QACd,MAAMC,WAAW,GAAGd,QAAQ,CAACe,aAAa,CAAC,sBAAsB,CAAqB;QACtF,IAAID,WAAW,EAAE;UACfA,WAAW,CAACE,KAAK,EAAE;;MAEvB,CAAC,EAAE,GAAG,CAAC;;EAEX;EAEAvF,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACF,WAAW,CAAC0F,IAAI,EAAE,EAAE;MAC3B;MACAC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC5F,WAAW,CAAC;MAC/C,IAAI,CAACQ,YAAY,GAAG,KAAK;MACzB,IAAI,CAACR,WAAW,GAAG,EAAE;;EAEzB;EAEA;EACA6F,iBAAiBA,CAAA;IACf,IAAI,CAAC5E,SAAS,EAAE;IAChB;EACF;EAEA6E,gBAAgBA,CAAA;IACd,IAAI,CAAC7E,SAAS,EAAE;IAChB;EACF;EAEA8E,kBAAkBA,CAAA;IAChB,IAAI,CAAC9E,SAAS,EAAE;IAChB;EACF;EAEAW,MAAMA,CAAA;IACJ;IACA,IAAI,CAACX,SAAS,EAAE;EAClB;EAEA;EACA+E,aAAaA,CAAA;IACX,OAAO,IAAI,CAACpH,SAAS,GAAG,IAAI,CAACC,aAAa;EAC5C;EAEAF,WAAWA,CAACsH,KAAa;IACvB,IAAIA,KAAK,GAAG,EAAE,EAAE,OAAO,KAAK;IAC5B,OAAOA,KAAK,CAACC,QAAQ,EAAE;EACzB;EAEApE,cAAcA,CAACqE,KAAa;IAC1B,OAAOC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAKH,KAAK;EAC3C;EAEA;EACAI,YAAYA,CAACC,KAAiB;IAC5B;EAAA;EAGFC,WAAWA,CAACD,KAAiB;IAC3B;EAAA;EAGFE,UAAUA,CAACF,KAAiB;IAC1B;EAAA;EAGF;EACAG,YAAYA,CAACC,KAAa;IACxB,OAAOA,KAAK;EACd;;;uBAzPW7E,qBAAqB,EAAA5D,EAAA,CAAA0I,iBAAA,CAAAC,EAAA,CAAAC,yBAAA,GAAA5I,EAAA,CAAA0I,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA9I,EAAA,CAAA0I,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAhJ,EAAA,CAAA0I,iBAAA,CAAAO,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAArBtF,qBAAqB;MAAAuF,SAAA;MAAAC,MAAA;QAAAlF,UAAA;QAAAC,UAAA;QAAAC,aAAA;MAAA;MAAAiF,OAAA;QAAAhF,UAAA;MAAA;MAAAiF,UAAA;MAAAC,QAAA,GAAAvJ,EAAA,CAAAwJ,mBAAA;MAAAC,kBAAA,EAAAC,GAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UClBlChK,EAAA,CAAAE,cAAA,aAG4C;UAG1CF,EAAA,CAAAkB,UAAA,IAAAgJ,uCAAA,qBAAiD;UAwD/ClK,EADF,CAAAE,cAAA,aAAqD,aACH;UAAtBF,EAAA,CAAAW,UAAA,mBAAAwJ,oDAAA;YAAA,OAASF,GAAA,CAAAnH,SAAA,EAAW;UAAA,EAAC;UAAC9C,EAAA,CAAAI,YAAA,EAAM;UACtDJ,EAAA,CAAAE,cAAA,aAA0B;UAcxBF,EAZA,CAAAkB,UAAA,IAAAkJ,oCAAA,iBAA8C,IAAAC,oCAAA,kBAYD;UAgB3CrK,EADF,CAAAE,cAAA,aAAsB,WAC2E;UAA7EF,EAAA,CAAAW,UAAA,mBAAA2J,kDAAA;YAAA,OAASL,GAAA,CAAAnH,SAAA,EAAW;UAAA,EAAC;UACrC9C,EAAA,CAAAC,SAAA,WAA2B;UAC3BD,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAG,MAAA,YAAI;UACZH,EADY,CAAAI,YAAA,EAAO,EACf;UAEJJ,EAAA,CAAAE,cAAA,aAAmH;UAAvFF,EAAA,CAAAW,UAAA,mBAAA4J,mDAAA;YAAA,OAASN,GAAA,CAAAnH,SAAA,EAAW;UAAA,EAAC;UAC/C9C,EAAA,CAAAC,SAAA,aAA+B;UAC/BD,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAG,MAAA,kBAAU;UAClBH,EADkB,CAAAI,YAAA,EAAO,EACrB;UAEJJ,EAAA,CAAAE,cAAA,aAA+G;UAArFF,EAAA,CAAAW,UAAA,mBAAA6J,mDAAA;YAAA,OAASP,GAAA,CAAAnH,SAAA,EAAW;UAAA,EAAC;UAC7C9C,EAAA,CAAAC,SAAA,aAA2B;UAC3BD,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAG,MAAA,gBAAQ;UAChBH,EADgB,CAAAI,YAAA,EAAO,EACnB;UAEJJ,EAAA,CAAAE,cAAA,aAA2G;UAAnFF,EAAA,CAAAW,UAAA,mBAAA8J,mDAAA;YAAA,OAASR,GAAA,CAAAnH,SAAA,EAAW;UAAA,EAAC;UAC3C9C,EAAA,CAAAC,SAAA,aAA2B;UAC3BD,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAG,MAAA,cAAM;UACdH,EADc,CAAAI,YAAA,EAAO,EACjB;UAEJJ,EAAA,CAAAE,cAAA,aAA2G;UAAnFF,EAAA,CAAAW,UAAA,mBAAA+J,mDAAA;YAAA,OAAST,GAAA,CAAAnH,SAAA,EAAW;UAAA,EAAC;UAC3C9C,EAAA,CAAAC,SAAA,aAA8B;UAC9BD,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAG,MAAA,cAAM;UACdH,EADc,CAAAI,YAAA,EAAO,EACjB;UAGJJ,EAAA,CAAAkB,UAAA,KAAAyJ,qCAAA,mBAA8C;UAqChD3K,EAAA,CAAAI,YAAA,EAAM;UAKFJ,EAFJ,CAAAE,cAAA,eAAyB,eACD,SACjB;UAAAF,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACpBJ,EAAA,CAAAE,cAAA,SAAG;UAAAF,EAAA,CAAAG,MAAA,uCAA0B;UAC/BH,EAD+B,CAAAI,YAAA,EAAI,EAC7B;UAEJJ,EADF,CAAAE,cAAA,eAA0B,aACQ;UAC9BF,EAAA,CAAAC,SAAA,aAAgC;UAClCD,EAAA,CAAAI,YAAA,EAAI;UACJJ,EAAA,CAAAE,cAAA,aAAgC;UAC9BF,EAAA,CAAAC,SAAA,aAA+B;UACjCD,EAAA,CAAAI,YAAA,EAAI;UACJJ,EAAA,CAAAE,cAAA,aAAgC;UAC9BF,EAAA,CAAAC,SAAA,aAA8B;UAKxCD,EAJQ,CAAAI,YAAA,EAAI,EACA,EACF,EACF,EACF;UAGNJ,EAAA,CAAAE,cAAA,gBAA0B;UACxBF,EAAA,CAAA4K,YAAA,IAAyB;UAC3B5K,EAAA,CAAAI,YAAA,EAAO;UAgCPJ,EA7BA,CAAAkB,UAAA,KAAA2J,qCAAA,oBAAwE,KAAAC,wCAAA,sBA6BL;UAarE9K,EAAA,CAAAI,YAAA,EAAM;;;UAhODJ,EAFA,CAAAoC,WAAA,cAAA6H,GAAA,CAAA9H,UAAA,CAA8B,gBAAA8H,GAAA,CAAA5H,YAAA,CACI,kBAAA4H,GAAA,CAAAzF,cAAA,CACI;UAGhCxE,EAAA,CAAAK,SAAA,EAAgB;UAAhBL,EAAA,CAAAkC,UAAA,SAAA+H,GAAA,CAAA/F,UAAA,CAAgB;UAuDAlE,EAAA,CAAAK,SAAA,EAA2B;UAA3BL,EAAA,CAAAoC,WAAA,WAAA6H,GAAA,CAAA9H,UAAA,CAA2B;UAIrBnC,EAAA,CAAAK,SAAA,GAAiB;UAAjBL,EAAA,CAAAkC,UAAA,SAAA+H,GAAA,CAAA1H,WAAA,CAAiB;UAYnBvC,EAAA,CAAAK,SAAA,EAAkB;UAAlBL,EAAA,CAAAkC,UAAA,UAAA+H,GAAA,CAAA1H,WAAA,CAAkB;UAgBiBvC,EAAA,CAAAK,SAAA,GAAoC;UAApCL,EAAA,CAAAoC,WAAA,WAAA6H,GAAA,CAAAtG,cAAA,MAAoC;UAK1B3D,EAAA,CAAAK,SAAA,GAA8C;UAA9CL,EAAA,CAAAoC,WAAA,WAAA6H,GAAA,CAAAtG,cAAA,gBAA8C;UAKhD3D,EAAA,CAAAK,SAAA,GAA4C;UAA5CL,EAAA,CAAAoC,WAAA,WAAA6H,GAAA,CAAAtG,cAAA,cAA4C;UAK9C3D,EAAA,CAAAK,SAAA,GAA0C;UAA1CL,EAAA,CAAAoC,WAAA,WAAA6H,GAAA,CAAAtG,cAAA,YAA0C;UAK1C3D,EAAA,CAAAK,SAAA,GAA0C;UAA1CL,EAAA,CAAAoC,WAAA,WAAA6H,GAAA,CAAAtG,cAAA,YAA0C;UAMpG3D,EAAA,CAAAK,SAAA,GAAiB;UAAjBL,EAAA,CAAAkC,UAAA,SAAA+H,GAAA,CAAA1H,WAAA,CAAiB;UAkEvBvC,EAAA,CAAAK,SAAA,IAAsC;UAAtCL,EAAA,CAAAkC,UAAA,SAAA+H,GAAA,CAAA7F,aAAA,KAAA6F,GAAA,CAAAzF,cAAA,CAAsC;UA6BnCxE,EAAA,CAAAK,SAAA,EAAkC;UAAlCL,EAAA,CAAAkC,UAAA,SAAA+H,GAAA,CAAA9F,UAAA,KAAA8F,GAAA,CAAA7F,aAAA,CAAkC;;;qBDxMjCvE,YAAY,EAAAkL,EAAA,CAAAC,IAAA,EAAElL,YAAY,EAAAmL,EAAA,CAAAC,UAAA,EAAEnL,WAAW,EAAAoL,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}