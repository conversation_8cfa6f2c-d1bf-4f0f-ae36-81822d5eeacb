{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { delayWhen } from './delayWhen';\nimport { timer } from '../observable/timer';\nexport function delay(due, scheduler = asyncScheduler) {\n  const duration = timer(due, scheduler);\n  return delayWhen(() => duration);\n}\n//# sourceMappingURL=delay.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}