<ion-header [translucent]="true">
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/tabs/cart"></ion-back-button>
    </ion-buttons>
    <ion-title>Checkout</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <!-- Order Success State -->
  <div *ngIf="orderPlaced" class="success-container">
    <div class="success-content">
      <ion-icon name="checkmark-circle" color="success"></ion-icon>
      <h2>Order Placed Successfully!</h2>
      <p>Your order #{{ orderNumber }} has been confirmed</p>
      <ion-button expand="block" (click)="goToOrders()" color="primary">
        View Orders
      </ion-button>
      <ion-button expand="block" fill="outline" (click)="continueShopping()">
        Continue Shopping
      </ion-button>
    </div>
  </div>

  <!-- Checkout Process -->
  <div *ngIf="!orderPlaced" class="checkout-container">
    <!-- Progress Steps -->
    <div class="progress-steps">
      <div class="step" [class.active]="currentStep >= 1" [class.completed]="currentStep > 1">
        <div class="step-number">1</div>
        <div class="step-label">Shipping</div>
      </div>
      <div class="step-divider"></div>
      <div class="step" [class.active]="currentStep >= 2" [class.completed]="currentStep > 2">
        <div class="step-number">2</div>
        <div class="step-label">Payment</div>
      </div>
      <div class="step-divider"></div>
      <div class="step" [class.active]="currentStep >= 3">
        <div class="step-number">3</div>
        <div class="step-label">Review</div>
      </div>
    </div>

    <!-- Step 1: Shipping Information -->
    <div *ngIf="currentStep === 1" class="step-content">
      <h3>Shipping Information</h3>
      
      <form [formGroup]="shippingForm" class="shipping-form">
        <ion-item>
          <ion-label position="stacked">Full Name *</ion-label>
          <ion-input formControlName="fullName" placeholder="Enter your full name"></ion-input>
        </ion-item>
        
        <ion-item>
          <ion-label position="stacked">Phone Number *</ion-label>
          <ion-input formControlName="phone" type="tel" placeholder="Enter phone number"></ion-input>
        </ion-item>
        
        <ion-item>
          <ion-label position="stacked">Address Line 1 *</ion-label>
          <ion-input formControlName="address1" placeholder="Street address"></ion-input>
        </ion-item>
        
        <ion-item>
          <ion-label position="stacked">Address Line 2</ion-label>
          <ion-input formControlName="address2" placeholder="Apartment, suite, etc."></ion-input>
        </ion-item>
        
        <ion-item>
          <ion-label position="stacked">City *</ion-label>
          <ion-input formControlName="city" placeholder="Enter city"></ion-input>
        </ion-item>
        
        <ion-item>
          <ion-label position="stacked">State *</ion-label>
          <ion-input formControlName="state" placeholder="Enter state"></ion-input>
        </ion-item>
        
        <ion-item>
          <ion-label position="stacked">PIN Code *</ion-label>
          <ion-input formControlName="pincode" placeholder="Enter PIN code"></ion-input>
        </ion-item>
      </form>
    </div>

    <!-- Step 2: Payment Method -->
    <div *ngIf="currentStep === 2" class="step-content">
      <h3>Payment Method</h3>
      
      <div class="payment-methods">
        <ion-radio-group [(ngModel)]="selectedPaymentMethod">
          <div *ngFor="let method of paymentMethods" class="payment-option">
            <ion-item>
              <ion-radio slot="start" [value]="method.id"></ion-radio>
              <ion-icon [name]="method.icon" slot="start"></ion-icon>
              <ion-label>
                <h3>{{ method.name }}</h3>
                <p>{{ method.description }}</p>
              </ion-label>
            </ion-item>
          </div>
        </ion-radio-group>
      </div>
    </div>

    <!-- Step 3: Order Review -->
    <div *ngIf="currentStep === 3" class="step-content">
      <h3>Order Review</h3>
      
      <!-- Cart Items -->
      <div class="order-items">
        <div *ngFor="let item of cartItems" class="order-item">
          <img [src]="item.product.images[0]" [alt]="item.product.name">
          <div class="item-details">
            <h4>{{ item.product.name }}</h4>
            <p>Size: {{ item.size }} | Color: {{ item.color }}</p>
            <p>Qty: {{ item.quantity }}</p>
          </div>
          <div class="item-price">
            ₹{{ item.product.price * item.quantity }}
          </div>
        </div>
      </div>

      <!-- Order Summary -->
      <div class="order-summary">
        <div class="summary-row">
          <span>Subtotal</span>
          <span>₹{{ cartSummary?.subtotal }}</span>
        </div>
        <div class="summary-row">
          <span>Shipping</span>
          <span>₹{{ cartSummary?.shipping }}</span>
        </div>
        <div class="summary-row">
          <span>Tax</span>
          <span>₹{{ cartSummary?.tax }}</span>
        </div>
        <div class="summary-row total">
          <span>Total</span>
          <span>₹{{ cartSummary?.total }}</span>
        </div>
      </div>

      <!-- Shipping Address -->
      <div class="shipping-address">
        <h4>Shipping Address</h4>
        <p>{{ shippingForm.get('fullName')?.value }}</p>
        <p>{{ shippingForm.get('address1')?.value }}</p>
        <p *ngIf="shippingForm.get('address2')?.value">{{ shippingForm.get('address2')?.value }}</p>
        <p>{{ shippingForm.get('city')?.value }}, {{ shippingForm.get('state')?.value }} {{ shippingForm.get('pincode')?.value }}</p>
        <p>{{ shippingForm.get('phone')?.value }}</p>
      </div>

      <!-- Payment Method -->
      <div class="payment-summary">
        <h4>Payment Method</h4>
        <p>{{ getSelectedPaymentMethodName() }}</p>
      </div>
    </div>

    <!-- Navigation Buttons -->
    <div class="checkout-actions">
      <ion-button 
        *ngIf="currentStep > 1" 
        fill="outline" 
        (click)="previousStep()"
        [disabled]="processing">
        Previous
      </ion-button>
      
      <ion-button 
        *ngIf="currentStep < 3" 
        expand="block" 
        (click)="nextStep()"
        [disabled]="!canProceed()">
        Continue
      </ion-button>
      
      <ion-button 
        *ngIf="currentStep === 3" 
        expand="block" 
        (click)="placeOrder()"
        [disabled]="processing">
        <ion-spinner *ngIf="processing" name="crescent"></ion-spinner>
        {{ processing ? 'Processing...' : 'Place Order' }}
      </ion-button>
    </div>
  </div>
</ion-content>
