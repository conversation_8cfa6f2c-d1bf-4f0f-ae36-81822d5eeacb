{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/trending.service\";\nimport * as i2 from \"../../../../core/services/social-interactions.service\";\nimport * as i3 from \"../../../../core/services/cart.service\";\nimport * as i4 from \"../../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3, 4, 5, 6, 7, 8];\nconst _c1 = () => [1, 2, 3, 4, 5];\nfunction TrendingProductsComponent_div_11_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵelement(1, \"div\", 16);\n    i0.ɵɵelementStart(2, \"div\", 17);\n    i0.ɵɵelement(3, \"div\", 18)(4, \"div\", 19)(5, \"div\", 20);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TrendingProductsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13);\n    i0.ɵɵtemplate(2, TrendingProductsComponent_div_11_div_2_Template, 6, 0, \"div\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction TrendingProductsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"ion-icon\", 22);\n    i0.ɵɵelementStart(2, \"p\", 23);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_12_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 25);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction TrendingProductsComponent_div_13_div_7_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDiscountPercentage(product_r5), \"% OFF \");\n  }\n}\nfunction TrendingProductsComponent_div_13_div_7_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r5.originalPrice));\n  }\n}\nfunction TrendingProductsComponent_div_13_div_7_ion_icon_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ion-icon\", 43);\n  }\n  if (rf & 2) {\n    const star_r6 = ctx.$implicit;\n    const product_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r6 <= product_r5.rating.average);\n    i0.ɵɵproperty(\"name\", star_r6 <= product_r5.rating.average ? \"star\" : \"star-outline\");\n  }\n}\nfunction TrendingProductsComponent_div_13_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_13_div_7_Template_div_click_0_listener() {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onProductClick(product_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 36);\n    i0.ɵɵelement(2, \"img\", 37);\n    i0.ɵɵelementStart(3, \"div\", 38);\n    i0.ɵɵelement(4, \"ion-icon\", 39);\n    i0.ɵɵtext(5, \" Trending \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, TrendingProductsComponent_div_13_div_7_div_6_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementStart(7, \"div\", 41)(8, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_13_div_7_Template_button_click_8_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onLikeProduct(product_r5, $event));\n    });\n    i0.ɵɵelement(9, \"ion-icon\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_13_div_7_Template_button_click_10_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onShareProduct(product_r5, $event));\n    });\n    i0.ɵɵelement(11, \"ion-icon\", 45);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 46)(13, \"div\", 47);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"h3\", 48);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 49)(18, \"span\", 50);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, TrendingProductsComponent_div_13_div_7_span_20_Template, 2, 1, \"span\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 52)(22, \"div\", 53);\n    i0.ɵɵtemplate(23, TrendingProductsComponent_div_13_div_7_ion_icon_23_Template, 1, 3, \"ion-icon\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\", 55);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 56)(27, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_13_div_7_Template_button_click_27_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAddToCart(product_r5, $event));\n    });\n    i0.ɵɵelement(28, \"ion-icon\", 58);\n    i0.ɵɵtext(29, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_13_div_7_Template_button_click_30_listener($event) {\n      const product_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAddToWishlist(product_r5, $event));\n    });\n    i0.ɵɵelement(31, \"ion-icon\", 60);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r5.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r5.images[0].alt || product_r5.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDiscountPercentage(product_r5) > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r1.isProductLiked(product_r5._id));\n    i0.ɵɵattribute(\"aria-label\", \"Like \" + product_r5.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isProductLiked(product_r5._id) ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", \"Share \" + product_r5.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(product_r5.brand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r5.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r5.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r5.originalPrice && product_r5.originalPrice > product_r5.price);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(14, _c1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r5.rating.count, \")\");\n  }\n}\nfunction TrendingProductsComponent_div_13_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_13_button_9_Template_button_click_0_listener() {\n      const i_r8 = i0.ɵɵrestoreView(_r7).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToSlide(i_r8));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r8 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r8 === ctx_r1.currentSlide);\n  }\n}\nfunction TrendingProductsComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_13_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slidePrev());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_13_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slideNext());\n    });\n    i0.ɵɵelement(4, \"ion-icon\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 30)(6, \"div\", 31);\n    i0.ɵɵtemplate(7, TrendingProductsComponent_div_13_div_7_Template, 32, 15, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 33);\n    i0.ɵɵtemplate(9, TrendingProductsComponent_div_13_button_9_Template, 1, 2, \"button\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide >= ctx_r1.maxSlide);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx_r1.translateX + \"px)\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.trendingProducts)(\"ngForTrackBy\", ctx_r1.trackByProductId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.dots);\n  }\n}\nfunction TrendingProductsComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵelement(1, \"ion-icon\", 65);\n    i0.ɵɵelementStart(2, \"h3\", 66);\n    i0.ɵɵtext(3, \"No Trending Products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 67);\n    i0.ɵɵtext(5, \"Check back later for trending items\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class TrendingProductsComponent {\n  get maxSlide() {\n    return Math.max(0, Math.ceil(this.trendingProducts.length / this.visibleCards) - 1);\n  }\n  constructor(trendingService, socialService, cartService, wishlistService, router) {\n    this.trendingService = trendingService;\n    this.socialService = socialService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.router = router;\n    this.trendingProducts = [];\n    this.isLoading = true;\n    this.error = null;\n    this.likedProducts = new Set();\n    this.subscription = new Subscription();\n    // Slider properties\n    this.currentSlide = 0;\n    this.translateX = 0;\n    this.cardWidth = 280; // Width of each product card including margin\n    this.visibleCards = 4; // Number of cards visible at once\n    this.dots = [];\n  }\n  ngOnInit() {\n    this.loadTrendingProducts();\n    this.subscribeTrendingProducts();\n    this.subscribeLikedProducts();\n    this.updateResponsiveSettings();\n    this.generateDots();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n  subscribeTrendingProducts() {\n    this.subscription.add(this.trendingService.trendingProducts$.subscribe(products => {\n      this.trendingProducts = products;\n      this.isLoading = false;\n    }));\n  }\n  subscribeLikedProducts() {\n    this.subscription.add(this.socialService.likedProducts$.subscribe(likedProducts => {\n      this.likedProducts = likedProducts;\n    }));\n  }\n  loadTrendingProducts() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        yield _this.trendingService.loadTrendingProducts(1, 8);\n      } catch (error) {\n        console.error('Error loading trending products:', error);\n        _this.error = 'Failed to load trending products';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onProductClick(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  onLikeProduct(product, event) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const result = yield _this2.socialService.likeProduct(product._id);\n        if (result.success) {\n          console.log(result.message);\n        } else {\n          console.error('Failed to like product:', result.message);\n        }\n      } catch (error) {\n        console.error('Error liking product:', error);\n      }\n    })();\n  }\n  onShareProduct(product, event) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        // For now, copy link to clipboard\n        const productUrl = `${window.location.origin}/product/${product._id}`;\n        yield navigator.clipboard.writeText(productUrl);\n        // Track the share\n        yield _this3.socialService.shareProduct(product._id, {\n          platform: 'copy_link',\n          message: `Check out this amazing ${product.name} from ${product.brand}!`\n        });\n        console.log('Product link copied to clipboard!');\n      } catch (error) {\n        console.error('Error sharing product:', error);\n      }\n    })();\n  }\n  onAddToCart(product, event) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        yield _this4.cartService.addToCart(product._id, 1);\n        console.log('Product added to cart!');\n      } catch (error) {\n        console.error('Error adding to cart:', error);\n      }\n    })();\n  }\n  onAddToWishlist(product, event) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        yield _this5.wishlistService.addToWishlist(product._id);\n        console.log('Product added to wishlist!');\n      } catch (error) {\n        console.error('Error adding to wishlist:', error);\n      }\n    })();\n  }\n  getDiscountPercentage(product) {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n    }\n    return 0;\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  onRetry() {\n    this.loadTrendingProducts();\n  }\n  onViewAll() {\n    this.router.navigate(['/products'], {\n      queryParams: {\n        filter: 'trending'\n      }\n    });\n  }\n  isProductLiked(productId) {\n    return this.likedProducts.has(productId);\n  }\n  trackByProductId(index, product) {\n    return product._id;\n  }\n  // Slider Methods\n  updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width < 576) {\n      this.visibleCards = 1;\n      this.cardWidth = width - 40; // Account for padding\n    } else if (width < 768) {\n      this.visibleCards = 2;\n      this.cardWidth = 280;\n    } else if (width < 992) {\n      this.visibleCards = 3;\n      this.cardWidth = 280;\n    } else {\n      this.visibleCards = 4;\n      this.cardWidth = 280;\n    }\n  }\n  generateDots() {\n    const totalDots = Math.ceil(this.trendingProducts.length / this.visibleCards);\n    this.dots = Array(totalDots).fill(0).map((_, i) => i);\n  }\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateTranslateX();\n    }\n  }\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateTranslateX();\n    }\n  }\n  goToSlide(slideIndex) {\n    this.currentSlide = slideIndex;\n    this.updateTranslateX();\n  }\n  updateTranslateX() {\n    this.translateX = -(this.currentSlide * this.visibleCards * this.cardWidth);\n  }\n  static {\n    this.ɵfac = function TrendingProductsComponent_Factory(t) {\n      return new (t || TrendingProductsComponent)(i0.ɵɵdirectiveInject(i1.TrendingService), i0.ɵɵdirectiveInject(i2.SocialInteractionsService), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService), i0.ɵɵdirectiveInject(i5.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TrendingProductsComponent,\n      selectors: [[\"app-trending-products\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 15,\n      vars: 4,\n      consts: [[1, \"trending-products-container\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"trending-up\", 1, \"title-icon\"], [1, \"section-subtitle\"], [1, \"view-all-btn\", 3, \"click\"], [\"name\", \"chevron-forward\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"products-slider-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-card\"], [1, \"loading-image\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"products-slider-container\"], [1, \"slider-nav\", \"prev-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-back\"], [1, \"slider-nav\", \"next-btn\", 3, \"click\", \"disabled\"], [1, \"slider-wrapper\"], [1, \"slider-track\"], [\"class\", \"product-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"slider-dots\"], [\"class\", \"dot\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"src\", \"alt\"], [1, \"trending-badge\"], [\"name\", \"trending-up\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"action-buttons\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"name\", \"share-outline\"], [1, \"product-info\"], [1, \"product-brand\"], [1, \"product-name\"], [1, \"price-section\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"rating-section\"], [1, \"stars\"], [3, \"name\", \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [1, \"product-actions\"], [1, \"cart-btn\", 3, \"click\"], [\"name\", \"bag-add-outline\"], [1, \"wishlist-btn\", 3, \"click\"], [\"name\", \"heart-outline\"], [1, \"discount-badge\"], [1, \"original-price\"], [1, \"dot\", 3, \"click\"], [1, \"empty-container\"], [\"name\", \"trending-up-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"]],\n      template: function TrendingProductsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n          i0.ɵɵelement(4, \"ion-icon\", 4);\n          i0.ɵɵtext(5, \" Trending Now \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \"Most popular products this week\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function TrendingProductsComponent_Template_button_click_8_listener() {\n            return ctx.onViewAll();\n          });\n          i0.ɵɵtext(9, \" View All \");\n          i0.ɵɵelement(10, \"ion-icon\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(11, TrendingProductsComponent_div_11_Template, 3, 2, \"div\", 8)(12, TrendingProductsComponent_div_12_Template, 7, 1, \"div\", 9)(13, TrendingProductsComponent_div_13_Template, 10, 7, \"div\", 10)(14, TrendingProductsComponent_div_14_Template, 6, 0, \"div\", 11);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.trendingProducts.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.trendingProducts.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i6.NgForOf, i6.NgIf, IonicModule, i7.IonIcon, CarouselModule],\n      styles: [\".trending-products-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #ff6b35;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  margin: 0;\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);\\n  color: white;\\n  border: none;\\n  padding: 12px 20px;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 20px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 200px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  border-radius: 6px;\\n  margin-bottom: 8px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0% {\\n    background-position: 200% 0;\\n  }\\n  100% {\\n    background-position: -200% 0;\\n  }\\n}\\n.error-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #dc3545;\\n  margin-bottom: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #666;\\n  margin-bottom: 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  color: white;\\n  border: none;\\n  padding: 12px 24px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 auto;\\n  transition: background 0.3s ease;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  background: #0056b3;\\n}\\n\\n.products-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  z-index: 10;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  border: none;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: rgba(0, 0, 0, 0.9);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .prev-btn[_ngcontent-%COMP%] {\\n  left: -20px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .next-btn[_ngcontent-%COMP%] {\\n  right: -20px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-wrapper[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  margin: 0 20px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-wrapper[_ngcontent-%COMP%]   .slider-track[_ngcontent-%COMP%] {\\n  display: flex;\\n  transition: transform 0.3s ease-in-out;\\n  gap: 20px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-dots[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 8px;\\n  margin-top: 20px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-dots[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  border: none;\\n  background: #ddd;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-dots[_ngcontent-%COMP%]   .dot.active[_ngcontent-%COMP%] {\\n  background: #ff6b35;\\n  transform: scale(1.2);\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-dots[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:hover {\\n  background: #ff6b35;\\n}\\n\\n.product-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.product-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n}\\n.product-card[_ngcontent-%COMP%]:hover   .action-buttons[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n\\n.product-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 200px;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  left: 12px;\\n  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);\\n  color: white;\\n  padding: 6px 12px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  background: #dc3545;\\n  color: white;\\n  padding: 6px 10px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 700;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  right: 12px;\\n  transform: translateY(-50%);\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  opacity: 0;\\n  transition: all 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: #333;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  transform: scale(1.1);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%] {\\n  background: rgba(220, 53, 69, 0.1);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.product-info[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  text-transform: uppercase;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 4px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1a1a1a;\\n  margin: 0 0 12px 0;\\n  line-height: 1.4;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 12px;\\n}\\n.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: #ff6b35;\\n}\\n.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #999;\\n  text-decoration: line-through;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #ddd;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon.filled[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);\\n  color: white;\\n  border: none;\\n  padding: 12px 16px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%] {\\n  width: 44px;\\n  height: 44px;\\n  border: 2px solid #e9ecef;\\n  background: white;\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: #666;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover {\\n  border-color: #ff6b35;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n}\\n\\n.empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: #ccc;\\n  margin-bottom: 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 8px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n}\\n\\n@media (max-width: 768px) {\\n  .trending-products-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .section-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 16px;\\n  }\\n  .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n    align-self: flex-end;\\n  }\\n  .products-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n    gap: 16px;\\n  }\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subscription", "IonicModule", "CarouselModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "TrendingProductsComponent_div_11_div_2_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵtext", "ɵɵlistener", "TrendingProductsComponent_div_12_Template_button_click_4_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onRetry", "ɵɵtextInterpolate", "error", "ɵɵtextInterpolate1", "getDiscountPercentage", "product_r5", "formatPrice", "originalPrice", "ɵɵclassProp", "star_r6", "rating", "average", "TrendingProductsComponent_div_13_div_7_Template_div_click_0_listener", "_r4", "$implicit", "onProductClick", "TrendingProductsComponent_div_13_div_7_div_6_Template", "TrendingProductsComponent_div_13_div_7_Template_button_click_8_listener", "$event", "onLikeProduct", "TrendingProductsComponent_div_13_div_7_Template_button_click_10_listener", "onShareProduct", "TrendingProductsComponent_div_13_div_7_span_20_Template", "TrendingProductsComponent_div_13_div_7_ion_icon_23_Template", "TrendingProductsComponent_div_13_div_7_Template_button_click_27_listener", "onAddToCart", "TrendingProductsComponent_div_13_div_7_Template_button_click_30_listener", "onAddToWishlist", "images", "url", "ɵɵsanitizeUrl", "alt", "name", "isProductLiked", "_id", "brand", "price", "_c1", "count", "TrendingProductsComponent_div_13_button_9_Template_button_click_0_listener", "i_r8", "_r7", "index", "goToSlide", "currentSlide", "TrendingProductsComponent_div_13_Template_button_click_1_listener", "_r3", "slidePrev", "TrendingProductsComponent_div_13_Template_button_click_3_listener", "slideNext", "TrendingProductsComponent_div_13_div_7_Template", "TrendingProductsComponent_div_13_button_9_Template", "maxSlide", "ɵɵstyleProp", "translateX", "trendingProducts", "trackByProductId", "dots", "TrendingProductsComponent", "Math", "max", "ceil", "length", "visibleCards", "constructor", "trendingService", "socialService", "cartService", "wishlistService", "router", "isLoading", "likedProducts", "Set", "subscription", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "loadTrendingProducts", "subscribeTrendingProducts", "subscribeLikedProducts", "updateResponsiveSettings", "generateDots", "ngOnDestroy", "unsubscribe", "add", "trendingProducts$", "subscribe", "products", "likedProducts$", "_this", "_asyncToGenerator", "console", "product", "navigate", "event", "_this2", "stopPropagation", "result", "likeProduct", "success", "log", "message", "_this3", "productUrl", "window", "location", "origin", "navigator", "clipboard", "writeText", "shareProduct", "platform", "_this4", "addToCart", "_this5", "addToWishlist", "round", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "onViewAll", "queryParams", "filter", "productId", "has", "width", "innerWidth", "totalDots", "Array", "fill", "map", "_", "i", "updateTranslateX", "slideIndex", "ɵɵdirectiveInject", "i1", "TrendingService", "i2", "SocialInteractionsService", "i3", "CartService", "i4", "WishlistService", "i5", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TrendingProductsComponent_Template", "rf", "ctx", "TrendingProductsComponent_Template_button_click_8_listener", "TrendingProductsComponent_div_11_Template", "TrendingProductsComponent_div_12_Template", "TrendingProductsComponent_div_13_Template", "TrendingProductsComponent_div_14_Template", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i7", "IonIcon", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\trending-products\\trending-products.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\trending-products\\trending-products.component.html"], "sourcesContent": ["import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { TrendingService } from '../../../../core/services/trending.service';\nimport { Product } from '../../../../core/models/product.model';\nimport { SocialInteractionsService } from '../../../../core/services/social-interactions.service';\nimport { CartService } from '../../../../core/services/cart.service';\nimport { WishlistService } from '../../../../core/services/wishlist.service';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\n\n@Component({\n  selector: 'app-trending-products',\n  standalone: true,\n  imports: [CommonModule, IonicModule, CarouselModule],\n  templateUrl: './trending-products.component.html',\n  styleUrls: ['./trending-products.component.scss']\n})\nexport class TrendingProductsComponent implements OnInit, OnDestroy {\n  trendingProducts: Product[] = [];\n  isLoading = true;\n  error: string | null = null;\n  likedProducts = new Set<string>();\n  private subscription: Subscription = new Subscription();\n\n  // Slider properties\n  currentSlide = 0;\n  translateX = 0;\n  cardWidth = 280; // Width of each product card including margin\n  visibleCards = 4; // Number of cards visible at once\n  dots: number[] = [];\n\n  get maxSlide(): number {\n    return Math.max(0, Math.ceil(this.trendingProducts.length / this.visibleCards) - 1);\n  }\n\n  constructor(\n    private trendingService: TrendingService,\n    private socialService: SocialInteractionsService,\n    private cartService: CartService,\n    private wishlistService: WishlistService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadTrendingProducts();\n    this.subscribeTrendingProducts();\n    this.subscribeLikedProducts();\n    this.updateResponsiveSettings();\n    this.generateDots();\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n\n  private subscribeTrendingProducts() {\n    this.subscription.add(\n      this.trendingService.trendingProducts$.subscribe(products => {\n        this.trendingProducts = products;\n        this.isLoading = false;\n      })\n    );\n  }\n\n  private subscribeLikedProducts() {\n    this.subscription.add(\n      this.socialService.likedProducts$.subscribe(likedProducts => {\n        this.likedProducts = likedProducts;\n      })\n    );\n  }\n\n  private async loadTrendingProducts() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      await this.trendingService.loadTrendingProducts(1, 8);\n    } catch (error) {\n      console.error('Error loading trending products:', error);\n      this.error = 'Failed to load trending products';\n      this.isLoading = false;\n    }\n  }\n\n  onProductClick(product: Product) {\n    this.router.navigate(['/product', product._id]);\n  }\n\n  async onLikeProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      const result = await this.socialService.likeProduct(product._id);\n      if (result.success) {\n        console.log(result.message);\n      } else {\n        console.error('Failed to like product:', result.message);\n      }\n    } catch (error) {\n      console.error('Error liking product:', error);\n    }\n  }\n\n  async onShareProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      // For now, copy link to clipboard\n      const productUrl = `${window.location.origin}/product/${product._id}`;\n      await navigator.clipboard.writeText(productUrl);\n\n      // Track the share\n      await this.socialService.shareProduct(product._id, {\n        platform: 'copy_link',\n        message: `Check out this amazing ${product.name} from ${product.brand}!`\n      });\n\n      console.log('Product link copied to clipboard!');\n    } catch (error) {\n      console.error('Error sharing product:', error);\n    }\n  }\n\n  async onAddToCart(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      await this.cartService.addToCart(product._id, 1);\n      console.log('Product added to cart!');\n    } catch (error) {\n      console.error('Error adding to cart:', error);\n    }\n  }\n\n  async onAddToWishlist(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      await this.wishlistService.addToWishlist(product._id);\n      console.log('Product added to wishlist!');\n    } catch (error) {\n      console.error('Error adding to wishlist:', error);\n    }\n  }\n\n  getDiscountPercentage(product: Product): number {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);\n    }\n    return 0;\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n\n  onRetry() {\n    this.loadTrendingProducts();\n  }\n\n  onViewAll() {\n    this.router.navigate(['/products'], {\n      queryParams: { filter: 'trending' }\n    });\n  }\n\n  isProductLiked(productId: string): boolean {\n    return this.likedProducts.has(productId);\n  }\n\n  trackByProductId(index: number, product: Product): string {\n    return product._id;\n  }\n\n  // Slider Methods\n  updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width < 576) {\n      this.visibleCards = 1;\n      this.cardWidth = width - 40; // Account for padding\n    } else if (width < 768) {\n      this.visibleCards = 2;\n      this.cardWidth = 280;\n    } else if (width < 992) {\n      this.visibleCards = 3;\n      this.cardWidth = 280;\n    } else {\n      this.visibleCards = 4;\n      this.cardWidth = 280;\n    }\n  }\n\n  generateDots() {\n    const totalDots = Math.ceil(this.trendingProducts.length / this.visibleCards);\n    this.dots = Array(totalDots).fill(0).map((_, i) => i);\n  }\n\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateTranslateX();\n    }\n  }\n\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateTranslateX();\n    }\n  }\n\n  goToSlide(slideIndex: number) {\n    this.currentSlide = slideIndex;\n    this.updateTranslateX();\n  }\n\n  private updateTranslateX() {\n    this.translateX = -(this.currentSlide * this.visibleCards * this.cardWidth);\n  }\n}\n", "<div class=\"trending-products-container\">\n  <!-- Header -->\n  <div class=\"section-header\">\n    <div class=\"header-content\">\n      <h2 class=\"section-title\">\n        <ion-icon name=\"trending-up\" class=\"title-icon\"></ion-icon>\n        Trending Now\n      </h2>\n      <p class=\"section-subtitle\">Most popular products this week</p>\n    </div>\n    <button class=\"view-all-btn\" (click)=\"onViewAll()\">\n      View All\n      <ion-icon name=\"chevron-forward\"></ion-icon>\n    </button>\n  </div>\n\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <div class=\"loading-grid\">\n      <div *ngFor=\"let item of [1,2,3,4,5,6,7,8]\" class=\"loading-card\">\n        <div class=\"loading-image\"></div>\n        <div class=\"loading-content\">\n          <div class=\"loading-line short\"></div>\n          <div class=\"loading-line medium\"></div>\n          <div class=\"loading-line long\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error && !isLoading\" class=\"error-container\">\n    <ion-icon name=\"alert-circle\" class=\"error-icon\"></ion-icon>\n    <p class=\"error-message\">{{ error }}</p>\n    <button class=\"retry-btn\" (click)=\"onRetry()\">\n      <ion-icon name=\"refresh\"></ion-icon>\n      Try Again\n    </button>\n  </div>\n\n  <!-- Products Slider -->\n  <div *ngIf=\"!isLoading && !error && trendingProducts.length > 0\" class=\"products-slider-container\">\n    <!-- Navigation Buttons -->\n    <button class=\"slider-nav prev-btn\" (click)=\"slidePrev()\" [disabled]=\"currentSlide === 0\">\n      <ion-icon name=\"chevron-back\"></ion-icon>\n    </button>\n    <button class=\"slider-nav next-btn\" (click)=\"slideNext()\" [disabled]=\"currentSlide >= maxSlide\">\n      <ion-icon name=\"chevron-forward\"></ion-icon>\n    </button>\n\n    <!-- Slider Wrapper -->\n    <div class=\"slider-wrapper\">\n      <div class=\"slider-track\" [style.transform]=\"'translateX(' + translateX + 'px)'\">\n        <div\n          *ngFor=\"let product of trendingProducts; trackBy: trackByProductId\"\n          class=\"product-card\"\n          (click)=\"onProductClick(product)\"\n        >\n\n      <div class=\"product-image-container\">\n        <img \n          [src]=\"product.images[0].url\"\n          [alt]=\"product.images[0].alt || product.name\"\n          class=\"product-image\"\n          loading=\"lazy\"\n        />\n        \n      \n        <div class=\"trending-badge\">\n          <ion-icon name=\"trending-up\"></ion-icon>\n          Trending\n        </div>\n\n      \n        <div *ngIf=\"getDiscountPercentage(product) > 0\" class=\"discount-badge\">\n          {{ getDiscountPercentage(product) }}% OFF\n        </div>\n\n       \n        <div class=\"action-buttons\">\n          <button\n            class=\"action-btn like-btn\"\n            [class.liked]=\"isProductLiked(product._id)\"\n            (click)=\"onLikeProduct(product, $event)\"\n            [attr.aria-label]=\"'Like ' + product.name\"\n          >\n            <ion-icon [name]=\"isProductLiked(product._id) ? 'heart' : 'heart-outline'\"></ion-icon>\n          </button>\n          <button \n            class=\"action-btn share-btn\" \n            (click)=\"onShareProduct(product, $event)\"\n            [attr.aria-label]=\"'Share ' + product.name\"\n          >\n            <ion-icon name=\"share-outline\"></ion-icon>\n          </button>\n        </div>\n      </div>\n\n    \n      <div class=\"product-info\">\n        <div class=\"product-brand\">{{ product.brand }}</div>\n        <h3 class=\"product-name\">{{ product.name }}</h3>\n        \n      \n        <div class=\"price-section\">\n          <span class=\"current-price\">{{ formatPrice(product.price) }}</span>\n          <span *ngIf=\"product.originalPrice && product.originalPrice > product.price\" \n                class=\"original-price\">{{ formatPrice(product.originalPrice) }}</span>\n        </div>\n\n       \n        <div class=\"rating-section\">\n          <div class=\"stars\">\n            <ion-icon \n              *ngFor=\"let star of [1,2,3,4,5]\" \n              [name]=\"star <= product.rating.average ? 'star' : 'star-outline'\"\n              [class.filled]=\"star <= product.rating.average\"\n            ></ion-icon>\n          </div>\n          <span class=\"rating-text\">({{ product.rating.count }})</span>\n        </div>\n\n        <!-- Action Buttons -->\n        <div class=\"product-actions\">\n          <button \n            class=\"cart-btn\" \n            (click)=\"onAddToCart(product, $event)\"\n          >\n            <ion-icon name=\"bag-add-outline\"></ion-icon>\n            Add to Cart\n          </button>\n          <button \n            class=\"wishlist-btn\" \n            (click)=\"onAddToWishlist(product, $event)\"\n          >\n            <ion-icon name=\"heart-outline\"></ion-icon>\n          </button>\n        </div>\n      </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Dots Indicator -->\n    <div class=\"slider-dots\">\n      <button\n        *ngFor=\"let dot of dots; let i = index\"\n        class=\"dot\"\n        [class.active]=\"i === currentSlide\"\n        (click)=\"goToSlide(i)\"\n      ></button>\n    </div>\n  </div>\n\n  <!-- Empty State -->\n  <div *ngIf=\"!isLoading && !error && trendingProducts.length === 0\" class=\"empty-container\">\n    <ion-icon name=\"trending-up-outline\" class=\"empty-icon\"></ion-icon>\n    <h3 class=\"empty-title\">No Trending Products</h3>\n    <p class=\"empty-message\">Check back later for trending items</p>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AAMnC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,oBAAoB;;;;;;;;;;;;;ICS7CC,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAE,SAAA,cAAiC;IACjCF,EAAA,CAAAC,cAAA,cAA6B;IAG3BD,EAFA,CAAAE,SAAA,cAAsC,cACC,cACF;IAEzCF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IARRH,EADF,CAAAC,cAAA,cAAiD,cACrB;IACxBD,EAAA,CAAAI,UAAA,IAAAC,+CAAA,kBAAiE;IASrEL,EADE,CAAAG,YAAA,EAAM,EACF;;;IAToBH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAoB;;;;;;IAY9CT,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,SAAA,mBAA4D;IAC5DF,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAU,MAAA,GAAW;IAAAV,EAAA,CAAAG,YAAA,EAAI;IACxCH,EAAA,CAAAC,cAAA,iBAA8C;IAApBD,EAAA,CAAAW,UAAA,mBAAAC,kEAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAG,OAAA,EAAS;IAAA,EAAC;IAC3ClB,EAAA,CAAAE,SAAA,mBAAoC;IACpCF,EAAA,CAAAU,MAAA,kBACF;IACFV,EADE,CAAAG,YAAA,EAAS,EACL;;;;IALqBH,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAmB,iBAAA,CAAAJ,MAAA,CAAAK,KAAA,CAAW;;;;;IAyChCpB,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAG,YAAA,EAAM;;;;;IADJH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAqB,kBAAA,MAAAN,MAAA,CAAAO,qBAAA,CAAAC,UAAA,YACF;;;;;IA8BEvB,EAAA,CAAAC,cAAA,eAC6B;IAAAD,EAAA,CAAAU,MAAA,GAAwC;IAAAV,EAAA,CAAAG,YAAA,EAAO;;;;;IAA/CH,EAAA,CAAAM,SAAA,EAAwC;IAAxCN,EAAA,CAAAmB,iBAAA,CAAAJ,MAAA,CAAAS,WAAA,CAAAD,UAAA,CAAAE,aAAA,EAAwC;;;;;IAMnEzB,EAAA,CAAAE,SAAA,mBAIY;;;;;IADVF,EAAA,CAAA0B,WAAA,WAAAC,OAAA,IAAAJ,UAAA,CAAAK,MAAA,CAAAC,OAAA,CAA+C;IAD/C7B,EAAA,CAAAO,UAAA,SAAAoB,OAAA,IAAAJ,UAAA,CAAAK,MAAA,CAAAC,OAAA,2BAAiE;;;;;;IA9DvE7B,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAW,UAAA,mBAAAmB,qEAAA;MAAA,MAAAP,UAAA,GAAAvB,EAAA,CAAAa,aAAA,CAAAkB,GAAA,EAAAC,SAAA;MAAA,MAAAjB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAkB,cAAA,CAAAV,UAAA,CAAuB;IAAA,EAAC;IAGrCvB,EAAA,CAAAC,cAAA,cAAqC;IACnCD,EAAA,CAAAE,SAAA,cAKE;IAGFF,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAE,SAAA,mBAAwC;IACxCF,EAAA,CAAAU,MAAA,iBACF;IAAAV,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAI,UAAA,IAAA8B,qDAAA,kBAAuE;IAMrElC,EADF,CAAAC,cAAA,cAA4B,iBAMzB;IAFCD,EAAA,CAAAW,UAAA,mBAAAwB,wEAAAC,MAAA;MAAA,MAAAb,UAAA,GAAAvB,EAAA,CAAAa,aAAA,CAAAkB,GAAA,EAAAC,SAAA;MAAA,MAAAjB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAsB,aAAA,CAAAd,UAAA,EAAAa,MAAA,CAA8B;IAAA,EAAC;IAGxCpC,EAAA,CAAAE,SAAA,mBAAsF;IACxFF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAW,UAAA,mBAAA2B,yEAAAF,MAAA;MAAA,MAAAb,UAAA,GAAAvB,EAAA,CAAAa,aAAA,CAAAkB,GAAA,EAAAC,SAAA;MAAA,MAAAjB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAwB,cAAA,CAAAhB,UAAA,EAAAa,MAAA,CAA+B;IAAA,EAAC;IAGzCpC,EAAA,CAAAE,SAAA,oBAA0C;IAGhDF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAIJH,EADF,CAAAC,cAAA,eAA0B,eACG;IAAAD,EAAA,CAAAU,MAAA,IAAmB;IAAAV,EAAA,CAAAG,YAAA,EAAM;IACpDH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAU,MAAA,IAAkB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAI9CH,EADF,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAU,MAAA,IAAgC;IAAAV,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAI,UAAA,KAAAoC,uDAAA,mBAC6B;IAC/BxC,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAA4B,eACP;IACjBD,EAAA,CAAAI,UAAA,KAAAqC,2DAAA,uBAIC;IACHzC,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAU,MAAA,IAA4B;IACxDV,EADwD,CAAAG,YAAA,EAAO,EACzD;IAIJH,EADF,CAAAC,cAAA,eAA6B,kBAI1B;IADCD,EAAA,CAAAW,UAAA,mBAAA+B,yEAAAN,MAAA;MAAA,MAAAb,UAAA,GAAAvB,EAAA,CAAAa,aAAA,CAAAkB,GAAA,EAAAC,SAAA;MAAA,MAAAjB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAA4B,WAAA,CAAApB,UAAA,EAAAa,MAAA,CAA4B;IAAA,EAAC;IAEtCpC,EAAA,CAAAE,SAAA,oBAA4C;IAC5CF,EAAA,CAAAU,MAAA,qBACF;IAAAV,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGC;IADCD,EAAA,CAAAW,UAAA,mBAAAiC,yEAAAR,MAAA;MAAA,MAAAb,UAAA,GAAAvB,EAAA,CAAAa,aAAA,CAAAkB,GAAA,EAAAC,SAAA;MAAA,MAAAjB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAA8B,eAAA,CAAAtB,UAAA,EAAAa,MAAA,CAAgC;IAAA,EAAC;IAE1CpC,EAAA,CAAAE,SAAA,oBAA0C;IAI9CF,EAHE,CAAAG,YAAA,EAAS,EACL,EACF,EACE;;;;;IA9EJH,EAAA,CAAAM,SAAA,GAA6B;IAC7BN,EADA,CAAAO,UAAA,QAAAgB,UAAA,CAAAuB,MAAA,IAAAC,GAAA,EAAA/C,EAAA,CAAAgD,aAAA,CAA6B,QAAAzB,UAAA,CAAAuB,MAAA,IAAAG,GAAA,IAAA1B,UAAA,CAAA2B,IAAA,CACgB;IAYzClD,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAO,UAAA,SAAAQ,MAAA,CAAAO,qBAAA,CAAAC,UAAA,MAAwC;IAQ1CvB,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAA0B,WAAA,UAAAX,MAAA,CAAAoC,cAAA,CAAA5B,UAAA,CAAA6B,GAAA,EAA2C;;IAIjCpD,EAAA,CAAAM,SAAA,EAAgE;IAAhEN,EAAA,CAAAO,UAAA,SAAAQ,MAAA,CAAAoC,cAAA,CAAA5B,UAAA,CAAA6B,GAAA,8BAAgE;IAK1EpD,EAAA,CAAAM,SAAA,EAA2C;;IASpBN,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAmB,iBAAA,CAAAI,UAAA,CAAA8B,KAAA,CAAmB;IACrBrD,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAmB,iBAAA,CAAAI,UAAA,CAAA2B,IAAA,CAAkB;IAIblD,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAmB,iBAAA,CAAAJ,MAAA,CAAAS,WAAA,CAAAD,UAAA,CAAA+B,KAAA,EAAgC;IACrDtD,EAAA,CAAAM,SAAA,EAAoE;IAApEN,EAAA,CAAAO,UAAA,SAAAgB,UAAA,CAAAE,aAAA,IAAAF,UAAA,CAAAE,aAAA,GAAAF,UAAA,CAAA+B,KAAA,CAAoE;IAQtDtD,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,KAAA+C,GAAA,EAAc;IAKTvD,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAqB,kBAAA,MAAAE,UAAA,CAAAK,MAAA,CAAA4B,KAAA,MAA4B;;;;;;IA0B1DxD,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAW,UAAA,mBAAA8C,2EAAA;MAAA,MAAAC,IAAA,GAAA1D,EAAA,CAAAa,aAAA,CAAA8C,GAAA,EAAAC,KAAA;MAAA,MAAA7C,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAA8C,SAAA,CAAAH,IAAA,CAAY;IAAA,EAAC;IACvB1D,EAAA,CAAAG,YAAA,EAAS;;;;;IAFRH,EAAA,CAAA0B,WAAA,WAAAgC,IAAA,KAAA3C,MAAA,CAAA+C,YAAA,CAAmC;;;;;;IAzGvC9D,EAFF,CAAAC,cAAA,cAAmG,iBAEP;IAAtDD,EAAA,CAAAW,UAAA,mBAAAoD,kEAAA;MAAA/D,EAAA,CAAAa,aAAA,CAAAmD,GAAA;MAAA,MAAAjD,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAkD,SAAA,EAAW;IAAA,EAAC;IACvDjE,EAAA,CAAAE,SAAA,mBAAyC;IAC3CF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAgG;IAA5DD,EAAA,CAAAW,UAAA,mBAAAuD,kEAAA;MAAAlE,EAAA,CAAAa,aAAA,CAAAmD,GAAA;MAAA,MAAAjD,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAoD,SAAA,EAAW;IAAA,EAAC;IACvDnE,EAAA,CAAAE,SAAA,kBAA4C;IAC9CF,EAAA,CAAAG,YAAA,EAAS;IAIPH,EADF,CAAAC,cAAA,cAA4B,cACuD;IAC/ED,EAAA,CAAAI,UAAA,IAAAgE,+CAAA,oBAIC;IAoFLpE,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAI,UAAA,IAAAiE,kDAAA,qBAKC;IAELrE,EADE,CAAAG,YAAA,EAAM,EACF;;;;IA7GsDH,EAAA,CAAAM,SAAA,EAA+B;IAA/BN,EAAA,CAAAO,UAAA,aAAAQ,MAAA,CAAA+C,YAAA,OAA+B;IAG/B9D,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAAO,UAAA,aAAAQ,MAAA,CAAA+C,YAAA,IAAA/C,MAAA,CAAAuD,QAAA,CAAqC;IAMnEtE,EAAA,CAAAM,SAAA,GAAsD;IAAtDN,EAAA,CAAAuE,WAAA,8BAAAxD,MAAA,CAAAyD,UAAA,SAAsD;IAExDxE,EAAA,CAAAM,SAAA,EAAqB;IAAAN,EAArB,CAAAO,UAAA,YAAAQ,MAAA,CAAA0D,gBAAA,CAAqB,iBAAA1D,MAAA,CAAA2D,gBAAA,CAAyB;IA4FpD1E,EAAA,CAAAM,SAAA,GAAS;IAATN,EAAA,CAAAO,UAAA,YAAAQ,MAAA,CAAA4D,IAAA,CAAS;;;;;IAS/B3E,EAAA,CAAAC,cAAA,cAA2F;IACzFD,EAAA,CAAAE,SAAA,mBAAmE;IACnEF,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAU,MAAA,2BAAoB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAU,MAAA,0CAAmC;IAC9DV,EAD8D,CAAAG,YAAA,EAAI,EAC5D;;;AD5IR,OAAM,MAAOyE,yBAAyB;EAcpC,IAAIN,QAAQA,CAAA;IACV,OAAOO,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,IAAI,CAAC,IAAI,CAACN,gBAAgB,CAACO,MAAM,GAAG,IAAI,CAACC,YAAY,CAAC,GAAG,CAAC,CAAC;EACrF;EAEAC,YACUC,eAAgC,EAChCC,aAAwC,EACxCC,WAAwB,EACxBC,eAAgC,EAChCC,MAAc;IAJd,KAAAJ,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IAtBhB,KAAAd,gBAAgB,GAAc,EAAE;IAChC,KAAAe,SAAS,GAAG,IAAI;IAChB,KAAApE,KAAK,GAAkB,IAAI;IAC3B,KAAAqE,aAAa,GAAG,IAAIC,GAAG,EAAU;IACzB,KAAAC,YAAY,GAAiB,IAAI9F,YAAY,EAAE;IAEvD;IACA,KAAAiE,YAAY,GAAG,CAAC;IAChB,KAAAU,UAAU,GAAG,CAAC;IACd,KAAAoB,SAAS,GAAG,GAAG,CAAC,CAAC;IACjB,KAAAX,YAAY,GAAG,CAAC,CAAC,CAAC;IAClB,KAAAN,IAAI,GAAa,EAAE;EAYhB;EAEHkB,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,yBAAyB,EAAE;IAChC,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,wBAAwB,EAAE;IAC/B,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACR,YAAY,CAACS,WAAW,EAAE;EACjC;EAEQL,yBAAyBA,CAAA;IAC/B,IAAI,CAACJ,YAAY,CAACU,GAAG,CACnB,IAAI,CAAClB,eAAe,CAACmB,iBAAiB,CAACC,SAAS,CAACC,QAAQ,IAAG;MAC1D,IAAI,CAAC/B,gBAAgB,GAAG+B,QAAQ;MAChC,IAAI,CAAChB,SAAS,GAAG,KAAK;IACxB,CAAC,CAAC,CACH;EACH;EAEQQ,sBAAsBA,CAAA;IAC5B,IAAI,CAACL,YAAY,CAACU,GAAG,CACnB,IAAI,CAACjB,aAAa,CAACqB,cAAc,CAACF,SAAS,CAACd,aAAa,IAAG;MAC1D,IAAI,CAACA,aAAa,GAAGA,aAAa;IACpC,CAAC,CAAC,CACH;EACH;EAEcK,oBAAoBA,CAAA;IAAA,IAAAY,KAAA;IAAA,OAAAC,iBAAA;MAChC,IAAI;QACFD,KAAI,CAAClB,SAAS,GAAG,IAAI;QACrBkB,KAAI,CAACtF,KAAK,GAAG,IAAI;QACjB,MAAMsF,KAAI,CAACvB,eAAe,CAACW,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC;OACtD,CAAC,OAAO1E,KAAK,EAAE;QACdwF,OAAO,CAACxF,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxDsF,KAAI,CAACtF,KAAK,GAAG,kCAAkC;QAC/CsF,KAAI,CAAClB,SAAS,GAAG,KAAK;;IACvB;EACH;EAEAvD,cAAcA,CAAC4E,OAAgB;IAC7B,IAAI,CAACtB,MAAM,CAACuB,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAACzD,GAAG,CAAC,CAAC;EACjD;EAEMf,aAAaA,CAACwE,OAAgB,EAAEE,KAAY;IAAA,IAAAC,MAAA;IAAA,OAAAL,iBAAA;MAChDI,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMC,MAAM,SAASF,MAAI,CAAC5B,aAAa,CAAC+B,WAAW,CAACN,OAAO,CAACzD,GAAG,CAAC;QAChE,IAAI8D,MAAM,CAACE,OAAO,EAAE;UAClBR,OAAO,CAACS,GAAG,CAACH,MAAM,CAACI,OAAO,CAAC;SAC5B,MAAM;UACLV,OAAO,CAACxF,KAAK,CAAC,yBAAyB,EAAE8F,MAAM,CAACI,OAAO,CAAC;;OAE3D,CAAC,OAAOlG,KAAK,EAAE;QACdwF,OAAO,CAACxF,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;IAC9C;EACH;EAEMmB,cAAcA,CAACsE,OAAgB,EAAEE,KAAY;IAAA,IAAAQ,MAAA;IAAA,OAAAZ,iBAAA;MACjDI,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF;QACA,MAAMO,UAAU,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,YAAYd,OAAO,CAACzD,GAAG,EAAE;QACrE,MAAMwE,SAAS,CAACC,SAAS,CAACC,SAAS,CAACN,UAAU,CAAC;QAE/C;QACA,MAAMD,MAAI,CAACnC,aAAa,CAAC2C,YAAY,CAAClB,OAAO,CAACzD,GAAG,EAAE;UACjD4E,QAAQ,EAAE,WAAW;UACrBV,OAAO,EAAE,0BAA0BT,OAAO,CAAC3D,IAAI,SAAS2D,OAAO,CAACxD,KAAK;SACtE,CAAC;QAEFuD,OAAO,CAACS,GAAG,CAAC,mCAAmC,CAAC;OACjD,CAAC,OAAOjG,KAAK,EAAE;QACdwF,OAAO,CAACxF,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;IAC/C;EACH;EAEMuB,WAAWA,CAACkE,OAAgB,EAAEE,KAAY;IAAA,IAAAkB,MAAA;IAAA,OAAAtB,iBAAA;MAC9CI,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMgB,MAAI,CAAC5C,WAAW,CAAC6C,SAAS,CAACrB,OAAO,CAACzD,GAAG,EAAE,CAAC,CAAC;QAChDwD,OAAO,CAACS,GAAG,CAAC,wBAAwB,CAAC;OACtC,CAAC,OAAOjG,KAAK,EAAE;QACdwF,OAAO,CAACxF,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;IAC9C;EACH;EAEMyB,eAAeA,CAACgE,OAAgB,EAAEE,KAAY;IAAA,IAAAoB,MAAA;IAAA,OAAAxB,iBAAA;MAClDI,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMkB,MAAI,CAAC7C,eAAe,CAAC8C,aAAa,CAACvB,OAAO,CAACzD,GAAG,CAAC;QACrDwD,OAAO,CAACS,GAAG,CAAC,4BAA4B,CAAC;OAC1C,CAAC,OAAOjG,KAAK,EAAE;QACdwF,OAAO,CAACxF,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;;IAClD;EACH;EAEAE,qBAAqBA,CAACuF,OAAgB;IACpC,IAAIA,OAAO,CAACpF,aAAa,IAAIoF,OAAO,CAACpF,aAAa,GAAGoF,OAAO,CAACvD,KAAK,EAAE;MAClE,OAAOuB,IAAI,CAACwD,KAAK,CAAE,CAACxB,OAAO,CAACpF,aAAa,GAAGoF,OAAO,CAACvD,KAAK,IAAIuD,OAAO,CAACpF,aAAa,GAAI,GAAG,CAAC;;IAE5F,OAAO,CAAC;EACV;EAEAD,WAAWA,CAAC8B,KAAa;IACvB,OAAO,IAAIgF,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACrF,KAAK,CAAC;EAClB;EAEApC,OAAOA,CAAA;IACL,IAAI,CAAC4E,oBAAoB,EAAE;EAC7B;EAEA8C,SAASA,CAAA;IACP,IAAI,CAACrD,MAAM,CAACuB,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;MAClC+B,WAAW,EAAE;QAAEC,MAAM,EAAE;MAAU;KAClC,CAAC;EACJ;EAEA3F,cAAcA,CAAC4F,SAAiB;IAC9B,OAAO,IAAI,CAACtD,aAAa,CAACuD,GAAG,CAACD,SAAS,CAAC;EAC1C;EAEArE,gBAAgBA,CAACd,KAAa,EAAEiD,OAAgB;IAC9C,OAAOA,OAAO,CAACzD,GAAG;EACpB;EAEA;EACA6C,wBAAwBA,CAAA;IACtB,MAAMgD,KAAK,GAAGxB,MAAM,CAACyB,UAAU;IAC/B,IAAID,KAAK,GAAG,GAAG,EAAE;MACf,IAAI,CAAChE,YAAY,GAAG,CAAC;MACrB,IAAI,CAACW,SAAS,GAAGqD,KAAK,GAAG,EAAE,CAAC,CAAC;KAC9B,MAAM,IAAIA,KAAK,GAAG,GAAG,EAAE;MACtB,IAAI,CAAChE,YAAY,GAAG,CAAC;MACrB,IAAI,CAACW,SAAS,GAAG,GAAG;KACrB,MAAM,IAAIqD,KAAK,GAAG,GAAG,EAAE;MACtB,IAAI,CAAChE,YAAY,GAAG,CAAC;MACrB,IAAI,CAACW,SAAS,GAAG,GAAG;KACrB,MAAM;MACL,IAAI,CAACX,YAAY,GAAG,CAAC;MACrB,IAAI,CAACW,SAAS,GAAG,GAAG;;EAExB;EAEAM,YAAYA,CAAA;IACV,MAAMiD,SAAS,GAAGtE,IAAI,CAACE,IAAI,CAAC,IAAI,CAACN,gBAAgB,CAACO,MAAM,GAAG,IAAI,CAACC,YAAY,CAAC;IAC7E,IAAI,CAACN,IAAI,GAAGyE,KAAK,CAACD,SAAS,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC;EACvD;EAEAvF,SAASA,CAAA;IACP,IAAI,IAAI,CAACH,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,IAAI,CAAC2F,gBAAgB,EAAE;;EAE3B;EAEAtF,SAASA,CAAA;IACP,IAAI,IAAI,CAACL,YAAY,GAAG,IAAI,CAACQ,QAAQ,EAAE;MACrC,IAAI,CAACR,YAAY,EAAE;MACnB,IAAI,CAAC2F,gBAAgB,EAAE;;EAE3B;EAEA5F,SAASA,CAAC6F,UAAkB;IAC1B,IAAI,CAAC5F,YAAY,GAAG4F,UAAU;IAC9B,IAAI,CAACD,gBAAgB,EAAE;EACzB;EAEQA,gBAAgBA,CAAA;IACtB,IAAI,CAACjF,UAAU,GAAG,EAAE,IAAI,CAACV,YAAY,GAAG,IAAI,CAACmB,YAAY,GAAG,IAAI,CAACW,SAAS,CAAC;EAC7E;;;uBAzMWhB,yBAAyB,EAAA5E,EAAA,CAAA2J,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAA7J,EAAA,CAAA2J,iBAAA,CAAAG,EAAA,CAAAC,yBAAA,GAAA/J,EAAA,CAAA2J,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAjK,EAAA,CAAA2J,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAnK,EAAA,CAAA2J,iBAAA,CAAAS,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAzBzF,yBAAyB;MAAA0F,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAxK,EAAA,CAAAyK,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCfhC/K,EAJN,CAAAC,cAAA,aAAyC,aAEX,aACE,YACA;UACxBD,EAAA,CAAAE,SAAA,kBAA2D;UAC3DF,EAAA,CAAAU,MAAA,qBACF;UAAAV,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAU,MAAA,sCAA+B;UAC7DV,EAD6D,CAAAG,YAAA,EAAI,EAC3D;UACNH,EAAA,CAAAC,cAAA,gBAAmD;UAAtBD,EAAA,CAAAW,UAAA,mBAAAsK,2DAAA;YAAA,OAASD,GAAA,CAAApC,SAAA,EAAW;UAAA,EAAC;UAChD5I,EAAA,CAAAU,MAAA,iBACA;UAAAV,EAAA,CAAAE,SAAA,mBAA4C;UAEhDF,EADE,CAAAG,YAAA,EAAS,EACL;UA6INH,EA1IA,CAAAI,UAAA,KAAA8K,yCAAA,iBAAiD,KAAAC,yCAAA,iBAcQ,KAAAC,yCAAA,mBAU0C,KAAAC,yCAAA,kBAkHR;UAK7FrL,EAAA,CAAAG,YAAA,EAAM;;;UA/IEH,EAAA,CAAAM,SAAA,IAAe;UAAfN,EAAA,CAAAO,UAAA,SAAAyK,GAAA,CAAAxF,SAAA,CAAe;UAcfxF,EAAA,CAAAM,SAAA,EAAyB;UAAzBN,EAAA,CAAAO,UAAA,SAAAyK,GAAA,CAAA5J,KAAA,KAAA4J,GAAA,CAAAxF,SAAA,CAAyB;UAUzBxF,EAAA,CAAAM,SAAA,EAAyD;UAAzDN,EAAA,CAAAO,UAAA,UAAAyK,GAAA,CAAAxF,SAAA,KAAAwF,GAAA,CAAA5J,KAAA,IAAA4J,GAAA,CAAAvG,gBAAA,CAAAO,MAAA,KAAyD;UAkHzDhF,EAAA,CAAAM,SAAA,EAA2D;UAA3DN,EAAA,CAAAO,UAAA,UAAAyK,GAAA,CAAAxF,SAAA,KAAAwF,GAAA,CAAA5J,KAAA,IAAA4J,GAAA,CAAAvG,gBAAA,CAAAO,MAAA,OAA2D;;;qBD5IvDpF,YAAY,EAAA0L,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE1L,WAAW,EAAA2L,EAAA,CAAAC,OAAA,EAAE3L,cAAc;MAAA4L,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}