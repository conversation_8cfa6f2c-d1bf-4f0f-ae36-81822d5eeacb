{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let PaymentService = /*#__PURE__*/(() => {\n  class PaymentService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = `${environment.apiUrl}/payments`;\n      this.paymentMethodsSubject = new BehaviorSubject([]);\n      this.paymentMethods$ = this.paymentMethodsSubject.asObservable();\n      this.defaultPaymentMethods = [{\n        id: 'card',\n        name: 'Credit/Debit Card',\n        type: 'card',\n        icon: 'card',\n        description: 'Visa, Mastercard, RuPay',\n        enabled: true\n      }, {\n        id: 'upi',\n        name: 'UPI',\n        type: 'upi',\n        icon: 'qr-code',\n        description: 'Google Pay, PhonePe, Paytm',\n        enabled: true\n      }, {\n        id: 'netbanking',\n        name: 'Net Banking',\n        type: 'netbanking',\n        icon: 'business',\n        description: 'All major banks',\n        enabled: true\n      }, {\n        id: 'wallet',\n        name: 'Wallet',\n        type: 'wallet',\n        icon: 'wallet',\n        description: 'Paytm, Amazon Pay',\n        enabled: true\n      }, {\n        id: 'cod',\n        name: 'Cash on Delivery',\n        type: 'cod',\n        icon: 'cash',\n        description: 'Pay when you receive',\n        enabled: true\n      }];\n      this.paymentMethodsSubject.next(this.defaultPaymentMethods);\n    }\n    /**\n     * Initiate payment for an order\n     */\n    initiatePayment(paymentData) {\n      return this.http.post(`${this.apiUrl}/initiate`, paymentData);\n    }\n    /**\n     * Verify payment after successful payment\n     */\n    verifyPayment(verificationData) {\n      return this.http.post(`${this.apiUrl}/verify`, verificationData);\n    }\n    /**\n     * Get payment details by ID\n     */\n    getPaymentDetails(paymentId) {\n      return this.http.get(`${this.apiUrl}/${paymentId}`);\n    }\n    /**\n     * Get payment history for the user\n     */\n    getPaymentHistory(params) {\n      let queryParams = '';\n      if (params) {\n        const searchParams = new URLSearchParams();\n        Object.entries(params).forEach(([key, value]) => {\n          if (value !== undefined && value !== null) {\n            searchParams.append(key, value.toString());\n          }\n        });\n        queryParams = searchParams.toString();\n      }\n      const url = queryParams ? `${this.apiUrl}?${queryParams}` : this.apiUrl;\n      return this.http.get(url);\n    }\n    /**\n     * Request refund for a payment\n     */\n    requestRefund(paymentId, refundData) {\n      return this.http.post(`${this.apiUrl}/${paymentId}/refund`, refundData);\n    }\n    /**\n     * Get available payment methods\n     */\n    getPaymentMethods() {\n      return this.paymentMethodsSubject.value;\n    }\n    /**\n     * Update payment methods availability\n     */\n    updatePaymentMethods(methods) {\n      this.paymentMethodsSubject.next(methods);\n    }\n    /**\n     * Load Razorpay script dynamically\n     */\n    loadRazorpayScript() {\n      return new Promise(resolve => {\n        if (window.Razorpay) {\n          resolve(true);\n          return;\n        }\n        const script = document.createElement('script');\n        script.src = 'https://checkout.razorpay.com/v1/checkout.js';\n        script.onload = () => resolve(true);\n        script.onerror = () => resolve(false);\n        document.head.appendChild(script);\n      });\n    }\n    /**\n     * Open Razorpay checkout\n     */\n    openRazorpayCheckout(options) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        const scriptLoaded = yield _this.loadRazorpayScript();\n        if (!scriptLoaded) {\n          throw new Error('Failed to load Razorpay script');\n        }\n        const razorpay = new window.Razorpay(options);\n        razorpay.open();\n      })();\n    }\n    /**\n     * Format amount for display\n     */\n    formatAmount(amount, currency = 'INR') {\n      return new Intl.NumberFormat('en-IN', {\n        style: 'currency',\n        currency: currency,\n        minimumFractionDigits: 2\n      }).format(amount);\n    }\n    /**\n     * Get payment method by ID\n     */\n    getPaymentMethodById(id) {\n      return this.paymentMethodsSubject.value.find(method => method.id === id);\n    }\n    /**\n     * Check if payment method is enabled\n     */\n    isPaymentMethodEnabled(id) {\n      const method = this.getPaymentMethodById(id);\n      return method ? method.enabled : false;\n    }\n    /**\n     * Get payment status display text\n     */\n    getPaymentStatusText(status) {\n      const statusMap = {\n        'pending': 'Pending',\n        'processing': 'Processing',\n        'completed': 'Completed',\n        'failed': 'Failed',\n        'cancelled': 'Cancelled',\n        'refunded': 'Refunded'\n      };\n      return statusMap[status] || status;\n    }\n    /**\n     * Get payment status color\n     */\n    getPaymentStatusColor(status) {\n      const colorMap = {\n        'pending': 'warning',\n        'processing': 'primary',\n        'completed': 'success',\n        'failed': 'danger',\n        'cancelled': 'medium',\n        'refunded': 'secondary'\n      };\n      return colorMap[status] || 'medium';\n    }\n    static {\n      this.ɵfac = function PaymentService_Factory(t) {\n        return new (t || PaymentService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: PaymentService,\n        factory: PaymentService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return PaymentService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}