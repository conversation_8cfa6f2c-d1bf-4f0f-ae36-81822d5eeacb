{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/trending.service\";\nimport * as i2 from \"../../../../core/services/social-interactions.service\";\nimport * as i3 from \"../../../../core/services/cart.service\";\nimport * as i4 from \"../../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@ionic/angular\";\nimport * as i8 from \"ngx-owl-carousel-o\";\nconst _c0 = () => [1, 2, 3, 4, 5, 6, 7, 8];\nconst _c1 = () => [1, 2, 3, 4, 5];\nfunction TrendingProductsComponent_div_11_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵelement(1, \"div\", 16);\n    i0.ɵɵelementStart(2, \"div\", 17);\n    i0.ɵɵelement(3, \"div\", 18)(4, \"div\", 19)(5, \"div\", 20);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TrendingProductsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13);\n    i0.ɵɵtemplate(2, TrendingProductsComponent_div_11_div_2_Template, 6, 0, \"div\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction TrendingProductsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"ion-icon\", 22);\n    i0.ɵɵelementStart(2, \"p\", 23);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_12_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 25);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction TrendingProductsComponent_div_13_2_ng_template_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r4 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDiscountPercentage(product_r4), \"% OFF \");\n  }\n}\nfunction TrendingProductsComponent_div_13_2_ng_template_0_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r4 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r4.originalPrice));\n  }\n}\nfunction TrendingProductsComponent_div_13_2_ng_template_0_ion_icon_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ion-icon\", 38);\n  }\n  if (rf & 2) {\n    const star_r5 = ctx.$implicit;\n    const product_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r5 <= product_r4.rating.average);\n    i0.ɵɵproperty(\"name\", star_r5 <= product_r4.rating.average ? \"star\" : \"star-outline\");\n  }\n}\nfunction TrendingProductsComponent_div_13_2_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_13_2_ng_template_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const product_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onProductClick(product_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 31);\n    i0.ɵɵelement(2, \"img\", 32);\n    i0.ɵɵelementStart(3, \"div\", 33);\n    i0.ɵɵelement(4, \"ion-icon\", 34);\n    i0.ɵɵtext(5, \" Trending \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, TrendingProductsComponent_div_13_2_ng_template_0_div_6_Template, 2, 1, \"div\", 35);\n    i0.ɵɵelementStart(7, \"div\", 36)(8, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_13_2_ng_template_0_Template_button_click_8_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const product_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onLikeProduct(product_r4, $event));\n    });\n    i0.ɵɵelement(9, \"ion-icon\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_13_2_ng_template_0_Template_button_click_10_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const product_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onShareProduct(product_r4, $event));\n    });\n    i0.ɵɵelement(11, \"ion-icon\", 40);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 41)(13, \"div\", 42);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"h3\", 43);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 44)(18, \"span\", 45);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, TrendingProductsComponent_div_13_2_ng_template_0_span_20_Template, 2, 1, \"span\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 47)(22, \"div\", 48);\n    i0.ɵɵtemplate(23, TrendingProductsComponent_div_13_2_ng_template_0_ion_icon_23_Template, 1, 3, \"ion-icon\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\", 50);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 51)(27, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_13_2_ng_template_0_Template_button_click_27_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const product_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAddToCart(product_r4, $event));\n    });\n    i0.ɵɵelement(28, \"ion-icon\", 53);\n    i0.ɵɵtext(29, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_13_2_ng_template_0_Template_button_click_30_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const product_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAddToWishlist(product_r4, $event));\n    });\n    i0.ɵɵelement(31, \"ion-icon\", 55);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r4.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r4.images[0].alt || product_r4.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDiscountPercentage(product_r4) > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r1.isProductLiked(product_r4._id));\n    i0.ɵɵattribute(\"aria-label\", \"Like \" + product_r4.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isProductLiked(product_r4._id) ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", \"Share \" + product_r4.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(product_r4.brand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r4.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r4.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r4.originalPrice && product_r4.originalPrice > product_r4.price);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(14, _c1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r4.rating.count, \")\");\n  }\n}\nfunction TrendingProductsComponent_div_13_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TrendingProductsComponent_div_13_2_ng_template_0_Template, 32, 15, \"ng-template\", 29);\n  }\n}\nfunction TrendingProductsComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"owl-carousel-o\", 27);\n    i0.ɵɵtemplate(2, TrendingProductsComponent_div_13_2_Template, 1, 0, null, 28);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r1.carouselOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.trendingProducts)(\"ngForTrackBy\", ctx_r1.trackByProductId);\n  }\n}\nfunction TrendingProductsComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵelement(1, \"ion-icon\", 59);\n    i0.ɵɵelementStart(2, \"h3\", 60);\n    i0.ɵɵtext(3, \"No Trending Products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 61);\n    i0.ɵɵtext(5, \"Check back later for trending items\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class TrendingProductsComponent {\n  constructor(trendingService, socialService, cartService, wishlistService, router) {\n    this.trendingService = trendingService;\n    this.socialService = socialService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.router = router;\n    this.trendingProducts = [];\n    this.isLoading = true;\n    this.error = null;\n    this.likedProducts = new Set();\n    this.subscription = new Subscription();\n    // Carousel configuration\n    this.carouselOptions = {\n      loop: true,\n      mouseDrag: true,\n      touchDrag: true,\n      pullDrag: false,\n      dots: false,\n      navSpeed: 700,\n      navText: ['<', '>'],\n      nav: true,\n      autoplay: true,\n      autoplayTimeout: 4000,\n      autoplayHoverPause: true,\n      margin: 8,\n      responsive: {\n        0: {\n          items: 1\n        },\n        400: {\n          items: 1\n        },\n        600: {\n          items: 2\n        },\n        900: {\n          items: 3\n        },\n        1200: {\n          items: 4\n        }\n      }\n    };\n  }\n  ngOnInit() {\n    this.loadTrendingProducts();\n    this.subscribeTrendingProducts();\n    this.subscribeLikedProducts();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n  subscribeTrendingProducts() {\n    this.subscription.add(this.trendingService.trendingProducts$.subscribe(products => {\n      this.trendingProducts = products;\n      this.isLoading = false;\n    }));\n  }\n  subscribeLikedProducts() {\n    this.subscription.add(this.socialService.likedProducts$.subscribe(likedProducts => {\n      this.likedProducts = likedProducts;\n    }));\n  }\n  loadTrendingProducts() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        yield _this.trendingService.loadTrendingProducts(1, 8);\n      } catch (error) {\n        console.error('Error loading trending products:', error);\n        _this.error = 'Failed to load trending products';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onProductClick(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  onLikeProduct(product, event) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const result = yield _this2.socialService.likeProduct(product._id);\n        if (result.success) {\n          console.log(result.message);\n        } else {\n          console.error('Failed to like product:', result.message);\n        }\n      } catch (error) {\n        console.error('Error liking product:', error);\n      }\n    })();\n  }\n  onShareProduct(product, event) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        // For now, copy link to clipboard\n        const productUrl = `${window.location.origin}/product/${product._id}`;\n        yield navigator.clipboard.writeText(productUrl);\n        // Track the share\n        yield _this3.socialService.shareProduct(product._id, {\n          platform: 'copy_link',\n          message: `Check out this amazing ${product.name} from ${product.brand}!`\n        });\n        console.log('Product link copied to clipboard!');\n      } catch (error) {\n        console.error('Error sharing product:', error);\n      }\n    })();\n  }\n  onAddToCart(product, event) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        yield _this4.cartService.addToCart(product._id, 1);\n        console.log('Product added to cart!');\n      } catch (error) {\n        console.error('Error adding to cart:', error);\n      }\n    })();\n  }\n  onAddToWishlist(product, event) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        yield _this5.wishlistService.addToWishlist(product._id);\n        console.log('Product added to wishlist!');\n      } catch (error) {\n        console.error('Error adding to wishlist:', error);\n      }\n    })();\n  }\n  getDiscountPercentage(product) {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n    }\n    return 0;\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  onRetry() {\n    this.loadTrendingProducts();\n  }\n  onViewAll() {\n    this.router.navigate(['/products'], {\n      queryParams: {\n        filter: 'trending'\n      }\n    });\n  }\n  isProductLiked(productId) {\n    return this.likedProducts.has(productId);\n  }\n  trackByProductId(index, product) {\n    return product._id;\n  }\n  static {\n    this.ɵfac = function TrendingProductsComponent_Factory(t) {\n      return new (t || TrendingProductsComponent)(i0.ɵɵdirectiveInject(i1.TrendingService), i0.ɵɵdirectiveInject(i2.SocialInteractionsService), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService), i0.ɵɵdirectiveInject(i5.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TrendingProductsComponent,\n      selectors: [[\"app-trending-products\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 15,\n      vars: 4,\n      consts: [[1, \"trending-products-container\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"trending-up\", 1, \"title-icon\"], [1, \"section-subtitle\"], [1, \"view-all-btn\", 3, \"click\"], [\"name\", \"chevron-forward\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"products-slider-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-card\"], [1, \"loading-image\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"products-slider-container\"], [1, \"products-slider\", 3, \"options\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"carouselSlide\", \"\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"src\", \"alt\"], [1, \"trending-badge\"], [\"name\", \"trending-up\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"action-buttons\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"name\", \"share-outline\"], [1, \"product-info\"], [1, \"product-brand\"], [1, \"product-name\"], [1, \"price-section\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"rating-section\"], [1, \"stars\"], [3, \"name\", \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [1, \"product-actions\"], [1, \"cart-btn\", 3, \"click\"], [\"name\", \"bag-add-outline\"], [1, \"wishlist-btn\", 3, \"click\"], [\"name\", \"heart-outline\"], [1, \"discount-badge\"], [1, \"original-price\"], [1, \"empty-container\"], [\"name\", \"trending-up-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"]],\n      template: function TrendingProductsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n          i0.ɵɵelement(4, \"ion-icon\", 4);\n          i0.ɵɵtext(5, \" Trending Now \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \"Most popular products this week\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function TrendingProductsComponent_Template_button_click_8_listener() {\n            return ctx.onViewAll();\n          });\n          i0.ɵɵtext(9, \" View All \");\n          i0.ɵɵelement(10, \"ion-icon\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(11, TrendingProductsComponent_div_11_Template, 3, 2, \"div\", 8)(12, TrendingProductsComponent_div_12_Template, 7, 1, \"div\", 9)(13, TrendingProductsComponent_div_13_Template, 3, 3, \"div\", 10)(14, TrendingProductsComponent_div_14_Template, 6, 0, \"div\", 11);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.trendingProducts.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.trendingProducts.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i6.NgForOf, i6.NgIf, IonicModule, i7.IonIcon, CarouselModule, i8.CarouselComponent, i8.CarouselSlideDirective],\n      styles: [\".trending-products-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #ff6b35;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  margin: 0;\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);\\n  color: white;\\n  border: none;\\n  padding: 12px 20px;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 20px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 200px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  border-radius: 6px;\\n  margin-bottom: 8px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0% {\\n    background-position: 200% 0;\\n  }\\n  100% {\\n    background-position: -200% 0;\\n  }\\n}\\n.error-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #dc3545;\\n  margin-bottom: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #666;\\n  margin-bottom: 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  color: white;\\n  border: none;\\n  padding: 12px 24px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 auto;\\n  transition: background 0.3s ease;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  background: #0056b3;\\n}\\n\\n.products-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 20px;\\n}\\n\\n.product-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.product-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n}\\n.product-card[_ngcontent-%COMP%]:hover   .action-buttons[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n\\n.product-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 200px;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  left: 12px;\\n  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);\\n  color: white;\\n  padding: 6px 12px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  background: #dc3545;\\n  color: white;\\n  padding: 6px 10px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 700;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  right: 12px;\\n  transform: translateY(-50%);\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  opacity: 0;\\n  transition: all 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: #333;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  transform: scale(1.1);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%] {\\n  background: rgba(220, 53, 69, 0.1);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.product-info[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  text-transform: uppercase;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 4px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1a1a1a;\\n  margin: 0 0 12px 0;\\n  line-height: 1.4;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 12px;\\n}\\n.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: #ff6b35;\\n}\\n.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #999;\\n  text-decoration: line-through;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #ddd;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon.filled[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);\\n  color: white;\\n  border: none;\\n  padding: 12px 16px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%] {\\n  width: 44px;\\n  height: 44px;\\n  border: 2px solid #e9ecef;\\n  background: white;\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: #666;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover {\\n  border-color: #ff6b35;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n}\\n\\n.empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: #ccc;\\n  margin-bottom: 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 8px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n}\\n\\n@media (max-width: 768px) {\\n  .trending-products-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .section-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 16px;\\n  }\\n  .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n    align-self: flex-end;\\n  }\\n  .products-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n    gap: 16px;\\n  }\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subscription", "IonicModule", "CarouselModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "TrendingProductsComponent_div_11_div_2_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵtext", "ɵɵlistener", "TrendingProductsComponent_div_12_Template_button_click_4_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onRetry", "ɵɵtextInterpolate", "error", "ɵɵtextInterpolate1", "getDiscountPercentage", "product_r4", "formatPrice", "originalPrice", "ɵɵclassProp", "star_r5", "rating", "average", "TrendingProductsComponent_div_13_2_ng_template_0_Template_div_click_0_listener", "_r3", "$implicit", "onProductClick", "TrendingProductsComponent_div_13_2_ng_template_0_div_6_Template", "TrendingProductsComponent_div_13_2_ng_template_0_Template_button_click_8_listener", "$event", "onLikeProduct", "TrendingProductsComponent_div_13_2_ng_template_0_Template_button_click_10_listener", "onShareProduct", "TrendingProductsComponent_div_13_2_ng_template_0_span_20_Template", "TrendingProductsComponent_div_13_2_ng_template_0_ion_icon_23_Template", "TrendingProductsComponent_div_13_2_ng_template_0_Template_button_click_27_listener", "onAddToCart", "TrendingProductsComponent_div_13_2_ng_template_0_Template_button_click_30_listener", "onAddToWishlist", "images", "url", "ɵɵsanitizeUrl", "alt", "name", "isProductLiked", "_id", "brand", "price", "_c1", "count", "TrendingProductsComponent_div_13_2_ng_template_0_Template", "TrendingProductsComponent_div_13_2_Template", "carouselOptions", "trendingProducts", "trackByProductId", "TrendingProductsComponent", "constructor", "trendingService", "socialService", "cartService", "wishlistService", "router", "isLoading", "likedProducts", "Set", "subscription", "loop", "mouseDrag", "touchDrag", "pullDrag", "dots", "navSpeed", "navText", "nav", "autoplay", "autoplayTimeout", "autoplayHoverPause", "margin", "responsive", "items", "ngOnInit", "loadTrendingProducts", "subscribeTrendingProducts", "subscribeLikedProducts", "ngOnDestroy", "unsubscribe", "add", "trendingProducts$", "subscribe", "products", "likedProducts$", "_this", "_asyncToGenerator", "console", "product", "navigate", "event", "_this2", "stopPropagation", "result", "likeProduct", "success", "log", "message", "_this3", "productUrl", "window", "location", "origin", "navigator", "clipboard", "writeText", "shareProduct", "platform", "_this4", "addToCart", "_this5", "addToWishlist", "Math", "round", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "onViewAll", "queryParams", "filter", "productId", "has", "index", "ɵɵdirectiveInject", "i1", "TrendingService", "i2", "SocialInteractionsService", "i3", "CartService", "i4", "WishlistService", "i5", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TrendingProductsComponent_Template", "rf", "ctx", "TrendingProductsComponent_Template_button_click_8_listener", "TrendingProductsComponent_div_11_Template", "TrendingProductsComponent_div_12_Template", "TrendingProductsComponent_div_13_Template", "TrendingProductsComponent_div_14_Template", "length", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i7", "IonIcon", "i8", "CarouselComponent", "CarouselSlideDirective", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\trending-products\\trending-products.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\trending-products\\trending-products.component.html"], "sourcesContent": ["import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { TrendingService } from '../../../../core/services/trending.service';\nimport { Product } from '../../../../core/models/product.model';\nimport { SocialInteractionsService } from '../../../../core/services/social-interactions.service';\nimport { CartService } from '../../../../core/services/cart.service';\nimport { WishlistService } from '../../../../core/services/wishlist.service';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule, OwlOptions } from 'ngx-owl-carousel-o';\n\n@Component({\n  selector: 'app-trending-products',\n  standalone: true,\n  imports: [CommonModule, IonicModule, CarouselModule],\n  templateUrl: './trending-products.component.html',\n  styleUrls: ['./trending-products.component.scss']\n})\nexport class TrendingProductsComponent implements OnInit, OnDestroy {\n  trendingProducts: Product[] = [];\n  isLoading = true;\n  error: string | null = null;\n  likedProducts = new Set<string>();\n  private subscription: Subscription = new Subscription();\n\n  // Carousel configuration\n  carouselOptions: OwlOptions = {\n    loop: true,\n    mouseDrag: true,\n    touchDrag: true,\n    pullDrag: false,\n    dots: false,\n    navSpeed: 700,\n    navText: ['<', '>'],\n    nav: true,\n    autoplay: true,\n    autoplayTimeout: 4000,\n    autoplayHoverPause: true,\n    margin: 8,\n    responsive: {\n      0: {\n        items: 1\n      },\n      400: {\n        items: 1\n      },\n      600: {\n        items: 2\n      },\n      900: {\n        items: 3\n      },\n      1200: {\n        items: 4\n      }\n    }\n  };\n\n  constructor(\n    private trendingService: TrendingService,\n    private socialService: SocialInteractionsService,\n    private cartService: CartService,\n    private wishlistService: WishlistService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadTrendingProducts();\n    this.subscribeTrendingProducts();\n    this.subscribeLikedProducts();\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n\n  private subscribeTrendingProducts() {\n    this.subscription.add(\n      this.trendingService.trendingProducts$.subscribe(products => {\n        this.trendingProducts = products;\n        this.isLoading = false;\n      })\n    );\n  }\n\n  private subscribeLikedProducts() {\n    this.subscription.add(\n      this.socialService.likedProducts$.subscribe(likedProducts => {\n        this.likedProducts = likedProducts;\n      })\n    );\n  }\n\n  private async loadTrendingProducts() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      await this.trendingService.loadTrendingProducts(1, 8);\n    } catch (error) {\n      console.error('Error loading trending products:', error);\n      this.error = 'Failed to load trending products';\n      this.isLoading = false;\n    }\n  }\n\n  onProductClick(product: Product) {\n    this.router.navigate(['/product', product._id]);\n  }\n\n  async onLikeProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      const result = await this.socialService.likeProduct(product._id);\n      if (result.success) {\n        console.log(result.message);\n      } else {\n        console.error('Failed to like product:', result.message);\n      }\n    } catch (error) {\n      console.error('Error liking product:', error);\n    }\n  }\n\n  async onShareProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      // For now, copy link to clipboard\n      const productUrl = `${window.location.origin}/product/${product._id}`;\n      await navigator.clipboard.writeText(productUrl);\n\n      // Track the share\n      await this.socialService.shareProduct(product._id, {\n        platform: 'copy_link',\n        message: `Check out this amazing ${product.name} from ${product.brand}!`\n      });\n\n      console.log('Product link copied to clipboard!');\n    } catch (error) {\n      console.error('Error sharing product:', error);\n    }\n  }\n\n  async onAddToCart(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      await this.cartService.addToCart(product._id, 1);\n      console.log('Product added to cart!');\n    } catch (error) {\n      console.error('Error adding to cart:', error);\n    }\n  }\n\n  async onAddToWishlist(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      await this.wishlistService.addToWishlist(product._id);\n      console.log('Product added to wishlist!');\n    } catch (error) {\n      console.error('Error adding to wishlist:', error);\n    }\n  }\n\n  getDiscountPercentage(product: Product): number {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);\n    }\n    return 0;\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n\n  onRetry() {\n    this.loadTrendingProducts();\n  }\n\n  onViewAll() {\n    this.router.navigate(['/products'], {\n      queryParams: { filter: 'trending' }\n    });\n  }\n\n  isProductLiked(productId: string): boolean {\n    return this.likedProducts.has(productId);\n  }\n\n  trackByProductId(index: number, product: Product): string {\n    return product._id;\n  }\n}\n", "<div class=\"trending-products-container\">\n  <!-- Header -->\n  <div class=\"section-header\">\n    <div class=\"header-content\">\n      <h2 class=\"section-title\">\n        <ion-icon name=\"trending-up\" class=\"title-icon\"></ion-icon>\n        Trending Now\n      </h2>\n      <p class=\"section-subtitle\">Most popular products this week</p>\n    </div>\n    <button class=\"view-all-btn\" (click)=\"onViewAll()\">\n      View All\n      <ion-icon name=\"chevron-forward\"></ion-icon>\n    </button>\n  </div>\n\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <div class=\"loading-grid\">\n      <div *ngFor=\"let item of [1,2,3,4,5,6,7,8]\" class=\"loading-card\">\n        <div class=\"loading-image\"></div>\n        <div class=\"loading-content\">\n          <div class=\"loading-line short\"></div>\n          <div class=\"loading-line medium\"></div>\n          <div class=\"loading-line long\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error && !isLoading\" class=\"error-container\">\n    <ion-icon name=\"alert-circle\" class=\"error-icon\"></ion-icon>\n    <p class=\"error-message\">{{ error }}</p>\n    <button class=\"retry-btn\" (click)=\"onRetry()\">\n      <ion-icon name=\"refresh\"></ion-icon>\n      Try Again\n    </button>\n  </div>\n\n  <!-- Products Slider -->\n  <div *ngIf=\"!isLoading && !error && trendingProducts.length > 0\" class=\"products-slider-container\">\n    <owl-carousel-o [options]=\"carouselOptions\" class=\"products-slider\">\n      <ng-template carouselSlide *ngFor=\"let product of trendingProducts; trackBy: trackByProductId\">\n        <div\n          class=\"product-card\"\n          (click)=\"onProductClick(product)\"\n        >\n      <!-- Product Image -->\n      <div class=\"product-image-container\">\n        <img \n          [src]=\"product.images[0].url\"\n          [alt]=\"product.images[0].alt || product.name\"\n          class=\"product-image\"\n          loading=\"lazy\"\n        />\n        \n        <!-- Trending Badge -->\n        <div class=\"trending-badge\">\n          <ion-icon name=\"trending-up\"></ion-icon>\n          Trending\n        </div>\n\n        <!-- Discount Badge -->\n        <div *ngIf=\"getDiscountPercentage(product) > 0\" class=\"discount-badge\">\n          {{ getDiscountPercentage(product) }}% OFF\n        </div>\n\n        <!-- Action Buttons -->\n        <div class=\"action-buttons\">\n          <button\n            class=\"action-btn like-btn\"\n            [class.liked]=\"isProductLiked(product._id)\"\n            (click)=\"onLikeProduct(product, $event)\"\n            [attr.aria-label]=\"'Like ' + product.name\"\n          >\n            <ion-icon [name]=\"isProductLiked(product._id) ? 'heart' : 'heart-outline'\"></ion-icon>\n          </button>\n          <button \n            class=\"action-btn share-btn\" \n            (click)=\"onShareProduct(product, $event)\"\n            [attr.aria-label]=\"'Share ' + product.name\"\n          >\n            <ion-icon name=\"share-outline\"></ion-icon>\n          </button>\n        </div>\n      </div>\n\n      <!-- Product Info -->\n      <div class=\"product-info\">\n        <div class=\"product-brand\">{{ product.brand }}</div>\n        <h3 class=\"product-name\">{{ product.name }}</h3>\n        \n        <!-- Price Section -->\n        <div class=\"price-section\">\n          <span class=\"current-price\">{{ formatPrice(product.price) }}</span>\n          <span *ngIf=\"product.originalPrice && product.originalPrice > product.price\" \n                class=\"original-price\">{{ formatPrice(product.originalPrice) }}</span>\n        </div>\n\n        <!-- Rating -->\n        <div class=\"rating-section\">\n          <div class=\"stars\">\n            <ion-icon \n              *ngFor=\"let star of [1,2,3,4,5]\" \n              [name]=\"star <= product.rating.average ? 'star' : 'star-outline'\"\n              [class.filled]=\"star <= product.rating.average\"\n            ></ion-icon>\n          </div>\n          <span class=\"rating-text\">({{ product.rating.count }})</span>\n        </div>\n\n        <!-- Action Buttons -->\n        <div class=\"product-actions\">\n          <button \n            class=\"cart-btn\" \n            (click)=\"onAddToCart(product, $event)\"\n          >\n            <ion-icon name=\"bag-add-outline\"></ion-icon>\n            Add to Cart\n          </button>\n          <button \n            class=\"wishlist-btn\" \n            (click)=\"onAddToWishlist(product, $event)\"\n          >\n            <ion-icon name=\"heart-outline\"></ion-icon>\n          </button>\n        </div>\n      </div>\n        </div>\n      </ng-template>\n    </owl-carousel-o>\n  </div>\n\n  <!-- Empty State -->\n  <div *ngIf=\"!isLoading && !error && trendingProducts.length === 0\" class=\"empty-container\">\n    <ion-icon name=\"trending-up-outline\" class=\"empty-icon\"></ion-icon>\n    <h3 class=\"empty-title\">No Trending Products</h3>\n    <p class=\"empty-message\">Check back later for trending items</p>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AAMnC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAoB,oBAAoB;;;;;;;;;;;;;;ICSzDC,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAE,SAAA,cAAiC;IACjCF,EAAA,CAAAC,cAAA,cAA6B;IAG3BD,EAFA,CAAAE,SAAA,cAAsC,cACC,cACF;IAEzCF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IARRH,EADF,CAAAC,cAAA,cAAiD,cACrB;IACxBD,EAAA,CAAAI,UAAA,IAAAC,+CAAA,kBAAiE;IASrEL,EADE,CAAAG,YAAA,EAAM,EACF;;;IAToBH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAoB;;;;;;IAY9CT,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,SAAA,mBAA4D;IAC5DF,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAU,MAAA,GAAW;IAAAV,EAAA,CAAAG,YAAA,EAAI;IACxCH,EAAA,CAAAC,cAAA,iBAA8C;IAApBD,EAAA,CAAAW,UAAA,mBAAAC,kEAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAG,OAAA,EAAS;IAAA,EAAC;IAC3ClB,EAAA,CAAAE,SAAA,mBAAoC;IACpCF,EAAA,CAAAU,MAAA,kBACF;IACFV,EADE,CAAAG,YAAA,EAAS,EACL;;;;IALqBH,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAmB,iBAAA,CAAAJ,MAAA,CAAAK,KAAA,CAAW;;;;;IA+BhCpB,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAG,YAAA,EAAM;;;;;IADJH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAqB,kBAAA,MAAAN,MAAA,CAAAO,qBAAA,CAAAC,UAAA,YACF;;;;;IA8BEvB,EAAA,CAAAC,cAAA,eAC6B;IAAAD,EAAA,CAAAU,MAAA,GAAwC;IAAAV,EAAA,CAAAG,YAAA,EAAO;;;;;IAA/CH,EAAA,CAAAM,SAAA,EAAwC;IAAxCN,EAAA,CAAAmB,iBAAA,CAAAJ,MAAA,CAAAS,WAAA,CAAAD,UAAA,CAAAE,aAAA,EAAwC;;;;;IAMnEzB,EAAA,CAAAE,SAAA,mBAIY;;;;;IADVF,EAAA,CAAA0B,WAAA,WAAAC,OAAA,IAAAJ,UAAA,CAAAK,MAAA,CAAAC,OAAA,CAA+C;IAD/C7B,EAAA,CAAAO,UAAA,SAAAoB,OAAA,IAAAJ,UAAA,CAAAK,MAAA,CAAAC,OAAA,2BAAiE;;;;;;IA7DvE7B,EAAA,CAAAC,cAAA,cAGC;IADCD,EAAA,CAAAW,UAAA,mBAAAmB,+EAAA;MAAA9B,EAAA,CAAAa,aAAA,CAAAkB,GAAA;MAAA,MAAAR,UAAA,GAAAvB,EAAA,CAAAgB,aAAA,GAAAgB,SAAA;MAAA,MAAAjB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAkB,cAAA,CAAAV,UAAA,CAAuB;IAAA,EAAC;IAGrCvB,EAAA,CAAAC,cAAA,cAAqC;IACnCD,EAAA,CAAAE,SAAA,cAKE;IAGFF,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAE,SAAA,mBAAwC;IACxCF,EAAA,CAAAU,MAAA,iBACF;IAAAV,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAI,UAAA,IAAA8B,+DAAA,kBAAuE;IAMrElC,EADF,CAAAC,cAAA,cAA4B,iBAMzB;IAFCD,EAAA,CAAAW,UAAA,mBAAAwB,kFAAAC,MAAA;MAAApC,EAAA,CAAAa,aAAA,CAAAkB,GAAA;MAAA,MAAAR,UAAA,GAAAvB,EAAA,CAAAgB,aAAA,GAAAgB,SAAA;MAAA,MAAAjB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAsB,aAAA,CAAAd,UAAA,EAAAa,MAAA,CAA8B;IAAA,EAAC;IAGxCpC,EAAA,CAAAE,SAAA,mBAAsF;IACxFF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAW,UAAA,mBAAA2B,mFAAAF,MAAA;MAAApC,EAAA,CAAAa,aAAA,CAAAkB,GAAA;MAAA,MAAAR,UAAA,GAAAvB,EAAA,CAAAgB,aAAA,GAAAgB,SAAA;MAAA,MAAAjB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAwB,cAAA,CAAAhB,UAAA,EAAAa,MAAA,CAA+B;IAAA,EAAC;IAGzCpC,EAAA,CAAAE,SAAA,oBAA0C;IAGhDF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAIJH,EADF,CAAAC,cAAA,eAA0B,eACG;IAAAD,EAAA,CAAAU,MAAA,IAAmB;IAAAV,EAAA,CAAAG,YAAA,EAAM;IACpDH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAU,MAAA,IAAkB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAI9CH,EADF,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAU,MAAA,IAAgC;IAAAV,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAI,UAAA,KAAAoC,iEAAA,mBAC6B;IAC/BxC,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAA4B,eACP;IACjBD,EAAA,CAAAI,UAAA,KAAAqC,qEAAA,uBAIC;IACHzC,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAU,MAAA,IAA4B;IACxDV,EADwD,CAAAG,YAAA,EAAO,EACzD;IAIJH,EADF,CAAAC,cAAA,eAA6B,kBAI1B;IADCD,EAAA,CAAAW,UAAA,mBAAA+B,mFAAAN,MAAA;MAAApC,EAAA,CAAAa,aAAA,CAAAkB,GAAA;MAAA,MAAAR,UAAA,GAAAvB,EAAA,CAAAgB,aAAA,GAAAgB,SAAA;MAAA,MAAAjB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAA4B,WAAA,CAAApB,UAAA,EAAAa,MAAA,CAA4B;IAAA,EAAC;IAEtCpC,EAAA,CAAAE,SAAA,oBAA4C;IAC5CF,EAAA,CAAAU,MAAA,qBACF;IAAAV,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGC;IADCD,EAAA,CAAAW,UAAA,mBAAAiC,mFAAAR,MAAA;MAAApC,EAAA,CAAAa,aAAA,CAAAkB,GAAA;MAAA,MAAAR,UAAA,GAAAvB,EAAA,CAAAgB,aAAA,GAAAgB,SAAA;MAAA,MAAAjB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAA8B,eAAA,CAAAtB,UAAA,EAAAa,MAAA,CAAgC;IAAA,EAAC;IAE1CpC,EAAA,CAAAE,SAAA,oBAA0C;IAI9CF,EAHE,CAAAG,YAAA,EAAS,EACL,EACF,EACE;;;;;IA9EJH,EAAA,CAAAM,SAAA,GAA6B;IAC7BN,EADA,CAAAO,UAAA,QAAAgB,UAAA,CAAAuB,MAAA,IAAAC,GAAA,EAAA/C,EAAA,CAAAgD,aAAA,CAA6B,QAAAzB,UAAA,CAAAuB,MAAA,IAAAG,GAAA,IAAA1B,UAAA,CAAA2B,IAAA,CACgB;IAYzClD,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAO,UAAA,SAAAQ,MAAA,CAAAO,qBAAA,CAAAC,UAAA,MAAwC;IAQ1CvB,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAA0B,WAAA,UAAAX,MAAA,CAAAoC,cAAA,CAAA5B,UAAA,CAAA6B,GAAA,EAA2C;;IAIjCpD,EAAA,CAAAM,SAAA,EAAgE;IAAhEN,EAAA,CAAAO,UAAA,SAAAQ,MAAA,CAAAoC,cAAA,CAAA5B,UAAA,CAAA6B,GAAA,8BAAgE;IAK1EpD,EAAA,CAAAM,SAAA,EAA2C;;IASpBN,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAmB,iBAAA,CAAAI,UAAA,CAAA8B,KAAA,CAAmB;IACrBrD,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAmB,iBAAA,CAAAI,UAAA,CAAA2B,IAAA,CAAkB;IAIblD,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAmB,iBAAA,CAAAJ,MAAA,CAAAS,WAAA,CAAAD,UAAA,CAAA+B,KAAA,EAAgC;IACrDtD,EAAA,CAAAM,SAAA,EAAoE;IAApEN,EAAA,CAAAO,UAAA,SAAAgB,UAAA,CAAAE,aAAA,IAAAF,UAAA,CAAAE,aAAA,GAAAF,UAAA,CAAA+B,KAAA,CAAoE;IAQtDtD,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,KAAA+C,GAAA,EAAc;IAKTvD,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAqB,kBAAA,MAAAE,UAAA,CAAAK,MAAA,CAAA4B,KAAA,MAA4B;;;;;IAlE1DxD,EAAA,CAAAI,UAAA,IAAAqD,yDAAA,4BAA+F;;;;;IADjGzD,EADF,CAAAC,cAAA,cAAmG,yBAC7B;IAClED,EAAA,CAAAI,UAAA,IAAAsD,2CAAA,iBAA+F;IAyFnG1D,EADE,CAAAG,YAAA,EAAiB,EACb;;;;IA1FYH,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAO,UAAA,YAAAQ,MAAA,CAAA4C,eAAA,CAA2B;IACM3D,EAAA,CAAAM,SAAA,EAAqB;IAAAN,EAArB,CAAAO,UAAA,YAAAQ,MAAA,CAAA6C,gBAAA,CAAqB,iBAAA7C,MAAA,CAAA8C,gBAAA,CAAyB;;;;;IA4FjG7D,EAAA,CAAAC,cAAA,cAA2F;IACzFD,EAAA,CAAAE,SAAA,mBAAmE;IACnEF,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAU,MAAA,2BAAoB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAU,MAAA,0CAAmC;IAC9DV,EAD8D,CAAAG,YAAA,EAAI,EAC5D;;;ADxHR,OAAM,MAAO2D,yBAAyB;EAwCpCC,YACUC,eAAgC,EAChCC,aAAwC,EACxCC,WAAwB,EACxBC,eAAgC,EAChCC,MAAc;IAJd,KAAAJ,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IA5ChB,KAAAR,gBAAgB,GAAc,EAAE;IAChC,KAAAS,SAAS,GAAG,IAAI;IAChB,KAAAjD,KAAK,GAAkB,IAAI;IAC3B,KAAAkD,aAAa,GAAG,IAAIC,GAAG,EAAU;IACzB,KAAAC,YAAY,GAAiB,IAAI3E,YAAY,EAAE;IAEvD;IACA,KAAA8D,eAAe,GAAe;MAC5Bc,IAAI,EAAE,IAAI;MACVC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,KAAK;MACfC,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;MACnBC,GAAG,EAAE,IAAI;MACTC,QAAQ,EAAE,IAAI;MACdC,eAAe,EAAE,IAAI;MACrBC,kBAAkB,EAAE,IAAI;MACxBC,MAAM,EAAE,CAAC;MACTC,UAAU,EAAE;QACV,CAAC,EAAE;UACDC,KAAK,EAAE;SACR;QACD,GAAG,EAAE;UACHA,KAAK,EAAE;SACR;QACD,GAAG,EAAE;UACHA,KAAK,EAAE;SACR;QACD,GAAG,EAAE;UACHA,KAAK,EAAE;SACR;QACD,IAAI,EAAE;UACJA,KAAK,EAAE;;;KAGZ;EAQE;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,yBAAyB,EAAE;IAChC,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACnB,YAAY,CAACoB,WAAW,EAAE;EACjC;EAEQH,yBAAyBA,CAAA;IAC/B,IAAI,CAACjB,YAAY,CAACqB,GAAG,CACnB,IAAI,CAAC7B,eAAe,CAAC8B,iBAAiB,CAACC,SAAS,CAACC,QAAQ,IAAG;MAC1D,IAAI,CAACpC,gBAAgB,GAAGoC,QAAQ;MAChC,IAAI,CAAC3B,SAAS,GAAG,KAAK;IACxB,CAAC,CAAC,CACH;EACH;EAEQqB,sBAAsBA,CAAA;IAC5B,IAAI,CAAClB,YAAY,CAACqB,GAAG,CACnB,IAAI,CAAC5B,aAAa,CAACgC,cAAc,CAACF,SAAS,CAACzB,aAAa,IAAG;MAC1D,IAAI,CAACA,aAAa,GAAGA,aAAa;IACpC,CAAC,CAAC,CACH;EACH;EAEckB,oBAAoBA,CAAA;IAAA,IAAAU,KAAA;IAAA,OAAAC,iBAAA;MAChC,IAAI;QACFD,KAAI,CAAC7B,SAAS,GAAG,IAAI;QACrB6B,KAAI,CAAC9E,KAAK,GAAG,IAAI;QACjB,MAAM8E,KAAI,CAAClC,eAAe,CAACwB,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC;OACtD,CAAC,OAAOpE,KAAK,EAAE;QACdgF,OAAO,CAAChF,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD8E,KAAI,CAAC9E,KAAK,GAAG,kCAAkC;QAC/C8E,KAAI,CAAC7B,SAAS,GAAG,KAAK;;IACvB;EACH;EAEApC,cAAcA,CAACoE,OAAgB;IAC7B,IAAI,CAACjC,MAAM,CAACkC,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAACjD,GAAG,CAAC,CAAC;EACjD;EAEMf,aAAaA,CAACgE,OAAgB,EAAEE,KAAY;IAAA,IAAAC,MAAA;IAAA,OAAAL,iBAAA;MAChDI,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMC,MAAM,SAASF,MAAI,CAACvC,aAAa,CAAC0C,WAAW,CAACN,OAAO,CAACjD,GAAG,CAAC;QAChE,IAAIsD,MAAM,CAACE,OAAO,EAAE;UAClBR,OAAO,CAACS,GAAG,CAACH,MAAM,CAACI,OAAO,CAAC;SAC5B,MAAM;UACLV,OAAO,CAAChF,KAAK,CAAC,yBAAyB,EAAEsF,MAAM,CAACI,OAAO,CAAC;;OAE3D,CAAC,OAAO1F,KAAK,EAAE;QACdgF,OAAO,CAAChF,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;IAC9C;EACH;EAEMmB,cAAcA,CAAC8D,OAAgB,EAAEE,KAAY;IAAA,IAAAQ,MAAA;IAAA,OAAAZ,iBAAA;MACjDI,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF;QACA,MAAMO,UAAU,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,YAAYd,OAAO,CAACjD,GAAG,EAAE;QACrE,MAAMgE,SAAS,CAACC,SAAS,CAACC,SAAS,CAACN,UAAU,CAAC;QAE/C;QACA,MAAMD,MAAI,CAAC9C,aAAa,CAACsD,YAAY,CAAClB,OAAO,CAACjD,GAAG,EAAE;UACjDoE,QAAQ,EAAE,WAAW;UACrBV,OAAO,EAAE,0BAA0BT,OAAO,CAACnD,IAAI,SAASmD,OAAO,CAAChD,KAAK;SACtE,CAAC;QAEF+C,OAAO,CAACS,GAAG,CAAC,mCAAmC,CAAC;OACjD,CAAC,OAAOzF,KAAK,EAAE;QACdgF,OAAO,CAAChF,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;IAC/C;EACH;EAEMuB,WAAWA,CAAC0D,OAAgB,EAAEE,KAAY;IAAA,IAAAkB,MAAA;IAAA,OAAAtB,iBAAA;MAC9CI,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMgB,MAAI,CAACvD,WAAW,CAACwD,SAAS,CAACrB,OAAO,CAACjD,GAAG,EAAE,CAAC,CAAC;QAChDgD,OAAO,CAACS,GAAG,CAAC,wBAAwB,CAAC;OACtC,CAAC,OAAOzF,KAAK,EAAE;QACdgF,OAAO,CAAChF,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;IAC9C;EACH;EAEMyB,eAAeA,CAACwD,OAAgB,EAAEE,KAAY;IAAA,IAAAoB,MAAA;IAAA,OAAAxB,iBAAA;MAClDI,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMkB,MAAI,CAACxD,eAAe,CAACyD,aAAa,CAACvB,OAAO,CAACjD,GAAG,CAAC;QACrDgD,OAAO,CAACS,GAAG,CAAC,4BAA4B,CAAC;OAC1C,CAAC,OAAOzF,KAAK,EAAE;QACdgF,OAAO,CAAChF,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;;IAClD;EACH;EAEAE,qBAAqBA,CAAC+E,OAAgB;IACpC,IAAIA,OAAO,CAAC5E,aAAa,IAAI4E,OAAO,CAAC5E,aAAa,GAAG4E,OAAO,CAAC/C,KAAK,EAAE;MAClE,OAAOuE,IAAI,CAACC,KAAK,CAAE,CAACzB,OAAO,CAAC5E,aAAa,GAAG4E,OAAO,CAAC/C,KAAK,IAAI+C,OAAO,CAAC5E,aAAa,GAAI,GAAG,CAAC;;IAE5F,OAAO,CAAC;EACV;EAEAD,WAAWA,CAAC8B,KAAa;IACvB,OAAO,IAAIyE,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAAC9E,KAAK,CAAC;EAClB;EAEApC,OAAOA,CAAA;IACL,IAAI,CAACsE,oBAAoB,EAAE;EAC7B;EAEA6C,SAASA,CAAA;IACP,IAAI,CAACjE,MAAM,CAACkC,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;MAClCgC,WAAW,EAAE;QAAEC,MAAM,EAAE;MAAU;KAClC,CAAC;EACJ;EAEApF,cAAcA,CAACqF,SAAiB;IAC9B,OAAO,IAAI,CAAClE,aAAa,CAACmE,GAAG,CAACD,SAAS,CAAC;EAC1C;EAEA3E,gBAAgBA,CAAC6E,KAAa,EAAErC,OAAgB;IAC9C,OAAOA,OAAO,CAACjD,GAAG;EACpB;;;uBA/KWU,yBAAyB,EAAA9D,EAAA,CAAA2I,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAA7I,EAAA,CAAA2I,iBAAA,CAAAG,EAAA,CAAAC,yBAAA,GAAA/I,EAAA,CAAA2I,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAjJ,EAAA,CAAA2I,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAnJ,EAAA,CAAA2I,iBAAA,CAAAS,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAzBvF,yBAAyB;MAAAwF,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAxJ,EAAA,CAAAyJ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCfhC/J,EAJN,CAAAC,cAAA,aAAyC,aAEX,aACE,YACA;UACxBD,EAAA,CAAAE,SAAA,kBAA2D;UAC3DF,EAAA,CAAAU,MAAA,qBACF;UAAAV,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAU,MAAA,sCAA+B;UAC7DV,EAD6D,CAAAG,YAAA,EAAI,EAC3D;UACNH,EAAA,CAAAC,cAAA,gBAAmD;UAAtBD,EAAA,CAAAW,UAAA,mBAAAsJ,2DAAA;YAAA,OAASD,GAAA,CAAA3B,SAAA,EAAW;UAAA,EAAC;UAChDrI,EAAA,CAAAU,MAAA,iBACA;UAAAV,EAAA,CAAAE,SAAA,mBAA4C;UAEhDF,EADE,CAAAG,YAAA,EAAS,EACL;UAyHNH,EAtHA,CAAAI,UAAA,KAAA8J,yCAAA,iBAAiD,KAAAC,yCAAA,iBAcQ,KAAAC,yCAAA,kBAU0C,KAAAC,yCAAA,kBA8FR;UAK7FrK,EAAA,CAAAG,YAAA,EAAM;;;UA3HEH,EAAA,CAAAM,SAAA,IAAe;UAAfN,EAAA,CAAAO,UAAA,SAAAyJ,GAAA,CAAA3F,SAAA,CAAe;UAcfrE,EAAA,CAAAM,SAAA,EAAyB;UAAzBN,EAAA,CAAAO,UAAA,SAAAyJ,GAAA,CAAA5I,KAAA,KAAA4I,GAAA,CAAA3F,SAAA,CAAyB;UAUzBrE,EAAA,CAAAM,SAAA,EAAyD;UAAzDN,EAAA,CAAAO,UAAA,UAAAyJ,GAAA,CAAA3F,SAAA,KAAA2F,GAAA,CAAA5I,KAAA,IAAA4I,GAAA,CAAApG,gBAAA,CAAA0G,MAAA,KAAyD;UA8FzDtK,EAAA,CAAAM,SAAA,EAA2D;UAA3DN,EAAA,CAAAO,UAAA,UAAAyJ,GAAA,CAAA3F,SAAA,KAAA2F,GAAA,CAAA5I,KAAA,IAAA4I,GAAA,CAAApG,gBAAA,CAAA0G,MAAA,OAA2D;;;qBDxHvD1K,YAAY,EAAA2K,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE3K,WAAW,EAAA4K,EAAA,CAAAC,OAAA,EAAE5K,cAAc,EAAA6K,EAAA,CAAAC,iBAAA,EAAAD,EAAA,CAAAE,sBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}