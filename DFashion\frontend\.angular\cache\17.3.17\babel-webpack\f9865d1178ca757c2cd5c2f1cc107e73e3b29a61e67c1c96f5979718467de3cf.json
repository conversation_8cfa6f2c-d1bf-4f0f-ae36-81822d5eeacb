{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"../../../../core/services/upload.service\";\nimport * as i5 from \"@angular/common\";\nfunction CreatePostComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"i\", 27);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Click to upload images or videos\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Support: JP<PERSON>, P<PERSON>, MP4, MOV\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CreatePostComponent_div_15_div_1_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 35);\n  }\n  if (rf & 2) {\n    const file_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", file_r4.preview, i0.ɵɵsanitizeUrl)(\"alt\", file_r4.name);\n  }\n}\nfunction CreatePostComponent_div_15_div_1_video_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 36);\n  }\n  if (rf & 2) {\n    const file_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", file_r4.preview, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction CreatePostComponent_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtemplate(1, CreatePostComponent_div_15_div_1_img_1_Template, 1, 2, \"img\", 31)(2, CreatePostComponent_div_15_div_1_video_2_Template, 1, 1, \"video\", 32);\n    i0.ɵɵelementStart(3, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function CreatePostComponent_div_15_div_1_Template_button_click_3_listener() {\n      const i_r5 = i0.ɵɵrestoreView(_r3).index;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.removeFile(i_r5));\n    });\n    i0.ɵɵelement(4, \"i\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", file_r4.type.startsWith(\"image\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", file_r4.type.startsWith(\"video\"));\n  }\n}\nfunction CreatePostComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtemplate(1, CreatePostComponent_div_15_div_1_Template, 5, 2, \"div\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.selectedFiles);\n  }\n}\nfunction CreatePostComponent_div_27_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵlistener(\"click\", function CreatePostComponent_div_27_div_1_Template_div_click_0_listener() {\n      const product_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.addProductTag(product_r8));\n    });\n    i0.ɵɵelement(1, \"img\", 35);\n    i0.ɵɵelementStart(2, \"div\", 40)(3, \"h4\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", product_r8.images[0] == null ? null : product_r8.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r8.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r8.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(7, 4, product_r8.price, \"1.0-0\"), \"\");\n  }\n}\nfunction CreatePostComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, CreatePostComponent_div_27_div_1_Template, 8, 7, \"div\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.searchResults);\n  }\n}\nfunction CreatePostComponent_div_28_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵelement(1, \"img\", 35);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function CreatePostComponent_div_28_div_4_Template_button_click_4_listener() {\n      const i_r10 = i0.ɵɵrestoreView(_r9).index;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.removeProductTag(i_r10));\n    });\n    i0.ɵɵelement(5, \"i\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r11 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", product_r11.images[0] == null ? null : product_r11.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r11.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r11.name);\n  }\n}\nfunction CreatePostComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"h4\");\n    i0.ɵɵtext(2, \"Tagged Products:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 42);\n    i0.ɵɵtemplate(4, CreatePostComponent_div_28_div_4_Template, 6, 3, \"div\", 43);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.taggedProducts);\n  }\n}\nfunction CreatePostComponent_div_33_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function CreatePostComponent_div_33_span_1_Template_button_click_2_listener() {\n      const i_r13 = i0.ɵɵrestoreView(_r12).index;\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.removeHashtag(i_r13));\n    });\n    i0.ɵɵtext(3, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tag_r14 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" #\", tag_r14, \" \");\n  }\n}\nfunction CreatePostComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵtemplate(1, CreatePostComponent_div_33_span_1_Template, 4, 1, \"span\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.hashtags);\n  }\n}\nfunction CreatePostComponent_span_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Publishing...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreatePostComponent_span_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Publish Post\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let CreatePostComponent = /*#__PURE__*/(() => {\n  class CreatePostComponent {\n    constructor(fb, router, http, uploadService) {\n      this.fb = fb;\n      this.router = router;\n      this.http = http;\n      this.uploadService = uploadService;\n      this.selectedFiles = [];\n      this.taggedProducts = [];\n      this.hashtags = [];\n      this.searchResults = [];\n      this.uploadProgress = null;\n      this.isUploading = false;\n      this.uploading = false;\n      this.postForm = this.fb.group({\n        caption: ['', [Validators.required, Validators.maxLength(2000)]],\n        allowComments: [true],\n        allowSharing: [true]\n      });\n    }\n    ngOnInit() {\n      // Subscribe to upload progress\n      this.uploadService.getUploadProgress().subscribe(progress => {\n        this.uploadProgress = progress;\n        this.isUploading = progress?.status === 'uploading';\n      });\n    }\n    onFileSelect(event) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        const files = Array.from(event.target.files);\n        // Validate files\n        const validation = _this.uploadService.validateFiles(files, 'any', 10);\n        if (!validation.isValid) {\n          alert(validation.errors.join('\\n'));\n          return;\n        }\n        // Add files to selection with previews\n        for (const file of files) {\n          try {\n            const preview = yield _this.uploadService.createFilePreview(file);\n            const fileType = _this.uploadService.getFileType(file);\n            _this.selectedFiles.push({\n              file,\n              preview,\n              type: file.type,\n              name: file.name,\n              size: _this.uploadService.formatFileSize(file.size),\n              fileType,\n              uploaded: false,\n              url: null\n            });\n          } catch (error) {\n            console.error('Error creating preview:', error);\n          }\n        }\n      })();\n    }\n    removeFile(index) {\n      this.selectedFiles.splice(index, 1);\n    }\n    // Upload selected media files\n    uploadMedia() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        if (_this2.selectedFiles.length === 0) {\n          return [];\n        }\n        const filesToUpload = _this2.selectedFiles.filter(file => !file.uploaded).map(file => file.file);\n        if (filesToUpload.length === 0) {\n          // All files already uploaded\n          return _this2.selectedFiles.map(file => ({\n            url: file.url,\n            type: file.fileType\n          })).filter(media => media.url);\n        }\n        try {\n          _this2.isUploading = true;\n          const response = yield _this2.uploadService.uploadPostMedia(filesToUpload).toPromise();\n          if (response?.success && response.data.media) {\n            // Update selected files with upload results\n            response.data.media.forEach((uploadedMedia, index) => {\n              const fileIndex = _this2.selectedFiles.findIndex(file => !file.uploaded);\n              if (fileIndex !== -1) {\n                _this2.selectedFiles[fileIndex].uploaded = true;\n                _this2.selectedFiles[fileIndex].url = uploadedMedia.url;\n              }\n            });\n            return response.data.media.map(media => ({\n              url: media.url,\n              type: media.type || 'image'\n            }));\n          }\n          throw new Error(response?.message || 'Upload failed');\n        } catch (error) {\n          console.error('Upload error:', error);\n          throw error;\n        } finally {\n          _this2.isUploading = false;\n        }\n      })();\n    }\n    searchProducts(event) {\n      const query = event.target.value;\n      if (query.length > 2) {\n        // Search products from API\n        this.searchResults = [];\n      } else {\n        this.searchResults = [];\n      }\n    }\n    addProductTag(product) {\n      if (!this.taggedProducts.find(p => p._id === product._id)) {\n        this.taggedProducts.push(product);\n      }\n      this.searchResults = [];\n    }\n    removeProductTag(index) {\n      this.taggedProducts.splice(index, 1);\n    }\n    addHashtag(event) {\n      const tag = event.target.value.trim().replace('#', '');\n      if (tag && !this.hashtags.includes(tag)) {\n        this.hashtags.push(tag);\n        event.target.value = '';\n      }\n    }\n    removeHashtag(index) {\n      this.hashtags.splice(index, 1);\n    }\n    saveDraft() {\n      // TODO: Implement save as draft functionality\n      console.log('Saving as draft...');\n    }\n    onSubmit() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this3.postForm.valid) {\n          alert('Please fill in all required fields');\n          return;\n        }\n        if (_this3.selectedFiles.length === 0) {\n          alert('Please select at least one media file');\n          return;\n        }\n        try {\n          _this3.uploading = true;\n          // Upload media files first\n          const mediaUrls = yield _this3.uploadMedia();\n          const postData = {\n            caption: _this3.postForm.value.caption,\n            media: mediaUrls,\n            products: _this3.taggedProducts.map(p => ({\n              product: p._id,\n              position: {\n                x: 50,\n                y: 50\n              } // Default position\n            })),\n            hashtags: _this3.hashtags,\n            settings: {\n              allowComments: _this3.postForm.value.allowComments,\n              allowSharing: _this3.postForm.value.allowSharing\n            }\n          };\n          // TODO: Implement actual post creation API\n          console.log('Creating post:', postData);\n          // Simulate API call\n          yield new Promise(resolve => setTimeout(resolve, 1000));\n          alert('Post created successfully!');\n          _this3.router.navigate(['/vendor/posts']);\n        } catch (error) {\n          console.error('Error creating post:', error);\n          alert('Failed to create post. Please try again.');\n        } finally {\n          _this3.uploading = false;\n        }\n      })();\n    }\n    static {\n      this.ɵfac = function CreatePostComponent_Factory(t) {\n        return new (t || CreatePostComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.HttpClient), i0.ɵɵdirectiveInject(i4.UploadService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CreatePostComponent,\n        selectors: [[\"app-create-post\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 52,\n        vars: 12,\n        consts: [[\"fileInput\", \"\"], [1, \"create-post-container\"], [1, \"header\"], [1, \"post-form\", 3, \"ngSubmit\", \"formGroup\"], [1, \"form-section\"], [1, \"media-upload\"], [1, \"upload-area\", 3, \"click\"], [\"type\", \"file\", \"multiple\", \"\", \"accept\", \"image/*,video/*\", 2, \"display\", \"none\", 3, \"change\"], [\"class\", \"upload-content\", 4, \"ngIf\"], [\"class\", \"file-preview\", 4, \"ngIf\"], [\"formControlName\", \"caption\", \"placeholder\", \"Write a caption for your post...\", \"rows\", \"4\", \"maxlength\", \"2000\"], [1, \"char-count\"], [1, \"product-search\"], [\"type\", \"text\", \"placeholder\", \"Search your products...\", 1, \"search-input\", 3, \"input\"], [\"class\", \"product-results\", 4, \"ngIf\"], [\"class\", \"tagged-products\", 4, \"ngIf\"], [\"type\", \"text\", \"placeholder\", \"Add hashtags (e.g., #fashion #style #trending)\", 1, \"hashtag-input\", 3, \"keyup.enter\"], [\"class\", \"hashtags\", 4, \"ngIf\"], [1, \"settings-grid\"], [1, \"setting-item\"], [\"type\", \"checkbox\", \"formControlName\", \"allowComments\"], [\"type\", \"checkbox\", \"formControlName\", \"allowSharing\"], [1, \"form-actions\"], [\"type\", \"button\", 1, \"btn-secondary\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn-primary\", 3, \"disabled\"], [4, \"ngIf\"], [1, \"upload-content\"], [1, \"fas\", \"fa-cloud-upload-alt\"], [1, \"file-preview\"], [\"class\", \"file-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"file-item\"], [3, \"src\", \"alt\", 4, \"ngIf\"], [\"controls\", \"\", 3, \"src\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"remove-file\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [3, \"src\", \"alt\"], [\"controls\", \"\", 3, \"src\"], [1, \"product-results\"], [\"class\", \"product-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-item\", 3, \"click\"], [1, \"product-info\"], [1, \"tagged-products\"], [1, \"tagged-list\"], [\"class\", \"tagged-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"tagged-item\"], [\"type\", \"button\", 3, \"click\"], [1, \"hashtags\"], [\"class\", \"hashtag\", 4, \"ngFor\", \"ngForOf\"], [1, \"hashtag\"]],\n        template: function CreatePostComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"h1\");\n            i0.ɵɵtext(3, \"Create New Post\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p\");\n            i0.ɵɵtext(5, \"Share your products with the community\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"form\", 3);\n            i0.ɵɵlistener(\"ngSubmit\", function CreatePostComponent_Template_form_ngSubmit_6_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSubmit());\n            });\n            i0.ɵɵelementStart(7, \"div\", 4)(8, \"h3\");\n            i0.ɵɵtext(9, \"Media\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"div\", 5)(11, \"div\", 6);\n            i0.ɵɵlistener(\"click\", function CreatePostComponent_Template_div_click_11_listener() {\n              i0.ɵɵrestoreView(_r1);\n              const fileInput_r2 = i0.ɵɵreference(13);\n              return i0.ɵɵresetView(fileInput_r2.click());\n            });\n            i0.ɵɵelementStart(12, \"input\", 7, 0);\n            i0.ɵɵlistener(\"change\", function CreatePostComponent_Template_input_change_12_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onFileSelect($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(14, CreatePostComponent_div_14_Template, 6, 0, \"div\", 8)(15, CreatePostComponent_div_15_Template, 2, 1, \"div\", 9);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(16, \"div\", 4)(17, \"h3\");\n            i0.ɵɵtext(18, \"Caption\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(19, \"textarea\", 10);\n            i0.ɵɵelementStart(20, \"div\", 11);\n            i0.ɵɵtext(21);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(22, \"div\", 4)(23, \"h3\");\n            i0.ɵɵtext(24, \"Tag Products\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"div\", 12)(26, \"input\", 13);\n            i0.ɵɵlistener(\"input\", function CreatePostComponent_Template_input_input_26_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.searchProducts($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(27, CreatePostComponent_div_27_Template, 2, 1, \"div\", 14);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(28, CreatePostComponent_div_28_Template, 5, 1, \"div\", 15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"div\", 4)(30, \"h3\");\n            i0.ɵɵtext(31, \"Hashtags\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"input\", 16);\n            i0.ɵɵlistener(\"keyup.enter\", function CreatePostComponent_Template_input_keyup_enter_32_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.addHashtag($event));\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(33, CreatePostComponent_div_33_Template, 2, 1, \"div\", 17);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"div\", 4)(35, \"h3\");\n            i0.ɵɵtext(36, \"Post Settings\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"div\", 18)(38, \"label\", 19);\n            i0.ɵɵelement(39, \"input\", 20);\n            i0.ɵɵelementStart(40, \"span\");\n            i0.ɵɵtext(41, \"Allow comments\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(42, \"label\", 19);\n            i0.ɵɵelement(43, \"input\", 21);\n            i0.ɵɵelementStart(44, \"span\");\n            i0.ɵɵtext(45, \"Allow sharing\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(46, \"div\", 22)(47, \"button\", 23);\n            i0.ɵɵlistener(\"click\", function CreatePostComponent_Template_button_click_47_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.saveDraft());\n            });\n            i0.ɵɵtext(48, \"Save as Draft\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"button\", 24);\n            i0.ɵɵtemplate(50, CreatePostComponent_span_50_Template, 2, 0, \"span\", 25)(51, CreatePostComponent_span_51_Template, 2, 0, \"span\", 25);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            let tmp_5_0;\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"formGroup\", ctx.postForm);\n            i0.ɵɵadvance(5);\n            i0.ɵɵclassProp(\"has-files\", ctx.selectedFiles.length > 0);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedFiles.length === 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedFiles.length > 0);\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate1(\"\", ((tmp_5_0 = ctx.postForm.get(\"caption\")) == null ? null : tmp_5_0.value == null ? null : tmp_5_0.value.length) || 0, \"/2000\");\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.searchResults.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.taggedProducts.length > 0);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.hashtags.length > 0);\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"disabled\", !ctx.postForm.valid || ctx.uploading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.uploading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.uploading);\n          }\n        },\n        dependencies: [CommonModule, i5.NgForOf, i5.NgIf, i5.DecimalPipe, FormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, ReactiveFormsModule, i1.FormGroupDirective, i1.FormControlName],\n        styles: [\".create-post-container[_ngcontent-%COMP%]{max-width:800px;margin:0 auto;padding:20px}.header[_ngcontent-%COMP%]{margin-bottom:30px}.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem;font-weight:600;margin-bottom:8px}.header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666}.post-form[_ngcontent-%COMP%]{background:#fff;border-radius:8px;padding:30px;border:1px solid #eee}.form-section[_ngcontent-%COMP%]{margin-bottom:30px}.form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:600;margin-bottom:15px}.upload-area[_ngcontent-%COMP%]{border:2px dashed #ddd;border-radius:8px;padding:40px;text-align:center;cursor:pointer;transition:all .2s}.upload-area[_ngcontent-%COMP%]:hover{border-color:#007bff;background:#f8f9ff}.upload-area.has-files[_ngcontent-%COMP%]{padding:20px}.upload-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:3rem;color:#ddd;margin-bottom:15px}.upload-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.1rem;margin-bottom:5px}.upload-content[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#666;font-size:.9rem}.file-preview[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(150px,1fr));gap:15px}.file-item[_ngcontent-%COMP%]{position:relative;border-radius:8px;overflow:hidden}.file-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .file-item[_ngcontent-%COMP%]   video[_ngcontent-%COMP%]{width:100%;height:150px;object-fit:cover}.remove-file[_ngcontent-%COMP%]{position:absolute;top:8px;right:8px;background:#000000b3;color:#fff;border:none;border-radius:50%;width:24px;height:24px;cursor:pointer}textarea[_ngcontent-%COMP%]{width:100%;padding:12px;border:1px solid #ddd;border-radius:6px;font-family:inherit;resize:vertical}.char-count[_ngcontent-%COMP%]{text-align:right;color:#666;font-size:.85rem;margin-top:5px}.search-input[_ngcontent-%COMP%]{width:100%;padding:12px;border:1px solid #ddd;border-radius:6px;margin-bottom:10px}.product-results[_ngcontent-%COMP%]{max-height:200px;overflow-y:auto;border:1px solid #eee;border-radius:6px}.product-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:12px;cursor:pointer;border-bottom:1px solid #f5f5f5}.product-item[_ngcontent-%COMP%]:hover{background:#f8f9fa}.product-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:50px;height:50px;object-fit:cover;border-radius:4px}.product-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:.9rem;margin-bottom:2px}.product-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:.85rem}.tagged-products[_ngcontent-%COMP%]{margin-top:15px}.tagged-list[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:10px;margin-top:10px}.tagged-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;background:#f8f9fa;padding:8px 12px;border-radius:20px;font-size:.85rem}.tagged-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:24px;height:24px;object-fit:cover;border-radius:50%}.tagged-item[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{background:none;border:none;color:#666;cursor:pointer;margin-left:5px}.hashtag-input[_ngcontent-%COMP%]{width:100%;padding:12px;border:1px solid #ddd;border-radius:6px}.hashtags[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:8px;margin-top:10px}.hashtag[_ngcontent-%COMP%]{background:#007bff;color:#fff;padding:4px 8px;border-radius:12px;font-size:.85rem;display:flex;align-items:center;gap:5px}.hashtag[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{background:none;border:none;color:#fff;cursor:pointer;font-size:1.1rem}.settings-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:15px}.setting-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;cursor:pointer}.form-actions[_ngcontent-%COMP%]{display:flex;gap:15px;justify-content:flex-end;margin-top:30px;padding-top:20px;border-top:1px solid #eee}.btn-primary[_ngcontent-%COMP%], .btn-secondary[_ngcontent-%COMP%]{padding:12px 24px;border-radius:6px;font-weight:500;cursor:pointer;border:none;transition:all .2s}.btn-primary[_ngcontent-%COMP%]{background:#007bff;color:#fff}.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled){background:#0056b3}.btn-primary[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.btn-secondary[_ngcontent-%COMP%]{background:#f8f9fa;color:#6c757d;border:1px solid #dee2e6}.btn-secondary[_ngcontent-%COMP%]:hover{background:#e9ecef}@media (max-width: 768px){.form-actions[_ngcontent-%COMP%]{flex-direction:column}.file-preview[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(120px,1fr))}}\"]\n      });\n    }\n  }\n  return CreatePostComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}