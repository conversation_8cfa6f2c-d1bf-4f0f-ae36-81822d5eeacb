.top-influencers-container {
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px;
  margin-bottom: 24px;
}

// Header Section
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  .header-content {
    flex: 1;
  }
  
  .section-title {
    font-size: 24px;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 12px;
    
    .title-icon {
      font-size: 28px;
      color: #ffd700;
    }
  }
  
  .section-subtitle {
    font-size: 14px;
    color: #666;
    margin: 0;
  }
}

// Loading States
.loading-container {
  .loading-grid {
    display: flex;
    gap: 20px;
    overflow-x: auto;
    padding-bottom: 8px;
  }
  
  .loading-influencer-card {
    flex: 0 0 220px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 16px;
    padding: 20px;
    
    .loading-avatar {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
      margin: 0 auto 16px;
    }
    
    .loading-content {
      .loading-line {
        height: 12px;
        border-radius: 6px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        margin-bottom: 8px;
        
        &.short { width: 60%; }
        &.medium { width: 80%; }
        &.long { width: 100%; }
      }
      
      .loading-stats {
        display: flex;
        gap: 10px;
        margin-top: 12px;
        
        .loading-stat {
          flex: 1;
          height: 20px;
          border-radius: 10px;
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200% 100%;
          animation: loading 1.5s infinite;
        }
      }
    }
  }
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

// Error State
.error-container {
  text-align: center;
  padding: 40px 20px;
  
  .error-icon {
    font-size: 48px;
    color: #e74c3c;
    margin-bottom: 16px;
  }
  
  .error-message {
    color: #666;
    margin-bottom: 20px;
    font-size: 16px;
  }
  
  .retry-btn {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
    }
  }
}

// Influencers Slider Styles
.influencers-slider-container {
  position: relative;
  margin: 0 -20px;
  
  .slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover:not(:disabled) {
      background: rgba(0, 0, 0, 0.9);
      transform: translateY(-50%) scale(1.1);
    }
    
    &:disabled {
      opacity: 0.3;
      cursor: not-allowed;
    }
    
    ion-icon {
      font-size: 18px;
    }
    
    &.prev-btn {
      left: -20px;
    }
    
    &.next-btn {
      right: -20px;
    }
  }
}

.influencers-slider-wrapper {
  overflow: hidden;
  padding: 0 20px;
}

.influencers-slider {
  display: flex;
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  gap: 20px;
  
  .influencer-card {
    flex: 0 0 220px;
    width: 220px;
  }
}

// Influencer Card Styles
.influencer-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
}

.influencer-avatar-container {
  position: relative;
  margin-bottom: 20px;
  
  .influencer-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #ffd700;
    margin: 0 auto;
    display: block;
  }
  
  .verified-badge {
    position: absolute;
    top: -5px;
    right: calc(50% - 55px);
    background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
    color: white;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    border: 3px solid white;
    
    ion-icon {
      font-size: 14px;
    }
  }
}

.influencer-info {
  margin-bottom: 20px;
  
  .influencer-name {
    font-size: 18px;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0 0 4px 0;
  }
  
  .username {
    font-size: 14px;
    color: #6c5ce7;
    margin: 0 0 8px 0;
    font-weight: 500;
  }
  
  .category {
    font-size: 12px;
    color: #ffd700;
    background: rgba(255, 215, 0, 0.1);
    padding: 4px 12px;
    border-radius: 12px;
    display: inline-block;
    margin: 0 0 16px 0;
    font-weight: 600;
  }
}

.stats-container {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  
  .stat {
    flex: 1;
    text-align: center;
    
    .stat-value {
      display: block;
      font-size: 16px;
      font-weight: 700;
      color: #1a1a1a;
      margin-bottom: 2px;
    }
    
    .stat-label {
      font-size: 11px;
      color: #666;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
}

.top-brands {
  .brands-label {
    font-size: 11px;
    color: #666;
    display: block;
    margin-bottom: 6px;
  }
  
  .brands-list {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    justify-content: center;
    
    .brand-tag {
      font-size: 10px;
      background: rgba(108, 92, 231, 0.1);
      color: #6c5ce7;
      padding: 2px 6px;
      border-radius: 8px;
      font-weight: 500;
    }
    
    .more-brands {
      font-size: 10px;
      color: #999;
      font-weight: 500;
    }
  }
}

.follow-btn {
  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
  color: #1a1a1a;
  border: none;
  padding: 10px 20px;
  border-radius: 25px;
  font-size: 13px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 6px;
  margin: 0 auto;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
  }
  
  &.following {
    background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
    color: white;
    
    &:hover {
      box-shadow: 0 8px 25px rgba(0, 184, 148, 0.4);
    }
  }
  
  ion-icon {
    font-size: 16px;
  }
}

// Empty State
.empty-container {
  text-align: center;
  padding: 60px 20px;
  
  .empty-icon {
    font-size: 64px;
    color: #ddd;
    margin-bottom: 20px;
  }
  
  .empty-title {
    font-size: 20px;
    font-weight: 600;
    color: #666;
    margin: 0 0 8px 0;
  }
  
  .empty-message {
    color: #999;
    margin: 0;
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .influencers-slider {
    .influencer-card {
      flex: 0 0 200px;
      width: 200px;
      padding: 20px;
    }
  }
}

@media (max-width: 768px) {
  .influencers-slider-container {
    margin: 0 -10px;
    
    .slider-nav {
      width: 35px;
      height: 35px;
      
      &.prev-btn {
        left: -15px;
      }
      
      &.next-btn {
        right: -15px;
      }
      
      ion-icon {
        font-size: 16px;
      }
    }
  }
  
  .influencers-slider-wrapper {
    padding: 0 10px;
  }
  
  .influencers-slider {
    gap: 15px;
    
    .influencer-card {
      flex: 0 0 180px;
      width: 180px;
      padding: 16px;
    }
  }
  
  .influencer-avatar-container .influencer-avatar {
    width: 80px;
    height: 80px;
  }
}

@media (max-width: 480px) {
  .influencers-slider {
    .influencer-card {
      flex: 0 0 170px;
      width: 170px;
    }
  }
}
