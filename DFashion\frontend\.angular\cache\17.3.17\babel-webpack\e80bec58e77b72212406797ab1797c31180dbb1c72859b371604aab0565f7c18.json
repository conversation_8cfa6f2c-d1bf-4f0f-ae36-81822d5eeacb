{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nfunction ExploreComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵlistener(\"click\", function ExploreComponent_div_15_Template_div_click_0_listener() {\n      const category_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navigateToCategory(category_r2.slug));\n    });\n    i0.ɵɵelementStart(1, \"div\", 20)(2, \"h3\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const category_r2 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + category_r2.image + \")\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(category_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", category_r2.itemCount, \" items\");\n  }\n}\nfunction ExploreComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"img\", 22);\n    i0.ɵɵelementStart(2, \"div\", 23)(3, \"h4\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 24);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const trend_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", trend_r4.image, i0.ɵɵsanitizeUrl)(\"alt\", trend_r4.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(trend_r4.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(trend_r4.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(trend_r4.tag);\n  }\n}\nfunction ExploreComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelement(1, \"img\", 22);\n    i0.ɵɵelementStart(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function ExploreComponent_div_25_Template_button_click_6_listener() {\n      const brand_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.exploreBrand(brand_r6.slug));\n    });\n    i0.ɵɵtext(7, \" Explore \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const brand_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", brand_r6.logo, i0.ɵɵsanitizeUrl)(\"alt\", brand_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(brand_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(brand_r6.description);\n  }\n}\nfunction ExploreComponent_div_30_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tag_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tag_r9);\n  }\n}\nfunction ExploreComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵlistener(\"click\", function ExploreComponent_div_30_Template_div_click_0_listener() {\n      const inspiration_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.viewInspiration(inspiration_r8.id));\n    });\n    i0.ɵɵelement(1, \"img\", 22);\n    i0.ɵɵelementStart(2, \"div\", 28)(3, \"h4\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 29);\n    i0.ɵɵtemplate(8, ExploreComponent_div_30_span_8_Template, 2, 1, \"span\", 30);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const inspiration_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", inspiration_r8.image, i0.ɵɵsanitizeUrl)(\"alt\", inspiration_r8.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(inspiration_r8.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(inspiration_r8.subtitle);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", inspiration_r8.tags);\n  }\n}\nexport let ExploreComponent = /*#__PURE__*/(() => {\n  class ExploreComponent {\n    constructor() {\n      this.searchQuery = '';\n      this.categories = [{\n        name: 'Women',\n        slug: 'women',\n        image: '/assets/images/categories/women.jpg',\n        itemCount: 1250\n      }, {\n        name: 'Men',\n        slug: 'men',\n        image: '/assets/images/categories/men.jpg',\n        itemCount: 890\n      }, {\n        name: 'Kids',\n        slug: 'kids',\n        image: '/assets/images/categories/kids.jpg',\n        itemCount: 450\n      }, {\n        name: 'Ethnic',\n        slug: 'ethnic',\n        image: '/assets/images/categories/ethnic.jpg',\n        itemCount: 320\n      }];\n      this.trendingItems = [];\n      this.featuredBrands = [{\n        name: 'StyleHub',\n        slug: 'stylehub',\n        logo: '/assets/images/brands/stylehub.png',\n        description: 'Contemporary fashion for modern lifestyle'\n      }, {\n        name: 'TrendWear',\n        slug: 'trendwear',\n        logo: '/assets/images/brands/trendwear.png',\n        description: 'Trendy and affordable fashion'\n      }, {\n        name: 'ElegantCo',\n        slug: 'elegantco',\n        logo: '/assets/images/brands/elegant.png',\n        description: 'Elegant and sophisticated designs'\n      }];\n      this.styleInspiration = [{\n        id: 1,\n        title: 'Office Chic',\n        subtitle: 'Professional yet stylish',\n        image: '/assets/images/inspiration/office.jpg',\n        tags: ['Professional', 'Elegant', 'Modern']\n      }, {\n        id: 2,\n        title: 'Weekend Casual',\n        subtitle: 'Comfortable and relaxed',\n        image: '/assets/images/inspiration/casual.jpg',\n        tags: ['Casual', 'Comfortable', 'Trendy']\n      }, {\n        id: 3,\n        title: 'Evening Glam',\n        subtitle: 'Glamorous night out looks',\n        image: '/assets/images/inspiration/evening.jpg',\n        tags: ['Glamorous', 'Party', 'Elegant']\n      }];\n    }\n    ngOnInit() {}\n    onSearch() {\n      if (this.searchQuery.trim()) {\n        // Navigate to search results\n        console.log('Searching for:', this.searchQuery);\n        // TODO: Implement search navigation\n      }\n    }\n    navigateToCategory(categorySlug) {\n      // Navigate to category page\n      console.log('Navigate to category:', categorySlug);\n      // TODO: Implement category navigation\n    }\n    exploreBrand(brandSlug) {\n      console.log('Explore brand:', brandSlug);\n      // TODO: Implement brand exploration\n    }\n    viewInspiration(inspirationId) {\n      console.log('View inspiration:', inspirationId);\n      // TODO: Implement inspiration view\n    }\n    static {\n      this.ɵfac = function ExploreComponent_Factory(t) {\n        return new (t || ExploreComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ExploreComponent,\n        selectors: [[\"app-explore\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 31,\n        vars: 5,\n        consts: [[1, \"explore-container\"], [1, \"hero-section\"], [1, \"hero-content\"], [1, \"search-bar\"], [\"type\", \"text\", \"placeholder\", \"Search for styles, brands, or trends...\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"categories-section\"], [1, \"categories-grid\"], [\"class\", \"category-card\", 3, \"background-image\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"trending-section\"], [1, \"trending-grid\"], [\"class\", \"trend-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"brands-section\"], [1, \"brands-grid\"], [\"class\", \"brand-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"inspiration-section\"], [1, \"inspiration-grid\"], [\"class\", \"inspiration-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"category-card\", 3, \"click\"], [1, \"category-overlay\"], [1, \"trend-item\"], [3, \"src\", \"alt\"], [1, \"trend-content\"], [1, \"trend-tag\"], [1, \"brand-card\"], [1, \"btn-explore\", 3, \"click\"], [1, \"inspiration-item\", 3, \"click\"], [1, \"inspiration-overlay\"], [1, \"inspiration-tags\"], [\"class\", \"tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"tag\"]],\n        template: function ExploreComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n            i0.ɵɵtext(4, \"Discover Fashion\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p\");\n            i0.ɵɵtext(6, \"Explore trending styles, discover new brands, and find your perfect look\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"div\", 3)(8, \"input\", 4);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function ExploreComponent_Template_input_ngModelChange_8_listener($event) {\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery, $event) || (ctx.searchQuery = $event);\n              return $event;\n            });\n            i0.ɵɵlistener(\"keyup.enter\", function ExploreComponent_Template_input_keyup_enter_8_listener() {\n              return ctx.onSearch();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"button\", 5);\n            i0.ɵɵlistener(\"click\", function ExploreComponent_Template_button_click_9_listener() {\n              return ctx.onSearch();\n            });\n            i0.ɵɵelement(10, \"i\", 6);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(11, \"div\", 7)(12, \"h2\");\n            i0.ɵɵtext(13, \"Shop by Category\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"div\", 8);\n            i0.ɵɵtemplate(15, ExploreComponent_div_15_Template, 6, 4, \"div\", 9);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 10)(17, \"h2\");\n            i0.ɵɵtext(18, \"Trending Now\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"div\", 11);\n            i0.ɵɵtemplate(20, ExploreComponent_div_20_Template, 9, 5, \"div\", 12);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(21, \"div\", 13)(22, \"h2\");\n            i0.ɵɵtext(23, \"Featured Brands\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"div\", 14);\n            i0.ɵɵtemplate(25, ExploreComponent_div_25_Template, 8, 4, \"div\", 15);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(26, \"div\", 16)(27, \"h2\");\n            i0.ɵɵtext(28, \"Style Inspiration\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"div\", 17);\n            i0.ɵɵtemplate(30, ExploreComponent_div_30_Template, 9, 5, \"div\", 18);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(8);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngForOf\", ctx.trendingItems);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngForOf\", ctx.featuredBrands);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngForOf\", ctx.styleInspiration);\n          }\n        },\n        dependencies: [CommonModule, i1.NgForOf, RouterModule, FormsModule, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgModel],\n        styles: [\".explore-container[_ngcontent-%COMP%]{padding:80px 0 40px;min-height:100vh}.hero-section[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;padding:80px 20px;text-align:center;margin-bottom:60px}.hero-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:3rem;font-weight:700;margin-bottom:1rem}.hero-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.2rem;margin-bottom:2rem;opacity:.9}.search-bar[_ngcontent-%COMP%]{display:flex;max-width:500px;margin:0 auto;background:#fff;border-radius:50px;overflow:hidden;box-shadow:0 10px 30px #0003}.search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{flex:1;padding:15px 20px;border:none;font-size:1rem;color:#333}.search-bar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{background:#667eea;border:none;padding:15px 20px;color:#fff;cursor:pointer;transition:background .3s}.search-bar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{background:#5a67d8}.categories-section[_ngcontent-%COMP%], .trending-section[_ngcontent-%COMP%], .brands-section[_ngcontent-%COMP%], .inspiration-section[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto 60px;padding:0 20px}.categories-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .trending-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .brands-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .inspiration-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:2rem;font-weight:600;margin-bottom:2rem;text-align:center}.categories-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px}.category-card[_ngcontent-%COMP%]{height:200px;border-radius:16px;background-size:cover;background-position:center;position:relative;cursor:pointer;transition:transform .3s ease;overflow:hidden}.category-card[_ngcontent-%COMP%]:hover{transform:translateY(-5px)}.category-overlay[_ngcontent-%COMP%]{position:absolute;bottom:0;left:0;right:0;background:linear-gradient(transparent,#000c);color:#fff;padding:20px}.category-overlay[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:600;margin-bottom:.5rem}.trending-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:30px}.trend-item[_ngcontent-%COMP%]{background:#fff;border-radius:16px;overflow:hidden;box-shadow:0 5px 20px #0000001a;transition:transform .3s ease}.trend-item[_ngcontent-%COMP%]:hover{transform:translateY(-5px)}.trend-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:200px;object-fit:cover}.trend-content[_ngcontent-%COMP%]{padding:20px}.trend-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;margin-bottom:.5rem}.trend-tag[_ngcontent-%COMP%]{background:#667eea;color:#fff;padding:4px 12px;border-radius:20px;font-size:.8rem;font-weight:500}.brands-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:30px}.brand-card[_ngcontent-%COMP%]{background:#fff;padding:30px;border-radius:16px;text-align:center;box-shadow:0 5px 20px #0000001a;transition:transform .3s ease}.brand-card[_ngcontent-%COMP%]:hover{transform:translateY(-5px)}.brand-card[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:80px;height:80px;object-fit:contain;margin-bottom:1rem}.btn-explore[_ngcontent-%COMP%]{background:#667eea;color:#fff;border:none;padding:10px 20px;border-radius:25px;cursor:pointer;transition:background .3s;margin-top:1rem}.btn-explore[_ngcontent-%COMP%]:hover{background:#5a67d8}.inspiration-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:20px}.inspiration-item[_ngcontent-%COMP%]{position:relative;height:300px;border-radius:16px;overflow:hidden;cursor:pointer;transition:transform .3s ease}.inspiration-item[_ngcontent-%COMP%]:hover{transform:scale(1.02)}.inspiration-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.inspiration-overlay[_ngcontent-%COMP%]{position:absolute;bottom:0;left:0;right:0;background:linear-gradient(transparent,#000c);color:#fff;padding:20px}.inspiration-tags[_ngcontent-%COMP%]{display:flex;gap:8px;margin-top:10px}.tag[_ngcontent-%COMP%]{background:#fff3;padding:4px 8px;border-radius:12px;font-size:.8rem}@media (max-width: 768px){.hero-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem}.categories-grid[_ngcontent-%COMP%], .trending-grid[_ngcontent-%COMP%], .brands-grid[_ngcontent-%COMP%], .inspiration-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}\"]\n      });\n    }\n  }\n  return ExploreComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}