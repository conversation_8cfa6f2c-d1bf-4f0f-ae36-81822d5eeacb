{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { TrendingProductsComponent } from '../trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../new-arrivals/new-arrivals.component';\nimport { SuggestedForYouComponent } from '../suggested-for-you/suggested-for-you.component';\nimport { TopFashionInfluencersComponent } from '../top-fashion-influencers/top-fashion-influencers.component';\nimport { ShopByCategoryComponent } from '../shop-by-category/shop-by-category.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/product.service\";\nimport * as i2 from \"@angular/router\";\nexport let SidebarComponent = /*#__PURE__*/(() => {\n  class SidebarComponent {\n    constructor(productService, router) {\n      this.productService = productService;\n      this.router = router;\n      this.suggestedUsers = [];\n      this.trendingProducts = [];\n      this.topInfluencers = [];\n      this.categories = [];\n    }\n    ngOnInit() {\n      this.loadSuggestedUsers();\n      this.loadTrendingProducts();\n      this.loadTopInfluencers();\n      this.loadCategories();\n    }\n    loadSuggestedUsers() {\n      // Mock data for suggested users\n      this.suggestedUsers = [{\n        id: '1',\n        username: 'fashionista_maya',\n        fullName: 'Maya Chen',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',\n        followedBy: 'Followed by 12 others',\n        isFollowing: false\n      }, {\n        id: '2',\n        username: 'style_guru_alex',\n        fullName: 'Alex Rodriguez',\n        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',\n        followedBy: 'Followed by 8 others',\n        isFollowing: false\n      }];\n    }\n    loadTrendingProducts() {\n      this.trendingProducts = [];\n    }\n    loadTopInfluencers() {\n      // Mock data for top influencers\n      this.topInfluencers = [{\n        id: '1',\n        username: 'fashion_queen',\n        fullName: 'Priya Sharma',\n        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',\n        followersCount: 25000,\n        postsCount: 156,\n        engagement: 8.5,\n        isFollowing: false\n      }, {\n        id: '2',\n        username: 'style_maven',\n        fullName: 'Kavya Reddy',\n        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150',\n        followersCount: 18000,\n        postsCount: 89,\n        engagement: 12.3,\n        isFollowing: true\n      }];\n    }\n    loadCategories() {\n      // Mock data for categories\n      this.categories = [{\n        id: '1',\n        name: 'Women',\n        slug: 'women',\n        image: 'https://images.unsplash.com/photo-1483985988355-763728e1935b?w=400'\n      }, {\n        id: '2',\n        name: 'Men',\n        slug: 'men',\n        image: 'https://images.unsplash.com/photo-1516257984-b1b4d707412e?w=400'\n      }, {\n        id: '3',\n        name: 'Accessories',\n        slug: 'accessories',\n        image: 'https://images.unsplash.com/photo-1506629905607-d405b7a30db9?w=400'\n      }, {\n        id: '4',\n        name: 'Footwear',\n        slug: 'footwear',\n        image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400'\n      }];\n    }\n    formatFollowerCount(count) {\n      if (count >= 1000) {\n        return (count / 1000).toFixed(1) + 'k';\n      }\n      return count.toString();\n    }\n    followUser(userId) {\n      const user = this.suggestedUsers.find(u => u.id === userId);\n      if (user) {\n        user.isFollowing = !user.isFollowing;\n      }\n    }\n    followInfluencer(influencerId) {\n      const influencer = this.topInfluencers.find(i => i.id === influencerId);\n      if (influencer) {\n        influencer.isFollowing = !influencer.isFollowing;\n      }\n    }\n    quickBuy(productId) {\n      console.log('Quick buy product:', productId);\n      // TODO: Implement quick buy functionality\n    }\n    browseCategory(categorySlug) {\n      console.log('Browse category:', categorySlug);\n      this.router.navigate(['/category', categorySlug]);\n    }\n    static {\n      this.ɵfac = function SidebarComponent_Factory(t) {\n        return new (t || SidebarComponent)(i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SidebarComponent,\n        selectors: [[\"app-sidebar\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 7,\n        vars: 0,\n        consts: [[1, \"sidebar\"]],\n        template: function SidebarComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"aside\", 0);\n            i0.ɵɵelement(1, \"app-trending-products\")(2, \"app-featured-brands\")(3, \"app-new-arrivals\")(4, \"app-suggested-for-you\")(5, \"app-top-fashion-influencers\")(6, \"app-shop-by-category\");\n            i0.ɵɵelementEnd();\n          }\n        },\n        dependencies: [CommonModule, RouterModule, IonicModule, TrendingProductsComponent, FeaturedBrandsComponent, NewArrivalsComponent, SuggestedForYouComponent, TopFashionInfluencersComponent, ShopByCategoryComponent],\n        styles: [\"@charset \\\"UTF-8\\\";.sidebar[_ngcontent-%COMP%]{position:sticky;top:80px;height:-moz-fit-content;height:fit-content;display:flex;flex-direction:column;gap:24px;max-height:calc(100vh - 100px);overflow-y:auto;padding-right:8px}app-trending-products[_ngcontent-%COMP%], app-featured-brands[_ngcontent-%COMP%], app-new-arrivals[_ngcontent-%COMP%]{width:100%;display:block;margin-bottom:24px}.suggestions[_ngcontent-%COMP%]{padding:20px;background:linear-gradient(135deg,#f8f9fa,#e9ecef);border-radius:16px;margin-bottom:24px}.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:24px}.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{flex:1}.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#1a1a1a;margin:0 0 8px;display:flex;align-items:center;gap:12px}.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%]{font-size:28px;color:#6c5ce7}.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%]{font-size:14px;color:#666;margin:0}.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#6c5ce7,#a29bfe);color:#fff;border:none;padding:12px 20px;border-radius:25px;font-weight:600;font-size:14px;display:flex;align-items:center;gap:8px;cursor:pointer;transition:all .3s ease;box-shadow:0 4px 15px #6c5ce74d}.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 6px 20px #6c5ce766}.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#1a1a1a;margin:0 0 24px;display:flex;align-items:center;gap:12px}.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:before{content:\\\"\\\\1f4a1\\\";font-size:28px}.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px}.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]{background:#fff;border-radius:16px;overflow:hidden;box-shadow:0 4px 20px #00000014;transition:all .3s ease;cursor:pointer;padding:16px;display:flex;align-items:center;gap:16px}.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 30px #0000001f}.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;object-fit:cover;border:3px solid #f8f9fa}.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-info[_ngcontent-%COMP%]{flex:1}.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{margin:0 0 4px;font-size:16px;font-weight:600;color:#1a1a1a}.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:12px;color:#666}.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#6c5ce7,#a29bfe);color:#fff;border:none;padding:8px 16px;border-radius:8px;font-size:12px;font-weight:600;cursor:pointer;transition:all .3s ease}.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 15px #6c5ce74d}.influencers[_ngcontent-%COMP%]{padding:20px;background:linear-gradient(135deg,#f8f9fa,#e9ecef);border-radius:16px;margin-bottom:24px}.influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#1a1a1a;margin:0 0 24px;display:flex;align-items:center;gap:12px}.influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:before{content:\\\"\\\\1f451\\\";font-size:28px}.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:20px}.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]{background:#fff;border-radius:16px;overflow:hidden;box-shadow:0 4px 20px #00000014;transition:all .3s ease;cursor:pointer;padding:20px;display:flex;align-items:flex-start;gap:16px}.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 30px #0000001f}.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:70px;height:70px;border-radius:50%;object-fit:cover;border:3px solid #f8f9fa}.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]{flex:1}.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{margin:0 0 6px;font-size:16px;font-weight:600;color:#1a1a1a}.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 12px;font-size:14px;color:#666;font-weight:500}.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-stats[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:6px;margin-bottom:16px}.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:12px;color:#666;background:#f8f9fa;padding:4px 8px;border-radius:12px;width:-moz-fit-content;width:fit-content;font-weight:500}.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fd79a8,#e84393);color:#fff;border:none;padding:10px 20px;border-radius:8px;font-size:12px;font-weight:600;cursor:pointer;transition:all .3s ease}.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 15px #e843934d}.categories[_ngcontent-%COMP%]{padding:20px;background:linear-gradient(135deg,#f8f9fa,#e9ecef);border-radius:16px;margin-bottom:24px}.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#1a1a1a;margin:0 0 24px;display:flex;align-items:center;gap:12px}.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:before{content:\\\"\\\\1f6cd\\\\fe0f\\\";font-size:28px}.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(2,1fr);gap:16px}.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]{background:#fff;border-radius:16px;overflow:hidden;box-shadow:0 4px 20px #00000014;transition:all .3s ease;cursor:pointer;padding:20px 16px;display:flex;flex-direction:column;align-items:center;text-align:center;text-decoration:none;color:inherit}.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 30px #0000001f}.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:70px;height:70px;border-radius:50%;object-fit:cover;margin-bottom:12px;border:3px solid #f8f9fa;transition:transform .3s ease}.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%]{transform:scale(1.1)}.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#1a1a1a;text-align:center}@media (max-width: 768px){.suggestions[_ngcontent-%COMP%], .influencers[_ngcontent-%COMP%], .categories[_ngcontent-%COMP%]{padding:16px}.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:20px}.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:before, .influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:before, .categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:before{font-size:24px}.suggestion-item[_ngcontent-%COMP%], .influencer-item[_ngcontent-%COMP%]{padding:12px;gap:12px}.suggestion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:50px;height:50px}.suggestion-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .influencer-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{font-size:14px}.category-list[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:12px}.category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]{flex-direction:row;text-align:left;padding:12px;gap:12px}.category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:50px;height:50px;margin-bottom:0}.category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:12px}}\"]\n      });\n    }\n  }\n  return SidebarComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}