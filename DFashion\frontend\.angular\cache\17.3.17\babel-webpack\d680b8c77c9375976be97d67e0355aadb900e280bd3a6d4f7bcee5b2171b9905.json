{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { OptimizedImageComponent } from '../../../../shared/components/optimized-image/optimized-image.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@ionic/angular\";\nfunction ProfileComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"ion-spinner\", 5);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading profile...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProfileComponent_div_2_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_2_div_15_Template_div_click_0_listener() {\n      const feature_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      return i0.ɵɵresetView(feature_r3.action());\n    });\n    i0.ɵɵelement(1, \"ion-icon\", 26);\n    i0.ɵɵelementStart(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const feature_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", feature_r3.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(feature_r3.title);\n  }\n}\nfunction ProfileComponent_div_2_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"h3\", 13);\n    i0.ɵɵtext(2, \"Vendor Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Access your vendor dashboard to manage products, view sales analytics, and handle orders.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_2_div_45_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.navigateToVendorDashboard());\n    });\n    i0.ɵɵelement(6, \"ion-icon\", 27);\n    i0.ɵɵtext(7, \" Go to Vendor Dashboard \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProfileComponent_div_2_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"h3\", 13);\n    i0.ɵɵtext(2, \"Administrator Access\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Access the admin panel to manage users, products, and system settings.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_2_div_46_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.navigateToAdminPanel());\n    });\n    i0.ɵɵelement(6, \"ion-icon\", 28);\n    i0.ɵɵtext(7, \" Go to Admin Panel \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProfileComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7);\n    i0.ɵɵelement(2, \"app-optimized-image\", 8);\n    i0.ɵɵelementStart(3, \"div\", 9)(4, \"h2\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 10);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 11)(11, \"div\", 12)(12, \"h3\", 13);\n    i0.ɵɵtext(13, \"Available Features\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 14);\n    i0.ɵɵtemplate(15, ProfileComponent_div_2_div_15_Template, 4, 2, \"div\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 12)(17, \"h3\", 13);\n    i0.ɵɵtext(18, \"Account Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"ul\", 16)(20, \"li\", 17)(21, \"span\");\n    i0.ɵɵtext(22, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\");\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"li\", 17)(26, \"span\");\n    i0.ɵɵtext(27, \"Username\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"li\", 17)(31, \"span\");\n    i0.ɵɵtext(32, \"Account Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"span\");\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"li\", 17)(36, \"span\");\n    i0.ɵɵtext(37, \"Account Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"span\");\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"li\", 17)(41, \"span\");\n    i0.ɵɵtext(42, \"Verification Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"span\");\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(45, ProfileComponent_div_2_div_45_Template, 8, 0, \"div\", 18)(46, ProfileComponent_div_2_div_46_Template, 8, 0, \"div\", 18);\n    i0.ɵɵelementStart(47, \"div\", 12)(48, \"h3\", 13);\n    i0.ɵɵtext(49, \"Quick Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 19)(51, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_2_Template_button_click_51_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.editProfile());\n    });\n    i0.ɵɵelement(52, \"ion-icon\", 21);\n    i0.ɵɵtext(53, \" Edit Profile \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_2_Template_button_click_54_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.navigateToSettings());\n    });\n    i0.ɵɵelement(55, \"ion-icon\", 23);\n    i0.ɵɵtext(56, \" Settings \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_2_Template_button_click_57_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.logout());\n    });\n    i0.ɵɵelement(58, \"ion-icon\", 24);\n    i0.ɵɵtext(59, \" Logout \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r4.currentUser.avatar)(\"alt\", ctx_r4.currentUser.fullName)(\"width\", 80)(\"height\", 80);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.currentUser.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(\"@\" + ctx_r4.currentUser.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getRoleDisplayName());\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.getRoleFeatures());\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r4.currentUser.email);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r4.currentUser.username);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r4.getRoleDisplayName());\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r4.currentUser.isActive ? \"text-success\" : \"text-danger\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.currentUser.isActive ? \"Active\" : \"Inactive\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r4.currentUser.isVerified ? \"text-success\" : \"text-warning\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.currentUser.isVerified ? \"Verified\" : \"Pending Verification\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.canAccessVendorDashboard());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.canAccessAdminPanel());\n  }\n}\nfunction ProfileComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵelement(1, \"ion-icon\", 30);\n    i0.ɵɵelementStart(2, \"h3\");\n    i0.ɵɵtext(3, \"No User Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Please log in to view your profile.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_3_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.navigateToLogin());\n    });\n    i0.ɵɵtext(7, \" Login \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let ProfileComponent = /*#__PURE__*/(() => {\n  class ProfileComponent {\n    constructor(authService, router) {\n      this.authService = authService;\n      this.router = router;\n      this.currentUser = null;\n      this.isLoading = true;\n    }\n    ngOnInit() {\n      this.loadUserProfile();\n    }\n    loadUserProfile() {\n      this.currentUser = this.authService.currentUserValue;\n      this.isLoading = false;\n    }\n    // Role-based feature access\n    canAccessVendorDashboard() {\n      return this.authService.isVendor() || this.authService.isAdmin();\n    }\n    canAccessAdminPanel() {\n      return this.authService.isAdmin();\n    }\n    canManageProducts() {\n      return this.authService.isVendor() || this.authService.isAdmin();\n    }\n    canViewAnalytics() {\n      return this.authService.isVendor() || this.authService.isAdmin();\n    }\n    // Navigation methods\n    navigateToVendorDashboard() {\n      if (this.canAccessVendorDashboard()) {\n        this.router.navigate(['/vendor/dashboard']);\n      }\n    }\n    navigateToAdminPanel() {\n      if (this.canAccessAdminPanel()) {\n        this.router.navigate(['/admin/dashboard']);\n      }\n    }\n    navigateToOrders() {\n      this.router.navigate(['/account/orders']);\n    }\n    navigateToWishlist() {\n      this.router.navigate(['/wishlist']);\n    }\n    navigateToCart() {\n      this.router.navigate(['/cart']);\n    }\n    navigateToSettings() {\n      this.router.navigate(['/account/settings']);\n    }\n    editProfile() {\n      this.router.navigate(['/account/edit-profile']);\n    }\n    logout() {\n      this.authService.logout();\n      this.router.navigate(['/auth/login']);\n    }\n    navigateToLogin() {\n      this.router.navigate(['/auth/login']);\n    }\n    getRoleDisplayName() {\n      switch (this.currentUser?.role) {\n        case 'customer':\n          return 'Customer';\n        case 'vendor':\n          return 'Vendor';\n        case 'admin':\n          return 'Administrator';\n        default:\n          return 'User';\n      }\n    }\n    getRoleFeatures() {\n      const baseFeatures = [{\n        icon: 'person-outline',\n        title: 'Edit Profile',\n        action: () => this.editProfile()\n      }, {\n        icon: 'bag-outline',\n        title: 'My Orders',\n        action: () => this.navigateToOrders()\n      }, {\n        icon: 'heart-outline',\n        title: 'Wishlist',\n        action: () => this.navigateToWishlist()\n      }, {\n        icon: 'cart-outline',\n        title: 'Shopping Cart',\n        action: () => this.navigateToCart()\n      }, {\n        icon: 'settings-outline',\n        title: 'Settings',\n        action: () => this.navigateToSettings()\n      }];\n      if (this.canAccessVendorDashboard()) {\n        baseFeatures.push({\n          icon: 'storefront-outline',\n          title: 'Vendor Dashboard',\n          action: () => this.navigateToVendorDashboard()\n        }, {\n          icon: 'cube-outline',\n          title: 'Manage Products',\n          action: () => this.router.navigate(['/vendor/products'])\n        }, {\n          icon: 'analytics-outline',\n          title: 'Sales Analytics',\n          action: () => this.router.navigate(['/vendor/analytics'])\n        });\n      }\n      if (this.canAccessAdminPanel()) {\n        baseFeatures.push({\n          icon: 'shield-outline',\n          title: 'Admin Panel',\n          action: () => this.navigateToAdminPanel()\n        }, {\n          icon: 'people-outline',\n          title: 'User Management',\n          action: () => this.router.navigate(['/admin/users'])\n        }, {\n          icon: 'bar-chart-outline',\n          title: 'System Analytics',\n          action: () => this.router.navigate(['/admin/analytics'])\n        });\n      }\n      return baseFeatures;\n    }\n    static {\n      this.ɵfac = function ProfileComponent_Factory(t) {\n        return new (t || ProfileComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProfileComponent,\n        selectors: [[\"app-profile\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 4,\n        vars: 3,\n        consts: [[1, \"profile-container\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"profile-content\", 4, \"ngIf\"], [\"class\", \"no-user-state\", 4, \"ngIf\"], [1, \"loading-container\"], [\"name\", \"crescent\"], [1, \"profile-content\"], [1, \"profile-header\"], [\"fallbackType\", \"user\", \"containerClass\", \"profile-avatar\", \"imageClass\", \"avatar-img\", \"objectFit\", \"cover\", 3, \"src\", \"alt\", \"width\", \"height\"], [1, \"profile-info\"], [1, \"profile-role\"], [1, \"profile-sections\"], [1, \"profile-section\"], [1, \"section-title\"], [1, \"role-features\"], [\"class\", \"feature-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"settings-list\"], [1, \"settings-item\"], [\"class\", \"profile-section\", 4, \"ngIf\"], [1, \"action-buttons\"], [1, \"btn-primary\", 3, \"click\"], [\"name\", \"create-outline\"], [1, \"btn-secondary\", 3, \"click\"], [\"name\", \"settings-outline\"], [\"name\", \"log-out-outline\"], [1, \"feature-card\", 3, \"click\"], [1, \"feature-icon\", 3, \"name\"], [\"name\", \"storefront-outline\"], [\"name\", \"shield-outline\"], [1, \"no-user-state\"], [\"name\", \"person-outline\", 1, \"large-icon\"]],\n        template: function ProfileComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, ProfileComponent_div_1_Template, 4, 0, \"div\", 1)(2, ProfileComponent_div_2_Template, 60, 19, \"div\", 2)(3, ProfileComponent_div_3_Template, 8, 0, \"div\", 3);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.currentUser);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.currentUser);\n          }\n        },\n        dependencies: [CommonModule, i3.NgForOf, i3.NgIf, FormsModule, IonicModule, i4.IonIcon, i4.IonSpinner, OptimizedImageComponent],\n        styles: [\".profile-container[_ngcontent-%COMP%]{padding:20px;max-width:800px;margin:0 auto}.profile-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:20px;margin-bottom:30px;padding:20px;background:linear-gradient(135deg,#667eea,#764ba2);border-radius:12px;color:#fff}.profile-avatar[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;border:3px solid white;overflow:hidden;flex-shrink:0}.avatar-img[_ngcontent-%COMP%]{border-radius:50%}.profile-info[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;font-size:1.5rem}.profile-role[_ngcontent-%COMP%]{background:#fff3;padding:4px 12px;border-radius:20px;font-size:.9rem;margin-top:8px;display:inline-block}.profile-sections[_ngcontent-%COMP%]{display:grid;gap:20px}.profile-section[_ngcontent-%COMP%]{background:#fff;border-radius:12px;padding:20px;box-shadow:0 2px 10px #0000001a}.section-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:600;margin-bottom:15px;color:#333}.role-features[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:15px}.feature-card[_ngcontent-%COMP%]{padding:15px;border:1px solid #e0e0e0;border-radius:8px;text-align:center;cursor:pointer;transition:all .3s ease}.feature-card[_ngcontent-%COMP%]:hover{border-color:#667eea;transform:translateY(-2px)}.feature-icon[_ngcontent-%COMP%]{font-size:2rem;margin-bottom:10px;color:#667eea}.settings-list[_ngcontent-%COMP%]{list-style:none;padding:0}.settings-item[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:12px 0;border-bottom:1px solid #f0f0f0}.settings-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.btn-primary[_ngcontent-%COMP%]{background:#667eea;color:#fff;border:none;padding:10px 20px;border-radius:6px;cursor:pointer}.btn-secondary[_ngcontent-%COMP%]{background:#f8f9fa;color:#333;border:1px solid #ddd;padding:10px 20px;border-radius:6px;cursor:pointer}.action-buttons[_ngcontent-%COMP%]{display:flex;gap:10px;flex-wrap:wrap}.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.loading-container[_ngcontent-%COMP%], .no-user-state[_ngcontent-%COMP%]{text-align:center;padding:40px}.large-icon[_ngcontent-%COMP%]{font-size:4rem;color:#ccc;margin-bottom:20px}.text-success[_ngcontent-%COMP%]{color:#28a745}.text-danger[_ngcontent-%COMP%]{color:#dc3545}.text-warning[_ngcontent-%COMP%]{color:#ffc107}@media (max-width: 768px){.profile-header[_ngcontent-%COMP%]{flex-direction:column;text-align:center}.role-features[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fit,minmax(150px,1fr))}.action-buttons[_ngcontent-%COMP%]{flex-direction:column}}\"]\n      });\n    }\n  }\n  return ProfileComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}