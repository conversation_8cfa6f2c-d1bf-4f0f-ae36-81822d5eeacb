{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let MediaService = /*#__PURE__*/(() => {\n  class MediaService {\n    constructor() {\n      this.mediaErrors = new BehaviorSubject([]);\n      this.mediaErrors$ = this.mediaErrors.asObservable();\n      // Fallback images for different scenarios\n      this.fallbackImages = {\n        user: '/assets/images/default-avatar.svg',\n        product: '/assets/images/default-product.svg',\n        post: '/assets/images/default-post.svg',\n        story: '/assets/images/default-story.svg'\n      };\n      // Backup fallback images (simple colored placeholders)\n      this.backupFallbacks = {\n        user: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFNUU3RUIiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik0xMiAxNEM5LjMzIDEzLjk5IDcuMDEgMTUuNjIgNiAxOEMxMC4wMSAyMCAxMy45OSAyMCAxOCAxOEMxNi45OSAxNS42MiAxNC42NyAxMy45OSAxMiAxNFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+Cjwvc3ZnPgo=',\n        product: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyOEMxNi42ODYzIDI4IDEzLjUwNTQgMjYuNjgzOSAxMS4xNzE2IDI0LjM1MDNDOC44Mzc4NCAyMi4wMTY3IDcuNTIxNzMgMTguODM1OCA3LjUyMTczIDE1LjUyMTdDNy41MjE3MyAxMi4yMDc2IDguODM3ODQgOS4wMjY3IDExLjE3MTYgNi42OTMwNEMxMy41MDU0IDQuMzU5MzggMTYuNjg2MyAzLjA0MzQ4IDIwIDMuMDQzNDhDMjMuMzEzNyAzLjA0MzQ4IDI2LjQ5NDYgNC4zNTkzOCAyOC44Mjg0IDYuNjkzMDRDMzEuMTYyMiA5LjAyNjcgMzIuNDc4MyAxMi4yMDc2IDMyLjQ3ODMgMTUuNTIxN0MzMi40NzgzIDE4LjgzNTggMzEuMTYyMiAyMi4wMTY3IDI4LjgyODQgMjQuMzUwM0MyNi40OTQ2IDI2LjY4MzkgMjMuMzEzNyAyOCAyMCAyOFoiIGZpbGw9IiNEMUQ1REIiLz4KPC9zdmc+',\n        post: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRkJGQ0ZEIi8+CjxwYXRoIGQ9Ik0yMCAyOEMxNi42ODYzIDI4IDEzLjUwNTQgMjYuNjgzOSAxMS4xNzE2IDI0LjM1MDNDOC44Mzc4NCAyMi4wMTY3IDcuNTIxNzMgMTguODM1OCA3LjUyMTczIDE1LjUyMTdDNy41MjE3MyAxMi4yMDc2IDguODM3ODQgOS4wMjY3IDExLjE3MTYgNi42OTMwNEMxMy41MDU0IDQuMzU5MzggMTYuNjg2MyAzLjA0MzQ4IDIwIDMuMDQzNDhDMjMuMzEzNyAzLjA0MzQ4IDI2LjQ5NDYgNC4zNTkzOCAyOC44Mjg0IDYuNjkzMDRDMzEuMTYyMiA5LjAyNjcgMzIuNDc4MyAxMi4yMDc2IDMyLjQ3ODMgMTUuNTIxN0MzMi40NzgzIDE4LjgzNTggMzEuMTYyMiAyMi4wMTY3IDI4LjgyODQgMjQuMzUwM0MyNi40OTQ2IDI2LjY4MzkgMjMuMzEzNyAyOCAyMCAyOFoiIGZpbGw9IiNFNUU3RUIiLz4KPC9zdmc+',\n        story: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRkVGM0Y0Ii8+CjxwYXRoIGQ9Ik0yMCAyOEMxNi42ODYzIDI4IDEzLjUwNTQgMjYuNjgzOSAxMS4xNzE2IDI0LjM1MDNDOC44Mzc4NCAyMi4wMTY3IDcuNTIxNzMgMTguODM1OCA3LjUyMTczIDE1LjUyMTdDNy41MjE3MyAxMi4yMDc2IDguODM3ODQgOS4wMjY3IDExLjE3MTYgNi42OTMwNEMxMy41MDU0IDQuMzU5MzggMTYuNjg2MyAzLjA0MzQ4IDIwIDMuMDQzNDhDMjMuMzEzNyAzLjA0MzQ4IDI2LjQ5NDYgNC4zNTkzOCAyOC44Mjg0IDYuNjkzMDRDMzEuMTYyMiA5LjAyNjcgMzIuNDc4MyAxMi4yMDc2IDMyLjQ3ODMgMTUuNTIxN0MzMi40NzgzIDE4LjgzNTggMzEuMTYyMiAyMi4wMTY3IDI4LjgyODQgMjQuMzUwM0MyNi40OTQ2IDI2LjY4MzkgMjMuMzEzNyAyOCAyMCAyOFoiIGZpbGw9IiNGQ0E1QTUiLz4KPC9zdmc+'\n      };\n      // Video library - loaded from API\n      this.videos = [];\n      // Broken URL patterns to fix\n      this.brokenUrlPatterns = ['/uploads/stories/images/', '/uploads/stories/videos/', 'sample-videos.com', 'localhost:4200/assets/', 'file://'];\n    }\n    /**\n     * Get a reliable fallback image that always works\n     */\n    getReliableFallback(type = 'post') {\n      return this.backupFallbacks[type];\n    }\n    /**\n     * Check if an image URL is likely to fail\n     */\n    isLikelyToFail(url) {\n      return this.isExternalImageUrl(url) || this.isBrokenUrl(url);\n    }\n    /**\n     * Get a safe image URL with fallback handling and broken URL fixing\n     */\n    getSafeImageUrl(url, type = 'post') {\n      if (!url || url.trim() === '') {\n        return this.backupFallbacks[type]; // Use base64 fallback for empty URLs\n      }\n      // Fix broken URLs\n      const fixedUrl = this.fixBrokenUrl(url, type);\n      if (fixedUrl !== url) {\n        return fixedUrl;\n      }\n      // Handle localhost URLs that might be broken\n      if (url.includes('localhost:4200/assets/')) {\n        const assetPath = url.split('localhost:4200')[1];\n        return assetPath;\n      }\n      // For external images that might fail, provide a more reliable fallback\n      if (this.isExternalImageUrl(url)) {\n        // Return the URL but we know it might fail and will fallback gracefully\n        return url;\n      }\n      // Check if URL is valid\n      try {\n        new URL(url);\n        return url;\n      } catch {\n        // If not a valid URL, treat as relative path\n        if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {\n          return url;\n        }\n        // Return base64 fallback for invalid URLs\n        return this.backupFallbacks[type];\n      }\n    }\n    /**\n     * Fix broken URLs by replacing them with working alternatives\n     */\n    fixBrokenUrl(url, type) {\n      // Check for broken patterns\n      for (const pattern of this.brokenUrlPatterns) {\n        if (url.includes(pattern)) {\n          // Replace with appropriate fallback or working URL\n          if (pattern === '/uploads/stories/images/' || pattern === '/uploads/stories/videos/') {\n            return this.getReplacementMediaUrl(url, type);\n          }\n          if (pattern === 'sample-videos.com') {\n            return this.getReliableFallback(type);\n          }\n          if (pattern === 'localhost:4200/assets/') {\n            // Extract the asset path and return it as relative\n            const assetPath = url.split('localhost:4200')[1];\n            return assetPath;\n          }\n          if (pattern === 'file://') {\n            return this.fallbackImages[type];\n          }\n        }\n      }\n      return url;\n    }\n    /**\n     * Get replacement media URL for broken local paths\n     */\n    getReplacementMediaUrl(originalUrl, type) {\n      // Map broken local URLs to working Unsplash URLs based on content\n      const urlMappings = {\n        'summer-collection': 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=800',\n        'behind-scenes': 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=800',\n        'customer-spotlight': 'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=800',\n        'styling-tips': 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800',\n        'design': 'https://images.unsplash.com/photo-1558769132-cb1aea458c5e?w=800'\n      };\n      // Try to match content from filename\n      for (const [key, replacementUrl] of Object.entries(urlMappings)) {\n        if (originalUrl.toLowerCase().includes(key)) {\n          return replacementUrl;\n        }\n      }\n      // Return appropriate fallback\n      return this.fallbackImages[type];\n    }\n    /**\n     * Handle image load errors with progressive fallback\n     */\n    handleImageError(event, fallbackType = 'post') {\n      const img = event.target;\n      if (!img) return;\n      const originalSrc = img.src;\n      // First try: Use SVG fallback from assets\n      if (!originalSrc.includes(this.fallbackImages[fallbackType]) && !originalSrc.startsWith('data:')) {\n        img.src = this.fallbackImages[fallbackType];\n        // Only log meaningful errors (not external image failures)\n        if (!this.isExternalImageUrl(originalSrc) && !originalSrc.includes('localhost:4200')) {\n          this.logMediaError({\n            id: originalSrc,\n            type: 'load_error',\n            message: `Failed to load image: ${originalSrc}`,\n            fallbackUrl: this.fallbackImages[fallbackType]\n          });\n        }\n        return;\n      }\n      // Second try: Use base64 backup fallback if SVG also fails\n      if (originalSrc.includes(this.fallbackImages[fallbackType])) {\n        img.src = this.backupFallbacks[fallbackType];\n        // Only warn for local asset failures, not external\n        if (!this.isExternalImageUrl(originalSrc)) {\n          console.warn(`SVG fallback failed, using backup for ${fallbackType}:`, originalSrc);\n        }\n        return;\n      }\n    }\n    /**\n     * Check if URL is an external image (Unsplash, etc.)\n     */\n    isExternalImageUrl(url) {\n      const externalDomains = ['unsplash.com', 'images.unsplash.com', 'picsum.photos', 'via.placeholder.com', 'placehold.it', 'placeholder.com'];\n      return externalDomains.some(domain => url.includes(domain));\n    }\n    /**\n     * Check if URL is a video\n     */\n    isVideoUrl(url) {\n      if (!url) return false;\n      const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi'];\n      const lowerUrl = url.toLowerCase();\n      return videoExtensions.some(ext => lowerUrl.includes(ext)) || lowerUrl.includes('video') || lowerUrl.includes('.mp4');\n    }\n    /**\n     * Get video thumbnail\n     */\n    getVideoThumbnail(videoUrl) {\n      // For videos, use fallback\n      return this.fallbackImages.post;\n    }\n    /**\n     * Get video duration\n     */\n    getVideoDuration(videoUrl) {\n      return 30; // Default 30 seconds\n    }\n    /**\n     * Process media items from database\n     */\n    processMediaItems(mediaArray) {\n      if (!mediaArray || !Array.isArray(mediaArray)) {\n        return [];\n      }\n      return mediaArray.map((media, index) => {\n        const isVideo = this.isVideoUrl(media.url);\n        return {\n          id: media._id || `media_${index}`,\n          type: isVideo ? 'video' : 'image',\n          url: this.getSafeImageUrl(media.url),\n          thumbnailUrl: isVideo ? this.getVideoThumbnail(media.url) : undefined,\n          alt: media.alt || '',\n          duration: isVideo ? this.getVideoDuration(media.url) : undefined,\n          aspectRatio: media.aspectRatio || (isVideo ? 16 / 9 : 1),\n          size: media.size\n        };\n      });\n    }\n    /**\n     * Check if URL is broken\n     */\n    isBrokenUrl(url) {\n      return this.brokenUrlPatterns.some(pattern => url.includes(pattern));\n    }\n    /**\n     * Log media errors for debugging (with smart filtering)\n     */\n    logMediaError(error) {\n      const currentErrors = this.mediaErrors.value;\n      this.mediaErrors.next([...currentErrors, error]);\n      // Only log to console if it's not an external image failure\n      if (!this.isExternalImageUrl(error.id)) {\n        console.warn('Media Error:', error);\n      }\n    }\n    /**\n     * Clear media errors\n     */\n    clearMediaErrors() {\n      this.mediaErrors.next([]);\n    }\n    /**\n     * Preload media for better performance with graceful error handling\n     */\n    preloadMedia(mediaItems) {\n      const promises = mediaItems.map(media => {\n        return new Promise(resolve => {\n          if (media.type === 'image') {\n            const img = new Image();\n            img.onload = () => resolve();\n            img.onerror = () => {\n              // Only log errors for non-external images\n              if (!this.isExternalImageUrl(media.url)) {\n                console.warn(`Failed to preload image: ${media.url}`);\n              }\n              resolve(); // Resolve anyway to not break the promise chain\n            };\n            img.src = media.url;\n          } else if (media.type === 'video') {\n            const video = document.createElement('video');\n            video.onloadeddata = () => resolve();\n            video.onerror = () => {\n              // Only log errors for non-external videos\n              if (!this.isExternalImageUrl(media.url)) {\n                console.warn(`Failed to preload video: ${media.url}`);\n              }\n              resolve(); // Resolve anyway to not break the promise chain\n            };\n            video.src = media.url;\n            video.load();\n          } else {\n            resolve(); // Unknown type, just resolve\n          }\n        });\n      });\n      return Promise.all(promises);\n    }\n    /**\n     * Optimize image URL with size parameters\n     */\n    optimizeImageUrl(url, width, height, quality = 80) {\n      if (!url || this.isExternalImageUrl(url)) {\n        return url; // Don't modify external URLs\n      }\n      // For local images, we can add optimization parameters if the backend supports it\n      const params = new URLSearchParams();\n      if (width) params.append('w', width.toString());\n      if (height) params.append('h', height.toString());\n      if (quality !== 80) params.append('q', quality.toString());\n      const separator = url.includes('?') ? '&' : '?';\n      return params.toString() ? `${url}${separator}${params.toString()}` : url;\n    }\n    /**\n     * Get responsive image URLs for different screen sizes\n     */\n    getResponsiveImageUrls(url) {\n      if (!url) return {};\n      return {\n        thumbnail: this.optimizeImageUrl(url, 150, 150, 70),\n        small: this.optimizeImageUrl(url, 300, 300, 75),\n        medium: this.optimizeImageUrl(url, 600, 600, 80),\n        large: this.optimizeImageUrl(url, 1200, 1200, 85),\n        original: url\n      };\n    }\n    /**\n     * Create a lazy loading image element with proper error handling\n     */\n    createLazyImage(src, alt = '', className = '') {\n      const img = document.createElement('img');\n      img.alt = alt;\n      img.className = className;\n      img.loading = 'lazy';\n      // Set up error handling\n      img.onerror = event => this.handleImageError(event, 'post');\n      // Use intersection observer for lazy loading\n      const observer = new IntersectionObserver(entries => {\n        entries.forEach(entry => {\n          if (entry.isIntersecting) {\n            img.src = this.getSafeImageUrl(src);\n            observer.unobserve(img);\n          }\n        });\n      }, {\n        rootMargin: '50px'\n      });\n      observer.observe(img);\n      return img;\n    }\n    /**\n     * Batch preload images with progress tracking\n     */\n    batchPreloadImages(urls, onProgress) {\n      let loaded = 0;\n      const total = urls.length;\n      const promises = urls.map(url => {\n        return new Promise(resolve => {\n          const img = new Image();\n          img.onload = img.onerror = () => {\n            loaded++;\n            if (onProgress) onProgress(loaded, total);\n            resolve();\n          };\n          img.src = this.getSafeImageUrl(url);\n        });\n      });\n      return Promise.all(promises).then(() => {});\n    }\n    static {\n      this.ɵfac = function MediaService_Factory(t) {\n        return new (t || MediaService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: MediaService,\n        factory: MediaService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return MediaService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}