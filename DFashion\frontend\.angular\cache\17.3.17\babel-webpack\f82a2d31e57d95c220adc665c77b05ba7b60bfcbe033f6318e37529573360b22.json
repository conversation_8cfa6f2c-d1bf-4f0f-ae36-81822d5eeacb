{"ast": null, "code": "import { reduce } from './reduce';\nimport { operate } from '../util/lift';\nconst arrReducer = (arr, value) => (arr.push(value), arr);\nexport function toArray() {\n  return operate((source, subscriber) => {\n    reduce(arrReducer, [])(source).subscribe(subscriber);\n  });\n}\n//# sourceMappingURL=toArray.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}