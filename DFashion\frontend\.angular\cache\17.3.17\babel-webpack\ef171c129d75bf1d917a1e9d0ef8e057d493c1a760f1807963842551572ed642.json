{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { Subject, of } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, switchMap, takeUntil } from 'rxjs/operators';\nimport { VisualSearchComponent } from '../visual-search/visual-search.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../core/services/search.service\";\nimport * as i3 from \"../../../core/services/product.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ionic/angular\";\nconst _c0 = [\"searchInput\"];\nfunction AdvancedSearchComponent_ion_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-button\", 13);\n    i0.ɵɵlistener(\"click\", function AdvancedSearchComponent_ion_button_5_Template_ion_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.startVoiceSearch());\n    });\n    i0.ɵɵelement(1, \"ion-icon\", 14);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdvancedSearchComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"app-visual-search\", 16);\n    i0.ɵɵlistener(\"searchResults\", function AdvancedSearchComponent_div_8_Template_app_visual_search_searchResults_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onVisualSearchResults($event));\n    })(\"searchError\", function AdvancedSearchComponent_div_8_Template_app_visual_search_searchError_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onVisualSearchError($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AdvancedSearchComponent_div_9_div_1_ion_item_5_ion_badge_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-badge\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const suggestion_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", suggestion_r6.popularity, \" \");\n  }\n}\nfunction AdvancedSearchComponent_div_9_div_1_ion_item_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-item\", 23);\n    i0.ɵɵlistener(\"click\", function AdvancedSearchComponent_div_9_div_1_ion_item_5_Template_ion_item_click_0_listener() {\n      const suggestion_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.selectSuggestion(suggestion_r6));\n    });\n    i0.ɵɵelement(1, \"ion-icon\", 24);\n    i0.ɵɵelementStart(2, \"ion-label\");\n    i0.ɵɵelement(3, \"h3\", 25);\n    i0.ɵɵelementStart(4, \"p\", 26);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, AdvancedSearchComponent_div_9_div_1_ion_item_5_ion_badge_6_Template, 2, 1, \"ion-badge\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const suggestion_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r2.getSuggestionIcon(suggestion_r6.type));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.highlightQuery(suggestion_r6.text), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getSuggestionTypeLabel(suggestion_r6.type));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", suggestion_r6.popularity > 0);\n  }\n}\nfunction AdvancedSearchComponent_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20);\n    i0.ɵɵelement(2, \"ion-icon\", 21);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Suggestions\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, AdvancedSearchComponent_div_9_div_1_ion_item_5_Template, 7, 4, \"ion-item\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.suggestions)(\"ngForTrackBy\", ctx_r2.trackSuggestion);\n  }\n}\nfunction AdvancedSearchComponent_div_9_div_2_ion_item_5_ion_chip_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-chip\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const trending_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" +\", trending_r8.growth, \" \");\n  }\n}\nfunction AdvancedSearchComponent_div_9_div_2_ion_item_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-item\", 31);\n    i0.ɵɵlistener(\"click\", function AdvancedSearchComponent_div_9_div_2_ion_item_5_Template_ion_item_click_0_listener() {\n      const trending_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.selectTrendingSearch(trending_r8));\n    });\n    i0.ɵɵelement(1, \"ion-icon\", 32);\n    i0.ɵɵelementStart(2, \"ion-label\")(3, \"h3\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, AdvancedSearchComponent_div_9_div_2_ion_item_5_ion_chip_7_Template, 2, 1, \"ion-chip\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const trending_r8 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(trending_r8.query);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", trending_r8.searches, \" searches\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", trending_r8.growth && trending_r8.growth > 0);\n  }\n}\nfunction AdvancedSearchComponent_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20);\n    i0.ɵɵelement(2, \"ion-icon\", 29);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Trending\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, AdvancedSearchComponent_div_9_div_2_ion_item_5_Template, 8, 3, \"ion-item\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.trendingSearches)(\"ngForTrackBy\", ctx_r2.trackTrending);\n  }\n}\nfunction AdvancedSearchComponent_div_9_div_3_ion_item_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-item\", 38);\n    i0.ɵɵlistener(\"click\", function AdvancedSearchComponent_div_9_div_3_ion_item_7_Template_ion_item_click_0_listener() {\n      const recent_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.selectRecentSearch(recent_r11));\n    });\n    i0.ɵɵelement(1, \"ion-icon\", 39);\n    i0.ɵɵelementStart(2, \"ion-label\")(3, \"h3\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const recent_r11 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(recent_r11.query);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", recent_r11.resultsCount, \" results \\u2022 \", ctx_r2.getRelativeTime(recent_r11.timestamp), \"\");\n  }\n}\nfunction AdvancedSearchComponent_div_9_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20);\n    i0.ɵɵelement(2, \"ion-icon\", 35);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Recent\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"ion-button\", 36);\n    i0.ɵɵlistener(\"click\", function AdvancedSearchComponent_div_9_div_3_Template_ion_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearRecentSearches());\n    });\n    i0.ɵɵtext(6, \" Clear \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, AdvancedSearchComponent_div_9_div_3_ion_item_7_Template, 7, 3, \"ion-item\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.recentSearches)(\"ngForTrackBy\", ctx_r2.trackRecent);\n  }\n}\nfunction AdvancedSearchComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtemplate(1, AdvancedSearchComponent_div_9_div_1_Template, 6, 2, \"div\", 18)(2, AdvancedSearchComponent_div_9_div_2_Template, 6, 2, \"div\", 18)(3, AdvancedSearchComponent_div_9_div_3_Template, 8, 2, \"div\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.suggestions.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.trendingSearches.length > 0 && !ctx_r2.searchQuery);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.recentSearches.length > 0 && !ctx_r2.searchQuery);\n  }\n}\nfunction AdvancedSearchComponent_div_10_ion_select_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-select-option\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", category_r13.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", category_r13.label, \" \");\n  }\n}\nfunction AdvancedSearchComponent_div_10_ion_select_option_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-select-option\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const brand_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", brand_r14);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", brand_r14, \" \");\n  }\n}\nfunction AdvancedSearchComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"form\", 41)(2, \"div\", 42)(3, \"ion-select\", 43);\n    i0.ɵɵlistener(\"ionChange\", function AdvancedSearchComponent_div_10_Template_ion_select_ionChange_3_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onFilterChange());\n    });\n    i0.ɵɵelementStart(4, \"ion-select-option\", 44);\n    i0.ɵɵtext(5, \"All Categories\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, AdvancedSearchComponent_div_10_ion_select_option_6_Template, 2, 2, \"ion-select-option\", 45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 42)(8, \"ion-select\", 46);\n    i0.ɵɵlistener(\"ionChange\", function AdvancedSearchComponent_div_10_Template_ion_select_ionChange_8_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onFilterChange());\n    });\n    i0.ɵɵelementStart(9, \"ion-select-option\", 44);\n    i0.ɵɵtext(10, \"All Brands\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, AdvancedSearchComponent_div_10_ion_select_option_11_Template, 2, 2, \"ion-select-option\", 45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 47)(13, \"ion-label\");\n    i0.ɵɵtext(14, \"Price Range\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 48)(16, \"ion-input\", 49);\n    i0.ɵɵlistener(\"ionBlur\", function AdvancedSearchComponent_div_10_Template_ion_input_ionBlur_16_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onFilterChange());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 50);\n    i0.ɵɵtext(18, \"-\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"ion-input\", 51);\n    i0.ɵɵlistener(\"ionBlur\", function AdvancedSearchComponent_div_10_Template_ion_input_ionBlur_19_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onFilterChange());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 42)(21, \"ion-select\", 52);\n    i0.ɵɵlistener(\"ionChange\", function AdvancedSearchComponent_div_10_Template_ion_select_ionChange_21_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onFilterChange());\n    });\n    i0.ɵɵelementStart(22, \"ion-select-option\", 44);\n    i0.ɵɵtext(23, \"Any Rating\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"ion-select-option\", 53);\n    i0.ɵɵtext(25, \"4+ Stars\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"ion-select-option\", 54);\n    i0.ɵɵtext(27, \"3+ Stars\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"ion-select-option\", 55);\n    i0.ɵɵtext(29, \"2+ Stars\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"div\", 56)(31, \"ion-checkbox\", 57);\n    i0.ɵɵlistener(\"ionChange\", function AdvancedSearchComponent_div_10_Template_ion_checkbox_ionChange_31_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onFilterChange());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"ion-label\");\n    i0.ɵɵtext(33, \"In Stock Only\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 56)(35, \"ion-checkbox\", 58);\n    i0.ɵɵlistener(\"ionChange\", function AdvancedSearchComponent_div_10_Template_ion_checkbox_ionChange_35_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onFilterChange());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"ion-label\");\n    i0.ɵɵtext(37, \"On Sale\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"div\", 42)(39, \"ion-select\", 59);\n    i0.ɵɵlistener(\"ionChange\", function AdvancedSearchComponent_div_10_Template_ion_select_ionChange_39_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onFilterChange());\n    });\n    i0.ɵɵelementStart(40, \"ion-select-option\", 60);\n    i0.ɵɵtext(41, \"Relevance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"ion-select-option\", 61);\n    i0.ɵɵtext(43, \"Price\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"ion-select-option\", 62);\n    i0.ɵɵtext(45, \"Rating\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"ion-select-option\", 63);\n    i0.ɵɵtext(47, \"Popularity\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"ion-select-option\", 64);\n    i0.ɵɵtext(49, \"Newest\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"ion-select-option\", 65);\n    i0.ɵɵtext(51, \"Name\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(52, \"ion-button\", 66);\n    i0.ɵɵlistener(\"click\", function AdvancedSearchComponent_div_10_Template_ion_button_click_52_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clearFilters());\n    });\n    i0.ɵɵelement(53, \"ion-icon\", 67);\n    i0.ɵɵtext(54, \" Clear Filters \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.filtersForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.categories);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.brands);\n  }\n}\nfunction AdvancedSearchComponent_div_11_ion_chip_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-chip\", 71);\n    i0.ɵɵlistener(\"click\", function AdvancedSearchComponent_div_11_ion_chip_1_Template_ion_chip_click_0_listener() {\n      const filter_r16 = i0.ɵɵrestoreView(_r15).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.removeFilter(filter_r16));\n    });\n    i0.ɵɵelementStart(1, \"ion-label\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"ion-icon\", 72);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r16 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", filter_r16.label, \": \", filter_r16.value, \"\");\n  }\n}\nfunction AdvancedSearchComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵtemplate(1, AdvancedSearchComponent_div_11_ion_chip_1_Template, 4, 2, \"ion-chip\", 70);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.activeFilters)(\"ngForTrackBy\", ctx_r2.trackFilter);\n  }\n}\nfunction AdvancedSearchComponent_div_12_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" for \\\"\", ctx_r2.searchQuery, \"\\\"\");\n  }\n}\nfunction AdvancedSearchComponent_div_12_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" (\", ctx_r2.getSearchTime(ctx_r2.searchResults.searchMeta.searchTime), \"ms) \");\n  }\n}\nfunction AdvancedSearchComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"p\")(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" results found \");\n    i0.ɵɵtemplate(5, AdvancedSearchComponent_div_12_span_5_Template, 2, 1, \"span\", 74)(6, AdvancedSearchComponent_div_12_span_6_Template, 2, 1, \"span\", 74);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.searchResults.pagination.total);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchQuery);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchResults.searchMeta.searchTime);\n  }\n}\nexport let AdvancedSearchComponent = /*#__PURE__*/(() => {\n  class AdvancedSearchComponent {\n    constructor(fb, searchService, productService) {\n      this.fb = fb;\n      this.searchService = searchService;\n      this.productService = productService;\n      this.placeholder = 'Search for products, brands, and more...';\n      this.showFilters = true;\n      this.enableVoiceSearch = true;\n      this.autoFocus = false;\n      this.searchPerformed = new EventEmitter();\n      this.suggestionSelected = new EventEmitter();\n      this.filtersChanged = new EventEmitter();\n      this.searchQuery = '';\n      this.showSuggestions = false;\n      // Data\n      this.suggestions = [];\n      this.trendingSearches = [];\n      this.recentSearches = [];\n      this.categories = [];\n      this.brands = [];\n      this.activeFilters = [];\n      this.searchResults = null;\n      this.showVisualSearch = false;\n      // Subjects for cleanup\n      this.destroy$ = new Subject();\n      this.searchSubject = new Subject();\n      this.filtersForm = this.createFiltersForm();\n    }\n    ngOnInit() {\n      this.initializeComponent();\n      this.setupSearchSubscriptions();\n      this.loadInitialData();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    createFiltersForm() {\n      return this.fb.group({\n        category: [''],\n        brand: [''],\n        minPrice: [''],\n        maxPrice: [''],\n        rating: [''],\n        inStock: [false],\n        onSale: [false],\n        sortBy: ['relevance']\n      });\n    }\n    initializeComponent() {\n      // Subscribe to search service state\n      this.searchService.searchQuery$.pipe(takeUntil(this.destroy$)).subscribe(query => {\n        if (query !== this.searchQuery) {\n          this.searchQuery = query;\n        }\n      });\n      this.searchService.searchResults$.pipe(takeUntil(this.destroy$)).subscribe(results => {\n        this.searchResults = results;\n        this.updateActiveFilters();\n      });\n    }\n    setupSearchSubscriptions() {\n      // Setup debounced search for suggestions\n      this.searchSubject.pipe(debounceTime(300), distinctUntilChanged(), switchMap(query => {\n        if (query.length > 1) {\n          return this.searchService.getSearchSuggestions(query, 8);\n        }\n        return of([]);\n      }), takeUntil(this.destroy$)).subscribe(suggestions => {\n        this.suggestions = suggestions;\n      });\n    }\n    loadInitialData() {\n      // Load categories\n      this.productService.getCategories().subscribe(response => {\n        if (response.success) {\n          this.categories = response.data.map(cat => ({\n            value: cat.name || cat,\n            label: cat.displayName || cat.name || cat\n          }));\n        }\n      });\n      // Load brands\n      this.productService.getBrands().subscribe(response => {\n        this.brands = response.brands || [];\n      });\n      // Load trending searches\n      this.searchService.getTrendingSearches(5).subscribe(trending => {\n        this.trendingSearches = trending;\n      });\n      // Load recent searches if user is authenticated\n      this.searchService.getSearchHistory(5).subscribe(history => {\n        this.recentSearches = history.searches;\n      });\n    }\n    // Search input handlers\n    onSearchInput(event) {\n      const query = event.target.value || '';\n      this.searchQuery = query;\n      this.searchSubject.next(query);\n      if (query.length > 0) {\n        this.showSuggestions = true;\n      }\n    }\n    onSearchFocus() {\n      this.showSuggestions = true;\n      if (!this.searchQuery) {\n        // Load trending and recent searches\n        this.loadInitialData();\n      }\n    }\n    onSearchBlur() {\n      // Delay hiding suggestions to allow for clicks\n      setTimeout(() => {\n        this.showSuggestions = false;\n      }, 200);\n    }\n    // Search actions\n    performSearch() {\n      const filters = this.getFiltersFromForm();\n      this.searchService.setSearchQuery(this.searchQuery);\n      this.searchService.setSearchFilters(filters);\n      this.searchPerformed.emit({\n        query: this.searchQuery,\n        filters\n      });\n      this.showSuggestions = false;\n    }\n    selectSuggestion(suggestion) {\n      this.searchQuery = suggestion.text;\n      this.searchService.setSearchQuery(suggestion.text);\n      this.suggestionSelected.emit(suggestion);\n      this.performSearch();\n    }\n    selectTrendingSearch(trending) {\n      this.searchQuery = trending.query;\n      this.performSearch();\n    }\n    selectRecentSearch(recent) {\n      this.searchQuery = recent.query;\n      if (recent.filters) {\n        this.filtersForm.patchValue(recent.filters);\n      }\n      this.performSearch();\n    }\n    // Voice search\n    startVoiceSearch() {\n      if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {\n        const SpeechRecognition = window.webkitSpeechRecognition || window.SpeechRecognition;\n        const recognition = new SpeechRecognition();\n        recognition.continuous = false;\n        recognition.interimResults = false;\n        recognition.lang = 'en-US';\n        recognition.onresult = event => {\n          const transcript = event.results[0][0].transcript;\n          this.searchQuery = transcript;\n          this.performSearch();\n        };\n        recognition.onerror = event => {\n          console.error('Speech recognition error:', event.error);\n        };\n        recognition.start();\n      }\n    }\n    // Visual search handlers\n    toggleVisualSearch() {\n      this.showVisualSearch = !this.showVisualSearch;\n      if (this.showVisualSearch) {\n        this.showSuggestions = false;\n      }\n    }\n    onVisualSearchResults(results) {\n      this.showVisualSearch = false;\n      this.searchResults.emit(results);\n    }\n    onVisualSearchError(error) {\n      console.error('Visual search error:', error);\n      // Could show a toast or alert here\n    }\n    // Filter handlers\n    onFilterChange() {\n      const filters = this.getFiltersFromForm();\n      this.searchService.setSearchFilters(filters);\n      this.filtersChanged.emit(filters);\n      // Track filter change\n      if (this.searchQuery) {\n        this.searchService.trackFilterChange(this.searchQuery).subscribe();\n      }\n    }\n    clearFilters() {\n      this.filtersForm.reset({\n        category: '',\n        brand: '',\n        minPrice: '',\n        maxPrice: '',\n        rating: '',\n        inStock: false,\n        onSale: false,\n        sortBy: 'relevance'\n      });\n      this.onFilterChange();\n    }\n    removeFilter(filter) {\n      this.filtersForm.patchValue({\n        [filter.key]: filter.key === 'inStock' || filter.key === 'onSale' ? false : ''\n      });\n      this.onFilterChange();\n    }\n    clearRecentSearches() {\n      this.searchService.clearSearchHistory('recent').subscribe(success => {\n        if (success) {\n          this.recentSearches = [];\n        }\n      });\n    }\n    // Helper methods\n    getFiltersFromForm() {\n      const formValue = this.filtersForm.value;\n      const filters = {};\n      Object.keys(formValue).forEach(key => {\n        const value = formValue[key];\n        if (value !== '' && value !== null && value !== false) {\n          filters[key] = value;\n        }\n      });\n      return filters;\n    }\n    updateActiveFilters() {\n      const filters = this.getFiltersFromForm();\n      this.activeFilters = [];\n      Object.keys(filters).forEach(key => {\n        const value = filters[key];\n        if (value !== undefined && value !== '' && value !== false) {\n          this.activeFilters.push({\n            key,\n            label: this.getFilterLabel(key),\n            value: this.getFilterDisplayValue(key, value)\n          });\n        }\n      });\n    }\n    getFilterLabel(key) {\n      const labels = {\n        category: 'Category',\n        brand: 'Brand',\n        minPrice: 'Min Price',\n        maxPrice: 'Max Price',\n        rating: 'Rating',\n        inStock: 'In Stock',\n        onSale: 'On Sale',\n        sortBy: 'Sort'\n      };\n      return labels[key] || key;\n    }\n    getFilterDisplayValue(key, value) {\n      if (key === 'rating') {\n        return `${value}+ Stars`;\n      }\n      if (key === 'inStock' || key === 'onSale') {\n        return 'Yes';\n      }\n      return value.toString();\n    }\n    // Template helper methods\n    getSuggestionIcon(type) {\n      const icons = {\n        completion: 'search',\n        product: 'cube',\n        brand: 'business',\n        category: 'grid',\n        trending: 'trending-up',\n        personal: 'person'\n      };\n      return icons[type] || 'search';\n    }\n    getSuggestionTypeLabel(type) {\n      const labels = {\n        completion: 'Search suggestion',\n        product: 'Product',\n        brand: 'Brand',\n        category: 'Category',\n        trending: 'Trending',\n        personal: 'From your history'\n      };\n      return labels[type] || type;\n    }\n    highlightQuery(text) {\n      if (!this.searchQuery) return text;\n      const regex = new RegExp(`(${this.searchQuery})`, 'gi');\n      return text.replace(regex, '<strong>$1</strong>');\n    }\n    getRelativeTime(timestamp) {\n      const now = new Date();\n      const time = new Date(timestamp);\n      const diffMs = now.getTime() - time.getTime();\n      const diffMins = Math.floor(diffMs / 60000);\n      const diffHours = Math.floor(diffMs / 3600000);\n      const diffDays = Math.floor(diffMs / 86400000);\n      if (diffMins < 1) return 'Just now';\n      if (diffMins < 60) return `${diffMins}m ago`;\n      if (diffHours < 24) return `${diffHours}h ago`;\n      return `${diffDays}d ago`;\n    }\n    getSearchTime(timestamp) {\n      return Date.now() - timestamp;\n    }\n    // Track by functions for performance\n    trackSuggestion(index, suggestion) {\n      return suggestion.text + suggestion.type;\n    }\n    trackTrending(index, trending) {\n      return trending.query;\n    }\n    trackRecent(index, recent) {\n      return recent.query + recent.timestamp;\n    }\n    trackFilter(index, filter) {\n      return filter.key + filter.value;\n    }\n    static {\n      this.ɵfac = function AdvancedSearchComponent_Factory(t) {\n        return new (t || AdvancedSearchComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.SearchService), i0.ɵɵdirectiveInject(i3.ProductService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AdvancedSearchComponent,\n        selectors: [[\"app-advanced-search\"]],\n        viewQuery: function AdvancedSearchComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.searchInputRef = _t.first);\n          }\n        },\n        inputs: {\n          placeholder: \"placeholder\",\n          showFilters: \"showFilters\",\n          enableVoiceSearch: \"enableVoiceSearch\",\n          autoFocus: \"autoFocus\"\n        },\n        outputs: {\n          searchPerformed: \"searchPerformed\",\n          suggestionSelected: \"suggestionSelected\",\n          filtersChanged: \"filtersChanged\"\n        },\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 13,\n        vars: 10,\n        consts: [[\"searchInput\", \"\"], [1, \"advanced-search-container\"], [1, \"search-input-container\"], [1, \"search-bar\"], [1, \"custom-searchbar\", 3, \"ngModelChange\", \"ionInput\", \"ionFocus\", \"ionBlur\", \"keydown.enter\", \"ngModel\", \"placeholder\", \"showClearButton\", \"debounce\"], [\"fill\", \"clear\", \"size\", \"small\", \"class\", \"voice-search-btn\", 3, \"click\", 4, \"ngIf\"], [\"fill\", \"clear\", \"size\", \"small\", 1, \"visual-search-btn\", 3, \"click\"], [\"name\", \"camera\", \"slot\", \"icon-only\"], [\"class\", \"visual-search-section\", 4, \"ngIf\"], [\"class\", \"suggestions-dropdown\", 4, \"ngIf\"], [\"class\", \"filters-container\", 4, \"ngIf\"], [\"class\", \"active-filters\", 4, \"ngIf\"], [\"class\", \"search-summary\", 4, \"ngIf\"], [\"fill\", \"clear\", \"size\", \"small\", 1, \"voice-search-btn\", 3, \"click\"], [\"name\", \"mic\", \"slot\", \"icon-only\"], [1, \"visual-search-section\"], [3, \"searchResults\", \"searchError\"], [1, \"suggestions-dropdown\"], [\"class\", \"suggestions-section\", 4, \"ngIf\"], [1, \"suggestions-section\"], [1, \"section-header\"], [\"name\", \"search\"], [\"button\", \"\", \"class\", \"suggestion-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"button\", \"\", 1, \"suggestion-item\", 3, \"click\"], [\"slot\", \"start\", 1, \"suggestion-icon\", 3, \"name\"], [3, \"innerHTML\"], [1, \"suggestion-type\"], [\"color\", \"medium\", \"slot\", \"end\", 4, \"ngIf\"], [\"color\", \"medium\", \"slot\", \"end\"], [\"name\", \"trending-up\"], [\"button\", \"\", \"class\", \"suggestion-item trending-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"button\", \"\", 1, \"suggestion-item\", \"trending-item\", 3, \"click\"], [\"name\", \"flame\", \"slot\", \"start\", \"color\", \"danger\"], [\"color\", \"success\", \"slot\", \"end\", 4, \"ngIf\"], [\"color\", \"success\", \"slot\", \"end\"], [\"name\", \"time\"], [\"fill\", \"clear\", \"size\", \"small\", \"slot\", \"end\", 3, \"click\"], [\"button\", \"\", \"class\", \"suggestion-item recent-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"button\", \"\", 1, \"suggestion-item\", \"recent-item\", 3, \"click\"], [\"name\", \"time-outline\", \"slot\", \"start\"], [1, \"filters-container\"], [1, \"filters-form\", 3, \"formGroup\"], [1, \"filter-group\"], [\"formControlName\", \"category\", \"placeholder\", \"Category\", \"interface\", \"popover\", 3, \"ionChange\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"brand\", \"placeholder\", \"Brand\", \"interface\", \"popover\", 3, \"ionChange\"], [1, \"filter-group\", \"price-range\"], [1, \"price-inputs\"], [\"formControlName\", \"minPrice\", \"type\", \"number\", \"placeholder\", \"Min\", 3, \"ionBlur\"], [1, \"price-separator\"], [\"formControlName\", \"maxPrice\", \"type\", \"number\", \"placeholder\", \"Max\", 3, \"ionBlur\"], [\"formControlName\", \"rating\", \"placeholder\", \"Rating\", \"interface\", \"popover\", 3, \"ionChange\"], [\"value\", \"4\"], [\"value\", \"3\"], [\"value\", \"2\"], [1, \"filter-group\", \"checkbox-filters\"], [\"formControlName\", \"inStock\", 3, \"ionChange\"], [\"formControlName\", \"onSale\", 3, \"ionChange\"], [\"formControlName\", \"sortBy\", \"placeholder\", \"Sort By\", \"interface\", \"popover\", 3, \"ionChange\"], [\"value\", \"relevance\"], [\"value\", \"price\"], [\"value\", \"rating\"], [\"value\", \"popularity\"], [\"value\", \"newest\"], [\"value\", \"name\"], [\"fill\", \"clear\", \"size\", \"small\", 1, \"clear-filters-btn\", 3, \"click\"], [\"name\", \"close-circle\", \"slot\", \"start\"], [3, \"value\"], [1, \"active-filters\"], [\"class\", \"filter-chip\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"filter-chip\", 3, \"click\"], [\"name\", \"close-circle\"], [1, \"search-summary\"], [4, \"ngIf\"]],\n        template: function AdvancedSearchComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            const _r1 = i0.ɵɵgetCurrentView();\n            i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"ion-searchbar\", 4, 0);\n            i0.ɵɵtwoWayListener(\"ngModelChange\", function AdvancedSearchComponent_Template_ion_searchbar_ngModelChange_3_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              i0.ɵɵtwoWayBindingSet(ctx.searchQuery, $event) || (ctx.searchQuery = $event);\n              return i0.ɵɵresetView($event);\n            });\n            i0.ɵɵlistener(\"ionInput\", function AdvancedSearchComponent_Template_ion_searchbar_ionInput_3_listener($event) {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSearchInput($event));\n            })(\"ionFocus\", function AdvancedSearchComponent_Template_ion_searchbar_ionFocus_3_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSearchFocus());\n            })(\"ionBlur\", function AdvancedSearchComponent_Template_ion_searchbar_ionBlur_3_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.onSearchBlur());\n            })(\"keydown.enter\", function AdvancedSearchComponent_Template_ion_searchbar_keydown_enter_3_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.performSearch());\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(5, AdvancedSearchComponent_ion_button_5_Template, 2, 0, \"ion-button\", 5);\n            i0.ɵɵelementStart(6, \"ion-button\", 6);\n            i0.ɵɵlistener(\"click\", function AdvancedSearchComponent_Template_ion_button_click_6_listener() {\n              i0.ɵɵrestoreView(_r1);\n              return i0.ɵɵresetView(ctx.toggleVisualSearch());\n            });\n            i0.ɵɵelement(7, \"ion-icon\", 7);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(8, AdvancedSearchComponent_div_8_Template, 2, 0, \"div\", 8)(9, AdvancedSearchComponent_div_9_Template, 4, 3, \"div\", 9);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(10, AdvancedSearchComponent_div_10_Template, 55, 3, \"div\", 10)(11, AdvancedSearchComponent_div_11_Template, 2, 2, \"div\", 11)(12, AdvancedSearchComponent_div_12_Template, 7, 3, \"div\", 12);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery);\n            i0.ɵɵproperty(\"placeholder\", ctx.placeholder)(\"showClearButton\", \"focus\")(\"debounce\", 0);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.enableVoiceSearch);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.showVisualSearch);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showSuggestions && (ctx.suggestions.length > 0 || ctx.trendingSearches.length > 0));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.showFilters);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.activeFilters.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.searchResults);\n          }\n        },\n        dependencies: [CommonModule, i4.NgForOf, i4.NgIf, FormsModule, i1.ɵNgNoValidate, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, ReactiveFormsModule, i1.FormGroupDirective, i1.FormControlName, IonicModule, i5.IonBadge, i5.IonButton, i5.IonCheckbox, i5.IonChip, i5.IonIcon, i5.IonInput, i5.IonItem, i5.IonLabel, i5.IonSearchbar, i5.IonSelect, i5.IonSelectOption, i5.BooleanValueAccessor, i5.NumericValueAccessor, i5.SelectValueAccessor, i5.TextValueAccessor, VisualSearchComponent],\n        styles: [\".advanced-search-container[_ngcontent-%COMP%]{position:relative;width:100%;max-width:800px;margin:0 auto}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]{position:relative;margin-bottom:1rem}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;background:var(--ion-color-light);border-radius:12px;padding:.25rem;box-shadow:0 2px 8px #0000001a}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .custom-searchbar[_ngcontent-%COMP%]{flex:1;--background: transparent;--box-shadow: none;--border-radius: 8px;--placeholder-color: var(--ion-color-medium);--color: var(--ion-color-dark);--icon-color: var(--ion-color-medium);--clear-button-color: var(--ion-color-medium)}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .custom-searchbar.searchbar-has-focus[_ngcontent-%COMP%]{--background: var(--ion-color-light-tint)}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .voice-search-btn[_ngcontent-%COMP%], .advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .visual-search-btn[_ngcontent-%COMP%]{--color: var(--ion-color-primary);--background: transparent;--border-radius: 8px;margin:0;height:40px;width:40px}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .voice-search-btn[_ngcontent-%COMP%]:hover, .advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .visual-search-btn[_ngcontent-%COMP%]:hover{--background: var(--ion-color-primary-tint);--color: white}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .visual-search-btn[_ngcontent-%COMP%]{--color: var(--ion-color-secondary)}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .visual-search-btn[_ngcontent-%COMP%]:hover{--background: var(--ion-color-secondary-tint);--color: white}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .visual-search-section[_ngcontent-%COMP%]{margin-top:1rem;padding:1rem;background:var(--ion-color-light-tint);border-radius:12px;border:2px dashed var(--ion-color-medium);animation:_ngcontent-%COMP%_slideDown .3s ease-out}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]{position:absolute;top:100%;left:0;right:0;background:#fff;border-radius:12px;box-shadow:0 8px 32px #00000026;z-index:1000;max-height:400px;overflow-y:auto;border:1px solid var(--ion-color-light-shade)}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]   .suggestions-section[_ngcontent-%COMP%]:not(:last-child){border-bottom:1px solid var(--ion-color-light)}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]   .suggestions-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.75rem 1rem .5rem;font-size:.875rem;font-weight:600;color:var(--ion-color-medium);background:var(--ion-color-light-tint)}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]   .suggestions-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:1rem}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]   .suggestions-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-left:auto;--color: var(--ion-color-medium);--background: transparent;font-size:.75rem}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]   .suggestions-section[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]{--background: white;--border-color: transparent;--inner-padding-end: 1rem;--inner-padding-start: 1rem;--padding-start: 0;--padding-end: 0;margin:0;cursor:pointer;transition:all .2s ease}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]   .suggestions-section[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]:hover{--background: var(--ion-color-light-tint)}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]   .suggestions-section[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]:active{--background: var(--ion-color-light-shade)}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]   .suggestions-section[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-icon[_ngcontent-%COMP%]{color:var(--ion-color-medium);margin-right:.5rem}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]   .suggestions-section[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:.95rem;font-weight:500;margin:0;color:var(--ion-color-dark)}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]   .suggestions-section[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]     strong{color:var(--ion-color-primary);font-weight:600}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]   .suggestions-section[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.8rem;color:var(--ion-color-medium);margin:.25rem 0 0}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]   .suggestions-section[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   p.suggestion-type[_ngcontent-%COMP%]{text-transform:capitalize}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]   .suggestions-section[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   ion-badge[_ngcontent-%COMP%]{font-size:.7rem;--background: var(--ion-color-light-shade);--color: var(--ion-color-medium)}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]   .suggestions-section[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   ion-chip[_ngcontent-%COMP%]{font-size:.7rem;height:1.5rem}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]   .suggestions-section[_ngcontent-%COMP%]   .trending-item[_ngcontent-%COMP%]   .suggestion-icon[_ngcontent-%COMP%]{color:var(--ion-color-danger)}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]   .suggestions-section[_ngcontent-%COMP%]   .recent-item[_ngcontent-%COMP%]   .suggestion-icon[_ngcontent-%COMP%]{color:var(--ion-color-tertiary)}.advanced-search-container[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]{background:#fff;border-radius:12px;padding:1rem;box-shadow:0 2px 8px #0000001a;margin-bottom:1rem}.advanced-search-container[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filters-form[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:1rem;align-items:end}.advanced-search-container[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filters-form[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5rem}.advanced-search-container[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filters-form[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:.875rem;font-weight:500;color:var(--ion-color-dark)}.advanced-search-container[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filters-form[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   ion-select[_ngcontent-%COMP%]{--background: var(--ion-color-light);--border-radius: 8px;--padding-start: 1rem;--padding-end: 1rem;--placeholder-color: var(--ion-color-medium)}.advanced-search-container[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filters-form[_ngcontent-%COMP%]   .filter-group.price-range[_ngcontent-%COMP%]   .price-inputs[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.advanced-search-container[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filters-form[_ngcontent-%COMP%]   .filter-group.price-range[_ngcontent-%COMP%]   .price-inputs[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{flex:1;--background: var(--ion-color-light);--border-radius: 8px;--padding-start: 1rem;--padding-end: 1rem;--placeholder-color: var(--ion-color-medium)}.advanced-search-container[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filters-form[_ngcontent-%COMP%]   .filter-group.price-range[_ngcontent-%COMP%]   .price-inputs[_ngcontent-%COMP%]   .price-separator[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-weight:500}.advanced-search-container[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filters-form[_ngcontent-%COMP%]   .filter-group.checkbox-filters[_ngcontent-%COMP%]{flex-direction:row;align-items:center;gap:.75rem}.advanced-search-container[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filters-form[_ngcontent-%COMP%]   .filter-group.checkbox-filters[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{--size: 20px;--checkbox-background-checked: var(--ion-color-primary);--border-color-checked: var(--ion-color-primary)}.advanced-search-container[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filters-form[_ngcontent-%COMP%]   .filter-group.checkbox-filters[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:.9rem;margin:0}.advanced-search-container[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filters-form[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%]{--color: var(--ion-color-medium);--background: transparent;font-size:.875rem;justify-self:end;align-self:center}.advanced-search-container[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filters-form[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%]:hover{--color: var(--ion-color-danger)}.advanced-search-container[_ngcontent-%COMP%]   .active-filters[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:.5rem;margin-bottom:1rem}.advanced-search-container[_ngcontent-%COMP%]   .active-filters[_ngcontent-%COMP%]   .filter-chip[_ngcontent-%COMP%]{--background: var(--ion-color-primary-tint);--color: var(--ion-color-primary);cursor:pointer;transition:all .2s ease}.advanced-search-container[_ngcontent-%COMP%]   .active-filters[_ngcontent-%COMP%]   .filter-chip[_ngcontent-%COMP%]:hover{--background: var(--ion-color-primary-shade);--color: white}.advanced-search-container[_ngcontent-%COMP%]   .active-filters[_ngcontent-%COMP%]   .filter-chip[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-left:.25rem;font-size:1rem}.advanced-search-container[_ngcontent-%COMP%]   .search-summary[_ngcontent-%COMP%]{padding:.75rem 1rem;background:var(--ion-color-light-tint);border-radius:8px;margin-bottom:1rem}.advanced-search-container[_ngcontent-%COMP%]   .search-summary[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:.9rem;color:var(--ion-color-dark)}.advanced-search-container[_ngcontent-%COMP%]   .search-summary[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:var(--ion-color-primary)}@media (max-width: 768px){.advanced-search-container[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filters-form[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:.75rem}.advanced-search-container[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]   .filters-form[_ngcontent-%COMP%]   .clear-filters-btn[_ngcontent-%COMP%]{justify-self:start;margin-top:.5rem}.advanced-search-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]{max-height:300px}.advanced-search-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]   .suggestions-section[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]{--inner-padding-start: .75rem;--inner-padding-end: .75rem}.advanced-search-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]   .suggestions-section[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:.9rem}.advanced-search-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]   .suggestions-section[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.75rem}.advanced-search-container[_ngcontent-%COMP%]   .active-filters[_ngcontent-%COMP%]   .filter-chip[_ngcontent-%COMP%]{font-size:.8rem;height:1.75rem}}@media (prefers-color-scheme: dark){.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{background:var(--ion-color-dark-tint)}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]{background:var(--ion-color-dark);border-color:var(--ion-color-dark-shade)}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]   .suggestions-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{background:var(--ion-color-dark-tint)}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]   .suggestions-section[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]{--background: var(--ion-color-dark)}.advanced-search-container[_ngcontent-%COMP%]   .search-input-container[_ngcontent-%COMP%]   .suggestions-dropdown[_ngcontent-%COMP%]   .suggestions-section[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]:hover{--background: var(--ion-color-dark-tint)}.advanced-search-container[_ngcontent-%COMP%]   .filters-container[_ngcontent-%COMP%]{background:var(--ion-color-dark)}.advanced-search-container[_ngcontent-%COMP%]   .search-summary[_ngcontent-%COMP%]{background:var(--ion-color-dark-tint)}}.suggestions-dropdown[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideDown .2s ease-out}@keyframes _ngcontent-%COMP%_slideDown{0%{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}.search-loading[_ngcontent-%COMP%]   .custom-searchbar[_ngcontent-%COMP%]{position:relative}.search-loading[_ngcontent-%COMP%]   .custom-searchbar[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:50%;right:3rem;width:16px;height:16px;border:2px solid var(--ion-color-light);border-top:2px solid var(--ion-color-primary);border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite;transform:translateY(-50%)}@keyframes _ngcontent-%COMP%_spin{0%{transform:translateY(-50%) rotate(0)}to{transform:translateY(-50%) rotate(360deg)}}\"]\n      });\n    }\n  }\n  return AdvancedSearchComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}