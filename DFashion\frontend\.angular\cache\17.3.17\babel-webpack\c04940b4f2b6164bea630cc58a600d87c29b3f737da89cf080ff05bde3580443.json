{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/auth.service\";\nimport * as i2 from \"../../../../core/services/vendor.service\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/router\";\nfunction VendorDashboardComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32);\n    i0.ɵɵelement(2, \"i\", 33);\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Loading dashboard data...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction VendorDashboardComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 36);\n    i0.ɵɵelement(3, \"i\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 37)(5, \"h3\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Total Products\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 35)(10, \"div\", 36);\n    i0.ɵɵelement(11, \"i\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 37)(13, \"h3\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\");\n    i0.ɵɵtext(16, \"Total Orders\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 35)(18, \"div\", 36);\n    i0.ɵɵelement(19, \"i\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 37)(21, \"h3\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"p\");\n    i0.ɵɵtext(24, \"Total Revenue\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 35)(26, \"div\", 36);\n    i0.ɵɵelement(27, \"i\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 37)(29, \"h3\");\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"p\");\n    i0.ɵɵtext(32, \"Pending Orders\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 35)(34, \"div\", 36);\n    i0.ɵɵelement(35, \"i\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 37)(37, \"h3\");\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"p\");\n    i0.ɵɵtext(40, \"Low Stock Items\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(41, \"div\", 35)(42, \"div\", 36);\n    i0.ɵɵelement(43, \"i\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\", 37)(45, \"h3\");\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"p\");\n    i0.ɵɵtext(48, \"Recent Orders (30d)\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r0.stats.totalProducts);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.stats.totalOrders);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.formatCurrency(ctx_r0.stats.totalRevenue));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.stats.pendingOrders);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.stats.lowStockProducts);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.stats.recentOrdersCount);\n  }\n}\nfunction VendorDashboardComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 43);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 44)(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 45);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const activity_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(activity_r2.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(activity_r2.message);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(8, 4, activity_r2.timestamp, \"short\"));\n  }\n}\nexport let VendorDashboardComponent = /*#__PURE__*/(() => {\n  class VendorDashboardComponent {\n    constructor(authService, vendorService, snackBar) {\n      this.authService = authService;\n      this.vendorService = vendorService;\n      this.snackBar = snackBar;\n      this.currentUser = null;\n      this.stats = {\n        totalProducts: 0,\n        totalOrders: 0,\n        totalRevenue: 0,\n        pendingOrders: 0,\n        lowStockProducts: 0,\n        recentOrdersCount: 0\n      };\n      this.monthlyRevenue = [];\n      this.loading = false;\n      this.recentActivity = [];\n    }\n    ngOnInit() {\n      this.loadUserData();\n      this.loadDashboardData();\n    }\n    loadUserData() {\n      this.authService.currentUser$.subscribe(user => {\n        this.currentUser = user;\n      });\n    }\n    loadDashboardData() {\n      this.loading = true;\n      this.vendorService.getDashboardStats().subscribe({\n        next: response => {\n          if (response.success && response.data) {\n            this.stats = response.data.stats;\n            this.monthlyRevenue = response.data.monthlyRevenue;\n            this.vendorService.updateStats(this.stats);\n            this.generateRecentActivity();\n          } else {\n            this.snackBar.open('Failed to load dashboard data', 'Close', {\n              duration: 3000\n            });\n          }\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Dashboard data loading error:', error);\n          this.snackBar.open('Failed to load dashboard data', 'Close', {\n            duration: 3000\n          });\n          this.loading = false;\n        }\n      });\n    }\n    generateRecentActivity() {\n      this.recentActivity = [];\n      if (this.stats.recentOrdersCount > 0) {\n        this.recentActivity.push({\n          icon: 'fas fa-shopping-cart text-primary',\n          message: `${this.stats.recentOrdersCount} new orders in the last 30 days`,\n          timestamp: new Date()\n        });\n      }\n      if (this.stats.lowStockProducts > 0) {\n        this.recentActivity.push({\n          icon: 'fas fa-exclamation-triangle text-warning',\n          message: `${this.stats.lowStockProducts} products are running low on stock`,\n          timestamp: new Date()\n        });\n      }\n      if (this.stats.pendingOrders > 0) {\n        this.recentActivity.push({\n          icon: 'fas fa-clock text-info',\n          message: `${this.stats.pendingOrders} orders are pending processing`,\n          timestamp: new Date()\n        });\n      }\n      // Add some default activities if none exist\n      if (this.recentActivity.length === 0) {\n        this.recentActivity = [{\n          icon: 'fas fa-chart-line text-success',\n          message: 'Dashboard loaded successfully',\n          timestamp: new Date()\n        }];\n      }\n    }\n    formatCurrency(amount) {\n      return this.vendorService.formatCurrency(amount);\n    }\n    refreshData() {\n      this.loadDashboardData();\n    }\n    static {\n      this.ɵfac = function VendorDashboardComponent_Factory(t) {\n        return new (t || VendorDashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.VendorService), i0.ɵɵdirectiveInject(i3.MatSnackBar));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: VendorDashboardComponent,\n        selectors: [[\"app-vendor-dashboard\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 73,\n        vars: 4,\n        consts: [[1, \"vendor-dashboard\"], [1, \"dashboard-header\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"stats-grid\", 4, \"ngIf\"], [1, \"quick-actions\"], [1, \"actions-grid\"], [\"routerLink\", \"/vendor/products/create\", 1, \"action-card\"], [1, \"action-icon\"], [1, \"fas\", \"fa-plus\"], [1, \"action-content\"], [\"routerLink\", \"/vendor/posts/create\", 1, \"action-card\"], [1, \"fas\", \"fa-camera\"], [\"routerLink\", \"/vendor/stories/create\", 1, \"action-card\"], [1, \"fas\", \"fa-video\"], [\"routerLink\", \"/vendor/orders\", 1, \"action-card\"], [1, \"fas\", \"fa-list\"], [1, \"recent-activity\"], [1, \"activity-list\"], [\"class\", \"activity-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"vendor-menu\"], [1, \"menu-grid\"], [\"routerLink\", \"/vendor/products\", 1, \"menu-item\"], [1, \"fas\", \"fa-box\"], [\"routerLink\", \"/vendor/posts\", 1, \"menu-item\"], [1, \"fas\", \"fa-images\"], [\"routerLink\", \"/vendor/stories\", 1, \"menu-item\"], [1, \"fas\", \"fa-play-circle\"], [\"routerLink\", \"/vendor/orders\", 1, \"menu-item\"], [1, \"fas\", \"fa-shopping-cart\"], [\"routerLink\", \"/vendor/analytics\", 1, \"menu-item\"], [1, \"fas\", \"fa-chart-bar\"], [1, \"loading-container\"], [1, \"loading-spinner\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"stats-grid\"], [1, \"stat-card\"], [1, \"stat-icon\"], [1, \"stat-content\"], [1, \"fas\", \"fa-rupee-sign\"], [1, \"fas\", \"fa-clock\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"fas\", \"fa-calendar\"], [1, \"activity-item\"], [1, \"activity-icon\"], [1, \"activity-content\"], [1, \"activity-time\"]],\n        template: function VendorDashboardComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n            i0.ɵɵtext(3, \"Vendor Dashboard\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"p\");\n            i0.ɵɵtext(5);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(6, VendorDashboardComponent_div_6_Template, 5, 0, \"div\", 2)(7, VendorDashboardComponent_div_7_Template, 49, 6, \"div\", 3);\n            i0.ɵɵelementStart(8, \"div\", 4)(9, \"h2\");\n            i0.ɵɵtext(10, \"Quick Actions\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"div\", 5)(12, \"a\", 6)(13, \"div\", 7);\n            i0.ɵɵelement(14, \"i\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"div\", 9)(16, \"h3\");\n            i0.ɵɵtext(17, \"Add Product\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"p\");\n            i0.ɵɵtext(19, \"Create a new product listing\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(20, \"a\", 10)(21, \"div\", 7);\n            i0.ɵɵelement(22, \"i\", 11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"div\", 9)(24, \"h3\");\n            i0.ɵɵtext(25, \"Create Post\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"p\");\n            i0.ɵɵtext(27, \"Share a new product post\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(28, \"a\", 12)(29, \"div\", 7);\n            i0.ɵɵelement(30, \"i\", 13);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"div\", 9)(32, \"h3\");\n            i0.ɵɵtext(33, \"Add Story\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"p\");\n            i0.ɵɵtext(35, \"Create a product story\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(36, \"a\", 14)(37, \"div\", 7);\n            i0.ɵɵelement(38, \"i\", 15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"div\", 9)(40, \"h3\");\n            i0.ɵɵtext(41, \"View Orders\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"p\");\n            i0.ɵɵtext(43, \"Manage your orders\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(44, \"div\", 16)(45, \"h2\");\n            i0.ɵɵtext(46, \"Recent Activity\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(47, \"div\", 17);\n            i0.ɵɵtemplate(48, VendorDashboardComponent_div_48_Template, 9, 7, \"div\", 18);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(49, \"div\", 19)(50, \"h2\");\n            i0.ɵɵtext(51, \"Vendor Tools\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(52, \"div\", 20)(53, \"a\", 21);\n            i0.ɵɵelement(54, \"i\", 22);\n            i0.ɵɵelementStart(55, \"span\");\n            i0.ɵɵtext(56, \"My Products\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(57, \"a\", 23);\n            i0.ɵɵelement(58, \"i\", 24);\n            i0.ɵɵelementStart(59, \"span\");\n            i0.ɵɵtext(60, \"My Posts\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(61, \"a\", 25);\n            i0.ɵɵelement(62, \"i\", 26);\n            i0.ɵɵelementStart(63, \"span\");\n            i0.ɵɵtext(64, \"My Stories\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(65, \"a\", 27);\n            i0.ɵɵelement(66, \"i\", 28);\n            i0.ɵɵelementStart(67, \"span\");\n            i0.ɵɵtext(68, \"Orders\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(69, \"a\", 29);\n            i0.ɵɵelement(70, \"i\", 30);\n            i0.ɵɵelementStart(71, \"span\");\n            i0.ɵɵtext(72, \"Analytics\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\"Welcome back, \", ctx.currentUser == null ? null : ctx.currentUser.fullName, \"!\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n            i0.ɵɵadvance(41);\n            i0.ɵɵproperty(\"ngForOf\", ctx.recentActivity);\n          }\n        },\n        dependencies: [CommonModule, i4.NgForOf, i4.NgIf, i4.DatePipe, RouterModule, i5.RouterLink],\n        styles: [\".vendor-dashboard[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:20px}.dashboard-header[_ngcontent-%COMP%]{margin-bottom:30px}.dashboard-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem;font-weight:600;margin-bottom:8px}.dashboard-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:1.1rem}.loading-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;padding:3rem;margin-bottom:2rem}.loading-spinner[_ngcontent-%COMP%]{text-align:center;color:#666}.loading-spinner[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:2rem;margin-bottom:1rem;color:#007bff}.stats-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px;margin-bottom:40px}.stat-card[_ngcontent-%COMP%]{background:#fff;border:1px solid #eee;border-radius:8px;padding:24px;display:flex;align-items:center;gap:16px}.stat-icon[_ngcontent-%COMP%]{width:50px;height:50px;background:#007bff;border-radius:8px;display:flex;align-items:center;justify-content:center;color:#fff;font-size:1.2rem}.stat-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.8rem;font-weight:600;margin-bottom:4px}.stat-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:.9rem}.quick-actions[_ngcontent-%COMP%], .recent-activity[_ngcontent-%COMP%], .vendor-menu[_ngcontent-%COMP%]{margin-bottom:40px}.quick-actions[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .recent-activity[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .vendor-menu[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.4rem;font-weight:600;margin-bottom:20px}.actions-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px}.action-card[_ngcontent-%COMP%]{background:#fff;border:1px solid #eee;border-radius:8px;padding:24px;display:flex;align-items:center;gap:16px;text-decoration:none;color:inherit;transition:all .2s}.action-card[_ngcontent-%COMP%]:hover{border-color:#007bff;transform:translateY(-2px);box-shadow:0 4px 12px #007bff26}.action-icon[_ngcontent-%COMP%]{width:50px;height:50px;background:#f8f9fa;border-radius:8px;display:flex;align-items:center;justify-content:center;color:#007bff;font-size:1.2rem}.action-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:600;margin-bottom:4px}.action-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:.9rem}.activity-list[_ngcontent-%COMP%]{background:#fff;border:1px solid #eee;border-radius:8px;overflow:hidden}.activity-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;padding:16px 20px;border-bottom:1px solid #f5f5f5}.activity-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.activity-icon[_ngcontent-%COMP%]{width:40px;height:40px;background:#f8f9fa;border-radius:50%;display:flex;align-items:center;justify-content:center;color:#007bff}.activity-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:4px;font-weight:500}.activity-time[_ngcontent-%COMP%]{color:#666;font-size:.85rem}.menu-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:16px}.menu-item[_ngcontent-%COMP%]{background:#fff;border:1px solid #eee;border-radius:8px;padding:20px;display:flex;flex-direction:column;align-items:center;gap:12px;text-decoration:none;color:inherit;transition:all .2s}.menu-item[_ngcontent-%COMP%]:hover{border-color:#007bff;background:#f8f9ff}.menu-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.5rem;color:#007bff}.menu-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500}@media (max-width: 768px){.stats-grid[_ngcontent-%COMP%], .actions-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.menu-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr)}}\"]\n      });\n    }\n  }\n  return VendorDashboardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}