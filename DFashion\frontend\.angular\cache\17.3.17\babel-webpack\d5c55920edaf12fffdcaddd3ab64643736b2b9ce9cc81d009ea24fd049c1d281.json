{"ast": null, "code": "import { AuthGuard } from '../../core/guards/auth.guard';\nexport const profileRoutes = [{\n  path: '',\n  loadComponent: () => import('./pages/profile/profile.component').then(m => m.ProfileComponent),\n  canActivate: [AuthGuard],\n  title: 'Profile - DFashion'\n}, {\n  path: 'settings',\n  loadComponent: () => import('./pages/settings/settings.component').then(m => m.SettingsComponent),\n  canActivate: [AuthGuard],\n  title: 'Settings - DFashion'\n}];", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "profileRoutes", "path", "loadComponent", "then", "m", "ProfileComponent", "canActivate", "title", "SettingsComponent"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\profile\\profile.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { AuthGuard } from '../../core/guards/auth.guard';\n\nexport const profileRoutes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./pages/profile/profile.component').then(m => m.ProfileComponent),\n    canActivate: [AuthGuard],\n    title: 'Profile - DFashion'\n  },\n  {\n    path: 'settings',\n    loadComponent: () => import('./pages/settings/settings.component').then(m => m.SettingsComponent),\n    canActivate: [AuthGuard],\n    title: 'Settings - DFashion'\n  }\n];\n"], "mappings": "AACA,SAASA,SAAS,QAAQ,8BAA8B;AAExD,OAAO,MAAMC,aAAa,GAAW,CACnC;EACEC,IAAI,EAAE,EAAE;EACRC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,gBAAgB,CAAC;EAC9FC,WAAW,EAAE,CAACP,SAAS,CAAC;EACxBQ,KAAK,EAAE;CACR,EACD;EACEN,IAAI,EAAE,UAAU;EAChBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,iBAAiB,CAAC;EACjGF,WAAW,EAAE,CAACP,SAAS,CAAC;EACxBQ,KAAK,EAAE;CACR,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}