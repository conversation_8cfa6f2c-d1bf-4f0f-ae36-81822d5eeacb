{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterOutlet, NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport { HeaderComponent } from './shared/components/header/header.component';\nimport { NotificationComponent } from './shared/components/notification/notification.component';\nimport { MobileLayoutComponent } from './shared/components/mobile-layout/mobile-layout.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./core/services/auth.service\";\nimport * as i3 from \"./core/services/data-flow.service\";\nimport * as i4 from \"./core/services/mobile-optimization.service\";\nimport * as i5 from \"@angular/common\";\nfunction AppComponent_app_mobile_layout_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"app-mobile-layout\", 2);\n    i0.ɵɵelement(1, \"router-outlet\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"showHeader\", ctx_r0.showHeader)(\"showFooter\", false)(\"showBottomNav\", true);\n  }\n}\nfunction AppComponent_div_1_app_header_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-header\");\n  }\n}\nfunction AppComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵtemplate(1, AppComponent_div_1_app_header_1_Template, 1, 0, \"app-header\", 4);\n    i0.ɵɵelementStart(2, \"main\", 5);\n    i0.ɵɵelement(3, \"router-outlet\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showHeader);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"with-header\", ctx_r0.showHeader);\n  }\n}\nexport class AppComponent {\n  constructor(router, authService, dataFlowService, mobileService) {\n    this.router = router;\n    this.authService = authService;\n    this.dataFlowService = dataFlowService;\n    this.mobileService = mobileService;\n    this.title = 'DFashion';\n    this.showHeader = true;\n    this.isMobile = false;\n    this.appState = null;\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    // Initialize data flow service\n    this.initializeDataFlow();\n    // Subscribe to device info for mobile detection\n    this.subscriptions.push(this.mobileService.getDeviceInfo$().subscribe(deviceInfo => {\n      this.isMobile = deviceInfo.isMobile;\n    }));\n    // Subscribe to app state\n    this.subscriptions.push(this.dataFlowService.getAppState$().subscribe(state => {\n      this.appState = state;\n    }));\n    // Hide header on auth pages and admin login\n    this.subscriptions.push(this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n      const navigationEnd = event;\n      const url = navigationEnd.url;\n      // Hide header on auth pages, admin login, stories, and post details for full-screen experience\n      const shouldHideHeader = url.includes('/auth') || url.includes('/admin/login') || url.includes('/admin/auth') || url.startsWith('/admin/login') || url.startsWith('/stories') || url.startsWith('/post/');\n      this.showHeader = !shouldHideHeader;\n    }));\n    // Set initial header visibility\n    const currentUrl = this.router.url;\n    const shouldHideHeader = currentUrl.includes('/auth') || currentUrl.includes('/admin/login') || currentUrl.includes('/admin/auth') || currentUrl.startsWith('/admin/login') || currentUrl.startsWith('/stories') || currentUrl.startsWith('/post/');\n    this.showHeader = !shouldHideHeader;\n    // Initialize auth state\n    this.authService.initializeAuth();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.dataFlowService.destroy();\n  }\n  initializeDataFlow() {\n    // Load initial data\n    this.dataFlowService.loadAnalytics().subscribe({\n      next: () => console.log('Analytics loaded'),\n      error: error => console.error('Failed to load analytics:', error)\n    });\n    // Load recommendations for anonymous users\n    this.dataFlowService.loadRecommendations().subscribe({\n      next: () => console.log('Recommendations loaded'),\n      error: error => console.error('Failed to load recommendations:', error)\n    });\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.DataFlowService), i0.ɵɵdirectiveInject(i4.MobileOptimizationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[3, \"showHeader\", \"showFooter\", \"showBottomNav\", 4, \"ngIf\"], [\"class\", \"app-container\", 4, \"ngIf\"], [3, \"showHeader\", \"showFooter\", \"showBottomNav\"], [1, \"app-container\"], [4, \"ngIf\"], [1, \"main-content\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AppComponent_app_mobile_layout_0_Template, 2, 3, \"app-mobile-layout\", 0)(1, AppComponent_div_1_Template, 4, 3, \"div\", 1);\n          i0.ɵɵelement(2, \"app-notification\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isMobile);\n        }\n      },\n      dependencies: [CommonModule, i5.NgIf, RouterOutlet, HeaderComponent, NotificationComponent, MobileLayoutComponent],\n      styles: [\".app-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  transition: all 0.3s ease;\\n}\\n\\n.main-content.with-header[_ngcontent-%COMP%] {\\n  margin-top: 60px;\\n}\\n\\n@media (max-width: 768px) {\\n  .main-content.with-header[_ngcontent-%COMP%] {\\n    margin-top: 56px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsaUJBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7QUFDRjs7QUFFQTtFQUNFLE9BQUE7RUFDQSx5QkFBQTtBQUNGOztBQUVBO0VBQ0UsZ0JBQUE7QUFDRjs7QUFFQTtFQUNFO0lBQ0UsZ0JBQUE7RUFDRjtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiLmFwcC1jb250YWluZXIge1xuICBtaW4taGVpZ2h0OiAxMDB2aDtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbn1cblxuLm1haW4tY29udGVudCB7XG4gIGZsZXg6IDE7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG59XG5cbi5tYWluLWNvbnRlbnQud2l0aC1oZWFkZXIge1xuICBtYXJnaW4tdG9wOiA2MHB4O1xufVxuXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgLm1haW4tY29udGVudC53aXRoLWhlYWRlciB7XG4gICAgbWFyZ2luLXRvcDogNTZweDtcbiAgfVxufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterOutlet", "NavigationEnd", "filter", "HeaderComponent", "NotificationComponent", "MobileLayoutComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵproperty", "ctx_r0", "showHeader", "ɵɵtemplate", "AppComponent_div_1_app_header_1_Template", "ɵɵadvance", "ɵɵclassProp", "AppComponent", "constructor", "router", "authService", "dataFlowService", "mobileService", "title", "isMobile", "appState", "subscriptions", "ngOnInit", "initializeDataFlow", "push", "getDeviceInfo$", "subscribe", "deviceInfo", "getAppState$", "state", "events", "pipe", "event", "navigationEnd", "url", "should<PERSON>ideHeader", "includes", "startsWith", "currentUrl", "initializeAuth", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "destroy", "loadAnalytics", "next", "console", "log", "error", "loadRecommendations", "ɵɵdirectiveInject", "i1", "Router", "i2", "AuthService", "i3", "DataFlowService", "i4", "MobileOptimizationService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "AppComponent_app_mobile_layout_0_Template", "AppComponent_div_1_Template", "i5", "NgIf", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\app.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterOutlet, Router, NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport { Subscription } from 'rxjs';\n\nimport { HeaderComponent } from './shared/components/header/header.component';\nimport { NotificationComponent } from './shared/components/notification/notification.component';\nimport { MobileLayoutComponent } from './shared/components/mobile-layout/mobile-layout.component';\nimport { AuthService } from './core/services/auth.service';\nimport { DataFlowService } from './core/services/data-flow.service';\nimport { MobileOptimizationService } from './core/services/mobile-optimization.service';\n\n@Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [CommonModule, RouterOutlet, HeaderComponent, NotificationComponent, MobileLayoutComponent],\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.scss']\n})\nexport class AppComponent implements OnInit, OnDestroy {\n  title = 'DFashion';\n  showHeader = true;\n  isMobile = false;\n  appState: any = null;\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private router: Router,\n    private authService: AuthService,\n    private dataFlowService: DataFlowService,\n    private mobileService: MobileOptimizationService\n  ) {}\n\n  ngOnInit() {\n    // Initialize data flow service\n    this.initializeDataFlow();\n\n    // Subscribe to device info for mobile detection\n    this.subscriptions.push(\n      this.mobileService.getDeviceInfo$().subscribe(deviceInfo => {\n        this.isMobile = deviceInfo.isMobile;\n      })\n    );\n\n    // Subscribe to app state\n    this.subscriptions.push(\n      this.dataFlowService.getAppState$().subscribe(state => {\n        this.appState = state;\n      })\n    );\n\n    // Hide header on auth pages and admin login\n    this.subscriptions.push(\n      this.router.events\n        .pipe(filter(event => event instanceof NavigationEnd))\n        .subscribe((event) => {\n          const navigationEnd = event as NavigationEnd;\n          const url = navigationEnd.url;\n\n          // Hide header on auth pages, admin login, stories, and post details for full-screen experience\n          const shouldHideHeader = url.includes('/auth') ||\n                                  url.includes('/admin/login') ||\n                                  url.includes('/admin/auth') ||\n                                  url.startsWith('/admin/login') ||\n                                  url.startsWith('/stories') ||\n                                  url.startsWith('/post/');\n\n          this.showHeader = !shouldHideHeader;\n        })\n    );\n\n    // Set initial header visibility\n    const currentUrl = this.router.url;\n    const shouldHideHeader = currentUrl.includes('/auth') ||\n                            currentUrl.includes('/admin/login') ||\n                            currentUrl.includes('/admin/auth') ||\n                            currentUrl.startsWith('/admin/login') ||\n                            currentUrl.startsWith('/stories') ||\n                            currentUrl.startsWith('/post/');\n\n    this.showHeader = !shouldHideHeader;\n\n    // Initialize auth state\n    this.authService.initializeAuth();\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.dataFlowService.destroy();\n  }\n\n  private initializeDataFlow() {\n    // Load initial data\n    this.dataFlowService.loadAnalytics().subscribe({\n      next: () => console.log('Analytics loaded'),\n      error: (error) => console.error('Failed to load analytics:', error)\n    });\n\n    // Load recommendations for anonymous users\n    this.dataFlowService.loadRecommendations().subscribe({\n      next: () => console.log('Recommendations loaded'),\n      error: (error) => console.error('Failed to load recommendations:', error)\n    });\n  }\n}\n", "<!-- Mobile Layout for Mobile Devices -->\n<app-mobile-layout\n  *ngIf=\"isMobile\"\n  [showHeader]=\"showHeader\"\n  [showFooter]=\"false\"\n  [showBottomNav]=\"true\">\n  <router-outlet></router-outlet>\n</app-mobile-layout>\n\n<!-- Desktop Layout for Desktop/Tablet -->\n<div *ngIf=\"!isMobile\" class=\"app-container\">\n  <app-header *ngIf=\"showHeader\"></app-header>\n  <main class=\"main-content\" [class.with-header]=\"showHeader\">\n    <router-outlet></router-outlet>\n  </main>\n</div>\n\n<!-- Notifications -->\n<app-notification></app-notification>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,EAAUC,aAAa,QAAQ,iBAAiB;AACrE,SAASC,MAAM,QAAQ,gBAAgB;AAGvC,SAASC,eAAe,QAAQ,6CAA6C;AAC7E,SAASC,qBAAqB,QAAQ,yDAAyD;AAC/F,SAASC,qBAAqB,QAAQ,2DAA2D;;;;;;;;;ICPjGC,EAAA,CAAAC,cAAA,2BAIyB;IACvBD,EAAA,CAAAE,SAAA,oBAA+B;IACjCF,EAAA,CAAAG,YAAA,EAAoB;;;;IAFlBH,EAFA,CAAAI,UAAA,eAAAC,MAAA,CAAAC,UAAA,CAAyB,qBACL,uBACE;;;;;IAMtBN,EAAA,CAAAE,SAAA,iBAA4C;;;;;IAD9CF,EAAA,CAAAC,cAAA,aAA6C;IAC3CD,EAAA,CAAAO,UAAA,IAAAC,wCAAA,wBAA+B;IAC/BR,EAAA,CAAAC,cAAA,cAA4D;IAC1DD,EAAA,CAAAE,SAAA,oBAA+B;IAEnCF,EADE,CAAAG,YAAA,EAAO,EACH;;;;IAJSH,EAAA,CAAAS,SAAA,EAAgB;IAAhBT,EAAA,CAAAI,UAAA,SAAAC,MAAA,CAAAC,UAAA,CAAgB;IACFN,EAAA,CAAAS,SAAA,EAAgC;IAAhCT,EAAA,CAAAU,WAAA,gBAAAL,MAAA,CAAAC,UAAA,CAAgC;;;ADQ7D,OAAM,MAAOK,YAAY;EAQvBC,YACUC,MAAc,EACdC,WAAwB,EACxBC,eAAgC,EAChCC,aAAwC;IAHxC,KAAAH,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IAXvB,KAAAC,KAAK,GAAG,UAAU;IAClB,KAAAX,UAAU,GAAG,IAAI;IACjB,KAAAY,QAAQ,GAAG,KAAK;IAChB,KAAAC,QAAQ,GAAQ,IAAI;IAEZ,KAAAC,aAAa,GAAmB,EAAE;EAOvC;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,kBAAkB,EAAE;IAEzB;IACA,IAAI,CAACF,aAAa,CAACG,IAAI,CACrB,IAAI,CAACP,aAAa,CAACQ,cAAc,EAAE,CAACC,SAAS,CAACC,UAAU,IAAG;MACzD,IAAI,CAACR,QAAQ,GAAGQ,UAAU,CAACR,QAAQ;IACrC,CAAC,CAAC,CACH;IAED;IACA,IAAI,CAACE,aAAa,CAACG,IAAI,CACrB,IAAI,CAACR,eAAe,CAACY,YAAY,EAAE,CAACF,SAAS,CAACG,KAAK,IAAG;MACpD,IAAI,CAACT,QAAQ,GAAGS,KAAK;IACvB,CAAC,CAAC,CACH;IAED;IACA,IAAI,CAACR,aAAa,CAACG,IAAI,CACrB,IAAI,CAACV,MAAM,CAACgB,MAAM,CACfC,IAAI,CAAClC,MAAM,CAACmC,KAAK,IAAIA,KAAK,YAAYpC,aAAa,CAAC,CAAC,CACrD8B,SAAS,CAAEM,KAAK,IAAI;MACnB,MAAMC,aAAa,GAAGD,KAAsB;MAC5C,MAAME,GAAG,GAAGD,aAAa,CAACC,GAAG;MAE7B;MACA,MAAMC,gBAAgB,GAAGD,GAAG,CAACE,QAAQ,CAAC,OAAO,CAAC,IACtBF,GAAG,CAACE,QAAQ,CAAC,cAAc,CAAC,IAC5BF,GAAG,CAACE,QAAQ,CAAC,aAAa,CAAC,IAC3BF,GAAG,CAACG,UAAU,CAAC,cAAc,CAAC,IAC9BH,GAAG,CAACG,UAAU,CAAC,UAAU,CAAC,IAC1BH,GAAG,CAACG,UAAU,CAAC,QAAQ,CAAC;MAEhD,IAAI,CAAC9B,UAAU,GAAG,CAAC4B,gBAAgB;IACrC,CAAC,CAAC,CACL;IAED;IACA,MAAMG,UAAU,GAAG,IAAI,CAACxB,MAAM,CAACoB,GAAG;IAClC,MAAMC,gBAAgB,GAAGG,UAAU,CAACF,QAAQ,CAAC,OAAO,CAAC,IAC7BE,UAAU,CAACF,QAAQ,CAAC,cAAc,CAAC,IACnCE,UAAU,CAACF,QAAQ,CAAC,aAAa,CAAC,IAClCE,UAAU,CAACD,UAAU,CAAC,cAAc,CAAC,IACrCC,UAAU,CAACD,UAAU,CAAC,UAAU,CAAC,IACjCC,UAAU,CAACD,UAAU,CAAC,QAAQ,CAAC;IAEvD,IAAI,CAAC9B,UAAU,GAAG,CAAC4B,gBAAgB;IAEnC;IACA,IAAI,CAACpB,WAAW,CAACwB,cAAc,EAAE;EACnC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACnB,aAAa,CAACoB,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAAC3B,eAAe,CAAC4B,OAAO,EAAE;EAChC;EAEQrB,kBAAkBA,CAAA;IACxB;IACA,IAAI,CAACP,eAAe,CAAC6B,aAAa,EAAE,CAACnB,SAAS,CAAC;MAC7CoB,IAAI,EAAEA,CAAA,KAAMC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC3CC,KAAK,EAAGA,KAAK,IAAKF,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK;KACnE,CAAC;IAEF;IACA,IAAI,CAACjC,eAAe,CAACkC,mBAAmB,EAAE,CAACxB,SAAS,CAAC;MACnDoB,IAAI,EAAEA,CAAA,KAAMC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACjDC,KAAK,EAAGA,KAAK,IAAKF,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEA,KAAK;KACzE,CAAC;EACJ;;;uBArFWrC,YAAY,EAAAX,EAAA,CAAAkD,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAApD,EAAA,CAAAkD,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAtD,EAAA,CAAAkD,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAxD,EAAA,CAAAkD,iBAAA,CAAAO,EAAA,CAAAC,yBAAA;IAAA;EAAA;;;YAAZ/C,YAAY;MAAAgD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA7D,EAAA,CAAA8D,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVzBpE,EATA,CAAAO,UAAA,IAAA+D,yCAAA,+BAIyB,IAAAC,2BAAA,iBAKoB;UAQ7CvE,EAAA,CAAAE,SAAA,uBAAqC;;;UAhBlCF,EAAA,CAAAI,UAAA,SAAAiE,GAAA,CAAAnD,QAAA,CAAc;UAQXlB,EAAA,CAAAS,SAAA,EAAe;UAAfT,EAAA,CAAAI,UAAA,UAAAiE,GAAA,CAAAnD,QAAA,CAAe;;;qBDMTzB,YAAY,EAAA+E,EAAA,CAAAC,IAAA,EAAE/E,YAAY,EAAEG,eAAe,EAAEC,qBAAqB,EAAEC,qBAAqB;MAAA2E,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}