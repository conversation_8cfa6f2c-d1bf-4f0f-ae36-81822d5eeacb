.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;

  &.overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
  }
}

.spinner-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.loading-message {
  margin: 0;
  color: var(--ion-color-medium);
  font-size: 0.875rem;
  text-align: center;
}
