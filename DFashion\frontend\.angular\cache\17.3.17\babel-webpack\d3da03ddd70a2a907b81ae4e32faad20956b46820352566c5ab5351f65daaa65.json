{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport { IonicModule } from '@ionic/angular';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/trending.service\";\nimport * as i2 from \"../../../../core/services/social-interactions.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ionic/angular\";\nimport * as i6 from \"ngx-owl-carousel-o\";\nconst _c0 = () => [1, 2, 3, 4];\nconst _c1 = () => [1, 2, 3];\nconst _c2 = () => [1, 2, 3, 4, 5];\nfunction FeaturedBrandsComponent_div_8_div_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 19);\n  }\n}\nfunction FeaturedBrandsComponent_div_8_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵelement(2, \"div\", 15)(3, \"div\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 17);\n    i0.ɵɵtemplate(5, FeaturedBrandsComponent_div_8_div_2_div_5_Template, 1, 0, \"div\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c1));\n  }\n}\nfunction FeaturedBrandsComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11);\n    i0.ɵɵtemplate(2, FeaturedBrandsComponent_div_8_div_2_Template, 6, 2, \"div\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction FeaturedBrandsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵelement(1, \"ion-icon\", 21);\n    i0.ɵɵelementStart(2, \"p\", 22);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_9_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 24);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r6.originalPrice));\n  }\n}\nfunction FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_ion_icon_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ion-icon\", 52);\n  }\n  if (rf & 2) {\n    const star_r7 = ctx.$implicit;\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r7 <= product_r6.rating.average);\n    i0.ɵɵproperty(\"name\", star_r7 <= product_r6.rating.average ? \"star\" : \"star-outline\");\n  }\n}\nfunction FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_Template_div_click_0_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onProductClick(product_r6, $event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 48);\n    i0.ɵɵelement(2, \"img\", 49);\n    i0.ɵɵelementStart(3, \"div\", 50)(4, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_Template_button_click_4_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onLikeProduct(product_r6, $event));\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_Template_button_click_6_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onShareProduct(product_r6, $event));\n    });\n    i0.ɵɵelement(7, \"ion-icon\", 54);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 55)(9, \"h5\", 56);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 57)(12, \"span\", 58);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_span_14_Template, 2, 1, \"span\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 60)(16, \"div\", 61);\n    i0.ɵɵtemplate(17, FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_ion_icon_17_Template, 1, 3, \"ion-icon\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 63);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r6.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r6.images[0].alt || product_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r1.isProductLiked(product_r6._id));\n    i0.ɵɵattribute(\"aria-label\", \"Like \" + product_r6.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isProductLiked(product_r6._id) ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", \"Share \" + product_r6.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(product_r6.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r6.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r6.originalPrice && product_r6.originalPrice > product_r6.price);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(12, _c2));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r6.rating.count, \")\");\n  }\n}\nfunction FeaturedBrandsComponent_div_10_2_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_10_2_ng_template_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const brand_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onBrandClick(brand_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 30)(2, \"div\", 31)(3, \"h3\", 32);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 33)(6, \"div\", 34);\n    i0.ɵɵelement(7, \"ion-icon\", 35);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 34);\n    i0.ɵɵelement(11, \"ion-icon\", 36);\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 34);\n    i0.ɵɵelement(15, \"ion-icon\", 37);\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(18, \"div\", 38);\n    i0.ɵɵelement(19, \"ion-icon\", 39);\n    i0.ɵɵtext(20, \" Featured \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 40)(22, \"h4\", 41);\n    i0.ɵɵtext(23, \"Top Products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 42);\n    i0.ɵɵtemplate(25, FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_Template, 20, 13, \"div\", 43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 44)(27, \"button\", 45)(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"ion-icon\", 46);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const brand_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(brand_r4.brand);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", brand_r4.productCount, \" Products\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", brand_r4.avgRating, \"/5\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(brand_r4.totalViews), \" Views\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", brand_r4.topProducts)(\"ngForTrackBy\", ctx_r1.trackByProductId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"View All \", brand_r4.brand, \" Products\");\n  }\n}\nfunction FeaturedBrandsComponent_div_10_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, FeaturedBrandsComponent_div_10_2_ng_template_0_Template, 31, 7, \"ng-template\", 28);\n  }\n}\nfunction FeaturedBrandsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"owl-carousel-o\", 26);\n    i0.ɵɵtemplate(2, FeaturedBrandsComponent_div_10_2_Template, 1, 0, null, 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r1.carouselOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.featuredBrands)(\"ngForTrackBy\", ctx_r1.trackByBrandName);\n  }\n}\nfunction FeaturedBrandsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵelement(1, \"ion-icon\", 66);\n    i0.ɵɵelementStart(2, \"h3\", 67);\n    i0.ɵɵtext(3, \"No Featured Brands\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 68);\n    i0.ɵɵtext(5, \"Check back later for featured brand collections\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class FeaturedBrandsComponent {\n  constructor(trendingService, socialService, router) {\n    this.trendingService = trendingService;\n    this.socialService = socialService;\n    this.router = router;\n    this.featuredBrands = [];\n    this.isLoading = true;\n    this.error = null;\n    this.likedProducts = new Set();\n    this.subscription = new Subscription();\n    // Carousel Options\n    this.carouselOptions = {\n      loop: true,\n      mouseDrag: true,\n      touchDrag: true,\n      pullDrag: false,\n      dots: false,\n      navSpeed: 700,\n      navText: ['<', '>'],\n      nav: true,\n      autoplay: true,\n      autoplayTimeout: 5000,\n      autoplayHoverPause: true,\n      margin: 2,\n      responsive: {\n        0: {\n          items: 1\n        },\n        600: {\n          items: 1.5\n        },\n        900: {\n          items: 2\n        }\n      }\n    };\n  }\n  ngOnInit() {\n    this.loadFeaturedBrands();\n    this.subscribeFeaturedBrands();\n    this.subscribeLikedProducts();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n  subscribeFeaturedBrands() {\n    this.subscription.add(this.trendingService.featuredBrands$.subscribe(brands => {\n      this.featuredBrands = brands;\n      this.isLoading = false;\n    }));\n  }\n  subscribeLikedProducts() {\n    this.subscription.add(this.socialService.likedProducts$.subscribe(likedProducts => {\n      this.likedProducts = likedProducts;\n    }));\n  }\n  loadFeaturedBrands() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        yield _this.trendingService.loadFeaturedBrands();\n      } catch (error) {\n        console.error('Error loading featured brands:', error);\n        _this.error = 'Failed to load featured brands';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onBrandClick(brand) {\n    this.router.navigate(['/products'], {\n      queryParams: {\n        brand: brand.brand\n      }\n    });\n  }\n  onProductClick(product, event) {\n    event.stopPropagation();\n    this.router.navigate(['/product', product._id]);\n  }\n  onLikeProduct(product, event) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const result = yield _this2.socialService.likeProduct(product._id);\n        if (result.success) {\n          console.log(result.message);\n        } else {\n          console.error('Failed to like product:', result.message);\n        }\n      } catch (error) {\n        console.error('Error liking product:', error);\n      }\n    })();\n  }\n  onShareProduct(product, event) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const productUrl = `${window.location.origin}/product/${product._id}`;\n        yield navigator.clipboard.writeText(productUrl);\n        yield _this3.socialService.shareProduct(product._id, {\n          platform: 'copy_link',\n          message: `Check out this amazing ${product.name} from ${product.brand}!`\n        });\n        console.log('Product link copied to clipboard!');\n      } catch (error) {\n        console.error('Error sharing product:', error);\n      }\n    })();\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  onRetry() {\n    this.loadFeaturedBrands();\n  }\n  trackByBrandName(index, brand) {\n    return brand.brand;\n  }\n  isProductLiked(productId) {\n    return this.likedProducts.has(productId);\n  }\n  trackByProductId(index, product) {\n    return product._id;\n  }\n  static {\n    this.ɵfac = function FeaturedBrandsComponent_Factory(t) {\n      return new (t || FeaturedBrandsComponent)(i0.ɵɵdirectiveInject(i1.TrendingService), i0.ɵɵdirectiveInject(i2.SocialInteractionsService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FeaturedBrandsComponent,\n      selectors: [[\"app-featured-brands\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 12,\n      vars: 4,\n      consts: [[1, \"featured-brands-container\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"diamond\", 1, \"title-icon\"], [1, \"section-subtitle\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"brands-slider-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-brand-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-brand-card\"], [1, \"loading-header\"], [1, \"loading-brand-name\"], [1, \"loading-stats\"], [1, \"loading-products\"], [\"class\", \"loading-product\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-product\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"brands-slider-container\"], [1, \"brands-slider\", 3, \"options\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"carouselSlide\", \"\"], [1, \"brand-card\", 3, \"click\"], [1, \"brand-header\"], [1, \"brand-info\"], [1, \"brand-name\"], [1, \"brand-stats\"], [1, \"stat-item\"], [\"name\", \"bag-outline\"], [\"name\", \"star\"], [\"name\", \"eye-outline\"], [1, \"brand-badge\"], [\"name\", \"diamond\"], [1, \"top-products\"], [1, \"products-title\"], [1, \"products-list\"], [\"class\", \"product-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"view-more-section\"], [1, \"view-more-btn\"], [\"name\", \"chevron-forward\"], [1, \"product-item\", 3, \"click\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"src\", \"alt\"], [1, \"product-actions\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"name\", \"share-outline\"], [1, \"product-details\"], [1, \"product-name\"], [1, \"product-price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"product-rating\"], [1, \"stars\"], [3, \"name\", \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-count\"], [1, \"original-price\"], [1, \"empty-container\"], [\"name\", \"diamond-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"]],\n      template: function FeaturedBrandsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n          i0.ɵɵelement(4, \"ion-icon\", 4);\n          i0.ɵɵtext(5, \" Featured Brands \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \"Top brands with amazing collections\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(8, FeaturedBrandsComponent_div_8_Template, 3, 2, \"div\", 6)(9, FeaturedBrandsComponent_div_9_Template, 7, 1, \"div\", 7)(10, FeaturedBrandsComponent_div_10_Template, 3, 3, \"div\", 8)(11, FeaturedBrandsComponent_div_11_Template, 6, 0, \"div\", 9);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.featuredBrands.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.featuredBrands.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, IonicModule, i5.IonIcon, CarouselModule, i6.CarouselComponent, i6.CarouselSlideDirective],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.featured-brands-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n  color: white;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 12px;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #ffd700;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.8);\\n  margin: 0;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 20px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  padding: 20px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-header[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-header[_ngcontent-%COMP%]   .loading-brand-name[_ngcontent-%COMP%] {\\n  height: 24px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 8px;\\n  margin-bottom: 8px;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-header[_ngcontent-%COMP%]   .loading-stats[_ngcontent-%COMP%] {\\n  height: 16px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 8px;\\n  width: 70%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-products[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-products[_ngcontent-%COMP%]   .loading-product[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 120px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 12px;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0%, 100% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n.error-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #ff6b6b;\\n  margin-bottom: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: rgba(255, 255, 255, 0.8);\\n  margin-bottom: 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 12px 24px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 auto;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n}\\n\\n.brands-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.brands-slider-container[_ngcontent-%COMP%]   .brands-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 100%;\\n  pointer-events: none;\\n}\\n.brands-slider-container[_ngcontent-%COMP%]   .brands-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-prev[_ngcontent-%COMP%], .brands-slider-container[_ngcontent-%COMP%]   .brands-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-next[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 40px;\\n  height: 40px;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  border: none;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 18px;\\n  font-weight: bold;\\n  cursor: pointer;\\n  pointer-events: all;\\n  transition: all 0.3s ease;\\n  z-index: 10;\\n}\\n.brands-slider-container[_ngcontent-%COMP%]   .brands-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-prev[_ngcontent-%COMP%]:hover, .brands-slider-container[_ngcontent-%COMP%]   .brands-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-next[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 0, 0, 0.9);\\n  transform: scale(1.1);\\n}\\n.brands-slider-container[_ngcontent-%COMP%]   .brands-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-prev[_ngcontent-%COMP%] {\\n  left: -20px;\\n}\\n.brands-slider-container[_ngcontent-%COMP%]   .brands-slider[_ngcontent-%COMP%]   .owl-nav[_ngcontent-%COMP%]   .owl-next[_ngcontent-%COMP%] {\\n  right: -20px;\\n}\\n.brands-slider-container[_ngcontent-%COMP%]   .brands-slider[_ngcontent-%COMP%]   .owl-dots[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.brand-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 20px;\\n  padding: 24px;\\n  -webkit-backdrop-filter: blur(15px);\\n          backdrop-filter: blur(15px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n  cursor: pointer;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.brand-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\\n  opacity: 0;\\n  transition: opacity 0.4s ease;\\n  pointer-events: none;\\n}\\n.brand-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-12px) scale(1.02);\\n  background: rgba(255, 255, 255, 0.18);\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3), 0 8px 20px rgba(255, 255, 255, 0.1) inset;\\n  border-color: rgba(255, 255, 255, 0.3);\\n}\\n.brand-card[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n\\n.brand-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  margin-bottom: 24px;\\n  position: relative;\\n  z-index: 1;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding-right: 16px;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-name[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 800;\\n  color: white;\\n  margin: 0 0 16px 0;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n  letter-spacing: -0.5px;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  font-size: 13px;\\n  color: rgba(255, 255, 255, 0.9);\\n  background: rgba(255, 255, 255, 0.1);\\n  padding: 6px 12px;\\n  border-radius: 12px;\\n  -webkit-backdrop-filter: blur(5px);\\n          backdrop-filter: blur(5px);\\n  transition: all 0.3s ease;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.15);\\n  transform: translateX(4px);\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #ffd700;\\n  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);\\n  color: #333;\\n  padding: 10px 16px;\\n  border-radius: 25px;\\n  font-size: 13px;\\n  font-weight: 700;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4), 0 2px 4px rgba(0, 0, 0, 0.2);\\n  transition: all 0.3s ease;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5), 0 4px 8px rgba(0, 0, 0, 0.3);\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  animation: _ngcontent-%COMP%_sparkle 2s ease-in-out infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_sparkle {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n}\\n.top-products[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  position: relative;\\n  z-index: 1;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 20px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);\\n}\\n.top-products[_ngcontent-%COMP%]   .products-title[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDD25\\\";\\n  font-size: 20px;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  overflow-x: auto;\\n  padding: 8px 0 16px 0;\\n  scroll-behavior: smooth;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 6px;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 3px;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: linear-gradient(90deg, #ffd700, #ffed4e);\\n  border-radius: 3px;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: linear-gradient(90deg, #ffed4e, #ffd700);\\n}\\n.top-products[_ngcontent-%COMP%]   .no-products[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n  background: rgba(255, 255, 255, 0.05);\\n  border-radius: 12px;\\n  border: 2px dashed rgba(255, 255, 255, 0.2);\\n}\\n.top-products[_ngcontent-%COMP%]   .no-products[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  color: rgba(255, 255, 255, 0.4);\\n  margin-bottom: 12px;\\n}\\n.top-products[_ngcontent-%COMP%]   .no-products[_ngcontent-%COMP%]   .empty-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.6);\\n  font-weight: 500;\\n}\\n\\n.product-item[_ngcontent-%COMP%] {\\n  flex: 0 0 160px;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  overflow: hidden;\\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n  cursor: pointer;\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.product-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px) scale(1.05);\\n  background: rgba(255, 255, 255, 0.18);\\n  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3), 0 4px 10px rgba(255, 255, 255, 0.1) inset;\\n  border-color: rgba(255, 255, 255, 0.2);\\n}\\n.product-item[_ngcontent-%COMP%]:hover   .product-actions[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n.product-item[_ngcontent-%COMP%]:hover   .product-image[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.product-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 120px;\\n  object-fit: cover;\\n  transition: transform 0.4s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  height: 50%;\\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.3));\\n  pointer-events: none;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 10px;\\n  right: 10px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 6px;\\n  opacity: 0;\\n  transform: translateY(-10px);\\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(15px);\\n          backdrop-filter: blur(15px);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #333;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  transform: scale(1.15);\\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(220, 53, 69, 0.1);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%] {\\n  background: rgba(220, 53, 69, 0.2);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 123, 255, 0.1);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.product-details[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: rgba(255, 255, 255, 0.05);\\n}\\n.product-details[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 10px 0;\\n  line-height: 1.4;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\\n}\\n.product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 10px;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 800;\\n  color: #ffd700;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\\n}\\n.product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: rgba(255, 255, 255, 0.6);\\n  text-decoration: line-through;\\n  background: rgba(255, 255, 255, 0.1);\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.3);\\n  transition: color 0.3s ease;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon.filled[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n  filter: drop-shadow(0 1px 2px rgba(255, 215, 0, 0.5));\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .rating-count[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: rgba(255, 255, 255, 0.7);\\n  background: rgba(255, 255, 255, 0.1);\\n  padding: 2px 6px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n}\\n\\n.view-more-section[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.1) 100%);\\n  border: 2px solid rgba(255, 255, 255, 0.2);\\n  color: white;\\n  padding: 16px 20px;\\n  border-radius: 16px;\\n  font-weight: 700;\\n  font-size: 15px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 12px;\\n  cursor: pointer;\\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  position: relative;\\n  overflow: hidden;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\\n}\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.6s ease;\\n}\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);\\n  border-color: rgba(255, 255, 255, 0.4);\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 2px 8px rgba(255, 255, 255, 0.1) inset;\\n}\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  transform: translateX(4px);\\n}\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(-2px);\\n}\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  transition: transform 0.3s ease;\\n}\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: rgba(255, 255, 255, 0.4);\\n  margin-bottom: 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 8px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n@media (max-width: 1024px) {\\n  .brands-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\\n    gap: 18px;\\n  }\\n  .brand-card[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  .brand-name[_ngcontent-%COMP%] {\\n    font-size: 22px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .featured-brands-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .brands-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 16px;\\n  }\\n  .brand-card[_ngcontent-%COMP%] {\\n    padding: 18px;\\n  }\\n  .brand-card[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-6px) scale(1.01);\\n  }\\n  .brand-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 16px;\\n    margin-bottom: 20px;\\n  }\\n  .brand-header[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%] {\\n    padding-right: 0;\\n  }\\n  .brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%] {\\n    align-self: flex-start;\\n    padding: 8px 14px;\\n    font-size: 12px;\\n  }\\n  .brand-header[_ngcontent-%COMP%]   .brand-name[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n    margin-bottom: 12px;\\n  }\\n  .brand-stats[_ngcontent-%COMP%] {\\n    flex-direction: row !important;\\n    flex-wrap: wrap;\\n    gap: 8px !important;\\n  }\\n  .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n    padding: 4px 8px;\\n  }\\n  .products-title[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .product-item[_ngcontent-%COMP%] {\\n    flex: 0 0 140px;\\n  }\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .view-more-btn[_ngcontent-%COMP%] {\\n    padding: 14px 18px;\\n    font-size: 14px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .brand-card[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .brand-name[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .product-item[_ngcontent-%COMP%] {\\n    flex: 0 0 120px;\\n  }\\n  .product-image[_ngcontent-%COMP%] {\\n    height: 100px;\\n  }\\n  .product-details[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subscription", "CarouselModule", "IonicModule", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵtemplate", "FeaturedBrandsComponent_div_8_div_2_div_5_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c1", "FeaturedBrandsComponent_div_8_div_2_Template", "_c0", "ɵɵtext", "ɵɵlistener", "FeaturedBrandsComponent_div_9_Template_button_click_4_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onRetry", "ɵɵtextInterpolate", "error", "formatPrice", "product_r6", "originalPrice", "ɵɵclassProp", "star_r7", "rating", "average", "FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_Template_div_click_0_listener", "$event", "_r5", "$implicit", "onProductClick", "FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_Template_button_click_4_listener", "onLikeProduct", "FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_Template_button_click_6_listener", "onShareProduct", "FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_span_14_Template", "FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_ion_icon_17_Template", "images", "url", "ɵɵsanitizeUrl", "alt", "name", "isProductLiked", "_id", "price", "_c2", "ɵɵtextInterpolate1", "count", "FeaturedBrandsComponent_div_10_2_ng_template_0_Template_div_click_0_listener", "_r3", "brand_r4", "onBrandClick", "FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_Template", "brand", "productCount", "avgRating", "formatNumber", "totalViews", "topProducts", "trackByProductId", "FeaturedBrandsComponent_div_10_2_ng_template_0_Template", "FeaturedBrandsComponent_div_10_2_Template", "carouselOptions", "featuredB<PERSON>s", "trackByBrandName", "FeaturedBrandsComponent", "constructor", "trendingService", "socialService", "router", "isLoading", "likedProducts", "Set", "subscription", "loop", "mouseDrag", "touchDrag", "pullDrag", "dots", "navSpeed", "navText", "nav", "autoplay", "autoplayTimeout", "autoplayHoverPause", "margin", "responsive", "items", "ngOnInit", "loadFeaturedBrands", "subscribeFeaturedBrands", "subscribeLikedProducts", "ngOnDestroy", "unsubscribe", "add", "featuredBrands$", "subscribe", "brands", "likedProducts$", "_this", "_asyncToGenerator", "console", "navigate", "queryParams", "product", "event", "stopPropagation", "_this2", "result", "likeProduct", "success", "log", "message", "_this3", "productUrl", "window", "location", "origin", "navigator", "clipboard", "writeText", "shareProduct", "platform", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "num", "toFixed", "toString", "index", "productId", "has", "ɵɵdirectiveInject", "i1", "TrendingService", "i2", "SocialInteractionsService", "i3", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "FeaturedBrandsComponent_Template", "rf", "ctx", "FeaturedBrandsComponent_div_8_Template", "FeaturedBrandsComponent_div_9_Template", "FeaturedBrandsComponent_div_10_Template", "FeaturedBrandsComponent_div_11_Template", "length", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "IonIcon", "i6", "CarouselComponent", "CarouselSlideDirective", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\featured-brands\\featured-brands.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\featured-brands\\featured-brands.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport { OwlOptions } from 'ngx-owl-carousel-o';\nimport { TrendingService, FeaturedBrand } from '../../../../core/services/trending.service';\nimport { Product } from '../../../../core/models/product.model';\nimport { SocialInteractionsService } from '../../../../core/services/social-interactions.service';\nimport { IonicModule } from '@ionic/angular';\n\n@Component({\n  selector: 'app-featured-brands',\n  standalone: true,\n  imports: [CommonModule, IonicModule, CarouselModule],\n  templateUrl: './featured-brands.component.html',\n  styleUrls: ['./featured-brands.component.scss']\n})\nexport class FeaturedBrandsComponent implements OnInit, OnDestroy {\n  featuredBrands: FeaturedBrand[] = [];\n  isLoading = true;\n  error: string | null = null;\n  likedProducts = new Set<string>();\n  private subscription: Subscription = new Subscription();\n\n  // Carousel Options\n  carouselOptions: OwlOptions = {\n    loop: true,\n    mouseDrag: true,\n    touchDrag: true,\n    pullDrag: false,\n    dots: false,\n    navSpeed: 700,\n    navText: ['<', '>'],\n    nav: true,\n    autoplay: true,\n    autoplayTimeout: 5000,\n    autoplayHoverPause: true,\n    margin: 2,\n    responsive: {\n      0: {\n        items: 1\n      },\n      600: {\n        items: 1.5\n      },\n      900: {\n        items: 2\n      }\n    }\n  };\n\n  constructor(\n    private trendingService: TrendingService,\n    private socialService: SocialInteractionsService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadFeaturedBrands();\n    this.subscribeFeaturedBrands();\n    this.subscribeLikedProducts();\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n\n  private subscribeFeaturedBrands() {\n    this.subscription.add(\n      this.trendingService.featuredBrands$.subscribe(brands => {\n        this.featuredBrands = brands;\n        this.isLoading = false;\n      })\n    );\n  }\n\n  private subscribeLikedProducts() {\n    this.subscription.add(\n      this.socialService.likedProducts$.subscribe(likedProducts => {\n        this.likedProducts = likedProducts;\n      })\n    );\n  }\n\n  private async loadFeaturedBrands() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      await this.trendingService.loadFeaturedBrands();\n    } catch (error) {\n      console.error('Error loading featured brands:', error);\n      this.error = 'Failed to load featured brands';\n      this.isLoading = false;\n    }\n  }\n\n  onBrandClick(brand: FeaturedBrand) {\n    this.router.navigate(['/products'], { \n      queryParams: { brand: brand.brand } \n    });\n  }\n\n  onProductClick(product: Product, event: Event) {\n    event.stopPropagation();\n    this.router.navigate(['/product', product._id]);\n  }\n\n  async onLikeProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      const result = await this.socialService.likeProduct(product._id);\n      if (result.success) {\n        console.log(result.message);\n      } else {\n        console.error('Failed to like product:', result.message);\n      }\n    } catch (error) {\n      console.error('Error liking product:', error);\n    }\n  }\n\n  async onShareProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      const productUrl = `${window.location.origin}/product/${product._id}`;\n      await navigator.clipboard.writeText(productUrl);\n\n      await this.socialService.shareProduct(product._id, {\n        platform: 'copy_link',\n        message: `Check out this amazing ${product.name} from ${product.brand}!`\n      });\n\n      console.log('Product link copied to clipboard!');\n    } catch (error) {\n      console.error('Error sharing product:', error);\n    }\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  onRetry() {\n    this.loadFeaturedBrands();\n  }\n\n  trackByBrandName(index: number, brand: FeaturedBrand): string {\n    return brand.brand;\n  }\n\n  isProductLiked(productId: string): boolean {\n    return this.likedProducts.has(productId);\n  }\n\n  trackByProductId(index: number, product: Product): string {\n    return product._id;\n  }\n}\n", "<div class=\"featured-brands-container\">\n  <!-- Header -->\n  <div class=\"section-header\">\n    <div class=\"header-content\">\n      <h2 class=\"section-title\">\n        <ion-icon name=\"diamond\" class=\"title-icon\"></ion-icon>\n        Featured Brands\n      </h2>\n      <p class=\"section-subtitle\">Top brands with amazing collections</p>\n    </div>\n  </div>\n\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <div class=\"loading-grid\">\n      <div *ngFor=\"let item of [1,2,3,4]\" class=\"loading-brand-card\">\n        <div class=\"loading-header\">\n          <div class=\"loading-brand-name\"></div>\n          <div class=\"loading-stats\"></div>\n        </div>\n        <div class=\"loading-products\">\n          <div *ngFor=\"let prod of [1,2,3]\" class=\"loading-product\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error && !isLoading\" class=\"error-container\">\n    <ion-icon name=\"alert-circle\" class=\"error-icon\"></ion-icon>\n    <p class=\"error-message\">{{ error }}</p>\n    <button class=\"retry-btn\" (click)=\"onRetry()\">\n      <ion-icon name=\"refresh\"></ion-icon>\n      Try Again\n    </button>\n  </div>\n\n  <!-- Brands Slider -->\n  <div *ngIf=\"!isLoading && !error && featuredBrands.length > 0\" class=\"brands-slider-container\">\n    <owl-carousel-o [options]=\"carouselOptions\" class=\"brands-slider\">\n      <ng-template carouselSlide *ngFor=\"let brand of featuredBrands; trackBy: trackByBrandName\">\n        <div\n          class=\"brand-card\"\n          (click)=\"onBrandClick(brand)\"\n        >\n      <!-- Brand Header -->\n      <div class=\"brand-header\">\n        <div class=\"brand-info\">\n          <h3 class=\"brand-name\">{{ brand.brand }}</h3>\n          <div class=\"brand-stats\">\n            <div class=\"stat-item\">\n              <ion-icon name=\"bag-outline\"></ion-icon>\n              <span>{{ brand.productCount }} Products</span>\n            </div>\n            <div class=\"stat-item\">\n              <ion-icon name=\"star\"></ion-icon>\n              <span>{{ brand.avgRating }}/5</span>\n            </div>\n            <div class=\"stat-item\">\n              <ion-icon name=\"eye-outline\"></ion-icon>\n              <span>{{ formatNumber(brand.totalViews) }} Views</span>\n            </div>\n          </div>\n        </div>\n        <div class=\"brand-badge\">\n          <ion-icon name=\"diamond\"></ion-icon>\n          Featured\n        </div>\n      </div>\n\n      <!-- Top Products -->\n      <div class=\"top-products\">\n        <h4 class=\"products-title\">Top Products</h4>\n        <div class=\"products-list\">\n          <div \n            *ngFor=\"let product of brand.topProducts; trackBy: trackByProductId\" \n            class=\"product-item\"\n            (click)=\"onProductClick(product, $event)\"\n          >\n            <div class=\"product-image-container\">\n              <img \n                [src]=\"product.images[0].url\"\n                [alt]=\"product.images[0].alt || product.name\"\n                class=\"product-image\"\n                loading=\"lazy\"\n              />\n              \n              <!-- Action Buttons -->\n              <div class=\"product-actions\">\n                <button\n                  class=\"action-btn like-btn\"\n                  [class.liked]=\"isProductLiked(product._id)\"\n                  (click)=\"onLikeProduct(product, $event)\"\n                  [attr.aria-label]=\"'Like ' + product.name\"\n                >\n                  <ion-icon [name]=\"isProductLiked(product._id) ? 'heart' : 'heart-outline'\"></ion-icon>\n                </button>\n                <button \n                  class=\"action-btn share-btn\" \n                  (click)=\"onShareProduct(product, $event)\"\n                  [attr.aria-label]=\"'Share ' + product.name\"\n                >\n                  <ion-icon name=\"share-outline\"></ion-icon>\n                </button>\n              </div>\n            </div>\n\n            <div class=\"product-details\">\n              <h5 class=\"product-name\">{{ product.name }}</h5>\n              <div class=\"product-price\">\n                <span class=\"current-price\">{{ formatPrice(product.price) }}</span>\n                <span *ngIf=\"product.originalPrice && product.originalPrice > product.price\" \n                      class=\"original-price\">{{ formatPrice(product.originalPrice) }}</span>\n              </div>\n              <div class=\"product-rating\">\n                <div class=\"stars\">\n                  <ion-icon \n                    *ngFor=\"let star of [1,2,3,4,5]\" \n                    [name]=\"star <= product.rating.average ? 'star' : 'star-outline'\"\n                    [class.filled]=\"star <= product.rating.average\"\n                  ></ion-icon>\n                </div>\n                <span class=\"rating-count\">({{ product.rating.count }})</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- View More Button -->\n      <div class=\"view-more-section\">\n        <button class=\"view-more-btn\">\n          <span>View All {{ brand.brand }} Products</span>\n          <ion-icon name=\"chevron-forward\"></ion-icon>\n        </button>\n      </div>\n        </div>\n      </ng-template>\n    </owl-carousel-o>\n  </div>\n\n  <!-- Empty State -->\n  <div *ngIf=\"!isLoading && !error && featuredBrands.length === 0\" class=\"empty-container\">\n    <ion-icon name=\"diamond-outline\" class=\"empty-icon\"></ion-icon>\n    <h3 class=\"empty-title\">No Featured Brands</h3>\n    <p class=\"empty-message\">Check back later for featured brand collections</p>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AACnC,SAASC,cAAc,QAAQ,oBAAoB;AAKnD,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;;;ICYlCC,EAAA,CAAAC,SAAA,cAAgE;;;;;IALlED,EADF,CAAAE,cAAA,cAA+D,cACjC;IAE1BF,EADA,CAAAC,SAAA,cAAsC,cACL;IACnCD,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,cAAA,cAA8B;IAC5BF,EAAA,CAAAI,UAAA,IAAAC,kDAAA,kBAA0D;IAE9DL,EADE,CAAAG,YAAA,EAAM,EACF;;;IAFoBH,EAAA,CAAAM,SAAA,GAAU;IAAVN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAU;;;;;IAPtCT,EADF,CAAAE,cAAA,cAAiD,cACrB;IACxBF,EAAA,CAAAI,UAAA,IAAAM,4CAAA,kBAA+D;IAUnEV,EADE,CAAAG,YAAA,EAAM,EACF;;;IAVoBH,EAAA,CAAAM,SAAA,GAAY;IAAZN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAG,GAAA,EAAY;;;;;;IAatCX,EAAA,CAAAE,cAAA,cAAyD;IACvDF,EAAA,CAAAC,SAAA,mBAA4D;IAC5DD,EAAA,CAAAE,cAAA,YAAyB;IAAAF,EAAA,CAAAY,MAAA,GAAW;IAAAZ,EAAA,CAAAG,YAAA,EAAI;IACxCH,EAAA,CAAAE,cAAA,iBAA8C;IAApBF,EAAA,CAAAa,UAAA,mBAAAC,+DAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAG,OAAA,EAAS;IAAA,EAAC;IAC3CpB,EAAA,CAAAC,SAAA,mBAAoC;IACpCD,EAAA,CAAAY,MAAA,kBACF;IACFZ,EADE,CAAAG,YAAA,EAAS,EACL;;;;IALqBH,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAqB,iBAAA,CAAAJ,MAAA,CAAAK,KAAA,CAAW;;;;;IAiFxBtB,EAAA,CAAAE,cAAA,eAC6B;IAAAF,EAAA,CAAAY,MAAA,GAAwC;IAAAZ,EAAA,CAAAG,YAAA,EAAO;;;;;IAA/CH,EAAA,CAAAM,SAAA,EAAwC;IAAxCN,EAAA,CAAAqB,iBAAA,CAAAJ,MAAA,CAAAM,WAAA,CAAAC,UAAA,CAAAC,aAAA,EAAwC;;;;;IAInEzB,EAAA,CAAAC,SAAA,mBAIY;;;;;IADVD,EAAA,CAAA0B,WAAA,WAAAC,OAAA,IAAAH,UAAA,CAAAI,MAAA,CAAAC,OAAA,CAA+C;IAD/C7B,EAAA,CAAAO,UAAA,SAAAoB,OAAA,IAAAH,UAAA,CAAAI,MAAA,CAAAC,OAAA,2BAAiE;;;;;;IA5C3E7B,EAAA,CAAAE,cAAA,cAIC;IADCF,EAAA,CAAAa,UAAA,mBAAAiB,oFAAAC,MAAA;MAAA,MAAAP,UAAA,GAAAxB,EAAA,CAAAe,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAiB,cAAA,CAAAV,UAAA,EAAAO,MAAA,CAA+B;IAAA,EAAC;IAEzC/B,EAAA,CAAAE,cAAA,cAAqC;IACnCF,EAAA,CAAAC,SAAA,cAKE;IAIAD,EADF,CAAAE,cAAA,cAA6B,iBAM1B;IAFCF,EAAA,CAAAa,UAAA,mBAAAsB,uFAAAJ,MAAA;MAAA,MAAAP,UAAA,GAAAxB,EAAA,CAAAe,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAmB,aAAA,CAAAZ,UAAA,EAAAO,MAAA,CAA8B;IAAA,EAAC;IAGxC/B,EAAA,CAAAC,SAAA,mBAAsF;IACxFD,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAE,cAAA,iBAIC;IAFCF,EAAA,CAAAa,UAAA,mBAAAwB,uFAAAN,MAAA;MAAA,MAAAP,UAAA,GAAAxB,EAAA,CAAAe,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAqB,cAAA,CAAAd,UAAA,EAAAO,MAAA,CAA+B;IAAA,EAAC;IAGzC/B,EAAA,CAAAC,SAAA,mBAA0C;IAGhDD,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAGJH,EADF,CAAAE,cAAA,cAA6B,aACF;IAAAF,EAAA,CAAAY,MAAA,IAAkB;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IAE9CH,EADF,CAAAE,cAAA,eAA2B,gBACG;IAAAF,EAAA,CAAAY,MAAA,IAAgC;IAAAZ,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAI,UAAA,KAAAmC,sEAAA,mBAC6B;IAC/BvC,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAE,cAAA,eAA4B,eACP;IACjBF,EAAA,CAAAI,UAAA,KAAAoC,0EAAA,uBAIC;IACHxC,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,cAAA,gBAA2B;IAAAF,EAAA,CAAAY,MAAA,IAA4B;IAG7DZ,EAH6D,CAAAG,YAAA,EAAO,EAC1D,EACF,EACF;;;;;IA5CAH,EAAA,CAAAM,SAAA,GAA6B;IAC7BN,EADA,CAAAO,UAAA,QAAAiB,UAAA,CAAAiB,MAAA,IAAAC,GAAA,EAAA1C,EAAA,CAAA2C,aAAA,CAA6B,QAAAnB,UAAA,CAAAiB,MAAA,IAAAG,GAAA,IAAApB,UAAA,CAAAqB,IAAA,CACgB;IAS3C7C,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAA0B,WAAA,UAAAT,MAAA,CAAA6B,cAAA,CAAAtB,UAAA,CAAAuB,GAAA,EAA2C;;IAIjC/C,EAAA,CAAAM,SAAA,EAAgE;IAAhEN,EAAA,CAAAO,UAAA,SAAAU,MAAA,CAAA6B,cAAA,CAAAtB,UAAA,CAAAuB,GAAA,8BAAgE;IAK1E/C,EAAA,CAAAM,SAAA,EAA2C;;IAQtBN,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAqB,iBAAA,CAAAG,UAAA,CAAAqB,IAAA,CAAkB;IAEb7C,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAqB,iBAAA,CAAAJ,MAAA,CAAAM,WAAA,CAAAC,UAAA,CAAAwB,KAAA,EAAgC;IACrDhD,EAAA,CAAAM,SAAA,EAAoE;IAApEN,EAAA,CAAAO,UAAA,SAAAiB,UAAA,CAAAC,aAAA,IAAAD,UAAA,CAAAC,aAAA,GAAAD,UAAA,CAAAwB,KAAA,CAAoE;IAMtDhD,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,KAAAyC,GAAA,EAAc;IAKRjD,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAkD,kBAAA,MAAA1B,UAAA,CAAAI,MAAA,CAAAuB,KAAA,MAA4B;;;;;;IAjF/DnD,EAAA,CAAAE,cAAA,cAGC;IADCF,EAAA,CAAAa,UAAA,mBAAAuC,6EAAA;MAAApD,EAAA,CAAAe,aAAA,CAAAsC,GAAA;MAAA,MAAAC,QAAA,GAAAtD,EAAA,CAAAkB,aAAA,GAAAe,SAAA;MAAA,MAAAhB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAsC,YAAA,CAAAD,QAAA,CAAmB;IAAA,EAAC;IAK7BtD,EAFJ,CAAAE,cAAA,cAA0B,cACA,aACC;IAAAF,EAAA,CAAAY,MAAA,GAAiB;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IAE3CH,EADF,CAAAE,cAAA,cAAyB,cACA;IACrBF,EAAA,CAAAC,SAAA,mBAAwC;IACxCD,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAY,MAAA,GAAiC;IACzCZ,EADyC,CAAAG,YAAA,EAAO,EAC1C;IACNH,EAAA,CAAAE,cAAA,eAAuB;IACrBF,EAAA,CAAAC,SAAA,oBAAiC;IACjCD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAY,MAAA,IAAuB;IAC/BZ,EAD+B,CAAAG,YAAA,EAAO,EAChC;IACNH,EAAA,CAAAE,cAAA,eAAuB;IACrBF,EAAA,CAAAC,SAAA,oBAAwC;IACxCD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAY,MAAA,IAA0C;IAGtDZ,EAHsD,CAAAG,YAAA,EAAO,EACnD,EACF,EACF;IACNH,EAAA,CAAAE,cAAA,eAAyB;IACvBF,EAAA,CAAAC,SAAA,oBAAoC;IACpCD,EAAA,CAAAY,MAAA,kBACF;IACFZ,EADE,CAAAG,YAAA,EAAM,EACF;IAIJH,EADF,CAAAE,cAAA,eAA0B,cACG;IAAAF,EAAA,CAAAY,MAAA,oBAAY;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAE,cAAA,eAA2B;IACzBF,EAAA,CAAAI,UAAA,KAAAoD,8DAAA,oBAIC;IAiDLxD,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAFJ,CAAAE,cAAA,eAA+B,kBACC,YACtB;IAAAF,EAAA,CAAAY,MAAA,IAAmC;IAAAZ,EAAA,CAAAG,YAAA,EAAO;IAChDH,EAAA,CAAAC,SAAA,oBAA4C;IAG9CD,EAFA,CAAAG,YAAA,EAAS,EACL,EACE;;;;;IAxFmBH,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAAqB,iBAAA,CAAAiC,QAAA,CAAAG,KAAA,CAAiB;IAI9BzD,EAAA,CAAAM,SAAA,GAAiC;IAAjCN,EAAA,CAAAkD,kBAAA,KAAAI,QAAA,CAAAI,YAAA,cAAiC;IAIjC1D,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAkD,kBAAA,KAAAI,QAAA,CAAAK,SAAA,OAAuB;IAIvB3D,EAAA,CAAAM,SAAA,GAA0C;IAA1CN,EAAA,CAAAkD,kBAAA,KAAAjC,MAAA,CAAA2C,YAAA,CAAAN,QAAA,CAAAO,UAAA,YAA0C;IAe9B7D,EAAA,CAAAM,SAAA,GAAsB;IAAAN,EAAtB,CAAAO,UAAA,YAAA+C,QAAA,CAAAQ,WAAA,CAAsB,iBAAA7C,MAAA,CAAA8C,gBAAA,CAAyB;IAyD/D/D,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAAkD,kBAAA,cAAAI,QAAA,CAAAG,KAAA,cAAmC;;;;;IA5F7CzD,EAAA,CAAAI,UAAA,IAAA4D,uDAAA,2BAA2F;;;;;IAD7FhE,EADF,CAAAE,cAAA,cAA+F,yBAC3B;IAChEF,EAAA,CAAAI,UAAA,IAAA6D,yCAAA,iBAA2F;IAmG/FjE,EADE,CAAAG,YAAA,EAAiB,EACb;;;;IApGYH,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAO,UAAA,YAAAU,MAAA,CAAAiD,eAAA,CAA2B;IACIlE,EAAA,CAAAM,SAAA,EAAmB;IAAAN,EAAnB,CAAAO,UAAA,YAAAU,MAAA,CAAAkD,cAAA,CAAmB,iBAAAlD,MAAA,CAAAmD,gBAAA,CAAyB;;;;;IAsG7FpE,EAAA,CAAAE,cAAA,cAAyF;IACvFF,EAAA,CAAAC,SAAA,mBAA+D;IAC/DD,EAAA,CAAAE,cAAA,aAAwB;IAAAF,EAAA,CAAAY,MAAA,yBAAkB;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IAC/CH,EAAA,CAAAE,cAAA,YAAyB;IAAAF,EAAA,CAAAY,MAAA,sDAA+C;IAC1EZ,EAD0E,CAAAG,YAAA,EAAI,EACxE;;;ADhIR,OAAM,MAAOkE,uBAAuB;EAkClCC,YACUC,eAAgC,EAChCC,aAAwC,EACxCC,MAAc;IAFd,KAAAF,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IApChB,KAAAN,cAAc,GAAoB,EAAE;IACpC,KAAAO,SAAS,GAAG,IAAI;IAChB,KAAApD,KAAK,GAAkB,IAAI;IAC3B,KAAAqD,aAAa,GAAG,IAAIC,GAAG,EAAU;IACzB,KAAAC,YAAY,GAAiB,IAAIhF,YAAY,EAAE;IAEvD;IACA,KAAAqE,eAAe,GAAe;MAC5BY,IAAI,EAAE,IAAI;MACVC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,KAAK;MACfC,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;MACnBC,GAAG,EAAE,IAAI;MACTC,QAAQ,EAAE,IAAI;MACdC,eAAe,EAAE,IAAI;MACrBC,kBAAkB,EAAE,IAAI;MACxBC,MAAM,EAAE,CAAC;MACTC,UAAU,EAAE;QACV,CAAC,EAAE;UACDC,KAAK,EAAE;SACR;QACD,GAAG,EAAE;UACHA,KAAK,EAAE;SACR;QACD,GAAG,EAAE;UACHA,KAAK,EAAE;;;KAGZ;EAME;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACnB,YAAY,CAACoB,WAAW,EAAE;EACjC;EAEQH,uBAAuBA,CAAA;IAC7B,IAAI,CAACjB,YAAY,CAACqB,GAAG,CACnB,IAAI,CAAC3B,eAAe,CAAC4B,eAAe,CAACC,SAAS,CAACC,MAAM,IAAG;MACtD,IAAI,CAAClC,cAAc,GAAGkC,MAAM;MAC5B,IAAI,CAAC3B,SAAS,GAAG,KAAK;IACxB,CAAC,CAAC,CACH;EACH;EAEQqB,sBAAsBA,CAAA;IAC5B,IAAI,CAAClB,YAAY,CAACqB,GAAG,CACnB,IAAI,CAAC1B,aAAa,CAAC8B,cAAc,CAACF,SAAS,CAACzB,aAAa,IAAG;MAC1D,IAAI,CAACA,aAAa,GAAGA,aAAa;IACpC,CAAC,CAAC,CACH;EACH;EAEckB,kBAAkBA,CAAA;IAAA,IAAAU,KAAA;IAAA,OAAAC,iBAAA;MAC9B,IAAI;QACFD,KAAI,CAAC7B,SAAS,GAAG,IAAI;QACrB6B,KAAI,CAACjF,KAAK,GAAG,IAAI;QACjB,MAAMiF,KAAI,CAAChC,eAAe,CAACsB,kBAAkB,EAAE;OAChD,CAAC,OAAOvE,KAAK,EAAE;QACdmF,OAAO,CAACnF,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtDiF,KAAI,CAACjF,KAAK,GAAG,gCAAgC;QAC7CiF,KAAI,CAAC7B,SAAS,GAAG,KAAK;;IACvB;EACH;EAEAnB,YAAYA,CAACE,KAAoB;IAC/B,IAAI,CAACgB,MAAM,CAACiC,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;MAClCC,WAAW,EAAE;QAAElD,KAAK,EAAEA,KAAK,CAACA;MAAK;KAClC,CAAC;EACJ;EAEAvB,cAAcA,CAAC0E,OAAgB,EAAEC,KAAY;IAC3CA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,CAACrC,MAAM,CAACiC,QAAQ,CAAC,CAAC,UAAU,EAAEE,OAAO,CAAC7D,GAAG,CAAC,CAAC;EACjD;EAEMX,aAAaA,CAACwE,OAAgB,EAAEC,KAAY;IAAA,IAAAE,MAAA;IAAA,OAAAP,iBAAA;MAChDK,KAAK,CAACC,eAAe,EAAE;MACvB,IAAI;QACF,MAAME,MAAM,SAASD,MAAI,CAACvC,aAAa,CAACyC,WAAW,CAACL,OAAO,CAAC7D,GAAG,CAAC;QAChE,IAAIiE,MAAM,CAACE,OAAO,EAAE;UAClBT,OAAO,CAACU,GAAG,CAACH,MAAM,CAACI,OAAO,CAAC;SAC5B,MAAM;UACLX,OAAO,CAACnF,KAAK,CAAC,yBAAyB,EAAE0F,MAAM,CAACI,OAAO,CAAC;;OAE3D,CAAC,OAAO9F,KAAK,EAAE;QACdmF,OAAO,CAACnF,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;IAC9C;EACH;EAEMgB,cAAcA,CAACsE,OAAgB,EAAEC,KAAY;IAAA,IAAAQ,MAAA;IAAA,OAAAb,iBAAA;MACjDK,KAAK,CAACC,eAAe,EAAE;MACvB,IAAI;QACF,MAAMQ,UAAU,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,YAAYb,OAAO,CAAC7D,GAAG,EAAE;QACrE,MAAM2E,SAAS,CAACC,SAAS,CAACC,SAAS,CAACN,UAAU,CAAC;QAE/C,MAAMD,MAAI,CAAC7C,aAAa,CAACqD,YAAY,CAACjB,OAAO,CAAC7D,GAAG,EAAE;UACjD+E,QAAQ,EAAE,WAAW;UACrBV,OAAO,EAAE,0BAA0BR,OAAO,CAAC/D,IAAI,SAAS+D,OAAO,CAACnD,KAAK;SACtE,CAAC;QAEFgD,OAAO,CAACU,GAAG,CAAC,mCAAmC,CAAC;OACjD,CAAC,OAAO7F,KAAK,EAAE;QACdmF,OAAO,CAACnF,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;IAC/C;EACH;EAEAC,WAAWA,CAACyB,KAAa;IACvB,OAAO,IAAI+E,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACpF,KAAK,CAAC;EAClB;EAEAY,YAAYA,CAACyE,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EAEAnH,OAAOA,CAAA;IACL,IAAI,CAACyE,kBAAkB,EAAE;EAC3B;EAEAzB,gBAAgBA,CAACoE,KAAa,EAAE/E,KAAoB;IAClD,OAAOA,KAAK,CAACA,KAAK;EACpB;EAEAX,cAAcA,CAAC2F,SAAiB;IAC9B,OAAO,IAAI,CAAC9D,aAAa,CAAC+D,GAAG,CAACD,SAAS,CAAC;EAC1C;EAEA1E,gBAAgBA,CAACyE,KAAa,EAAE5B,OAAgB;IAC9C,OAAOA,OAAO,CAAC7D,GAAG;EACpB;;;uBAxJWsB,uBAAuB,EAAArE,EAAA,CAAA2I,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAA7I,EAAA,CAAA2I,iBAAA,CAAAG,EAAA,CAAAC,yBAAA,GAAA/I,EAAA,CAAA2I,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAvB5E,uBAAuB;MAAA6E,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAApJ,EAAA,CAAAqJ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCd9B3J,EAJN,CAAAE,cAAA,aAAuC,aAET,aACE,YACA;UACxBF,EAAA,CAAAC,SAAA,kBAAuD;UACvDD,EAAA,CAAAY,MAAA,wBACF;UAAAZ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAE,cAAA,WAA4B;UAAAF,EAAA,CAAAY,MAAA,0CAAmC;UAEnEZ,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAoINH,EAjIA,CAAAI,UAAA,IAAAyJ,sCAAA,iBAAiD,IAAAC,sCAAA,iBAeQ,KAAAC,uCAAA,iBAUsC,KAAAC,uCAAA,iBAwGN;UAK3FhK,EAAA,CAAAG,YAAA,EAAM;;;UAtIEH,EAAA,CAAAM,SAAA,GAAe;UAAfN,EAAA,CAAAO,UAAA,SAAAqJ,GAAA,CAAAlF,SAAA,CAAe;UAef1E,EAAA,CAAAM,SAAA,EAAyB;UAAzBN,EAAA,CAAAO,UAAA,SAAAqJ,GAAA,CAAAtI,KAAA,KAAAsI,GAAA,CAAAlF,SAAA,CAAyB;UAUzB1E,EAAA,CAAAM,SAAA,EAAuD;UAAvDN,EAAA,CAAAO,UAAA,UAAAqJ,GAAA,CAAAlF,SAAA,KAAAkF,GAAA,CAAAtI,KAAA,IAAAsI,GAAA,CAAAzF,cAAA,CAAA8F,MAAA,KAAuD;UAwGvDjK,EAAA,CAAAM,SAAA,EAAyD;UAAzDN,EAAA,CAAAO,UAAA,UAAAqJ,GAAA,CAAAlF,SAAA,KAAAkF,GAAA,CAAAtI,KAAA,IAAAsI,GAAA,CAAAzF,cAAA,CAAA8F,MAAA,OAAyD;;;qBDhIrDrK,YAAY,EAAAsK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAErK,WAAW,EAAAsK,EAAA,CAAAC,OAAA,EAAExK,cAAc,EAAAyK,EAAA,CAAAC,iBAAA,EAAAD,EAAA,CAAAE,sBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}