<mat-sidenav-container class="sidebar-container">
  <mat-sidenav #sidenav mode="side" opened class="sidebar">
    <!-- Logo Section -->
    <div class="sidebar-header">
      <div class="logo">
        <mat-icon>store</mat-icon>
        <span>DFashion Admin</span>
      </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="sidebar-nav">
      <div *ngFor="let item of menuItems" class="nav-item">
        <!-- Main Menu Item -->
        <a 
          *ngIf="!item.children"
          mat-button 
          [routerLink]="item.route"
          routerLinkActive="active"
          class="nav-link">
          <mat-icon>{{ item.icon }}</mat-icon>
          <span>{{ item.label }}</span>
          <span *ngIf="item.badge" class="nav-badge">{{ item.badge }}</span>
        </a>

        <!-- Menu Item with Children -->
        <div *ngIf="item.children" class="nav-group">
          <button 
            mat-button 
            class="nav-link expandable"
            [class.expanded]="isExpanded(item.label)"
            (click)="toggleExpanded(item.label)">
            <mat-icon>{{ item.icon }}</mat-icon>
            <span>{{ item.label }}</span>
            <mat-icon class="expand-icon">
              {{ isExpanded(item.label) ? 'expand_less' : 'expand_more' }}
            </mat-icon>
          </button>

          <div class="sub-menu" [class.expanded]="isExpanded(item.label)">
            <a 
              *ngFor="let child of item.children"
              mat-button 
              [routerLink]="child.route"
              routerLinkActive="active"
              class="nav-link sub-link">
              <mat-icon>{{ child.icon }}</mat-icon>
              <span>{{ child.label }}</span>
              <span *ngIf="child.badge" class="nav-badge">{{ child.badge }}</span>
            </a>
          </div>
        </div>
      </div>
    </nav>

    <!-- User Section -->
    <div class="sidebar-footer">
      <div class="user-info" *ngIf="currentUser">
        <div class="user-avatar">
          <mat-icon>account_circle</mat-icon>
        </div>
        <div class="user-details">
          <div class="user-name">{{ currentUser.fullName }}</div>
          <div class="user-role">{{ currentUser.role | titlecase }}</div>
        </div>
      </div>
      
      <button mat-icon-button [matMenuTriggerFor]="userMenu" class="user-menu-trigger">
        <mat-icon>more_vert</mat-icon>
      </button>

      <mat-menu #userMenu="matMenu">
        <button mat-menu-item routerLink="/admin/profile">
          <mat-icon>person</mat-icon>
          <span>Profile</span>
        </button>
        <button mat-menu-item routerLink="/admin/settings">
          <mat-icon>settings</mat-icon>
          <span>Settings</span>
        </button>
        <mat-divider></mat-divider>
        <button mat-menu-item (click)="logout()">
          <mat-icon>logout</mat-icon>
          <span>Logout</span>
        </button>
      </mat-menu>
    </div>
  </mat-sidenav>

  <mat-sidenav-content class="main-content">
    <ng-content></ng-content>
  </mat-sidenav-content>
</mat-sidenav-container>
