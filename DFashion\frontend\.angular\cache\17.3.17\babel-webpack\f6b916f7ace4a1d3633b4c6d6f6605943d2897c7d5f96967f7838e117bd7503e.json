{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterOutlet, NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport { HeaderComponent } from './shared/components/header/header.component';\nimport { NotificationComponent } from './shared/components/notification/notification.component';\nimport { MobileLayoutComponent } from './shared/components/mobile-layout/mobile-layout.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./core/services/auth.service\";\nimport * as i3 from \"./core/services/data-flow.service\";\nimport * as i4 from \"./core/services/mobile-optimization.service\";\nimport * as i5 from \"@angular/common\";\nfunction AppComponent_app_mobile_layout_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"app-mobile-layout\", 2);\n    i0.ɵɵelement(1, \"router-outlet\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"showHeader\", ctx_r0.showHeader)(\"showFooter\", false)(\"showBottomNav\", true);\n  }\n}\nfunction AppComponent_div_1_app_header_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-header\");\n  }\n}\nfunction AppComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵtemplate(1, AppComponent_div_1_app_header_1_Template, 1, 0, \"app-header\", 4);\n    i0.ɵɵelementStart(2, \"main\", 5);\n    i0.ɵɵelement(3, \"router-outlet\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showHeader);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"with-header\", ctx_r0.showHeader);\n  }\n}\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    constructor(router, authService, dataFlowService, mobileService) {\n      this.router = router;\n      this.authService = authService;\n      this.dataFlowService = dataFlowService;\n      this.mobileService = mobileService;\n      this.title = 'DFashion';\n      this.showHeader = true;\n      this.isMobile = false;\n      this.appState = null;\n      this.subscriptions = [];\n    }\n    ngOnInit() {\n      // Initialize data flow service\n      this.initializeDataFlow();\n      // Subscribe to device info for mobile detection\n      this.subscriptions.push(this.mobileService.getDeviceInfo$().subscribe(deviceInfo => {\n        this.isMobile = deviceInfo.isMobile;\n      }));\n      // Subscribe to app state\n      this.subscriptions.push(this.dataFlowService.getAppState$().subscribe(state => {\n        this.appState = state;\n      }));\n      // Hide header on auth pages and admin login\n      this.subscriptions.push(this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n        const navigationEnd = event;\n        const url = navigationEnd.url;\n        // Hide header on auth pages, admin login, stories, and post details for full-screen experience\n        const shouldHideHeader = url.includes('/auth') || url.includes('/admin/login') || url.includes('/admin/auth') || url.startsWith('/admin/login') || url.startsWith('/stories') || url.startsWith('/post/');\n        this.showHeader = !shouldHideHeader;\n      }));\n      // Set initial header visibility\n      const currentUrl = this.router.url;\n      const shouldHideHeader = currentUrl.includes('/auth') || currentUrl.includes('/admin/login') || currentUrl.includes('/admin/auth') || currentUrl.startsWith('/admin/login') || currentUrl.startsWith('/stories') || currentUrl.startsWith('/post/');\n      this.showHeader = !shouldHideHeader;\n      // Initialize auth state\n      this.authService.initializeAuth();\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n      this.dataFlowService.destroy();\n    }\n    initializeDataFlow() {\n      // Load initial data\n      this.dataFlowService.loadAnalytics().subscribe({\n        next: () => console.log('Analytics loaded'),\n        error: error => console.error('Failed to load analytics:', error)\n      });\n      // Load recommendations for anonymous users\n      this.dataFlowService.loadRecommendations().subscribe({\n        next: () => console.log('Recommendations loaded'),\n        error: error => console.error('Failed to load recommendations:', error)\n      });\n    }\n    static {\n      this.ɵfac = function AppComponent_Factory(t) {\n        return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.DataFlowService), i0.ɵɵdirectiveInject(i4.MobileOptimizationService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppComponent,\n        selectors: [[\"app-root\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 3,\n        vars: 2,\n        consts: [[3, \"showHeader\", \"showFooter\", \"showBottomNav\", 4, \"ngIf\"], [\"class\", \"app-container\", 4, \"ngIf\"], [3, \"showHeader\", \"showFooter\", \"showBottomNav\"], [1, \"app-container\"], [4, \"ngIf\"], [1, \"main-content\"]],\n        template: function AppComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵtemplate(0, AppComponent_app_mobile_layout_0_Template, 2, 3, \"app-mobile-layout\", 0)(1, AppComponent_div_1_Template, 4, 3, \"div\", 1);\n            i0.ɵɵelement(2, \"app-notification\");\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isMobile);\n          }\n        },\n        dependencies: [CommonModule, i5.NgIf, RouterOutlet, HeaderComponent, NotificationComponent, MobileLayoutComponent],\n        styles: [\".app-container[_ngcontent-%COMP%]{min-height:100vh;display:flex;flex-direction:column}.main-content[_ngcontent-%COMP%]{flex:1;transition:all .3s ease}.main-content.with-header[_ngcontent-%COMP%]{margin-top:60px}@media (max-width: 768px){.main-content.with-header[_ngcontent-%COMP%]{margin-top:56px}}\"]\n      });\n    }\n  }\n  return AppComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}