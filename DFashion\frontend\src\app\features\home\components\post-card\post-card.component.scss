.post {
  background: #fff;
  border: 1px solid #dbdbdb;
  border-radius: 8px;
  overflow: hidden;
}

.post-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.user-details h4 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 2px;
}

.user-details span {
  font-size: 12px;
  color: #8e8e8e;
}

.more-options {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  color: #262626;
}

.post-media {
  position: relative;
  width: 100%;
  aspect-ratio: 1;
  overflow: hidden;
  background: #f8f9fa;
}

.post-media.video-container {
  background: #000;
}

.media-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #f8f9fa;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e3e3e3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.post-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  cursor: pointer;
  transition: transform 0.1s ease;
}

.post-image:active {
  transform: scale(0.98);
}

.post-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  cursor: pointer;
  transition: transform 0.1s ease;
}

.post-video:active {
  transform: scale(0.98);
}

/* Video Controls */
.video-controls {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.video-controls.visible {
  opacity: 1;
  pointer-events: auto;
}

.post-media:hover .video-controls {
  opacity: 1;
  pointer-events: auto;
}

.video-info {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.video-duration {
  font-family: monospace;
}

.play-pause-btn {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 20px;
  color: #333;
}

.play-pause-btn:hover {
  background: white;
  transform: scale(1.1);
}

.play-pause-btn.playing {
  background: rgba(0, 0, 0, 0.7);
  color: white;
}

.video-progress {
  position: absolute;
  bottom: 10px;
  left: 10px;
  right: 10px;
  height: 3px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: #007bff;
  border-radius: 2px;
  transition: width 0.1s ease;
}

/* Media Navigation */
.media-navigation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  border: none;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  pointer-events: auto;
  opacity: 0;
}

.post-media:hover .nav-btn {
  opacity: 1;
}

.nav-btn:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: translateY(-50%) scale(1.1);
}

.nav-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.prev-btn {
  left: 10px;
}

.next-btn {
  right: 10px;
}

.media-indicators {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  pointer-events: auto;
}

.indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: white;
  transform: scale(1.2);
}

.indicator.video {
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 12px;
  height: 12px;
}

.indicator.video i {
  font-size: 6px;
  color: #333;
}

.indicator.video.active i {
  color: white;
}

/* Heart Animation */
.heart-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  opacity: 0;
  z-index: 1000;
}

.heart-animation.animate {
  animation: heartPop 1s ease-out;
}

.heart-animation i {
  font-size: 80px;
  color: #e91e63;
  filter: drop-shadow(0 0 10px rgba(233, 30, 99, 0.5));
}

@keyframes heartPop {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  15% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
  30% {
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .post-card {
    margin: 0 -1rem;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }

  .post-header {
    padding: 1rem;
  }

  .user-avatar {
    width: 35px;
    height: 35px;
  }

  .user-details h4 {
    font-size: 0.9rem;
  }

  .user-details span {
    font-size: 0.8rem;
  }

  .post-media {
    aspect-ratio: 4/5; /* More mobile-friendly aspect ratio */
  }

  .nav-btn {
    width: 35px;
    height: 35px;
    font-size: 14px;
  }

  .play-pause-btn {
    width: 50px;
    height: 50px;
    font-size: 18px;
  }

  .post-actions {
    padding: 0.75rem 1rem;
  }

  .action-btn {
    width: 35px;
    height: 35px;
    font-size: 1.2rem;
  }

  .post-content {
    padding: 0 1rem 1rem;
  }

  .likes-count {
    font-size: 0.9rem;
  }

  .post-caption {
    font-size: 0.9rem;
  }

  .comments-section {
    padding: 0 1rem 1rem;
  }

  .comment-input {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .product-showcase {
    padding: 1rem;
  }

  .product-item {
    min-width: 140px;
  }

  .product-image {
    width: 60px;
    height: 60px;
  }

  .product-info h5 {
    font-size: 0.8rem;
  }

  .product-price {
    font-size: 0.8rem;
  }

  .product-actions {
    gap: 0.25rem;
  }

  .product-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.7rem;
  }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  .nav-btn,
  .play-pause-btn,
  .action-btn,
  .product-btn {
    transform: none;
    transition: background-color 0.2s ease;
  }

  .nav-btn:active,
  .play-pause-btn:active,
  .action-btn:active,
  .product-btn:active {
    transform: scale(0.95);
  }

  .post-media:hover .nav-btn {
    opacity: 1;
  }

  .post-media:hover .video-controls {
    opacity: 1;
  }
}

.product-tags {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-tags.show-tags {
  opacity: 1;
}

.product-tag {
  position: absolute;
  cursor: pointer;
}

.tag-dot {
  width: 20px;
  height: 20px;
  background: #fff;
  border-radius: 50%;
  border: 2px solid var(--primary-color);
  position: relative;
  animation: pulse 2s infinite;
}

.tag-dot::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: var(--primary-color);
  border-radius: 50%;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(0, 149, 246, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(0, 149, 246, 0); }
  100% { box-shadow: 0 0 0 0 rgba(0, 149, 246, 0); }
}

.product-info {
  position: absolute;
  top: -120px;
  left: -100px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 12px;
  width: 200px;
  display: none;
  z-index: 10;
}

.product-tag:hover .product-info {
  display: block;
}

.product-info img {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  object-fit: cover;
  float: left;
  margin-right: 12px;
}

.product-details h5 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
}

.product-details p {
  font-size: 14px;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 8px;
}

.buy-now-btn {
  background: var(--primary-color);
  color: #fff;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  width: 100%;
}

.product-quick-actions {
  display: flex;
  gap: 4px;
  margin-top: 8px;
  justify-content: center;
}

.quick-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  transition: all 0.3s ease;
}

.quick-btn.buy-btn {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
}

.quick-btn.cart-btn {
  background: linear-gradient(135deg, #4834d4, #686de0);
  color: white;
}

.quick-btn.wishlist-btn {
  background: linear-gradient(135deg, #ff9ff3, #f368e0);
  color: white;
}

.quick-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.shopping-indicator {
  position: absolute;
  bottom: 12px;
  left: 12px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  backdrop-filter: blur(10px);
  animation: fadeInUp 0.3s ease;
  cursor: pointer;

  i {
    font-size: 14px;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.post-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
}

.action-buttons {
  display: flex;
  gap: 16px;
}

.action-btn,
.save-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  font-size: 20px;
  color: #262626;
  transition: color 0.2s;
}

.action-btn:hover,
.save-btn:hover {
  color: #8e8e8e;
}

.like-btn.liked {
  color: #ed4956;
}

.save-btn.saved {
  color: #262626;
}

.post-stats {
  padding: 0 16px;
  margin-bottom: 8px;
}

.post-stats p {
  font-size: 14px;
  font-weight: 600;
}

.post-caption {
  padding: 0 16px;
  margin-bottom: 8px;
}

.post-caption p {
  font-size: 14px;
  line-height: 1.4;
}

.post-comments {
  padding: 0 16px 16px;
}

.view-comments {
  font-size: 14px;
  color: #8e8e8e;
  cursor: pointer;
  margin-bottom: 8px;
}

.comment {
  margin-bottom: 4px;
}

.comment p {
  font-size: 14px;
}

.add-comment {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.add-comment input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 14px;
  background: transparent;
}

.post-comment-btn {
  background: none;
  border: none;
  color: var(--primary-color);
  font-weight: 600;
  cursor: pointer;
  font-size: 14px;
}

.hashtag {
  color: var(--primary-color);
  cursor: pointer;
}

.ecommerce-actions {
  border-top: 1px solid #efefef;
  padding: 16px;
  background: #fafafa;
}

.products-showcase {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.product-showcase {
  display: flex;
  align-items: center;
  gap: 12px;
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.product-thumb {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  object-fit: cover;
}

.product-info-inline {
  flex: 1;
}

.product-info-inline h5 {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #262626;
}

.product-info-inline .price {
  font-size: 16px;
  font-weight: 700;
  color: #e91e63;
  margin: 0 0 8px 0;
}

.product-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-wishlist {
  background: none;
  border: 1px solid #ddd;
  width: 36px;
  height: 36px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  color: #666;
}

.btn-wishlist:hover {
  border-color: #e91e63;
  color: #e91e63;
}

.btn-wishlist.active {
  background: #e91e63;
  border-color: #e91e63;
  color: white;
}

.btn-cart {
  background: #2196f3;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: background 0.2s;
}

.btn-cart:hover {
  background: #1976d2;
}

.btn-buy-now {
  background: #ff9800;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
}

.btn-buy-now:hover {
  background: #f57c00;
}

/* Middle Navigation Button */
.middle-navigation {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 4;

  .middle-nav-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border: none;
    border-radius: 25px;
    padding: 10px 20px;
    color: white;
    font-weight: 600;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
    }

    &:active {
      transform: translateY(0);
    }

    i {
      font-size: 14px;
    }

    span {
      font-size: 12px;
    }
  }
}

@media (max-width: 768px) {
  .product-actions {
    flex-direction: column;
    gap: 6px;
    align-items: stretch;
  }

  .btn-cart,
  .btn-buy-now {
    justify-content: center;
  }

  .middle-navigation {
    bottom: 15px;

    .middle-nav-btn {
      padding: 8px 16px;
      font-size: 11px;

      i {
        font-size: 12px;
      }

      span {
        font-size: 11px;
      }
    }
  }
}
