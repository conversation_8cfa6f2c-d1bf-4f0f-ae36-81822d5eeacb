{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Returns `true` if the document or host element\n * has a `dir` set to `rtl`. The host value will always\n * take priority over the root document value.\n */\nconst isRTL = hostEl => {\n  if (hostEl) {\n    if (hostEl.dir !== '') {\n      return hostEl.dir.toLowerCase() === 'rtl';\n    }\n  }\n  return (document === null || document === void 0 ? void 0 : document.dir.toLowerCase()) === 'rtl';\n};\nexport { isRTL as i };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}