{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let ProductService = /*#__PURE__*/(() => {\n  class ProductService {\n    constructor(http) {\n      this.http = http;\n      this.API_URL = 'http://localhost:5000/api';\n    }\n    getProducts(filters = {}) {\n      let params = new HttpParams();\n      Object.keys(filters).forEach(key => {\n        const value = filters[key];\n        if (value !== undefined && value !== null && value !== '') {\n          params = params.set(key, value.toString());\n        }\n      });\n      return this.http.get(`${this.API_URL}/products`, {\n        params\n      });\n    }\n    getProduct(id) {\n      return this.http.get(`${this.API_URL}/products/${id}`);\n    }\n    createProduct(productData) {\n      return this.http.post(`${this.API_URL}/products`, productData);\n    }\n    updateProduct(id, productData) {\n      return this.http.put(`${this.API_URL}/products/${id}`, productData);\n    }\n    deleteProduct(id) {\n      return this.http.delete(`${this.API_URL}/products/${id}`);\n    }\n    addReview(productId, reviewData) {\n      return this.http.post(`${this.API_URL}/products/${productId}/review`, reviewData);\n    }\n    getFeaturedProducts() {\n      return this.http.get(`${this.API_URL}/products/featured`);\n    }\n    getTrendingProducts() {\n      return this.http.get(`${this.API_URL}/products/trending`);\n    }\n    getVendorProducts(vendorId, filters = {}) {\n      let params = new HttpParams();\n      Object.keys(filters).forEach(key => {\n        const value = filters[key];\n        if (value !== undefined && value !== null && value !== '') {\n          params = params.set(key, value.toString());\n        }\n      });\n      return this.http.get(`${this.API_URL}/products/vendor/${vendorId}`, {\n        params\n      });\n    }\n    searchProducts(query, filters = {}) {\n      const searchFilters = {\n        ...filters,\n        search: query\n      };\n      return this.getProducts(searchFilters);\n    }\n    // Advanced search with full search engine capabilities\n    advancedSearch(query, filters = {}, options = {}) {\n      let params = new HttpParams();\n      if (query) params = params.set('q', query);\n      // Add filters\n      Object.keys(filters).forEach(key => {\n        const value = filters[key];\n        if (value !== undefined && value !== null && value !== '') {\n          if (Array.isArray(value)) {\n            params = params.set(key, value.join(','));\n          } else {\n            params = params.set(key, value.toString());\n          }\n        }\n      });\n      // Add options\n      Object.keys(options).forEach(key => {\n        const value = options[key];\n        if (value !== undefined && value !== null && value !== '') {\n          params = params.set(key, value.toString());\n        }\n      });\n      return this.http.get(`${this.API_URL}/search`, {\n        params\n      });\n    }\n    getCategories() {\n      return this.http.get(`${this.API_URL}/v1/categories`);\n    }\n    getBrands() {\n      return this.http.get(`${this.API_URL}/products/brands`);\n    }\n    // Featured Brands\n    getFeaturedBrands() {\n      return this.http.get(`${this.API_URL}/brands/featured`);\n    }\n    // New Arrivals\n    getNewArrivals() {\n      return this.http.get(`${this.API_URL}/products/new-arrivals`);\n    }\n    // Get suggested users for sidebar\n    getSuggestedUsers() {\n      return this.http.get(`${this.API_URL}/v1/users/suggested`);\n    }\n    // Get top influencers for sidebar\n    getTopInfluencers() {\n      return this.http.get(`${this.API_URL}/v1/users/influencers`);\n    }\n    // Get product by ID\n    getProductById(id) {\n      return this.http.get(`${this.API_URL}/products/${id}`);\n    }\n    // Product interactions\n    toggleProductLike(productId) {\n      return this.http.post(`${this.API_URL}/products/${productId}/like`, {});\n    }\n    shareProduct(productId) {\n      return this.http.post(`${this.API_URL}/products/${productId}/share`, {});\n    }\n    // Category products\n    getCategoryProducts(categorySlug, filters = {}) {\n      let params = new HttpParams();\n      Object.keys(filters).forEach(key => {\n        const value = filters[key];\n        if (value !== undefined && value !== null && value !== '') {\n          params = params.set(key, value.toString());\n        }\n      });\n      return this.http.get(`${this.API_URL}/products/category/${categorySlug}`, {\n        params\n      });\n    }\n    static {\n      this.ɵfac = function ProductService_Factory(t) {\n        return new (t || ProductService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: ProductService,\n        factory: ProductService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return ProductService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}