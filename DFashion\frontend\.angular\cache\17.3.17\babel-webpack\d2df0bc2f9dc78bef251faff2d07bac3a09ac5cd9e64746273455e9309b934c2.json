{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"../../../../core/services/cart.service\";\nimport * as i4 from \"../../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"ngx-owl-carousel-o\";\nconst _c0 = [\"storiesContainer\"];\nconst _c1 = [\"feedCover\"];\nconst _c2 = [\"storiesSlider\"];\nconst _c3 = () => [1, 2, 3, 4, 5];\nfunction ViewAddStoriesComponent_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"div\", 10)(2, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_1_div_1_Template, 3, 0, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c3));\n  }\n}\nfunction ViewAddStoriesComponent_div_2_ng_container_13_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_2_ng_container_13_ng_template_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const i_r4 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openStories(i_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 15);\n    i0.ɵɵelement(2, \"div\", 27)(3, \"div\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 20);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const story_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + story_r5.user.avatar + \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"viewed\", story_r5.isViewed);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(story_r5.user.username);\n  }\n}\nfunction ViewAddStoriesComponent_div_2_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_2_ng_container_13_ng_template_1_Template, 6, 5, \"ng-template\", 25);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ViewAddStoriesComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13)(2, \"div\", 14);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_2_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openAddStoryModal());\n    });\n    i0.ɵɵelementStart(3, \"div\", 15)(4, \"div\", 16)(5, \"div\", 17);\n    i0.ɵɵelement(6, \"i\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"div\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 20);\n    i0.ɵɵtext(9, \"Add Story\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 21)(11, \"div\", 22)(12, \"owl-carousel-o\", 23);\n    i0.ɵɵlistener(\"initialized\", function ViewAddStoriesComponent_div_2_Template_owl_carousel_o_initialized_12_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onInitialized($event));\n    })(\"changed\", function ViewAddStoriesComponent_div_2_Template_owl_carousel_o_changed_12_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSlideChanged($event));\n    });\n    i0.ɵɵtemplate(13, ViewAddStoriesComponent_div_2_ng_container_13_Template, 2, 0, \"ng-container\", 24);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentUserAvatar() + \")\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"options\", ctx_r1.customOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵelement(1, \"div\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r7 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r7 === ctx_r1.currentIndex)(\"completed\", i_r7 < ctx_r1.currentIndex);\n  }\n}\nfunction ViewAddStoriesComponent_div_3_video_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"video\", 62);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.getCurrentStory().mediaUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 63);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentStory().mediaUrl + \")\");\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getCurrentStory().caption, \" \");\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_div_21_div_1_Template_div_click_0_listener() {\n      const product_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(product_r9._id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 68);\n    i0.ɵɵtext(2, \"\\uD83D\\uDECD\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 69)(4, \"div\", 70);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 71);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r9 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(product_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r9.price));\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_3_div_21_div_1_Template, 8, 2, \"div\", 66);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getStoryProducts());\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_div_22_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.viewProduct(ctx_r1.getStoryProducts()[0]._id));\n    });\n    i0.ɵɵelement(2, \"i\", 74);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Shop Now\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ViewAddStoriesComponent_div_3_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_div_31_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.buyNow());\n    });\n    i0.ɵɵelement(2, \"i\", 77);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Buy Now\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_div_31_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToWishlist());\n    });\n    i0.ɵɵelement(6, \"i\", 51);\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Wishlist\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_div_31_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addToCart());\n    });\n    i0.ɵɵelement(10, \"i\", 18);\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"Add to Cart\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ViewAddStoriesComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 30, 0)(3, \"div\", 31);\n    i0.ɵɵtemplate(4, ViewAddStoriesComponent_div_3_div_4_Template, 2, 4, \"div\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_Template_div_click_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onStoryClick($event));\n    })(\"touchstart\", function ViewAddStoriesComponent_div_3_Template_div_touchstart_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchStart($event));\n    })(\"touchmove\", function ViewAddStoriesComponent_div_3_Template_div_touchmove_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchMove($event));\n    })(\"touchend\", function ViewAddStoriesComponent_div_3_Template_div_touchend_5_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTouchEnd($event));\n    });\n    i0.ɵɵelementStart(6, \"div\", 34)(7, \"div\", 35);\n    i0.ɵɵelement(8, \"div\", 36);\n    i0.ɵɵelementStart(9, \"div\", 37);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 38);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 39);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function ViewAddStoriesComponent_div_3_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeStories());\n    });\n    i0.ɵɵelement(16, \"i\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 42);\n    i0.ɵɵtemplate(18, ViewAddStoriesComponent_div_3_video_18_Template, 1, 1, \"video\", 43)(19, ViewAddStoriesComponent_div_3_div_19_Template, 1, 2, \"div\", 44)(20, ViewAddStoriesComponent_div_3_div_20_Template, 2, 1, \"div\", 45)(21, ViewAddStoriesComponent_div_3_div_21_Template, 2, 1, \"div\", 46)(22, ViewAddStoriesComponent_div_3_div_22_Template, 5, 0, \"div\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 48)(24, \"div\", 49)(25, \"button\", 50);\n    i0.ɵɵelement(26, \"i\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 52);\n    i0.ɵɵelement(28, \"i\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"button\", 54);\n    i0.ɵɵelement(30, \"i\", 55);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(31, ViewAddStoriesComponent_div_3_div_31_Template, 13, 0, \"div\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(32, \"div\", 57)(33, \"div\", 58);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(34, \"div\", 59, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"is-open\", ctx_r1.isOpen);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.stories);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"data-story-id\", ctx_r1.currentIndex);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + ctx_r1.getCurrentStory().user.avatar + \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getCurrentStory().user.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTimeAgo(ctx_r1.getCurrentStory().createdAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(ctx_r1.getCurrentStory().views), \" views\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().mediaType === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().mediaType === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentStory().caption);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasProducts());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasProducts());\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasProducts());\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"is-hidden\", ctx_r1.isOpen);\n  }\n}\nfunction ViewAddStoriesComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"div\", 81);\n    i0.ɵɵelement(2, \"i\", 82);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Tap to go back\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 83)(6, \"span\");\n    i0.ɵɵtext(7, \"Tap to continue\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 84);\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ViewAddStoriesComponent {\n  constructor(router, http, cartService, wishlistService) {\n    this.router = router;\n    this.http = http;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.stories = [];\n    this.showAddStory = true;\n    this.currentUser = null;\n    this.storyClick = new EventEmitter();\n    this.isLoadingStories = true;\n    this.currentIndex = 0;\n    this.isOpen = false;\n    this.isRotating = false;\n    this.isDragging = false;\n    this.rotateY = 0;\n    this.targetRotateY = 0;\n    this.targetDirection = null;\n    // Touch/drag properties\n    this.dragStartX = 0;\n    this.dragCurrentX = 0;\n    this.minDragPercentToTransition = 0.5;\n    this.minVelocityToTransition = 0.65;\n    this.transitionSpeed = 6;\n    // Carousel state properties\n    this.isCarouselInitialized = false;\n    this.isAutoPlaying = true;\n    this.currentSlideIndex = 0;\n    // Owl Carousel Options\n    this.customOptions = {\n      loop: true,\n      mouseDrag: true,\n      touchDrag: true,\n      pullDrag: false,\n      dots: false,\n      navSpeed: 700,\n      navText: ['<i class=\"fas fa-chevron-left\"></i>', '<i class=\"fas fa-chevron-right\"></i>'],\n      responsive: {\n        0: {\n          items: 3,\n          nav: false\n        },\n        400: {\n          items: 4,\n          nav: false\n        },\n        740: {\n          items: 5,\n          nav: true\n        },\n        940: {\n          items: 6,\n          nav: true\n        }\n      },\n      nav: true,\n      margin: 2,\n      stagePadding: 0,\n      autoplay: true,\n      autoplayTimeout: 4000,\n      autoplayHoverPause: true,\n      autoplaySpeed: 1000 // Animation speed for auto sliding\n    };\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    // Only load stories if none are provided as input\n    if (!this.stories || this.stories.length === 0) {\n      this.loadStories();\n    } else {\n      this.isLoadingStories = false;\n    }\n    this.setupEventListeners();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n  loadStories() {\n    this.isLoadingStories = true;\n    // Try to load from API first\n    this.subscriptions.push(this.http.get(`http://localhost:5000/api/v1/stories/active`).subscribe({\n      next: response => {\n        if (response.success && response.data && response.data.length > 0) {\n          this.stories = response.data;\n        } else {\n          this.loadFallbackStories();\n        }\n        this.isLoadingStories = false;\n      },\n      error: error => {\n        console.error('Error loading stories:', error);\n        this.loadFallbackStories();\n        this.isLoadingStories = false;\n      }\n    }));\n  }\n  loadFallbackStories() {\n    // Fallback stories with realistic data\n    this.stories = [{\n      _id: 'story-1',\n      user: {\n        _id: 'user-1',\n        username: 'ai_fashionista_maya',\n        fullName: 'Maya Chen',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400',\n      mediaType: 'image',\n      caption: 'Sustainable fashion is the future! 🌱✨',\n      createdAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 19 * 60 * 60 * 1000).toISOString(),\n      views: 1247,\n      isActive: true,\n      products: [{\n        _id: 'prod-1',\n        name: 'Eco-Friendly Summer Dress',\n        price: 2499,\n        image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=200'\n      }]\n    }, {\n      _id: 'story-2',\n      user: {\n        _id: 'user-2',\n        username: 'style_guru_alex',\n        fullName: 'Alex Rodriguez',\n        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400',\n      mediaType: 'image',\n      caption: 'New collection drop! Limited edition pieces 🔥',\n      createdAt: new Date(Date.now() - 15 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 18.75 * 60 * 60 * 1000).toISOString(),\n      views: 892,\n      isActive: true,\n      products: [{\n        _id: 'prod-2',\n        name: 'Designer Leather Jacket',\n        price: 8999,\n        image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=200'\n      }]\n    }, {\n      _id: 'story-3',\n      user: {\n        _id: 'user-3',\n        username: 'trendy_sarah',\n        fullName: 'Sarah Johnson',\n        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=400',\n      mediaType: 'image',\n      caption: 'Summer vibes with this amazing outfit! ☀️👗',\n      createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 18.5 * 60 * 60 * 1000).toISOString(),\n      views: 1534,\n      isActive: true,\n      products: [{\n        _id: 'prod-3',\n        name: 'Floral Summer Dress',\n        price: 3499,\n        image: 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=200'\n      }]\n    }, {\n      _id: 'story-4',\n      user: {\n        _id: 'user-4',\n        username: 'fashion_forward_mike',\n        fullName: 'Mike Thompson',\n        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1490578474895-699cd4e2cf59?w=400',\n      mediaType: 'image',\n      caption: 'Streetwear meets luxury fashion 🔥',\n      createdAt: new Date(Date.now() - 45 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 18.25 * 60 * 60 * 1000).toISOString(),\n      views: 2103,\n      isActive: true,\n      products: [{\n        _id: 'prod-4',\n        name: 'Premium Sneakers',\n        price: 12999,\n        image: 'https://images.unsplash.com/photo-1490578474895-699cd4e2cf59?w=200'\n      }]\n    }, {\n      _id: 'story-5',\n      user: {\n        _id: 'user-5',\n        username: 'chic_emma',\n        fullName: 'Emma Wilson',\n        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150'\n      },\n      mediaUrl: 'https://images.unsplash.com/photo-1483985988355-763728e1935b?w=400',\n      mediaType: 'image',\n      caption: 'Minimalist fashion for the modern woman 💫',\n      createdAt: new Date(Date.now() - 60 * 60 * 1000).toISOString(),\n      expiresAt: new Date(Date.now() + 18 * 60 * 60 * 1000).toISOString(),\n      views: 756,\n      isActive: true,\n      products: [{\n        _id: 'prod-5',\n        name: 'Minimalist Blazer',\n        price: 5999,\n        image: 'https://images.unsplash.com/photo-1483985988355-763728e1935b?w=200'\n      }]\n    }];\n  }\n  getCurrentStory() {\n    return this.stories[this.currentIndex] || this.stories[0];\n  }\n  getTimeAgo(dateString) {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  openStories(index = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n    // Emit story click event\n    if (this.stories[index]) {\n      this.storyClick.emit({\n        story: this.stories[index],\n        index\n      });\n    }\n  }\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    // Add closing animation\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n  showStory(index) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    // Reset container transform\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n  handleKeydown(event) {\n    if (!this.isOpen) return;\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n  onStoryClick(event) {\n    if (this.isRotating) return;\n    const rect = event.target.getBoundingClientRect();\n    const clickX = event.clientX - rect.left;\n    const width = rect.width;\n    if (clickX < width / 2) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n  onTouchStart(event) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n  onTouchMove(event) {\n    if (!this.isDragging) return;\n    this.dragCurrentX = event.touches[0].clientX;\n    const dragDistance = this.dragCurrentX - this.dragStartX;\n    const dragPercent = Math.abs(dragDistance) / window.innerWidth;\n    if (dragPercent > this.minDragPercentToTransition) {\n      if (dragDistance > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n      this.isDragging = false;\n    }\n  }\n  onTouchEnd(_event) {\n    this.isDragging = false;\n  }\n  setupEventListeners() {\n    // Add any additional event listeners here\n  }\n  removeEventListeners() {\n    // Remove any additional event listeners here\n  }\n  pauseAllVideos() {\n    const videos = document.querySelectorAll('video');\n    videos.forEach(video => {\n      video.pause();\n    });\n  }\n  update() {\n    if (!this.isRotating) return;\n    const diff = this.targetRotateY - this.rotateY;\n    this.rotateY += diff * 0.1;\n    if (Math.abs(diff) < 0.1) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n      if (this.targetDirection === 'forward') {\n        this.currentIndex++;\n      } else if (this.targetDirection === 'back') {\n        this.currentIndex--;\n      }\n      this.targetRotateY = 0;\n      this.targetDirection = null;\n    }\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n    if (this.isRotating) {\n      requestAnimationFrame(() => this.update());\n    }\n  }\n  hasProducts() {\n    const story = this.getCurrentStory();\n    return !!(story?.products && story.products.length > 0);\n  }\n  getStoryProducts() {\n    return this.getCurrentStory().products || [];\n  }\n  formatPrice(price) {\n    return `₹${(price / 100).toLocaleString('en-IN')}`;\n  }\n  viewProductDetails(product) {\n    console.log('Viewing product:', product);\n    // Navigate to product page or show product modal\n    this.router.navigate(['/products', product._id]);\n  }\n  getCurrentUserAvatar() {\n    // Use currentUser input if available, otherwise return default avatar\n    return this.currentUser?.avatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150';\n  }\n  openAddStoryModal() {\n    console.log('Opening add story modal');\n    // Navigate to add story page or open modal\n    this.router.navigate(['/add-story']);\n  }\n  buyNow() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0]; // Get first product for now\n      console.log('Buying product:', product);\n      // Navigate to checkout with product\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: product._id,\n          source: 'story'\n        }\n      });\n    }\n  }\n  // Direct product navigation\n  viewProduct(productId) {\n    // Track product click analytics\n    this.trackProductClick(productId, 'view_product');\n    // Navigate to product detail page\n    this.router.navigate(['/shop/product', productId]);\n  }\n  viewCategory(categoryId) {\n    // Navigate to category page\n    this.router.navigate(['/shop/category', categoryId]);\n  }\n  trackProductClick(productId, action) {\n    // Track analytics for product clicks from stories\n    console.log(`Story product ${action} tracked:`, productId);\n    // TODO: Implement analytics tracking API call\n  }\n  addToWishlist() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0];\n      console.log('Adding to wishlist:', product);\n      this.wishlistService.addToWishlist(product._id).subscribe({\n        next: response => {\n          if (response.success) {\n            alert('Product added to wishlist!');\n          } else {\n            alert('Failed to add product to wishlist');\n          }\n        },\n        error: error => {\n          console.error('Error adding to wishlist:', error);\n          alert('Error adding product to wishlist');\n        }\n      });\n    }\n  }\n  addToCart() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0];\n      console.log('Adding to cart:', product);\n      this.cartService.addToCart(product._id, 1, undefined, undefined).subscribe({\n        next: response => {\n          if (response.success) {\n            alert('Product added to cart!');\n          } else {\n            alert('Failed to add product to cart');\n          }\n        },\n        error: error => {\n          console.error('Error adding to cart:', error);\n          alert('Error adding product to cart');\n        }\n      });\n    }\n  }\n  // Owl Carousel Event Handlers\n  onSlideChanged(event) {\n    // Handle slide change events\n    if (event && event.startPosition !== undefined) {\n      this.currentSlideIndex = event.startPosition;\n      // Log slide change for debugging\n      console.log(`Stories slide changed to: ${this.currentSlideIndex}`);\n      // Update any slide-specific logic here\n      this.updateSlideAnalytics();\n    }\n  }\n  onInitialized(_event) {\n    // Handle carousel initialization\n    this.isCarouselInitialized = true;\n    console.log('Stories carousel initialized successfully with auto-sliding enabled');\n  }\n  // Analytics for slide changes\n  updateSlideAnalytics() {\n    // Track slide interactions for analytics\n    if (this.stories && this.stories[this.currentSlideIndex]) {\n      const currentStory = this.stories[this.currentSlideIndex];\n      console.log(`Viewing story from: ${currentStory.user.username}`);\n    }\n  }\n  // Method to toggle auto-play (can be called from template if needed)\n  toggleAutoPlay() {\n    this.isAutoPlaying = !this.isAutoPlaying;\n    // Note: Owl Carousel doesn't have a direct method to toggle autoplay\n    // This would require reinitializing the carousel with new options\n    console.log(`Auto-play ${this.isAutoPlaying ? 'enabled' : 'disabled'}`);\n  }\n  static {\n    this.ɵfac = function ViewAddStoriesComponent_Factory(t) {\n      return new (t || ViewAddStoriesComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewAddStoriesComponent,\n      selectors: [[\"app-view-add-stories\"]],\n      viewQuery: function ViewAddStoriesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.feedCover = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.storiesSlider = _t.first);\n        }\n      },\n      hostBindings: function ViewAddStoriesComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function ViewAddStoriesComponent_keydown_HostBindingHandler($event) {\n            return ctx.handleKeydown($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      inputs: {\n        stories: \"stories\",\n        showAddStory: \"showAddStory\",\n        currentUser: \"currentUser\"\n      },\n      outputs: {\n        storyClick: \"storyClick\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 4,\n      consts: [[\"storiesContainer\", \"\"], [\"feedCover\", \"\"], [1, \"stories-container\"], [\"class\", \"stories-loading\", 4, \"ngIf\"], [\"class\", \"stories-section\", 4, \"ngIf\"], [\"class\", \"stories-wrapper\", 3, \"is-open\", 4, \"ngIf\"], [\"class\", \"touch-indicators\", 4, \"ngIf\"], [1, \"stories-loading\"], [\"class\", \"story-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"story-skeleton\"], [1, \"skeleton-avatar\"], [1, \"skeleton-name\"], [1, \"stories-section\"], [1, \"add-story-static\"], [1, \"story-item\", \"add-story-item\", 3, \"click\"], [1, \"story-avatar-container\"], [1, \"add-story-avatar\"], [1, \"add-story-icon\"], [1, \"fas\", \"fa-plus\"], [1, \"current-user-avatar\"], [1, \"story-username\"], [1, \"stories-slider-wrapper\"], [1, \"stories-slider-container\"], [3, \"initialized\", \"changed\", \"options\"], [4, \"ngFor\", \"ngForOf\"], [\"carouselSlide\", \"\"], [1, \"story-slide\", 3, \"click\"], [1, \"story-avatar\"], [1, \"story-ring\"], [1, \"stories-wrapper\"], [1, \"stories\"], [1, \"story-progress\"], [\"class\", \"story-progress__bar\", 3, \"active\", \"completed\", 4, \"ngFor\", \"ngForOf\"], [1, \"story\", 3, \"click\", \"touchstart\", \"touchmove\", \"touchend\"], [1, \"story__top\"], [1, \"story__details\"], [1, \"story__avatar\"], [1, \"story__user\"], [1, \"story__time\"], [1, \"story__views\"], [1, \"story__close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"story__content\"], [\"class\", \"story__video\", \"autoplay\", \"\", \"muted\", \"\", \"loop\", \"\", \"playsinline\", \"\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"story__image\", 3, \"background-image\", 4, \"ngIf\"], [\"class\", \"story__caption\", 4, \"ngIf\"], [\"class\", \"story__product-tags\", 4, \"ngIf\"], [\"class\", \"middle-navigation\", 4, \"ngIf\"], [1, \"story__bottom\"], [1, \"story__actions\"], [1, \"story__action-btn\", \"like-btn\"], [1, \"fas\", \"fa-heart\"], [1, \"story__action-btn\", \"comment-btn\"], [1, \"fas\", \"fa-comment\"], [1, \"story__action-btn\", \"share-btn\"], [1, \"fas\", \"fa-share\"], [\"class\", \"story__ecommerce-actions\", 4, \"ngIf\"], [1, \"story__nav-area\", \"story__nav-prev\"], [1, \"story__nav-area\", \"story__nav-next\"], [1, \"feed__cover\"], [1, \"story-progress__bar\"], [1, \"story-progress__fill\"], [\"autoplay\", \"\", \"muted\", \"\", \"loop\", \"\", \"playsinline\", \"\", 1, \"story__video\", 3, \"src\"], [1, \"story__image\"], [1, \"story__caption\"], [1, \"story__product-tags\"], [\"class\", \"product-tag\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag\", 3, \"click\"], [1, \"product-tag-icon\"], [1, \"product-tag-info\"], [1, \"product-tag-name\"], [1, \"product-tag-price\"], [1, \"middle-navigation\"], [1, \"middle-nav-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"story__ecommerce-actions\"], [1, \"ecommerce-btn\", \"buy-now-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"ecommerce-btn\", \"wishlist-btn\", 3, \"click\"], [1, \"ecommerce-btn\", \"cart-btn\", 3, \"click\"], [1, \"touch-indicators\"], [1, \"touch-indicator\", \"left\"], [1, \"fas\", \"fa-chevron-left\"], [1, \"touch-indicator\", \"right\"], [1, \"fas\", \"fa-chevron-right\"]],\n      template: function ViewAddStoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 2);\n          i0.ɵɵtemplate(1, ViewAddStoriesComponent_div_1_Template, 2, 2, \"div\", 3)(2, ViewAddStoriesComponent_div_2_Template, 14, 4, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, ViewAddStoriesComponent_div_3_Template, 36, 17, \"div\", 5)(4, ViewAddStoriesComponent_div_4_Template, 9, 0, \"div\", 6);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingStories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n        }\n      },\n      dependencies: [CommonModule, i5.NgForOf, i5.NgIf, CarouselModule, i6.CarouselComponent, i6.CarouselSlideDirective],\n      styles: [\".stories-container[_ngcontent-%COMP%] {\\n  background: white;\\n  border-bottom: 1px solid #dbdbdb;\\n  padding: 16px 0;\\n  margin-bottom: 0;\\n  max-width: 500px;\\n}\\n\\n.stories-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 16px;\\n  padding: 0 16px;\\n}\\n\\n.add-story-static[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  width: 82px;\\n}\\n\\n.stories-slider-wrapper[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: hidden;\\n  max-width: calc(100% - 98px);\\n  position: relative;\\n}\\n.stories-slider-wrapper[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  right: 0;\\n  width: 20px;\\n  height: 100%;\\n  background: linear-gradient(to left, rgba(255, 255, 255, 0.8), transparent);\\n  pointer-events: none;\\n  z-index: 5;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.stories-slider-wrapper.has-overflow[_ngcontent-%COMP%]::after {\\n  opacity: 1;\\n}\\n\\n.stories-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  overflow: visible; \\n\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage-outer {\\n  padding: 0;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage {\\n  display: flex;\\n  align-items: flex-start;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-item {\\n  display: flex;\\n  justify-content: center;\\n  align-items: flex-start;\\n  min-height: 120px;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 100%;\\n  pointer-events: none;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: rgba(0, 0, 0, 0.5); \\n\\n  color: #fff; \\n\\n  border: none;\\n  display: none; \\n\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  z-index: 10;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\\n  pointer-events: all;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev:hover, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next:hover {\\n  background: rgba(0, 0, 0, 0.7);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev:active, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next:active {\\n  transform: translateY(-50%) scale(0.95);\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev i, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev .fas, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next i, .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next .fas {\\n  font-size: 14px;\\n  color: #fff;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev {\\n  left: -20px; \\n\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next {\\n  right: -20px; \\n\\n}\\n@media (max-width: 768px) {\\n  .stories-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav {\\n    display: none;\\n  }\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel.owl-loaded .owl-stage-outer {\\n  position: relative;\\n}\\n.stories-slider-container[_ngcontent-%COMP%]     .owl-carousel.owl-loaded .owl-stage-outer::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -2px;\\n  left: 0;\\n  right: 0;\\n  height: 2px;\\n  background: linear-gradient(90deg, transparent 0%, #405de6 50%, transparent 100%);\\n  opacity: 0.3;\\n  animation: _ngcontent-%COMP%_autoSlideIndicator 4s infinite linear;\\n}\\n\\n@keyframes _ngcontent-%COMP%_autoSlideIndicator {\\n  0% {\\n    transform: translateX(-100%);\\n  }\\n  100% {\\n    transform: translateX(100%);\\n  }\\n}\\n.story-slide[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n  width: 66px;\\n}\\n.story-slide[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  animation-play-state: paused;\\n}\\n\\n.slider-nav-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.9);\\n  border: 1px solid rgba(0, 0, 0, 0.1);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  z-index: 10;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.slider-nav-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.slider-nav-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(-50%) scale(0.95);\\n}\\n.slider-nav-btn.hidden[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  pointer-events: none;\\n}\\n.slider-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #262626;\\n}\\n\\n.slider-nav-left[_ngcontent-%COMP%] {\\n  left: -16px;\\n}\\n\\n.slider-nav-right[_ngcontent-%COMP%] {\\n  right: -16px;\\n}\\n\\n.story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  cursor: pointer;\\n  flex-shrink: 0;\\n  transition: all 0.3s ease;\\n  width: 82px;\\n  min-width: 82px;\\n  position: relative;\\n}\\n.story-item[_ngcontent-%COMP%]:hover, .story-slide[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.story-item[_ngcontent-%COMP%]:hover   .story-ring[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%]:hover   .story-ring[_ngcontent-%COMP%] {\\n  animation-duration: 1s;\\n}\\n.story-item[_ngcontent-%COMP%]:hover   .story-username[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%]:hover   .story-username[_ngcontent-%COMP%] {\\n  color: #0095f6;\\n  font-weight: 600;\\n}\\n.story-item[_ngcontent-%COMP%]:active, .story-slide[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n\\n.story-slide.active[_ngcontent-%COMP%]   .story-ring[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n.story-slide.active[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n  color: #405de6;\\n  font-weight: 600;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  50% {\\n    transform: scale(1.05);\\n    opacity: 0.8;\\n  }\\n  100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n.story-avatar-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 8px;\\n}\\n\\n.story-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 2px solid white;\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n.story-ring[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  width: 70px;\\n  height: 70px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: 1;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n.story-ring.viewed[_ngcontent-%COMP%] {\\n  background: #c7c7c7;\\n  animation: none;\\n}\\n.story-ring.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  animation: _ngcontent-%COMP%_pulse 1.5s infinite;\\n  box-shadow: 0 0 10px rgba(240, 148, 51, 0.5);\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n  }\\n}\\n.story-username[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #262626;\\n  font-weight: 400;\\n  max-width: 74px;\\n  text-align: center;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.add-story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #262626;\\n}\\n\\n.add-story-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  position: relative;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 2;\\n}\\n\\n.add-story-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 2px;\\n  right: 2px;\\n  width: 20px;\\n  height: 20px;\\n  background: #0095f6;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border: 2px solid white;\\n  z-index: 3;\\n}\\n.add-story-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 10px;\\n  font-weight: bold;\\n}\\n\\n.current-user-avatar[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 2px solid white;\\n}\\n\\n.stories-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  padding: 0 16px;\\n}\\n\\n.story-skeleton[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.skeleton-avatar[_ngcontent-%COMP%] {\\n  width: 66px;\\n  height: 66px;\\n  border-radius: 50%;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n}\\n\\n.skeleton-name[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 12px;\\n  border-radius: 6px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    background-position: -200% 0;\\n  }\\n  100% {\\n    background-position: 200% 0;\\n  }\\n}\\n.story-bar__user[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n}\\n.story-bar__user[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.story-bar__user.bounce[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_bounce 0.3s ease;\\n}\\n\\n.story-bar__user-avatar[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 3px solid transparent;\\n  background-clip: padding-box;\\n  position: relative;\\n}\\n.story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -3px;\\n  left: -3px;\\n  right: -3px;\\n  bottom: -3px;\\n  border-radius: 50%;\\n  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);\\n  z-index: -1;\\n}\\n\\n.story-bar__user-name[_ngcontent-%COMP%] {\\n  margin-top: 4px;\\n  font-size: 12px;\\n  color: #262626;\\n  text-align: center;\\n  max-width: 64px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.stories-wrapper[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: #000;\\n  z-index: 9999;\\n  perspective: 400px;\\n  overflow: hidden;\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: opacity 0.3s ease, visibility 0.3s ease;\\n}\\n.stories-wrapper.is-open[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n}\\n\\n.stories[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  transform-style: preserve-3d;\\n  transform: translateZ(-50vw);\\n  transition: transform 0.25s ease-out;\\n}\\n.stories.is-closed[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  transform: scale(0.1);\\n}\\n\\n.story-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  left: 8px;\\n  right: 8px;\\n  display: flex;\\n  gap: 2px;\\n  z-index: 100;\\n}\\n\\n.story-progress__bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 2px;\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 1px;\\n  overflow: hidden;\\n}\\n.story-progress__bar.completed[_ngcontent-%COMP%]   .story-progress__fill[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.story-progress__bar.active[_ngcontent-%COMP%]   .story-progress__fill[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_progress 15s linear;\\n}\\n\\n.story-progress__fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: #fff;\\n  width: 0%;\\n  transition: width 0.1s ease;\\n}\\n\\n@keyframes _ngcontent-%COMP%_progress {\\n  from {\\n    width: 0%;\\n  }\\n  to {\\n    width: 100%;\\n  }\\n}\\n.story[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 100%;\\n  height: 100%;\\n  overflow: hidden;\\n  display: flex;\\n  flex-direction: column;\\n  -webkit-user-select: none;\\n          user-select: none;\\n}\\n\\n.story__top[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  padding: 48px 16px 16px;\\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);\\n  z-index: 10;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.story__details[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.story__avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-size: cover;\\n  background-position: center;\\n  border: 2px solid #fff;\\n}\\n\\n.story__user[_ngcontent-%COMP%] {\\n  color: #fff;\\n  font-weight: 600;\\n  font-size: 14px;\\n}\\n\\n.story__time[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 12px;\\n}\\n\\n.story__views[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.6);\\n  font-size: 11px;\\n  margin-left: 8px;\\n}\\n\\n.story__close[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #fff;\\n  font-size: 18px;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: background 0.2s ease;\\n}\\n.story__close[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n}\\n\\n.story__content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #000;\\n}\\n\\n.story__video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.story__image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  background-size: cover;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n.story__caption[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 120px;\\n  left: 16px;\\n  right: 16px;\\n  background: rgba(0, 0, 0, 0.6);\\n  color: white;\\n  padding: 12px 16px;\\n  border-radius: 20px;\\n  font-size: 14px;\\n  line-height: 1.4;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  z-index: 5;\\n}\\n\\n.story__product-tags[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 20px;\\n  transform: translateY(-50%);\\n  z-index: 6;\\n}\\n\\n.product-tag[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  border-radius: 12px;\\n  padding: 8px 12px;\\n  margin-bottom: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  min-width: 160px;\\n}\\n.product-tag[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  background: rgb(255, 255, 255);\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\\n}\\n\\n.product-tag-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.product-tag-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.product-tag-name[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 2px;\\n  line-height: 1.2;\\n}\\n\\n.product-tag-price[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #666;\\n  font-weight: 500;\\n}\\n\\n.story__bottom[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  padding: 16px;\\n  background: linear-gradient(0deg, rgba(0, 0, 0, 0.6) 0%, transparent 100%);\\n  z-index: 10;\\n}\\n\\n.story__actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 12px;\\n}\\n\\n.story__action-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #fff;\\n  font-size: 20px;\\n  cursor: pointer;\\n  padding: 8px;\\n  border-radius: 50%;\\n  transition: all 0.2s ease;\\n}\\n.story__action-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.1);\\n  transform: scale(1.1);\\n}\\n\\n.story__ecommerce-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  justify-content: center;\\n}\\n\\n.ecommerce-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  padding: 8px 12px;\\n  border: none;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.ecommerce-btn.buy-now-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff6b6b, #ee5a24);\\n  color: #fff;\\n}\\n.ecommerce-btn.buy-now-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);\\n}\\n.ecommerce-btn.wishlist-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff9ff3, #f368e0);\\n  color: #fff;\\n}\\n.ecommerce-btn.wishlist-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(255, 159, 243, 0.4);\\n}\\n.ecommerce-btn.cart-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #54a0ff, #2e86de);\\n  color: #fff;\\n}\\n.ecommerce-btn.cart-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(84, 160, 255, 0.4);\\n}\\n\\n.story__nav-area[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  width: 33%;\\n  z-index: 5;\\n  cursor: pointer;\\n}\\n.story__nav-area.story__nav-prev[_ngcontent-%COMP%] {\\n  left: 0;\\n}\\n.story__nav-area.story__nav-next[_ngcontent-%COMP%] {\\n  right: 0;\\n  width: 67%;\\n}\\n\\n.feed__cover[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: #fff;\\n  z-index: -1;\\n}\\n.feed__cover.is-hidden[_ngcontent-%COMP%] {\\n  opacity: 0;\\n}\\n\\n.touch-indicators[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 50%;\\n  left: 0;\\n  right: 0;\\n  transform: translateY(-50%);\\n  z-index: 101;\\n  pointer-events: none;\\n  display: none;\\n}\\n@media (max-width: 768px) {\\n  .touch-indicators[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n\\n.touch-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  color: rgba(255, 255, 255, 0.7);\\n  font-size: 12px;\\n  animation: _ngcontent-%COMP%_fadeInOut 3s infinite;\\n}\\n.touch-indicator.left[_ngcontent-%COMP%] {\\n  left: 16px;\\n}\\n.touch-indicator.right[_ngcontent-%COMP%] {\\n  right: 16px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInOut {\\n  0%, 100% {\\n    opacity: 0;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_bounce {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(0.8);\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n    gap: 10px;\\n    overflow-x: auto;\\n    scroll-behavior: smooth;\\n    -webkit-overflow-scrolling: touch;\\n  }\\n  .stories-wrapper[_ngcontent-%COMP%] {\\n    touch-action: pan-y;\\n  }\\n  .story[_ngcontent-%COMP%] {\\n    touch-action: manipulation;\\n  }\\n  .stories-section[_ngcontent-%COMP%] {\\n    gap: 12px;\\n    padding: 0 12px;\\n  }\\n  .add-story-static[_ngcontent-%COMP%] {\\n    width: 70px;\\n  }\\n  .stories-slider-wrapper[_ngcontent-%COMP%] {\\n    max-width: calc(100% - 82px);\\n  }\\n  .story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%] {\\n    width: 70px;\\n    min-width: 70px;\\n  }\\n  .stories-list[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n  .slider-nav-btn[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    gap: 8px;\\n    scrollbar-width: none;\\n    -ms-overflow-style: none;\\n  }\\n  .story-bar[_ngcontent-%COMP%]::-webkit-scrollbar {\\n    display: none;\\n  }\\n  .stories-section[_ngcontent-%COMP%] {\\n    gap: 10px;\\n    padding: 0 8px;\\n  }\\n  .add-story-static[_ngcontent-%COMP%] {\\n    width: 60px;\\n  }\\n  .stories-slider-wrapper[_ngcontent-%COMP%] {\\n    max-width: calc(100% - 70px);\\n  }\\n  .story-item[_ngcontent-%COMP%], .story-slide[_ngcontent-%COMP%] {\\n    width: 60px;\\n    min-width: 60px;\\n  }\\n  .stories-list[_ngcontent-%COMP%] {\\n    gap: 10px;\\n  }\\n  .slider-nav-btn[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .story-avatar[_ngcontent-%COMP%] {\\n    width: 56px;\\n    height: 56px;\\n  }\\n  .story-ring[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n    top: -2px;\\n    left: -2px;\\n  }\\n  .add-story-avatar[_ngcontent-%COMP%] {\\n    width: 56px;\\n    height: 56px;\\n  }\\n  .current-user-avatar[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n  }\\n  .story-username[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 60px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n    top: -2px;\\n    left: -2px;\\n    right: -2px;\\n    bottom: -2px;\\n  }\\n  .story-bar__user-name[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n    max-width: 56px;\\n  }\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 40px 12px 12px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    flex-wrap: wrap;\\n    gap: 6px;\\n    justify-content: space-between;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    font-size: 11px;\\n    flex: 1;\\n    min-width: 80px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .story__actions[_ngcontent-%COMP%] {\\n    gap: 12px;\\n    margin-bottom: 8px;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n    padding: 6px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .story-bar[_ngcontent-%COMP%] {\\n    padding: 6px 8px;\\n    gap: 6px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n  .story-bar__user-avatar[_ngcontent-%COMP%]::before {\\n    top: -2px;\\n    left: -2px;\\n    right: -2px;\\n    bottom: -2px;\\n  }\\n  .story-bar__user-name[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n    max-width: 48px;\\n  }\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 32px 8px 8px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 4px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 6px 8px;\\n    font-size: 10px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .story__actions[_ngcontent-%COMP%] {\\n    gap: 8px;\\n    margin-bottom: 6px;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    padding: 4px;\\n  }\\n  .story__user[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .story__time[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .story__avatar[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n  }\\n}\\n@media (hover: none) and (pointer: coarse) {\\n  .story-bar__user[_ngcontent-%COMP%]:active {\\n    transform: scale(0.95);\\n    transition: transform 0.1s ease;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%]:active {\\n    transform: scale(0.95);\\n    transition: transform 0.1s ease;\\n  }\\n  .story__action-btn[_ngcontent-%COMP%]:active {\\n    transform: scale(0.9);\\n    transition: transform 0.1s ease;\\n  }\\n  .story__close[_ngcontent-%COMP%]:active {\\n    transform: scale(0.9);\\n    transition: transform 0.1s ease;\\n  }\\n}\\n@media (max-width: 896px) and (orientation: landscape) {\\n  .story__top[_ngcontent-%COMP%] {\\n    padding: 24px 12px 8px;\\n  }\\n  .story__bottom[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n  }\\n  .story__ecommerce-actions[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    gap: 8px;\\n  }\\n  .ecommerce-btn[_ngcontent-%COMP%] {\\n    padding: 6px 10px;\\n    font-size: 10px;\\n  }\\n}\\n\\n\\n.middle-navigation[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 100px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  z-index: 4;\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(45deg, #ff6b6b, #ee5a24);\\n  border: none;\\n  border-radius: 25px;\\n  padding: 12px 24px;\\n  color: white;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n\\n@media (min-resolution: 192dpi) {\\n  .story-bar__user-avatar[_ngcontent-%COMP%] {\\n    image-rendering: -webkit-optimize-contrast;\\n    image-rendering: crisp-edges;\\n  }\\n  .story__avatar[_ngcontent-%COMP%] {\\n    image-rendering: -webkit-optimize-contrast;\\n    image-rendering: crisp-edges;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .middle-navigation[_ngcontent-%COMP%] {\\n    bottom: 80px;\\n  }\\n  .middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%] {\\n    padding: 10px 20px;\\n    font-size: 12px;\\n  }\\n  .middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .middle-navigation[_ngcontent-%COMP%]   .middle-nav-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "CommonModule", "CarouselModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "ViewAddStoriesComponent_div_1_div_1_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c3", "ɵɵlistener", "ViewAddStoriesComponent_div_2_ng_container_13_ng_template_1_Template_div_click_0_listener", "ɵɵrestoreView", "_r3", "i_r4", "ɵɵnextContext", "index", "ctx_r1", "ɵɵresetView", "openStories", "ɵɵtext", "ɵɵstyleProp", "story_r5", "user", "avatar", "ɵɵclassProp", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵtextInterpolate", "username", "ɵɵelementContainerStart", "ViewAddStoriesComponent_div_2_ng_container_13_ng_template_1_Template", "ViewAddStoriesComponent_div_2_Template_div_click_2_listener", "_r1", "openAddStoryModal", "ViewAddStoriesComponent_div_2_Template_owl_carousel_o_initialized_12_listener", "$event", "onInitialized", "ViewAddStoriesComponent_div_2_Template_owl_carousel_o_changed_12_listener", "onSlideChanged", "ViewAddStoriesComponent_div_2_ng_container_13_Template", "getCurrentUserAvatar", "customOptions", "stories", "i_r7", "currentIndex", "getCurrentStory", "mediaUrl", "ɵɵsanitizeUrl", "ɵɵtextInterpolate1", "caption", "ViewAddStoriesComponent_div_3_div_21_div_1_Template_div_click_0_listener", "product_r9", "_r8", "$implicit", "viewProduct", "_id", "name", "formatPrice", "price", "ViewAddStoriesComponent_div_3_div_21_div_1_Template", "getStoryProducts", "ViewAddStoriesComponent_div_3_div_22_Template_button_click_1_listener", "_r10", "ViewAddStoriesComponent_div_3_div_31_Template_button_click_1_listener", "_r11", "buyNow", "ViewAddStoriesComponent_div_3_div_31_Template_button_click_5_listener", "addToWishlist", "ViewAddStoriesComponent_div_3_div_31_Template_button_click_9_listener", "addToCart", "ViewAddStoriesComponent_div_3_div_4_Template", "ViewAddStoriesComponent_div_3_Template_div_click_5_listener", "_r6", "onStoryClick", "ViewAddStoriesComponent_div_3_Template_div_touchstart_5_listener", "onTouchStart", "ViewAddStoriesComponent_div_3_Template_div_touchmove_5_listener", "onTouchMove", "ViewAddStoriesComponent_div_3_Template_div_touchend_5_listener", "onTouchEnd", "ViewAddStoriesComponent_div_3_Template_button_click_15_listener", "closeStories", "ViewAddStoriesComponent_div_3_video_18_Template", "ViewAddStoriesComponent_div_3_div_19_Template", "ViewAddStoriesComponent_div_3_div_20_Template", "ViewAddStoriesComponent_div_3_div_21_Template", "ViewAddStoriesComponent_div_3_div_22_Template", "ViewAddStoriesComponent_div_3_div_31_Template", "isOpen", "fullName", "getTimeAgo", "createdAt", "formatNumber", "views", "mediaType", "hasProducts", "ViewAddStoriesComponent", "constructor", "router", "http", "cartService", "wishlistService", "showAddStory", "currentUser", "storyClick", "isLoadingStories", "isRotating", "isDragging", "rotateY", "targetRotateY", "targetDirection", "dragStartX", "dragCurrentX", "minDragPercentToTransition", "minVelocityToTransition", "transitionSpeed", "isCarouselInitialized", "isAutoPlaying", "currentSlideIndex", "loop", "mouseDrag", "touchDrag", "pullDrag", "dots", "navSpeed", "navText", "responsive", "items", "nav", "margin", "stagePadding", "autoplay", "autoplayTimeout", "autoplayHoverPause", "autoplaySpeed", "subscriptions", "ngOnInit", "length", "loadStories", "setupEventListeners", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "removeEventListeners", "push", "get", "subscribe", "next", "response", "success", "data", "loadFallbackStories", "error", "console", "Date", "now", "toISOString", "expiresAt", "isActive", "products", "image", "dateString", "date", "diffInMinutes", "Math", "floor", "getTime", "diffInHours", "diffInDays", "num", "toFixed", "toString", "showStory", "document", "body", "style", "overflow", "emit", "story", "pauseAllVideos", "storiesContainer", "nativeElement", "classList", "add", "setTimeout", "remove", "transform", "nextStory", "update", "previousStory", "handleKeydown", "event", "key", "rect", "target", "getBoundingClientRect", "clickX", "clientX", "left", "width", "touches", "dragDistance", "dragPercent", "abs", "window", "innerWidth", "_event", "videos", "querySelectorAll", "video", "pause", "diff", "requestAnimationFrame", "toLocaleString", "viewProductDetails", "product", "log", "navigate", "queryParams", "productId", "source", "trackProductClick", "viewCategory", "categoryId", "action", "alert", "undefined", "startPosition", "updateSlideAnalytics", "currentStory", "toggleAutoPlay", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "i3", "CartService", "i4", "WishlistService", "selectors", "viewQuery", "ViewAddStoriesComponent_Query", "rf", "ctx", "ViewAddStoriesComponent_keydown_HostBindingHandler", "ɵɵresolveDocument", "ViewAddStoriesComponent_div_1_Template", "ViewAddStoriesComponent_div_2_Template", "ViewAddStoriesComponent_div_3_Template", "ViewAddStoriesComponent_div_4_Template", "i5", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i6", "CarouselComponent", "CarouselSlideDirective", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\view-add-stories\\view-add-stories.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\view-add-stories\\view-add-stories.component.html"], "sourcesContent": ["import { <PERSON>mponent, On<PERSON>nit, On<PERSON><PERSON>roy, ElementRef, ViewChild, HostListener, Input, Output, EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { CartService } from '../../../../core/services/cart.service';\nimport { WishlistService } from '../../../../core/services/wishlist.service';\nimport { HttpClient } from '@angular/common/http';\nimport { Subscription } from 'rxjs';\nimport { CarouselModule, OwlOptions } from 'ngx-owl-carousel-o';\n// import { environment } from '../../../../environments/environment';\n\nexport interface Story {\n  _id: string;\n  user: {\n    _id: string;\n    username: string;\n    fullName: string;\n    avatar: string;\n  };\n  mediaUrl: string;\n  mediaType: 'image' | 'video';\n  caption?: string;\n  createdAt: string;\n  expiresAt: string;\n  views: number;\n  isActive: boolean;\n  isViewed?: boolean; // Added for story viewing state\n  products?: Array<{\n    _id: string;\n    name: string;\n    price: number;\n    image: string;\n  }>;\n}\n\nexport interface CurrentUser {\n  _id: string;\n  username: string;\n  fullName: string;\n  avatar: string;\n}\n\n@Component({\n  selector: 'app-view-add-stories',\n  standalone: true,\n  imports: [CommonModule, CarouselModule],\n  templateUrl: './view-add-stories.component.html',\n  styleUrls: ['./view-add-stories.component.scss']\n})\nexport class ViewAddStoriesComponent implements OnInit, OnDestroy {\n  @ViewChild('storiesContainer', { static: false }) storiesContainer!: ElementRef;\n  @ViewChild('feedCover', { static: false }) feedCover!: ElementRef;\n  @ViewChild('storiesSlider', { static: false }) storiesSlider!: ElementRef;\n\n  @Input() stories: Story[] = [];\n  @Input() showAddStory: boolean = true;\n  @Input() currentUser: CurrentUser | null = null;\n  @Output() storyClick = new EventEmitter<{ story: Story; index: number }>();\n\n  isLoadingStories = true;\n\n  currentIndex = 0;\n  isOpen = false;\n  isRotating = false;\n  isDragging = false;\n  rotateY = 0;\n  targetRotateY = 0;\n  targetDirection: 'forward' | 'back' | null = null;\n  \n  // Touch/drag properties\n  dragStartX = 0;\n  dragCurrentX = 0;\n  minDragPercentToTransition = 0.5;\n  minVelocityToTransition = 0.65;\n  transitionSpeed = 6;\n\n  // Carousel state properties\n  isCarouselInitialized = false;\n  isAutoPlaying = true;\n  currentSlideIndex = 0;\n\n  // Owl Carousel Options\n  customOptions: OwlOptions = {\n    loop: true,\n    mouseDrag: true,\n    touchDrag: true,\n    pullDrag: false,\n    dots: false,\n    navSpeed: 700,\n    navText: ['<i class=\"fas fa-chevron-left\"></i>', '<i class=\"fas fa-chevron-right\"></i>'],\n    responsive: {\n      0: {\n        items: 3,\n        nav: false\n      },\n      400: {\n        items: 4,\n        nav: false\n      },\n      740: {\n        items: 5,\n        nav: true\n      },\n      940: {\n        items: 6,\n        nav: true\n      }\n    },\n    nav: true,\n    margin: 2, // Minimal gap between items\n    stagePadding: 0,\n    autoplay: true, // Enable auto sliding\n    autoplayTimeout: 4000, // 4 seconds between slides\n    autoplayHoverPause: true, // Pause on hover\n    autoplaySpeed: 1000 // Animation speed for auto sliding\n  };\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private router: Router,\n    private http: HttpClient,\n    private cartService: CartService,\n    private wishlistService: WishlistService\n  ) {}\n\n  ngOnInit() {\n    // Only load stories if none are provided as input\n    if (!this.stories || this.stories.length === 0) {\n      this.loadStories();\n    } else {\n      this.isLoadingStories = false;\n    }\n    this.setupEventListeners();\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.removeEventListeners();\n  }\n\n  loadStories() {\n    this.isLoadingStories = true;\n\n    // Try to load from API first\n    this.subscriptions.push(\n      this.http.get<any>(`http://localhost:5000/api/v1/stories/active`).subscribe({\n        next: (response) => {\n          if (response.success && response.data && response.data.length > 0) {\n            this.stories = response.data;\n          } else {\n            this.loadFallbackStories();\n          }\n          this.isLoadingStories = false;\n        },\n        error: (error) => {\n          console.error('Error loading stories:', error);\n          this.loadFallbackStories();\n          this.isLoadingStories = false;\n        }\n      })\n    );\n  }\n\n  loadFallbackStories() {\n    // Fallback stories with realistic data\n    this.stories = [\n      {\n        _id: 'story-1',\n        user: {\n          _id: 'user-1',\n          username: 'ai_fashionista_maya',\n          fullName: 'Maya Chen',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400',\n        mediaType: 'image',\n        caption: 'Sustainable fashion is the future! 🌱✨',\n        createdAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5 minutes ago\n        expiresAt: new Date(Date.now() + 19 * 60 * 60 * 1000).toISOString(), // 19 hours from now\n        views: 1247,\n        isActive: true,\n        products: [\n          {\n            _id: 'prod-1',\n            name: 'Eco-Friendly Summer Dress',\n            price: 2499,\n            image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=200'\n          }\n        ]\n      },\n      {\n        _id: 'story-2',\n        user: {\n          _id: 'user-2',\n          username: 'style_guru_alex',\n          fullName: 'Alex Rodriguez',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400',\n        mediaType: 'image',\n        caption: 'New collection drop! Limited edition pieces 🔥',\n        createdAt: new Date(Date.now() - 15 * 60 * 1000).toISOString(), // 15 minutes ago\n        expiresAt: new Date(Date.now() + 18.75 * 60 * 60 * 1000).toISOString(),\n        views: 892,\n        isActive: true,\n        products: [\n          {\n            _id: 'prod-2',\n            name: 'Designer Leather Jacket',\n            price: 8999,\n            image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=200'\n          }\n        ]\n      },\n      {\n        _id: 'story-3',\n        user: {\n          _id: 'user-3',\n          username: 'trendy_sarah',\n          fullName: 'Sarah Johnson',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=400',\n        mediaType: 'image',\n        caption: 'Summer vibes with this amazing outfit! ☀️👗',\n        createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago\n        expiresAt: new Date(Date.now() + 18.5 * 60 * 60 * 1000).toISOString(),\n        views: 1534,\n        isActive: true,\n        products: [\n          {\n            _id: 'prod-3',\n            name: 'Floral Summer Dress',\n            price: 3499,\n            image: 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=200'\n          }\n        ]\n      },\n      {\n        _id: 'story-4',\n        user: {\n          _id: 'user-4',\n          username: 'fashion_forward_mike',\n          fullName: 'Mike Thompson',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1490578474895-699cd4e2cf59?w=400',\n        mediaType: 'image',\n        caption: 'Streetwear meets luxury fashion 🔥',\n        createdAt: new Date(Date.now() - 45 * 60 * 1000).toISOString(), // 45 minutes ago\n        expiresAt: new Date(Date.now() + 18.25 * 60 * 60 * 1000).toISOString(),\n        views: 2103,\n        isActive: true,\n        products: [\n          {\n            _id: 'prod-4',\n            name: 'Premium Sneakers',\n            price: 12999,\n            image: 'https://images.unsplash.com/photo-1490578474895-699cd4e2cf59?w=200'\n          }\n        ]\n      },\n      {\n        _id: 'story-5',\n        user: {\n          _id: 'user-5',\n          username: 'chic_emma',\n          fullName: 'Emma Wilson',\n          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150'\n        },\n        mediaUrl: 'https://images.unsplash.com/photo-1483985988355-763728e1935b?w=400',\n        mediaType: 'image',\n        caption: 'Minimalist fashion for the modern woman 💫',\n        createdAt: new Date(Date.now() - 60 * 60 * 1000).toISOString(), // 1 hour ago\n        expiresAt: new Date(Date.now() + 18 * 60 * 60 * 1000).toISOString(),\n        views: 756,\n        isActive: true,\n        products: [\n          {\n            _id: 'prod-5',\n            name: 'Minimalist Blazer',\n            price: 5999,\n            image: 'https://images.unsplash.com/photo-1483985988355-763728e1935b?w=200'\n          }\n        ]\n      }\n    ];\n  }\n\n  getCurrentStory(): Story {\n    return this.stories[this.currentIndex] || this.stories[0];\n  }\n\n  getTimeAgo(dateString: string): string {\n    const now = new Date();\n    const date = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n    \n    if (diffInMinutes < 1) return 'now';\n    if (diffInMinutes < 60) return `${diffInMinutes}m`;\n    \n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `${diffInHours}h`;\n    \n    const diffInDays = Math.floor(diffInHours / 24);\n    return `${diffInDays}d`;\n  }\n\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  openStories(index: number = 0) {\n    this.currentIndex = index;\n    this.isOpen = true;\n    this.showStory(index);\n    document.body.style.overflow = 'hidden';\n\n    // Emit story click event\n    if (this.stories[index]) {\n      this.storyClick.emit({ story: this.stories[index], index });\n    }\n  }\n\n  closeStories() {\n    this.isOpen = false;\n    this.pauseAllVideos();\n    document.body.style.overflow = 'auto';\n    \n    // Add closing animation\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.classList.add('is-closed');\n    }\n    \n    setTimeout(() => {\n      if (this.storiesContainer) {\n        this.storiesContainer.nativeElement.classList.remove('is-closed');\n      }\n    }, 300);\n  }\n\n  showStory(index: number) {\n    this.currentIndex = index;\n    this.rotateY = 0;\n    \n    // Reset container transform\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = 'translateZ(-50vw)';\n    }\n  }\n\n  nextStory() {\n    if (this.currentIndex < this.stories.length - 1) {\n      this.targetRotateY = -90;\n      this.targetDirection = 'forward';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  previousStory() {\n    if (this.currentIndex > 0) {\n      this.targetRotateY = 90;\n      this.targetDirection = 'back';\n      this.isRotating = true;\n      this.update();\n    } else {\n      this.closeStories();\n    }\n  }\n\n  @HostListener('document:keydown', ['$event'])\n  handleKeydown(event: KeyboardEvent) {\n    if (!this.isOpen) return;\n\n    switch (event.key) {\n      case 'ArrowLeft':\n        this.previousStory();\n        break;\n      case 'ArrowRight':\n        this.nextStory();\n        break;\n      case 'Escape':\n        this.closeStories();\n        break;\n    }\n  }\n\n  onStoryClick(event: MouseEvent) {\n    if (this.isRotating) return;\n\n    const rect = (event.target as HTMLElement).getBoundingClientRect();\n    const clickX = event.clientX - rect.left;\n    const width = rect.width;\n\n    if (clickX < width / 2) {\n      this.previousStory();\n    } else {\n      this.nextStory();\n    }\n  }\n\n  onTouchStart(event: TouchEvent) {\n    this.isDragging = true;\n    this.dragStartX = event.touches[0].clientX;\n    this.dragCurrentX = this.dragStartX;\n  }\n\n  onTouchMove(event: TouchEvent) {\n    if (!this.isDragging) return;\n\n    this.dragCurrentX = event.touches[0].clientX;\n    const dragDistance = this.dragCurrentX - this.dragStartX;\n    const dragPercent = Math.abs(dragDistance) / window.innerWidth;\n\n    if (dragPercent > this.minDragPercentToTransition) {\n      if (dragDistance > 0) {\n        this.previousStory();\n      } else {\n        this.nextStory();\n      }\n      this.isDragging = false;\n    }\n  }\n\n  onTouchEnd(_event: TouchEvent) {\n    this.isDragging = false;\n  }\n\n  private setupEventListeners() {\n    // Add any additional event listeners here\n  }\n\n  private removeEventListeners() {\n    // Remove any additional event listeners here\n  }\n\n  private pauseAllVideos() {\n    const videos = document.querySelectorAll('video');\n    videos.forEach(video => {\n      video.pause();\n    });\n  }\n\n  private update() {\n    if (!this.isRotating) return;\n\n    const diff = this.targetRotateY - this.rotateY;\n    this.rotateY += diff * 0.1;\n\n    if (Math.abs(diff) < 0.1) {\n      this.rotateY = this.targetRotateY;\n      this.isRotating = false;\n\n      if (this.targetDirection === 'forward') {\n        this.currentIndex++;\n      } else if (this.targetDirection === 'back') {\n        this.currentIndex--;\n      }\n\n      this.targetRotateY = 0;\n      this.targetDirection = null;\n    }\n\n    if (this.storiesContainer) {\n      this.storiesContainer.nativeElement.style.transform = `translateZ(-50vw) rotateY(${this.rotateY}deg)`;\n    }\n\n    if (this.isRotating) {\n      requestAnimationFrame(() => this.update());\n    }\n  }\n\n  hasProducts(): boolean {\n    const story = this.getCurrentStory();\n    return !!(story?.products && story.products.length > 0);\n  }\n\n  getStoryProducts() {\n    return this.getCurrentStory().products || [];\n  }\n\n  formatPrice(price: number): string {\n    return `₹${(price / 100).toLocaleString('en-IN')}`;\n  }\n\n  viewProductDetails(product: any) {\n    console.log('Viewing product:', product);\n    // Navigate to product page or show product modal\n    this.router.navigate(['/products', product._id]);\n  }\n\n  getCurrentUserAvatar(): string {\n    // Use currentUser input if available, otherwise return default avatar\n    return this.currentUser?.avatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150';\n  }\n\n  openAddStoryModal() {\n    console.log('Opening add story modal');\n    // Navigate to add story page or open modal\n    this.router.navigate(['/add-story']);\n  }\n\n  buyNow() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0]; // Get first product for now\n      console.log('Buying product:', product);\n      // Navigate to checkout with product\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: product._id,\n          source: 'story'\n        }\n      });\n    }\n  }\n\n  // Direct product navigation\n  viewProduct(productId: string): void {\n    // Track product click analytics\n    this.trackProductClick(productId, 'view_product');\n\n    // Navigate to product detail page\n    this.router.navigate(['/shop/product', productId]);\n  }\n\n  viewCategory(categoryId: string): void {\n    // Navigate to category page\n    this.router.navigate(['/shop/category', categoryId]);\n  }\n\n  private trackProductClick(productId: string, action: string): void {\n    // Track analytics for product clicks from stories\n    console.log(`Story product ${action} tracked:`, productId);\n    // TODO: Implement analytics tracking API call\n  }\n\n  addToWishlist() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0];\n      console.log('Adding to wishlist:', product);\n\n      this.wishlistService.addToWishlist(product._id).subscribe({\n        next: (response) => {\n          if (response.success) {\n            alert('Product added to wishlist!');\n          } else {\n            alert('Failed to add product to wishlist');\n          }\n        },\n        error: (error) => {\n          console.error('Error adding to wishlist:', error);\n          alert('Error adding product to wishlist');\n        }\n      });\n    }\n  }\n\n  addToCart() {\n    const products = this.getStoryProducts();\n    if (products.length > 0) {\n      const product = products[0];\n      console.log('Adding to cart:', product);\n\n      this.cartService.addToCart(product._id, 1, undefined, undefined).subscribe({\n        next: (response) => {\n          if (response.success) {\n            alert('Product added to cart!');\n          } else {\n            alert('Failed to add product to cart');\n          }\n        },\n        error: (error) => {\n          console.error('Error adding to cart:', error);\n          alert('Error adding product to cart');\n        }\n      });\n    }\n  }\n\n  // Owl Carousel Event Handlers\n  onSlideChanged(event: any) {\n    // Handle slide change events\n    if (event && event.startPosition !== undefined) {\n      this.currentSlideIndex = event.startPosition;\n\n      // Log slide change for debugging\n      console.log(`Stories slide changed to: ${this.currentSlideIndex}`);\n\n      // Update any slide-specific logic here\n      this.updateSlideAnalytics();\n    }\n  }\n\n  onInitialized(_event: any) {\n    // Handle carousel initialization\n    this.isCarouselInitialized = true;\n    console.log('Stories carousel initialized successfully with auto-sliding enabled');\n  }\n\n  // Analytics for slide changes\n  private updateSlideAnalytics() {\n    // Track slide interactions for analytics\n    if (this.stories && this.stories[this.currentSlideIndex]) {\n      const currentStory = this.stories[this.currentSlideIndex];\n      console.log(`Viewing story from: ${currentStory.user.username}`);\n    }\n  }\n\n  // Method to toggle auto-play (can be called from template if needed)\n  toggleAutoPlay() {\n    this.isAutoPlaying = !this.isAutoPlaying;\n    // Note: Owl Carousel doesn't have a direct method to toggle autoplay\n    // This would require reinitializing the carousel with new options\n    console.log(`Auto-play ${this.isAutoPlaying ? 'enabled' : 'disabled'}`);\n  }\n}\n", "<!-- Instagram-style Stories Bar -->\n<div class=\"stories-container\">\n  <!-- Loading State -->\n  <div *ngIf=\"isLoadingStories\" class=\"stories-loading\">\n    <div *ngFor=\"let item of [1,2,3,4,5]\" class=\"story-skeleton\">\n      <div class=\"skeleton-avatar\"></div>\n      <div class=\"skeleton-name\"></div>\n    </div>\n  </div>\n\n  <!-- Stories Section -->\n  <div class=\"stories-section\" *ngIf=\"!isLoadingStories\">\n    <!-- Add Story Button (Static - Outside Slider) -->\n    <div class=\"add-story-static\">\n      <div class=\"story-item add-story-item\" (click)=\"openAddStoryModal()\">\n        <div class=\"story-avatar-container\">\n          <div class=\"add-story-avatar\">\n            <div class=\"add-story-icon\">\n              <i class=\"fas fa-plus\"></i>\n            </div>\n            <div class=\"current-user-avatar\" [style.background-image]=\"'url(' + getCurrentUserAvatar() + ')'\"></div>\n          </div>\n        </div>\n        <div class=\"story-username\">Add Story</div>\n      </div>\n    </div>\n\n    <!-- Stories Slider Container -->\n    <div class=\"stories-slider-wrapper\">\n      <!-- Owl Carousel Stories Slider -->\n      <div class=\"stories-slider-container\">\n        <owl-carousel-o\n          [options]=\"customOptions\"\n          (initialized)=\"onInitialized($event)\"\n          (changed)=\"onSlideChanged($event)\">\n\n          <!-- User Stories -->\n          <ng-container *ngFor=\"let story of stories; let i = index\">\n            <ng-template carouselSlide>\n              <div class=\"story-slide\" (click)=\"openStories(i)\">\n                <div class=\"story-avatar-container\">\n                  <div class=\"story-avatar\"\n                       [style.background-image]=\"'url(' + story.user.avatar + ')'\">\n                  </div>\n                  <div class=\"story-ring\"\n                       [class.viewed]=\"story.isViewed\">\n                  </div>\n                </div>\n                <div class=\"story-username\">{{ story.user.username }}</div>\n              </div>\n            </ng-template>\n          </ng-container>\n        </owl-carousel-o>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Stories Viewer Modal -->\n<div class=\"stories-wrapper\" [class.is-open]=\"isOpen\" *ngIf=\"isOpen\">\n  <div class=\"stories\" #storiesContainer>\n    \n    <!-- Story Progress Bars -->\n    <div class=\"story-progress\">\n      <div \n        *ngFor=\"let story of stories; let i = index\" \n        class=\"story-progress__bar\"\n        [class.active]=\"i === currentIndex\"\n        [class.completed]=\"i < currentIndex\">\n        <div class=\"story-progress__fill\"></div>\n      </div>\n    </div>\n\n    <!-- Current Story -->\n    <div class=\"story\" \n         [attr.data-story-id]=\"currentIndex\"\n         (click)=\"onStoryClick($event)\"\n         (touchstart)=\"onTouchStart($event)\"\n         (touchmove)=\"onTouchMove($event)\"\n         (touchend)=\"onTouchEnd($event)\">\n      \n      <!-- Story Header -->\n      <div class=\"story__top\">\n        <div class=\"story__details\">\n          <div class=\"story__avatar\" [style.background-image]=\"'url(' + getCurrentStory().user.avatar + ')'\"></div>\n          <div class=\"story__user\">{{ getCurrentStory().user.fullName }}</div>\n          <div class=\"story__time\">{{ getTimeAgo(getCurrentStory().createdAt) }}</div>\n          <div class=\"story__views\">{{ formatNumber(getCurrentStory().views) }} views</div>\n        </div>\n        <button class=\"story__close\" (click)=\"closeStories()\">\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n\n      <!-- Story Content -->\n      <div class=\"story__content\">\n        <!-- Video Story -->\n        <video\n          *ngIf=\"getCurrentStory().mediaType === 'video'\"\n          class=\"story__video\"\n          [src]=\"getCurrentStory().mediaUrl\"\n          autoplay\n          muted\n          loop\n          playsinline>\n        </video>\n\n        <!-- Image Story -->\n        <div\n          *ngIf=\"getCurrentStory().mediaType === 'image'\"\n          class=\"story__image\"\n          [style.background-image]=\"'url(' + getCurrentStory().mediaUrl + ')'\">\n        </div>\n\n        <!-- Story Caption -->\n        <div *ngIf=\"getCurrentStory().caption\" class=\"story__caption\">\n          {{ getCurrentStory().caption }}\n        </div>\n\n        <!-- Product Tags -->\n        <div *ngIf=\"hasProducts()\" class=\"story__product-tags\">\n          <div\n            *ngFor=\"let product of getStoryProducts()\"\n            class=\"product-tag\"\n            (click)=\"viewProduct(product._id)\">\n            <div class=\"product-tag-icon\">🛍️</div>\n            <div class=\"product-tag-info\">\n              <div class=\"product-tag-name\">{{ product.name }}</div>\n              <div class=\"product-tag-price\">{{ formatPrice(product.price) }}</div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Middle Point Navigation Button -->\n        <div class=\"middle-navigation\" *ngIf=\"hasProducts()\">\n          <button class=\"middle-nav-btn\" (click)=\"viewProduct(getStoryProducts()[0]._id)\">\n            <i class=\"fas fa-shopping-bag\"></i>\n            <span>Shop Now</span>\n          </button>\n        </div>\n      </div>\n\n      <!-- Story Bottom Actions -->\n      <div class=\"story__bottom\">\n        <div class=\"story__actions\">\n          <button class=\"story__action-btn like-btn\">\n            <i class=\"fas fa-heart\"></i>\n          </button>\n          <button class=\"story__action-btn comment-btn\">\n            <i class=\"fas fa-comment\"></i>\n          </button>\n          <button class=\"story__action-btn share-btn\">\n            <i class=\"fas fa-share\"></i>\n          </button>\n        </div>\n        \n        <!-- E-commerce Actions -->\n        <div class=\"story__ecommerce-actions\" *ngIf=\"hasProducts()\">\n          <button class=\"ecommerce-btn buy-now-btn\" (click)=\"buyNow()\">\n            <i class=\"fas fa-shopping-cart\"></i>\n            <span>Buy Now</span>\n          </button>\n          <button class=\"ecommerce-btn wishlist-btn\" (click)=\"addToWishlist()\">\n            <i class=\"fas fa-heart\"></i>\n            <span>Wishlist</span>\n          </button>\n          <button class=\"ecommerce-btn cart-btn\" (click)=\"addToCart()\">\n            <i class=\"fas fa-plus\"></i>\n            <span>Add to Cart</span>\n          </button>\n        </div>\n      </div>\n\n      <!-- Navigation Areas (Invisible) -->\n      <div class=\"story__nav-area story__nav-prev\"></div>\n      <div class=\"story__nav-area story__nav-next\"></div>\n    </div>\n  </div>\n\n  <!-- Feed Cover (Background) -->\n  <div class=\"feed__cover\" #feedCover [class.is-hidden]=\"isOpen\"></div>\n</div>\n\n<!-- Mobile-specific touch indicators -->\n<div class=\"touch-indicators\" *ngIf=\"isOpen\">\n  <div class=\"touch-indicator left\">\n    <i class=\"fas fa-chevron-left\"></i>\n    <span>Tap to go back</span>\n  </div>\n  <div class=\"touch-indicator right\">\n    <span>Tap to continue</span>\n    <i class=\"fas fa-chevron-right\"></i>\n  </div>\n</div>\n"], "mappings": "AAAA,SAA2FA,YAAY,QAAQ,eAAe;AAC9H,SAASC,YAAY,QAAQ,iBAAiB;AAM9C,SAASC,cAAc,QAAoB,oBAAoB;;;;;;;;;;;;;;ICH3DC,EAAA,CAAAC,cAAA,aAA6D;IAE3DD,EADA,CAAAE,SAAA,cAAmC,cACF;IACnCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,aAAsD;IACpDD,EAAA,CAAAI,UAAA,IAAAC,4CAAA,iBAA6D;IAI/DL,EAAA,CAAAG,YAAA,EAAM;;;IAJkBH,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAc;;;;;;IAmC1BT,EAAA,CAAAC,cAAA,cAAkD;IAAzBD,EAAA,CAAAU,UAAA,mBAAAC,0FAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,IAAA,GAAAd,EAAA,CAAAe,aAAA,GAAAC,KAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAE,WAAA,CAAAL,IAAA,CAAc;IAAA,EAAC;IAC/Cd,EAAA,CAAAC,cAAA,cAAoC;IAIlCD,EAHA,CAAAE,SAAA,cAEM,cAGA;IACRF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAoB,MAAA,GAAyB;IACvDpB,EADuD,CAAAG,YAAA,EAAM,EACvD;;;;IAPGH,EAAA,CAAAM,SAAA,GAA2D;IAA3DN,EAAA,CAAAqB,WAAA,8BAAAC,QAAA,CAAAC,IAAA,CAAAC,MAAA,OAA2D;IAG3DxB,EAAA,CAAAM,SAAA,EAA+B;IAA/BN,EAAA,CAAAyB,WAAA,WAAAH,QAAA,CAAAI,QAAA,CAA+B;IAGV1B,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAA2B,iBAAA,CAAAL,QAAA,CAAAC,IAAA,CAAAK,QAAA,CAAyB;;;;;IAX3D5B,EAAA,CAAA6B,uBAAA,GAA2D;IACzD7B,EAAA,CAAAI,UAAA,IAAA0B,oEAAA,0BAA2B;;;;;;;IAxBjC9B,EAHJ,CAAAC,cAAA,cAAuD,cAEvB,cACyC;IAA9BD,EAAA,CAAAU,UAAA,mBAAAqB,4DAAA;MAAA/B,EAAA,CAAAY,aAAA,CAAAoB,GAAA;MAAA,MAAAf,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAgB,iBAAA,EAAmB;IAAA,EAAC;IAG9DjC,EAFJ,CAAAC,cAAA,cAAoC,cACJ,cACA;IAC1BD,EAAA,CAAAE,SAAA,YAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,SAAA,cAAwG;IAE5GF,EADE,CAAAG,YAAA,EAAM,EACF;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAoB,MAAA,gBAAS;IAEzCpB,EAFyC,CAAAG,YAAA,EAAM,EACvC,EACF;IAMFH,EAHJ,CAAAC,cAAA,eAAoC,eAEI,0BAIC;IAAnCD,EADA,CAAAU,UAAA,yBAAAwB,8EAAAC,MAAA;MAAAnC,EAAA,CAAAY,aAAA,CAAAoB,GAAA;MAAA,MAAAf,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAAeD,MAAA,CAAAmB,aAAA,CAAAD,MAAA,CAAqB;IAAA,EAAC,qBAAAE,0EAAAF,MAAA;MAAAnC,EAAA,CAAAY,aAAA,CAAAoB,GAAA;MAAA,MAAAf,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAC1BD,MAAA,CAAAqB,cAAA,CAAAH,MAAA,CAAsB;IAAA,EAAC;IAGlCnC,EAAA,CAAAI,UAAA,KAAAmC,sDAAA,2BAA2D;IAkBnEvC,EAHM,CAAAG,YAAA,EAAiB,EACb,EACF,EACF;;;;IAnCqCH,EAAA,CAAAM,SAAA,GAAgE;IAAhEN,EAAA,CAAAqB,WAAA,8BAAAJ,MAAA,CAAAuB,oBAAA,SAAgE;IAYnGxC,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAO,UAAA,YAAAU,MAAA,CAAAwB,aAAA,CAAyB;IAKOzC,EAAA,CAAAM,SAAA,EAAY;IAAZN,EAAA,CAAAO,UAAA,YAAAU,MAAA,CAAAyB,OAAA,CAAY;;;;;IA2BhD1C,EAAA,CAAAC,cAAA,cAIuC;IACrCD,EAAA,CAAAE,SAAA,cAAwC;IAC1CF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFJH,EADA,CAAAyB,WAAA,WAAAkB,IAAA,KAAA1B,MAAA,CAAA2B,YAAA,CAAmC,cAAAD,IAAA,GAAA1B,MAAA,CAAA2B,YAAA,CACC;;;;;IA6BpC5C,EAAA,CAAAE,SAAA,gBAQQ;;;;IALNF,EAAA,CAAAO,UAAA,QAAAU,MAAA,CAAA4B,eAAA,GAAAC,QAAA,EAAA9C,EAAA,CAAA+C,aAAA,CAAkC;;;;;IAQpC/C,EAAA,CAAAE,SAAA,cAIM;;;;IADJF,EAAA,CAAAqB,WAAA,8BAAAJ,MAAA,CAAA4B,eAAA,GAAAC,QAAA,OAAoE;;;;;IAItE9C,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAoB,MAAA,GACF;IAAApB,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAgD,kBAAA,MAAA/B,MAAA,CAAA4B,eAAA,GAAAI,OAAA,MACF;;;;;;IAIEjD,EAAA,CAAAC,cAAA,cAGqC;IAAnCD,EAAA,CAAAU,UAAA,mBAAAwC,yEAAA;MAAA,MAAAC,UAAA,GAAAnD,EAAA,CAAAY,aAAA,CAAAwC,GAAA,EAAAC,SAAA;MAAA,MAAApC,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAqC,WAAA,CAAAH,UAAA,CAAAI,GAAA,CAAwB;IAAA,EAAC;IAClCvD,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAoB,MAAA,yBAAG;IAAApB,EAAA,CAAAG,YAAA,EAAM;IAErCH,EADF,CAAAC,cAAA,cAA8B,cACE;IAAAD,EAAA,CAAAoB,MAAA,GAAkB;IAAApB,EAAA,CAAAG,YAAA,EAAM;IACtDH,EAAA,CAAAC,cAAA,cAA+B;IAAAD,EAAA,CAAAoB,MAAA,GAAgC;IAEnEpB,EAFmE,CAAAG,YAAA,EAAM,EACjE,EACF;;;;;IAH4BH,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAA2B,iBAAA,CAAAwB,UAAA,CAAAK,IAAA,CAAkB;IACjBxD,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAA2B,iBAAA,CAAAV,MAAA,CAAAwC,WAAA,CAAAN,UAAA,CAAAO,KAAA,EAAgC;;;;;IARrE1D,EAAA,CAAAC,cAAA,cAAuD;IACrDD,EAAA,CAAAI,UAAA,IAAAuD,mDAAA,kBAGqC;IAOvC3D,EAAA,CAAAG,YAAA,EAAM;;;;IATkBH,EAAA,CAAAM,SAAA,EAAqB;IAArBN,EAAA,CAAAO,UAAA,YAAAU,MAAA,CAAA2C,gBAAA,GAAqB;;;;;;IAa3C5D,EADF,CAAAC,cAAA,cAAqD,iBAC6B;IAAjDD,EAAA,CAAAU,UAAA,mBAAAmD,sEAAA;MAAA7D,EAAA,CAAAY,aAAA,CAAAkD,IAAA;MAAA,MAAA7C,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAqC,WAAA,CAAYrC,MAAA,CAAA2C,gBAAA,EAAkB,CAAC,CAAC,EAAAL,GAAA,CAAM;IAAA,EAAC;IAC7EvD,EAAA,CAAAE,SAAA,YAAmC;IACnCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAoB,MAAA,eAAQ;IAElBpB,EAFkB,CAAAG,YAAA,EAAO,EACd,EACL;;;;;;IAmBJH,EADF,CAAAC,cAAA,cAA4D,iBACG;IAAnBD,EAAA,CAAAU,UAAA,mBAAAqD,sEAAA;MAAA/D,EAAA,CAAAY,aAAA,CAAAoD,IAAA;MAAA,MAAA/C,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAgD,MAAA,EAAQ;IAAA,EAAC;IAC1DjE,EAAA,CAAAE,SAAA,YAAoC;IACpCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAoB,MAAA,cAAO;IACfpB,EADe,CAAAG,YAAA,EAAO,EACb;IACTH,EAAA,CAAAC,cAAA,iBAAqE;IAA1BD,EAAA,CAAAU,UAAA,mBAAAwD,sEAAA;MAAAlE,EAAA,CAAAY,aAAA,CAAAoD,IAAA;MAAA,MAAA/C,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAkD,aAAA,EAAe;IAAA,EAAC;IAClEnE,EAAA,CAAAE,SAAA,YAA4B;IAC5BF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAoB,MAAA,eAAQ;IAChBpB,EADgB,CAAAG,YAAA,EAAO,EACd;IACTH,EAAA,CAAAC,cAAA,iBAA6D;IAAtBD,EAAA,CAAAU,UAAA,mBAAA0D,sEAAA;MAAApE,EAAA,CAAAY,aAAA,CAAAoD,IAAA;MAAA,MAAA/C,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAoD,SAAA,EAAW;IAAA,EAAC;IAC1DrE,EAAA,CAAAE,SAAA,aAA2B;IAC3BF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAoB,MAAA,mBAAW;IAErBpB,EAFqB,CAAAG,YAAA,EAAO,EACjB,EACL;;;;;;IA3GVH,EAJJ,CAAAC,cAAA,cAAqE,iBAC5B,cAGT;IAC1BD,EAAA,CAAAI,UAAA,IAAAkE,4CAAA,kBAIuC;IAGzCtE,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAKqC;IAAhCD,EAHA,CAAAU,UAAA,mBAAA6D,4DAAApC,MAAA;MAAAnC,EAAA,CAAAY,aAAA,CAAA4D,GAAA;MAAA,MAAAvD,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAwD,YAAA,CAAAtC,MAAA,CAAoB;IAAA,EAAC,wBAAAuC,iEAAAvC,MAAA;MAAAnC,EAAA,CAAAY,aAAA,CAAA4D,GAAA;MAAA,MAAAvD,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAChBD,MAAA,CAAA0D,YAAA,CAAAxC,MAAA,CAAoB;IAAA,EAAC,uBAAAyC,gEAAAzC,MAAA;MAAAnC,EAAA,CAAAY,aAAA,CAAA4D,GAAA;MAAA,MAAAvD,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CACtBD,MAAA,CAAA4D,WAAA,CAAA1C,MAAA,CAAmB;IAAA,EAAC,sBAAA2C,+DAAA3C,MAAA;MAAAnC,EAAA,CAAAY,aAAA,CAAA4D,GAAA;MAAA,MAAAvD,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CACrBD,MAAA,CAAA8D,UAAA,CAAA5C,MAAA,CAAkB;IAAA,EAAC;IAIhCnC,EADF,CAAAC,cAAA,cAAwB,cACM;IAC1BD,EAAA,CAAAE,SAAA,cAAyG;IACzGF,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAoB,MAAA,IAAqC;IAAApB,EAAA,CAAAG,YAAA,EAAM;IACpEH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAoB,MAAA,IAA6C;IAAApB,EAAA,CAAAG,YAAA,EAAM;IAC5EH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAoB,MAAA,IAAiD;IAC7EpB,EAD6E,CAAAG,YAAA,EAAM,EAC7E;IACNH,EAAA,CAAAC,cAAA,kBAAsD;IAAzBD,EAAA,CAAAU,UAAA,mBAAAsE,gEAAA;MAAAhF,EAAA,CAAAY,aAAA,CAAA4D,GAAA;MAAA,MAAAvD,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAASD,MAAA,CAAAgE,YAAA,EAAc;IAAA,EAAC;IACnDjF,EAAA,CAAAE,SAAA,aAA4B;IAEhCF,EADE,CAAAG,YAAA,EAAS,EACL;IAGNH,EAAA,CAAAC,cAAA,eAA4B;IAuC1BD,EArCA,CAAAI,UAAA,KAAA8E,+CAAA,oBAOc,KAAAC,6CAAA,kBAOyD,KAAAC,6CAAA,kBAIT,KAAAC,6CAAA,kBAKP,KAAAC,6CAAA,kBAcF;IAMvDtF,EAAA,CAAAG,YAAA,EAAM;IAKFH,EAFJ,CAAAC,cAAA,eAA2B,eACG,kBACiB;IACzCD,EAAA,CAAAE,SAAA,aAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA8C;IAC5CD,EAAA,CAAAE,SAAA,aAA8B;IAChCF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA4C;IAC1CD,EAAA,CAAAE,SAAA,aAA4B;IAEhCF,EADE,CAAAG,YAAA,EAAS,EACL;IAGNH,EAAA,CAAAI,UAAA,KAAAmF,6CAAA,mBAA4D;IAc9DvF,EAAA,CAAAG,YAAA,EAAM;IAINH,EADA,CAAAE,SAAA,eAAmD,eACA;IAEvDF,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAE,SAAA,kBAAqE;IACvEF,EAAA,CAAAG,YAAA,EAAM;;;;IA1HuBH,EAAA,CAAAyB,WAAA,YAAAR,MAAA,CAAAuE,MAAA,CAAwB;IAM3BxF,EAAA,CAAAM,SAAA,GAAY;IAAZN,EAAA,CAAAO,UAAA,YAAAU,MAAA,CAAAyB,OAAA,CAAY;IAU7B1C,EAAA,CAAAM,SAAA,EAAmC;;IASPN,EAAA,CAAAM,SAAA,GAAuE;IAAvEN,EAAA,CAAAqB,WAAA,8BAAAJ,MAAA,CAAA4B,eAAA,GAAAtB,IAAA,CAAAC,MAAA,OAAuE;IACzExB,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAA2B,iBAAA,CAAAV,MAAA,CAAA4B,eAAA,GAAAtB,IAAA,CAAAkE,QAAA,CAAqC;IACrCzF,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAA2B,iBAAA,CAAAV,MAAA,CAAAyE,UAAA,CAAAzE,MAAA,CAAA4B,eAAA,GAAA8C,SAAA,EAA6C;IAC5C3F,EAAA,CAAAM,SAAA,GAAiD;IAAjDN,EAAA,CAAAgD,kBAAA,KAAA/B,MAAA,CAAA2E,YAAA,CAAA3E,MAAA,CAAA4B,eAAA,GAAAgD,KAAA,YAAiD;IAW1E7F,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAAO,UAAA,SAAAU,MAAA,CAAA4B,eAAA,GAAAiD,SAAA,aAA6C;IAW7C9F,EAAA,CAAAM,SAAA,EAA6C;IAA7CN,EAAA,CAAAO,UAAA,SAAAU,MAAA,CAAA4B,eAAA,GAAAiD,SAAA,aAA6C;IAM1C9F,EAAA,CAAAM,SAAA,EAA+B;IAA/BN,EAAA,CAAAO,UAAA,SAAAU,MAAA,CAAA4B,eAAA,GAAAI,OAAA,CAA+B;IAK/BjD,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAO,UAAA,SAAAU,MAAA,CAAA8E,WAAA,GAAmB;IAcO/F,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAO,UAAA,SAAAU,MAAA,CAAA8E,WAAA,GAAmB;IAuBZ/F,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAO,UAAA,SAAAU,MAAA,CAAA8E,WAAA,GAAmB;IAuB5B/F,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAyB,WAAA,cAAAR,MAAA,CAAAuE,MAAA,CAA0B;;;;;IAK9DxF,EADF,CAAAC,cAAA,cAA6C,cACT;IAChCD,EAAA,CAAAE,SAAA,YAAmC;IACnCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAoB,MAAA,qBAAc;IACtBpB,EADsB,CAAAG,YAAA,EAAO,EACvB;IAEJH,EADF,CAAAC,cAAA,cAAmC,WAC3B;IAAAD,EAAA,CAAAoB,MAAA,sBAAe;IAAApB,EAAA,CAAAG,YAAA,EAAO;IAC5BH,EAAA,CAAAE,SAAA,YAAoC;IAExCF,EADE,CAAAG,YAAA,EAAM,EACF;;;ADjJN,OAAM,MAAO6F,uBAAuB;EAsElCC,YACUC,MAAc,EACdC,IAAgB,EAChBC,WAAwB,EACxBC,eAAgC;IAHhC,KAAAH,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IArEhB,KAAA3D,OAAO,GAAY,EAAE;IACrB,KAAA4D,YAAY,GAAY,IAAI;IAC5B,KAAAC,WAAW,GAAuB,IAAI;IACrC,KAAAC,UAAU,GAAG,IAAI3G,YAAY,EAAmC;IAE1E,KAAA4G,gBAAgB,GAAG,IAAI;IAEvB,KAAA7D,YAAY,GAAG,CAAC;IAChB,KAAA4C,MAAM,GAAG,KAAK;IACd,KAAAkB,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,OAAO,GAAG,CAAC;IACX,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,eAAe,GAA8B,IAAI;IAEjD;IACA,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,0BAA0B,GAAG,GAAG;IAChC,KAAAC,uBAAuB,GAAG,IAAI;IAC9B,KAAAC,eAAe,GAAG,CAAC;IAEnB;IACA,KAAAC,qBAAqB,GAAG,KAAK;IAC7B,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,iBAAiB,GAAG,CAAC;IAErB;IACA,KAAA7E,aAAa,GAAe;MAC1B8E,IAAI,EAAE,IAAI;MACVC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,KAAK;MACfC,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE,CAAC,qCAAqC,EAAE,sCAAsC,CAAC;MACxFC,UAAU,EAAE;QACV,CAAC,EAAE;UACDC,KAAK,EAAE,CAAC;UACRC,GAAG,EAAE;SACN;QACD,GAAG,EAAE;UACHD,KAAK,EAAE,CAAC;UACRC,GAAG,EAAE;SACN;QACD,GAAG,EAAE;UACHD,KAAK,EAAE,CAAC;UACRC,GAAG,EAAE;SACN;QACD,GAAG,EAAE;UACHD,KAAK,EAAE,CAAC;UACRC,GAAG,EAAE;;OAER;MACDA,GAAG,EAAE,IAAI;MACTC,MAAM,EAAE,CAAC;MACTC,YAAY,EAAE,CAAC;MACfC,QAAQ,EAAE,IAAI;MACdC,eAAe,EAAE,IAAI;MACrBC,kBAAkB,EAAE,IAAI;MACxBC,aAAa,EAAE,IAAI,CAAC;KACrB;IAEO,KAAAC,aAAa,GAAmB,EAAE;EAOvC;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAAC,IAAI,CAAC9F,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC+F,MAAM,KAAK,CAAC,EAAE;MAC9C,IAAI,CAACC,WAAW,EAAE;KACnB,MAAM;MACL,IAAI,CAACjC,gBAAgB,GAAG,KAAK;;IAE/B,IAAI,CAACkC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACL,aAAa,CAACM,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAN,WAAWA,CAAA;IACT,IAAI,CAACjC,gBAAgB,GAAG,IAAI;IAE5B;IACA,IAAI,CAAC8B,aAAa,CAACU,IAAI,CACrB,IAAI,CAAC9C,IAAI,CAAC+C,GAAG,CAAM,6CAA6C,CAAC,CAACC,SAAS,CAAC;MAC1EC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACd,MAAM,GAAG,CAAC,EAAE;UACjE,IAAI,CAAC/F,OAAO,GAAG2G,QAAQ,CAACE,IAAI;SAC7B,MAAM;UACL,IAAI,CAACC,mBAAmB,EAAE;;QAE5B,IAAI,CAAC/C,gBAAgB,GAAG,KAAK;MAC/B,CAAC;MACDgD,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACD,mBAAmB,EAAE;QAC1B,IAAI,CAAC/C,gBAAgB,GAAG,KAAK;MAC/B;KACD,CAAC,CACH;EACH;EAEA+C,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAAC9G,OAAO,GAAG,CACb;MACEa,GAAG,EAAE,SAAS;MACdhC,IAAI,EAAE;QACJgC,GAAG,EAAE,QAAQ;QACb3B,QAAQ,EAAE,qBAAqB;QAC/B6D,QAAQ,EAAE,WAAW;QACrBjE,MAAM,EAAE;OACT;MACDsB,QAAQ,EAAE,oEAAoE;MAC9EgD,SAAS,EAAE,OAAO;MAClB7C,OAAO,EAAE,wCAAwC;MACjD0C,SAAS,EAAE,IAAIgE,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAC7DC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEhE,KAAK,EAAE,IAAI;MACXkE,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,CACR;QACEzG,GAAG,EAAE,QAAQ;QACbC,IAAI,EAAE,2BAA2B;QACjCE,KAAK,EAAE,IAAI;QACXuG,KAAK,EAAE;OACR;KAEJ,EACD;MACE1G,GAAG,EAAE,SAAS;MACdhC,IAAI,EAAE;QACJgC,GAAG,EAAE,QAAQ;QACb3B,QAAQ,EAAE,iBAAiB;QAC3B6D,QAAQ,EAAE,gBAAgB;QAC1BjE,MAAM,EAAE;OACT;MACDsB,QAAQ,EAAE,oEAAoE;MAC9EgD,SAAS,EAAE,OAAO;MAClB7C,OAAO,EAAE,gDAAgD;MACzD0C,SAAS,EAAE,IAAIgE,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAC9DC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACtEhE,KAAK,EAAE,GAAG;MACVkE,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,CACR;QACEzG,GAAG,EAAE,QAAQ;QACbC,IAAI,EAAE,yBAAyB;QAC/BE,KAAK,EAAE,IAAI;QACXuG,KAAK,EAAE;OACR;KAEJ,EACD;MACE1G,GAAG,EAAE,SAAS;MACdhC,IAAI,EAAE;QACJgC,GAAG,EAAE,QAAQ;QACb3B,QAAQ,EAAE,cAAc;QACxB6D,QAAQ,EAAE,eAAe;QACzBjE,MAAM,EAAE;OACT;MACDsB,QAAQ,EAAE,oEAAoE;MAC9EgD,SAAS,EAAE,OAAO;MAClB7C,OAAO,EAAE,6CAA6C;MACtD0C,SAAS,EAAE,IAAIgE,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAC9DC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACrEhE,KAAK,EAAE,IAAI;MACXkE,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,CACR;QACEzG,GAAG,EAAE,QAAQ;QACbC,IAAI,EAAE,qBAAqB;QAC3BE,KAAK,EAAE,IAAI;QACXuG,KAAK,EAAE;OACR;KAEJ,EACD;MACE1G,GAAG,EAAE,SAAS;MACdhC,IAAI,EAAE;QACJgC,GAAG,EAAE,QAAQ;QACb3B,QAAQ,EAAE,sBAAsB;QAChC6D,QAAQ,EAAE,eAAe;QACzBjE,MAAM,EAAE;OACT;MACDsB,QAAQ,EAAE,oEAAoE;MAC9EgD,SAAS,EAAE,OAAO;MAClB7C,OAAO,EAAE,oCAAoC;MAC7C0C,SAAS,EAAE,IAAIgE,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAC9DC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACtEhE,KAAK,EAAE,IAAI;MACXkE,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,CACR;QACEzG,GAAG,EAAE,QAAQ;QACbC,IAAI,EAAE,kBAAkB;QACxBE,KAAK,EAAE,KAAK;QACZuG,KAAK,EAAE;OACR;KAEJ,EACD;MACE1G,GAAG,EAAE,SAAS;MACdhC,IAAI,EAAE;QACJgC,GAAG,EAAE,QAAQ;QACb3B,QAAQ,EAAE,WAAW;QACrB6D,QAAQ,EAAE,aAAa;QACvBjE,MAAM,EAAE;OACT;MACDsB,QAAQ,EAAE,oEAAoE;MAC9EgD,SAAS,EAAE,OAAO;MAClB7C,OAAO,EAAE,4CAA4C;MACrD0C,SAAS,EAAE,IAAIgE,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MAC9DC,SAAS,EAAE,IAAIH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,EAAE;MACnEhE,KAAK,EAAE,GAAG;MACVkE,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,CACR;QACEzG,GAAG,EAAE,QAAQ;QACbC,IAAI,EAAE,mBAAmB;QACzBE,KAAK,EAAE,IAAI;QACXuG,KAAK,EAAE;OACR;KAEJ,CACF;EACH;EAEApH,eAAeA,CAAA;IACb,OAAO,IAAI,CAACH,OAAO,CAAC,IAAI,CAACE,YAAY,CAAC,IAAI,IAAI,CAACF,OAAO,CAAC,CAAC,CAAC;EAC3D;EAEAgD,UAAUA,CAACwE,UAAkB;IAC3B,MAAMN,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAMQ,IAAI,GAAG,IAAIR,IAAI,CAACO,UAAU,CAAC;IACjC,MAAME,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACV,GAAG,CAACW,OAAO,EAAE,GAAGJ,IAAI,CAACI,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEhF,IAAIH,aAAa,GAAG,CAAC,EAAE,OAAO,KAAK;IACnC,IAAIA,aAAa,GAAG,EAAE,EAAE,OAAO,GAAGA,aAAa,GAAG;IAElD,MAAMI,WAAW,GAAGH,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC;IAClD,IAAII,WAAW,GAAG,EAAE,EAAE,OAAO,GAAGA,WAAW,GAAG;IAE9C,MAAMC,UAAU,GAAGJ,IAAI,CAACC,KAAK,CAACE,WAAW,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAGC,UAAU,GAAG;EACzB;EAEA7E,YAAYA,CAAC8E,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EAEAzJ,WAAWA,CAACH,KAAA,GAAgB,CAAC;IAC3B,IAAI,CAAC4B,YAAY,GAAG5B,KAAK;IACzB,IAAI,CAACwE,MAAM,GAAG,IAAI;IAClB,IAAI,CAACqF,SAAS,CAAC7J,KAAK,CAAC;IACrB8J,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IAEvC;IACA,IAAI,IAAI,CAACvI,OAAO,CAAC1B,KAAK,CAAC,EAAE;MACvB,IAAI,CAACwF,UAAU,CAAC0E,IAAI,CAAC;QAAEC,KAAK,EAAE,IAAI,CAACzI,OAAO,CAAC1B,KAAK,CAAC;QAAEA;MAAK,CAAE,CAAC;;EAE/D;EAEAiE,YAAYA,CAAA;IACV,IAAI,CAACO,MAAM,GAAG,KAAK;IACnB,IAAI,CAAC4F,cAAc,EAAE;IACrBN,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;IAErC;IACA,IAAI,IAAI,CAACI,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;;IAGhEC,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACJ,gBAAgB,EAAE;QACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACC,SAAS,CAACG,MAAM,CAAC,WAAW,CAAC;;IAErE,CAAC,EAAE,GAAG,CAAC;EACT;EAEAb,SAASA,CAAC7J,KAAa;IACrB,IAAI,CAAC4B,YAAY,GAAG5B,KAAK;IACzB,IAAI,CAAC4F,OAAO,GAAG,CAAC;IAEhB;IACA,IAAI,IAAI,CAACyE,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACN,KAAK,CAACW,SAAS,GAAG,mBAAmB;;EAE7E;EAEAC,SAASA,CAAA;IACP,IAAI,IAAI,CAAChJ,YAAY,GAAG,IAAI,CAACF,OAAO,CAAC+F,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAAC5B,aAAa,GAAG,CAAC,EAAE;MACxB,IAAI,CAACC,eAAe,GAAG,SAAS;MAChC,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACmF,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAAC5G,YAAY,EAAE;;EAEvB;EAEA6G,aAAaA,CAAA;IACX,IAAI,IAAI,CAAClJ,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACiE,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,eAAe,GAAG,MAAM;MAC7B,IAAI,CAACJ,UAAU,GAAG,IAAI;MACtB,IAAI,CAACmF,MAAM,EAAE;KACd,MAAM;MACL,IAAI,CAAC5G,YAAY,EAAE;;EAEvB;EAGA8G,aAAaA,CAACC,KAAoB;IAChC,IAAI,CAAC,IAAI,CAACxG,MAAM,EAAE;IAElB,QAAQwG,KAAK,CAACC,GAAG;MACf,KAAK,WAAW;QACd,IAAI,CAACH,aAAa,EAAE;QACpB;MACF,KAAK,YAAY;QACf,IAAI,CAACF,SAAS,EAAE;QAChB;MACF,KAAK,QAAQ;QACX,IAAI,CAAC3G,YAAY,EAAE;QACnB;;EAEN;EAEAR,YAAYA,CAACuH,KAAiB;IAC5B,IAAI,IAAI,CAACtF,UAAU,EAAE;IAErB,MAAMwF,IAAI,GAAIF,KAAK,CAACG,MAAsB,CAACC,qBAAqB,EAAE;IAClE,MAAMC,MAAM,GAAGL,KAAK,CAACM,OAAO,GAAGJ,IAAI,CAACK,IAAI;IACxC,MAAMC,KAAK,GAAGN,IAAI,CAACM,KAAK;IAExB,IAAIH,MAAM,GAAGG,KAAK,GAAG,CAAC,EAAE;MACtB,IAAI,CAACV,aAAa,EAAE;KACrB,MAAM;MACL,IAAI,CAACF,SAAS,EAAE;;EAEpB;EAEAjH,YAAYA,CAACqH,KAAiB;IAC5B,IAAI,CAACrF,UAAU,GAAG,IAAI;IACtB,IAAI,CAACI,UAAU,GAAGiF,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAACH,OAAO;IAC1C,IAAI,CAACtF,YAAY,GAAG,IAAI,CAACD,UAAU;EACrC;EAEAlC,WAAWA,CAACmH,KAAiB;IAC3B,IAAI,CAAC,IAAI,CAACrF,UAAU,EAAE;IAEtB,IAAI,CAACK,YAAY,GAAGgF,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAACH,OAAO;IAC5C,MAAMI,YAAY,GAAG,IAAI,CAAC1F,YAAY,GAAG,IAAI,CAACD,UAAU;IACxD,MAAM4F,WAAW,GAAGtC,IAAI,CAACuC,GAAG,CAACF,YAAY,CAAC,GAAGG,MAAM,CAACC,UAAU;IAE9D,IAAIH,WAAW,GAAG,IAAI,CAAC1F,0BAA0B,EAAE;MACjD,IAAIyF,YAAY,GAAG,CAAC,EAAE;QACpB,IAAI,CAACZ,aAAa,EAAE;OACrB,MAAM;QACL,IAAI,CAACF,SAAS,EAAE;;MAElB,IAAI,CAACjF,UAAU,GAAG,KAAK;;EAE3B;EAEA5B,UAAUA,CAACgI,MAAkB;IAC3B,IAAI,CAACpG,UAAU,GAAG,KAAK;EACzB;EAEQgC,mBAAmBA,CAAA;IACzB;EAAA;EAGMK,oBAAoBA,CAAA;IAC1B;EAAA;EAGMoC,cAAcA,CAAA;IACpB,MAAM4B,MAAM,GAAGlC,QAAQ,CAACmC,gBAAgB,CAAC,OAAO,CAAC;IACjDD,MAAM,CAACnE,OAAO,CAACqE,KAAK,IAAG;MACrBA,KAAK,CAACC,KAAK,EAAE;IACf,CAAC,CAAC;EACJ;EAEQtB,MAAMA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACnF,UAAU,EAAE;IAEtB,MAAM0G,IAAI,GAAG,IAAI,CAACvG,aAAa,GAAG,IAAI,CAACD,OAAO;IAC9C,IAAI,CAACA,OAAO,IAAIwG,IAAI,GAAG,GAAG;IAE1B,IAAI/C,IAAI,CAACuC,GAAG,CAACQ,IAAI,CAAC,GAAG,GAAG,EAAE;MACxB,IAAI,CAACxG,OAAO,GAAG,IAAI,CAACC,aAAa;MACjC,IAAI,CAACH,UAAU,GAAG,KAAK;MAEvB,IAAI,IAAI,CAACI,eAAe,KAAK,SAAS,EAAE;QACtC,IAAI,CAAClE,YAAY,EAAE;OACpB,MAAM,IAAI,IAAI,CAACkE,eAAe,KAAK,MAAM,EAAE;QAC1C,IAAI,CAAClE,YAAY,EAAE;;MAGrB,IAAI,CAACiE,aAAa,GAAG,CAAC;MACtB,IAAI,CAACC,eAAe,GAAG,IAAI;;IAG7B,IAAI,IAAI,CAACuE,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACC,aAAa,CAACN,KAAK,CAACW,SAAS,GAAG,6BAA6B,IAAI,CAAC/E,OAAO,MAAM;;IAGvG,IAAI,IAAI,CAACF,UAAU,EAAE;MACnB2G,qBAAqB,CAAC,MAAM,IAAI,CAACxB,MAAM,EAAE,CAAC;;EAE9C;EAEA9F,WAAWA,CAAA;IACT,MAAMoF,KAAK,GAAG,IAAI,CAACtI,eAAe,EAAE;IACpC,OAAO,CAAC,EAAEsI,KAAK,EAAEnB,QAAQ,IAAImB,KAAK,CAACnB,QAAQ,CAACvB,MAAM,GAAG,CAAC,CAAC;EACzD;EAEA7E,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACf,eAAe,EAAE,CAACmH,QAAQ,IAAI,EAAE;EAC9C;EAEAvG,WAAWA,CAACC,KAAa;IACvB,OAAO,IAAI,CAACA,KAAK,GAAG,GAAG,EAAE4J,cAAc,CAAC,OAAO,CAAC,EAAE;EACpD;EAEAC,kBAAkBA,CAACC,OAAY;IAC7B9D,OAAO,CAAC+D,GAAG,CAAC,kBAAkB,EAAED,OAAO,CAAC;IACxC;IACA,IAAI,CAACtH,MAAM,CAACwH,QAAQ,CAAC,CAAC,WAAW,EAAEF,OAAO,CAACjK,GAAG,CAAC,CAAC;EAClD;EAEAf,oBAAoBA,CAAA;IAClB;IACA,OAAO,IAAI,CAAC+D,WAAW,EAAE/E,MAAM,IAAI,oEAAoE;EACzG;EAEAS,iBAAiBA,CAAA;IACfyH,OAAO,CAAC+D,GAAG,CAAC,yBAAyB,CAAC;IACtC;IACA,IAAI,CAACvH,MAAM,CAACwH,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;EACtC;EAEAzJ,MAAMA,CAAA;IACJ,MAAM+F,QAAQ,GAAG,IAAI,CAACpG,gBAAgB,EAAE;IACxC,IAAIoG,QAAQ,CAACvB,MAAM,GAAG,CAAC,EAAE;MACvB,MAAM+E,OAAO,GAAGxD,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7BN,OAAO,CAAC+D,GAAG,CAAC,iBAAiB,EAAED,OAAO,CAAC;MACvC;MACA,IAAI,CAACtH,MAAM,CAACwH,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;QAClCC,WAAW,EAAE;UACXC,SAAS,EAAEJ,OAAO,CAACjK,GAAG;UACtBsK,MAAM,EAAE;;OAEX,CAAC;;EAEN;EAEA;EACAvK,WAAWA,CAACsK,SAAiB;IAC3B;IACA,IAAI,CAACE,iBAAiB,CAACF,SAAS,EAAE,cAAc,CAAC;IAEjD;IACA,IAAI,CAAC1H,MAAM,CAACwH,QAAQ,CAAC,CAAC,eAAe,EAAEE,SAAS,CAAC,CAAC;EACpD;EAEAG,YAAYA,CAACC,UAAkB;IAC7B;IACA,IAAI,CAAC9H,MAAM,CAACwH,QAAQ,CAAC,CAAC,gBAAgB,EAAEM,UAAU,CAAC,CAAC;EACtD;EAEQF,iBAAiBA,CAACF,SAAiB,EAAEK,MAAc;IACzD;IACAvE,OAAO,CAAC+D,GAAG,CAAC,iBAAiBQ,MAAM,WAAW,EAAEL,SAAS,CAAC;IAC1D;EACF;EAEAzJ,aAAaA,CAAA;IACX,MAAM6F,QAAQ,GAAG,IAAI,CAACpG,gBAAgB,EAAE;IACxC,IAAIoG,QAAQ,CAACvB,MAAM,GAAG,CAAC,EAAE;MACvB,MAAM+E,OAAO,GAAGxD,QAAQ,CAAC,CAAC,CAAC;MAC3BN,OAAO,CAAC+D,GAAG,CAAC,qBAAqB,EAAED,OAAO,CAAC;MAE3C,IAAI,CAACnH,eAAe,CAAClC,aAAa,CAACqJ,OAAO,CAACjK,GAAG,CAAC,CAAC4F,SAAS,CAAC;QACxDC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpB4E,KAAK,CAAC,4BAA4B,CAAC;WACpC,MAAM;YACLA,KAAK,CAAC,mCAAmC,CAAC;;QAE9C,CAAC;QACDzE,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjDyE,KAAK,CAAC,kCAAkC,CAAC;QAC3C;OACD,CAAC;;EAEN;EAEA7J,SAASA,CAAA;IACP,MAAM2F,QAAQ,GAAG,IAAI,CAACpG,gBAAgB,EAAE;IACxC,IAAIoG,QAAQ,CAACvB,MAAM,GAAG,CAAC,EAAE;MACvB,MAAM+E,OAAO,GAAGxD,QAAQ,CAAC,CAAC,CAAC;MAC3BN,OAAO,CAAC+D,GAAG,CAAC,iBAAiB,EAAED,OAAO,CAAC;MAEvC,IAAI,CAACpH,WAAW,CAAC/B,SAAS,CAACmJ,OAAO,CAACjK,GAAG,EAAE,CAAC,EAAE4K,SAAS,EAAEA,SAAS,CAAC,CAAChF,SAAS,CAAC;QACzEC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpB4E,KAAK,CAAC,wBAAwB,CAAC;WAChC,MAAM;YACLA,KAAK,CAAC,+BAA+B,CAAC;;QAE1C,CAAC;QACDzE,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAC7CyE,KAAK,CAAC,8BAA8B,CAAC;QACvC;OACD,CAAC;;EAEN;EAEA;EACA5L,cAAcA,CAAC0J,KAAU;IACvB;IACA,IAAIA,KAAK,IAAIA,KAAK,CAACoC,aAAa,KAAKD,SAAS,EAAE;MAC9C,IAAI,CAAC7G,iBAAiB,GAAG0E,KAAK,CAACoC,aAAa;MAE5C;MACA1E,OAAO,CAAC+D,GAAG,CAAC,6BAA6B,IAAI,CAACnG,iBAAiB,EAAE,CAAC;MAElE;MACA,IAAI,CAAC+G,oBAAoB,EAAE;;EAE/B;EAEAjM,aAAaA,CAAC2K,MAAW;IACvB;IACA,IAAI,CAAC3F,qBAAqB,GAAG,IAAI;IACjCsC,OAAO,CAAC+D,GAAG,CAAC,qEAAqE,CAAC;EACpF;EAEA;EACQY,oBAAoBA,CAAA;IAC1B;IACA,IAAI,IAAI,CAAC3L,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC,IAAI,CAAC4E,iBAAiB,CAAC,EAAE;MACxD,MAAMgH,YAAY,GAAG,IAAI,CAAC5L,OAAO,CAAC,IAAI,CAAC4E,iBAAiB,CAAC;MACzDoC,OAAO,CAAC+D,GAAG,CAAC,uBAAuBa,YAAY,CAAC/M,IAAI,CAACK,QAAQ,EAAE,CAAC;;EAEpE;EAEA;EACA2M,cAAcA,CAAA;IACZ,IAAI,CAAClH,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IACxC;IACA;IACAqC,OAAO,CAAC+D,GAAG,CAAC,aAAa,IAAI,CAACpG,aAAa,GAAG,SAAS,GAAG,UAAU,EAAE,CAAC;EACzE;;;uBAhkBWrB,uBAAuB,EAAAhG,EAAA,CAAAwO,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA1O,EAAA,CAAAwO,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAA5O,EAAA,CAAAwO,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA9O,EAAA,CAAAwO,iBAAA,CAAAO,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAvBhJ,uBAAuB;MAAAiJ,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UAAvBpP,EAAA,CAAAU,UAAA,qBAAA4O,mDAAAnN,MAAA;YAAA,OAAAkN,GAAA,CAAAtD,aAAA,CAAA5J,MAAA,CAAqB;UAAA,UAAAnC,EAAA,CAAAuP,iBAAA,CAAE;;;;;;;;;;;;;;;;;;UC/CpCvP,EAAA,CAAAC,cAAA,aAA+B;UAU7BD,EARA,CAAAI,UAAA,IAAAoP,sCAAA,iBAAsD,IAAAC,sCAAA,kBAQC;UA6CzDzP,EAAA,CAAAG,YAAA,EAAM;UAgINH,EA7HA,CAAAI,UAAA,IAAAsP,sCAAA,mBAAqE,IAAAC,sCAAA,iBA6HxB;;;UArLrC3P,EAAA,CAAAM,SAAA,EAAsB;UAAtBN,EAAA,CAAAO,UAAA,SAAA8O,GAAA,CAAA5I,gBAAA,CAAsB;UAQEzG,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAO,UAAA,UAAA8O,GAAA,CAAA5I,gBAAA,CAAuB;UAgDAzG,EAAA,CAAAM,SAAA,EAAY;UAAZN,EAAA,CAAAO,UAAA,SAAA8O,GAAA,CAAA7J,MAAA,CAAY;UA6HpCxF,EAAA,CAAAM,SAAA,EAAY;UAAZN,EAAA,CAAAO,UAAA,SAAA8O,GAAA,CAAA7J,MAAA,CAAY;;;qBD5I/B1F,YAAY,EAAA8P,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE/P,cAAc,EAAAgQ,EAAA,CAAAC,iBAAA,EAAAD,EAAA,CAAAE,sBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}