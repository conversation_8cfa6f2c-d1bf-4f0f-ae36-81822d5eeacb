{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class StatusPipe {\n  constructor() {\n    this.statusDisplayNames = {\n      // User statuses\n      'active': 'Active',\n      'inactive': 'Inactive',\n      'pending': 'Pending',\n      'suspended': 'Suspended',\n      'verified': 'Verified',\n      'unverified': 'Unverified',\n      // Order statuses\n      'confirmed': 'Confirmed',\n      'processing': 'Processing',\n      'shipped': 'Shipped',\n      'delivered': 'Delivered',\n      'cancelled': 'Cancelled',\n      'refunded': 'Refunded',\n      // Payment statuses\n      'paid': 'Paid',\n      'failed': 'Failed',\n      'partial': 'Partial',\n      // Product statuses\n      'approved': 'Approved',\n      'rejected': 'Rejected',\n      'draft': 'Draft',\n      'published': 'Published',\n      'featured': 'Featured'\n    };\n  }\n  transform(status) {\n    return this.statusDisplayNames[status] || status;\n  }\n  static {\n    this.ɵfac = function StatusPipe_Factory(t) {\n      return new (t || StatusPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"status\",\n      type: StatusPipe,\n      pure: true\n    });\n  }\n}", "map": {"version": 3, "names": ["StatusPipe", "constructor", "statusDisplayNames", "transform", "status", "pure"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\admin\\pipes\\status.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\n\n@Pipe({\n  name: 'status'\n})\nexport class StatusPipe implements PipeTransform {\n\n  private statusDisplayNames: { [key: string]: string } = {\n    // User statuses\n    'active': 'Active',\n    'inactive': 'Inactive',\n    'pending': 'Pending',\n    'suspended': 'Suspended',\n    'verified': 'Verified',\n    'unverified': 'Unverified',\n    \n    // Order statuses\n    'confirmed': 'Confirmed',\n    'processing': 'Processing',\n    'shipped': 'Shipped',\n    'delivered': 'Delivered',\n    'cancelled': 'Cancelled',\n    'refunded': 'Refunded',\n    \n    // Payment statuses\n    'paid': 'Paid',\n    'failed': 'Failed',\n    'partial': 'Partial',\n    \n    // Product statuses\n    'approved': 'Approved',\n    'rejected': 'Rejected',\n    'draft': 'Draft',\n    'published': 'Published',\n    'featured': 'Featured'\n  };\n\n  transform(status: string): string {\n    return this.statusDisplayNames[status] || status;\n  }\n}\n"], "mappings": ";AAKA,OAAM,MAAOA,UAAU;EAHvBC,YAAA;IAKU,KAAAC,kBAAkB,GAA8B;MACtD;MACA,QAAQ,EAAE,QAAQ;MAClB,UAAU,EAAE,UAAU;MACtB,SAAS,EAAE,SAAS;MACpB,WAAW,EAAE,WAAW;MACxB,UAAU,EAAE,UAAU;MACtB,YAAY,EAAE,YAAY;MAE1B;MACA,WAAW,EAAE,WAAW;MACxB,YAAY,EAAE,YAAY;MAC1B,SAAS,EAAE,SAAS;MACpB,WAAW,EAAE,WAAW;MACxB,WAAW,EAAE,WAAW;MACxB,UAAU,EAAE,UAAU;MAEtB;MACA,MAAM,EAAE,MAAM;MACd,QAAQ,EAAE,QAAQ;MAClB,SAAS,EAAE,SAAS;MAEpB;MACA,UAAU,EAAE,UAAU;MACtB,UAAU,EAAE,UAAU;MACtB,OAAO,EAAE,OAAO;MAChB,WAAW,EAAE,WAAW;MACxB,UAAU,EAAE;KACb;;EAEDC,SAASA,CAACC,MAAc;IACtB,OAAO,IAAI,CAACF,kBAAkB,CAACE,MAAM,CAAC,IAAIA,MAAM;EAClD;;;uBAlCWJ,UAAU;IAAA;EAAA;;;;YAAVA,UAAU;MAAAK,IAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}