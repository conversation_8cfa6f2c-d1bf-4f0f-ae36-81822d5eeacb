<div class="shop-categories-container">
  <!-- Header -->
  <div class="section-header">
    <div class="header-content">
      <h2 class="section-title">
        <ion-icon name="grid" class="title-icon"></ion-icon>
        Shop by Category
      </h2>
      <p class="section-subtitle">Explore our collections</p>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="loading-grid">
      <div *ngFor="let item of [1,2,3,4]" class="loading-category-card">
        <div class="loading-image"></div>
        <div class="loading-content">
          <div class="loading-line short"></div>
          <div class="loading-line medium"></div>
          <div class="loading-line long"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !isLoading" class="error-container">
    <ion-icon name="alert-circle" class="error-icon"></ion-icon>
    <p class="error-message">{{ error }}</p>
    <button class="retry-btn" (click)="onRetry()">
      <ion-icon name="refresh"></ion-icon>
      Try Again
    </button>
  </div>

  <!-- Categories Slider -->
  <div *ngIf="!isLoading && !error && categories.length > 0" class="categories-slider-container">
    <!-- Navigation Buttons -->
    <button class="slider-nav prev-btn" (click)="slidePrev()" [disabled]="currentSlide === 0">
      <ion-icon name="chevron-back"></ion-icon>
    </button>
    <button class="slider-nav next-btn" (click)="slideNext()" [disabled]="currentSlide >= maxSlide">
      <ion-icon name="chevron-forward"></ion-icon>
    </button>
    
    <!-- Slider Wrapper -->
    <div class="categories-slider-wrapper" (mouseenter)="pauseAutoSlide()" (mouseleave)="resumeAutoSlide()">
      <div class="categories-slider" [style.transform]="'translateX(' + slideOffset + 'px)'">
        <div 
          *ngFor="let category of categories; trackBy: trackByCategoryId" 
          class="category-card"
          [class.trending]="category.trending"
          (click)="onCategoryClick(category)"
        >
          <!-- Category Image -->
          <div class="category-image-container">
            <img 
              [src]="category.image"
              [alt]="category.name"
              class="category-image"
              loading="lazy"
            />
            
            <!-- Trending Badge -->
            <div *ngIf="category.trending" class="trending-badge">
              <ion-icon name="trending-up"></ion-icon>
              <span>Trending</span>
            </div>
            
            <!-- Discount Badge -->
            <div *ngIf="category.discount" class="discount-badge">
              {{ category.discount }}% OFF
            </div>
            
            <!-- Overlay -->
            <div class="category-overlay">
              <ion-icon name="arrow-forward" class="explore-icon"></ion-icon>
            </div>
          </div>

          <!-- Category Info -->
          <div class="category-info">
            <h3 class="category-name">{{ category.name }}</h3>
            <p class="category-description">{{ category.description }}</p>
            <p class="product-count">{{ formatProductCount(category.productCount) }} Products</p>
            
            <!-- Subcategories -->
            <div class="subcategories">
              <span 
                *ngFor="let sub of category.subcategories.slice(0, 3)" 
                class="subcategory-tag"
              >
                {{ sub }}
              </span>
              <span *ngIf="category.subcategories.length > 3" class="more-subcategories">
                +{{ category.subcategories.length - 3 }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div> <!-- End categories-slider-wrapper -->
  </div> <!-- End categories-slider-container -->

  <!-- Empty State -->
  <div *ngIf="!isLoading && !error && categories.length === 0" class="empty-container">
    <ion-icon name="grid-outline" class="empty-icon"></ion-icon>
    <h3 class="empty-title">No Categories</h3>
    <p class="empty-message">Check back later for product categories</p>
  </div>
</div>
