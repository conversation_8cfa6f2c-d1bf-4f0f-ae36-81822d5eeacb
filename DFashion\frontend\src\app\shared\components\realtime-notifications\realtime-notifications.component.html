<div class="notification-bell">
  <button 
    mat-icon-button 
    [matMenuTriggerFor]="notificationMenu"
    [matBadge]="unreadCount"
    [matBadgeHidden]="unreadCount === 0"
    matBadgeColor="warn"
    matBadgeSize="small"
    [matTooltip]="unreadCount > 0 ? unreadCount + ' unread notifications' : 'No new notifications'"
    class="notification-button"
    [class.has-notifications]="unreadCount > 0"
  >
    <mat-icon>notifications</mat-icon>
  </button>

  <mat-menu #notificationMenu="matMenu" class="notification-menu" xPosition="before">
    <div class="notification-header" (click)="$event.stopPropagation()">
      <h3>Notifications</h3>
      <div class="header-actions">
        <button 
          mat-icon-button 
          *ngIf="unreadCount > 0"
          (click)="markAllAsRead()"
          matTooltip="Mark all as read"
          class="mark-all-read-btn"
        >
          <mat-icon>done_all</mat-icon>
        </button>
        <div class="connection-status" [class.connected]="isConnected" [class.disconnected]="!isConnected">
          <mat-icon>{{ isConnected ? 'wifi' : 'wifi_off' }}</mat-icon>
        </div>
      </div>
    </div>

    <mat-divider></mat-divider>

    <div class="notification-list" (click)="$event.stopPropagation()">
      <div *ngIf="loading" class="loading-container">
        <mat-spinner diameter="30"></mat-spinner>
        <span>Loading notifications...</span>
      </div>

      <div *ngIf="!loading && notifications.length === 0" class="empty-state">
        <mat-icon>notifications_none</mat-icon>
        <p>No notifications yet</p>
      </div>

      <div 
        *ngFor="let notification of notifications; trackBy: trackByNotificationId"
        class="notification-item"
        [class.unread]="!notification.isRead"
        [class.urgent]="notification.priority === 'urgent'"
        [class.high]="notification.priority === 'high'"
        (click)="handleNotificationClick(notification)"
      >
        <div class="notification-icon">
          <mat-icon [style.color]="getNotificationColor(notification.priority)">
            {{ getNotificationIcon(notification.type) }}
          </mat-icon>
        </div>

        <div class="notification-content">
          <div class="notification-title">{{ notification.title }}</div>
          <div class="notification-message">{{ notification.message }}</div>
          <div class="notification-meta">
            <span class="notification-time">{{ notification.timeAgo }}</span>
            <span class="notification-category">{{ notification.category }}</span>
          </div>
        </div>

        <div class="notification-actions">
          <button 
            mat-icon-button 
            *ngIf="!notification.isRead"
            (click)="markAsRead(notification._id, $event)"
            matTooltip="Mark as read"
            class="mark-read-btn"
          >
            <mat-icon>done</mat-icon>
          </button>
        </div>
      </div>

      <div *ngIf="notifications.length > 0" class="notification-footer">
        <button mat-button (click)="viewAllNotifications()" class="view-all-btn">
          View All Notifications
        </button>
      </div>
    </div>
  </mat-menu>
</div>
