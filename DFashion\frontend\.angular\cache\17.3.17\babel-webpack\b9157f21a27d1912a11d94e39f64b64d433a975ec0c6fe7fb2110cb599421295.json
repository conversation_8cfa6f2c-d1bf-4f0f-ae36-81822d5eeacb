{"ast": null, "code": "import { inject } from '@angular/core';\nimport { AuthService } from '../services/auth.service';\nexport const authInterceptor = (req, next) => {\n  const authService = inject(AuthService);\n  const token = authService.getToken();\n  if (token) {\n    const authReq = req.clone({\n      headers: req.headers.set('Authorization', `Bearer ${token}`)\n    });\n    return next(authReq);\n  }\n  return next(req);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}