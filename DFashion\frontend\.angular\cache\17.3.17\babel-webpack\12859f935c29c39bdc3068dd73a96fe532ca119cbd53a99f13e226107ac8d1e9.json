{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3];\nfunction TopFashionInfluencersComponent_div_8_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵelement(1, \"div\", 14);\n    i0.ɵɵelementStart(2, \"div\", 15);\n    i0.ɵɵelement(3, \"div\", 16)(4, \"div\", 17)(5, \"div\", 18);\n    i0.ɵɵelementStart(6, \"div\", 19);\n    i0.ɵɵelement(7, \"div\", 20)(8, \"div\", 20);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction TopFashionInfluencersComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11);\n    i0.ɵɵtemplate(2, TopFashionInfluencersComponent_div_8_div_2_Template, 9, 0, \"div\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction TopFashionInfluencersComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"ion-icon\", 22);\n    i0.ɵɵelementStart(2, \"p\", 23);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_9_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 25);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction TopFashionInfluencersComponent_div_10_div_7_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵelement(1, \"ion-icon\", 54);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TopFashionInfluencersComponent_div_10_div_7_span_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const brand_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", brand_r6, \" \");\n  }\n}\nfunction TopFashionInfluencersComponent_div_10_div_7_span_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 56);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const influencer_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" +\", influencer_r5.topBrands.length - 2, \" \");\n  }\n}\nfunction TopFashionInfluencersComponent_div_10_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_10_div_7_Template_div_click_0_listener() {\n      const influencer_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onInfluencerClick(influencer_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 35);\n    i0.ɵɵelement(2, \"img\", 36);\n    i0.ɵɵtemplate(3, TopFashionInfluencersComponent_div_10_div_7_div_3_Template, 2, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 38)(5, \"h3\", 39);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 40);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 41);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 42)(12, \"div\", 43)(13, \"span\", 44);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 45);\n    i0.ɵɵtext(16, \"Followers\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 43)(18, \"span\", 44);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 45);\n    i0.ɵɵtext(21, \"Engagement\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 46)(23, \"span\", 47);\n    i0.ɵɵtext(24, \"Works with:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 48);\n    i0.ɵɵtemplate(26, TopFashionInfluencersComponent_div_10_div_7_span_26_Template, 2, 1, \"span\", 49)(27, TopFashionInfluencersComponent_div_10_div_7_span_27_Template, 2, 1, \"span\", 50);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_10_div_7_Template_button_click_28_listener($event) {\n      const influencer_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onFollowInfluencer(influencer_r5, $event));\n    });\n    i0.ɵɵelementStart(29, \"span\");\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(31, \"ion-icon\", 52);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const influencer_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", influencer_r5.avatar, i0.ɵɵsanitizeUrl)(\"alt\", influencer_r5.fullName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", influencer_r5.isVerified);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(influencer_r5.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"@\", influencer_r5.username, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(influencer_r5.category);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatFollowerCount(influencer_r5.followerCount));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", influencer_r5.engagementRate, \"%\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", influencer_r5.topBrands.slice(0, 2));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", influencer_r5.topBrands.length > 2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"following\", influencer_r5.isFollowing);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(influencer_r5.isFollowing ? \"Following\" : \"Follow\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", influencer_r5.isFollowing ? \"checkmark\" : \"add\");\n  }\n}\nfunction TopFashionInfluencersComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_10_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slidePrev());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_10_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slideNext());\n    });\n    i0.ɵɵelement(4, \"ion-icon\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 31);\n    i0.ɵɵlistener(\"mouseenter\", function TopFashionInfluencersComponent_div_10_Template_div_mouseenter_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pauseAutoSlide());\n    })(\"mouseleave\", function TopFashionInfluencersComponent_div_10_Template_div_mouseleave_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resumeAutoSlide());\n    });\n    i0.ɵɵelementStart(6, \"div\", 32);\n    i0.ɵɵtemplate(7, TopFashionInfluencersComponent_div_10_div_7_Template, 32, 14, \"div\", 33);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide >= ctx_r1.maxSlide);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx_r1.slideOffset + \"px)\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.topInfluencers)(\"ngForTrackBy\", ctx_r1.trackByInfluencerId);\n  }\n}\nfunction TopFashionInfluencersComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵelement(1, \"ion-icon\", 58);\n    i0.ɵɵelementStart(2, \"h3\", 59);\n    i0.ɵɵtext(3, \"No Influencers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 60);\n    i0.ɵɵtext(5, \"Check back later for top fashion influencers\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class TopFashionInfluencersComponent {\n  constructor(router) {\n    this.router = router;\n    this.topInfluencers = [];\n    this.isLoading = true;\n    this.error = null;\n    this.subscription = new Subscription();\n    // Slider properties\n    this.currentSlide = 0;\n    this.slideOffset = 0;\n    this.cardWidth = 240; // Width of each influencer card including margin\n    this.visibleCards = 3; // Number of cards visible at once\n    this.maxSlide = 0;\n    this.autoSlideDelay = 6000; // 6 seconds for influencers\n    this.isAutoSliding = true;\n    this.isPaused = false;\n  }\n  ngOnInit() {\n    this.loadTopInfluencers();\n    this.updateResponsiveSettings();\n    this.setupResizeListener();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n  loadTopInfluencers() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        // Mock data for top fashion influencers\n        _this.topInfluencers = [{\n          id: '1',\n          username: 'fashionista_queen',\n          fullName: 'Priya Sharma',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',\n          followerCount: 2500000,\n          category: 'High Fashion',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 8.5,\n          recentPosts: 24,\n          topBrands: ['Gucci', 'Prada', 'Versace']\n        }, {\n          id: '2',\n          username: 'street_style_king',\n          fullName: 'Arjun Kapoor',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n          followerCount: 1800000,\n          category: 'Streetwear',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 12.3,\n          recentPosts: 18,\n          topBrands: ['Nike', 'Adidas', 'Supreme']\n        }, {\n          id: '3',\n          username: 'boho_goddess',\n          fullName: 'Ananya Singh',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n          followerCount: 1200000,\n          category: 'Boho Chic',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 9.7,\n          recentPosts: 32,\n          topBrands: ['Free People', 'Anthropologie', 'Zara']\n        }, {\n          id: '4',\n          username: 'luxury_lifestyle',\n          fullName: 'Kavya Reddy',\n          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',\n          followerCount: 3200000,\n          category: 'Luxury',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 6.8,\n          recentPosts: 15,\n          topBrands: ['Chanel', 'Dior', 'Louis Vuitton']\n        }, {\n          id: '5',\n          username: 'minimalist_maven',\n          fullName: 'Ravi Kumar',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n          followerCount: 950000,\n          category: 'Minimalist',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 11.2,\n          recentPosts: 21,\n          topBrands: ['COS', 'Uniqlo', 'Everlane']\n        }, {\n          id: '6',\n          username: 'vintage_vibes',\n          fullName: 'Meera Patel',\n          avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',\n          followerCount: 780000,\n          category: 'Vintage',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 13.5,\n          recentPosts: 28,\n          topBrands: ['Vintage Stores', 'Thrift Finds', 'Custom']\n        }];\n        _this.isLoading = false;\n        _this.updateSliderOnInfluencersLoad();\n      } catch (error) {\n        console.error('Error loading top influencers:', error);\n        _this.error = 'Failed to load top influencers';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onInfluencerClick(influencer) {\n    this.router.navigate(['/profile', influencer.username]);\n  }\n  onFollowInfluencer(influencer, event) {\n    event.stopPropagation();\n    influencer.isFollowing = !influencer.isFollowing;\n    if (influencer.isFollowing) {\n      influencer.followerCount++;\n    } else {\n      influencer.followerCount--;\n    }\n  }\n  formatFollowerCount(count) {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  onRetry() {\n    this.loadTopInfluencers();\n  }\n  trackByInfluencerId(index, influencer) {\n    return influencer.id;\n  }\n  // Auto-sliding methods\n  startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.topInfluencers.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n  autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n  // Responsive methods\n  updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 480) {\n      this.cardWidth = 200;\n      this.visibleCards = 1;\n    } else if (width <= 768) {\n      this.cardWidth = 220;\n      this.visibleCards = 2;\n    } else if (width <= 1200) {\n      this.cardWidth = 240;\n      this.visibleCards = 2;\n    } else {\n      this.cardWidth = 260;\n      this.visibleCards = 3;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n  setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.topInfluencers.length - this.visibleCards);\n  }\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n  restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000);\n  }\n  // Update slider when influencers load\n  updateSliderOnInfluencersLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n  static {\n    this.ɵfac = function TopFashionInfluencersComponent_Factory(t) {\n      return new (t || TopFashionInfluencersComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TopFashionInfluencersComponent,\n      selectors: [[\"app-top-fashion-influencers\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 12,\n      vars: 4,\n      consts: [[1, \"top-influencers-container\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"star\", 1, \"title-icon\"], [1, \"section-subtitle\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"influencers-slider-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-influencer-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-influencer-card\"], [1, \"loading-avatar\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [1, \"loading-stats\"], [1, \"loading-stat\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"influencers-slider-container\"], [1, \"slider-nav\", \"prev-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-back\"], [1, \"slider-nav\", \"next-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-forward\"], [1, \"influencers-slider-wrapper\", 3, \"mouseenter\", \"mouseleave\"], [1, \"influencers-slider\"], [\"class\", \"influencer-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"influencer-card\", 3, \"click\"], [1, \"influencer-avatar-container\"], [\"loading\", \"lazy\", 1, \"influencer-avatar\", 3, \"src\", \"alt\"], [\"class\", \"verified-badge\", 4, \"ngIf\"], [1, \"influencer-info\"], [1, \"influencer-name\"], [1, \"username\"], [1, \"category\"], [1, \"stats-container\"], [1, \"stat\"], [1, \"stat-value\"], [1, \"stat-label\"], [1, \"top-brands\"], [1, \"brands-label\"], [1, \"brands-list\"], [\"class\", \"brand-tag\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"more-brands\", 4, \"ngIf\"], [1, \"follow-btn\", 3, \"click\"], [3, \"name\"], [1, \"verified-badge\"], [\"name\", \"checkmark\"], [1, \"brand-tag\"], [1, \"more-brands\"], [1, \"empty-container\"], [\"name\", \"star-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"]],\n      template: function TopFashionInfluencersComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n          i0.ɵɵelement(4, \"ion-icon\", 4);\n          i0.ɵɵtext(5, \" Top Fashion Influencers \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \"Follow the trendsetters\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(8, TopFashionInfluencersComponent_div_8_Template, 3, 2, \"div\", 6)(9, TopFashionInfluencersComponent_div_9_Template, 7, 1, \"div\", 7)(10, TopFashionInfluencersComponent_div_10_Template, 8, 6, \"div\", 8)(11, TopFashionInfluencersComponent_div_11_Template, 6, 0, \"div\", 9);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.topInfluencers.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.topInfluencers.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, IonicModule, i3.IonIcon, CarouselModule],\n      styles: [\".top-influencers-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #ffd700;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  margin: 0;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  overflow-x: auto;\\n  padding-bottom: 8px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%] {\\n  flex: 0 0 220px;\\n  background: rgba(255, 255, 255, 0.7);\\n  border-radius: 16px;\\n  padding: 20px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-avatar[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  border-radius: 50%;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  margin: 0 auto 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  border-radius: 6px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  margin-bottom: 8px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  margin-top: 12px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-stats[_ngcontent-%COMP%]   .loading-stat[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 20px;\\n  border-radius: 10px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0% {\\n    background-position: 200% 0;\\n  }\\n  100% {\\n    background-position: -200% 0;\\n  }\\n}\\n.error-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #e74c3c;\\n  margin-bottom: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 20px;\\n  font-size: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);\\n  color: white;\\n  border: none;\\n  padding: 12px 24px;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 auto;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);\\n}\\n\\n.influencers-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin: 0 -20px;\\n}\\n.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  z-index: 10;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  border: none;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: rgba(0, 0, 0, 0.9);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n}\\n.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%] {\\n  left: -20px;\\n}\\n.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%] {\\n  right: -20px;\\n}\\n\\n.influencers-slider-wrapper[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  padding: 0 20px;\\n}\\n\\n.influencers-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n  gap: 20px;\\n}\\n.influencers-slider[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%] {\\n  flex: 0 0 220px;\\n  width: 220px;\\n}\\n\\n.influencer-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 24px;\\n  text-align: center;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.influencer-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n}\\n\\n.influencer-avatar-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 20px;\\n}\\n.influencer-avatar-container[_ngcontent-%COMP%]   .influencer-avatar[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 4px solid #ffd700;\\n  margin: 0 auto;\\n  display: block;\\n}\\n.influencer-avatar-container[_ngcontent-%COMP%]   .verified-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -5px;\\n  right: calc(50% - 55px);\\n  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);\\n  color: white;\\n  width: 28px;\\n  height: 28px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 14px;\\n  border: 3px solid white;\\n}\\n.influencer-avatar-container[_ngcontent-%COMP%]   .verified-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n\\n.influencer-info[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.influencer-info[_ngcontent-%COMP%]   .influencer-name[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 4px 0;\\n}\\n.influencer-info[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #6c5ce7;\\n  margin: 0 0 8px 0;\\n  font-weight: 500;\\n}\\n.influencer-info[_ngcontent-%COMP%]   .category[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #ffd700;\\n  background: rgba(255, 215, 0, 0.1);\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  display: inline-block;\\n  margin: 0 0 16px 0;\\n  font-weight: 600;\\n}\\n\\n.stats-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 16px;\\n}\\n.stats-container[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%] {\\n  flex: 1;\\n  text-align: center;\\n}\\n.stats-container[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 16px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin-bottom: 2px;\\n}\\n.stats-container[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #666;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n\\n.top-brands[_ngcontent-%COMP%]   .brands-label[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #666;\\n  display: block;\\n  margin-bottom: 6px;\\n}\\n.top-brands[_ngcontent-%COMP%]   .brands-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 4px;\\n  justify-content: center;\\n}\\n.top-brands[_ngcontent-%COMP%]   .brands-list[_ngcontent-%COMP%]   .brand-tag[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  background: rgba(108, 92, 231, 0.1);\\n  color: #6c5ce7;\\n  padding: 2px 6px;\\n  border-radius: 8px;\\n  font-weight: 500;\\n}\\n.top-brands[_ngcontent-%COMP%]   .brands-list[_ngcontent-%COMP%]   .more-brands[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: #999;\\n  font-weight: 500;\\n}\\n\\n.follow-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);\\n  color: #1a1a1a;\\n  border: none;\\n  padding: 10px 20px;\\n  border-radius: 25px;\\n  font-size: 13px;\\n  font-weight: 700;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  margin: 0 auto;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.follow-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);\\n}\\n.follow-btn.following[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);\\n  color: white;\\n}\\n.follow-btn.following[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 8px 25px rgba(0, 184, 148, 0.4);\\n}\\n.follow-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: #ddd;\\n  margin-bottom: 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #666;\\n  margin: 0 0 8px 0;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  color: #999;\\n  margin: 0;\\n}\\n\\n@media (max-width: 1200px) {\\n  .influencers-slider[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%] {\\n    flex: 0 0 200px;\\n    width: 200px;\\n    padding: 20px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .influencers-slider-container[_ngcontent-%COMP%] {\\n    margin: 0 -10px;\\n  }\\n  .influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .influencers-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%] {\\n    left: -15px;\\n  }\\n  .influencers-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%] {\\n    right: -15px;\\n  }\\n  .influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .influencers-slider-wrapper[_ngcontent-%COMP%] {\\n    padding: 0 10px;\\n  }\\n  .influencers-slider[_ngcontent-%COMP%] {\\n    gap: 15px;\\n  }\\n  .influencers-slider[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%] {\\n    flex: 0 0 180px;\\n    width: 180px;\\n    padding: 16px;\\n  }\\n  .influencer-avatar-container[_ngcontent-%COMP%]   .influencer-avatar[_ngcontent-%COMP%] {\\n    width: 80px;\\n    height: 80px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .influencers-slider[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%] {\\n    flex: 0 0 170px;\\n    width: 170px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subscription", "IonicModule", "CarouselModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "TopFashionInfluencersComponent_div_8_div_2_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵtext", "ɵɵlistener", "TopFashionInfluencersComponent_div_9_Template_button_click_4_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onRetry", "ɵɵtextInterpolate", "error", "ɵɵtextInterpolate1", "brand_r6", "influencer_r5", "topBrands", "length", "TopFashionInfluencersComponent_div_10_div_7_Template_div_click_0_listener", "_r4", "$implicit", "onInfluencerClick", "TopFashionInfluencersComponent_div_10_div_7_div_3_Template", "TopFashionInfluencersComponent_div_10_div_7_span_26_Template", "TopFashionInfluencersComponent_div_10_div_7_span_27_Template", "TopFashionInfluencersComponent_div_10_div_7_Template_button_click_28_listener", "$event", "onFollowInfluencer", "avatar", "ɵɵsanitizeUrl", "fullName", "isVerified", "username", "category", "formatFollowerCount", "followerCount", "engagementRate", "slice", "ɵɵclassProp", "isFollowing", "TopFashionInfluencersComponent_div_10_Template_button_click_1_listener", "_r3", "slidePrev", "TopFashionInfluencersComponent_div_10_Template_button_click_3_listener", "slideNext", "TopFashionInfluencersComponent_div_10_Template_div_mouseenter_5_listener", "pauseAutoSlide", "TopFashionInfluencersComponent_div_10_Template_div_mouseleave_5_listener", "resumeAutoSlide", "TopFashionInfluencersComponent_div_10_div_7_Template", "currentSlide", "maxSlide", "ɵɵstyleProp", "slideOffset", "topInfluencers", "trackByInfluencerId", "TopFashionInfluencersComponent", "constructor", "router", "isLoading", "subscription", "<PERSON><PERSON><PERSON><PERSON>", "visibleCards", "autoSlideDelay", "isAutoSliding", "isPaused", "ngOnInit", "loadTopInfluencers", "updateResponsiveSettings", "setupResizeListener", "ngOnDestroy", "unsubscribe", "stopAutoSlide", "_this", "_asyncToGenerator", "id", "recentPosts", "updateSliderOnInfluencersLoad", "console", "influencer", "navigate", "event", "stopPropagation", "count", "toFixed", "toString", "index", "startAutoSlide", "autoSlideInterval", "setInterval", "autoSlideNext", "clearInterval", "updateSlideOffset", "width", "window", "innerWidth", "updateSliderLimits", "addEventListener", "Math", "max", "restartAutoSlideAfterInteraction", "setTimeout", "ɵɵdirectiveInject", "i1", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TopFashionInfluencersComponent_Template", "rf", "ctx", "TopFashionInfluencersComponent_div_8_Template", "TopFashionInfluencersComponent_div_9_Template", "TopFashionInfluencersComponent_div_10_Template", "TopFashionInfluencersComponent_div_11_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "IonIcon", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\top-fashion-influencers\\top-fashion-influencers.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\top-fashion-influencers\\top-fashion-influencers.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\n\ninterface TopInfluencer {\n  id: string;\n  username: string;\n  fullName: string;\n  avatar: string;\n  followerCount: number;\n  category: string;\n  isVerified: boolean;\n  isFollowing: boolean;\n  engagementRate: number;\n  recentPosts: number;\n  topBrands: string[];\n}\n\n@Component({\n  selector: 'app-top-fashion-influencers',\n  standalone: true,\n  imports: [CommonModule, IonicModule, CarouselModule],\n  templateUrl: './top-fashion-influencers.component.html',\n  styleUrls: ['./top-fashion-influencers.component.scss']\n})\nexport class TopFashionInfluencersComponent implements OnInit, OnDestroy {\n  topInfluencers: TopInfluencer[] = [];\n  isLoading = true;\n  error: string | null = null;\n  private subscription: Subscription = new Subscription();\n\n  // Slider properties\n  currentSlide = 0;\n  slideOffset = 0;\n  cardWidth = 240; // Width of each influencer card including margin\n  visibleCards = 3; // Number of cards visible at once\n  maxSlide = 0;\n  \n  // Auto-sliding properties\n  autoSlideInterval: any;\n  autoSlideDelay = 6000; // 6 seconds for influencers\n  isAutoSliding = true;\n  isPaused = false;\n\n  constructor(private router: Router) {}\n\n  ngOnInit() {\n    this.loadTopInfluencers();\n    this.updateResponsiveSettings();\n    this.setupResizeListener();\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n\n  private async loadTopInfluencers() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      \n      // Mock data for top fashion influencers\n      this.topInfluencers = [\n        {\n          id: '1',\n          username: 'fashionista_queen',\n          fullName: 'Priya Sharma',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',\n          followerCount: 2500000,\n          category: 'High Fashion',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 8.5,\n          recentPosts: 24,\n          topBrands: ['Gucci', 'Prada', 'Versace']\n        },\n        {\n          id: '2',\n          username: 'street_style_king',\n          fullName: 'Arjun Kapoor',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n          followerCount: 1800000,\n          category: 'Streetwear',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 12.3,\n          recentPosts: 18,\n          topBrands: ['Nike', 'Adidas', 'Supreme']\n        },\n        {\n          id: '3',\n          username: 'boho_goddess',\n          fullName: 'Ananya Singh',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n          followerCount: 1200000,\n          category: 'Boho Chic',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 9.7,\n          recentPosts: 32,\n          topBrands: ['Free People', 'Anthropologie', 'Zara']\n        },\n        {\n          id: '4',\n          username: 'luxury_lifestyle',\n          fullName: 'Kavya Reddy',\n          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',\n          followerCount: 3200000,\n          category: 'Luxury',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 6.8,\n          recentPosts: 15,\n          topBrands: ['Chanel', 'Dior', 'Louis Vuitton']\n        },\n        {\n          id: '5',\n          username: 'minimalist_maven',\n          fullName: 'Ravi Kumar',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n          followerCount: 950000,\n          category: 'Minimalist',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 11.2,\n          recentPosts: 21,\n          topBrands: ['COS', 'Uniqlo', 'Everlane']\n        },\n        {\n          id: '6',\n          username: 'vintage_vibes',\n          fullName: 'Meera Patel',\n          avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',\n          followerCount: 780000,\n          category: 'Vintage',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 13.5,\n          recentPosts: 28,\n          topBrands: ['Vintage Stores', 'Thrift Finds', 'Custom']\n        }\n      ];\n      \n      this.isLoading = false;\n      this.updateSliderOnInfluencersLoad();\n    } catch (error) {\n      console.error('Error loading top influencers:', error);\n      this.error = 'Failed to load top influencers';\n      this.isLoading = false;\n    }\n  }\n\n  onInfluencerClick(influencer: TopInfluencer) {\n    this.router.navigate(['/profile', influencer.username]);\n  }\n\n  onFollowInfluencer(influencer: TopInfluencer, event: Event) {\n    event.stopPropagation();\n    influencer.isFollowing = !influencer.isFollowing;\n    \n    if (influencer.isFollowing) {\n      influencer.followerCount++;\n    } else {\n      influencer.followerCount--;\n    }\n  }\n\n  formatFollowerCount(count: number): string {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n\n  onRetry() {\n    this.loadTopInfluencers();\n  }\n\n  trackByInfluencerId(index: number, influencer: TopInfluencer): string {\n    return influencer.id;\n  }\n\n  // Auto-sliding methods\n  private startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n    \n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.topInfluencers.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n\n  private stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n\n  private autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n\n  // Responsive methods\n  private updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 480) {\n      this.cardWidth = 200;\n      this.visibleCards = 1;\n    } else if (width <= 768) {\n      this.cardWidth = 220;\n      this.visibleCards = 2;\n    } else if (width <= 1200) {\n      this.cardWidth = 240;\n      this.visibleCards = 2;\n    } else {\n      this.cardWidth = 260;\n      this.visibleCards = 3;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n\n  private setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.topInfluencers.length - this.visibleCards);\n  }\n\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n\n  private updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n\n  private restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000);\n  }\n\n  // Update slider when influencers load\n  private updateSliderOnInfluencersLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n}\n", "<div class=\"top-influencers-container\">\n  <!-- Header -->\n  <div class=\"section-header\">\n    <div class=\"header-content\">\n      <h2 class=\"section-title\">\n        <ion-icon name=\"star\" class=\"title-icon\"></ion-icon>\n        Top Fashion Influencers\n      </h2>\n      <p class=\"section-subtitle\">Follow the trendsetters</p>\n    </div>\n  </div>\n\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <div class=\"loading-grid\">\n      <div *ngFor=\"let item of [1,2,3]\" class=\"loading-influencer-card\">\n        <div class=\"loading-avatar\"></div>\n        <div class=\"loading-content\">\n          <div class=\"loading-line short\"></div>\n          <div class=\"loading-line medium\"></div>\n          <div class=\"loading-line long\"></div>\n          <div class=\"loading-stats\">\n            <div class=\"loading-stat\"></div>\n            <div class=\"loading-stat\"></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error && !isLoading\" class=\"error-container\">\n    <ion-icon name=\"alert-circle\" class=\"error-icon\"></ion-icon>\n    <p class=\"error-message\">{{ error }}</p>\n    <button class=\"retry-btn\" (click)=\"onRetry()\">\n      <ion-icon name=\"refresh\"></ion-icon>\n      Try Again\n    </button>\n  </div>\n\n  <!-- Influencers Slider -->\n  <div *ngIf=\"!isLoading && !error && topInfluencers.length > 0\" class=\"influencers-slider-container\">\n    <!-- Navigation Buttons -->\n    <button class=\"slider-nav prev-btn\" (click)=\"slidePrev()\" [disabled]=\"currentSlide === 0\">\n      <ion-icon name=\"chevron-back\"></ion-icon>\n    </button>\n    <button class=\"slider-nav next-btn\" (click)=\"slideNext()\" [disabled]=\"currentSlide >= maxSlide\">\n      <ion-icon name=\"chevron-forward\"></ion-icon>\n    </button>\n    \n    <!-- Slider Wrapper -->\n    <div class=\"influencers-slider-wrapper\" (mouseenter)=\"pauseAutoSlide()\" (mouseleave)=\"resumeAutoSlide()\">\n      <div class=\"influencers-slider\" [style.transform]=\"'translateX(' + slideOffset + 'px)'\">\n        <div \n          *ngFor=\"let influencer of topInfluencers; trackBy: trackByInfluencerId\" \n          class=\"influencer-card\"\n          (click)=\"onInfluencerClick(influencer)\"\n        >\n          <!-- Influencer Avatar -->\n          <div class=\"influencer-avatar-container\">\n            <img \n              [src]=\"influencer.avatar\"\n              [alt]=\"influencer.fullName\"\n              class=\"influencer-avatar\"\n              loading=\"lazy\"\n            />\n            <div *ngIf=\"influencer.isVerified\" class=\"verified-badge\">\n              <ion-icon name=\"checkmark\"></ion-icon>\n            </div>\n          </div>\n\n          <!-- Influencer Info -->\n          <div class=\"influencer-info\">\n            <h3 class=\"influencer-name\">{{ influencer.fullName }}</h3>\n            <p class=\"username\">&#64;{{ influencer.username }}</p>\n            <p class=\"category\">{{ influencer.category }}</p>\n            \n            <!-- Stats -->\n            <div class=\"stats-container\">\n              <div class=\"stat\">\n                <span class=\"stat-value\">{{ formatFollowerCount(influencer.followerCount) }}</span>\n                <span class=\"stat-label\">Followers</span>\n              </div>\n              <div class=\"stat\">\n                <span class=\"stat-value\">{{ influencer.engagementRate }}%</span>\n                <span class=\"stat-label\">Engagement</span>\n              </div>\n            </div>\n            \n            <!-- Top Brands -->\n            <div class=\"top-brands\">\n              <span class=\"brands-label\">Works with:</span>\n              <div class=\"brands-list\">\n                <span *ngFor=\"let brand of influencer.topBrands.slice(0, 2)\" class=\"brand-tag\">\n                  {{ brand }}\n                </span>\n                <span *ngIf=\"influencer.topBrands.length > 2\" class=\"more-brands\">\n                  +{{ influencer.topBrands.length - 2 }}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          <!-- Follow Button -->\n          <button \n            class=\"follow-btn\"\n            [class.following]=\"influencer.isFollowing\"\n            (click)=\"onFollowInfluencer(influencer, $event)\"\n          >\n            <span>{{ influencer.isFollowing ? 'Following' : 'Follow' }}</span>\n            <ion-icon [name]=\"influencer.isFollowing ? 'checkmark' : 'add'\"></ion-icon>\n          </button>\n        </div>\n      </div>\n    </div> <!-- End influencers-slider-wrapper -->\n  </div> <!-- End influencers-slider-container -->\n\n  <!-- Empty State -->\n  <div *ngIf=\"!isLoading && !error && topInfluencers.length === 0\" class=\"empty-container\">\n    <ion-icon name=\"star-outline\" class=\"empty-icon\"></ion-icon>\n    <h3 class=\"empty-title\">No Influencers</h3>\n    <p class=\"empty-message\">Check back later for top fashion influencers</p>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AACnC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,oBAAoB;;;;;;;;ICU7CC,EAAA,CAAAC,cAAA,cAAkE;IAChED,EAAA,CAAAE,SAAA,cAAkC;IAClCF,EAAA,CAAAC,cAAA,cAA6B;IAG3BD,EAFA,CAAAE,SAAA,cAAsC,cACC,cACF;IACrCF,EAAA,CAAAC,cAAA,cAA2B;IAEzBD,EADA,CAAAE,SAAA,cAAgC,cACA;IAGtCF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;;IAZRH,EADF,CAAAC,cAAA,cAAiD,cACrB;IACxBD,EAAA,CAAAI,UAAA,IAAAC,mDAAA,kBAAkE;IAatEL,EADE,CAAAG,YAAA,EAAM,EACF;;;IAboBH,EAAA,CAAAM,SAAA,GAAU;IAAVN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAU;;;;;;IAgBpCT,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,SAAA,mBAA4D;IAC5DF,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAU,MAAA,GAAW;IAAAV,EAAA,CAAAG,YAAA,EAAI;IACxCH,EAAA,CAAAC,cAAA,iBAA8C;IAApBD,EAAA,CAAAW,UAAA,mBAAAC,sEAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAG,OAAA,EAAS;IAAA,EAAC;IAC3ClB,EAAA,CAAAE,SAAA,mBAAoC;IACpCF,EAAA,CAAAU,MAAA,kBACF;IACFV,EADE,CAAAG,YAAA,EAAS,EACL;;;;IALqBH,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAmB,iBAAA,CAAAJ,MAAA,CAAAK,KAAA,CAAW;;;;;IAiC5BpB,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAAE,SAAA,mBAAsC;IACxCF,EAAA,CAAAG,YAAA,EAAM;;;;;IAyBFH,EAAA,CAAAC,cAAA,eAA+E;IAC7ED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAqB,kBAAA,MAAAC,QAAA,MACF;;;;;IACAtB,EAAA,CAAAC,cAAA,eAAkE;IAChED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAqB,kBAAA,OAAAE,aAAA,CAAAC,SAAA,CAAAC,MAAA,UACF;;;;;;IA7CRzB,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAW,UAAA,mBAAAe,0EAAA;MAAA,MAAAH,aAAA,GAAAvB,EAAA,CAAAa,aAAA,CAAAc,GAAA,EAAAC,SAAA;MAAA,MAAAb,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAc,iBAAA,CAAAN,aAAA,CAA6B;IAAA,EAAC;IAGvCvB,EAAA,CAAAC,cAAA,cAAyC;IACvCD,EAAA,CAAAE,SAAA,cAKE;IACFF,EAAA,CAAAI,UAAA,IAAA0B,0DAAA,kBAA0D;IAG5D9B,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,cAA6B,aACC;IAAAD,EAAA,CAAAU,MAAA,GAAyB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAC1DH,EAAA,CAAAC,cAAA,YAAoB;IAAAD,EAAA,CAAAU,MAAA,GAA8B;IAAAV,EAAA,CAAAG,YAAA,EAAI;IACtDH,EAAA,CAAAC,cAAA,YAAoB;IAAAD,EAAA,CAAAU,MAAA,IAAyB;IAAAV,EAAA,CAAAG,YAAA,EAAI;IAK7CH,EAFJ,CAAAC,cAAA,eAA6B,eACT,gBACS;IAAAD,EAAA,CAAAU,MAAA,IAAmD;IAAAV,EAAA,CAAAG,YAAA,EAAO;IACnFH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAU,MAAA,iBAAS;IACpCV,EADoC,CAAAG,YAAA,EAAO,EACrC;IAEJH,EADF,CAAAC,cAAA,eAAkB,gBACS;IAAAD,EAAA,CAAAU,MAAA,IAAgC;IAAAV,EAAA,CAAAG,YAAA,EAAO;IAChEH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAU,MAAA,kBAAU;IAEvCV,EAFuC,CAAAG,YAAA,EAAO,EACtC,EACF;IAIJH,EADF,CAAAC,cAAA,eAAwB,gBACK;IAAAD,EAAA,CAAAU,MAAA,mBAAW;IAAAV,EAAA,CAAAG,YAAA,EAAO;IAC7CH,EAAA,CAAAC,cAAA,eAAyB;IAIvBD,EAHA,CAAAI,UAAA,KAAA2B,4DAAA,mBAA+E,KAAAC,4DAAA,mBAGb;IAKxEhC,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAGNH,EAAA,CAAAC,cAAA,kBAIC;IADCD,EAAA,CAAAW,UAAA,mBAAAsB,8EAAAC,MAAA;MAAA,MAAAX,aAAA,GAAAvB,EAAA,CAAAa,aAAA,CAAAc,GAAA,EAAAC,SAAA;MAAA,MAAAb,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAoB,kBAAA,CAAAZ,aAAA,EAAAW,MAAA,CAAsC;IAAA,EAAC;IAEhDlC,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,IAAqD;IAAAV,EAAA,CAAAG,YAAA,EAAO;IAClEH,EAAA,CAAAE,SAAA,oBAA2E;IAE/EF,EADE,CAAAG,YAAA,EAAS,EACL;;;;;IAnDAH,EAAA,CAAAM,SAAA,GAAyB;IACzBN,EADA,CAAAO,UAAA,QAAAgB,aAAA,CAAAa,MAAA,EAAApC,EAAA,CAAAqC,aAAA,CAAyB,QAAAd,aAAA,CAAAe,QAAA,CACE;IAIvBtC,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAO,UAAA,SAAAgB,aAAA,CAAAgB,UAAA,CAA2B;IAOLvC,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAmB,iBAAA,CAAAI,aAAA,CAAAe,QAAA,CAAyB;IACjCtC,EAAA,CAAAM,SAAA,GAA8B;IAA9BN,EAAA,CAAAqB,kBAAA,MAAAE,aAAA,CAAAiB,QAAA,KAA8B;IAC9BxC,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAmB,iBAAA,CAAAI,aAAA,CAAAkB,QAAA,CAAyB;IAKhBzC,EAAA,CAAAM,SAAA,GAAmD;IAAnDN,EAAA,CAAAmB,iBAAA,CAAAJ,MAAA,CAAA2B,mBAAA,CAAAnB,aAAA,CAAAoB,aAAA,EAAmD;IAInD3C,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAqB,kBAAA,KAAAE,aAAA,CAAAqB,cAAA,MAAgC;IASjC5C,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAAO,UAAA,YAAAgB,aAAA,CAAAC,SAAA,CAAAqB,KAAA,OAAmC;IAGpD7C,EAAA,CAAAM,SAAA,EAAqC;IAArCN,EAAA,CAAAO,UAAA,SAAAgB,aAAA,CAAAC,SAAA,CAAAC,MAAA,KAAqC;IAUhDzB,EAAA,CAAAM,SAAA,EAA0C;IAA1CN,EAAA,CAAA8C,WAAA,cAAAvB,aAAA,CAAAwB,WAAA,CAA0C;IAGpC/C,EAAA,CAAAM,SAAA,GAAqD;IAArDN,EAAA,CAAAmB,iBAAA,CAAAI,aAAA,CAAAwB,WAAA,0BAAqD;IACjD/C,EAAA,CAAAM,SAAA,EAAqD;IAArDN,EAAA,CAAAO,UAAA,SAAAgB,aAAA,CAAAwB,WAAA,uBAAqD;;;;;;IAnEvE/C,EAFF,CAAAC,cAAA,cAAoG,iBAER;IAAtDD,EAAA,CAAAW,UAAA,mBAAAqC,uEAAA;MAAAhD,EAAA,CAAAa,aAAA,CAAAoC,GAAA;MAAA,MAAAlC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAmC,SAAA,EAAW;IAAA,EAAC;IACvDlD,EAAA,CAAAE,SAAA,mBAAyC;IAC3CF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAgG;IAA5DD,EAAA,CAAAW,UAAA,mBAAAwC,uEAAA;MAAAnD,EAAA,CAAAa,aAAA,CAAAoC,GAAA;MAAA,MAAAlC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAqC,SAAA,EAAW;IAAA,EAAC;IACvDpD,EAAA,CAAAE,SAAA,mBAA4C;IAC9CF,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,cAAyG;IAAjCD,EAAhC,CAAAW,UAAA,wBAAA0C,yEAAA;MAAArD,EAAA,CAAAa,aAAA,CAAAoC,GAAA;MAAA,MAAAlC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAcF,MAAA,CAAAuC,cAAA,EAAgB;IAAA,EAAC,wBAAAC,yEAAA;MAAAvD,EAAA,CAAAa,aAAA,CAAAoC,GAAA;MAAA,MAAAlC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAeF,MAAA,CAAAyC,eAAA,EAAiB;IAAA,EAAC;IACtGxD,EAAA,CAAAC,cAAA,cAAwF;IACtFD,EAAA,CAAAI,UAAA,IAAAqD,oDAAA,oBAIC;IA0DPzD,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAxEsDH,EAAA,CAAAM,SAAA,EAA+B;IAA/BN,EAAA,CAAAO,UAAA,aAAAQ,MAAA,CAAA2C,YAAA,OAA+B;IAG/B1D,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAAO,UAAA,aAAAQ,MAAA,CAAA2C,YAAA,IAAA3C,MAAA,CAAA4C,QAAA,CAAqC;IAM7D3D,EAAA,CAAAM,SAAA,GAAuD;IAAvDN,EAAA,CAAA4D,WAAA,8BAAA7C,MAAA,CAAA8C,WAAA,SAAuD;IAE5D7D,EAAA,CAAAM,SAAA,EAAmB;IAAAN,EAAnB,CAAAO,UAAA,YAAAQ,MAAA,CAAA+C,cAAA,CAAmB,iBAAA/C,MAAA,CAAAgD,mBAAA,CAA4B;;;;;IAgE9E/D,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAE,SAAA,mBAA4D;IAC5DF,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAU,MAAA,qBAAc;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAU,MAAA,mDAA4C;IACvEV,EADuE,CAAAG,YAAA,EAAI,EACrE;;;AD9FR,OAAM,MAAO6D,8BAA8B;EAmBzCC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAlB1B,KAAAJ,cAAc,GAAoB,EAAE;IACpC,KAAAK,SAAS,GAAG,IAAI;IAChB,KAAA/C,KAAK,GAAkB,IAAI;IACnB,KAAAgD,YAAY,GAAiB,IAAIvE,YAAY,EAAE;IAEvD;IACA,KAAA6D,YAAY,GAAG,CAAC;IAChB,KAAAG,WAAW,GAAG,CAAC;IACf,KAAAQ,SAAS,GAAG,GAAG,CAAC,CAAC;IACjB,KAAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAClB,KAAAX,QAAQ,GAAG,CAAC;IAIZ,KAAAY,cAAc,GAAG,IAAI,CAAC,CAAC;IACvB,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,QAAQ,GAAG,KAAK;EAEqB;EAErCC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,wBAAwB,EAAE;IAC/B,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACV,YAAY,CAACW,WAAW,EAAE;IAC/B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEcL,kBAAkBA,CAAA;IAAA,IAAAM,KAAA;IAAA,OAAAC,iBAAA;MAC9B,IAAI;QACFD,KAAI,CAACd,SAAS,GAAG,IAAI;QACrBc,KAAI,CAAC7D,KAAK,GAAG,IAAI;QAEjB;QACA6D,KAAI,CAACnB,cAAc,GAAG,CACpB;UACEqB,EAAE,EAAE,GAAG;UACP3C,QAAQ,EAAE,mBAAmB;UAC7BF,QAAQ,EAAE,cAAc;UACxBF,MAAM,EAAE,6FAA6F;UACrGO,aAAa,EAAE,OAAO;UACtBF,QAAQ,EAAE,cAAc;UACxBF,UAAU,EAAE,IAAI;UAChBQ,WAAW,EAAE,KAAK;UAClBH,cAAc,EAAE,GAAG;UACnBwC,WAAW,EAAE,EAAE;UACf5D,SAAS,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS;SACxC,EACD;UACE2D,EAAE,EAAE,GAAG;UACP3C,QAAQ,EAAE,mBAAmB;UAC7BF,QAAQ,EAAE,cAAc;UACxBF,MAAM,EAAE,6FAA6F;UACrGO,aAAa,EAAE,OAAO;UACtBF,QAAQ,EAAE,YAAY;UACtBF,UAAU,EAAE,IAAI;UAChBQ,WAAW,EAAE,KAAK;UAClBH,cAAc,EAAE,IAAI;UACpBwC,WAAW,EAAE,EAAE;UACf5D,SAAS,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,SAAS;SACxC,EACD;UACE2D,EAAE,EAAE,GAAG;UACP3C,QAAQ,EAAE,cAAc;UACxBF,QAAQ,EAAE,cAAc;UACxBF,MAAM,EAAE,6FAA6F;UACrGO,aAAa,EAAE,OAAO;UACtBF,QAAQ,EAAE,WAAW;UACrBF,UAAU,EAAE,IAAI;UAChBQ,WAAW,EAAE,KAAK;UAClBH,cAAc,EAAE,GAAG;UACnBwC,WAAW,EAAE,EAAE;UACf5D,SAAS,EAAE,CAAC,aAAa,EAAE,eAAe,EAAE,MAAM;SACnD,EACD;UACE2D,EAAE,EAAE,GAAG;UACP3C,QAAQ,EAAE,kBAAkB;UAC5BF,QAAQ,EAAE,aAAa;UACvBF,MAAM,EAAE,0FAA0F;UAClGO,aAAa,EAAE,OAAO;UACtBF,QAAQ,EAAE,QAAQ;UAClBF,UAAU,EAAE,IAAI;UAChBQ,WAAW,EAAE,KAAK;UAClBH,cAAc,EAAE,GAAG;UACnBwC,WAAW,EAAE,EAAE;UACf5D,SAAS,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,eAAe;SAC9C,EACD;UACE2D,EAAE,EAAE,GAAG;UACP3C,QAAQ,EAAE,kBAAkB;UAC5BF,QAAQ,EAAE,YAAY;UACtBF,MAAM,EAAE,6FAA6F;UACrGO,aAAa,EAAE,MAAM;UACrBF,QAAQ,EAAE,YAAY;UACtBF,UAAU,EAAE,IAAI;UAChBQ,WAAW,EAAE,KAAK;UAClBH,cAAc,EAAE,IAAI;UACpBwC,WAAW,EAAE,EAAE;UACf5D,SAAS,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,UAAU;SACxC,EACD;UACE2D,EAAE,EAAE,GAAG;UACP3C,QAAQ,EAAE,eAAe;UACzBF,QAAQ,EAAE,aAAa;UACvBF,MAAM,EAAE,6FAA6F;UACrGO,aAAa,EAAE,MAAM;UACrBF,QAAQ,EAAE,SAAS;UACnBF,UAAU,EAAE,IAAI;UAChBQ,WAAW,EAAE,KAAK;UAClBH,cAAc,EAAE,IAAI;UACpBwC,WAAW,EAAE,EAAE;UACf5D,SAAS,EAAE,CAAC,gBAAgB,EAAE,cAAc,EAAE,QAAQ;SACvD,CACF;QAEDyD,KAAI,CAACd,SAAS,GAAG,KAAK;QACtBc,KAAI,CAACI,6BAA6B,EAAE;OACrC,CAAC,OAAOjE,KAAK,EAAE;QACdkE,OAAO,CAAClE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD6D,KAAI,CAAC7D,KAAK,GAAG,gCAAgC;QAC7C6D,KAAI,CAACd,SAAS,GAAG,KAAK;;IACvB;EACH;EAEAtC,iBAAiBA,CAAC0D,UAAyB;IACzC,IAAI,CAACrB,MAAM,CAACsB,QAAQ,CAAC,CAAC,UAAU,EAAED,UAAU,CAAC/C,QAAQ,CAAC,CAAC;EACzD;EAEAL,kBAAkBA,CAACoD,UAAyB,EAAEE,KAAY;IACxDA,KAAK,CAACC,eAAe,EAAE;IACvBH,UAAU,CAACxC,WAAW,GAAG,CAACwC,UAAU,CAACxC,WAAW;IAEhD,IAAIwC,UAAU,CAACxC,WAAW,EAAE;MAC1BwC,UAAU,CAAC5C,aAAa,EAAE;KAC3B,MAAM;MACL4C,UAAU,CAAC5C,aAAa,EAAE;;EAE9B;EAEAD,mBAAmBA,CAACiD,KAAa;IAC/B,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEA3E,OAAOA,CAAA;IACL,IAAI,CAACyD,kBAAkB,EAAE;EAC3B;EAEAZ,mBAAmBA,CAAC+B,KAAa,EAAEP,UAAyB;IAC1D,OAAOA,UAAU,CAACJ,EAAE;EACtB;EAEA;EACQY,cAAcA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACvB,aAAa,IAAI,IAAI,CAACC,QAAQ,EAAE;IAE1C,IAAI,CAACO,aAAa,EAAE;IACpB,IAAI,CAACgB,iBAAiB,GAAGC,WAAW,CAAC,MAAK;MACxC,IAAI,CAAC,IAAI,CAACxB,QAAQ,IAAI,IAAI,CAACX,cAAc,CAACrC,MAAM,GAAG,IAAI,CAAC6C,YAAY,EAAE;QACpE,IAAI,CAAC4B,aAAa,EAAE;;IAExB,CAAC,EAAE,IAAI,CAAC3B,cAAc,CAAC;EACzB;EAEQS,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACgB,iBAAiB,EAAE;MAC1BG,aAAa,CAAC,IAAI,CAACH,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;EAEjC;EAEQE,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACxC,YAAY,IAAI,IAAI,CAACC,QAAQ,EAAE;MACtC,IAAI,CAACD,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;IAErB,IAAI,CAAC0C,iBAAiB,EAAE;EAC1B;EAEA9C,cAAcA,CAAA;IACZ,IAAI,CAACmB,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACO,aAAa,EAAE;EACtB;EAEAxB,eAAeA,CAAA;IACb,IAAI,CAACiB,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACsB,cAAc,EAAE;EACvB;EAEA;EACQnB,wBAAwBA,CAAA;IAC9B,MAAMyB,KAAK,GAAGC,MAAM,CAACC,UAAU;IAC/B,IAAIF,KAAK,IAAI,GAAG,EAAE;MAChB,IAAI,CAAChC,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAI+B,KAAK,IAAI,GAAG,EAAE;MACvB,IAAI,CAAChC,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAI+B,KAAK,IAAI,IAAI,EAAE;MACxB,IAAI,CAAChC,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACD,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;;IAEvB,IAAI,CAACkC,kBAAkB,EAAE;IACzB,IAAI,CAACJ,iBAAiB,EAAE;EAC1B;EAEQvB,mBAAmBA,CAAA;IACzByB,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MACrC,IAAI,CAAC7B,wBAAwB,EAAE;IACjC,CAAC,CAAC;EACJ;EAEA;EACA4B,kBAAkBA,CAAA;IAChB,IAAI,CAAC7C,QAAQ,GAAG+C,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC7C,cAAc,CAACrC,MAAM,GAAG,IAAI,CAAC6C,YAAY,CAAC;EAC7E;EAEApB,SAASA,CAAA;IACP,IAAI,IAAI,CAACQ,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,IAAI,CAAC0C,iBAAiB,EAAE;MACxB,IAAI,CAACQ,gCAAgC,EAAE;;EAE3C;EAEAxD,SAASA,CAAA;IACP,IAAI,IAAI,CAACM,YAAY,GAAG,IAAI,CAACC,QAAQ,EAAE;MACrC,IAAI,CAACD,YAAY,EAAE;MACnB,IAAI,CAAC0C,iBAAiB,EAAE;MACxB,IAAI,CAACQ,gCAAgC,EAAE;;EAE3C;EAEQR,iBAAiBA,CAAA;IACvB,IAAI,CAACvC,WAAW,GAAG,CAAC,IAAI,CAACH,YAAY,GAAG,IAAI,CAACW,SAAS;EACxD;EAEQuC,gCAAgCA,CAAA;IACtC,IAAI,CAAC5B,aAAa,EAAE;IACpB6B,UAAU,CAAC,MAAK;MACd,IAAI,CAACd,cAAc,EAAE;IACvB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACQV,6BAA6BA,CAAA;IACnCwB,UAAU,CAAC,MAAK;MACd,IAAI,CAACL,kBAAkB,EAAE;MACzB,IAAI,CAAC9C,YAAY,GAAG,CAAC;MACrB,IAAI,CAACG,WAAW,GAAG,CAAC;MACpB,IAAI,CAACkC,cAAc,EAAE;IACvB,CAAC,EAAE,GAAG,CAAC;EACT;;;uBAxQW/B,8BAA8B,EAAAhE,EAAA,CAAA8G,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAA9BhD,8BAA8B;MAAAiD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAnH,EAAA,CAAAoH,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCxBrC1H,EAJN,CAAAC,cAAA,aAAuC,aAET,aACE,YACA;UACxBD,EAAA,CAAAE,SAAA,kBAAoD;UACpDF,EAAA,CAAAU,MAAA,gCACF;UAAAV,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAU,MAAA,8BAAuB;UAEvDV,EAFuD,CAAAG,YAAA,EAAI,EACnD,EACF;UA4GNH,EAzGA,CAAAI,UAAA,IAAAwH,6CAAA,iBAAiD,IAAAC,6CAAA,iBAkBQ,KAAAC,8CAAA,iBAU2C,KAAAC,8CAAA,iBA6EX;UAK3F/H,EAAA,CAAAG,YAAA,EAAM;;;UA9GEH,EAAA,CAAAM,SAAA,GAAe;UAAfN,EAAA,CAAAO,UAAA,SAAAoH,GAAA,CAAAxD,SAAA,CAAe;UAkBfnE,EAAA,CAAAM,SAAA,EAAyB;UAAzBN,EAAA,CAAAO,UAAA,SAAAoH,GAAA,CAAAvG,KAAA,KAAAuG,GAAA,CAAAxD,SAAA,CAAyB;UAUzBnE,EAAA,CAAAM,SAAA,EAAuD;UAAvDN,EAAA,CAAAO,UAAA,UAAAoH,GAAA,CAAAxD,SAAA,KAAAwD,GAAA,CAAAvG,KAAA,IAAAuG,GAAA,CAAA7D,cAAA,CAAArC,MAAA,KAAuD;UA6EvDzB,EAAA,CAAAM,SAAA,EAAyD;UAAzDN,EAAA,CAAAO,UAAA,UAAAoH,GAAA,CAAAxD,SAAA,KAAAwD,GAAA,CAAAvG,KAAA,IAAAuG,GAAA,CAAA7D,cAAA,CAAArC,MAAA,OAAyD;;;qBD9FrD7B,YAAY,EAAAoI,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEpI,WAAW,EAAAqI,EAAA,CAAAC,OAAA,EAAErI,cAAc;MAAAsI,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}