{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { Validators, ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../../core/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction RegisterComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtext(1, \" Full name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_div_13_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Username is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_div_13_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Username must be at least 3 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtemplate(1, RegisterComponent_div_13_span_1_Template, 2, 0, \"span\", 20)(2, RegisterComponent_div_13_span_2_Template, 2, 0, \"span\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r0.registerForm.get(\"username\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.registerForm.get(\"username\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"minlength\"]);\n  }\n}\nfunction RegisterComponent_div_16_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_div_16_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Please enter a valid email\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtemplate(1, RegisterComponent_div_16_span_1_Template, 2, 0, \"span\", 20)(2, RegisterComponent_div_16_span_2_Template, 2, 0, \"span\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r0.registerForm.get(\"email\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.registerForm.get(\"email\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"email\"]);\n  }\n}\nfunction RegisterComponent_div_19_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_div_19_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Password must be at least 6 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtemplate(1, RegisterComponent_div_19_span_1_Template, 2, 0, \"span\", 20)(2, RegisterComponent_div_19_span_2_Template, 2, 0, \"span\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r0.registerForm.get(\"password\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.registerForm.get(\"password\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"minlength\"]);\n  }\n}\nfunction RegisterComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 5);\n    i0.ɵɵelement(2, \"input\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 5);\n    i0.ɵɵelement(4, \"input\", 23);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RegisterComponent_span_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 24);\n  }\n}\nfunction RegisterComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.errorMessage, \" \");\n  }\n}\nexport let RegisterComponent = /*#__PURE__*/(() => {\n  class RegisterComponent {\n    constructor(fb, authService, router) {\n      this.fb = fb;\n      this.authService = authService;\n      this.router = router;\n      this.loading = false;\n      this.errorMessage = '';\n      this.registerForm = this.fb.group({\n        fullName: ['', Validators.required],\n        username: ['', [Validators.required, Validators.minLength(3)]],\n        email: ['', [Validators.required, Validators.email]],\n        password: ['', [Validators.required, Validators.minLength(6)]],\n        role: ['customer', Validators.required],\n        businessName: [''],\n        businessType: ['']\n      });\n    }\n    onSubmit() {\n      if (this.registerForm.valid) {\n        this.loading = true;\n        this.errorMessage = '';\n        const formData = {\n          ...this.registerForm.value\n        };\n        if (formData.role === 'vendor') {\n          formData.vendorInfo = {\n            businessName: formData.businessName,\n            businessType: formData.businessType\n          };\n        }\n        delete formData.businessName;\n        delete formData.businessType;\n        this.authService.register(formData).subscribe({\n          next: response => {\n            this.loading = false;\n            this.router.navigate(['/home']);\n          },\n          error: error => {\n            this.loading = false;\n            this.errorMessage = error.error?.message || 'Registration failed. Please try again.';\n          }\n        });\n      }\n    }\n    static {\n      this.ɵfac = function RegisterComponent_Factory(t) {\n        return new (t || RegisterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: RegisterComponent,\n        selectors: [[\"app-register\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 36,\n        vars: 18,\n        consts: [[1, \"auth-container\"], [1, \"auth-card\"], [1, \"logo\"], [1, \"gradient-text\"], [1, \"auth-form\", 3, \"ngSubmit\", \"formGroup\"], [1, \"form-group\"], [\"type\", \"text\", \"formControlName\", \"fullName\", \"placeholder\", \"Full Name\", 1, \"form-control\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"username\", \"placeholder\", \"Username\", 1, \"form-control\"], [\"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Email\", 1, \"form-control\"], [\"type\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Password\", 1, \"form-control\"], [\"formControlName\", \"role\", 1, \"form-control\"], [\"value\", \"customer\"], [\"value\", \"vendor\"], [\"class\", \"vendor-info\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"btn-primary\", \"auth-btn\", 3, \"disabled\"], [\"class\", \"loading-spinner\", 4, \"ngIf\"], [1, \"auth-link\"], [\"routerLink\", \"/auth/login\"], [1, \"error-message\"], [4, \"ngIf\"], [1, \"vendor-info\"], [\"type\", \"text\", \"formControlName\", \"businessName\", \"placeholder\", \"Business Name\", 1, \"form-control\"], [\"type\", \"text\", \"formControlName\", \"businessType\", \"placeholder\", \"Business Type\", 1, \"form-control\"], [1, \"loading-spinner\"]],\n        template: function RegisterComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n            i0.ɵɵtext(4, \"DFashion\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p\");\n            i0.ɵɵtext(6, \"Join the Social E-commerce Revolution\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"form\", 4);\n            i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_Template_form_ngSubmit_7_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(8, \"div\", 5);\n            i0.ɵɵelement(9, \"input\", 6);\n            i0.ɵɵtemplate(10, RegisterComponent_div_10_Template, 2, 0, \"div\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"div\", 5);\n            i0.ɵɵelement(12, \"input\", 8);\n            i0.ɵɵtemplate(13, RegisterComponent_div_13_Template, 3, 2, \"div\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"div\", 5);\n            i0.ɵɵelement(15, \"input\", 9);\n            i0.ɵɵtemplate(16, RegisterComponent_div_16_Template, 3, 2, \"div\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"div\", 5);\n            i0.ɵɵelement(18, \"input\", 10);\n            i0.ɵɵtemplate(19, RegisterComponent_div_19_Template, 3, 2, \"div\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"div\", 5)(21, \"select\", 11)(22, \"option\", 12);\n            i0.ɵɵtext(23, \"Customer\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"option\", 13);\n            i0.ɵɵtext(25, \"Vendor\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(26, RegisterComponent_div_26_Template, 5, 0, \"div\", 14);\n            i0.ɵɵelementStart(27, \"button\", 15);\n            i0.ɵɵtemplate(28, RegisterComponent_span_28_Template, 1, 0, \"span\", 16);\n            i0.ɵɵtext(29);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(30, RegisterComponent_div_30_Template, 2, 1, \"div\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(31, \"div\", 17)(32, \"p\");\n            i0.ɵɵtext(33, \"Already have an account? \");\n            i0.ɵɵelementStart(34, \"a\", 18);\n            i0.ɵɵtext(35, \"Sign in\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            let tmp_1_0;\n            let tmp_2_0;\n            let tmp_3_0;\n            let tmp_4_0;\n            let tmp_5_0;\n            let tmp_6_0;\n            let tmp_7_0;\n            let tmp_8_0;\n            let tmp_9_0;\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"formGroup\", ctx.registerForm);\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"error\", ((tmp_1_0 = ctx.registerForm.get(\"fullName\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.registerForm.get(\"fullName\")) == null ? null : tmp_1_0.touched));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.registerForm.get(\"fullName\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.registerForm.get(\"fullName\")) == null ? null : tmp_2_0.touched));\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"error\", ((tmp_3_0 = ctx.registerForm.get(\"username\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.registerForm.get(\"username\")) == null ? null : tmp_3_0.touched));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.registerForm.get(\"username\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.registerForm.get(\"username\")) == null ? null : tmp_4_0.touched));\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"error\", ((tmp_5_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_5_0.touched));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_6_0.touched));\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"error\", ((tmp_7_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_7_0.touched));\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_8_0.touched));\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_9_0 = ctx.registerForm.get(\"role\")) == null ? null : tmp_9_0.value) === \"vendor\");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"disabled\", ctx.registerForm.invalid || ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵtextInterpolate1(\" \", ctx.loading ? \"Creating Account...\" : \"Create Account\", \" \");\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          }\n        },\n        dependencies: [CommonModule, i4.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, RouterModule, i3.RouterLink],\n        styles: [\".auth-container[_ngcontent-%COMP%]{min-height:100vh;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#667eea,#764ba2);padding:20px}.auth-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;padding:40px;box-shadow:0 10px 25px #0000001a;width:100%;max-width:400px}.logo[_ngcontent-%COMP%]{text-align:center;margin-bottom:32px}.logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:32px;font-weight:700;margin-bottom:8px}.logo[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#8e8e8e;font-size:14px}.auth-form[_ngcontent-%COMP%]{margin-bottom:24px}.form-group[_ngcontent-%COMP%]{margin-bottom:20px}.form-control[_ngcontent-%COMP%]{width:100%;padding:12px 16px;border:1px solid #dbdbdb;border-radius:8px;font-size:14px;outline:none;transition:all .2s}.form-control[_ngcontent-%COMP%]:focus{border-color:var(--primary-color);box-shadow:0 0 0 3px #0095f61a}.form-control.error[_ngcontent-%COMP%]{border-color:#ef4444}.error-message[_ngcontent-%COMP%]{color:#ef4444;font-size:12px;margin-top:4px}.auth-btn[_ngcontent-%COMP%]{width:100%;padding:12px;font-size:16px;font-weight:600;margin-bottom:16px;display:flex;align-items:center;justify-content:center;gap:8px}.auth-btn[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.vendor-info[_ngcontent-%COMP%]{padding:16px;background:#f8fafc;border-radius:8px;margin-bottom:20px}.auth-link[_ngcontent-%COMP%]{text-align:center}.auth-link[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;color:#8e8e8e}.auth-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:var(--primary-color);text-decoration:none;font-weight:600}.auth-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}@media (max-width: 480px){.auth-card[_ngcontent-%COMP%]{padding:24px}.logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:28px}}\"]\n      });\n    }\n  }\n  return RegisterComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}