{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class TrendingService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = environment.apiUrl;\n    // BehaviorSubjects for caching\n    this.trendingProductsSubject = new BehaviorSubject([]);\n    this.suggestedProductsSubject = new BehaviorSubject([]);\n    this.newArrivalsSubject = new BehaviorSubject([]);\n    this.featuredBrandsSubject = new BehaviorSubject([]);\n    this.influencersSubject = new BehaviorSubject([]);\n    // Public observables\n    this.trendingProducts$ = this.trendingProductsSubject.asObservable();\n    this.suggestedProducts$ = this.suggestedProductsSubject.asObservable();\n    this.newArrivals$ = this.newArrivalsSubject.asObservable();\n    this.featuredBrands$ = this.featuredBrandsSubject.asObservable();\n    this.influencers$ = this.influencersSubject.asObservable();\n  }\n  // Get trending products\n  getTrendingProducts(page = 1, limit = 12) {\n    return this.http.get(`${this.API_URL}/v1/products/trending`, {\n      params: {\n        page: page.toString(),\n        limit: limit.toString()\n      }\n    });\n  }\n  // Get suggested products\n  getSuggestedProducts(page = 1, limit = 12) {\n    return this.http.get(`${this.API_URL}/v1/products/suggested`, {\n      params: {\n        page: page.toString(),\n        limit: limit.toString()\n      }\n    });\n  }\n  // Get new arrivals\n  getNewArrivals(page = 1, limit = 12) {\n    return this.http.get(`${this.API_URL}/v1/products/new-arrivals`, {\n      params: {\n        page: page.toString(),\n        limit: limit.toString()\n      }\n    });\n  }\n  // Get featured brands\n  getFeaturedBrands() {\n    return this.http.get(`${this.API_URL}/v1/products/featured-brands`);\n  }\n  // Get influencers\n  getInfluencers(page = 1, limit = 10) {\n    return this.http.get(`${this.API_URL}/v1/users/influencers`, {\n      params: {\n        page: page.toString(),\n        limit: limit.toString()\n      }\n    });\n  }\n  // Load and cache trending products\n  loadTrendingProducts() {\n    var _this = this;\n    return _asyncToGenerator(function* (page = 1, limit = 12) {\n      try {\n        const response = yield _this.getTrendingProducts(page, limit).toPromise();\n        if (response?.success && response?.products) {\n          _this.trendingProductsSubject.next(response.products);\n        }\n      } catch (error) {\n        console.error('Error loading trending products:', error);\n      }\n    }).apply(this, arguments);\n  }\n  // Load and cache suggested products\n  loadSuggestedProducts() {\n    var _this2 = this;\n    return _asyncToGenerator(function* (page = 1, limit = 12) {\n      try {\n        const response = yield _this2.getSuggestedProducts(page, limit).toPromise();\n        if (response?.success && response?.products) {\n          _this2.suggestedProductsSubject.next(response.products);\n        }\n      } catch (error) {\n        console.error('Error loading suggested products:', error);\n      }\n    }).apply(this, arguments);\n  }\n  // Load and cache new arrivals\n  loadNewArrivals() {\n    var _this3 = this;\n    return _asyncToGenerator(function* (page = 1, limit = 12) {\n      try {\n        const response = yield _this3.getNewArrivals(page, limit).toPromise();\n        if (response?.success && response?.products) {\n          _this3.newArrivalsSubject.next(response.products);\n        }\n      } catch (error) {\n        console.error('Error loading new arrivals:', error);\n      }\n    }).apply(this, arguments);\n  }\n  // Load and cache featured brands\n  loadFeaturedBrands() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const response = yield _this4.getFeaturedBrands().toPromise();\n        if (response?.success && response?.brands) {\n          _this4.featuredBrandsSubject.next(response.brands);\n        }\n      } catch (error) {\n        console.error('Error loading featured brands:', error);\n      }\n    })();\n  }\n  // Load and cache influencers\n  loadInfluencers() {\n    var _this5 = this;\n    return _asyncToGenerator(function* (page = 1, limit = 10) {\n      try {\n        const response = yield _this5.getInfluencers(page, limit).toPromise();\n        if (response?.success && response?.influencers) {\n          _this5.influencersSubject.next(response.influencers);\n        }\n      } catch (error) {\n        console.error('Error loading influencers:', error);\n      }\n    }).apply(this, arguments);\n  }\n  // Clear all cached data\n  clearCache() {\n    this.trendingProductsSubject.next([]);\n    this.suggestedProductsSubject.next([]);\n    this.newArrivalsSubject.next([]);\n    this.featuredBrandsSubject.next([]);\n    this.influencersSubject.next([]);\n  }\n  // Get current cached data\n  getCurrentTrendingProducts() {\n    return this.trendingProductsSubject.value;\n  }\n  getCurrentSuggestedProducts() {\n    return this.suggestedProductsSubject.value;\n  }\n  getCurrentNewArrivals() {\n    return this.newArrivalsSubject.value;\n  }\n  getCurrentFeaturedBrands() {\n    return this.featuredBrandsSubject.value;\n  }\n  getCurrentInfluencers() {\n    return this.influencersSubject.value;\n  }\n  static {\n    this.ɵfac = function TrendingService_Factory(t) {\n      return new (t || TrendingService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TrendingService,\n      factory: TrendingService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "environment", "TrendingService", "constructor", "http", "API_URL", "apiUrl", "trendingProductsSubject", "suggestedProductsSubject", "newArrivalsSubject", "featuredBrandsSubject", "influencersSubject", "trendingProducts$", "asObservable", "suggestedProducts$", "newArrivals$", "featuredBrands$", "influencers$", "getTrendingProducts", "page", "limit", "get", "params", "toString", "getSuggestedProducts", "getNewArrivals", "getFeaturedBrands", "getInfluencers", "loadTrendingProducts", "_this", "_asyncToGenerator", "response", "to<PERSON>romise", "success", "products", "next", "error", "console", "apply", "arguments", "loadSuggestedProducts", "_this2", "loadNewArrivals", "_this3", "loadFeaturedBrands", "_this4", "brands", "loadInfluencers", "_this5", "influencers", "clearCache", "getCurrentTrendingProducts", "value", "getCurrentSuggestedProducts", "getCurrentNewArrivals", "getCurrentFeaturedBrands", "getCurrentInfluencers", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\core\\services\\trending.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, BehaviorSubject } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport { Product } from '../models/product.model';\n\nexport interface TrendingResponse {\n  success: boolean;\n  products: Product[];\n  pagination: {\n    currentPage: number;\n    totalPages: number;\n    totalItems: number;\n    hasNextPage: boolean;\n    hasPrevPage: boolean;\n  };\n}\n\nexport interface FeaturedBrand {\n  brand: string;\n  productCount: number;\n  avgRating: number;\n  totalViews: number;\n  topProducts: Product[];\n}\n\nexport interface FeaturedBrandsResponse {\n  success: boolean;\n  brands: FeaturedBrand[];\n}\n\nexport interface Influencer {\n  _id: string;\n  username: string;\n  fullName: string;\n  avatar: string;\n  bio: string;\n  socialStats: {\n    followersCount: number;\n    postsCount: number;\n    followingCount: number;\n  };\n  isInfluencer: boolean;\n}\n\nexport interface InfluencersResponse {\n  success: boolean;\n  influencers: Influencer[];\n  pagination: {\n    currentPage: number;\n    totalPages: number;\n    totalItems: number;\n    hasNextPage: boolean;\n    hasPrevPage: boolean;\n  };\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class TrendingService {\n  private readonly API_URL = environment.apiUrl;\n\n  // BehaviorSubjects for caching\n  private trendingProductsSubject = new BehaviorSubject<Product[]>([]);\n  private suggestedProductsSubject = new BehaviorSubject<Product[]>([]);\n  private newArrivalsSubject = new BehaviorSubject<Product[]>([]);\n  private featuredBrandsSubject = new BehaviorSubject<FeaturedBrand[]>([]);\n  private influencersSubject = new BehaviorSubject<Influencer[]>([]);\n\n  // Public observables\n  public trendingProducts$ = this.trendingProductsSubject.asObservable();\n  public suggestedProducts$ = this.suggestedProductsSubject.asObservable();\n  public newArrivals$ = this.newArrivalsSubject.asObservable();\n  public featuredBrands$ = this.featuredBrandsSubject.asObservable();\n  public influencers$ = this.influencersSubject.asObservable();\n\n  constructor(private http: HttpClient) {}\n\n  // Get trending products\n  getTrendingProducts(page: number = 1, limit: number = 12): Observable<TrendingResponse> {\n    return this.http.get<TrendingResponse>(`${this.API_URL}/v1/products/trending`, {\n      params: { page: page.toString(), limit: limit.toString() }\n    });\n  }\n\n  // Get suggested products\n  getSuggestedProducts(page: number = 1, limit: number = 12): Observable<TrendingResponse> {\n    return this.http.get<TrendingResponse>(`${this.API_URL}/v1/products/suggested`, {\n      params: { page: page.toString(), limit: limit.toString() }\n    });\n  }\n\n  // Get new arrivals\n  getNewArrivals(page: number = 1, limit: number = 12): Observable<TrendingResponse> {\n    return this.http.get<TrendingResponse>(`${this.API_URL}/v1/products/new-arrivals`, {\n      params: { page: page.toString(), limit: limit.toString() }\n    });\n  }\n\n  // Get featured brands\n  getFeaturedBrands(): Observable<FeaturedBrandsResponse> {\n    return this.http.get<FeaturedBrandsResponse>(`${this.API_URL}/v1/products/featured-brands`);\n  }\n\n  // Get influencers\n  getInfluencers(page: number = 1, limit: number = 10): Observable<InfluencersResponse> {\n    return this.http.get<InfluencersResponse>(`${this.API_URL}/v1/users/influencers`, {\n      params: { page: page.toString(), limit: limit.toString() }\n    });\n  }\n\n  // Load and cache trending products\n  async loadTrendingProducts(page: number = 1, limit: number = 12): Promise<void> {\n    try {\n      const response = await this.getTrendingProducts(page, limit).toPromise();\n      if (response?.success && response?.products) {\n        this.trendingProductsSubject.next(response.products);\n      }\n    } catch (error) {\n      console.error('Error loading trending products:', error);\n    }\n  }\n\n  // Load and cache suggested products\n  async loadSuggestedProducts(page: number = 1, limit: number = 12): Promise<void> {\n    try {\n      const response = await this.getSuggestedProducts(page, limit).toPromise();\n      if (response?.success && response?.products) {\n        this.suggestedProductsSubject.next(response.products);\n      }\n    } catch (error) {\n      console.error('Error loading suggested products:', error);\n    }\n  }\n\n  // Load and cache new arrivals\n  async loadNewArrivals(page: number = 1, limit: number = 12): Promise<void> {\n    try {\n      const response = await this.getNewArrivals(page, limit).toPromise();\n      if (response?.success && response?.products) {\n        this.newArrivalsSubject.next(response.products);\n      }\n    } catch (error) {\n      console.error('Error loading new arrivals:', error);\n    }\n  }\n\n  // Load and cache featured brands\n  async loadFeaturedBrands(): Promise<void> {\n    try {\n      const response = await this.getFeaturedBrands().toPromise();\n      if (response?.success && response?.brands) {\n        this.featuredBrandsSubject.next(response.brands);\n      }\n    } catch (error) {\n      console.error('Error loading featured brands:', error);\n    }\n  }\n\n  // Load and cache influencers\n  async loadInfluencers(page: number = 1, limit: number = 10): Promise<void> {\n    try {\n      const response = await this.getInfluencers(page, limit).toPromise();\n      if (response?.success && response?.influencers) {\n        this.influencersSubject.next(response.influencers);\n      }\n    } catch (error) {\n      console.error('Error loading influencers:', error);\n    }\n  }\n\n  // Clear all cached data\n  clearCache(): void {\n    this.trendingProductsSubject.next([]);\n    this.suggestedProductsSubject.next([]);\n    this.newArrivalsSubject.next([]);\n    this.featuredBrandsSubject.next([]);\n    this.influencersSubject.next([]);\n  }\n\n  // Get current cached data\n  getCurrentTrendingProducts(): Product[] {\n    return this.trendingProductsSubject.value;\n  }\n\n  getCurrentSuggestedProducts(): Product[] {\n    return this.suggestedProductsSubject.value;\n  }\n\n  getCurrentNewArrivals(): Product[] {\n    return this.newArrivalsSubject.value;\n  }\n\n  getCurrentFeaturedBrands(): FeaturedBrand[] {\n    return this.featuredBrandsSubject.value;\n  }\n\n  getCurrentInfluencers(): Influencer[] {\n    return this.influencersSubject.value;\n  }\n}\n"], "mappings": ";AAEA,SAAqBA,eAAe,QAAQ,MAAM;AAClD,SAASC,WAAW,QAAQ,mCAAmC;;;AAyD/D,OAAM,MAAOC,eAAe;EAiB1BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAhBP,KAAAC,OAAO,GAAGJ,WAAW,CAACK,MAAM;IAE7C;IACQ,KAAAC,uBAAuB,GAAG,IAAIP,eAAe,CAAY,EAAE,CAAC;IAC5D,KAAAQ,wBAAwB,GAAG,IAAIR,eAAe,CAAY,EAAE,CAAC;IAC7D,KAAAS,kBAAkB,GAAG,IAAIT,eAAe,CAAY,EAAE,CAAC;IACvD,KAAAU,qBAAqB,GAAG,IAAIV,eAAe,CAAkB,EAAE,CAAC;IAChE,KAAAW,kBAAkB,GAAG,IAAIX,eAAe,CAAe,EAAE,CAAC;IAElE;IACO,KAAAY,iBAAiB,GAAG,IAAI,CAACL,uBAAuB,CAACM,YAAY,EAAE;IAC/D,KAAAC,kBAAkB,GAAG,IAAI,CAACN,wBAAwB,CAACK,YAAY,EAAE;IACjE,KAAAE,YAAY,GAAG,IAAI,CAACN,kBAAkB,CAACI,YAAY,EAAE;IACrD,KAAAG,eAAe,GAAG,IAAI,CAACN,qBAAqB,CAACG,YAAY,EAAE;IAC3D,KAAAI,YAAY,GAAG,IAAI,CAACN,kBAAkB,CAACE,YAAY,EAAE;EAErB;EAEvC;EACAK,mBAAmBA,CAACC,IAAA,GAAe,CAAC,EAAEC,KAAA,GAAgB,EAAE;IACtD,OAAO,IAAI,CAAChB,IAAI,CAACiB,GAAG,CAAmB,GAAG,IAAI,CAAChB,OAAO,uBAAuB,EAAE;MAC7EiB,MAAM,EAAE;QAAEH,IAAI,EAAEA,IAAI,CAACI,QAAQ,EAAE;QAAEH,KAAK,EAAEA,KAAK,CAACG,QAAQ;MAAE;KACzD,CAAC;EACJ;EAEA;EACAC,oBAAoBA,CAACL,IAAA,GAAe,CAAC,EAAEC,KAAA,GAAgB,EAAE;IACvD,OAAO,IAAI,CAAChB,IAAI,CAACiB,GAAG,CAAmB,GAAG,IAAI,CAAChB,OAAO,wBAAwB,EAAE;MAC9EiB,MAAM,EAAE;QAAEH,IAAI,EAAEA,IAAI,CAACI,QAAQ,EAAE;QAAEH,KAAK,EAAEA,KAAK,CAACG,QAAQ;MAAE;KACzD,CAAC;EACJ;EAEA;EACAE,cAAcA,CAACN,IAAA,GAAe,CAAC,EAAEC,KAAA,GAAgB,EAAE;IACjD,OAAO,IAAI,CAAChB,IAAI,CAACiB,GAAG,CAAmB,GAAG,IAAI,CAAChB,OAAO,2BAA2B,EAAE;MACjFiB,MAAM,EAAE;QAAEH,IAAI,EAAEA,IAAI,CAACI,QAAQ,EAAE;QAAEH,KAAK,EAAEA,KAAK,CAACG,QAAQ;MAAE;KACzD,CAAC;EACJ;EAEA;EACAG,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACtB,IAAI,CAACiB,GAAG,CAAyB,GAAG,IAAI,CAAChB,OAAO,8BAA8B,CAAC;EAC7F;EAEA;EACAsB,cAAcA,CAACR,IAAA,GAAe,CAAC,EAAEC,KAAA,GAAgB,EAAE;IACjD,OAAO,IAAI,CAAChB,IAAI,CAACiB,GAAG,CAAsB,GAAG,IAAI,CAAChB,OAAO,uBAAuB,EAAE;MAChFiB,MAAM,EAAE;QAAEH,IAAI,EAAEA,IAAI,CAACI,QAAQ,EAAE;QAAEH,KAAK,EAAEA,KAAK,CAACG,QAAQ;MAAE;KACzD,CAAC;EACJ;EAEA;EACMK,oBAAoBA,CAAA,EAAqC;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,YAApCX,IAAA,GAAe,CAAC,EAAEC,KAAA,GAAgB,EAAE;MAC7D,IAAI;QACF,MAAMW,QAAQ,SAASF,KAAI,CAACX,mBAAmB,CAACC,IAAI,EAAEC,KAAK,CAAC,CAACY,SAAS,EAAE;QACxE,IAAID,QAAQ,EAAEE,OAAO,IAAIF,QAAQ,EAAEG,QAAQ,EAAE;UAC3CL,KAAI,CAACtB,uBAAuB,CAAC4B,IAAI,CAACJ,QAAQ,CAACG,QAAQ,CAAC;;OAEvD,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;IACzD,GAAAE,KAAA,OAAAC,SAAA;EACH;EAEA;EACMC,qBAAqBA,CAAA,EAAqC;IAAA,IAAAC,MAAA;IAAA,OAAAX,iBAAA,YAApCX,IAAA,GAAe,CAAC,EAAEC,KAAA,GAAgB,EAAE;MAC9D,IAAI;QACF,MAAMW,QAAQ,SAASU,MAAI,CAACjB,oBAAoB,CAACL,IAAI,EAAEC,KAAK,CAAC,CAACY,SAAS,EAAE;QACzE,IAAID,QAAQ,EAAEE,OAAO,IAAIF,QAAQ,EAAEG,QAAQ,EAAE;UAC3CO,MAAI,CAACjC,wBAAwB,CAAC2B,IAAI,CAACJ,QAAQ,CAACG,QAAQ,CAAC;;OAExD,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;;IAC1D,GAAAE,KAAA,OAAAC,SAAA;EACH;EAEA;EACMG,eAAeA,CAAA,EAAqC;IAAA,IAAAC,MAAA;IAAA,OAAAb,iBAAA,YAApCX,IAAA,GAAe,CAAC,EAAEC,KAAA,GAAgB,EAAE;MACxD,IAAI;QACF,MAAMW,QAAQ,SAASY,MAAI,CAAClB,cAAc,CAACN,IAAI,EAAEC,KAAK,CAAC,CAACY,SAAS,EAAE;QACnE,IAAID,QAAQ,EAAEE,OAAO,IAAIF,QAAQ,EAAEG,QAAQ,EAAE;UAC3CS,MAAI,CAAClC,kBAAkB,CAAC0B,IAAI,CAACJ,QAAQ,CAACG,QAAQ,CAAC;;OAElD,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;;IACpD,GAAAE,KAAA,OAAAC,SAAA;EACH;EAEA;EACMK,kBAAkBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAf,iBAAA;MACtB,IAAI;QACF,MAAMC,QAAQ,SAASc,MAAI,CAACnB,iBAAiB,EAAE,CAACM,SAAS,EAAE;QAC3D,IAAID,QAAQ,EAAEE,OAAO,IAAIF,QAAQ,EAAEe,MAAM,EAAE;UACzCD,MAAI,CAACnC,qBAAqB,CAACyB,IAAI,CAACJ,QAAQ,CAACe,MAAM,CAAC;;OAEnD,CAAC,OAAOV,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;;IACvD;EACH;EAEA;EACMW,eAAeA,CAAA,EAAqC;IAAA,IAAAC,MAAA;IAAA,OAAAlB,iBAAA,YAApCX,IAAA,GAAe,CAAC,EAAEC,KAAA,GAAgB,EAAE;MACxD,IAAI;QACF,MAAMW,QAAQ,SAASiB,MAAI,CAACrB,cAAc,CAACR,IAAI,EAAEC,KAAK,CAAC,CAACY,SAAS,EAAE;QACnE,IAAID,QAAQ,EAAEE,OAAO,IAAIF,QAAQ,EAAEkB,WAAW,EAAE;UAC9CD,MAAI,CAACrC,kBAAkB,CAACwB,IAAI,CAACJ,QAAQ,CAACkB,WAAW,CAAC;;OAErD,CAAC,OAAOb,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;;IACnD,GAAAE,KAAA,OAAAC,SAAA;EACH;EAEA;EACAW,UAAUA,CAAA;IACR,IAAI,CAAC3C,uBAAuB,CAAC4B,IAAI,CAAC,EAAE,CAAC;IACrC,IAAI,CAAC3B,wBAAwB,CAAC2B,IAAI,CAAC,EAAE,CAAC;IACtC,IAAI,CAAC1B,kBAAkB,CAAC0B,IAAI,CAAC,EAAE,CAAC;IAChC,IAAI,CAACzB,qBAAqB,CAACyB,IAAI,CAAC,EAAE,CAAC;IACnC,IAAI,CAACxB,kBAAkB,CAACwB,IAAI,CAAC,EAAE,CAAC;EAClC;EAEA;EACAgB,0BAA0BA,CAAA;IACxB,OAAO,IAAI,CAAC5C,uBAAuB,CAAC6C,KAAK;EAC3C;EAEAC,2BAA2BA,CAAA;IACzB,OAAO,IAAI,CAAC7C,wBAAwB,CAAC4C,KAAK;EAC5C;EAEAE,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAAC7C,kBAAkB,CAAC2C,KAAK;EACtC;EAEAG,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAAC7C,qBAAqB,CAAC0C,KAAK;EACzC;EAEAI,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAAC7C,kBAAkB,CAACyC,KAAK;EACtC;;;uBA5IWlD,eAAe,EAAAuD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAf1D,eAAe;MAAA2D,OAAA,EAAf3D,eAAe,CAAA4D,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}