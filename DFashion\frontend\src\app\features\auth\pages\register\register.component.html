<div class="auth-container">
  <div class="auth-card">
    <!-- Logo -->
    <div class="logo">
      <h1 class="gradient-text">DFashion</h1>
      <p>Join the Social E-commerce Revolution</p>
    </div>

    <!-- Register Form -->
    <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="auth-form">
      <div class="form-group">
        <input
          type="text"
          formControlName="fullName"
          placeholder="Full Name"
          class="form-control"
          [class.error]="registerForm.get('fullName')?.invalid && registerForm.get('fullName')?.touched"
        >
        <div *ngIf="registerForm.get('fullName')?.invalid && registerForm.get('fullName')?.touched" class="error-message">
          Full name is required
        </div>
      </div>

      <div class="form-group">
        <input
          type="text"
          formControlName="username"
          placeholder="Username"
          class="form-control"
          [class.error]="registerForm.get('username')?.invalid && registerForm.get('username')?.touched"
        >
        <div *ngIf="registerForm.get('username')?.invalid && registerForm.get('username')?.touched" class="error-message">
          <span *ngIf="registerForm.get('username')?.errors?.['required']">Username is required</span>
          <span *ngIf="registerForm.get('username')?.errors?.['minlength']">Username must be at least 3 characters</span>
        </div>
      </div>

      <div class="form-group">
        <input
          type="email"
          formControlName="email"
          placeholder="Email"
          class="form-control"
          [class.error]="registerForm.get('email')?.invalid && registerForm.get('email')?.touched"
        >
        <div *ngIf="registerForm.get('email')?.invalid && registerForm.get('email')?.touched" class="error-message">
          <span *ngIf="registerForm.get('email')?.errors?.['required']">Email is required</span>
          <span *ngIf="registerForm.get('email')?.errors?.['email']">Please enter a valid email</span>
        </div>
      </div>

      <div class="form-group">
        <input
          type="password"
          formControlName="password"
          placeholder="Password"
          class="form-control"
          [class.error]="registerForm.get('password')?.invalid && registerForm.get('password')?.touched"
        >
        <div *ngIf="registerForm.get('password')?.invalid && registerForm.get('password')?.touched" class="error-message">
          <span *ngIf="registerForm.get('password')?.errors?.['required']">Password is required</span>
          <span *ngIf="registerForm.get('password')?.errors?.['minlength']">Password must be at least 6 characters</span>
        </div>
      </div>

      <div class="form-group">
        <select formControlName="role" class="form-control">
          <option value="customer">Customer</option>
          <option value="vendor">Vendor</option>
        </select>
      </div>

      <!-- Vendor Info -->
      <div *ngIf="registerForm.get('role')?.value === 'vendor'" class="vendor-info">
        <div class="form-group">
          <input
            type="text"
            formControlName="businessName"
            placeholder="Business Name"
            class="form-control"
          >
        </div>
        <div class="form-group">
          <input
            type="text"
            formControlName="businessType"
            placeholder="Business Type"
            class="form-control"
          >
        </div>
      </div>

      <button 
        type="submit" 
        class="btn-primary auth-btn"
        [disabled]="registerForm.invalid || loading"
      >
        <span *ngIf="loading" class="loading-spinner"></span>
        {{ loading ? 'Creating Account...' : 'Create Account' }}
      </button>

      <div *ngIf="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>
    </form>

    <!-- Login Link -->
    <div class="auth-link">
      <p>Already have an account? <a routerLink="/auth/login">Sign in</a></p>
    </div>
  </div>
</div>
