{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/trending.service\";\nimport * as i2 from \"../../../../core/services/social-interactions.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3, 4];\nconst _c1 = () => [1, 2, 3];\nconst _c2 = () => [1, 2, 3, 4, 5];\nfunction FeaturedBrandsComponent_div_8_div_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 19);\n  }\n}\nfunction FeaturedBrandsComponent_div_8_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵelement(2, \"div\", 15)(3, \"div\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 17);\n    i0.ɵɵtemplate(5, FeaturedBrandsComponent_div_8_div_2_div_5_Template, 1, 0, \"div\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c1));\n  }\n}\nfunction FeaturedBrandsComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11);\n    i0.ɵɵtemplate(2, FeaturedBrandsComponent_div_8_div_2_Template, 6, 2, \"div\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction FeaturedBrandsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵelement(1, \"ion-icon\", 21);\n    i0.ɵɵelementStart(2, \"p\", 22);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_9_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 24);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r6.originalPrice));\n  }\n}\nfunction FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_ion_icon_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ion-icon\", 52);\n  }\n  if (rf & 2) {\n    const star_r7 = ctx.$implicit;\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r7 <= product_r6.rating.average);\n    i0.ɵɵproperty(\"name\", star_r7 <= product_r6.rating.average ? \"star\" : \"star-outline\");\n  }\n}\nfunction FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_Template_div_click_0_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onProductClick(product_r6, $event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 48);\n    i0.ɵɵelement(2, \"img\", 49);\n    i0.ɵɵelementStart(3, \"div\", 50)(4, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_Template_button_click_4_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onLikeProduct(product_r6, $event));\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_Template_button_click_6_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onShareProduct(product_r6, $event));\n    });\n    i0.ɵɵelement(7, \"ion-icon\", 54);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 55)(9, \"h5\", 56);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 57)(12, \"span\", 58);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_span_14_Template, 2, 1, \"span\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 60)(16, \"div\", 61);\n    i0.ɵɵtemplate(17, FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_ion_icon_17_Template, 1, 3, \"ion-icon\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 63);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r6.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r6.images[0].alt || product_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r1.isProductLiked(product_r6._id));\n    i0.ɵɵattribute(\"aria-label\", \"Like \" + product_r6.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isProductLiked(product_r6._id) ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", \"Share \" + product_r6.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(product_r6.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r6.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r6.originalPrice && product_r6.originalPrice > product_r6.price);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(12, _c2));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r6.rating.count, \")\");\n  }\n}\nfunction FeaturedBrandsComponent_div_10_2_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_10_2_ng_template_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const brand_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onBrandClick(brand_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 30)(2, \"div\", 31)(3, \"h3\", 32);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 33)(6, \"div\", 34);\n    i0.ɵɵelement(7, \"ion-icon\", 35);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 34);\n    i0.ɵɵelement(11, \"ion-icon\", 36);\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 34);\n    i0.ɵɵelement(15, \"ion-icon\", 37);\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(18, \"div\", 38);\n    i0.ɵɵelement(19, \"ion-icon\", 39);\n    i0.ɵɵtext(20, \" Featured \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 40)(22, \"h4\", 41);\n    i0.ɵɵtext(23, \"Top Products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 42);\n    i0.ɵɵtemplate(25, FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_Template, 20, 13, \"div\", 43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 44)(27, \"button\", 45)(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"ion-icon\", 46);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const brand_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(brand_r4.brand);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", brand_r4.productCount, \" Products\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", brand_r4.avgRating, \"/5\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(brand_r4.totalViews), \" Views\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", brand_r4.topProducts)(\"ngForTrackBy\", ctx_r1.trackByProductId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"View All \", brand_r4.brand, \" Products\");\n  }\n}\nfunction FeaturedBrandsComponent_div_10_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, FeaturedBrandsComponent_div_10_2_ng_template_0_Template, 31, 7, \"ng-template\", 28);\n  }\n}\nfunction FeaturedBrandsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"owl-carousel-o\", 26);\n    i0.ɵɵtemplate(2, FeaturedBrandsComponent_div_10_2_Template, 1, 0, null, 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r1.carouselOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.featuredBrands)(\"ngForTrackBy\", ctx_r1.trackByBrandName);\n  }\n}\nfunction FeaturedBrandsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵelement(1, \"ion-icon\", 66);\n    i0.ɵɵelementStart(2, \"h3\", 67);\n    i0.ɵɵtext(3, \"No Featured Brands\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 68);\n    i0.ɵɵtext(5, \"Check back later for featured brand collections\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class FeaturedBrandsComponent {\n  constructor(trendingService, socialService, router) {\n    this.trendingService = trendingService;\n    this.socialService = socialService;\n    this.router = router;\n    this.featuredBrands = [];\n    this.isLoading = true;\n    this.error = null;\n    this.likedProducts = new Set();\n    this.subscription = new Subscription();\n  }\n  ngOnInit() {\n    this.loadFeaturedBrands();\n    this.subscribeFeaturedBrands();\n    this.subscribeLikedProducts();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n  subscribeFeaturedBrands() {\n    this.subscription.add(this.trendingService.featuredBrands$.subscribe(brands => {\n      this.featuredBrands = brands;\n      this.isLoading = false;\n    }));\n  }\n  subscribeLikedProducts() {\n    this.subscription.add(this.socialService.likedProducts$.subscribe(likedProducts => {\n      this.likedProducts = likedProducts;\n    }));\n  }\n  loadFeaturedBrands() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        yield _this.trendingService.loadFeaturedBrands();\n      } catch (error) {\n        console.error('Error loading featured brands:', error);\n        _this.error = 'Failed to load featured brands';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onBrandClick(brand) {\n    this.router.navigate(['/products'], {\n      queryParams: {\n        brand: brand.brand\n      }\n    });\n  }\n  onProductClick(product, event) {\n    event.stopPropagation();\n    this.router.navigate(['/product', product._id]);\n  }\n  onLikeProduct(product, event) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const result = yield _this2.socialService.likeProduct(product._id);\n        if (result.success) {\n          console.log(result.message);\n        } else {\n          console.error('Failed to like product:', result.message);\n        }\n      } catch (error) {\n        console.error('Error liking product:', error);\n      }\n    })();\n  }\n  onShareProduct(product, event) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const productUrl = `${window.location.origin}/product/${product._id}`;\n        yield navigator.clipboard.writeText(productUrl);\n        yield _this3.socialService.shareProduct(product._id, {\n          platform: 'copy_link',\n          message: `Check out this amazing ${product.name} from ${product.brand}!`\n        });\n        console.log('Product link copied to clipboard!');\n      } catch (error) {\n        console.error('Error sharing product:', error);\n      }\n    })();\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  onRetry() {\n    this.loadFeaturedBrands();\n  }\n  trackByBrandName(index, brand) {\n    return brand.brand;\n  }\n  isProductLiked(productId) {\n    return this.likedProducts.has(productId);\n  }\n  trackByProductId(index, product) {\n    return product._id;\n  }\n  static {\n    this.ɵfac = function FeaturedBrandsComponent_Factory(t) {\n      return new (t || FeaturedBrandsComponent)(i0.ɵɵdirectiveInject(i1.TrendingService), i0.ɵɵdirectiveInject(i2.SocialInteractionsService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FeaturedBrandsComponent,\n      selectors: [[\"app-featured-brands\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 12,\n      vars: 4,\n      consts: [[1, \"featured-brands-container\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"diamond\", 1, \"title-icon\"], [1, \"section-subtitle\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"brands-slider-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-brand-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-brand-card\"], [1, \"loading-header\"], [1, \"loading-brand-name\"], [1, \"loading-stats\"], [1, \"loading-products\"], [\"class\", \"loading-product\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-product\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"brands-slider-container\"], [1, \"brands-slider\", 3, \"options\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"carouselSlide\", \"\"], [1, \"brand-card\", 3, \"click\"], [1, \"brand-header\"], [1, \"brand-info\"], [1, \"brand-name\"], [1, \"brand-stats\"], [1, \"stat-item\"], [\"name\", \"bag-outline\"], [\"name\", \"star\"], [\"name\", \"eye-outline\"], [1, \"brand-badge\"], [\"name\", \"diamond\"], [1, \"top-products\"], [1, \"products-title\"], [1, \"products-list\"], [\"class\", \"product-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"view-more-section\"], [1, \"view-more-btn\"], [\"name\", \"chevron-forward\"], [1, \"product-item\", 3, \"click\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"src\", \"alt\"], [1, \"product-actions\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"name\", \"share-outline\"], [1, \"product-details\"], [1, \"product-name\"], [1, \"product-price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"product-rating\"], [1, \"stars\"], [3, \"name\", \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-count\"], [1, \"original-price\"], [1, \"empty-container\"], [\"name\", \"diamond-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"]],\n      template: function FeaturedBrandsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n          i0.ɵɵelement(4, \"ion-icon\", 4);\n          i0.ɵɵtext(5, \" Featured Brands \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \"Top brands with amazing collections\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(8, FeaturedBrandsComponent_div_8_Template, 3, 2, \"div\", 6)(9, FeaturedBrandsComponent_div_9_Template, 7, 1, \"div\", 7)(10, FeaturedBrandsComponent_div_10_Template, 3, 3, \"div\", 8)(11, FeaturedBrandsComponent_div_11_Template, 6, 0, \"div\", 9);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.featuredBrands.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.featuredBrands.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, IonicModule, i5.IonIcon],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.featured-brands-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n  color: white;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 12px;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #ffd700;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.8);\\n  margin: 0;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 20px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  padding: 20px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-header[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-header[_ngcontent-%COMP%]   .loading-brand-name[_ngcontent-%COMP%] {\\n  height: 24px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 8px;\\n  margin-bottom: 8px;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-header[_ngcontent-%COMP%]   .loading-stats[_ngcontent-%COMP%] {\\n  height: 16px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 8px;\\n  width: 70%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-products[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-products[_ngcontent-%COMP%]   .loading-product[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 120px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 12px;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0%, 100% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n.error-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #ff6b6b;\\n  margin-bottom: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: rgba(255, 255, 255, 0.8);\\n  margin-bottom: 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 12px 24px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 auto;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n}\\n\\n.brands-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 20px;\\n}\\n\\n.brand-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 20px;\\n  padding: 24px;\\n  -webkit-backdrop-filter: blur(15px);\\n          backdrop-filter: blur(15px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n  cursor: pointer;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.brand-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\\n  opacity: 0;\\n  transition: opacity 0.4s ease;\\n  pointer-events: none;\\n}\\n.brand-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-12px) scale(1.02);\\n  background: rgba(255, 255, 255, 0.18);\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3), 0 8px 20px rgba(255, 255, 255, 0.1) inset;\\n  border-color: rgba(255, 255, 255, 0.3);\\n}\\n.brand-card[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n\\n.brand-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  margin-bottom: 24px;\\n  position: relative;\\n  z-index: 1;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding-right: 16px;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-name[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 800;\\n  color: white;\\n  margin: 0 0 16px 0;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n  letter-spacing: -0.5px;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  font-size: 13px;\\n  color: rgba(255, 255, 255, 0.9);\\n  background: rgba(255, 255, 255, 0.1);\\n  padding: 6px 12px;\\n  border-radius: 12px;\\n  -webkit-backdrop-filter: blur(5px);\\n          backdrop-filter: blur(5px);\\n  transition: all 0.3s ease;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.15);\\n  transform: translateX(4px);\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #ffd700;\\n  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);\\n  color: #333;\\n  padding: 10px 16px;\\n  border-radius: 25px;\\n  font-size: 13px;\\n  font-weight: 700;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4), 0 2px 4px rgba(0, 0, 0, 0.2);\\n  transition: all 0.3s ease;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5), 0 4px 8px rgba(0, 0, 0, 0.3);\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  animation: _ngcontent-%COMP%_sparkle 2s ease-in-out infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_sparkle {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n}\\n.top-products[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  position: relative;\\n  z-index: 1;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 20px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);\\n}\\n.top-products[_ngcontent-%COMP%]   .products-title[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDD25\\\";\\n  font-size: 20px;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  overflow-x: auto;\\n  padding: 8px 0 16px 0;\\n  scroll-behavior: smooth;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 6px;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 3px;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: linear-gradient(90deg, #ffd700, #ffed4e);\\n  border-radius: 3px;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: linear-gradient(90deg, #ffed4e, #ffd700);\\n}\\n.top-products[_ngcontent-%COMP%]   .no-products[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n  background: rgba(255, 255, 255, 0.05);\\n  border-radius: 12px;\\n  border: 2px dashed rgba(255, 255, 255, 0.2);\\n}\\n.top-products[_ngcontent-%COMP%]   .no-products[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  color: rgba(255, 255, 255, 0.4);\\n  margin-bottom: 12px;\\n}\\n.top-products[_ngcontent-%COMP%]   .no-products[_ngcontent-%COMP%]   .empty-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.6);\\n  font-weight: 500;\\n}\\n\\n.product-item[_ngcontent-%COMP%] {\\n  flex: 0 0 160px;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  overflow: hidden;\\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n  cursor: pointer;\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.product-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px) scale(1.05);\\n  background: rgba(255, 255, 255, 0.18);\\n  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3), 0 4px 10px rgba(255, 255, 255, 0.1) inset;\\n  border-color: rgba(255, 255, 255, 0.2);\\n}\\n.product-item[_ngcontent-%COMP%]:hover   .product-actions[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n.product-item[_ngcontent-%COMP%]:hover   .product-image[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.product-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 120px;\\n  object-fit: cover;\\n  transition: transform 0.4s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  height: 50%;\\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.3));\\n  pointer-events: none;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 10px;\\n  right: 10px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 6px;\\n  opacity: 0;\\n  transform: translateY(-10px);\\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(15px);\\n          backdrop-filter: blur(15px);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #333;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  transform: scale(1.15);\\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(220, 53, 69, 0.1);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%] {\\n  background: rgba(220, 53, 69, 0.2);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 123, 255, 0.1);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.product-details[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  background: rgba(255, 255, 255, 0.05);\\n}\\n.product-details[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 10px 0;\\n  line-height: 1.4;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\\n}\\n.product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 10px;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 800;\\n  color: #ffd700;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\\n}\\n.product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: rgba(255, 255, 255, 0.6);\\n  text-decoration: line-through;\\n  background: rgba(255, 255, 255, 0.1);\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.3);\\n  transition: color 0.3s ease;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon.filled[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n  filter: drop-shadow(0 1px 2px rgba(255, 215, 0, 0.5));\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .rating-count[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: rgba(255, 255, 255, 0.7);\\n  background: rgba(255, 255, 255, 0.1);\\n  padding: 2px 6px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n}\\n\\n.view-more-section[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.1) 100%);\\n  border: 2px solid rgba(255, 255, 255, 0.2);\\n  color: white;\\n  padding: 16px 20px;\\n  border-radius: 16px;\\n  font-weight: 700;\\n  font-size: 15px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 12px;\\n  cursor: pointer;\\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  position: relative;\\n  overflow: hidden;\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\\n}\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\\n  transition: left 0.6s ease;\\n}\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);\\n  border-color: rgba(255, 255, 255, 0.4);\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 2px 8px rgba(255, 255, 255, 0.1) inset;\\n}\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n}\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  transform: translateX(4px);\\n}\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(-2px);\\n}\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  transition: transform 0.3s ease;\\n}\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: rgba(255, 255, 255, 0.4);\\n  margin-bottom: 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 8px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n@media (max-width: 1024px) {\\n  .brands-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\\n    gap: 18px;\\n  }\\n  .brand-card[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  .brand-name[_ngcontent-%COMP%] {\\n    font-size: 22px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .featured-brands-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .brands-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 16px;\\n  }\\n  .brand-card[_ngcontent-%COMP%] {\\n    padding: 18px;\\n  }\\n  .brand-card[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-6px) scale(1.01);\\n  }\\n  .brand-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 16px;\\n    margin-bottom: 20px;\\n  }\\n  .brand-header[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%] {\\n    padding-right: 0;\\n  }\\n  .brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%] {\\n    align-self: flex-start;\\n    padding: 8px 14px;\\n    font-size: 12px;\\n  }\\n  .brand-header[_ngcontent-%COMP%]   .brand-name[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n    margin-bottom: 12px;\\n  }\\n  .brand-stats[_ngcontent-%COMP%] {\\n    flex-direction: row !important;\\n    flex-wrap: wrap;\\n    gap: 8px !important;\\n  }\\n  .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n    padding: 4px 8px;\\n  }\\n  .products-title[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .product-item[_ngcontent-%COMP%] {\\n    flex: 0 0 140px;\\n  }\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .view-more-btn[_ngcontent-%COMP%] {\\n    padding: 14px 18px;\\n    font-size: 14px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .brand-card[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .brand-name[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .product-item[_ngcontent-%COMP%] {\\n    flex: 0 0 120px;\\n  }\\n  .product-image[_ngcontent-%COMP%] {\\n    height: 100px;\\n  }\\n  .product-details[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subscription", "IonicModule", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵtemplate", "FeaturedBrandsComponent_div_8_div_2_div_5_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c1", "FeaturedBrandsComponent_div_8_div_2_Template", "_c0", "ɵɵtext", "ɵɵlistener", "FeaturedBrandsComponent_div_9_Template_button_click_4_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onRetry", "ɵɵtextInterpolate", "error", "formatPrice", "product_r6", "originalPrice", "ɵɵclassProp", "star_r7", "rating", "average", "FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_Template_div_click_0_listener", "$event", "_r5", "$implicit", "onProductClick", "FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_Template_button_click_4_listener", "onLikeProduct", "FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_Template_button_click_6_listener", "onShareProduct", "FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_span_14_Template", "FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_ion_icon_17_Template", "images", "url", "ɵɵsanitizeUrl", "alt", "name", "isProductLiked", "_id", "price", "_c2", "ɵɵtextInterpolate1", "count", "FeaturedBrandsComponent_div_10_2_ng_template_0_Template_div_click_0_listener", "_r3", "brand_r4", "onBrandClick", "FeaturedBrandsComponent_div_10_2_ng_template_0_div_25_Template", "brand", "productCount", "avgRating", "formatNumber", "totalViews", "topProducts", "trackByProductId", "FeaturedBrandsComponent_div_10_2_ng_template_0_Template", "FeaturedBrandsComponent_div_10_2_Template", "carouselOptions", "featuredB<PERSON>s", "trackByBrandName", "FeaturedBrandsComponent", "constructor", "trendingService", "socialService", "router", "isLoading", "likedProducts", "Set", "subscription", "ngOnInit", "loadFeaturedBrands", "subscribeFeaturedBrands", "subscribeLikedProducts", "ngOnDestroy", "unsubscribe", "add", "featuredBrands$", "subscribe", "brands", "likedProducts$", "_this", "_asyncToGenerator", "console", "navigate", "queryParams", "product", "event", "stopPropagation", "_this2", "result", "likeProduct", "success", "log", "message", "_this3", "productUrl", "window", "location", "origin", "navigator", "clipboard", "writeText", "shareProduct", "platform", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "num", "toFixed", "toString", "index", "productId", "has", "ɵɵdirectiveInject", "i1", "TrendingService", "i2", "SocialInteractionsService", "i3", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "FeaturedBrandsComponent_Template", "rf", "ctx", "FeaturedBrandsComponent_div_8_Template", "FeaturedBrandsComponent_div_9_Template", "FeaturedBrandsComponent_div_10_Template", "FeaturedBrandsComponent_div_11_Template", "length", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "IonIcon", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\featured-brands\\featured-brands.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\featured-brands\\featured-brands.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport { OwlOptions } from 'ngx-owl-carousel-o';\nimport { TrendingService, FeaturedBrand } from '../../../../core/services/trending.service';\nimport { Product } from '../../../../core/models/product.model';\nimport { SocialInteractionsService } from '../../../../core/services/social-interactions.service';\nimport { IonicModule } from '@ionic/angular';\n\n@Component({\n  selector: 'app-featured-brands',\n  standalone: true,\n  imports: [CommonModule, IonicModule],\n  templateUrl: './featured-brands.component.html',\n  styleUrls: ['./featured-brands.component.scss']\n})\nexport class FeaturedBrandsComponent implements OnInit, OnDestroy {\n  featuredBrands: FeaturedBrand[] = [];\n  isLoading = true;\n  error: string | null = null;\n  likedProducts = new Set<string>();\n  private subscription: Subscription = new Subscription();\n\n  constructor(\n    private trendingService: TrendingService,\n    private socialService: SocialInteractionsService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadFeaturedBrands();\n    this.subscribeFeaturedBrands();\n    this.subscribeLikedProducts();\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n\n  private subscribeFeaturedBrands() {\n    this.subscription.add(\n      this.trendingService.featuredBrands$.subscribe(brands => {\n        this.featuredBrands = brands;\n        this.isLoading = false;\n      })\n    );\n  }\n\n  private subscribeLikedProducts() {\n    this.subscription.add(\n      this.socialService.likedProducts$.subscribe(likedProducts => {\n        this.likedProducts = likedProducts;\n      })\n    );\n  }\n\n  private async loadFeaturedBrands() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      await this.trendingService.loadFeaturedBrands();\n    } catch (error) {\n      console.error('Error loading featured brands:', error);\n      this.error = 'Failed to load featured brands';\n      this.isLoading = false;\n    }\n  }\n\n  onBrandClick(brand: FeaturedBrand) {\n    this.router.navigate(['/products'], { \n      queryParams: { brand: brand.brand } \n    });\n  }\n\n  onProductClick(product: Product, event: Event) {\n    event.stopPropagation();\n    this.router.navigate(['/product', product._id]);\n  }\n\n  async onLikeProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      const result = await this.socialService.likeProduct(product._id);\n      if (result.success) {\n        console.log(result.message);\n      } else {\n        console.error('Failed to like product:', result.message);\n      }\n    } catch (error) {\n      console.error('Error liking product:', error);\n    }\n  }\n\n  async onShareProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      const productUrl = `${window.location.origin}/product/${product._id}`;\n      await navigator.clipboard.writeText(productUrl);\n\n      await this.socialService.shareProduct(product._id, {\n        platform: 'copy_link',\n        message: `Check out this amazing ${product.name} from ${product.brand}!`\n      });\n\n      console.log('Product link copied to clipboard!');\n    } catch (error) {\n      console.error('Error sharing product:', error);\n    }\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n\n  formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  onRetry() {\n    this.loadFeaturedBrands();\n  }\n\n  trackByBrandName(index: number, brand: FeaturedBrand): string {\n    return brand.brand;\n  }\n\n  isProductLiked(productId: string): boolean {\n    return this.likedProducts.has(productId);\n  }\n\n  trackByProductId(index: number, product: Product): string {\n    return product._id;\n  }\n}\n", "<div class=\"featured-brands-container\">\n  <!-- Header -->\n  <div class=\"section-header\">\n    <div class=\"header-content\">\n      <h2 class=\"section-title\">\n        <ion-icon name=\"diamond\" class=\"title-icon\"></ion-icon>\n        Featured Brands\n      </h2>\n      <p class=\"section-subtitle\">Top brands with amazing collections</p>\n    </div>\n  </div>\n\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <div class=\"loading-grid\">\n      <div *ngFor=\"let item of [1,2,3,4]\" class=\"loading-brand-card\">\n        <div class=\"loading-header\">\n          <div class=\"loading-brand-name\"></div>\n          <div class=\"loading-stats\"></div>\n        </div>\n        <div class=\"loading-products\">\n          <div *ngFor=\"let prod of [1,2,3]\" class=\"loading-product\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error && !isLoading\" class=\"error-container\">\n    <ion-icon name=\"alert-circle\" class=\"error-icon\"></ion-icon>\n    <p class=\"error-message\">{{ error }}</p>\n    <button class=\"retry-btn\" (click)=\"onRetry()\">\n      <ion-icon name=\"refresh\"></ion-icon>\n      Try Again\n    </button>\n  </div>\n\n  <!-- Brands Slider -->\n  <div *ngIf=\"!isLoading && !error && featuredBrands.length > 0\" class=\"brands-slider-container\">\n    <owl-carousel-o [options]=\"carouselOptions\" class=\"brands-slider\">\n      <ng-template carouselSlide *ngFor=\"let brand of featuredBrands; trackBy: trackByBrandName\">\n        <div\n          class=\"brand-card\"\n          (click)=\"onBrandClick(brand)\"\n        >\n      <!-- Brand Header -->\n      <div class=\"brand-header\">\n        <div class=\"brand-info\">\n          <h3 class=\"brand-name\">{{ brand.brand }}</h3>\n          <div class=\"brand-stats\">\n            <div class=\"stat-item\">\n              <ion-icon name=\"bag-outline\"></ion-icon>\n              <span>{{ brand.productCount }} Products</span>\n            </div>\n            <div class=\"stat-item\">\n              <ion-icon name=\"star\"></ion-icon>\n              <span>{{ brand.avgRating }}/5</span>\n            </div>\n            <div class=\"stat-item\">\n              <ion-icon name=\"eye-outline\"></ion-icon>\n              <span>{{ formatNumber(brand.totalViews) }} Views</span>\n            </div>\n          </div>\n        </div>\n        <div class=\"brand-badge\">\n          <ion-icon name=\"diamond\"></ion-icon>\n          Featured\n        </div>\n      </div>\n\n      <!-- Top Products -->\n      <div class=\"top-products\">\n        <h4 class=\"products-title\">Top Products</h4>\n        <div class=\"products-list\">\n          <div \n            *ngFor=\"let product of brand.topProducts; trackBy: trackByProductId\" \n            class=\"product-item\"\n            (click)=\"onProductClick(product, $event)\"\n          >\n            <div class=\"product-image-container\">\n              <img \n                [src]=\"product.images[0].url\"\n                [alt]=\"product.images[0].alt || product.name\"\n                class=\"product-image\"\n                loading=\"lazy\"\n              />\n              \n              <!-- Action Buttons -->\n              <div class=\"product-actions\">\n                <button\n                  class=\"action-btn like-btn\"\n                  [class.liked]=\"isProductLiked(product._id)\"\n                  (click)=\"onLikeProduct(product, $event)\"\n                  [attr.aria-label]=\"'Like ' + product.name\"\n                >\n                  <ion-icon [name]=\"isProductLiked(product._id) ? 'heart' : 'heart-outline'\"></ion-icon>\n                </button>\n                <button \n                  class=\"action-btn share-btn\" \n                  (click)=\"onShareProduct(product, $event)\"\n                  [attr.aria-label]=\"'Share ' + product.name\"\n                >\n                  <ion-icon name=\"share-outline\"></ion-icon>\n                </button>\n              </div>\n            </div>\n\n            <div class=\"product-details\">\n              <h5 class=\"product-name\">{{ product.name }}</h5>\n              <div class=\"product-price\">\n                <span class=\"current-price\">{{ formatPrice(product.price) }}</span>\n                <span *ngIf=\"product.originalPrice && product.originalPrice > product.price\" \n                      class=\"original-price\">{{ formatPrice(product.originalPrice) }}</span>\n              </div>\n              <div class=\"product-rating\">\n                <div class=\"stars\">\n                  <ion-icon \n                    *ngFor=\"let star of [1,2,3,4,5]\" \n                    [name]=\"star <= product.rating.average ? 'star' : 'star-outline'\"\n                    [class.filled]=\"star <= product.rating.average\"\n                  ></ion-icon>\n                </div>\n                <span class=\"rating-count\">({{ product.rating.count }})</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- View More Button -->\n      <div class=\"view-more-section\">\n        <button class=\"view-more-btn\">\n          <span>View All {{ brand.brand }} Products</span>\n          <ion-icon name=\"chevron-forward\"></ion-icon>\n        </button>\n      </div>\n        </div>\n      </ng-template>\n    </owl-carousel-o>\n  </div>\n\n  <!-- Empty State -->\n  <div *ngIf=\"!isLoading && !error && featuredBrands.length === 0\" class=\"empty-container\">\n    <ion-icon name=\"diamond-outline\" class=\"empty-icon\"></ion-icon>\n    <h3 class=\"empty-title\">No Featured Brands</h3>\n    <p class=\"empty-message\">Check back later for featured brand collections</p>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AAMnC,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;;ICYlCC,EAAA,CAAAC,SAAA,cAAgE;;;;;IALlED,EADF,CAAAE,cAAA,cAA+D,cACjC;IAE1BF,EADA,CAAAC,SAAA,cAAsC,cACL;IACnCD,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,cAAA,cAA8B;IAC5BF,EAAA,CAAAI,UAAA,IAAAC,kDAAA,kBAA0D;IAE9DL,EADE,CAAAG,YAAA,EAAM,EACF;;;IAFoBH,EAAA,CAAAM,SAAA,GAAU;IAAVN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAU;;;;;IAPtCT,EADF,CAAAE,cAAA,cAAiD,cACrB;IACxBF,EAAA,CAAAI,UAAA,IAAAM,4CAAA,kBAA+D;IAUnEV,EADE,CAAAG,YAAA,EAAM,EACF;;;IAVoBH,EAAA,CAAAM,SAAA,GAAY;IAAZN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAG,GAAA,EAAY;;;;;;IAatCX,EAAA,CAAAE,cAAA,cAAyD;IACvDF,EAAA,CAAAC,SAAA,mBAA4D;IAC5DD,EAAA,CAAAE,cAAA,YAAyB;IAAAF,EAAA,CAAAY,MAAA,GAAW;IAAAZ,EAAA,CAAAG,YAAA,EAAI;IACxCH,EAAA,CAAAE,cAAA,iBAA8C;IAApBF,EAAA,CAAAa,UAAA,mBAAAC,+DAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAG,OAAA,EAAS;IAAA,EAAC;IAC3CpB,EAAA,CAAAC,SAAA,mBAAoC;IACpCD,EAAA,CAAAY,MAAA,kBACF;IACFZ,EADE,CAAAG,YAAA,EAAS,EACL;;;;IALqBH,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAqB,iBAAA,CAAAJ,MAAA,CAAAK,KAAA,CAAW;;;;;IAiFxBtB,EAAA,CAAAE,cAAA,eAC6B;IAAAF,EAAA,CAAAY,MAAA,GAAwC;IAAAZ,EAAA,CAAAG,YAAA,EAAO;;;;;IAA/CH,EAAA,CAAAM,SAAA,EAAwC;IAAxCN,EAAA,CAAAqB,iBAAA,CAAAJ,MAAA,CAAAM,WAAA,CAAAC,UAAA,CAAAC,aAAA,EAAwC;;;;;IAInEzB,EAAA,CAAAC,SAAA,mBAIY;;;;;IADVD,EAAA,CAAA0B,WAAA,WAAAC,OAAA,IAAAH,UAAA,CAAAI,MAAA,CAAAC,OAAA,CAA+C;IAD/C7B,EAAA,CAAAO,UAAA,SAAAoB,OAAA,IAAAH,UAAA,CAAAI,MAAA,CAAAC,OAAA,2BAAiE;;;;;;IA5C3E7B,EAAA,CAAAE,cAAA,cAIC;IADCF,EAAA,CAAAa,UAAA,mBAAAiB,oFAAAC,MAAA;MAAA,MAAAP,UAAA,GAAAxB,EAAA,CAAAe,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAiB,cAAA,CAAAV,UAAA,EAAAO,MAAA,CAA+B;IAAA,EAAC;IAEzC/B,EAAA,CAAAE,cAAA,cAAqC;IACnCF,EAAA,CAAAC,SAAA,cAKE;IAIAD,EADF,CAAAE,cAAA,cAA6B,iBAM1B;IAFCF,EAAA,CAAAa,UAAA,mBAAAsB,uFAAAJ,MAAA;MAAA,MAAAP,UAAA,GAAAxB,EAAA,CAAAe,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAmB,aAAA,CAAAZ,UAAA,EAAAO,MAAA,CAA8B;IAAA,EAAC;IAGxC/B,EAAA,CAAAC,SAAA,mBAAsF;IACxFD,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAE,cAAA,iBAIC;IAFCF,EAAA,CAAAa,UAAA,mBAAAwB,uFAAAN,MAAA;MAAA,MAAAP,UAAA,GAAAxB,EAAA,CAAAe,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAqB,cAAA,CAAAd,UAAA,EAAAO,MAAA,CAA+B;IAAA,EAAC;IAGzC/B,EAAA,CAAAC,SAAA,mBAA0C;IAGhDD,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAGJH,EADF,CAAAE,cAAA,cAA6B,aACF;IAAAF,EAAA,CAAAY,MAAA,IAAkB;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IAE9CH,EADF,CAAAE,cAAA,eAA2B,gBACG;IAAAF,EAAA,CAAAY,MAAA,IAAgC;IAAAZ,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAI,UAAA,KAAAmC,sEAAA,mBAC6B;IAC/BvC,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAE,cAAA,eAA4B,eACP;IACjBF,EAAA,CAAAI,UAAA,KAAAoC,0EAAA,uBAIC;IACHxC,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,cAAA,gBAA2B;IAAAF,EAAA,CAAAY,MAAA,IAA4B;IAG7DZ,EAH6D,CAAAG,YAAA,EAAO,EAC1D,EACF,EACF;;;;;IA5CAH,EAAA,CAAAM,SAAA,GAA6B;IAC7BN,EADA,CAAAO,UAAA,QAAAiB,UAAA,CAAAiB,MAAA,IAAAC,GAAA,EAAA1C,EAAA,CAAA2C,aAAA,CAA6B,QAAAnB,UAAA,CAAAiB,MAAA,IAAAG,GAAA,IAAApB,UAAA,CAAAqB,IAAA,CACgB;IAS3C7C,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAA0B,WAAA,UAAAT,MAAA,CAAA6B,cAAA,CAAAtB,UAAA,CAAAuB,GAAA,EAA2C;;IAIjC/C,EAAA,CAAAM,SAAA,EAAgE;IAAhEN,EAAA,CAAAO,UAAA,SAAAU,MAAA,CAAA6B,cAAA,CAAAtB,UAAA,CAAAuB,GAAA,8BAAgE;IAK1E/C,EAAA,CAAAM,SAAA,EAA2C;;IAQtBN,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAqB,iBAAA,CAAAG,UAAA,CAAAqB,IAAA,CAAkB;IAEb7C,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAqB,iBAAA,CAAAJ,MAAA,CAAAM,WAAA,CAAAC,UAAA,CAAAwB,KAAA,EAAgC;IACrDhD,EAAA,CAAAM,SAAA,EAAoE;IAApEN,EAAA,CAAAO,UAAA,SAAAiB,UAAA,CAAAC,aAAA,IAAAD,UAAA,CAAAC,aAAA,GAAAD,UAAA,CAAAwB,KAAA,CAAoE;IAMtDhD,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,KAAAyC,GAAA,EAAc;IAKRjD,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAkD,kBAAA,MAAA1B,UAAA,CAAAI,MAAA,CAAAuB,KAAA,MAA4B;;;;;;IAjF/DnD,EAAA,CAAAE,cAAA,cAGC;IADCF,EAAA,CAAAa,UAAA,mBAAAuC,6EAAA;MAAApD,EAAA,CAAAe,aAAA,CAAAsC,GAAA;MAAA,MAAAC,QAAA,GAAAtD,EAAA,CAAAkB,aAAA,GAAAe,SAAA;MAAA,MAAAhB,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAsC,YAAA,CAAAD,QAAA,CAAmB;IAAA,EAAC;IAK7BtD,EAFJ,CAAAE,cAAA,cAA0B,cACA,aACC;IAAAF,EAAA,CAAAY,MAAA,GAAiB;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IAE3CH,EADF,CAAAE,cAAA,cAAyB,cACA;IACrBF,EAAA,CAAAC,SAAA,mBAAwC;IACxCD,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAY,MAAA,GAAiC;IACzCZ,EADyC,CAAAG,YAAA,EAAO,EAC1C;IACNH,EAAA,CAAAE,cAAA,eAAuB;IACrBF,EAAA,CAAAC,SAAA,oBAAiC;IACjCD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAY,MAAA,IAAuB;IAC/BZ,EAD+B,CAAAG,YAAA,EAAO,EAChC;IACNH,EAAA,CAAAE,cAAA,eAAuB;IACrBF,EAAA,CAAAC,SAAA,oBAAwC;IACxCD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAY,MAAA,IAA0C;IAGtDZ,EAHsD,CAAAG,YAAA,EAAO,EACnD,EACF,EACF;IACNH,EAAA,CAAAE,cAAA,eAAyB;IACvBF,EAAA,CAAAC,SAAA,oBAAoC;IACpCD,EAAA,CAAAY,MAAA,kBACF;IACFZ,EADE,CAAAG,YAAA,EAAM,EACF;IAIJH,EADF,CAAAE,cAAA,eAA0B,cACG;IAAAF,EAAA,CAAAY,MAAA,oBAAY;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAE,cAAA,eAA2B;IACzBF,EAAA,CAAAI,UAAA,KAAAoD,8DAAA,oBAIC;IAiDLxD,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAFJ,CAAAE,cAAA,eAA+B,kBACC,YACtB;IAAAF,EAAA,CAAAY,MAAA,IAAmC;IAAAZ,EAAA,CAAAG,YAAA,EAAO;IAChDH,EAAA,CAAAC,SAAA,oBAA4C;IAG9CD,EAFA,CAAAG,YAAA,EAAS,EACL,EACE;;;;;IAxFmBH,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAAqB,iBAAA,CAAAiC,QAAA,CAAAG,KAAA,CAAiB;IAI9BzD,EAAA,CAAAM,SAAA,GAAiC;IAAjCN,EAAA,CAAAkD,kBAAA,KAAAI,QAAA,CAAAI,YAAA,cAAiC;IAIjC1D,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAkD,kBAAA,KAAAI,QAAA,CAAAK,SAAA,OAAuB;IAIvB3D,EAAA,CAAAM,SAAA,GAA0C;IAA1CN,EAAA,CAAAkD,kBAAA,KAAAjC,MAAA,CAAA2C,YAAA,CAAAN,QAAA,CAAAO,UAAA,YAA0C;IAe9B7D,EAAA,CAAAM,SAAA,GAAsB;IAAAN,EAAtB,CAAAO,UAAA,YAAA+C,QAAA,CAAAQ,WAAA,CAAsB,iBAAA7C,MAAA,CAAA8C,gBAAA,CAAyB;IAyD/D/D,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAAkD,kBAAA,cAAAI,QAAA,CAAAG,KAAA,cAAmC;;;;;IA5F7CzD,EAAA,CAAAI,UAAA,IAAA4D,uDAAA,2BAA2F;;;;;IAD7FhE,EADF,CAAAE,cAAA,cAA+F,yBAC3B;IAChEF,EAAA,CAAAI,UAAA,IAAA6D,yCAAA,iBAA2F;IAmG/FjE,EADE,CAAAG,YAAA,EAAiB,EACb;;;;IApGYH,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAO,UAAA,YAAAU,MAAA,CAAAiD,eAAA,CAA2B;IACIlE,EAAA,CAAAM,SAAA,EAAmB;IAAAN,EAAnB,CAAAO,UAAA,YAAAU,MAAA,CAAAkD,cAAA,CAAmB,iBAAAlD,MAAA,CAAAmD,gBAAA,CAAyB;;;;;IAsG7FpE,EAAA,CAAAE,cAAA,cAAyF;IACvFF,EAAA,CAAAC,SAAA,mBAA+D;IAC/DD,EAAA,CAAAE,cAAA,aAAwB;IAAAF,EAAA,CAAAY,MAAA,yBAAkB;IAAAZ,EAAA,CAAAG,YAAA,EAAK;IAC/CH,EAAA,CAAAE,cAAA,YAAyB;IAAAF,EAAA,CAAAY,MAAA,sDAA+C;IAC1EZ,EAD0E,CAAAG,YAAA,EAAI,EACxE;;;ADhIR,OAAM,MAAOkE,uBAAuB;EAOlCC,YACUC,eAAgC,EAChCC,aAAwC,EACxCC,MAAc;IAFd,KAAAF,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IAThB,KAAAN,cAAc,GAAoB,EAAE;IACpC,KAAAO,SAAS,GAAG,IAAI;IAChB,KAAApD,KAAK,GAAkB,IAAI;IAC3B,KAAAqD,aAAa,GAAG,IAAIC,GAAG,EAAU;IACzB,KAAAC,YAAY,GAAiB,IAAI/E,YAAY,EAAE;EAMpD;EAEHgF,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACL,YAAY,CAACM,WAAW,EAAE;EACjC;EAEQH,uBAAuBA,CAAA;IAC7B,IAAI,CAACH,YAAY,CAACO,GAAG,CACnB,IAAI,CAACb,eAAe,CAACc,eAAe,CAACC,SAAS,CAACC,MAAM,IAAG;MACtD,IAAI,CAACpB,cAAc,GAAGoB,MAAM;MAC5B,IAAI,CAACb,SAAS,GAAG,KAAK;IACxB,CAAC,CAAC,CACH;EACH;EAEQO,sBAAsBA,CAAA;IAC5B,IAAI,CAACJ,YAAY,CAACO,GAAG,CACnB,IAAI,CAACZ,aAAa,CAACgB,cAAc,CAACF,SAAS,CAACX,aAAa,IAAG;MAC1D,IAAI,CAACA,aAAa,GAAGA,aAAa;IACpC,CAAC,CAAC,CACH;EACH;EAEcI,kBAAkBA,CAAA;IAAA,IAAAU,KAAA;IAAA,OAAAC,iBAAA;MAC9B,IAAI;QACFD,KAAI,CAACf,SAAS,GAAG,IAAI;QACrBe,KAAI,CAACnE,KAAK,GAAG,IAAI;QACjB,MAAMmE,KAAI,CAAClB,eAAe,CAACQ,kBAAkB,EAAE;OAChD,CAAC,OAAOzD,KAAK,EAAE;QACdqE,OAAO,CAACrE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtDmE,KAAI,CAACnE,KAAK,GAAG,gCAAgC;QAC7CmE,KAAI,CAACf,SAAS,GAAG,KAAK;;IACvB;EACH;EAEAnB,YAAYA,CAACE,KAAoB;IAC/B,IAAI,CAACgB,MAAM,CAACmB,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;MAClCC,WAAW,EAAE;QAAEpC,KAAK,EAAEA,KAAK,CAACA;MAAK;KAClC,CAAC;EACJ;EAEAvB,cAAcA,CAAC4D,OAAgB,EAAEC,KAAY;IAC3CA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,CAACvB,MAAM,CAACmB,QAAQ,CAAC,CAAC,UAAU,EAAEE,OAAO,CAAC/C,GAAG,CAAC,CAAC;EACjD;EAEMX,aAAaA,CAAC0D,OAAgB,EAAEC,KAAY;IAAA,IAAAE,MAAA;IAAA,OAAAP,iBAAA;MAChDK,KAAK,CAACC,eAAe,EAAE;MACvB,IAAI;QACF,MAAME,MAAM,SAASD,MAAI,CAACzB,aAAa,CAAC2B,WAAW,CAACL,OAAO,CAAC/C,GAAG,CAAC;QAChE,IAAImD,MAAM,CAACE,OAAO,EAAE;UAClBT,OAAO,CAACU,GAAG,CAACH,MAAM,CAACI,OAAO,CAAC;SAC5B,MAAM;UACLX,OAAO,CAACrE,KAAK,CAAC,yBAAyB,EAAE4E,MAAM,CAACI,OAAO,CAAC;;OAE3D,CAAC,OAAOhF,KAAK,EAAE;QACdqE,OAAO,CAACrE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;IAC9C;EACH;EAEMgB,cAAcA,CAACwD,OAAgB,EAAEC,KAAY;IAAA,IAAAQ,MAAA;IAAA,OAAAb,iBAAA;MACjDK,KAAK,CAACC,eAAe,EAAE;MACvB,IAAI;QACF,MAAMQ,UAAU,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,YAAYb,OAAO,CAAC/C,GAAG,EAAE;QACrE,MAAM6D,SAAS,CAACC,SAAS,CAACC,SAAS,CAACN,UAAU,CAAC;QAE/C,MAAMD,MAAI,CAAC/B,aAAa,CAACuC,YAAY,CAACjB,OAAO,CAAC/C,GAAG,EAAE;UACjDiE,QAAQ,EAAE,WAAW;UACrBV,OAAO,EAAE,0BAA0BR,OAAO,CAACjD,IAAI,SAASiD,OAAO,CAACrC,KAAK;SACtE,CAAC;QAEFkC,OAAO,CAACU,GAAG,CAAC,mCAAmC,CAAC;OACjD,CAAC,OAAO/E,KAAK,EAAE;QACdqE,OAAO,CAACrE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;IAC/C;EACH;EAEAC,WAAWA,CAACyB,KAAa;IACvB,OAAO,IAAIiE,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACtE,KAAK,CAAC;EAClB;EAEAY,YAAYA,CAAC2D,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EAEArG,OAAOA,CAAA;IACL,IAAI,CAAC2D,kBAAkB,EAAE;EAC3B;EAEAX,gBAAgBA,CAACsD,KAAa,EAAEjE,KAAoB;IAClD,OAAOA,KAAK,CAACA,KAAK;EACpB;EAEAX,cAAcA,CAAC6E,SAAiB;IAC9B,OAAO,IAAI,CAAChD,aAAa,CAACiD,GAAG,CAACD,SAAS,CAAC;EAC1C;EAEA5D,gBAAgBA,CAAC2D,KAAa,EAAE5B,OAAgB;IAC9C,OAAOA,OAAO,CAAC/C,GAAG;EACpB;;;uBA7HWsB,uBAAuB,EAAArE,EAAA,CAAA6H,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAA/H,EAAA,CAAA6H,iBAAA,CAAAG,EAAA,CAAAC,yBAAA,GAAAjI,EAAA,CAAA6H,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAvB9D,uBAAuB;MAAA+D,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAtI,EAAA,CAAAuI,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCd9B7I,EAJN,CAAAE,cAAA,aAAuC,aAET,aACE,YACA;UACxBF,EAAA,CAAAC,SAAA,kBAAuD;UACvDD,EAAA,CAAAY,MAAA,wBACF;UAAAZ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAE,cAAA,WAA4B;UAAAF,EAAA,CAAAY,MAAA,0CAAmC;UAEnEZ,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAoINH,EAjIA,CAAAI,UAAA,IAAA2I,sCAAA,iBAAiD,IAAAC,sCAAA,iBAeQ,KAAAC,uCAAA,iBAUsC,KAAAC,uCAAA,iBAwGN;UAK3FlJ,EAAA,CAAAG,YAAA,EAAM;;;UAtIEH,EAAA,CAAAM,SAAA,GAAe;UAAfN,EAAA,CAAAO,UAAA,SAAAuI,GAAA,CAAApE,SAAA,CAAe;UAef1E,EAAA,CAAAM,SAAA,EAAyB;UAAzBN,EAAA,CAAAO,UAAA,SAAAuI,GAAA,CAAAxH,KAAA,KAAAwH,GAAA,CAAApE,SAAA,CAAyB;UAUzB1E,EAAA,CAAAM,SAAA,EAAuD;UAAvDN,EAAA,CAAAO,UAAA,UAAAuI,GAAA,CAAApE,SAAA,KAAAoE,GAAA,CAAAxH,KAAA,IAAAwH,GAAA,CAAA3E,cAAA,CAAAgF,MAAA,KAAuD;UAwGvDnJ,EAAA,CAAAM,SAAA,EAAyD;UAAzDN,EAAA,CAAAO,UAAA,UAAAuI,GAAA,CAAApE,SAAA,KAAAoE,GAAA,CAAAxH,KAAA,IAAAwH,GAAA,CAAA3E,cAAA,CAAAgF,MAAA,OAAyD;;;qBDhIrDtJ,YAAY,EAAAuJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEvJ,WAAW,EAAAwJ,EAAA,CAAAC,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}