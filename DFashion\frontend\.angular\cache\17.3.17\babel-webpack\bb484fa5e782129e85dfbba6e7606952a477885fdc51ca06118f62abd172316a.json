{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nlet SuggestedForYouComponent = class SuggestedForYouComponent {\n  constructor(router) {\n    this.router = router;\n    this.suggestedUsers = [];\n    this.isLoading = true;\n    this.error = null;\n    this.subscription = new Subscription();\n    // Slider properties\n    this.currentSlide = 0;\n    this.slideOffset = 0;\n    this.cardWidth = 200; // Width of each user card including margin\n    this.visibleCards = 4; // Number of cards visible at once\n    this.maxSlide = 0;\n    this.autoSlideDelay = 5000; // 5 seconds for users\n    this.isAutoSliding = true;\n    this.isPaused = false;\n  }\n  ngOnInit() {\n    this.loadSuggestedUsers();\n    this.updateResponsiveSettings();\n    this.setupResizeListener();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n  loadSuggestedUsers() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        // Mock data for suggested users\n        _this.suggestedUsers = [{\n          id: '1',\n          username: 'fashionista_maya',\n          fullName: 'Maya Patel',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by john_doe and 12 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 45000,\n          category: 'Fashion'\n        }, {\n          id: '2',\n          username: 'style_guru_raj',\n          fullName: 'Raj Kumar',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by sarah_k and 8 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 32000,\n          category: 'Menswear'\n        }, {\n          id: '3',\n          username: 'trendy_sara',\n          fullName: 'Sara Johnson',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by alex_m and 15 others',\n          isFollowing: false,\n          isInfluencer: false,\n          followerCount: 8500,\n          category: 'Casual'\n        }, {\n          id: '4',\n          username: 'luxury_lover',\n          fullName: 'Emma Wilson',\n          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by mike_t and 20 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 67000,\n          category: 'Luxury'\n        }, {\n          id: '5',\n          username: 'street_style_alex',\n          fullName: 'Alex Chen',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by lisa_p and 5 others',\n          isFollowing: false,\n          isInfluencer: false,\n          followerCount: 12000,\n          category: 'Streetwear'\n        }, {\n          id: '6',\n          username: 'boho_bella',\n          fullName: 'Isabella Rodriguez',\n          avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by tom_h and 18 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 28000,\n          category: 'Boho'\n        }];\n        _this.isLoading = false;\n        _this.updateSliderOnUsersLoad();\n      } catch (error) {\n        console.error('Error loading suggested users:', error);\n        _this.error = 'Failed to load suggested users';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onUserClick(user) {\n    this.router.navigate(['/profile', user.username]);\n  }\n  onFollowUser(user, event) {\n    event.stopPropagation();\n    user.isFollowing = !user.isFollowing;\n    if (user.isFollowing) {\n      user.followerCount++;\n    } else {\n      user.followerCount--;\n    }\n  }\n  formatFollowerCount(count) {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  onRetry() {\n    this.loadSuggestedUsers();\n  }\n  trackByUserId(index, user) {\n    return user.id;\n  }\n  // Auto-sliding methods\n  startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.suggestedUsers.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n  autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n  // Responsive methods\n  updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 480) {\n      this.cardWidth = 180;\n      this.visibleCards = 1;\n    } else if (width <= 768) {\n      this.cardWidth = 200;\n      this.visibleCards = 2;\n    } else if (width <= 1200) {\n      this.cardWidth = 220;\n      this.visibleCards = 3;\n    } else {\n      this.cardWidth = 220;\n      this.visibleCards = 4;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n  setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.suggestedUsers.length - this.visibleCards);\n  }\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n  restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000);\n  }\n  // Update slider when users load\n  updateSliderOnUsersLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n};\nSuggestedForYouComponent = __decorate([Component({\n  selector: 'app-suggested-for-you',\n  standalone: true,\n  imports: [CommonModule, IonicModule, CarouselModule],\n  templateUrl: './suggested-for-you.component.html',\n  styleUrls: ['./suggested-for-you.component.scss']\n})], SuggestedForYouComponent);\nexport { SuggestedForYouComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "Subscription", "IonicModule", "CarouselModule", "SuggestedForYouComponent", "constructor", "router", "suggestedUsers", "isLoading", "error", "subscription", "currentSlide", "slideOffset", "<PERSON><PERSON><PERSON><PERSON>", "visibleCards", "maxSlide", "autoSlideDelay", "isAutoSliding", "isPaused", "ngOnInit", "loadSuggestedUsers", "updateResponsiveSettings", "setupResizeListener", "ngOnDestroy", "unsubscribe", "stopAutoSlide", "_this", "_asyncToGenerator", "id", "username", "fullName", "avatar", "<PERSON><PERSON><PERSON>", "isFollowing", "isInfluencer", "followerCount", "category", "updateSliderOnUsersLoad", "console", "onUserClick", "user", "navigate", "onFollowUser", "event", "stopPropagation", "formatFollowerCount", "count", "toFixed", "toString", "onRetry", "trackByUserId", "index", "startAutoSlide", "autoSlideInterval", "setInterval", "length", "autoSlideNext", "clearInterval", "updateSlideOffset", "pauseAutoSlide", "resumeAutoSlide", "width", "window", "innerWidth", "updateSliderLimits", "addEventListener", "Math", "max", "slidePrev", "restartAutoSlideAfterInteraction", "slideNext", "setTimeout", "__decorate", "selector", "standalone", "imports", "templateUrl", "styleUrls"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\suggested-for-you\\suggested-for-you.component.ts"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\n\ninterface SuggestedUser {\n  id: string;\n  username: string;\n  fullName: string;\n  avatar: string;\n  followedBy: string;\n  isFollowing: boolean;\n  isInfluencer: boolean;\n  followerCount: number;\n  category: string;\n}\n\n@Component({\n  selector: 'app-suggested-for-you',\n  standalone: true,\n  imports: [CommonModule, IonicModule, CarouselModule],\n  templateUrl: './suggested-for-you.component.html',\n  styleUrls: ['./suggested-for-you.component.scss']\n})\nexport class SuggestedForYouComponent implements OnInit, OnDestroy {\n  suggestedUsers: SuggestedUser[] = [];\n  isLoading = true;\n  error: string | null = null;\n  private subscription: Subscription = new Subscription();\n\n  // Slider properties\n  currentSlide = 0;\n  slideOffset = 0;\n  cardWidth = 200; // Width of each user card including margin\n  visibleCards = 4; // Number of cards visible at once\n  maxSlide = 0;\n  \n  // Auto-sliding properties\n  autoSlideInterval: any;\n  autoSlideDelay = 5000; // 5 seconds for users\n  isAutoSliding = true;\n  isPaused = false;\n\n  constructor(private router: Router) {}\n\n  ngOnInit() {\n    this.loadSuggestedUsers();\n    this.updateResponsiveSettings();\n    this.setupResizeListener();\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n\n  private async loadSuggestedUsers() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      \n      // Mock data for suggested users\n      this.suggestedUsers = [\n        {\n          id: '1',\n          username: 'fashionista_maya',\n          fullName: 'Maya Patel',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by john_doe and 12 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 45000,\n          category: 'Fashion'\n        },\n        {\n          id: '2',\n          username: 'style_guru_raj',\n          fullName: 'Raj Kumar',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by sarah_k and 8 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 32000,\n          category: 'Menswear'\n        },\n        {\n          id: '3',\n          username: 'trendy_sara',\n          fullName: 'Sara Johnson',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by alex_m and 15 others',\n          isFollowing: false,\n          isInfluencer: false,\n          followerCount: 8500,\n          category: 'Casual'\n        },\n        {\n          id: '4',\n          username: 'luxury_lover',\n          fullName: 'Emma Wilson',\n          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by mike_t and 20 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 67000,\n          category: 'Luxury'\n        },\n        {\n          id: '5',\n          username: 'street_style_alex',\n          fullName: 'Alex Chen',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by lisa_p and 5 others',\n          isFollowing: false,\n          isInfluencer: false,\n          followerCount: 12000,\n          category: 'Streetwear'\n        },\n        {\n          id: '6',\n          username: 'boho_bella',\n          fullName: 'Isabella Rodriguez',\n          avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by tom_h and 18 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 28000,\n          category: 'Boho'\n        }\n      ];\n      \n      this.isLoading = false;\n      this.updateSliderOnUsersLoad();\n    } catch (error) {\n      console.error('Error loading suggested users:', error);\n      this.error = 'Failed to load suggested users';\n      this.isLoading = false;\n    }\n  }\n\n  onUserClick(user: SuggestedUser) {\n    this.router.navigate(['/profile', user.username]);\n  }\n\n  onFollowUser(user: SuggestedUser, event: Event) {\n    event.stopPropagation();\n    user.isFollowing = !user.isFollowing;\n    \n    if (user.isFollowing) {\n      user.followerCount++;\n    } else {\n      user.followerCount--;\n    }\n  }\n\n  formatFollowerCount(count: number): string {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n\n  onRetry() {\n    this.loadSuggestedUsers();\n  }\n\n  trackByUserId(index: number, user: SuggestedUser): string {\n    return user.id;\n  }\n\n  // Auto-sliding methods\n  private startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n    \n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.suggestedUsers.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n\n  private stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n\n  private autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n\n  // Responsive methods\n  private updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 480) {\n      this.cardWidth = 180;\n      this.visibleCards = 1;\n    } else if (width <= 768) {\n      this.cardWidth = 200;\n      this.visibleCards = 2;\n    } else if (width <= 1200) {\n      this.cardWidth = 220;\n      this.visibleCards = 3;\n    } else {\n      this.cardWidth = 220;\n      this.visibleCards = 4;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n\n  private setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.suggestedUsers.length - this.visibleCards);\n  }\n\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n\n  private updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n\n  private restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000);\n  }\n\n  // Update slider when users load\n  private updateSliderOnUsersLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n}\n"], "mappings": ";;AAAA,SAASA,SAAS,QAA2B,eAAe;AAC5D,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AACnC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,oBAAoB;AAqB5C,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB;EAmBnCC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAlB1B,KAAAC,cAAc,GAAoB,EAAE;IACpC,KAAAC,SAAS,GAAG,IAAI;IAChB,KAAAC,KAAK,GAAkB,IAAI;IACnB,KAAAC,YAAY,GAAiB,IAAIT,YAAY,EAAE;IAEvD;IACA,KAAAU,YAAY,GAAG,CAAC;IAChB,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,SAAS,GAAG,GAAG,CAAC,CAAC;IACjB,KAAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAClB,KAAAC,QAAQ,GAAG,CAAC;IAIZ,KAAAC,cAAc,GAAG,IAAI,CAAC,CAAC;IACvB,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,QAAQ,GAAG,KAAK;EAEqB;EAErCC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,wBAAwB,EAAE;IAC/B,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACb,YAAY,CAACc,WAAW,EAAE;IAC/B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEcL,kBAAkBA,CAAA;IAAA,IAAAM,KAAA;IAAA,OAAAC,iBAAA;MAC9B,IAAI;QACFD,KAAI,CAAClB,SAAS,GAAG,IAAI;QACrBkB,KAAI,CAACjB,KAAK,GAAG,IAAI;QAEjB;QACAiB,KAAI,CAACnB,cAAc,GAAG,CACpB;UACEqB,EAAE,EAAE,GAAG;UACPC,QAAQ,EAAE,kBAAkB;UAC5BC,QAAQ,EAAE,YAAY;UACtBC,MAAM,EAAE,6FAA6F;UACrGC,UAAU,EAAE,oCAAoC;UAChDC,WAAW,EAAE,KAAK;UAClBC,YAAY,EAAE,IAAI;UAClBC,aAAa,EAAE,KAAK;UACpBC,QAAQ,EAAE;SACX,EACD;UACER,EAAE,EAAE,GAAG;UACPC,QAAQ,EAAE,gBAAgB;UAC1BC,QAAQ,EAAE,WAAW;UACrBC,MAAM,EAAE,6FAA6F;UACrGC,UAAU,EAAE,kCAAkC;UAC9CC,WAAW,EAAE,KAAK;UAClBC,YAAY,EAAE,IAAI;UAClBC,aAAa,EAAE,KAAK;UACpBC,QAAQ,EAAE;SACX,EACD;UACER,EAAE,EAAE,GAAG;UACPC,QAAQ,EAAE,aAAa;UACvBC,QAAQ,EAAE,cAAc;UACxBC,MAAM,EAAE,6FAA6F;UACrGC,UAAU,EAAE,kCAAkC;UAC9CC,WAAW,EAAE,KAAK;UAClBC,YAAY,EAAE,KAAK;UACnBC,aAAa,EAAE,IAAI;UACnBC,QAAQ,EAAE;SACX,EACD;UACER,EAAE,EAAE,GAAG;UACPC,QAAQ,EAAE,cAAc;UACxBC,QAAQ,EAAE,aAAa;UACvBC,MAAM,EAAE,0FAA0F;UAClGC,UAAU,EAAE,kCAAkC;UAC9CC,WAAW,EAAE,KAAK;UAClBC,YAAY,EAAE,IAAI;UAClBC,aAAa,EAAE,KAAK;UACpBC,QAAQ,EAAE;SACX,EACD;UACER,EAAE,EAAE,GAAG;UACPC,QAAQ,EAAE,mBAAmB;UAC7BC,QAAQ,EAAE,WAAW;UACrBC,MAAM,EAAE,6FAA6F;UACrGC,UAAU,EAAE,iCAAiC;UAC7CC,WAAW,EAAE,KAAK;UAClBC,YAAY,EAAE,KAAK;UACnBC,aAAa,EAAE,KAAK;UACpBC,QAAQ,EAAE;SACX,EACD;UACER,EAAE,EAAE,GAAG;UACPC,QAAQ,EAAE,YAAY;UACtBC,QAAQ,EAAE,oBAAoB;UAC9BC,MAAM,EAAE,6FAA6F;UACrGC,UAAU,EAAE,iCAAiC;UAC7CC,WAAW,EAAE,KAAK;UAClBC,YAAY,EAAE,IAAI;UAClBC,aAAa,EAAE,KAAK;UACpBC,QAAQ,EAAE;SACX,CACF;QAEDV,KAAI,CAAClB,SAAS,GAAG,KAAK;QACtBkB,KAAI,CAACW,uBAAuB,EAAE;OAC/B,CAAC,OAAO5B,KAAK,EAAE;QACd6B,OAAO,CAAC7B,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtDiB,KAAI,CAACjB,KAAK,GAAG,gCAAgC;QAC7CiB,KAAI,CAAClB,SAAS,GAAG,KAAK;;IACvB;EACH;EAEA+B,WAAWA,CAACC,IAAmB;IAC7B,IAAI,CAAClC,MAAM,CAACmC,QAAQ,CAAC,CAAC,UAAU,EAAED,IAAI,CAACX,QAAQ,CAAC,CAAC;EACnD;EAEAa,YAAYA,CAACF,IAAmB,EAAEG,KAAY;IAC5CA,KAAK,CAACC,eAAe,EAAE;IACvBJ,IAAI,CAACP,WAAW,GAAG,CAACO,IAAI,CAACP,WAAW;IAEpC,IAAIO,IAAI,CAACP,WAAW,EAAE;MACpBO,IAAI,CAACL,aAAa,EAAE;KACrB,MAAM;MACLK,IAAI,CAACL,aAAa,EAAE;;EAExB;EAEAU,mBAAmBA,CAACC,KAAa;IAC/B,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEAC,OAAOA,CAAA;IACL,IAAI,CAAC7B,kBAAkB,EAAE;EAC3B;EAEA8B,aAAaA,CAACC,KAAa,EAAEX,IAAmB;IAC9C,OAAOA,IAAI,CAACZ,EAAE;EAChB;EAEA;EACQwB,cAAcA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACnC,aAAa,IAAI,IAAI,CAACC,QAAQ,EAAE;IAE1C,IAAI,CAACO,aAAa,EAAE;IACpB,IAAI,CAAC4B,iBAAiB,GAAGC,WAAW,CAAC,MAAK;MACxC,IAAI,CAAC,IAAI,CAACpC,QAAQ,IAAI,IAAI,CAACX,cAAc,CAACgD,MAAM,GAAG,IAAI,CAACzC,YAAY,EAAE;QACpE,IAAI,CAAC0C,aAAa,EAAE;;IAExB,CAAC,EAAE,IAAI,CAACxC,cAAc,CAAC;EACzB;EAEQS,aAAaA,CAAA;IACnB,IAAI,IAAI,CAAC4B,iBAAiB,EAAE;MAC1BI,aAAa,CAAC,IAAI,CAACJ,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;EAEjC;EAEQG,aAAaA,CAAA;IACnB,IAAI,IAAI,CAAC7C,YAAY,IAAI,IAAI,CAACI,QAAQ,EAAE;MACtC,IAAI,CAACJ,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;IAErB,IAAI,CAAC+C,iBAAiB,EAAE;EAC1B;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAACzC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACO,aAAa,EAAE;EACtB;EAEAmC,eAAeA,CAAA;IACb,IAAI,CAAC1C,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACkC,cAAc,EAAE;EACvB;EAEA;EACQ/B,wBAAwBA,CAAA;IAC9B,MAAMwC,KAAK,GAAGC,MAAM,CAACC,UAAU;IAC/B,IAAIF,KAAK,IAAI,GAAG,EAAE;MAChB,IAAI,CAAChD,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAI+C,KAAK,IAAI,GAAG,EAAE;MACvB,IAAI,CAAChD,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAI+C,KAAK,IAAI,IAAI,EAAE;MACxB,IAAI,CAAChD,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACD,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;;IAEvB,IAAI,CAACkD,kBAAkB,EAAE;IACzB,IAAI,CAACN,iBAAiB,EAAE;EAC1B;EAEQpC,mBAAmBA,CAAA;IACzBwC,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MACrC,IAAI,CAAC5C,wBAAwB,EAAE;IACjC,CAAC,CAAC;EACJ;EAEA;EACA2C,kBAAkBA,CAAA;IAChB,IAAI,CAACjD,QAAQ,GAAGmD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC5D,cAAc,CAACgD,MAAM,GAAG,IAAI,CAACzC,YAAY,CAAC;EAC7E;EAEAsD,SAASA,CAAA;IACP,IAAI,IAAI,CAACzD,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,IAAI,CAAC+C,iBAAiB,EAAE;MACxB,IAAI,CAACW,gCAAgC,EAAE;;EAE3C;EAEAC,SAASA,CAAA;IACP,IAAI,IAAI,CAAC3D,YAAY,GAAG,IAAI,CAACI,QAAQ,EAAE;MACrC,IAAI,CAACJ,YAAY,EAAE;MACnB,IAAI,CAAC+C,iBAAiB,EAAE;MACxB,IAAI,CAACW,gCAAgC,EAAE;;EAE3C;EAEQX,iBAAiBA,CAAA;IACvB,IAAI,CAAC9C,WAAW,GAAG,CAAC,IAAI,CAACD,YAAY,GAAG,IAAI,CAACE,SAAS;EACxD;EAEQwD,gCAAgCA,CAAA;IACtC,IAAI,CAAC5C,aAAa,EAAE;IACpB8C,UAAU,CAAC,MAAK;MACd,IAAI,CAACnB,cAAc,EAAE;IACvB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACQf,uBAAuBA,CAAA;IAC7BkC,UAAU,CAAC,MAAK;MACd,IAAI,CAACP,kBAAkB,EAAE;MACzB,IAAI,CAACrD,YAAY,GAAG,CAAC;MACrB,IAAI,CAACC,WAAW,GAAG,CAAC;MACpB,IAAI,CAACwC,cAAc,EAAE;IACvB,CAAC,EAAE,GAAG,CAAC;EACT;CACD;AA7PYhD,wBAAwB,GAAAoE,UAAA,EAPpCzE,SAAS,CAAC;EACT0E,QAAQ,EAAE,uBAAuB;EACjCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC3E,YAAY,EAAEE,WAAW,EAAEC,cAAc,CAAC;EACpDyE,WAAW,EAAE,oCAAoC;EACjDC,SAAS,EAAE,CAAC,oCAAoC;CACjD,CAAC,C,EACWzE,wBAAwB,CA6PpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}