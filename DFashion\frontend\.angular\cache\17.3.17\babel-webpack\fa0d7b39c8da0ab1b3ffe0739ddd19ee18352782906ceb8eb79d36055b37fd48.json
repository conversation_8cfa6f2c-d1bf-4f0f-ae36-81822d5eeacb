{"ast": null, "code": "import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, APP_ID, Injectable, Inject, QueryList, isSignal, effect, booleanAttribute, Directive, Input, InjectionToken, Optional, EventEmitter, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/platform';\nimport { Platform, _getFocusedElementPierceShadowDom, normalizePassiveListenerOptions, _getEventTarget, _getShadowRoot } from '@angular/cdk/platform';\nimport { Subject, Subscription, BehaviorSubject, of } from 'rxjs';\nimport { hasModifierKey, A, Z, ZERO, NINE, PAGE_DOWN, PAGE_UP, END, HOME, LEFT_ARROW, RIGHT_ARROW, UP_ARROW, DOWN_ARROW, TAB, ALT, CONTROL, MAC_META, META, SHIFT } from '@angular/cdk/keycodes';\nimport { tap, debounceTime, filter, map, take, skip, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport * as i1$1 from '@angular/cdk/observers';\nimport { ObserversModule } from '@angular/cdk/observers';\nimport { coerceElement } from '@angular/cdk/coercion';\nimport { BreakpointObserver } from '@angular/cdk/layout';\n\n/** IDs are delimited by an empty space, as per the spec. */\nconst ID_DELIMITER = ' ';\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction addAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n  id = id.trim();\n  if (ids.some(existingId => existingId.trim() === id)) {\n    return;\n  }\n  ids.push(id);\n  el.setAttribute(attr, ids.join(ID_DELIMITER));\n}\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction removeAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n  id = id.trim();\n  const filteredIds = ids.filter(val => val !== id);\n  if (filteredIds.length) {\n    el.setAttribute(attr, filteredIds.join(ID_DELIMITER));\n  } else {\n    el.removeAttribute(attr);\n  }\n}\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction getAriaReferenceIds(el, attr) {\n  // Get string array of all individual ids (whitespace delimited) in the attribute value\n  const attrValue = el.getAttribute(attr);\n  return attrValue?.match(/\\S+/g) ?? [];\n}\n\n/**\n * ID used for the body container where all messages are appended.\n * @deprecated No longer being used. To be removed.\n * @breaking-change 14.0.0\n */\nconst MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n/**\n * ID prefix used for each created message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n/**\n * Attribute given to each host element that is described by a message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n/** Global incremental identifier for each registered message element. */\nlet nextId = 0;\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n */\nclass AriaDescriber {\n  constructor(_document,\n  /**\n   * @deprecated To be turned into a required parameter.\n   * @breaking-change 14.0.0\n   */\n  _platform) {\n    this._platform = _platform;\n    /** Map of all registered message elements that have been placed into the document. */\n    this._messageRegistry = new Map();\n    /** Container for all registered messages. */\n    this._messagesContainer = null;\n    /** Unique ID for the service. */\n    this._id = `${nextId++}`;\n    this._document = _document;\n    this._id = inject(APP_ID) + '-' + nextId++;\n  }\n  describe(hostElement, message, role) {\n    if (!this._canBeDescribed(hostElement, message)) {\n      return;\n    }\n    const key = getKey(message, role);\n    if (typeof message !== 'string') {\n      // We need to ensure that the element has an ID.\n      setMessageId(message, this._id);\n      this._messageRegistry.set(key, {\n        messageElement: message,\n        referenceCount: 0\n      });\n    } else if (!this._messageRegistry.has(key)) {\n      this._createMessageElement(message, role);\n    }\n    if (!this._isElementDescribedByMessage(hostElement, key)) {\n      this._addMessageReference(hostElement, key);\n    }\n  }\n  removeDescription(hostElement, message, role) {\n    if (!message || !this._isElementNode(hostElement)) {\n      return;\n    }\n    const key = getKey(message, role);\n    if (this._isElementDescribedByMessage(hostElement, key)) {\n      this._removeMessageReference(hostElement, key);\n    }\n    // If the message is a string, it means that it's one that we created for the\n    // consumer so we can remove it safely, otherwise we should leave it in place.\n    if (typeof message === 'string') {\n      const registeredMessage = this._messageRegistry.get(key);\n      if (registeredMessage && registeredMessage.referenceCount === 0) {\n        this._deleteMessageElement(key);\n      }\n    }\n    if (this._messagesContainer?.childNodes.length === 0) {\n      this._messagesContainer.remove();\n      this._messagesContainer = null;\n    }\n  }\n  /** Unregisters all created message elements and removes the message container. */\n  ngOnDestroy() {\n    const describedElements = this._document.querySelectorAll(`[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}=\"${this._id}\"]`);\n    for (let i = 0; i < describedElements.length; i++) {\n      this._removeCdkDescribedByReferenceIds(describedElements[i]);\n      describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n    this._messagesContainer?.remove();\n    this._messagesContainer = null;\n    this._messageRegistry.clear();\n  }\n  /**\n   * Creates a new element in the visually hidden message container element with the message\n   * as its content and adds it to the message registry.\n   */\n  _createMessageElement(message, role) {\n    const messageElement = this._document.createElement('div');\n    setMessageId(messageElement, this._id);\n    messageElement.textContent = message;\n    if (role) {\n      messageElement.setAttribute('role', role);\n    }\n    this._createMessagesContainer();\n    this._messagesContainer.appendChild(messageElement);\n    this._messageRegistry.set(getKey(message, role), {\n      messageElement,\n      referenceCount: 0\n    });\n  }\n  /** Deletes the message element from the global messages container. */\n  _deleteMessageElement(key) {\n    this._messageRegistry.get(key)?.messageElement?.remove();\n    this._messageRegistry.delete(key);\n  }\n  /** Creates the global container for all aria-describedby messages. */\n  _createMessagesContainer() {\n    if (this._messagesContainer) {\n      return;\n    }\n    const containerClassName = 'cdk-describedby-message-container';\n    const serverContainers = this._document.querySelectorAll(`.${containerClassName}[platform=\"server\"]`);\n    for (let i = 0; i < serverContainers.length; i++) {\n      // When going from the server to the client, we may end up in a situation where there's\n      // already a container on the page, but we don't have a reference to it. Clear the\n      // old container so we don't get duplicates. Doing this, instead of emptying the previous\n      // container, should be slightly faster.\n      serverContainers[i].remove();\n    }\n    const messagesContainer = this._document.createElement('div');\n    // We add `visibility: hidden` in order to prevent text in this container from\n    // being searchable by the browser's Ctrl + F functionality.\n    // Screen-readers will still read the description for elements with aria-describedby even\n    // when the description element is not visible.\n    messagesContainer.style.visibility = 'hidden';\n    // Even though we use `visibility: hidden`, we still apply `cdk-visually-hidden` so that\n    // the description element doesn't impact page layout.\n    messagesContainer.classList.add(containerClassName);\n    messagesContainer.classList.add('cdk-visually-hidden');\n    // @breaking-change 14.0.0 Remove null check for `_platform`.\n    if (this._platform && !this._platform.isBrowser) {\n      messagesContainer.setAttribute('platform', 'server');\n    }\n    this._document.body.appendChild(messagesContainer);\n    this._messagesContainer = messagesContainer;\n  }\n  /** Removes all cdk-describedby messages that are hosted through the element. */\n  _removeCdkDescribedByReferenceIds(element) {\n    // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n    const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby').filter(id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0);\n    element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n  }\n  /**\n   * Adds a message reference to the element using aria-describedby and increments the registered\n   * message's reference count.\n   */\n  _addMessageReference(element, key) {\n    const registeredMessage = this._messageRegistry.get(key);\n    // Add the aria-describedby reference and set the\n    // describedby_host attribute to mark the element.\n    addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, this._id);\n    registeredMessage.referenceCount++;\n  }\n  /**\n   * Removes a message reference from the element using aria-describedby\n   * and decrements the registered message's reference count.\n   */\n  _removeMessageReference(element, key) {\n    const registeredMessage = this._messageRegistry.get(key);\n    registeredMessage.referenceCount--;\n    removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n  }\n  /** Returns true if the element has been described by the provided message ID. */\n  _isElementDescribedByMessage(element, key) {\n    const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n    const registeredMessage = this._messageRegistry.get(key);\n    const messageId = registeredMessage && registeredMessage.messageElement.id;\n    return !!messageId && referenceIds.indexOf(messageId) != -1;\n  }\n  /** Determines whether a message can be described on a particular element. */\n  _canBeDescribed(element, message) {\n    if (!this._isElementNode(element)) {\n      return false;\n    }\n    if (message && typeof message === 'object') {\n      // We'd have to make some assumptions about the description element's text, if the consumer\n      // passed in an element. Assume that if an element is passed in, the consumer has verified\n      // that it can be used as a description.\n      return true;\n    }\n    const trimmedMessage = message == null ? '' : `${message}`.trim();\n    const ariaLabel = element.getAttribute('aria-label');\n    // We shouldn't set descriptions if they're exactly the same as the `aria-label` of the\n    // element, because screen readers will end up reading out the same text twice in a row.\n    return trimmedMessage ? !ariaLabel || ariaLabel.trim() !== trimmedMessage : false;\n  }\n  /** Checks whether a node is an Element node. */\n  _isElementNode(element) {\n    return element.nodeType === this._document.ELEMENT_NODE;\n  }\n  static {\n    this.ɵfac = function AriaDescriber_Factory(t) {\n      return new (t || AriaDescriber)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1.Platform));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AriaDescriber,\n      factory: AriaDescriber.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AriaDescriber, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i1.Platform\n  }], null);\n})();\n/** Gets a key that can be used to look messages up in the registry. */\nfunction getKey(message, role) {\n  return typeof message === 'string' ? `${role || ''}/${message}` : message;\n}\n/** Assigns a unique ID to an element, if it doesn't have one already. */\nfunction setMessageId(element, serviceId) {\n  if (!element.id) {\n    element.id = `${CDK_DESCRIBEDBY_ID_PREFIX}-${serviceId}-${nextId++}`;\n  }\n}\n\n/**\n * This class manages keyboard events for selectable lists. If you pass it a query list\n * of items, it will set the active item correctly when arrow events occur.\n */\nclass ListKeyManager {\n  constructor(_items, injector) {\n    this._items = _items;\n    this._activeItemIndex = -1;\n    this._activeItem = null;\n    this._wrap = false;\n    this._letterKeyStream = new Subject();\n    this._typeaheadSubscription = Subscription.EMPTY;\n    this._vertical = true;\n    this._allowedModifierKeys = [];\n    this._homeAndEnd = false;\n    this._pageUpAndDown = {\n      enabled: false,\n      delta: 10\n    };\n    /**\n     * Predicate function that can be used to check whether an item should be skipped\n     * by the key manager. By default, disabled items are skipped.\n     */\n    this._skipPredicateFn = item => item.disabled;\n    // Buffer for the letters that the user has pressed when the typeahead option is turned on.\n    this._pressedLetters = [];\n    /**\n     * Stream that emits any time the TAB key is pressed, so components can react\n     * when focus is shifted off of the list.\n     */\n    this.tabOut = new Subject();\n    /** Stream that emits whenever the active item of the list manager changes. */\n    this.change = new Subject();\n    // We allow for the items to be an array because, in some cases, the consumer may\n    // not have access to a QueryList of the items they want to manage (e.g. when the\n    // items aren't being collected via `ViewChildren` or `ContentChildren`).\n    if (_items instanceof QueryList) {\n      this._itemChangesSubscription = _items.changes.subscribe(newItems => this._itemsChanged(newItems.toArray()));\n    } else if (isSignal(_items)) {\n      if (!injector && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw new Error('ListKeyManager constructed with a signal must receive an injector');\n      }\n      this._effectRef = effect(() => this._itemsChanged(_items()), {\n        injector\n      });\n    }\n  }\n  /**\n   * Sets the predicate function that determines which items should be skipped by the\n   * list key manager.\n   * @param predicate Function that determines whether the given item should be skipped.\n   */\n  skipPredicate(predicate) {\n    this._skipPredicateFn = predicate;\n    return this;\n  }\n  /**\n   * Configures wrapping mode, which determines whether the active item will wrap to\n   * the other end of list when there are no more items in the given direction.\n   * @param shouldWrap Whether the list should wrap when reaching the end.\n   */\n  withWrap(shouldWrap = true) {\n    this._wrap = shouldWrap;\n    return this;\n  }\n  /**\n   * Configures whether the key manager should be able to move the selection vertically.\n   * @param enabled Whether vertical selection should be enabled.\n   */\n  withVerticalOrientation(enabled = true) {\n    this._vertical = enabled;\n    return this;\n  }\n  /**\n   * Configures the key manager to move the selection horizontally.\n   * Passing in `null` will disable horizontal movement.\n   * @param direction Direction in which the selection can be moved.\n   */\n  withHorizontalOrientation(direction) {\n    this._horizontal = direction;\n    return this;\n  }\n  /**\n   * Modifier keys which are allowed to be held down and whose default actions will be prevented\n   * as the user is pressing the arrow keys. Defaults to not allowing any modifier keys.\n   */\n  withAllowedModifierKeys(keys) {\n    this._allowedModifierKeys = keys;\n    return this;\n  }\n  /**\n   * Turns on typeahead mode which allows users to set the active item by typing.\n   * @param debounceInterval Time to wait after the last keystroke before setting the active item.\n   */\n  withTypeAhead(debounceInterval = 200) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const items = this._getItemsArray();\n      if (items.length > 0 && items.some(item => typeof item.getLabel !== 'function')) {\n        throw Error('ListKeyManager items in typeahead mode must implement the `getLabel` method.');\n      }\n    }\n    this._typeaheadSubscription.unsubscribe();\n    // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n    // and convert those letters back into a string. Afterwards find the first item that starts\n    // with that string and select it.\n    this._typeaheadSubscription = this._letterKeyStream.pipe(tap(letter => this._pressedLetters.push(letter)), debounceTime(debounceInterval), filter(() => this._pressedLetters.length > 0), map(() => this._pressedLetters.join(''))).subscribe(inputString => {\n      const items = this._getItemsArray();\n      // Start at 1 because we want to start searching at the item immediately\n      // following the current active item.\n      for (let i = 1; i < items.length + 1; i++) {\n        const index = (this._activeItemIndex + i) % items.length;\n        const item = items[index];\n        if (!this._skipPredicateFn(item) && item.getLabel().toUpperCase().trim().indexOf(inputString) === 0) {\n          this.setActiveItem(index);\n          break;\n        }\n      }\n      this._pressedLetters = [];\n    });\n    return this;\n  }\n  /** Cancels the current typeahead sequence. */\n  cancelTypeahead() {\n    this._pressedLetters = [];\n    return this;\n  }\n  /**\n   * Configures the key manager to activate the first and last items\n   * respectively when the Home or End key is pressed.\n   * @param enabled Whether pressing the Home or End key activates the first/last item.\n   */\n  withHomeAndEnd(enabled = true) {\n    this._homeAndEnd = enabled;\n    return this;\n  }\n  /**\n   * Configures the key manager to activate every 10th, configured or first/last element in up/down direction\n   * respectively when the Page-Up or Page-Down key is pressed.\n   * @param enabled Whether pressing the Page-Up or Page-Down key activates the first/last item.\n   * @param delta Whether pressing the Home or End key activates the first/last item.\n   */\n  withPageUpDown(enabled = true, delta = 10) {\n    this._pageUpAndDown = {\n      enabled,\n      delta\n    };\n    return this;\n  }\n  setActiveItem(item) {\n    const previousActiveItem = this._activeItem;\n    this.updateActiveItem(item);\n    if (this._activeItem !== previousActiveItem) {\n      this.change.next(this._activeItemIndex);\n    }\n  }\n  /**\n   * Sets the active item depending on the key event passed in.\n   * @param event Keyboard event to be used for determining which element should be active.\n   */\n  onKeydown(event) {\n    const keyCode = event.keyCode;\n    const modifiers = ['altKey', 'ctrlKey', 'metaKey', 'shiftKey'];\n    const isModifierAllowed = modifiers.every(modifier => {\n      return !event[modifier] || this._allowedModifierKeys.indexOf(modifier) > -1;\n    });\n    switch (keyCode) {\n      case TAB:\n        this.tabOut.next();\n        return;\n      case DOWN_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n      case UP_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n      case RIGHT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setPreviousItemActive() : this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n      case LEFT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setNextItemActive() : this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n      case HOME:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setFirstItemActive();\n          break;\n        } else {\n          return;\n        }\n      case END:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setLastItemActive();\n          break;\n        } else {\n          return;\n        }\n      case PAGE_UP:\n        if (this._pageUpAndDown.enabled && isModifierAllowed) {\n          const targetIndex = this._activeItemIndex - this._pageUpAndDown.delta;\n          this._setActiveItemByIndex(targetIndex > 0 ? targetIndex : 0, 1);\n          break;\n        } else {\n          return;\n        }\n      case PAGE_DOWN:\n        if (this._pageUpAndDown.enabled && isModifierAllowed) {\n          const targetIndex = this._activeItemIndex + this._pageUpAndDown.delta;\n          const itemsLength = this._getItemsArray().length;\n          this._setActiveItemByIndex(targetIndex < itemsLength ? targetIndex : itemsLength - 1, -1);\n          break;\n        } else {\n          return;\n        }\n      default:\n        if (isModifierAllowed || hasModifierKey(event, 'shiftKey')) {\n          // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n          // otherwise fall back to resolving alphanumeric characters via the keyCode.\n          if (event.key && event.key.length === 1) {\n            this._letterKeyStream.next(event.key.toLocaleUpperCase());\n          } else if (keyCode >= A && keyCode <= Z || keyCode >= ZERO && keyCode <= NINE) {\n            this._letterKeyStream.next(String.fromCharCode(keyCode));\n          }\n        }\n        // Note that we return here, in order to avoid preventing\n        // the default action of non-navigational keys.\n        return;\n    }\n    this._pressedLetters = [];\n    event.preventDefault();\n  }\n  /** Index of the currently active item. */\n  get activeItemIndex() {\n    return this._activeItemIndex;\n  }\n  /** The active item. */\n  get activeItem() {\n    return this._activeItem;\n  }\n  /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n  isTyping() {\n    return this._pressedLetters.length > 0;\n  }\n  /** Sets the active item to the first enabled item in the list. */\n  setFirstItemActive() {\n    this._setActiveItemByIndex(0, 1);\n  }\n  /** Sets the active item to the last enabled item in the list. */\n  setLastItemActive() {\n    this._setActiveItemByIndex(this._getItemsArray().length - 1, -1);\n  }\n  /** Sets the active item to the next enabled item in the list. */\n  setNextItemActive() {\n    this._activeItemIndex < 0 ? this.setFirstItemActive() : this._setActiveItemByDelta(1);\n  }\n  /** Sets the active item to a previous enabled item in the list. */\n  setPreviousItemActive() {\n    this._activeItemIndex < 0 && this._wrap ? this.setLastItemActive() : this._setActiveItemByDelta(-1);\n  }\n  updateActiveItem(item) {\n    const itemArray = this._getItemsArray();\n    const index = typeof item === 'number' ? item : itemArray.indexOf(item);\n    const activeItem = itemArray[index];\n    // Explicitly check for `null` and `undefined` because other falsy values are valid.\n    this._activeItem = activeItem == null ? null : activeItem;\n    this._activeItemIndex = index;\n  }\n  /** Cleans up the key manager. */\n  destroy() {\n    this._typeaheadSubscription.unsubscribe();\n    this._itemChangesSubscription?.unsubscribe();\n    this._effectRef?.destroy();\n    this._letterKeyStream.complete();\n    this.tabOut.complete();\n    this.change.complete();\n    this._pressedLetters = [];\n  }\n  /**\n   * This method sets the active item, given a list of items and the delta between the\n   * currently active item and the new active item. It will calculate differently\n   * depending on whether wrap mode is turned on.\n   */\n  _setActiveItemByDelta(delta) {\n    this._wrap ? this._setActiveInWrapMode(delta) : this._setActiveInDefaultMode(delta);\n  }\n  /**\n   * Sets the active item properly given \"wrap\" mode. In other words, it will continue to move\n   * down the list until it finds an item that is not disabled, and it will wrap if it\n   * encounters either end of the list.\n   */\n  _setActiveInWrapMode(delta) {\n    const items = this._getItemsArray();\n    for (let i = 1; i <= items.length; i++) {\n      const index = (this._activeItemIndex + delta * i + items.length) % items.length;\n      const item = items[index];\n      if (!this._skipPredicateFn(item)) {\n        this.setActiveItem(index);\n        return;\n      }\n    }\n  }\n  /**\n   * Sets the active item properly given the default mode. In other words, it will\n   * continue to move down the list until it finds an item that is not disabled. If\n   * it encounters either end of the list, it will stop and not wrap.\n   */\n  _setActiveInDefaultMode(delta) {\n    this._setActiveItemByIndex(this._activeItemIndex + delta, delta);\n  }\n  /**\n   * Sets the active item to the first enabled item starting at the index specified. If the\n   * item is disabled, it will move in the fallbackDelta direction until it either\n   * finds an enabled item or encounters the end of the list.\n   */\n  _setActiveItemByIndex(index, fallbackDelta) {\n    const items = this._getItemsArray();\n    if (!items[index]) {\n      return;\n    }\n    while (this._skipPredicateFn(items[index])) {\n      index += fallbackDelta;\n      if (!items[index]) {\n        return;\n      }\n    }\n    this.setActiveItem(index);\n  }\n  /** Returns the items as an array. */\n  _getItemsArray() {\n    if (isSignal(this._items)) {\n      return this._items();\n    }\n    return this._items instanceof QueryList ? this._items.toArray() : this._items;\n  }\n  /** Callback for when the items have changed. */\n  _itemsChanged(newItems) {\n    if (this._activeItem) {\n      const newIndex = newItems.indexOf(this._activeItem);\n      if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n        this._activeItemIndex = newIndex;\n      }\n    }\n  }\n}\nclass ActiveDescendantKeyManager extends ListKeyManager {\n  setActiveItem(index) {\n    if (this.activeItem) {\n      this.activeItem.setInactiveStyles();\n    }\n    super.setActiveItem(index);\n    if (this.activeItem) {\n      this.activeItem.setActiveStyles();\n    }\n  }\n}\nclass FocusKeyManager extends ListKeyManager {\n  constructor() {\n    super(...arguments);\n    this._origin = 'program';\n  }\n  /**\n   * Sets the focus origin that will be passed in to the items for any subsequent `focus` calls.\n   * @param origin Focus origin to be used when focusing items.\n   */\n  setFocusOrigin(origin) {\n    this._origin = origin;\n    return this;\n  }\n  setActiveItem(item) {\n    super.setActiveItem(item);\n    if (this.activeItem) {\n      this.activeItem.focus(this._origin);\n    }\n  }\n}\n\n/**\n * Configuration for the isFocusable method.\n */\nclass IsFocusableConfig {\n  constructor() {\n    /**\n     * Whether to count an element as focusable even if it is not currently visible.\n     */\n    this.ignoreVisibility = false;\n  }\n}\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n/**\n * Utility for checking the interactivity of an element, such as whether it is focusable or\n * tabbable.\n */\nclass InteractivityChecker {\n  constructor(_platform) {\n    this._platform = _platform;\n  }\n  /**\n   * Gets whether an element is disabled.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is disabled.\n   */\n  isDisabled(element) {\n    // This does not capture some cases, such as a non-form control with a disabled attribute or\n    // a form control inside of a disabled form, but should capture the most common cases.\n    return element.hasAttribute('disabled');\n  }\n  /**\n   * Gets whether an element is visible for the purposes of interactivity.\n   *\n   * This will capture states like `display: none` and `visibility: hidden`, but not things like\n   * being clipped by an `overflow: hidden` parent or being outside the viewport.\n   *\n   * @returns Whether the element is visible.\n   */\n  isVisible(element) {\n    return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n  }\n  /**\n   * Gets whether an element can be reached via Tab key.\n   * Assumes that the element has already been checked with isFocusable.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is tabbable.\n   */\n  isTabbable(element) {\n    // Nothing is tabbable on the server 😎\n    if (!this._platform.isBrowser) {\n      return false;\n    }\n    const frameElement = getFrameElement(getWindow(element));\n    if (frameElement) {\n      // Frame elements inherit their tabindex onto all child elements.\n      if (getTabIndexValue(frameElement) === -1) {\n        return false;\n      }\n      // Browsers disable tabbing to an element inside of an invisible frame.\n      if (!this.isVisible(frameElement)) {\n        return false;\n      }\n    }\n    let nodeName = element.nodeName.toLowerCase();\n    let tabIndexValue = getTabIndexValue(element);\n    if (element.hasAttribute('contenteditable')) {\n      return tabIndexValue !== -1;\n    }\n    if (nodeName === 'iframe' || nodeName === 'object') {\n      // The frame or object's content may be tabbable depending on the content, but it's\n      // not possibly to reliably detect the content of the frames. We always consider such\n      // elements as non-tabbable.\n      return false;\n    }\n    // In iOS, the browser only considers some specific elements as tabbable.\n    if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n      return false;\n    }\n    if (nodeName === 'audio') {\n      // Audio elements without controls enabled are never tabbable, regardless\n      // of the tabindex attribute explicitly being set.\n      if (!element.hasAttribute('controls')) {\n        return false;\n      }\n      // Audio elements with controls are by default tabbable unless the\n      // tabindex attribute is set to `-1` explicitly.\n      return tabIndexValue !== -1;\n    }\n    if (nodeName === 'video') {\n      // For all video elements, if the tabindex attribute is set to `-1`, the video\n      // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n      // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n      // tabindex attribute is the source of truth here.\n      if (tabIndexValue === -1) {\n        return false;\n      }\n      // If the tabindex is explicitly set, and not `-1` (as per check before), the\n      // video element is always tabbable (regardless of whether it has controls or not).\n      if (tabIndexValue !== null) {\n        return true;\n      }\n      // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n      // has controls enabled. Firefox is special as videos are always tabbable regardless\n      // of whether there are controls or not.\n      return this._platform.FIREFOX || element.hasAttribute('controls');\n    }\n    return element.tabIndex >= 0;\n  }\n  /**\n   * Gets whether an element can be focused by the user.\n   *\n   * @param element Element to be checked.\n   * @param config The config object with options to customize this method's behavior\n   * @returns Whether the element is focusable.\n   */\n  isFocusable(element, config) {\n    // Perform checks in order of left to most expensive.\n    // Again, naive approach that does not capture many edge cases and browser quirks.\n    return isPotentiallyFocusable(element) && !this.isDisabled(element) && (config?.ignoreVisibility || this.isVisible(element));\n  }\n  static {\n    this.ɵfac = function InteractivityChecker_Factory(t) {\n      return new (t || InteractivityChecker)(i0.ɵɵinject(i1.Platform));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: InteractivityChecker,\n      factory: InteractivityChecker.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InteractivityChecker, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.Platform\n  }], null);\n})();\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window) {\n  try {\n    return window.frameElement;\n  } catch {\n    return null;\n  }\n}\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element) {\n  // Use logic from jQuery to check for an invisible element.\n  // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n  return !!(element.offsetWidth || element.offsetHeight || typeof element.getClientRects === 'function' && element.getClientRects().length);\n}\n/** Gets whether an element's  */\nfunction isNativeFormElement(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  return nodeName === 'input' || nodeName === 'select' || nodeName === 'button' || nodeName === 'textarea';\n}\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element) {\n  return isInputElement(element) && element.type == 'hidden';\n}\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element) {\n  return isAnchorElement(element) && element.hasAttribute('href');\n}\n/** Gets whether an element is an input element. */\nfunction isInputElement(element) {\n  return element.nodeName.toLowerCase() == 'input';\n}\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element) {\n  return element.nodeName.toLowerCase() == 'a';\n}\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element) {\n  if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n    return false;\n  }\n  let tabIndex = element.getAttribute('tabindex');\n  return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element) {\n  if (!hasValidTabIndex(element)) {\n    return null;\n  }\n  // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n  const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n  return isNaN(tabIndex) ? -1 : tabIndex;\n}\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  let inputType = nodeName === 'input' && element.type;\n  return inputType === 'text' || inputType === 'password' || nodeName === 'select' || nodeName === 'textarea';\n}\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element) {\n  // Inputs are potentially focusable *unless* they're type=\"hidden\".\n  if (isHiddenInput(element)) {\n    return false;\n  }\n  return isNativeFormElement(element) || isAnchorWithHref(element) || element.hasAttribute('contenteditable') || hasValidTabIndex(element);\n}\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node) {\n  // ownerDocument is null if `node` itself *is* a document.\n  return node.ownerDocument && node.ownerDocument.defaultView || window;\n}\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to be misaligned.\n */\nclass FocusTrap {\n  /** Whether the focus trap is active. */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    this._enabled = value;\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(value, this._startAnchor);\n      this._toggleAnchorTabIndex(value, this._endAnchor);\n    }\n  }\n  constructor(_element, _checker, _ngZone, _document, deferAnchors = false) {\n    this._element = _element;\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._document = _document;\n    this._hasAttached = false;\n    // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n    this.startAnchorListener = () => this.focusLastTabbableElement();\n    this.endAnchorListener = () => this.focusFirstTabbableElement();\n    this._enabled = true;\n    if (!deferAnchors) {\n      this.attachAnchors();\n    }\n  }\n  /** Destroys the focus trap by cleaning up the anchors. */\n  destroy() {\n    const startAnchor = this._startAnchor;\n    const endAnchor = this._endAnchor;\n    if (startAnchor) {\n      startAnchor.removeEventListener('focus', this.startAnchorListener);\n      startAnchor.remove();\n    }\n    if (endAnchor) {\n      endAnchor.removeEventListener('focus', this.endAnchorListener);\n      endAnchor.remove();\n    }\n    this._startAnchor = this._endAnchor = null;\n    this._hasAttached = false;\n  }\n  /**\n   * Inserts the anchors into the DOM. This is usually done automatically\n   * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n   * @returns Whether the focus trap managed to attach successfully. This may not be the case\n   * if the target element isn't currently in the DOM.\n   */\n  attachAnchors() {\n    // If we're not on the browser, there can be no focus to trap.\n    if (this._hasAttached) {\n      return true;\n    }\n    this._ngZone.runOutsideAngular(() => {\n      if (!this._startAnchor) {\n        this._startAnchor = this._createAnchor();\n        this._startAnchor.addEventListener('focus', this.startAnchorListener);\n      }\n      if (!this._endAnchor) {\n        this._endAnchor = this._createAnchor();\n        this._endAnchor.addEventListener('focus', this.endAnchorListener);\n      }\n    });\n    if (this._element.parentNode) {\n      this._element.parentNode.insertBefore(this._startAnchor, this._element);\n      this._element.parentNode.insertBefore(this._endAnchor, this._element.nextSibling);\n      this._hasAttached = true;\n    }\n    return this._hasAttached;\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses the first tabbable element.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusInitialElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusInitialElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the first tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusFirstTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusFirstTabbableElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the last tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusLastTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusLastTabbableElement(options)));\n    });\n  }\n  /**\n   * Get the specified boundary element of the trapped region.\n   * @param bound The boundary to get (start or end of trapped region).\n   * @returns The boundary element.\n   */\n  _getRegionBoundary(bound) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` + `[cdkFocusRegion${bound}], ` + `[cdk-focus-${bound}]`);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      for (let i = 0; i < markers.length; i++) {\n        // @breaking-change 8.0.0\n        if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated ` + `attribute will be removed in 8.0.0.`, markers[i]);\n        } else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` + `will be removed in 8.0.0.`, markers[i]);\n        }\n      }\n    }\n    if (bound == 'start') {\n      return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n    }\n    return markers.length ? markers[markers.length - 1] : this._getLastTabbableElement(this._element);\n  }\n  /**\n   * Focuses the element that should be focused when the focus trap is initialized.\n   * @returns Whether focus was moved successfully.\n   */\n  focusInitialElement(options) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` + `[cdkFocusInitial]`);\n    if (redirectToElement) {\n      // @breaking-change 8.0.0\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && redirectToElement.hasAttribute(`cdk-focus-initial`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-initial', ` + `use 'cdkFocusInitial' instead. The deprecated attribute ` + `will be removed in 8.0.0`, redirectToElement);\n      }\n      // Warn the consumer if the element they've pointed to\n      // isn't focusable, when not in production mode.\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._checker.isFocusable(redirectToElement)) {\n        console.warn(`Element matching '[cdkFocusInitial]' is not focusable.`, redirectToElement);\n      }\n      if (!this._checker.isFocusable(redirectToElement)) {\n        const focusableChild = this._getFirstTabbableElement(redirectToElement);\n        focusableChild?.focus(options);\n        return !!focusableChild;\n      }\n      redirectToElement.focus(options);\n      return true;\n    }\n    return this.focusFirstTabbableElement(options);\n  }\n  /**\n   * Focuses the first tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusFirstTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('start');\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n    return !!redirectToElement;\n  }\n  /**\n   * Focuses the last tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusLastTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('end');\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n    return !!redirectToElement;\n  }\n  /**\n   * Checks whether the focus trap has successfully been attached.\n   */\n  hasAttached() {\n    return this._hasAttached;\n  }\n  /** Get the first tabbable element from a DOM subtree (inclusive). */\n  _getFirstTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n    const children = root.children;\n    for (let i = 0; i < children.length; i++) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getFirstTabbableElement(children[i]) : null;\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n    return null;\n  }\n  /** Get the last tabbable element from a DOM subtree (inclusive). */\n  _getLastTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n    // Iterate in reverse DOM order.\n    const children = root.children;\n    for (let i = children.length - 1; i >= 0; i--) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getLastTabbableElement(children[i]) : null;\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n    return null;\n  }\n  /** Creates an anchor element. */\n  _createAnchor() {\n    const anchor = this._document.createElement('div');\n    this._toggleAnchorTabIndex(this._enabled, anchor);\n    anchor.classList.add('cdk-visually-hidden');\n    anchor.classList.add('cdk-focus-trap-anchor');\n    anchor.setAttribute('aria-hidden', 'true');\n    return anchor;\n  }\n  /**\n   * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n   * @param isEnabled Whether the focus trap is enabled.\n   * @param anchor Anchor on which to toggle the tabindex.\n   */\n  _toggleAnchorTabIndex(isEnabled, anchor) {\n    // Remove the tabindex completely, rather than setting it to -1, because if the\n    // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n    isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n  }\n  /**\n   * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n   * @param enabled: Whether the anchors should trap Tab.\n   */\n  toggleAnchors(enabled) {\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(enabled, this._startAnchor);\n      this._toggleAnchorTabIndex(enabled, this._endAnchor);\n    }\n  }\n  /** Executes a function when the zone is stable. */\n  _executeOnStable(fn) {\n    if (this._ngZone.isStable) {\n      fn();\n    } else {\n      this._ngZone.onStable.pipe(take(1)).subscribe(fn);\n    }\n  }\n}\n/**\n * Factory that allows easy instantiation of focus traps.\n */\nclass FocusTrapFactory {\n  constructor(_checker, _ngZone, _document) {\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._document = _document;\n  }\n  /**\n   * Creates a focus-trapped region around the given element.\n   * @param element The element around which focus will be trapped.\n   * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n   *     manually by the user.\n   * @returns The created focus trap instance.\n   */\n  create(element, deferCaptureElements = false) {\n    return new FocusTrap(element, this._checker, this._ngZone, this._document, deferCaptureElements);\n  }\n  static {\n    this.ɵfac = function FocusTrapFactory_Factory(t) {\n      return new (t || FocusTrapFactory)(i0.ɵɵinject(InteractivityChecker), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FocusTrapFactory,\n      factory: FocusTrapFactory.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: InteractivityChecker\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n/** Directive for trapping focus within a region. */\nclass CdkTrapFocus {\n  /** Whether the focus trap is active. */\n  get enabled() {\n    return this.focusTrap?.enabled || false;\n  }\n  set enabled(value) {\n    if (this.focusTrap) {\n      this.focusTrap.enabled = value;\n    }\n  }\n  constructor(_elementRef, _focusTrapFactory,\n  /**\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 13.0.0\n   */\n  _document) {\n    this._elementRef = _elementRef;\n    this._focusTrapFactory = _focusTrapFactory;\n    /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n    this._previouslyFocusedElement = null;\n    const platform = inject(Platform);\n    if (platform.isBrowser) {\n      this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n    }\n  }\n  ngOnDestroy() {\n    this.focusTrap?.destroy();\n    // If we stored a previously focused element when using autoCapture, return focus to that\n    // element now that the trapped region is being destroyed.\n    if (this._previouslyFocusedElement) {\n      this._previouslyFocusedElement.focus();\n      this._previouslyFocusedElement = null;\n    }\n  }\n  ngAfterContentInit() {\n    this.focusTrap?.attachAnchors();\n    if (this.autoCapture) {\n      this._captureFocus();\n    }\n  }\n  ngDoCheck() {\n    if (this.focusTrap && !this.focusTrap.hasAttached()) {\n      this.focusTrap.attachAnchors();\n    }\n  }\n  ngOnChanges(changes) {\n    const autoCaptureChange = changes['autoCapture'];\n    if (autoCaptureChange && !autoCaptureChange.firstChange && this.autoCapture && this.focusTrap?.hasAttached()) {\n      this._captureFocus();\n    }\n  }\n  _captureFocus() {\n    this._previouslyFocusedElement = _getFocusedElementPierceShadowDom();\n    this.focusTrap?.focusInitialElementWhenReady();\n  }\n  static {\n    this.ɵfac = function CdkTrapFocus_Factory(t) {\n      return new (t || CdkTrapFocus)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(FocusTrapFactory), i0.ɵɵdirectiveInject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkTrapFocus,\n      selectors: [[\"\", \"cdkTrapFocus\", \"\"]],\n      inputs: {\n        enabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkTrapFocus\", \"enabled\", booleanAttribute],\n        autoCapture: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkTrapFocusAutoCapture\", \"autoCapture\", booleanAttribute]\n      },\n      exportAs: [\"cdkTrapFocus\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTrapFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTrapFocus]',\n      exportAs: 'cdkTrapFocus',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: FocusTrapFactory\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], {\n    enabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTrapFocus',\n        transform: booleanAttribute\n      }]\n    }],\n    autoCapture: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTrapFocusAutoCapture',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class uses a strategy pattern that determines how it traps focus.\n * See FocusTrapInertStrategy.\n */\nclass ConfigurableFocusTrap extends FocusTrap {\n  /** Whether the FocusTrap is enabled. */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    this._enabled = value;\n    if (this._enabled) {\n      this._focusTrapManager.register(this);\n    } else {\n      this._focusTrapManager.deregister(this);\n    }\n  }\n  constructor(_element, _checker, _ngZone, _document, _focusTrapManager, _inertStrategy, config) {\n    super(_element, _checker, _ngZone, _document, config.defer);\n    this._focusTrapManager = _focusTrapManager;\n    this._inertStrategy = _inertStrategy;\n    this._focusTrapManager.register(this);\n  }\n  /** Notifies the FocusTrapManager that this FocusTrap will be destroyed. */\n  destroy() {\n    this._focusTrapManager.deregister(this);\n    super.destroy();\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _enable() {\n    this._inertStrategy.preventFocus(this);\n    this.toggleAnchors(true);\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _disable() {\n    this._inertStrategy.allowFocus(this);\n    this.toggleAnchors(false);\n  }\n}\n\n/** The injection token used to specify the inert strategy. */\nconst FOCUS_TRAP_INERT_STRATEGY = new InjectionToken('FOCUS_TRAP_INERT_STRATEGY');\n\n/**\n * Lightweight FocusTrapInertStrategy that adds a document focus event\n * listener to redirect focus back inside the FocusTrap.\n */\nclass EventListenerFocusTrapInertStrategy {\n  constructor() {\n    /** Focus event handler. */\n    this._listener = null;\n  }\n  /** Adds a document event listener that keeps focus inside the FocusTrap. */\n  preventFocus(focusTrap) {\n    // Ensure there's only one listener per document\n    if (this._listener) {\n      focusTrap._document.removeEventListener('focus', this._listener, true);\n    }\n    this._listener = e => this._trapFocus(focusTrap, e);\n    focusTrap._ngZone.runOutsideAngular(() => {\n      focusTrap._document.addEventListener('focus', this._listener, true);\n    });\n  }\n  /** Removes the event listener added in preventFocus. */\n  allowFocus(focusTrap) {\n    if (!this._listener) {\n      return;\n    }\n    focusTrap._document.removeEventListener('focus', this._listener, true);\n    this._listener = null;\n  }\n  /**\n   * Refocuses the first element in the FocusTrap if the focus event target was outside\n   * the FocusTrap.\n   *\n   * This is an event listener callback. The event listener is added in runOutsideAngular,\n   * so all this code runs outside Angular as well.\n   */\n  _trapFocus(focusTrap, event) {\n    const target = event.target;\n    const focusTrapRoot = focusTrap._element;\n    // Don't refocus if target was in an overlay, because the overlay might be associated\n    // with an element inside the FocusTrap, ex. mat-select.\n    if (target && !focusTrapRoot.contains(target) && !target.closest?.('div.cdk-overlay-pane')) {\n      // Some legacy FocusTrap usages have logic that focuses some element on the page\n      // just before FocusTrap is destroyed. For backwards compatibility, wait\n      // to be sure FocusTrap is still enabled before refocusing.\n      setTimeout(() => {\n        // Check whether focus wasn't put back into the focus trap while the timeout was pending.\n        if (focusTrap.enabled && !focusTrapRoot.contains(focusTrap._document.activeElement)) {\n          focusTrap.focusFirstTabbableElement();\n        }\n      });\n    }\n  }\n}\n\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\nclass FocusTrapManager {\n  constructor() {\n    // A stack of the FocusTraps on the page. Only the FocusTrap at the\n    // top of the stack is active.\n    this._focusTrapStack = [];\n  }\n  /**\n   * Disables the FocusTrap at the top of the stack, and then pushes\n   * the new FocusTrap onto the stack.\n   */\n  register(focusTrap) {\n    // Dedupe focusTraps that register multiple times.\n    this._focusTrapStack = this._focusTrapStack.filter(ft => ft !== focusTrap);\n    let stack = this._focusTrapStack;\n    if (stack.length) {\n      stack[stack.length - 1]._disable();\n    }\n    stack.push(focusTrap);\n    focusTrap._enable();\n  }\n  /**\n   * Removes the FocusTrap from the stack, and activates the\n   * FocusTrap that is the new top of the stack.\n   */\n  deregister(focusTrap) {\n    focusTrap._disable();\n    const stack = this._focusTrapStack;\n    const i = stack.indexOf(focusTrap);\n    if (i !== -1) {\n      stack.splice(i, 1);\n      if (stack.length) {\n        stack[stack.length - 1]._enable();\n      }\n    }\n  }\n  static {\n    this.ɵfac = function FocusTrapManager_Factory(t) {\n      return new (t || FocusTrapManager)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FocusTrapManager,\n      factory: FocusTrapManager.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapManager, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/** Factory that allows easy instantiation of configurable focus traps. */\nclass ConfigurableFocusTrapFactory {\n  constructor(_checker, _ngZone, _focusTrapManager, _document, _inertStrategy) {\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._focusTrapManager = _focusTrapManager;\n    this._document = _document;\n    // TODO split up the strategies into different modules, similar to DateAdapter.\n    this._inertStrategy = _inertStrategy || new EventListenerFocusTrapInertStrategy();\n  }\n  create(element, config = {\n    defer: false\n  }) {\n    let configObject;\n    if (typeof config === 'boolean') {\n      configObject = {\n        defer: config\n      };\n    } else {\n      configObject = config;\n    }\n    return new ConfigurableFocusTrap(element, this._checker, this._ngZone, this._document, this._focusTrapManager, this._inertStrategy, configObject);\n  }\n  static {\n    this.ɵfac = function ConfigurableFocusTrapFactory_Factory(t) {\n      return new (t || ConfigurableFocusTrapFactory)(i0.ɵɵinject(InteractivityChecker), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(FocusTrapManager), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(FOCUS_TRAP_INERT_STRATEGY, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ConfigurableFocusTrapFactory,\n      factory: ConfigurableFocusTrapFactory.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ConfigurableFocusTrapFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: InteractivityChecker\n  }, {\n    type: i0.NgZone\n  }, {\n    type: FocusTrapManager\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [FOCUS_TRAP_INERT_STRATEGY]\n    }]\n  }], null);\n})();\n\n/** Gets whether an event could be a faked `mousedown` event dispatched by a screen reader. */\nfunction isFakeMousedownFromScreenReader(event) {\n  // Some screen readers will dispatch a fake `mousedown` event when pressing enter or space on\n  // a clickable element. We can distinguish these events when `event.buttons` is zero, or\n  // `event.detail` is zero depending on the browser:\n  // - `event.buttons` works on Firefox, but fails on Chrome.\n  // - `detail` works on Chrome, but fails on Firefox.\n  return event.buttons === 0 || event.detail === 0;\n}\n/** Gets whether an event could be a faked `touchstart` event dispatched by a screen reader. */\nfunction isFakeTouchstartFromScreenReader(event) {\n  const touch = event.touches && event.touches[0] || event.changedTouches && event.changedTouches[0];\n  // A fake `touchstart` can be distinguished from a real one by looking at the `identifier`\n  // which is typically >= 0 on a real device versus -1 from a screen reader. Just to be safe,\n  // we can also look at `radiusX` and `radiusY`. This behavior was observed against a Windows 10\n  // device with a touch screen running NVDA v2020.4 and Firefox 85 or Chrome 88.\n  return !!touch && touch.identifier === -1 && (touch.radiusX == null || touch.radiusX === 1) && (touch.radiusY == null || touch.radiusY === 1);\n}\n\n/**\n * Injectable options for the InputModalityDetector. These are shallowly merged with the default\n * options.\n */\nconst INPUT_MODALITY_DETECTOR_OPTIONS = new InjectionToken('cdk-input-modality-detector-options');\n/**\n * Default options for the InputModalityDetector.\n *\n * Modifier keys are ignored by default (i.e. when pressed won't cause the service to detect\n * keyboard input modality) for two reasons:\n *\n * 1. Modifier keys are commonly used with mouse to perform actions such as 'right click' or 'open\n *    in new tab', and are thus less representative of actual keyboard interaction.\n * 2. VoiceOver triggers some keyboard events when linearly navigating with Control + Option (but\n *    confusingly not with Caps Lock). Thus, to have parity with other screen readers, we ignore\n *    these keys so as to not update the input modality.\n *\n * Note that we do not by default ignore the right Meta key on Safari because it has the same key\n * code as the ContextMenu key on other browsers. When we switch to using event.key, we can\n * distinguish between the two.\n */\nconst INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS = {\n  ignoreKeys: [ALT, CONTROL, MAC_META, META, SHIFT]\n};\n/**\n * The amount of time needed to pass after a touchstart event in order for a subsequent mousedown\n * event to be attributed as mouse and not touch.\n *\n * This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n * that a value of around 650ms seems appropriate.\n */\nconst TOUCH_BUFFER_MS = 650;\n/**\n * Event listener options that enable capturing and also mark the listener as passive if the browser\n * supports it.\n */\nconst modalityEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/**\n * Service that detects the user's input modality.\n *\n * This service does not update the input modality when a user navigates with a screen reader\n * (e.g. linear navigation with VoiceOver, object navigation / browse mode with NVDA, virtual PC\n * cursor mode with JAWS). This is in part due to technical limitations (i.e. keyboard events do not\n * fire as expected in these modes) but is also arguably the correct behavior. Navigating with a\n * screen reader is akin to visually scanning a page, and should not be interpreted as actual user\n * input interaction.\n *\n * When a user is not navigating but *interacting* with a screen reader, this service attempts to\n * update the input modality to keyboard, but in general this service's behavior is largely\n * undefined.\n */\nclass InputModalityDetector {\n  /** The most recently detected input modality. */\n  get mostRecentModality() {\n    return this._modality.value;\n  }\n  constructor(_platform, ngZone, document, options) {\n    this._platform = _platform;\n    /**\n     * The most recently detected input modality event target. Is null if no input modality has been\n     * detected or if the associated event target is null for some unknown reason.\n     */\n    this._mostRecentTarget = null;\n    /** The underlying BehaviorSubject that emits whenever an input modality is detected. */\n    this._modality = new BehaviorSubject(null);\n    /**\n     * The timestamp of the last touch input modality. Used to determine whether mousedown events\n     * should be attributed to mouse or touch.\n     */\n    this._lastTouchMs = 0;\n    /**\n     * Handles keydown events. Must be an arrow function in order to preserve the context when it gets\n     * bound.\n     */\n    this._onKeydown = event => {\n      // If this is one of the keys we should ignore, then ignore it and don't update the input\n      // modality to keyboard.\n      if (this._options?.ignoreKeys?.some(keyCode => keyCode === event.keyCode)) {\n        return;\n      }\n      this._modality.next('keyboard');\n      this._mostRecentTarget = _getEventTarget(event);\n    };\n    /**\n     * Handles mousedown events. Must be an arrow function in order to preserve the context when it\n     * gets bound.\n     */\n    this._onMousedown = event => {\n      // Touches trigger both touch and mouse events, so we need to distinguish between mouse events\n      // that were triggered via mouse vs touch. To do so, check if the mouse event occurs closely\n      // after the previous touch event.\n      if (Date.now() - this._lastTouchMs < TOUCH_BUFFER_MS) {\n        return;\n      }\n      // Fake mousedown events are fired by some screen readers when controls are activated by the\n      // screen reader. Attribute them to keyboard input modality.\n      this._modality.next(isFakeMousedownFromScreenReader(event) ? 'keyboard' : 'mouse');\n      this._mostRecentTarget = _getEventTarget(event);\n    };\n    /**\n     * Handles touchstart events. Must be an arrow function in order to preserve the context when it\n     * gets bound.\n     */\n    this._onTouchstart = event => {\n      // Same scenario as mentioned in _onMousedown, but on touch screen devices, fake touchstart\n      // events are fired. Again, attribute to keyboard input modality.\n      if (isFakeTouchstartFromScreenReader(event)) {\n        this._modality.next('keyboard');\n        return;\n      }\n      // Store the timestamp of this touch event, as it's used to distinguish between mouse events\n      // triggered via mouse vs touch.\n      this._lastTouchMs = Date.now();\n      this._modality.next('touch');\n      this._mostRecentTarget = _getEventTarget(event);\n    };\n    this._options = {\n      ...INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,\n      ...options\n    };\n    // Skip the first emission as it's null.\n    this.modalityDetected = this._modality.pipe(skip(1));\n    this.modalityChanged = this.modalityDetected.pipe(distinctUntilChanged());\n    // If we're not in a browser, this service should do nothing, as there's no relevant input\n    // modality to detect.\n    if (_platform.isBrowser) {\n      ngZone.runOutsideAngular(() => {\n        document.addEventListener('keydown', this._onKeydown, modalityEventListenerOptions);\n        document.addEventListener('mousedown', this._onMousedown, modalityEventListenerOptions);\n        document.addEventListener('touchstart', this._onTouchstart, modalityEventListenerOptions);\n      });\n    }\n  }\n  ngOnDestroy() {\n    this._modality.complete();\n    if (this._platform.isBrowser) {\n      document.removeEventListener('keydown', this._onKeydown, modalityEventListenerOptions);\n      document.removeEventListener('mousedown', this._onMousedown, modalityEventListenerOptions);\n      document.removeEventListener('touchstart', this._onTouchstart, modalityEventListenerOptions);\n    }\n  }\n  static {\n    this.ɵfac = function InputModalityDetector_Factory(t) {\n      return new (t || InputModalityDetector)(i0.ɵɵinject(i1.Platform), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(INPUT_MODALITY_DETECTOR_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: InputModalityDetector,\n      factory: InputModalityDetector.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputModalityDetector, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.Platform\n  }, {\n    type: i0.NgZone\n  }, {\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [INPUT_MODALITY_DETECTOR_OPTIONS]\n    }]\n  }], null);\n})();\nconst LIVE_ANNOUNCER_ELEMENT_TOKEN = new InjectionToken('liveAnnouncerElement', {\n  providedIn: 'root',\n  factory: LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY\n});\n/** @docs-private */\nfunction LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY() {\n  return null;\n}\n/** Injection token that can be used to configure the default options for the LiveAnnouncer. */\nconst LIVE_ANNOUNCER_DEFAULT_OPTIONS = new InjectionToken('LIVE_ANNOUNCER_DEFAULT_OPTIONS');\nlet uniqueIds = 0;\nclass LiveAnnouncer {\n  constructor(elementToken, _ngZone, _document, _defaultOptions) {\n    this._ngZone = _ngZone;\n    this._defaultOptions = _defaultOptions;\n    // We inject the live element and document as `any` because the constructor signature cannot\n    // reference browser globals (HTMLElement, Document) on non-browser environments, since having\n    // a class decorator causes TypeScript to preserve the constructor signature types.\n    this._document = _document;\n    this._liveElement = elementToken || this._createLiveElement();\n  }\n  announce(message, ...args) {\n    const defaultOptions = this._defaultOptions;\n    let politeness;\n    let duration;\n    if (args.length === 1 && typeof args[0] === 'number') {\n      duration = args[0];\n    } else {\n      [politeness, duration] = args;\n    }\n    this.clear();\n    clearTimeout(this._previousTimeout);\n    if (!politeness) {\n      politeness = defaultOptions && defaultOptions.politeness ? defaultOptions.politeness : 'polite';\n    }\n    if (duration == null && defaultOptions) {\n      duration = defaultOptions.duration;\n    }\n    // TODO: ensure changing the politeness works on all environments we support.\n    this._liveElement.setAttribute('aria-live', politeness);\n    if (this._liveElement.id) {\n      this._exposeAnnouncerToModals(this._liveElement.id);\n    }\n    // This 100ms timeout is necessary for some browser + screen-reader combinations:\n    // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n    // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n    //   second time without clearing and then using a non-zero delay.\n    // (using JAWS 17 at time of this writing).\n    return this._ngZone.runOutsideAngular(() => {\n      if (!this._currentPromise) {\n        this._currentPromise = new Promise(resolve => this._currentResolve = resolve);\n      }\n      clearTimeout(this._previousTimeout);\n      this._previousTimeout = setTimeout(() => {\n        this._liveElement.textContent = message;\n        if (typeof duration === 'number') {\n          this._previousTimeout = setTimeout(() => this.clear(), duration);\n        }\n        // For some reason in tests this can be undefined\n        // Probably related to ZoneJS and every other thing that patches browser APIs in tests\n        this._currentResolve?.();\n        this._currentPromise = this._currentResolve = undefined;\n      }, 100);\n      return this._currentPromise;\n    });\n  }\n  /**\n   * Clears the current text from the announcer element. Can be used to prevent\n   * screen readers from reading the text out again while the user is going\n   * through the page landmarks.\n   */\n  clear() {\n    if (this._liveElement) {\n      this._liveElement.textContent = '';\n    }\n  }\n  ngOnDestroy() {\n    clearTimeout(this._previousTimeout);\n    this._liveElement?.remove();\n    this._liveElement = null;\n    this._currentResolve?.();\n    this._currentPromise = this._currentResolve = undefined;\n  }\n  _createLiveElement() {\n    const elementClass = 'cdk-live-announcer-element';\n    const previousElements = this._document.getElementsByClassName(elementClass);\n    const liveEl = this._document.createElement('div');\n    // Remove any old containers. This can happen when coming in from a server-side-rendered page.\n    for (let i = 0; i < previousElements.length; i++) {\n      previousElements[i].remove();\n    }\n    liveEl.classList.add(elementClass);\n    liveEl.classList.add('cdk-visually-hidden');\n    liveEl.setAttribute('aria-atomic', 'true');\n    liveEl.setAttribute('aria-live', 'polite');\n    liveEl.id = `cdk-live-announcer-${uniqueIds++}`;\n    this._document.body.appendChild(liveEl);\n    return liveEl;\n  }\n  /**\n   * Some browsers won't expose the accessibility node of the live announcer element if there is an\n   * `aria-modal` and the live announcer is outside of it. This method works around the issue by\n   * pointing the `aria-owns` of all modals to the live announcer element.\n   */\n  _exposeAnnouncerToModals(id) {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n    // the `SnakBarContainer` and other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    for (let i = 0; i < modals.length; i++) {\n      const modal = modals[i];\n      const ariaOwns = modal.getAttribute('aria-owns');\n      if (!ariaOwns) {\n        modal.setAttribute('aria-owns', id);\n      } else if (ariaOwns.indexOf(id) === -1) {\n        modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n      }\n    }\n  }\n  static {\n    this.ɵfac = function LiveAnnouncer_Factory(t) {\n      return new (t || LiveAnnouncer)(i0.ɵɵinject(LIVE_ANNOUNCER_ELEMENT_TOKEN, 8), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(LIVE_ANNOUNCER_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: LiveAnnouncer,\n      factory: LiveAnnouncer.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LiveAnnouncer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [LIVE_ANNOUNCER_ELEMENT_TOKEN]\n    }]\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [LIVE_ANNOUNCER_DEFAULT_OPTIONS]\n    }]\n  }], null);\n})();\n/**\n * A directive that works similarly to aria-live, but uses the LiveAnnouncer to ensure compatibility\n * with a wider range of browsers and screen readers.\n */\nclass CdkAriaLive {\n  /** The aria-live politeness level to use when announcing messages. */\n  get politeness() {\n    return this._politeness;\n  }\n  set politeness(value) {\n    this._politeness = value === 'off' || value === 'assertive' ? value : 'polite';\n    if (this._politeness === 'off') {\n      if (this._subscription) {\n        this._subscription.unsubscribe();\n        this._subscription = null;\n      }\n    } else if (!this._subscription) {\n      this._subscription = this._ngZone.runOutsideAngular(() => {\n        return this._contentObserver.observe(this._elementRef).subscribe(() => {\n          // Note that we use textContent here, rather than innerText, in order to avoid a reflow.\n          const elementText = this._elementRef.nativeElement.textContent;\n          // The `MutationObserver` fires also for attribute\n          // changes which we don't want to announce.\n          if (elementText !== this._previousAnnouncedText) {\n            this._liveAnnouncer.announce(elementText, this._politeness, this.duration);\n            this._previousAnnouncedText = elementText;\n          }\n        });\n      });\n    }\n  }\n  constructor(_elementRef, _liveAnnouncer, _contentObserver, _ngZone) {\n    this._elementRef = _elementRef;\n    this._liveAnnouncer = _liveAnnouncer;\n    this._contentObserver = _contentObserver;\n    this._ngZone = _ngZone;\n    this._politeness = 'polite';\n  }\n  ngOnDestroy() {\n    if (this._subscription) {\n      this._subscription.unsubscribe();\n    }\n  }\n  static {\n    this.ɵfac = function CdkAriaLive_Factory(t) {\n      return new (t || CdkAriaLive)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(LiveAnnouncer), i0.ɵɵdirectiveInject(i1$1.ContentObserver), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkAriaLive,\n      selectors: [[\"\", \"cdkAriaLive\", \"\"]],\n      inputs: {\n        politeness: [i0.ɵɵInputFlags.None, \"cdkAriaLive\", \"politeness\"],\n        duration: [i0.ɵɵInputFlags.None, \"cdkAriaLiveDuration\", \"duration\"]\n      },\n      exportAs: [\"cdkAriaLive\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAriaLive, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkAriaLive]',\n      exportAs: 'cdkAriaLive',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: LiveAnnouncer\n  }, {\n    type: i1$1.ContentObserver\n  }, {\n    type: i0.NgZone\n  }], {\n    politeness: [{\n      type: Input,\n      args: ['cdkAriaLive']\n    }],\n    duration: [{\n      type: Input,\n      args: ['cdkAriaLiveDuration']\n    }]\n  });\n})();\n\n/** Detection mode used for attributing the origin of a focus event. */\nvar FocusMonitorDetectionMode;\n(function (FocusMonitorDetectionMode) {\n  /**\n   * Any mousedown, keydown, or touchstart event that happened in the previous\n   * tick or the current tick will be used to assign a focus event's origin (to\n   * either mouse, keyboard, or touch). This is the default option.\n   */\n  FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"IMMEDIATE\"] = 0] = \"IMMEDIATE\";\n  /**\n   * A focus event's origin is always attributed to the last corresponding\n   * mousedown, keydown, or touchstart event, no matter how long ago it occurred.\n   */\n  FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"EVENTUAL\"] = 1] = \"EVENTUAL\";\n})(FocusMonitorDetectionMode || (FocusMonitorDetectionMode = {}));\n/** InjectionToken for FocusMonitorOptions. */\nconst FOCUS_MONITOR_DEFAULT_OPTIONS = new InjectionToken('cdk-focus-monitor-default-options');\n/**\n * Event listener options that enable capturing and also\n * mark the listener as passive if the browser supports it.\n */\nconst captureEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\nclass FocusMonitor {\n  constructor(_ngZone, _platform, _inputModalityDetector, /** @breaking-change 11.0.0 make document required */\n  document, options) {\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    this._inputModalityDetector = _inputModalityDetector;\n    /** The focus origin that the next focus event is a result of. */\n    this._origin = null;\n    /** Whether the window has just been focused. */\n    this._windowFocused = false;\n    /**\n     * Whether the origin was determined via a touch interaction. Necessary as properly attributing\n     * focus events to touch interactions requires special logic.\n     */\n    this._originFromTouchInteraction = false;\n    /** Map of elements being monitored to their info. */\n    this._elementInfo = new Map();\n    /** The number of elements currently being monitored. */\n    this._monitoredElementCount = 0;\n    /**\n     * Keeps track of the root nodes to which we've currently bound a focus/blur handler,\n     * as well as the number of monitored elements that they contain. We have to treat focus/blur\n     * handlers differently from the rest of the events, because the browser won't emit events\n     * to the document when focus moves inside of a shadow root.\n     */\n    this._rootNodeFocusListenerCount = new Map();\n    /**\n     * Event listener for `focus` events on the window.\n     * Needs to be an arrow function in order to preserve the context when it gets bound.\n     */\n    this._windowFocusListener = () => {\n      // Make a note of when the window regains focus, so we can\n      // restore the origin info for the focused element.\n      this._windowFocused = true;\n      this._windowFocusTimeoutId = window.setTimeout(() => this._windowFocused = false);\n    };\n    /** Subject for stopping our InputModalityDetector subscription. */\n    this._stopInputModalityDetector = new Subject();\n    /**\n     * Event listener for `focus` and 'blur' events on the document.\n     * Needs to be an arrow function in order to preserve the context when it gets bound.\n     */\n    this._rootNodeFocusAndBlurListener = event => {\n      const target = _getEventTarget(event);\n      // We need to walk up the ancestor chain in order to support `checkChildren`.\n      for (let element = target; element; element = element.parentElement) {\n        if (event.type === 'focus') {\n          this._onFocus(event, element);\n        } else {\n          this._onBlur(event, element);\n        }\n      }\n    };\n    this._document = document;\n    this._detectionMode = options?.detectionMode || FocusMonitorDetectionMode.IMMEDIATE;\n  }\n  monitor(element, checkChildren = false) {\n    const nativeElement = coerceElement(element);\n    // Do nothing if we're not on the browser platform or the passed in node isn't an element.\n    if (!this._platform.isBrowser || nativeElement.nodeType !== 1) {\n      // Note: we don't want the observable to emit at all so we don't pass any parameters.\n      return of();\n    }\n    // If the element is inside the shadow DOM, we need to bind our focus/blur listeners to\n    // the shadow root, rather than the `document`, because the browser won't emit focus events\n    // to the `document`, if focus is moving within the same shadow root.\n    const rootNode = _getShadowRoot(nativeElement) || this._getDocument();\n    const cachedInfo = this._elementInfo.get(nativeElement);\n    // Check if we're already monitoring this element.\n    if (cachedInfo) {\n      if (checkChildren) {\n        // TODO(COMP-318): this can be problematic, because it'll turn all non-checkChildren\n        // observers into ones that behave as if `checkChildren` was turned on. We need a more\n        // robust solution.\n        cachedInfo.checkChildren = true;\n      }\n      return cachedInfo.subject;\n    }\n    // Create monitored element info.\n    const info = {\n      checkChildren: checkChildren,\n      subject: new Subject(),\n      rootNode\n    };\n    this._elementInfo.set(nativeElement, info);\n    this._registerGlobalListeners(info);\n    return info.subject;\n  }\n  stopMonitoring(element) {\n    const nativeElement = coerceElement(element);\n    const elementInfo = this._elementInfo.get(nativeElement);\n    if (elementInfo) {\n      elementInfo.subject.complete();\n      this._setClasses(nativeElement);\n      this._elementInfo.delete(nativeElement);\n      this._removeGlobalListeners(elementInfo);\n    }\n  }\n  focusVia(element, origin, options) {\n    const nativeElement = coerceElement(element);\n    const focusedElement = this._getDocument().activeElement;\n    // If the element is focused already, calling `focus` again won't trigger the event listener\n    // which means that the focus classes won't be updated. If that's the case, update the classes\n    // directly without waiting for an event.\n    if (nativeElement === focusedElement) {\n      this._getClosestElementsInfo(nativeElement).forEach(([currentElement, info]) => this._originChanged(currentElement, origin, info));\n    } else {\n      this._setOrigin(origin);\n      // `focus` isn't available on the server\n      if (typeof nativeElement.focus === 'function') {\n        nativeElement.focus(options);\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n  }\n  /** Access injected document if available or fallback to global document reference */\n  _getDocument() {\n    return this._document || document;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  _getWindow() {\n    const doc = this._getDocument();\n    return doc.defaultView || window;\n  }\n  _getFocusOrigin(focusEventTarget) {\n    if (this._origin) {\n      // If the origin was realized via a touch interaction, we need to perform additional checks\n      // to determine whether the focus origin should be attributed to touch or program.\n      if (this._originFromTouchInteraction) {\n        return this._shouldBeAttributedToTouch(focusEventTarget) ? 'touch' : 'program';\n      } else {\n        return this._origin;\n      }\n    }\n    // If the window has just regained focus, we can restore the most recent origin from before the\n    // window blurred. Otherwise, we've reached the point where we can't identify the source of the\n    // focus. This typically means one of two things happened:\n    //\n    // 1) The element was programmatically focused, or\n    // 2) The element was focused via screen reader navigation (which generally doesn't fire\n    //    events).\n    //\n    // Because we can't distinguish between these two cases, we default to setting `program`.\n    if (this._windowFocused && this._lastFocusOrigin) {\n      return this._lastFocusOrigin;\n    }\n    // If the interaction is coming from an input label, we consider it a mouse interactions.\n    // This is a special case where focus moves on `click`, rather than `mousedown` which breaks\n    // our detection, because all our assumptions are for `mousedown`. We need to handle this\n    // special case, because it's very common for checkboxes and radio buttons.\n    if (focusEventTarget && this._isLastInteractionFromInputLabel(focusEventTarget)) {\n      return 'mouse';\n    }\n    return 'program';\n  }\n  /**\n   * Returns whether the focus event should be attributed to touch. Recall that in IMMEDIATE mode, a\n   * touch origin isn't immediately reset at the next tick (see _setOrigin). This means that when we\n   * handle a focus event following a touch interaction, we need to determine whether (1) the focus\n   * event was directly caused by the touch interaction or (2) the focus event was caused by a\n   * subsequent programmatic focus call triggered by the touch interaction.\n   * @param focusEventTarget The target of the focus event under examination.\n   */\n  _shouldBeAttributedToTouch(focusEventTarget) {\n    // Please note that this check is not perfect. Consider the following edge case:\n    //\n    // <div #parent tabindex=\"0\">\n    //   <div #child tabindex=\"0\" (click)=\"#parent.focus()\"></div>\n    // </div>\n    //\n    // Suppose there is a FocusMonitor in IMMEDIATE mode attached to #parent. When the user touches\n    // #child, #parent is programmatically focused. This code will attribute the focus to touch\n    // instead of program. This is a relatively minor edge-case that can be worked around by using\n    // focusVia(parent, 'program') to focus #parent.\n    return this._detectionMode === FocusMonitorDetectionMode.EVENTUAL || !!focusEventTarget?.contains(this._inputModalityDetector._mostRecentTarget);\n  }\n  /**\n   * Sets the focus classes on the element based on the given focus origin.\n   * @param element The element to update the classes on.\n   * @param origin The focus origin.\n   */\n  _setClasses(element, origin) {\n    element.classList.toggle('cdk-focused', !!origin);\n    element.classList.toggle('cdk-touch-focused', origin === 'touch');\n    element.classList.toggle('cdk-keyboard-focused', origin === 'keyboard');\n    element.classList.toggle('cdk-mouse-focused', origin === 'mouse');\n    element.classList.toggle('cdk-program-focused', origin === 'program');\n  }\n  /**\n   * Updates the focus origin. If we're using immediate detection mode, we schedule an async\n   * function to clear the origin at the end of a timeout. The duration of the timeout depends on\n   * the origin being set.\n   * @param origin The origin to set.\n   * @param isFromInteraction Whether we are setting the origin from an interaction event.\n   */\n  _setOrigin(origin, isFromInteraction = false) {\n    this._ngZone.runOutsideAngular(() => {\n      this._origin = origin;\n      this._originFromTouchInteraction = origin === 'touch' && isFromInteraction;\n      // If we're in IMMEDIATE mode, reset the origin at the next tick (or in `TOUCH_BUFFER_MS` ms\n      // for a touch event). We reset the origin at the next tick because Firefox focuses one tick\n      // after the interaction event. We wait `TOUCH_BUFFER_MS` ms before resetting the origin for\n      // a touch event because when a touch event is fired, the associated focus event isn't yet in\n      // the event queue. Before doing so, clear any pending timeouts.\n      if (this._detectionMode === FocusMonitorDetectionMode.IMMEDIATE) {\n        clearTimeout(this._originTimeoutId);\n        const ms = this._originFromTouchInteraction ? TOUCH_BUFFER_MS : 1;\n        this._originTimeoutId = setTimeout(() => this._origin = null, ms);\n      }\n    });\n  }\n  /**\n   * Handles focus events on a registered element.\n   * @param event The focus event.\n   * @param element The monitored element.\n   */\n  _onFocus(event, element) {\n    // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n    // focus event affecting the monitored element. If we want to use the origin of the first event\n    // instead we should check for the cdk-focused class here and return if the element already has\n    // it. (This only matters for elements that have includesChildren = true).\n    // If we are not counting child-element-focus as focused, make sure that the event target is the\n    // monitored element itself.\n    const elementInfo = this._elementInfo.get(element);\n    const focusEventTarget = _getEventTarget(event);\n    if (!elementInfo || !elementInfo.checkChildren && element !== focusEventTarget) {\n      return;\n    }\n    this._originChanged(element, this._getFocusOrigin(focusEventTarget), elementInfo);\n  }\n  /**\n   * Handles blur events on a registered element.\n   * @param event The blur event.\n   * @param element The monitored element.\n   */\n  _onBlur(event, element) {\n    // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n    // order to focus another child of the monitored element.\n    const elementInfo = this._elementInfo.get(element);\n    if (!elementInfo || elementInfo.checkChildren && event.relatedTarget instanceof Node && element.contains(event.relatedTarget)) {\n      return;\n    }\n    this._setClasses(element);\n    this._emitOrigin(elementInfo, null);\n  }\n  _emitOrigin(info, origin) {\n    if (info.subject.observers.length) {\n      this._ngZone.run(() => info.subject.next(origin));\n    }\n  }\n  _registerGlobalListeners(elementInfo) {\n    if (!this._platform.isBrowser) {\n      return;\n    }\n    const rootNode = elementInfo.rootNode;\n    const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode) || 0;\n    if (!rootNodeFocusListeners) {\n      this._ngZone.runOutsideAngular(() => {\n        rootNode.addEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        rootNode.addEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n      });\n    }\n    this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners + 1);\n    // Register global listeners when first element is monitored.\n    if (++this._monitoredElementCount === 1) {\n      // Note: we listen to events in the capture phase so we\n      // can detect them even if the user stops propagation.\n      this._ngZone.runOutsideAngular(() => {\n        const window = this._getWindow();\n        window.addEventListener('focus', this._windowFocusListener);\n      });\n      // The InputModalityDetector is also just a collection of global listeners.\n      this._inputModalityDetector.modalityDetected.pipe(takeUntil(this._stopInputModalityDetector)).subscribe(modality => {\n        this._setOrigin(modality, true /* isFromInteraction */);\n      });\n    }\n  }\n  _removeGlobalListeners(elementInfo) {\n    const rootNode = elementInfo.rootNode;\n    if (this._rootNodeFocusListenerCount.has(rootNode)) {\n      const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode);\n      if (rootNodeFocusListeners > 1) {\n        this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners - 1);\n      } else {\n        rootNode.removeEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        rootNode.removeEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        this._rootNodeFocusListenerCount.delete(rootNode);\n      }\n    }\n    // Unregister global listeners when last element is unmonitored.\n    if (! --this._monitoredElementCount) {\n      const window = this._getWindow();\n      window.removeEventListener('focus', this._windowFocusListener);\n      // Equivalently, stop our InputModalityDetector subscription.\n      this._stopInputModalityDetector.next();\n      // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n      clearTimeout(this._windowFocusTimeoutId);\n      clearTimeout(this._originTimeoutId);\n    }\n  }\n  /** Updates all the state on an element once its focus origin has changed. */\n  _originChanged(element, origin, elementInfo) {\n    this._setClasses(element, origin);\n    this._emitOrigin(elementInfo, origin);\n    this._lastFocusOrigin = origin;\n  }\n  /**\n   * Collects the `MonitoredElementInfo` of a particular element and\n   * all of its ancestors that have enabled `checkChildren`.\n   * @param element Element from which to start the search.\n   */\n  _getClosestElementsInfo(element) {\n    const results = [];\n    this._elementInfo.forEach((info, currentElement) => {\n      if (currentElement === element || info.checkChildren && currentElement.contains(element)) {\n        results.push([currentElement, info]);\n      }\n    });\n    return results;\n  }\n  /**\n   * Returns whether an interaction is likely to have come from the user clicking the `label` of\n   * an `input` or `textarea` in order to focus it.\n   * @param focusEventTarget Target currently receiving focus.\n   */\n  _isLastInteractionFromInputLabel(focusEventTarget) {\n    const {\n      _mostRecentTarget: mostRecentTarget,\n      mostRecentModality\n    } = this._inputModalityDetector;\n    // If the last interaction used the mouse on an element contained by one of the labels\n    // of an `input`/`textarea` that is currently focused, it is very likely that the\n    // user redirected focus using the label.\n    if (mostRecentModality !== 'mouse' || !mostRecentTarget || mostRecentTarget === focusEventTarget || focusEventTarget.nodeName !== 'INPUT' && focusEventTarget.nodeName !== 'TEXTAREA' || focusEventTarget.disabled) {\n      return false;\n    }\n    const labels = focusEventTarget.labels;\n    if (labels) {\n      for (let i = 0; i < labels.length; i++) {\n        if (labels[i].contains(mostRecentTarget)) {\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n  static {\n    this.ɵfac = function FocusMonitor_Factory(t) {\n      return new (t || FocusMonitor)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.Platform), i0.ɵɵinject(InputModalityDetector), i0.ɵɵinject(DOCUMENT, 8), i0.ɵɵinject(FOCUS_MONITOR_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FocusMonitor,\n      factory: FocusMonitor.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusMonitor, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i1.Platform\n  }, {\n    type: InputModalityDetector\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [FOCUS_MONITOR_DEFAULT_OPTIONS]\n    }]\n  }], null);\n})();\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\nclass CdkMonitorFocus {\n  constructor(_elementRef, _focusMonitor) {\n    this._elementRef = _elementRef;\n    this._focusMonitor = _focusMonitor;\n    this._focusOrigin = null;\n    this.cdkFocusChange = new EventEmitter();\n  }\n  get focusOrigin() {\n    return this._focusOrigin;\n  }\n  ngAfterViewInit() {\n    const element = this._elementRef.nativeElement;\n    this._monitorSubscription = this._focusMonitor.monitor(element, element.nodeType === 1 && element.hasAttribute('cdkMonitorSubtreeFocus')).subscribe(origin => {\n      this._focusOrigin = origin;\n      this.cdkFocusChange.emit(origin);\n    });\n  }\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    if (this._monitorSubscription) {\n      this._monitorSubscription.unsubscribe();\n    }\n  }\n  static {\n    this.ɵfac = function CdkMonitorFocus_Factory(t) {\n      return new (t || CdkMonitorFocus)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(FocusMonitor));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkMonitorFocus,\n      selectors: [[\"\", \"cdkMonitorElementFocus\", \"\"], [\"\", \"cdkMonitorSubtreeFocus\", \"\"]],\n      outputs: {\n        cdkFocusChange: \"cdkFocusChange\"\n      },\n      exportAs: [\"cdkMonitorFocus\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkMonitorFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]',\n      exportAs: 'cdkMonitorFocus',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: FocusMonitor\n  }], {\n    cdkFocusChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/** Set of possible high-contrast mode backgrounds. */\nvar HighContrastMode;\n(function (HighContrastMode) {\n  HighContrastMode[HighContrastMode[\"NONE\"] = 0] = \"NONE\";\n  HighContrastMode[HighContrastMode[\"BLACK_ON_WHITE\"] = 1] = \"BLACK_ON_WHITE\";\n  HighContrastMode[HighContrastMode[\"WHITE_ON_BLACK\"] = 2] = \"WHITE_ON_BLACK\";\n})(HighContrastMode || (HighContrastMode = {}));\n/** CSS class applied to the document body when in black-on-white high-contrast mode. */\nconst BLACK_ON_WHITE_CSS_CLASS = 'cdk-high-contrast-black-on-white';\n/** CSS class applied to the document body when in white-on-black high-contrast mode. */\nconst WHITE_ON_BLACK_CSS_CLASS = 'cdk-high-contrast-white-on-black';\n/** CSS class applied to the document body when in high-contrast mode. */\nconst HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS = 'cdk-high-contrast-active';\n/**\n * Service to determine whether the browser is currently in a high-contrast-mode environment.\n *\n * Microsoft Windows supports an accessibility feature called \"High Contrast Mode\". This mode\n * changes the appearance of all applications, including web applications, to dramatically increase\n * contrast.\n *\n * IE, Edge, and Firefox currently support this mode. Chrome does not support Windows High Contrast\n * Mode. This service does not detect high-contrast mode as added by the Chrome \"High Contrast\"\n * browser extension.\n */\nclass HighContrastModeDetector {\n  constructor(_platform, document) {\n    this._platform = _platform;\n    this._document = document;\n    this._breakpointSubscription = inject(BreakpointObserver).observe('(forced-colors: active)').subscribe(() => {\n      if (this._hasCheckedHighContrastMode) {\n        this._hasCheckedHighContrastMode = false;\n        this._applyBodyHighContrastModeCssClasses();\n      }\n    });\n  }\n  /** Gets the current high-contrast-mode for the page. */\n  getHighContrastMode() {\n    if (!this._platform.isBrowser) {\n      return HighContrastMode.NONE;\n    }\n    // Create a test element with an arbitrary background-color that is neither black nor\n    // white; high-contrast mode will coerce the color to either black or white. Also ensure that\n    // appending the test element to the DOM does not affect layout by absolutely positioning it\n    const testElement = this._document.createElement('div');\n    testElement.style.backgroundColor = 'rgb(1,2,3)';\n    testElement.style.position = 'absolute';\n    this._document.body.appendChild(testElement);\n    // Get the computed style for the background color, collapsing spaces to normalize between\n    // browsers. Once we get this color, we no longer need the test element. Access the `window`\n    // via the document so we can fake it in tests. Note that we have extra null checks, because\n    // this logic will likely run during app bootstrap and throwing can break the entire app.\n    const documentWindow = this._document.defaultView || window;\n    const computedStyle = documentWindow && documentWindow.getComputedStyle ? documentWindow.getComputedStyle(testElement) : null;\n    const computedColor = (computedStyle && computedStyle.backgroundColor || '').replace(/ /g, '');\n    testElement.remove();\n    switch (computedColor) {\n      // Pre Windows 11 dark theme.\n      case 'rgb(0,0,0)':\n      // Windows 11 dark themes.\n      case 'rgb(45,50,54)':\n      case 'rgb(32,32,32)':\n        return HighContrastMode.WHITE_ON_BLACK;\n      // Pre Windows 11 light theme.\n      case 'rgb(255,255,255)':\n      // Windows 11 light theme.\n      case 'rgb(255,250,239)':\n        return HighContrastMode.BLACK_ON_WHITE;\n    }\n    return HighContrastMode.NONE;\n  }\n  ngOnDestroy() {\n    this._breakpointSubscription.unsubscribe();\n  }\n  /** Applies CSS classes indicating high-contrast mode to document body (browser-only). */\n  _applyBodyHighContrastModeCssClasses() {\n    if (!this._hasCheckedHighContrastMode && this._platform.isBrowser && this._document.body) {\n      const bodyClasses = this._document.body.classList;\n      bodyClasses.remove(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n      this._hasCheckedHighContrastMode = true;\n      const mode = this.getHighContrastMode();\n      if (mode === HighContrastMode.BLACK_ON_WHITE) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS);\n      } else if (mode === HighContrastMode.WHITE_ON_BLACK) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n      }\n    }\n  }\n  static {\n    this.ɵfac = function HighContrastModeDetector_Factory(t) {\n      return new (t || HighContrastModeDetector)(i0.ɵɵinject(i1.Platform), i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: HighContrastModeDetector,\n      factory: HighContrastModeDetector.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HighContrastModeDetector, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.Platform\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\nclass A11yModule {\n  constructor(highContrastModeDetector) {\n    highContrastModeDetector._applyBodyHighContrastModeCssClasses();\n  }\n  static {\n    this.ɵfac = function A11yModule_Factory(t) {\n      return new (t || A11yModule)(i0.ɵɵinject(HighContrastModeDetector));\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: A11yModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [ObserversModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(A11yModule, [{\n    type: NgModule,\n    args: [{\n      imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n      exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus]\n    }]\n  }], () => [{\n    type: HighContrastModeDetector\n  }], null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { A11yModule, ActiveDescendantKeyManager, AriaDescriber, CDK_DESCRIBEDBY_HOST_ATTRIBUTE, CDK_DESCRIBEDBY_ID_PREFIX, CdkAriaLive, CdkMonitorFocus, CdkTrapFocus, ConfigurableFocusTrap, ConfigurableFocusTrapFactory, EventListenerFocusTrapInertStrategy, FOCUS_MONITOR_DEFAULT_OPTIONS, FOCUS_TRAP_INERT_STRATEGY, FocusKeyManager, FocusMonitor, FocusMonitorDetectionMode, FocusTrap, FocusTrapFactory, HighContrastMode, HighContrastModeDetector, INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS, INPUT_MODALITY_DETECTOR_OPTIONS, InputModalityDetector, InteractivityChecker, IsFocusableConfig, LIVE_ANNOUNCER_DEFAULT_OPTIONS, LIVE_ANNOUNCER_ELEMENT_TOKEN, LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY, ListKeyManager, LiveAnnouncer, MESSAGES_CONTAINER_ID, addAriaReferencedId, getAriaReferenceIds, isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader, removeAriaReferencedId };", "map": {"version": 3, "names": ["DOCUMENT", "i0", "inject", "APP_ID", "Injectable", "Inject", "QueryList", "isSignal", "effect", "booleanAttribute", "Directive", "Input", "InjectionToken", "Optional", "EventEmitter", "Output", "NgModule", "i1", "Platform", "_getFocusedElementPierceShadowDom", "normalizePassiveListenerOptions", "_getEventTarget", "_getShadowRoot", "Subject", "Subscription", "BehaviorSubject", "of", "hasModifierKey", "A", "Z", "ZERO", "NINE", "PAGE_DOWN", "PAGE_UP", "END", "HOME", "LEFT_ARROW", "RIGHT_ARROW", "UP_ARROW", "DOWN_ARROW", "TAB", "ALT", "CONTROL", "MAC_META", "META", "SHIFT", "tap", "debounceTime", "filter", "map", "take", "skip", "distinctUntilChanged", "takeUntil", "i1$1", "ObserversModule", "coerceElement", "BreakpointObserver", "ID_DELIMITER", "addAriaReferencedId", "el", "attr", "id", "ids", "getAriaReferenceIds", "trim", "some", "existingId", "push", "setAttribute", "join", "removeAriaReferencedId", "filteredIds", "val", "length", "removeAttribute", "attrValue", "getAttribute", "match", "MESSAGES_CONTAINER_ID", "CDK_DESCRIBEDBY_ID_PREFIX", "CDK_DESCRIBEDBY_HOST_ATTRIBUTE", "nextId", "AriaDescriber", "constructor", "_document", "_platform", "_messageRegistry", "Map", "_messagesContainer", "_id", "describe", "hostElement", "message", "role", "_canBeDescribed", "key", "<PERSON><PERSON><PERSON>", "setMessageId", "set", "messageElement", "referenceCount", "has", "_createMessageElement", "_isElementDescribedByMessage", "_addMessageReference", "removeDescription", "_isElementNode", "_removeMessageReference", "registeredMessage", "get", "_deleteMessageElement", "childNodes", "remove", "ngOnDestroy", "describedE<PERSON>s", "querySelectorAll", "i", "_removeCdkDescribedByReferenceIds", "clear", "createElement", "textContent", "_createMessagesContainer", "append<PERSON><PERSON><PERSON>", "delete", "containerClassName", "serverContainers", "messagesContainer", "style", "visibility", "classList", "add", "<PERSON><PERSON><PERSON><PERSON>", "body", "element", "originalReferenceIds", "indexOf", "referenceIds", "messageId", "trimmedMessage", "aria<PERSON><PERSON><PERSON>", "nodeType", "ELEMENT_NODE", "ɵfac", "AriaDescriber_Factory", "t", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "undefined", "decorators", "serviceId", "ListKeyManager", "_items", "injector", "_activeItemIndex", "_activeItem", "_wrap", "_letterKeyStream", "_typeaheadSubscription", "EMPTY", "_vertical", "_allowedModifierKeys", "_homeAndEnd", "_pageUpAndDown", "enabled", "delta", "_skipPredicateFn", "item", "disabled", "_pressedLetters", "tabOut", "change", "_itemChangesSubscription", "changes", "subscribe", "newItems", "_itemsChanged", "toArray", "Error", "_effectRef", "skipPredicate", "predicate", "withWrap", "shouldWrap", "withVerticalOrientation", "withHorizontalOrientation", "direction", "_horizontal", "withAllowedModifierKeys", "keys", "withTypeAhead", "debounceInterval", "items", "_getItemsArray", "get<PERSON><PERSON><PERSON>", "unsubscribe", "pipe", "letter", "inputString", "index", "toUpperCase", "setActiveItem", "cancelTypeahead", "withHomeAndEnd", "withPageUpDown", "previousActiveItem", "updateActiveItem", "next", "onKeydown", "event", "keyCode", "modifiers", "isModifierAllowed", "every", "modifier", "setNextItemActive", "setPreviousItemActive", "setFirstItemActive", "setLastItemActive", "targetIndex", "_setActiveItemByIndex", "itemsLength", "toLocaleUpperCase", "String", "fromCharCode", "preventDefault", "activeItemIndex", "activeItem", "isTyping", "_setActiveItemByDelta", "itemArray", "destroy", "complete", "_setActiveInWrapMode", "_setActiveInDefaultMode", "fallback<PERSON><PERSON><PERSON>", "newIndex", "ActiveDescendantKeyManager", "setInactiveStyles", "setActiveStyles", "FocusKeyManager", "arguments", "_origin", "setFocusOrigin", "origin", "focus", "IsFocusableConfig", "ignoreVisibility", "InteractivityChecker", "isDisabled", "hasAttribute", "isVisible", "hasGeometry", "getComputedStyle", "isTabbable", "frameElement", "getFrameElement", "getWindow", "getTabIndexValue", "nodeName", "toLowerCase", "tabIndexValue", "WEBKIT", "IOS", "isPotentiallyTabbableIOS", "FIREFOX", "tabIndex", "isFocusable", "config", "isPotentiallyFocusable", "InteractivityChecker_Factory", "window", "offsetWidth", "offsetHeight", "getClientRects", "isNativeFormElement", "isHiddenInput", "isInputElement", "isAnchorWithHref", "isAnchorElement", "hasValidTabIndex", "isNaN", "parseInt", "inputType", "node", "ownerDocument", "defaultView", "FocusTrap", "_enabled", "value", "_startAnchor", "_endAnchor", "_toggleAnchorTabIndex", "_element", "_checker", "_ngZone", "deferAnchors", "_hasAttached", "startAnchorListener", "focusLastTabbableElement", "endAnchorListener", "focusFirstTabbableElement", "attachAnchors", "startAnchor", "endAnchor", "removeEventListener", "runOutsideAngular", "_createAnchor", "addEventListener", "parentNode", "insertBefore", "nextS<PERSON>ling", "focusInitialElementWhenReady", "options", "Promise", "resolve", "_executeOnStable", "focusInitialElement", "focusFirstTabbableElementWhenReady", "focusLastTabbableElementWhenReady", "_getRegionBoundary", "bound", "markers", "console", "warn", "_getFirstTabbableElement", "_getLastTabbableElement", "redirectToElement", "querySelector", "focus<PERSON><PERSON><PERSON><PERSON>", "has<PERSON>tta<PERSON>", "root", "children", "tabbable<PERSON><PERSON><PERSON>", "anchor", "isEnabled", "toggleAnchors", "fn", "isStable", "onStable", "FocusTrapFactory", "create", "deferCaptureElements", "FocusTrapFactory_Factory", "NgZone", "CdkTrapFocus", "focusTrap", "_elementRef", "_focusTrapFactory", "_previouslyFocusedElement", "platform", "nativeElement", "ngAfterContentInit", "autoCapture", "_captureFocus", "ngDoCheck", "ngOnChanges", "autoCaptureChange", "firstChange", "CdkTrapFocus_Factory", "ɵɵdirectiveInject", "ElementRef", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "exportAs", "standalone", "features", "ɵɵInputTransformsFeature", "ɵɵNgOnChangesFeature", "selector", "alias", "transform", "ConfigurableFocusTrap", "_focusTrapManager", "register", "deregister", "_inertStrategy", "defer", "_enable", "preventFocus", "_disable", "allowFocus", "FOCUS_TRAP_INERT_STRATEGY", "EventListenerFocusTrapInertStrategy", "_listener", "e", "_trapFocus", "target", "focusTrapRoot", "contains", "closest", "setTimeout", "activeElement", "FocusTrapManager", "_focusTrapStack", "ft", "stack", "splice", "FocusTrapManager_Factory", "ConfigurableFocusTrapFactory", "configObject", "ConfigurableFocusTrapFactory_Factory", "isFakeMousedownFromScreenReader", "buttons", "detail", "isFakeTouchstartFromScreenReader", "touch", "touches", "changedTouches", "identifier", "radiusX", "radiusY", "INPUT_MODALITY_DETECTOR_OPTIONS", "INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS", "<PERSON><PERSON><PERSON><PERSON>", "TOUCH_BUFFER_MS", "modalityEventListenerOptions", "passive", "capture", "InputModalityDetector", "mostRecentModality", "_modality", "ngZone", "document", "_mostRecentTarget", "_lastTouchMs", "_onKeydown", "_options", "_onMousedown", "Date", "now", "_onTouchstart", "modalityDetected", "modalityChanged", "InputModalityDetector_Factory", "Document", "LIVE_ANNOUNCER_ELEMENT_TOKEN", "LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY", "LIVE_ANNOUNCER_DEFAULT_OPTIONS", "uniqueIds", "LiveAnnouncer", "elementToken", "_defaultOptions", "_liveElement", "_createLiveElement", "announce", "defaultOptions", "politeness", "duration", "clearTimeout", "_previousTimeout", "_exposeAnnouncerToModals", "_currentPromise", "_currentResolve", "elementClass", "previousElements", "getElementsByClassName", "liveEl", "modals", "modal", "ariaOwns", "LiveAnnouncer_Factory", "CdkAriaLive", "_politeness", "_subscription", "_contentObserver", "observe", "elementText", "_previousAnnouncedText", "_liveAnnouncer", "CdkAriaLive_Factory", "ContentObserver", "None", "FocusMonitorDetectionMode", "FOCUS_MONITOR_DEFAULT_OPTIONS", "captureEventListenerOptions", "FocusMonitor", "_inputModalityDetector", "_windowFocused", "_originFromTouchInteraction", "_elementInfo", "_monitoredElementCount", "_rootNodeFocusListenerCount", "_windowFocusListener", "_windowFocusTimeoutId", "_stopInputModalityDetector", "_rootNodeFocusAndBlurListener", "parentElement", "_onFocus", "_onBlur", "_detectionMode", "detectionMode", "IMMEDIATE", "monitor", "check<PERSON><PERSON><PERSON><PERSON>", "rootNode", "_getDocument", "cachedInfo", "subject", "info", "_registerGlobalListeners", "stopMonitoring", "elementInfo", "_setClasses", "_removeGlobalListeners", "focusVia", "focusedElement", "_getClosestElementsInfo", "for<PERSON>ach", "currentElement", "_originChanged", "_set<PERSON><PERSON><PERSON>", "_info", "_getWindow", "doc", "_getFocus<PERSON><PERSON>in", "focusEventTarget", "_shouldBeAttributedToTouch", "_last<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_isLastInteractionFromInputLabel", "EVENTUAL", "toggle", "isFromInteraction", "_originTimeoutId", "ms", "relatedTarget", "Node", "_emit<PERSON><PERSON>in", "observers", "run", "rootNodeFocusListeners", "modality", "results", "mostRecentTarget", "labels", "FocusMonitor_Factory", "CdkMonitorFocus", "_focusMonitor", "_focus<PERSON><PERSON>in", "cdkFocusChange", "<PERSON><PERSON><PERSON><PERSON>", "ngAfterViewInit", "_monitorSubscription", "emit", "CdkMonitorFocus_Factory", "outputs", "HighContrastMode", "BLACK_ON_WHITE_CSS_CLASS", "WHITE_ON_BLACK_CSS_CLASS", "HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS", "HighContrastModeDetector", "_breakpointSubscription", "_hasCheckedHighContrastMode", "_applyBodyHighContrastModeCssClasses", "getHighContrastMode", "NONE", "testElement", "backgroundColor", "position", "documentWindow", "computedStyle", "computedColor", "replace", "WHITE_ON_BLACK", "BLACK_ON_WHITE", "bodyClasses", "mode", "HighContrastModeDetector_Factory", "A11yModule", "highContrastModeDetector", "A11yModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports"], "sources": ["E:/Fashion/DFashion/frontend/node_modules/@angular/cdk/fesm2022/a11y.mjs"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, APP_ID, Injectable, Inject, QueryList, isSignal, effect, booleanAttribute, Directive, Input, InjectionToken, Optional, EventEmitter, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/platform';\nimport { Platform, _getFocusedElementPierceShadowDom, normalizePassiveListenerOptions, _getEventTarget, _getShadowRoot } from '@angular/cdk/platform';\nimport { Subject, Subscription, BehaviorSubject, of } from 'rxjs';\nimport { hasModifierKey, A, Z, ZERO, NINE, PAGE_DOWN, PAGE_UP, END, HOME, LEFT_ARROW, RIGHT_ARROW, UP_ARROW, DOWN_ARROW, TAB, ALT, CONTROL, MAC_META, META, SHIFT } from '@angular/cdk/keycodes';\nimport { tap, debounceTime, filter, map, take, skip, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport * as i1$1 from '@angular/cdk/observers';\nimport { ObserversModule } from '@angular/cdk/observers';\nimport { coerceElement } from '@angular/cdk/coercion';\nimport { BreakpointObserver } from '@angular/cdk/layout';\n\n/** IDs are delimited by an empty space, as per the spec. */\nconst ID_DELIMITER = ' ';\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction addAriaReferencedId(el, attr, id) {\n    const ids = getAriaReferenceIds(el, attr);\n    id = id.trim();\n    if (ids.some(existingId => existingId.trim() === id)) {\n        return;\n    }\n    ids.push(id);\n    el.setAttribute(attr, ids.join(ID_DELIMITER));\n}\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction removeAriaReferencedId(el, attr, id) {\n    const ids = getAriaReferenceIds(el, attr);\n    id = id.trim();\n    const filteredIds = ids.filter(val => val !== id);\n    if (filteredIds.length) {\n        el.setAttribute(attr, filteredIds.join(ID_DELIMITER));\n    }\n    else {\n        el.removeAttribute(attr);\n    }\n}\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction getAriaReferenceIds(el, attr) {\n    // Get string array of all individual ids (whitespace delimited) in the attribute value\n    const attrValue = el.getAttribute(attr);\n    return attrValue?.match(/\\S+/g) ?? [];\n}\n\n/**\n * ID used for the body container where all messages are appended.\n * @deprecated No longer being used. To be removed.\n * @breaking-change 14.0.0\n */\nconst MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n/**\n * ID prefix used for each created message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n/**\n * Attribute given to each host element that is described by a message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n/** Global incremental identifier for each registered message element. */\nlet nextId = 0;\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n */\nclass AriaDescriber {\n    constructor(_document, \n    /**\n     * @deprecated To be turned into a required parameter.\n     * @breaking-change 14.0.0\n     */\n    _platform) {\n        this._platform = _platform;\n        /** Map of all registered message elements that have been placed into the document. */\n        this._messageRegistry = new Map();\n        /** Container for all registered messages. */\n        this._messagesContainer = null;\n        /** Unique ID for the service. */\n        this._id = `${nextId++}`;\n        this._document = _document;\n        this._id = inject(APP_ID) + '-' + nextId++;\n    }\n    describe(hostElement, message, role) {\n        if (!this._canBeDescribed(hostElement, message)) {\n            return;\n        }\n        const key = getKey(message, role);\n        if (typeof message !== 'string') {\n            // We need to ensure that the element has an ID.\n            setMessageId(message, this._id);\n            this._messageRegistry.set(key, { messageElement: message, referenceCount: 0 });\n        }\n        else if (!this._messageRegistry.has(key)) {\n            this._createMessageElement(message, role);\n        }\n        if (!this._isElementDescribedByMessage(hostElement, key)) {\n            this._addMessageReference(hostElement, key);\n        }\n    }\n    removeDescription(hostElement, message, role) {\n        if (!message || !this._isElementNode(hostElement)) {\n            return;\n        }\n        const key = getKey(message, role);\n        if (this._isElementDescribedByMessage(hostElement, key)) {\n            this._removeMessageReference(hostElement, key);\n        }\n        // If the message is a string, it means that it's one that we created for the\n        // consumer so we can remove it safely, otherwise we should leave it in place.\n        if (typeof message === 'string') {\n            const registeredMessage = this._messageRegistry.get(key);\n            if (registeredMessage && registeredMessage.referenceCount === 0) {\n                this._deleteMessageElement(key);\n            }\n        }\n        if (this._messagesContainer?.childNodes.length === 0) {\n            this._messagesContainer.remove();\n            this._messagesContainer = null;\n        }\n    }\n    /** Unregisters all created message elements and removes the message container. */\n    ngOnDestroy() {\n        const describedElements = this._document.querySelectorAll(`[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}=\"${this._id}\"]`);\n        for (let i = 0; i < describedElements.length; i++) {\n            this._removeCdkDescribedByReferenceIds(describedElements[i]);\n            describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n        }\n        this._messagesContainer?.remove();\n        this._messagesContainer = null;\n        this._messageRegistry.clear();\n    }\n    /**\n     * Creates a new element in the visually hidden message container element with the message\n     * as its content and adds it to the message registry.\n     */\n    _createMessageElement(message, role) {\n        const messageElement = this._document.createElement('div');\n        setMessageId(messageElement, this._id);\n        messageElement.textContent = message;\n        if (role) {\n            messageElement.setAttribute('role', role);\n        }\n        this._createMessagesContainer();\n        this._messagesContainer.appendChild(messageElement);\n        this._messageRegistry.set(getKey(message, role), { messageElement, referenceCount: 0 });\n    }\n    /** Deletes the message element from the global messages container. */\n    _deleteMessageElement(key) {\n        this._messageRegistry.get(key)?.messageElement?.remove();\n        this._messageRegistry.delete(key);\n    }\n    /** Creates the global container for all aria-describedby messages. */\n    _createMessagesContainer() {\n        if (this._messagesContainer) {\n            return;\n        }\n        const containerClassName = 'cdk-describedby-message-container';\n        const serverContainers = this._document.querySelectorAll(`.${containerClassName}[platform=\"server\"]`);\n        for (let i = 0; i < serverContainers.length; i++) {\n            // When going from the server to the client, we may end up in a situation where there's\n            // already a container on the page, but we don't have a reference to it. Clear the\n            // old container so we don't get duplicates. Doing this, instead of emptying the previous\n            // container, should be slightly faster.\n            serverContainers[i].remove();\n        }\n        const messagesContainer = this._document.createElement('div');\n        // We add `visibility: hidden` in order to prevent text in this container from\n        // being searchable by the browser's Ctrl + F functionality.\n        // Screen-readers will still read the description for elements with aria-describedby even\n        // when the description element is not visible.\n        messagesContainer.style.visibility = 'hidden';\n        // Even though we use `visibility: hidden`, we still apply `cdk-visually-hidden` so that\n        // the description element doesn't impact page layout.\n        messagesContainer.classList.add(containerClassName);\n        messagesContainer.classList.add('cdk-visually-hidden');\n        // @breaking-change 14.0.0 Remove null check for `_platform`.\n        if (this._platform && !this._platform.isBrowser) {\n            messagesContainer.setAttribute('platform', 'server');\n        }\n        this._document.body.appendChild(messagesContainer);\n        this._messagesContainer = messagesContainer;\n    }\n    /** Removes all cdk-describedby messages that are hosted through the element. */\n    _removeCdkDescribedByReferenceIds(element) {\n        // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n        const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby').filter(id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0);\n        element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n    }\n    /**\n     * Adds a message reference to the element using aria-describedby and increments the registered\n     * message's reference count.\n     */\n    _addMessageReference(element, key) {\n        const registeredMessage = this._messageRegistry.get(key);\n        // Add the aria-describedby reference and set the\n        // describedby_host attribute to mark the element.\n        addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n        element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, this._id);\n        registeredMessage.referenceCount++;\n    }\n    /**\n     * Removes a message reference from the element using aria-describedby\n     * and decrements the registered message's reference count.\n     */\n    _removeMessageReference(element, key) {\n        const registeredMessage = this._messageRegistry.get(key);\n        registeredMessage.referenceCount--;\n        removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n        element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n    /** Returns true if the element has been described by the provided message ID. */\n    _isElementDescribedByMessage(element, key) {\n        const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n        const registeredMessage = this._messageRegistry.get(key);\n        const messageId = registeredMessage && registeredMessage.messageElement.id;\n        return !!messageId && referenceIds.indexOf(messageId) != -1;\n    }\n    /** Determines whether a message can be described on a particular element. */\n    _canBeDescribed(element, message) {\n        if (!this._isElementNode(element)) {\n            return false;\n        }\n        if (message && typeof message === 'object') {\n            // We'd have to make some assumptions about the description element's text, if the consumer\n            // passed in an element. Assume that if an element is passed in, the consumer has verified\n            // that it can be used as a description.\n            return true;\n        }\n        const trimmedMessage = message == null ? '' : `${message}`.trim();\n        const ariaLabel = element.getAttribute('aria-label');\n        // We shouldn't set descriptions if they're exactly the same as the `aria-label` of the\n        // element, because screen readers will end up reading out the same text twice in a row.\n        return trimmedMessage ? !ariaLabel || ariaLabel.trim() !== trimmedMessage : false;\n    }\n    /** Checks whether a node is an Element node. */\n    _isElementNode(element) {\n        return element.nodeType === this._document.ELEMENT_NODE;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: AriaDescriber, deps: [{ token: DOCUMENT }, { token: i1.Platform }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: AriaDescriber, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: AriaDescriber, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1.Platform }] });\n/** Gets a key that can be used to look messages up in the registry. */\nfunction getKey(message, role) {\n    return typeof message === 'string' ? `${role || ''}/${message}` : message;\n}\n/** Assigns a unique ID to an element, if it doesn't have one already. */\nfunction setMessageId(element, serviceId) {\n    if (!element.id) {\n        element.id = `${CDK_DESCRIBEDBY_ID_PREFIX}-${serviceId}-${nextId++}`;\n    }\n}\n\n/**\n * This class manages keyboard events for selectable lists. If you pass it a query list\n * of items, it will set the active item correctly when arrow events occur.\n */\nclass ListKeyManager {\n    constructor(_items, injector) {\n        this._items = _items;\n        this._activeItemIndex = -1;\n        this._activeItem = null;\n        this._wrap = false;\n        this._letterKeyStream = new Subject();\n        this._typeaheadSubscription = Subscription.EMPTY;\n        this._vertical = true;\n        this._allowedModifierKeys = [];\n        this._homeAndEnd = false;\n        this._pageUpAndDown = { enabled: false, delta: 10 };\n        /**\n         * Predicate function that can be used to check whether an item should be skipped\n         * by the key manager. By default, disabled items are skipped.\n         */\n        this._skipPredicateFn = (item) => item.disabled;\n        // Buffer for the letters that the user has pressed when the typeahead option is turned on.\n        this._pressedLetters = [];\n        /**\n         * Stream that emits any time the TAB key is pressed, so components can react\n         * when focus is shifted off of the list.\n         */\n        this.tabOut = new Subject();\n        /** Stream that emits whenever the active item of the list manager changes. */\n        this.change = new Subject();\n        // We allow for the items to be an array because, in some cases, the consumer may\n        // not have access to a QueryList of the items they want to manage (e.g. when the\n        // items aren't being collected via `ViewChildren` or `ContentChildren`).\n        if (_items instanceof QueryList) {\n            this._itemChangesSubscription = _items.changes.subscribe((newItems) => this._itemsChanged(newItems.toArray()));\n        }\n        else if (isSignal(_items)) {\n            if (!injector && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw new Error('ListKeyManager constructed with a signal must receive an injector');\n            }\n            this._effectRef = effect(() => this._itemsChanged(_items()), { injector });\n        }\n    }\n    /**\n     * Sets the predicate function that determines which items should be skipped by the\n     * list key manager.\n     * @param predicate Function that determines whether the given item should be skipped.\n     */\n    skipPredicate(predicate) {\n        this._skipPredicateFn = predicate;\n        return this;\n    }\n    /**\n     * Configures wrapping mode, which determines whether the active item will wrap to\n     * the other end of list when there are no more items in the given direction.\n     * @param shouldWrap Whether the list should wrap when reaching the end.\n     */\n    withWrap(shouldWrap = true) {\n        this._wrap = shouldWrap;\n        return this;\n    }\n    /**\n     * Configures whether the key manager should be able to move the selection vertically.\n     * @param enabled Whether vertical selection should be enabled.\n     */\n    withVerticalOrientation(enabled = true) {\n        this._vertical = enabled;\n        return this;\n    }\n    /**\n     * Configures the key manager to move the selection horizontally.\n     * Passing in `null` will disable horizontal movement.\n     * @param direction Direction in which the selection can be moved.\n     */\n    withHorizontalOrientation(direction) {\n        this._horizontal = direction;\n        return this;\n    }\n    /**\n     * Modifier keys which are allowed to be held down and whose default actions will be prevented\n     * as the user is pressing the arrow keys. Defaults to not allowing any modifier keys.\n     */\n    withAllowedModifierKeys(keys) {\n        this._allowedModifierKeys = keys;\n        return this;\n    }\n    /**\n     * Turns on typeahead mode which allows users to set the active item by typing.\n     * @param debounceInterval Time to wait after the last keystroke before setting the active item.\n     */\n    withTypeAhead(debounceInterval = 200) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            const items = this._getItemsArray();\n            if (items.length > 0 && items.some(item => typeof item.getLabel !== 'function')) {\n                throw Error('ListKeyManager items in typeahead mode must implement the `getLabel` method.');\n            }\n        }\n        this._typeaheadSubscription.unsubscribe();\n        // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n        // and convert those letters back into a string. Afterwards find the first item that starts\n        // with that string and select it.\n        this._typeaheadSubscription = this._letterKeyStream\n            .pipe(tap(letter => this._pressedLetters.push(letter)), debounceTime(debounceInterval), filter(() => this._pressedLetters.length > 0), map(() => this._pressedLetters.join('')))\n            .subscribe(inputString => {\n            const items = this._getItemsArray();\n            // Start at 1 because we want to start searching at the item immediately\n            // following the current active item.\n            for (let i = 1; i < items.length + 1; i++) {\n                const index = (this._activeItemIndex + i) % items.length;\n                const item = items[index];\n                if (!this._skipPredicateFn(item) &&\n                    item.getLabel().toUpperCase().trim().indexOf(inputString) === 0) {\n                    this.setActiveItem(index);\n                    break;\n                }\n            }\n            this._pressedLetters = [];\n        });\n        return this;\n    }\n    /** Cancels the current typeahead sequence. */\n    cancelTypeahead() {\n        this._pressedLetters = [];\n        return this;\n    }\n    /**\n     * Configures the key manager to activate the first and last items\n     * respectively when the Home or End key is pressed.\n     * @param enabled Whether pressing the Home or End key activates the first/last item.\n     */\n    withHomeAndEnd(enabled = true) {\n        this._homeAndEnd = enabled;\n        return this;\n    }\n    /**\n     * Configures the key manager to activate every 10th, configured or first/last element in up/down direction\n     * respectively when the Page-Up or Page-Down key is pressed.\n     * @param enabled Whether pressing the Page-Up or Page-Down key activates the first/last item.\n     * @param delta Whether pressing the Home or End key activates the first/last item.\n     */\n    withPageUpDown(enabled = true, delta = 10) {\n        this._pageUpAndDown = { enabled, delta };\n        return this;\n    }\n    setActiveItem(item) {\n        const previousActiveItem = this._activeItem;\n        this.updateActiveItem(item);\n        if (this._activeItem !== previousActiveItem) {\n            this.change.next(this._activeItemIndex);\n        }\n    }\n    /**\n     * Sets the active item depending on the key event passed in.\n     * @param event Keyboard event to be used for determining which element should be active.\n     */\n    onKeydown(event) {\n        const keyCode = event.keyCode;\n        const modifiers = ['altKey', 'ctrlKey', 'metaKey', 'shiftKey'];\n        const isModifierAllowed = modifiers.every(modifier => {\n            return !event[modifier] || this._allowedModifierKeys.indexOf(modifier) > -1;\n        });\n        switch (keyCode) {\n            case TAB:\n                this.tabOut.next();\n                return;\n            case DOWN_ARROW:\n                if (this._vertical && isModifierAllowed) {\n                    this.setNextItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case UP_ARROW:\n                if (this._vertical && isModifierAllowed) {\n                    this.setPreviousItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case RIGHT_ARROW:\n                if (this._horizontal && isModifierAllowed) {\n                    this._horizontal === 'rtl' ? this.setPreviousItemActive() : this.setNextItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case LEFT_ARROW:\n                if (this._horizontal && isModifierAllowed) {\n                    this._horizontal === 'rtl' ? this.setNextItemActive() : this.setPreviousItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case HOME:\n                if (this._homeAndEnd && isModifierAllowed) {\n                    this.setFirstItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case END:\n                if (this._homeAndEnd && isModifierAllowed) {\n                    this.setLastItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case PAGE_UP:\n                if (this._pageUpAndDown.enabled && isModifierAllowed) {\n                    const targetIndex = this._activeItemIndex - this._pageUpAndDown.delta;\n                    this._setActiveItemByIndex(targetIndex > 0 ? targetIndex : 0, 1);\n                    break;\n                }\n                else {\n                    return;\n                }\n            case PAGE_DOWN:\n                if (this._pageUpAndDown.enabled && isModifierAllowed) {\n                    const targetIndex = this._activeItemIndex + this._pageUpAndDown.delta;\n                    const itemsLength = this._getItemsArray().length;\n                    this._setActiveItemByIndex(targetIndex < itemsLength ? targetIndex : itemsLength - 1, -1);\n                    break;\n                }\n                else {\n                    return;\n                }\n            default:\n                if (isModifierAllowed || hasModifierKey(event, 'shiftKey')) {\n                    // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n                    // otherwise fall back to resolving alphanumeric characters via the keyCode.\n                    if (event.key && event.key.length === 1) {\n                        this._letterKeyStream.next(event.key.toLocaleUpperCase());\n                    }\n                    else if ((keyCode >= A && keyCode <= Z) || (keyCode >= ZERO && keyCode <= NINE)) {\n                        this._letterKeyStream.next(String.fromCharCode(keyCode));\n                    }\n                }\n                // Note that we return here, in order to avoid preventing\n                // the default action of non-navigational keys.\n                return;\n        }\n        this._pressedLetters = [];\n        event.preventDefault();\n    }\n    /** Index of the currently active item. */\n    get activeItemIndex() {\n        return this._activeItemIndex;\n    }\n    /** The active item. */\n    get activeItem() {\n        return this._activeItem;\n    }\n    /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n    isTyping() {\n        return this._pressedLetters.length > 0;\n    }\n    /** Sets the active item to the first enabled item in the list. */\n    setFirstItemActive() {\n        this._setActiveItemByIndex(0, 1);\n    }\n    /** Sets the active item to the last enabled item in the list. */\n    setLastItemActive() {\n        this._setActiveItemByIndex(this._getItemsArray().length - 1, -1);\n    }\n    /** Sets the active item to the next enabled item in the list. */\n    setNextItemActive() {\n        this._activeItemIndex < 0 ? this.setFirstItemActive() : this._setActiveItemByDelta(1);\n    }\n    /** Sets the active item to a previous enabled item in the list. */\n    setPreviousItemActive() {\n        this._activeItemIndex < 0 && this._wrap\n            ? this.setLastItemActive()\n            : this._setActiveItemByDelta(-1);\n    }\n    updateActiveItem(item) {\n        const itemArray = this._getItemsArray();\n        const index = typeof item === 'number' ? item : itemArray.indexOf(item);\n        const activeItem = itemArray[index];\n        // Explicitly check for `null` and `undefined` because other falsy values are valid.\n        this._activeItem = activeItem == null ? null : activeItem;\n        this._activeItemIndex = index;\n    }\n    /** Cleans up the key manager. */\n    destroy() {\n        this._typeaheadSubscription.unsubscribe();\n        this._itemChangesSubscription?.unsubscribe();\n        this._effectRef?.destroy();\n        this._letterKeyStream.complete();\n        this.tabOut.complete();\n        this.change.complete();\n        this._pressedLetters = [];\n    }\n    /**\n     * This method sets the active item, given a list of items and the delta between the\n     * currently active item and the new active item. It will calculate differently\n     * depending on whether wrap mode is turned on.\n     */\n    _setActiveItemByDelta(delta) {\n        this._wrap ? this._setActiveInWrapMode(delta) : this._setActiveInDefaultMode(delta);\n    }\n    /**\n     * Sets the active item properly given \"wrap\" mode. In other words, it will continue to move\n     * down the list until it finds an item that is not disabled, and it will wrap if it\n     * encounters either end of the list.\n     */\n    _setActiveInWrapMode(delta) {\n        const items = this._getItemsArray();\n        for (let i = 1; i <= items.length; i++) {\n            const index = (this._activeItemIndex + delta * i + items.length) % items.length;\n            const item = items[index];\n            if (!this._skipPredicateFn(item)) {\n                this.setActiveItem(index);\n                return;\n            }\n        }\n    }\n    /**\n     * Sets the active item properly given the default mode. In other words, it will\n     * continue to move down the list until it finds an item that is not disabled. If\n     * it encounters either end of the list, it will stop and not wrap.\n     */\n    _setActiveInDefaultMode(delta) {\n        this._setActiveItemByIndex(this._activeItemIndex + delta, delta);\n    }\n    /**\n     * Sets the active item to the first enabled item starting at the index specified. If the\n     * item is disabled, it will move in the fallbackDelta direction until it either\n     * finds an enabled item or encounters the end of the list.\n     */\n    _setActiveItemByIndex(index, fallbackDelta) {\n        const items = this._getItemsArray();\n        if (!items[index]) {\n            return;\n        }\n        while (this._skipPredicateFn(items[index])) {\n            index += fallbackDelta;\n            if (!items[index]) {\n                return;\n            }\n        }\n        this.setActiveItem(index);\n    }\n    /** Returns the items as an array. */\n    _getItemsArray() {\n        if (isSignal(this._items)) {\n            return this._items();\n        }\n        return this._items instanceof QueryList ? this._items.toArray() : this._items;\n    }\n    /** Callback for when the items have changed. */\n    _itemsChanged(newItems) {\n        if (this._activeItem) {\n            const newIndex = newItems.indexOf(this._activeItem);\n            if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n                this._activeItemIndex = newIndex;\n            }\n        }\n    }\n}\n\nclass ActiveDescendantKeyManager extends ListKeyManager {\n    setActiveItem(index) {\n        if (this.activeItem) {\n            this.activeItem.setInactiveStyles();\n        }\n        super.setActiveItem(index);\n        if (this.activeItem) {\n            this.activeItem.setActiveStyles();\n        }\n    }\n}\n\nclass FocusKeyManager extends ListKeyManager {\n    constructor() {\n        super(...arguments);\n        this._origin = 'program';\n    }\n    /**\n     * Sets the focus origin that will be passed in to the items for any subsequent `focus` calls.\n     * @param origin Focus origin to be used when focusing items.\n     */\n    setFocusOrigin(origin) {\n        this._origin = origin;\n        return this;\n    }\n    setActiveItem(item) {\n        super.setActiveItem(item);\n        if (this.activeItem) {\n            this.activeItem.focus(this._origin);\n        }\n    }\n}\n\n/**\n * Configuration for the isFocusable method.\n */\nclass IsFocusableConfig {\n    constructor() {\n        /**\n         * Whether to count an element as focusable even if it is not currently visible.\n         */\n        this.ignoreVisibility = false;\n    }\n}\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n/**\n * Utility for checking the interactivity of an element, such as whether it is focusable or\n * tabbable.\n */\nclass InteractivityChecker {\n    constructor(_platform) {\n        this._platform = _platform;\n    }\n    /**\n     * Gets whether an element is disabled.\n     *\n     * @param element Element to be checked.\n     * @returns Whether the element is disabled.\n     */\n    isDisabled(element) {\n        // This does not capture some cases, such as a non-form control with a disabled attribute or\n        // a form control inside of a disabled form, but should capture the most common cases.\n        return element.hasAttribute('disabled');\n    }\n    /**\n     * Gets whether an element is visible for the purposes of interactivity.\n     *\n     * This will capture states like `display: none` and `visibility: hidden`, but not things like\n     * being clipped by an `overflow: hidden` parent or being outside the viewport.\n     *\n     * @returns Whether the element is visible.\n     */\n    isVisible(element) {\n        return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n    }\n    /**\n     * Gets whether an element can be reached via Tab key.\n     * Assumes that the element has already been checked with isFocusable.\n     *\n     * @param element Element to be checked.\n     * @returns Whether the element is tabbable.\n     */\n    isTabbable(element) {\n        // Nothing is tabbable on the server 😎\n        if (!this._platform.isBrowser) {\n            return false;\n        }\n        const frameElement = getFrameElement(getWindow(element));\n        if (frameElement) {\n            // Frame elements inherit their tabindex onto all child elements.\n            if (getTabIndexValue(frameElement) === -1) {\n                return false;\n            }\n            // Browsers disable tabbing to an element inside of an invisible frame.\n            if (!this.isVisible(frameElement)) {\n                return false;\n            }\n        }\n        let nodeName = element.nodeName.toLowerCase();\n        let tabIndexValue = getTabIndexValue(element);\n        if (element.hasAttribute('contenteditable')) {\n            return tabIndexValue !== -1;\n        }\n        if (nodeName === 'iframe' || nodeName === 'object') {\n            // The frame or object's content may be tabbable depending on the content, but it's\n            // not possibly to reliably detect the content of the frames. We always consider such\n            // elements as non-tabbable.\n            return false;\n        }\n        // In iOS, the browser only considers some specific elements as tabbable.\n        if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n            return false;\n        }\n        if (nodeName === 'audio') {\n            // Audio elements without controls enabled are never tabbable, regardless\n            // of the tabindex attribute explicitly being set.\n            if (!element.hasAttribute('controls')) {\n                return false;\n            }\n            // Audio elements with controls are by default tabbable unless the\n            // tabindex attribute is set to `-1` explicitly.\n            return tabIndexValue !== -1;\n        }\n        if (nodeName === 'video') {\n            // For all video elements, if the tabindex attribute is set to `-1`, the video\n            // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n            // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n            // tabindex attribute is the source of truth here.\n            if (tabIndexValue === -1) {\n                return false;\n            }\n            // If the tabindex is explicitly set, and not `-1` (as per check before), the\n            // video element is always tabbable (regardless of whether it has controls or not).\n            if (tabIndexValue !== null) {\n                return true;\n            }\n            // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n            // has controls enabled. Firefox is special as videos are always tabbable regardless\n            // of whether there are controls or not.\n            return this._platform.FIREFOX || element.hasAttribute('controls');\n        }\n        return element.tabIndex >= 0;\n    }\n    /**\n     * Gets whether an element can be focused by the user.\n     *\n     * @param element Element to be checked.\n     * @param config The config object with options to customize this method's behavior\n     * @returns Whether the element is focusable.\n     */\n    isFocusable(element, config) {\n        // Perform checks in order of left to most expensive.\n        // Again, naive approach that does not capture many edge cases and browser quirks.\n        return (isPotentiallyFocusable(element) &&\n            !this.isDisabled(element) &&\n            (config?.ignoreVisibility || this.isVisible(element)));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: InteractivityChecker, deps: [{ token: i1.Platform }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: InteractivityChecker, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: InteractivityChecker, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: i1.Platform }] });\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window) {\n    try {\n        return window.frameElement;\n    }\n    catch {\n        return null;\n    }\n}\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element) {\n    // Use logic from jQuery to check for an invisible element.\n    // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n    return !!(element.offsetWidth ||\n        element.offsetHeight ||\n        (typeof element.getClientRects === 'function' && element.getClientRects().length));\n}\n/** Gets whether an element's  */\nfunction isNativeFormElement(element) {\n    let nodeName = element.nodeName.toLowerCase();\n    return (nodeName === 'input' ||\n        nodeName === 'select' ||\n        nodeName === 'button' ||\n        nodeName === 'textarea');\n}\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element) {\n    return isInputElement(element) && element.type == 'hidden';\n}\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element) {\n    return isAnchorElement(element) && element.hasAttribute('href');\n}\n/** Gets whether an element is an input element. */\nfunction isInputElement(element) {\n    return element.nodeName.toLowerCase() == 'input';\n}\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element) {\n    return element.nodeName.toLowerCase() == 'a';\n}\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element) {\n    if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n        return false;\n    }\n    let tabIndex = element.getAttribute('tabindex');\n    return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element) {\n    if (!hasValidTabIndex(element)) {\n        return null;\n    }\n    // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n    const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n    return isNaN(tabIndex) ? -1 : tabIndex;\n}\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element) {\n    let nodeName = element.nodeName.toLowerCase();\n    let inputType = nodeName === 'input' && element.type;\n    return (inputType === 'text' ||\n        inputType === 'password' ||\n        nodeName === 'select' ||\n        nodeName === 'textarea');\n}\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element) {\n    // Inputs are potentially focusable *unless* they're type=\"hidden\".\n    if (isHiddenInput(element)) {\n        return false;\n    }\n    return (isNativeFormElement(element) ||\n        isAnchorWithHref(element) ||\n        element.hasAttribute('contenteditable') ||\n        hasValidTabIndex(element));\n}\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node) {\n    // ownerDocument is null if `node` itself *is* a document.\n    return (node.ownerDocument && node.ownerDocument.defaultView) || window;\n}\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to be misaligned.\n */\nclass FocusTrap {\n    /** Whether the focus trap is active. */\n    get enabled() {\n        return this._enabled;\n    }\n    set enabled(value) {\n        this._enabled = value;\n        if (this._startAnchor && this._endAnchor) {\n            this._toggleAnchorTabIndex(value, this._startAnchor);\n            this._toggleAnchorTabIndex(value, this._endAnchor);\n        }\n    }\n    constructor(_element, _checker, _ngZone, _document, deferAnchors = false) {\n        this._element = _element;\n        this._checker = _checker;\n        this._ngZone = _ngZone;\n        this._document = _document;\n        this._hasAttached = false;\n        // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n        this.startAnchorListener = () => this.focusLastTabbableElement();\n        this.endAnchorListener = () => this.focusFirstTabbableElement();\n        this._enabled = true;\n        if (!deferAnchors) {\n            this.attachAnchors();\n        }\n    }\n    /** Destroys the focus trap by cleaning up the anchors. */\n    destroy() {\n        const startAnchor = this._startAnchor;\n        const endAnchor = this._endAnchor;\n        if (startAnchor) {\n            startAnchor.removeEventListener('focus', this.startAnchorListener);\n            startAnchor.remove();\n        }\n        if (endAnchor) {\n            endAnchor.removeEventListener('focus', this.endAnchorListener);\n            endAnchor.remove();\n        }\n        this._startAnchor = this._endAnchor = null;\n        this._hasAttached = false;\n    }\n    /**\n     * Inserts the anchors into the DOM. This is usually done automatically\n     * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n     * @returns Whether the focus trap managed to attach successfully. This may not be the case\n     * if the target element isn't currently in the DOM.\n     */\n    attachAnchors() {\n        // If we're not on the browser, there can be no focus to trap.\n        if (this._hasAttached) {\n            return true;\n        }\n        this._ngZone.runOutsideAngular(() => {\n            if (!this._startAnchor) {\n                this._startAnchor = this._createAnchor();\n                this._startAnchor.addEventListener('focus', this.startAnchorListener);\n            }\n            if (!this._endAnchor) {\n                this._endAnchor = this._createAnchor();\n                this._endAnchor.addEventListener('focus', this.endAnchorListener);\n            }\n        });\n        if (this._element.parentNode) {\n            this._element.parentNode.insertBefore(this._startAnchor, this._element);\n            this._element.parentNode.insertBefore(this._endAnchor, this._element.nextSibling);\n            this._hasAttached = true;\n        }\n        return this._hasAttached;\n    }\n    /**\n     * Waits for the zone to stabilize, then focuses the first tabbable element.\n     * @returns Returns a promise that resolves with a boolean, depending\n     * on whether focus was moved successfully.\n     */\n    focusInitialElementWhenReady(options) {\n        return new Promise(resolve => {\n            this._executeOnStable(() => resolve(this.focusInitialElement(options)));\n        });\n    }\n    /**\n     * Waits for the zone to stabilize, then focuses\n     * the first tabbable element within the focus trap region.\n     * @returns Returns a promise that resolves with a boolean, depending\n     * on whether focus was moved successfully.\n     */\n    focusFirstTabbableElementWhenReady(options) {\n        return new Promise(resolve => {\n            this._executeOnStable(() => resolve(this.focusFirstTabbableElement(options)));\n        });\n    }\n    /**\n     * Waits for the zone to stabilize, then focuses\n     * the last tabbable element within the focus trap region.\n     * @returns Returns a promise that resolves with a boolean, depending\n     * on whether focus was moved successfully.\n     */\n    focusLastTabbableElementWhenReady(options) {\n        return new Promise(resolve => {\n            this._executeOnStable(() => resolve(this.focusLastTabbableElement(options)));\n        });\n    }\n    /**\n     * Get the specified boundary element of the trapped region.\n     * @param bound The boundary to get (start or end of trapped region).\n     * @returns The boundary element.\n     */\n    _getRegionBoundary(bound) {\n        // Contains the deprecated version of selector, for temporary backwards comparability.\n        const markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` + `[cdkFocusRegion${bound}], ` + `[cdk-focus-${bound}]`);\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            for (let i = 0; i < markers.length; i++) {\n                // @breaking-change 8.0.0\n                if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n                    console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}', ` +\n                        `use 'cdkFocusRegion${bound}' instead. The deprecated ` +\n                        `attribute will be removed in 8.0.0.`, markers[i]);\n                }\n                else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n                    console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}', ` +\n                        `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` +\n                        `will be removed in 8.0.0.`, markers[i]);\n                }\n            }\n        }\n        if (bound == 'start') {\n            return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n        }\n        return markers.length\n            ? markers[markers.length - 1]\n            : this._getLastTabbableElement(this._element);\n    }\n    /**\n     * Focuses the element that should be focused when the focus trap is initialized.\n     * @returns Whether focus was moved successfully.\n     */\n    focusInitialElement(options) {\n        // Contains the deprecated version of selector, for temporary backwards comparability.\n        const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` + `[cdkFocusInitial]`);\n        if (redirectToElement) {\n            // @breaking-change 8.0.0\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n                redirectToElement.hasAttribute(`cdk-focus-initial`)) {\n                console.warn(`Found use of deprecated attribute 'cdk-focus-initial', ` +\n                    `use 'cdkFocusInitial' instead. The deprecated attribute ` +\n                    `will be removed in 8.0.0`, redirectToElement);\n            }\n            // Warn the consumer if the element they've pointed to\n            // isn't focusable, when not in production mode.\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n                !this._checker.isFocusable(redirectToElement)) {\n                console.warn(`Element matching '[cdkFocusInitial]' is not focusable.`, redirectToElement);\n            }\n            if (!this._checker.isFocusable(redirectToElement)) {\n                const focusableChild = this._getFirstTabbableElement(redirectToElement);\n                focusableChild?.focus(options);\n                return !!focusableChild;\n            }\n            redirectToElement.focus(options);\n            return true;\n        }\n        return this.focusFirstTabbableElement(options);\n    }\n    /**\n     * Focuses the first tabbable element within the focus trap region.\n     * @returns Whether focus was moved successfully.\n     */\n    focusFirstTabbableElement(options) {\n        const redirectToElement = this._getRegionBoundary('start');\n        if (redirectToElement) {\n            redirectToElement.focus(options);\n        }\n        return !!redirectToElement;\n    }\n    /**\n     * Focuses the last tabbable element within the focus trap region.\n     * @returns Whether focus was moved successfully.\n     */\n    focusLastTabbableElement(options) {\n        const redirectToElement = this._getRegionBoundary('end');\n        if (redirectToElement) {\n            redirectToElement.focus(options);\n        }\n        return !!redirectToElement;\n    }\n    /**\n     * Checks whether the focus trap has successfully been attached.\n     */\n    hasAttached() {\n        return this._hasAttached;\n    }\n    /** Get the first tabbable element from a DOM subtree (inclusive). */\n    _getFirstTabbableElement(root) {\n        if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n            return root;\n        }\n        const children = root.children;\n        for (let i = 0; i < children.length; i++) {\n            const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE\n                ? this._getFirstTabbableElement(children[i])\n                : null;\n            if (tabbableChild) {\n                return tabbableChild;\n            }\n        }\n        return null;\n    }\n    /** Get the last tabbable element from a DOM subtree (inclusive). */\n    _getLastTabbableElement(root) {\n        if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n            return root;\n        }\n        // Iterate in reverse DOM order.\n        const children = root.children;\n        for (let i = children.length - 1; i >= 0; i--) {\n            const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE\n                ? this._getLastTabbableElement(children[i])\n                : null;\n            if (tabbableChild) {\n                return tabbableChild;\n            }\n        }\n        return null;\n    }\n    /** Creates an anchor element. */\n    _createAnchor() {\n        const anchor = this._document.createElement('div');\n        this._toggleAnchorTabIndex(this._enabled, anchor);\n        anchor.classList.add('cdk-visually-hidden');\n        anchor.classList.add('cdk-focus-trap-anchor');\n        anchor.setAttribute('aria-hidden', 'true');\n        return anchor;\n    }\n    /**\n     * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n     * @param isEnabled Whether the focus trap is enabled.\n     * @param anchor Anchor on which to toggle the tabindex.\n     */\n    _toggleAnchorTabIndex(isEnabled, anchor) {\n        // Remove the tabindex completely, rather than setting it to -1, because if the\n        // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n        isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n    }\n    /**\n     * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n     * @param enabled: Whether the anchors should trap Tab.\n     */\n    toggleAnchors(enabled) {\n        if (this._startAnchor && this._endAnchor) {\n            this._toggleAnchorTabIndex(enabled, this._startAnchor);\n            this._toggleAnchorTabIndex(enabled, this._endAnchor);\n        }\n    }\n    /** Executes a function when the zone is stable. */\n    _executeOnStable(fn) {\n        if (this._ngZone.isStable) {\n            fn();\n        }\n        else {\n            this._ngZone.onStable.pipe(take(1)).subscribe(fn);\n        }\n    }\n}\n/**\n * Factory that allows easy instantiation of focus traps.\n */\nclass FocusTrapFactory {\n    constructor(_checker, _ngZone, _document) {\n        this._checker = _checker;\n        this._ngZone = _ngZone;\n        this._document = _document;\n    }\n    /**\n     * Creates a focus-trapped region around the given element.\n     * @param element The element around which focus will be trapped.\n     * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n     *     manually by the user.\n     * @returns The created focus trap instance.\n     */\n    create(element, deferCaptureElements = false) {\n        return new FocusTrap(element, this._checker, this._ngZone, this._document, deferCaptureElements);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: FocusTrapFactory, deps: [{ token: InteractivityChecker }, { token: i0.NgZone }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: FocusTrapFactory, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: FocusTrapFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: InteractivityChecker }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n/** Directive for trapping focus within a region. */\nclass CdkTrapFocus {\n    /** Whether the focus trap is active. */\n    get enabled() {\n        return this.focusTrap?.enabled || false;\n    }\n    set enabled(value) {\n        if (this.focusTrap) {\n            this.focusTrap.enabled = value;\n        }\n    }\n    constructor(_elementRef, _focusTrapFactory, \n    /**\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 13.0.0\n     */\n    _document) {\n        this._elementRef = _elementRef;\n        this._focusTrapFactory = _focusTrapFactory;\n        /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n        this._previouslyFocusedElement = null;\n        const platform = inject(Platform);\n        if (platform.isBrowser) {\n            this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n        }\n    }\n    ngOnDestroy() {\n        this.focusTrap?.destroy();\n        // If we stored a previously focused element when using autoCapture, return focus to that\n        // element now that the trapped region is being destroyed.\n        if (this._previouslyFocusedElement) {\n            this._previouslyFocusedElement.focus();\n            this._previouslyFocusedElement = null;\n        }\n    }\n    ngAfterContentInit() {\n        this.focusTrap?.attachAnchors();\n        if (this.autoCapture) {\n            this._captureFocus();\n        }\n    }\n    ngDoCheck() {\n        if (this.focusTrap && !this.focusTrap.hasAttached()) {\n            this.focusTrap.attachAnchors();\n        }\n    }\n    ngOnChanges(changes) {\n        const autoCaptureChange = changes['autoCapture'];\n        if (autoCaptureChange &&\n            !autoCaptureChange.firstChange &&\n            this.autoCapture &&\n            this.focusTrap?.hasAttached()) {\n            this._captureFocus();\n        }\n    }\n    _captureFocus() {\n        this._previouslyFocusedElement = _getFocusedElementPierceShadowDom();\n        this.focusTrap?.focusInitialElementWhenReady();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTrapFocus, deps: [{ token: i0.ElementRef }, { token: FocusTrapFactory }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: CdkTrapFocus, isStandalone: true, selector: \"[cdkTrapFocus]\", inputs: { enabled: [\"cdkTrapFocus\", \"enabled\", booleanAttribute], autoCapture: [\"cdkTrapFocusAutoCapture\", \"autoCapture\", booleanAttribute] }, exportAs: [\"cdkTrapFocus\"], usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTrapFocus, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkTrapFocus]',\n                    exportAs: 'cdkTrapFocus',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: FocusTrapFactory }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }], propDecorators: { enabled: [{\n                type: Input,\n                args: [{ alias: 'cdkTrapFocus', transform: booleanAttribute }]\n            }], autoCapture: [{\n                type: Input,\n                args: [{ alias: 'cdkTrapFocusAutoCapture', transform: booleanAttribute }]\n            }] } });\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class uses a strategy pattern that determines how it traps focus.\n * See FocusTrapInertStrategy.\n */\nclass ConfigurableFocusTrap extends FocusTrap {\n    /** Whether the FocusTrap is enabled. */\n    get enabled() {\n        return this._enabled;\n    }\n    set enabled(value) {\n        this._enabled = value;\n        if (this._enabled) {\n            this._focusTrapManager.register(this);\n        }\n        else {\n            this._focusTrapManager.deregister(this);\n        }\n    }\n    constructor(_element, _checker, _ngZone, _document, _focusTrapManager, _inertStrategy, config) {\n        super(_element, _checker, _ngZone, _document, config.defer);\n        this._focusTrapManager = _focusTrapManager;\n        this._inertStrategy = _inertStrategy;\n        this._focusTrapManager.register(this);\n    }\n    /** Notifies the FocusTrapManager that this FocusTrap will be destroyed. */\n    destroy() {\n        this._focusTrapManager.deregister(this);\n        super.destroy();\n    }\n    /** @docs-private Implemented as part of ManagedFocusTrap. */\n    _enable() {\n        this._inertStrategy.preventFocus(this);\n        this.toggleAnchors(true);\n    }\n    /** @docs-private Implemented as part of ManagedFocusTrap. */\n    _disable() {\n        this._inertStrategy.allowFocus(this);\n        this.toggleAnchors(false);\n    }\n}\n\n/** The injection token used to specify the inert strategy. */\nconst FOCUS_TRAP_INERT_STRATEGY = new InjectionToken('FOCUS_TRAP_INERT_STRATEGY');\n\n/**\n * Lightweight FocusTrapInertStrategy that adds a document focus event\n * listener to redirect focus back inside the FocusTrap.\n */\nclass EventListenerFocusTrapInertStrategy {\n    constructor() {\n        /** Focus event handler. */\n        this._listener = null;\n    }\n    /** Adds a document event listener that keeps focus inside the FocusTrap. */\n    preventFocus(focusTrap) {\n        // Ensure there's only one listener per document\n        if (this._listener) {\n            focusTrap._document.removeEventListener('focus', this._listener, true);\n        }\n        this._listener = (e) => this._trapFocus(focusTrap, e);\n        focusTrap._ngZone.runOutsideAngular(() => {\n            focusTrap._document.addEventListener('focus', this._listener, true);\n        });\n    }\n    /** Removes the event listener added in preventFocus. */\n    allowFocus(focusTrap) {\n        if (!this._listener) {\n            return;\n        }\n        focusTrap._document.removeEventListener('focus', this._listener, true);\n        this._listener = null;\n    }\n    /**\n     * Refocuses the first element in the FocusTrap if the focus event target was outside\n     * the FocusTrap.\n     *\n     * This is an event listener callback. The event listener is added in runOutsideAngular,\n     * so all this code runs outside Angular as well.\n     */\n    _trapFocus(focusTrap, event) {\n        const target = event.target;\n        const focusTrapRoot = focusTrap._element;\n        // Don't refocus if target was in an overlay, because the overlay might be associated\n        // with an element inside the FocusTrap, ex. mat-select.\n        if (target && !focusTrapRoot.contains(target) && !target.closest?.('div.cdk-overlay-pane')) {\n            // Some legacy FocusTrap usages have logic that focuses some element on the page\n            // just before FocusTrap is destroyed. For backwards compatibility, wait\n            // to be sure FocusTrap is still enabled before refocusing.\n            setTimeout(() => {\n                // Check whether focus wasn't put back into the focus trap while the timeout was pending.\n                if (focusTrap.enabled && !focusTrapRoot.contains(focusTrap._document.activeElement)) {\n                    focusTrap.focusFirstTabbableElement();\n                }\n            });\n        }\n    }\n}\n\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\nclass FocusTrapManager {\n    constructor() {\n        // A stack of the FocusTraps on the page. Only the FocusTrap at the\n        // top of the stack is active.\n        this._focusTrapStack = [];\n    }\n    /**\n     * Disables the FocusTrap at the top of the stack, and then pushes\n     * the new FocusTrap onto the stack.\n     */\n    register(focusTrap) {\n        // Dedupe focusTraps that register multiple times.\n        this._focusTrapStack = this._focusTrapStack.filter(ft => ft !== focusTrap);\n        let stack = this._focusTrapStack;\n        if (stack.length) {\n            stack[stack.length - 1]._disable();\n        }\n        stack.push(focusTrap);\n        focusTrap._enable();\n    }\n    /**\n     * Removes the FocusTrap from the stack, and activates the\n     * FocusTrap that is the new top of the stack.\n     */\n    deregister(focusTrap) {\n        focusTrap._disable();\n        const stack = this._focusTrapStack;\n        const i = stack.indexOf(focusTrap);\n        if (i !== -1) {\n            stack.splice(i, 1);\n            if (stack.length) {\n                stack[stack.length - 1]._enable();\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: FocusTrapManager, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: FocusTrapManager, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: FocusTrapManager, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n/** Factory that allows easy instantiation of configurable focus traps. */\nclass ConfigurableFocusTrapFactory {\n    constructor(_checker, _ngZone, _focusTrapManager, _document, _inertStrategy) {\n        this._checker = _checker;\n        this._ngZone = _ngZone;\n        this._focusTrapManager = _focusTrapManager;\n        this._document = _document;\n        // TODO split up the strategies into different modules, similar to DateAdapter.\n        this._inertStrategy = _inertStrategy || new EventListenerFocusTrapInertStrategy();\n    }\n    create(element, config = { defer: false }) {\n        let configObject;\n        if (typeof config === 'boolean') {\n            configObject = { defer: config };\n        }\n        else {\n            configObject = config;\n        }\n        return new ConfigurableFocusTrap(element, this._checker, this._ngZone, this._document, this._focusTrapManager, this._inertStrategy, configObject);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: ConfigurableFocusTrapFactory, deps: [{ token: InteractivityChecker }, { token: i0.NgZone }, { token: FocusTrapManager }, { token: DOCUMENT }, { token: FOCUS_TRAP_INERT_STRATEGY, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: ConfigurableFocusTrapFactory, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: ConfigurableFocusTrapFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: InteractivityChecker }, { type: i0.NgZone }, { type: FocusTrapManager }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [FOCUS_TRAP_INERT_STRATEGY]\n                }] }] });\n\n/** Gets whether an event could be a faked `mousedown` event dispatched by a screen reader. */\nfunction isFakeMousedownFromScreenReader(event) {\n    // Some screen readers will dispatch a fake `mousedown` event when pressing enter or space on\n    // a clickable element. We can distinguish these events when `event.buttons` is zero, or\n    // `event.detail` is zero depending on the browser:\n    // - `event.buttons` works on Firefox, but fails on Chrome.\n    // - `detail` works on Chrome, but fails on Firefox.\n    return event.buttons === 0 || event.detail === 0;\n}\n/** Gets whether an event could be a faked `touchstart` event dispatched by a screen reader. */\nfunction isFakeTouchstartFromScreenReader(event) {\n    const touch = (event.touches && event.touches[0]) || (event.changedTouches && event.changedTouches[0]);\n    // A fake `touchstart` can be distinguished from a real one by looking at the `identifier`\n    // which is typically >= 0 on a real device versus -1 from a screen reader. Just to be safe,\n    // we can also look at `radiusX` and `radiusY`. This behavior was observed against a Windows 10\n    // device with a touch screen running NVDA v2020.4 and Firefox 85 or Chrome 88.\n    return (!!touch &&\n        touch.identifier === -1 &&\n        (touch.radiusX == null || touch.radiusX === 1) &&\n        (touch.radiusY == null || touch.radiusY === 1));\n}\n\n/**\n * Injectable options for the InputModalityDetector. These are shallowly merged with the default\n * options.\n */\nconst INPUT_MODALITY_DETECTOR_OPTIONS = new InjectionToken('cdk-input-modality-detector-options');\n/**\n * Default options for the InputModalityDetector.\n *\n * Modifier keys are ignored by default (i.e. when pressed won't cause the service to detect\n * keyboard input modality) for two reasons:\n *\n * 1. Modifier keys are commonly used with mouse to perform actions such as 'right click' or 'open\n *    in new tab', and are thus less representative of actual keyboard interaction.\n * 2. VoiceOver triggers some keyboard events when linearly navigating with Control + Option (but\n *    confusingly not with Caps Lock). Thus, to have parity with other screen readers, we ignore\n *    these keys so as to not update the input modality.\n *\n * Note that we do not by default ignore the right Meta key on Safari because it has the same key\n * code as the ContextMenu key on other browsers. When we switch to using event.key, we can\n * distinguish between the two.\n */\nconst INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS = {\n    ignoreKeys: [ALT, CONTROL, MAC_META, META, SHIFT],\n};\n/**\n * The amount of time needed to pass after a touchstart event in order for a subsequent mousedown\n * event to be attributed as mouse and not touch.\n *\n * This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n * that a value of around 650ms seems appropriate.\n */\nconst TOUCH_BUFFER_MS = 650;\n/**\n * Event listener options that enable capturing and also mark the listener as passive if the browser\n * supports it.\n */\nconst modalityEventListenerOptions = normalizePassiveListenerOptions({\n    passive: true,\n    capture: true,\n});\n/**\n * Service that detects the user's input modality.\n *\n * This service does not update the input modality when a user navigates with a screen reader\n * (e.g. linear navigation with VoiceOver, object navigation / browse mode with NVDA, virtual PC\n * cursor mode with JAWS). This is in part due to technical limitations (i.e. keyboard events do not\n * fire as expected in these modes) but is also arguably the correct behavior. Navigating with a\n * screen reader is akin to visually scanning a page, and should not be interpreted as actual user\n * input interaction.\n *\n * When a user is not navigating but *interacting* with a screen reader, this service attempts to\n * update the input modality to keyboard, but in general this service's behavior is largely\n * undefined.\n */\nclass InputModalityDetector {\n    /** The most recently detected input modality. */\n    get mostRecentModality() {\n        return this._modality.value;\n    }\n    constructor(_platform, ngZone, document, options) {\n        this._platform = _platform;\n        /**\n         * The most recently detected input modality event target. Is null if no input modality has been\n         * detected or if the associated event target is null for some unknown reason.\n         */\n        this._mostRecentTarget = null;\n        /** The underlying BehaviorSubject that emits whenever an input modality is detected. */\n        this._modality = new BehaviorSubject(null);\n        /**\n         * The timestamp of the last touch input modality. Used to determine whether mousedown events\n         * should be attributed to mouse or touch.\n         */\n        this._lastTouchMs = 0;\n        /**\n         * Handles keydown events. Must be an arrow function in order to preserve the context when it gets\n         * bound.\n         */\n        this._onKeydown = (event) => {\n            // If this is one of the keys we should ignore, then ignore it and don't update the input\n            // modality to keyboard.\n            if (this._options?.ignoreKeys?.some(keyCode => keyCode === event.keyCode)) {\n                return;\n            }\n            this._modality.next('keyboard');\n            this._mostRecentTarget = _getEventTarget(event);\n        };\n        /**\n         * Handles mousedown events. Must be an arrow function in order to preserve the context when it\n         * gets bound.\n         */\n        this._onMousedown = (event) => {\n            // Touches trigger both touch and mouse events, so we need to distinguish between mouse events\n            // that were triggered via mouse vs touch. To do so, check if the mouse event occurs closely\n            // after the previous touch event.\n            if (Date.now() - this._lastTouchMs < TOUCH_BUFFER_MS) {\n                return;\n            }\n            // Fake mousedown events are fired by some screen readers when controls are activated by the\n            // screen reader. Attribute them to keyboard input modality.\n            this._modality.next(isFakeMousedownFromScreenReader(event) ? 'keyboard' : 'mouse');\n            this._mostRecentTarget = _getEventTarget(event);\n        };\n        /**\n         * Handles touchstart events. Must be an arrow function in order to preserve the context when it\n         * gets bound.\n         */\n        this._onTouchstart = (event) => {\n            // Same scenario as mentioned in _onMousedown, but on touch screen devices, fake touchstart\n            // events are fired. Again, attribute to keyboard input modality.\n            if (isFakeTouchstartFromScreenReader(event)) {\n                this._modality.next('keyboard');\n                return;\n            }\n            // Store the timestamp of this touch event, as it's used to distinguish between mouse events\n            // triggered via mouse vs touch.\n            this._lastTouchMs = Date.now();\n            this._modality.next('touch');\n            this._mostRecentTarget = _getEventTarget(event);\n        };\n        this._options = {\n            ...INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,\n            ...options,\n        };\n        // Skip the first emission as it's null.\n        this.modalityDetected = this._modality.pipe(skip(1));\n        this.modalityChanged = this.modalityDetected.pipe(distinctUntilChanged());\n        // If we're not in a browser, this service should do nothing, as there's no relevant input\n        // modality to detect.\n        if (_platform.isBrowser) {\n            ngZone.runOutsideAngular(() => {\n                document.addEventListener('keydown', this._onKeydown, modalityEventListenerOptions);\n                document.addEventListener('mousedown', this._onMousedown, modalityEventListenerOptions);\n                document.addEventListener('touchstart', this._onTouchstart, modalityEventListenerOptions);\n            });\n        }\n    }\n    ngOnDestroy() {\n        this._modality.complete();\n        if (this._platform.isBrowser) {\n            document.removeEventListener('keydown', this._onKeydown, modalityEventListenerOptions);\n            document.removeEventListener('mousedown', this._onMousedown, modalityEventListenerOptions);\n            document.removeEventListener('touchstart', this._onTouchstart, modalityEventListenerOptions);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: InputModalityDetector, deps: [{ token: i1.Platform }, { token: i0.NgZone }, { token: DOCUMENT }, { token: INPUT_MODALITY_DETECTOR_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: InputModalityDetector, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: InputModalityDetector, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: i1.Platform }, { type: i0.NgZone }, { type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [INPUT_MODALITY_DETECTOR_OPTIONS]\n                }] }] });\n\nconst LIVE_ANNOUNCER_ELEMENT_TOKEN = new InjectionToken('liveAnnouncerElement', {\n    providedIn: 'root',\n    factory: LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY,\n});\n/** @docs-private */\nfunction LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY() {\n    return null;\n}\n/** Injection token that can be used to configure the default options for the LiveAnnouncer. */\nconst LIVE_ANNOUNCER_DEFAULT_OPTIONS = new InjectionToken('LIVE_ANNOUNCER_DEFAULT_OPTIONS');\n\nlet uniqueIds = 0;\nclass LiveAnnouncer {\n    constructor(elementToken, _ngZone, _document, _defaultOptions) {\n        this._ngZone = _ngZone;\n        this._defaultOptions = _defaultOptions;\n        // We inject the live element and document as `any` because the constructor signature cannot\n        // reference browser globals (HTMLElement, Document) on non-browser environments, since having\n        // a class decorator causes TypeScript to preserve the constructor signature types.\n        this._document = _document;\n        this._liveElement = elementToken || this._createLiveElement();\n    }\n    announce(message, ...args) {\n        const defaultOptions = this._defaultOptions;\n        let politeness;\n        let duration;\n        if (args.length === 1 && typeof args[0] === 'number') {\n            duration = args[0];\n        }\n        else {\n            [politeness, duration] = args;\n        }\n        this.clear();\n        clearTimeout(this._previousTimeout);\n        if (!politeness) {\n            politeness =\n                defaultOptions && defaultOptions.politeness ? defaultOptions.politeness : 'polite';\n        }\n        if (duration == null && defaultOptions) {\n            duration = defaultOptions.duration;\n        }\n        // TODO: ensure changing the politeness works on all environments we support.\n        this._liveElement.setAttribute('aria-live', politeness);\n        if (this._liveElement.id) {\n            this._exposeAnnouncerToModals(this._liveElement.id);\n        }\n        // This 100ms timeout is necessary for some browser + screen-reader combinations:\n        // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n        // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n        //   second time without clearing and then using a non-zero delay.\n        // (using JAWS 17 at time of this writing).\n        return this._ngZone.runOutsideAngular(() => {\n            if (!this._currentPromise) {\n                this._currentPromise = new Promise(resolve => (this._currentResolve = resolve));\n            }\n            clearTimeout(this._previousTimeout);\n            this._previousTimeout = setTimeout(() => {\n                this._liveElement.textContent = message;\n                if (typeof duration === 'number') {\n                    this._previousTimeout = setTimeout(() => this.clear(), duration);\n                }\n                // For some reason in tests this can be undefined\n                // Probably related to ZoneJS and every other thing that patches browser APIs in tests\n                this._currentResolve?.();\n                this._currentPromise = this._currentResolve = undefined;\n            }, 100);\n            return this._currentPromise;\n        });\n    }\n    /**\n     * Clears the current text from the announcer element. Can be used to prevent\n     * screen readers from reading the text out again while the user is going\n     * through the page landmarks.\n     */\n    clear() {\n        if (this._liveElement) {\n            this._liveElement.textContent = '';\n        }\n    }\n    ngOnDestroy() {\n        clearTimeout(this._previousTimeout);\n        this._liveElement?.remove();\n        this._liveElement = null;\n        this._currentResolve?.();\n        this._currentPromise = this._currentResolve = undefined;\n    }\n    _createLiveElement() {\n        const elementClass = 'cdk-live-announcer-element';\n        const previousElements = this._document.getElementsByClassName(elementClass);\n        const liveEl = this._document.createElement('div');\n        // Remove any old containers. This can happen when coming in from a server-side-rendered page.\n        for (let i = 0; i < previousElements.length; i++) {\n            previousElements[i].remove();\n        }\n        liveEl.classList.add(elementClass);\n        liveEl.classList.add('cdk-visually-hidden');\n        liveEl.setAttribute('aria-atomic', 'true');\n        liveEl.setAttribute('aria-live', 'polite');\n        liveEl.id = `cdk-live-announcer-${uniqueIds++}`;\n        this._document.body.appendChild(liveEl);\n        return liveEl;\n    }\n    /**\n     * Some browsers won't expose the accessibility node of the live announcer element if there is an\n     * `aria-modal` and the live announcer is outside of it. This method works around the issue by\n     * pointing the `aria-owns` of all modals to the live announcer element.\n     */\n    _exposeAnnouncerToModals(id) {\n        // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n        // the `SnakBarContainer` and other usages.\n        //\n        // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n        // section of the DOM we need to look through. This should cover all the cases we support, but\n        // the selector can be expanded if it turns out to be too narrow.\n        const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n        for (let i = 0; i < modals.length; i++) {\n            const modal = modals[i];\n            const ariaOwns = modal.getAttribute('aria-owns');\n            if (!ariaOwns) {\n                modal.setAttribute('aria-owns', id);\n            }\n            else if (ariaOwns.indexOf(id) === -1) {\n                modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: LiveAnnouncer, deps: [{ token: LIVE_ANNOUNCER_ELEMENT_TOKEN, optional: true }, { token: i0.NgZone }, { token: DOCUMENT }, { token: LIVE_ANNOUNCER_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: LiveAnnouncer, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: LiveAnnouncer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [LIVE_ANNOUNCER_ELEMENT_TOKEN]\n                }] }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [LIVE_ANNOUNCER_DEFAULT_OPTIONS]\n                }] }] });\n/**\n * A directive that works similarly to aria-live, but uses the LiveAnnouncer to ensure compatibility\n * with a wider range of browsers and screen readers.\n */\nclass CdkAriaLive {\n    /** The aria-live politeness level to use when announcing messages. */\n    get politeness() {\n        return this._politeness;\n    }\n    set politeness(value) {\n        this._politeness = value === 'off' || value === 'assertive' ? value : 'polite';\n        if (this._politeness === 'off') {\n            if (this._subscription) {\n                this._subscription.unsubscribe();\n                this._subscription = null;\n            }\n        }\n        else if (!this._subscription) {\n            this._subscription = this._ngZone.runOutsideAngular(() => {\n                return this._contentObserver.observe(this._elementRef).subscribe(() => {\n                    // Note that we use textContent here, rather than innerText, in order to avoid a reflow.\n                    const elementText = this._elementRef.nativeElement.textContent;\n                    // The `MutationObserver` fires also for attribute\n                    // changes which we don't want to announce.\n                    if (elementText !== this._previousAnnouncedText) {\n                        this._liveAnnouncer.announce(elementText, this._politeness, this.duration);\n                        this._previousAnnouncedText = elementText;\n                    }\n                });\n            });\n        }\n    }\n    constructor(_elementRef, _liveAnnouncer, _contentObserver, _ngZone) {\n        this._elementRef = _elementRef;\n        this._liveAnnouncer = _liveAnnouncer;\n        this._contentObserver = _contentObserver;\n        this._ngZone = _ngZone;\n        this._politeness = 'polite';\n    }\n    ngOnDestroy() {\n        if (this._subscription) {\n            this._subscription.unsubscribe();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkAriaLive, deps: [{ token: i0.ElementRef }, { token: LiveAnnouncer }, { token: i1$1.ContentObserver }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkAriaLive, isStandalone: true, selector: \"[cdkAriaLive]\", inputs: { politeness: [\"cdkAriaLive\", \"politeness\"], duration: [\"cdkAriaLiveDuration\", \"duration\"] }, exportAs: [\"cdkAriaLive\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkAriaLive, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkAriaLive]',\n                    exportAs: 'cdkAriaLive',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: LiveAnnouncer }, { type: i1$1.ContentObserver }, { type: i0.NgZone }], propDecorators: { politeness: [{\n                type: Input,\n                args: ['cdkAriaLive']\n            }], duration: [{\n                type: Input,\n                args: ['cdkAriaLiveDuration']\n            }] } });\n\n/** Detection mode used for attributing the origin of a focus event. */\nvar FocusMonitorDetectionMode;\n(function (FocusMonitorDetectionMode) {\n    /**\n     * Any mousedown, keydown, or touchstart event that happened in the previous\n     * tick or the current tick will be used to assign a focus event's origin (to\n     * either mouse, keyboard, or touch). This is the default option.\n     */\n    FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"IMMEDIATE\"] = 0] = \"IMMEDIATE\";\n    /**\n     * A focus event's origin is always attributed to the last corresponding\n     * mousedown, keydown, or touchstart event, no matter how long ago it occurred.\n     */\n    FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"EVENTUAL\"] = 1] = \"EVENTUAL\";\n})(FocusMonitorDetectionMode || (FocusMonitorDetectionMode = {}));\n/** InjectionToken for FocusMonitorOptions. */\nconst FOCUS_MONITOR_DEFAULT_OPTIONS = new InjectionToken('cdk-focus-monitor-default-options');\n/**\n * Event listener options that enable capturing and also\n * mark the listener as passive if the browser supports it.\n */\nconst captureEventListenerOptions = normalizePassiveListenerOptions({\n    passive: true,\n    capture: true,\n});\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\nclass FocusMonitor {\n    constructor(_ngZone, _platform, _inputModalityDetector, \n    /** @breaking-change 11.0.0 make document required */\n    document, options) {\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        this._inputModalityDetector = _inputModalityDetector;\n        /** The focus origin that the next focus event is a result of. */\n        this._origin = null;\n        /** Whether the window has just been focused. */\n        this._windowFocused = false;\n        /**\n         * Whether the origin was determined via a touch interaction. Necessary as properly attributing\n         * focus events to touch interactions requires special logic.\n         */\n        this._originFromTouchInteraction = false;\n        /** Map of elements being monitored to their info. */\n        this._elementInfo = new Map();\n        /** The number of elements currently being monitored. */\n        this._monitoredElementCount = 0;\n        /**\n         * Keeps track of the root nodes to which we've currently bound a focus/blur handler,\n         * as well as the number of monitored elements that they contain. We have to treat focus/blur\n         * handlers differently from the rest of the events, because the browser won't emit events\n         * to the document when focus moves inside of a shadow root.\n         */\n        this._rootNodeFocusListenerCount = new Map();\n        /**\n         * Event listener for `focus` events on the window.\n         * Needs to be an arrow function in order to preserve the context when it gets bound.\n         */\n        this._windowFocusListener = () => {\n            // Make a note of when the window regains focus, so we can\n            // restore the origin info for the focused element.\n            this._windowFocused = true;\n            this._windowFocusTimeoutId = window.setTimeout(() => (this._windowFocused = false));\n        };\n        /** Subject for stopping our InputModalityDetector subscription. */\n        this._stopInputModalityDetector = new Subject();\n        /**\n         * Event listener for `focus` and 'blur' events on the document.\n         * Needs to be an arrow function in order to preserve the context when it gets bound.\n         */\n        this._rootNodeFocusAndBlurListener = (event) => {\n            const target = _getEventTarget(event);\n            // We need to walk up the ancestor chain in order to support `checkChildren`.\n            for (let element = target; element; element = element.parentElement) {\n                if (event.type === 'focus') {\n                    this._onFocus(event, element);\n                }\n                else {\n                    this._onBlur(event, element);\n                }\n            }\n        };\n        this._document = document;\n        this._detectionMode = options?.detectionMode || FocusMonitorDetectionMode.IMMEDIATE;\n    }\n    monitor(element, checkChildren = false) {\n        const nativeElement = coerceElement(element);\n        // Do nothing if we're not on the browser platform or the passed in node isn't an element.\n        if (!this._platform.isBrowser || nativeElement.nodeType !== 1) {\n            // Note: we don't want the observable to emit at all so we don't pass any parameters.\n            return of();\n        }\n        // If the element is inside the shadow DOM, we need to bind our focus/blur listeners to\n        // the shadow root, rather than the `document`, because the browser won't emit focus events\n        // to the `document`, if focus is moving within the same shadow root.\n        const rootNode = _getShadowRoot(nativeElement) || this._getDocument();\n        const cachedInfo = this._elementInfo.get(nativeElement);\n        // Check if we're already monitoring this element.\n        if (cachedInfo) {\n            if (checkChildren) {\n                // TODO(COMP-318): this can be problematic, because it'll turn all non-checkChildren\n                // observers into ones that behave as if `checkChildren` was turned on. We need a more\n                // robust solution.\n                cachedInfo.checkChildren = true;\n            }\n            return cachedInfo.subject;\n        }\n        // Create monitored element info.\n        const info = {\n            checkChildren: checkChildren,\n            subject: new Subject(),\n            rootNode,\n        };\n        this._elementInfo.set(nativeElement, info);\n        this._registerGlobalListeners(info);\n        return info.subject;\n    }\n    stopMonitoring(element) {\n        const nativeElement = coerceElement(element);\n        const elementInfo = this._elementInfo.get(nativeElement);\n        if (elementInfo) {\n            elementInfo.subject.complete();\n            this._setClasses(nativeElement);\n            this._elementInfo.delete(nativeElement);\n            this._removeGlobalListeners(elementInfo);\n        }\n    }\n    focusVia(element, origin, options) {\n        const nativeElement = coerceElement(element);\n        const focusedElement = this._getDocument().activeElement;\n        // If the element is focused already, calling `focus` again won't trigger the event listener\n        // which means that the focus classes won't be updated. If that's the case, update the classes\n        // directly without waiting for an event.\n        if (nativeElement === focusedElement) {\n            this._getClosestElementsInfo(nativeElement).forEach(([currentElement, info]) => this._originChanged(currentElement, origin, info));\n        }\n        else {\n            this._setOrigin(origin);\n            // `focus` isn't available on the server\n            if (typeof nativeElement.focus === 'function') {\n                nativeElement.focus(options);\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n    }\n    /** Access injected document if available or fallback to global document reference */\n    _getDocument() {\n        return this._document || document;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        const doc = this._getDocument();\n        return doc.defaultView || window;\n    }\n    _getFocusOrigin(focusEventTarget) {\n        if (this._origin) {\n            // If the origin was realized via a touch interaction, we need to perform additional checks\n            // to determine whether the focus origin should be attributed to touch or program.\n            if (this._originFromTouchInteraction) {\n                return this._shouldBeAttributedToTouch(focusEventTarget) ? 'touch' : 'program';\n            }\n            else {\n                return this._origin;\n            }\n        }\n        // If the window has just regained focus, we can restore the most recent origin from before the\n        // window blurred. Otherwise, we've reached the point where we can't identify the source of the\n        // focus. This typically means one of two things happened:\n        //\n        // 1) The element was programmatically focused, or\n        // 2) The element was focused via screen reader navigation (which generally doesn't fire\n        //    events).\n        //\n        // Because we can't distinguish between these two cases, we default to setting `program`.\n        if (this._windowFocused && this._lastFocusOrigin) {\n            return this._lastFocusOrigin;\n        }\n        // If the interaction is coming from an input label, we consider it a mouse interactions.\n        // This is a special case where focus moves on `click`, rather than `mousedown` which breaks\n        // our detection, because all our assumptions are for `mousedown`. We need to handle this\n        // special case, because it's very common for checkboxes and radio buttons.\n        if (focusEventTarget && this._isLastInteractionFromInputLabel(focusEventTarget)) {\n            return 'mouse';\n        }\n        return 'program';\n    }\n    /**\n     * Returns whether the focus event should be attributed to touch. Recall that in IMMEDIATE mode, a\n     * touch origin isn't immediately reset at the next tick (see _setOrigin). This means that when we\n     * handle a focus event following a touch interaction, we need to determine whether (1) the focus\n     * event was directly caused by the touch interaction or (2) the focus event was caused by a\n     * subsequent programmatic focus call triggered by the touch interaction.\n     * @param focusEventTarget The target of the focus event under examination.\n     */\n    _shouldBeAttributedToTouch(focusEventTarget) {\n        // Please note that this check is not perfect. Consider the following edge case:\n        //\n        // <div #parent tabindex=\"0\">\n        //   <div #child tabindex=\"0\" (click)=\"#parent.focus()\"></div>\n        // </div>\n        //\n        // Suppose there is a FocusMonitor in IMMEDIATE mode attached to #parent. When the user touches\n        // #child, #parent is programmatically focused. This code will attribute the focus to touch\n        // instead of program. This is a relatively minor edge-case that can be worked around by using\n        // focusVia(parent, 'program') to focus #parent.\n        return (this._detectionMode === FocusMonitorDetectionMode.EVENTUAL ||\n            !!focusEventTarget?.contains(this._inputModalityDetector._mostRecentTarget));\n    }\n    /**\n     * Sets the focus classes on the element based on the given focus origin.\n     * @param element The element to update the classes on.\n     * @param origin The focus origin.\n     */\n    _setClasses(element, origin) {\n        element.classList.toggle('cdk-focused', !!origin);\n        element.classList.toggle('cdk-touch-focused', origin === 'touch');\n        element.classList.toggle('cdk-keyboard-focused', origin === 'keyboard');\n        element.classList.toggle('cdk-mouse-focused', origin === 'mouse');\n        element.classList.toggle('cdk-program-focused', origin === 'program');\n    }\n    /**\n     * Updates the focus origin. If we're using immediate detection mode, we schedule an async\n     * function to clear the origin at the end of a timeout. The duration of the timeout depends on\n     * the origin being set.\n     * @param origin The origin to set.\n     * @param isFromInteraction Whether we are setting the origin from an interaction event.\n     */\n    _setOrigin(origin, isFromInteraction = false) {\n        this._ngZone.runOutsideAngular(() => {\n            this._origin = origin;\n            this._originFromTouchInteraction = origin === 'touch' && isFromInteraction;\n            // If we're in IMMEDIATE mode, reset the origin at the next tick (or in `TOUCH_BUFFER_MS` ms\n            // for a touch event). We reset the origin at the next tick because Firefox focuses one tick\n            // after the interaction event. We wait `TOUCH_BUFFER_MS` ms before resetting the origin for\n            // a touch event because when a touch event is fired, the associated focus event isn't yet in\n            // the event queue. Before doing so, clear any pending timeouts.\n            if (this._detectionMode === FocusMonitorDetectionMode.IMMEDIATE) {\n                clearTimeout(this._originTimeoutId);\n                const ms = this._originFromTouchInteraction ? TOUCH_BUFFER_MS : 1;\n                this._originTimeoutId = setTimeout(() => (this._origin = null), ms);\n            }\n        });\n    }\n    /**\n     * Handles focus events on a registered element.\n     * @param event The focus event.\n     * @param element The monitored element.\n     */\n    _onFocus(event, element) {\n        // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n        // focus event affecting the monitored element. If we want to use the origin of the first event\n        // instead we should check for the cdk-focused class here and return if the element already has\n        // it. (This only matters for elements that have includesChildren = true).\n        // If we are not counting child-element-focus as focused, make sure that the event target is the\n        // monitored element itself.\n        const elementInfo = this._elementInfo.get(element);\n        const focusEventTarget = _getEventTarget(event);\n        if (!elementInfo || (!elementInfo.checkChildren && element !== focusEventTarget)) {\n            return;\n        }\n        this._originChanged(element, this._getFocusOrigin(focusEventTarget), elementInfo);\n    }\n    /**\n     * Handles blur events on a registered element.\n     * @param event The blur event.\n     * @param element The monitored element.\n     */\n    _onBlur(event, element) {\n        // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n        // order to focus another child of the monitored element.\n        const elementInfo = this._elementInfo.get(element);\n        if (!elementInfo ||\n            (elementInfo.checkChildren &&\n                event.relatedTarget instanceof Node &&\n                element.contains(event.relatedTarget))) {\n            return;\n        }\n        this._setClasses(element);\n        this._emitOrigin(elementInfo, null);\n    }\n    _emitOrigin(info, origin) {\n        if (info.subject.observers.length) {\n            this._ngZone.run(() => info.subject.next(origin));\n        }\n    }\n    _registerGlobalListeners(elementInfo) {\n        if (!this._platform.isBrowser) {\n            return;\n        }\n        const rootNode = elementInfo.rootNode;\n        const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode) || 0;\n        if (!rootNodeFocusListeners) {\n            this._ngZone.runOutsideAngular(() => {\n                rootNode.addEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n                rootNode.addEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n            });\n        }\n        this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners + 1);\n        // Register global listeners when first element is monitored.\n        if (++this._monitoredElementCount === 1) {\n            // Note: we listen to events in the capture phase so we\n            // can detect them even if the user stops propagation.\n            this._ngZone.runOutsideAngular(() => {\n                const window = this._getWindow();\n                window.addEventListener('focus', this._windowFocusListener);\n            });\n            // The InputModalityDetector is also just a collection of global listeners.\n            this._inputModalityDetector.modalityDetected\n                .pipe(takeUntil(this._stopInputModalityDetector))\n                .subscribe(modality => {\n                this._setOrigin(modality, true /* isFromInteraction */);\n            });\n        }\n    }\n    _removeGlobalListeners(elementInfo) {\n        const rootNode = elementInfo.rootNode;\n        if (this._rootNodeFocusListenerCount.has(rootNode)) {\n            const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode);\n            if (rootNodeFocusListeners > 1) {\n                this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners - 1);\n            }\n            else {\n                rootNode.removeEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n                rootNode.removeEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n                this._rootNodeFocusListenerCount.delete(rootNode);\n            }\n        }\n        // Unregister global listeners when last element is unmonitored.\n        if (!--this._monitoredElementCount) {\n            const window = this._getWindow();\n            window.removeEventListener('focus', this._windowFocusListener);\n            // Equivalently, stop our InputModalityDetector subscription.\n            this._stopInputModalityDetector.next();\n            // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n            clearTimeout(this._windowFocusTimeoutId);\n            clearTimeout(this._originTimeoutId);\n        }\n    }\n    /** Updates all the state on an element once its focus origin has changed. */\n    _originChanged(element, origin, elementInfo) {\n        this._setClasses(element, origin);\n        this._emitOrigin(elementInfo, origin);\n        this._lastFocusOrigin = origin;\n    }\n    /**\n     * Collects the `MonitoredElementInfo` of a particular element and\n     * all of its ancestors that have enabled `checkChildren`.\n     * @param element Element from which to start the search.\n     */\n    _getClosestElementsInfo(element) {\n        const results = [];\n        this._elementInfo.forEach((info, currentElement) => {\n            if (currentElement === element || (info.checkChildren && currentElement.contains(element))) {\n                results.push([currentElement, info]);\n            }\n        });\n        return results;\n    }\n    /**\n     * Returns whether an interaction is likely to have come from the user clicking the `label` of\n     * an `input` or `textarea` in order to focus it.\n     * @param focusEventTarget Target currently receiving focus.\n     */\n    _isLastInteractionFromInputLabel(focusEventTarget) {\n        const { _mostRecentTarget: mostRecentTarget, mostRecentModality } = this._inputModalityDetector;\n        // If the last interaction used the mouse on an element contained by one of the labels\n        // of an `input`/`textarea` that is currently focused, it is very likely that the\n        // user redirected focus using the label.\n        if (mostRecentModality !== 'mouse' ||\n            !mostRecentTarget ||\n            mostRecentTarget === focusEventTarget ||\n            (focusEventTarget.nodeName !== 'INPUT' && focusEventTarget.nodeName !== 'TEXTAREA') ||\n            focusEventTarget.disabled) {\n            return false;\n        }\n        const labels = focusEventTarget.labels;\n        if (labels) {\n            for (let i = 0; i < labels.length; i++) {\n                if (labels[i].contains(mostRecentTarget)) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: FocusMonitor, deps: [{ token: i0.NgZone }, { token: i1.Platform }, { token: InputModalityDetector }, { token: DOCUMENT, optional: true }, { token: FOCUS_MONITOR_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: FocusMonitor, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: FocusMonitor, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: i0.NgZone }, { type: i1.Platform }, { type: InputModalityDetector }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [FOCUS_MONITOR_DEFAULT_OPTIONS]\n                }] }] });\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\nclass CdkMonitorFocus {\n    constructor(_elementRef, _focusMonitor) {\n        this._elementRef = _elementRef;\n        this._focusMonitor = _focusMonitor;\n        this._focusOrigin = null;\n        this.cdkFocusChange = new EventEmitter();\n    }\n    get focusOrigin() {\n        return this._focusOrigin;\n    }\n    ngAfterViewInit() {\n        const element = this._elementRef.nativeElement;\n        this._monitorSubscription = this._focusMonitor\n            .monitor(element, element.nodeType === 1 && element.hasAttribute('cdkMonitorSubtreeFocus'))\n            .subscribe(origin => {\n            this._focusOrigin = origin;\n            this.cdkFocusChange.emit(origin);\n        });\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this._elementRef);\n        if (this._monitorSubscription) {\n            this._monitorSubscription.unsubscribe();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkMonitorFocus, deps: [{ token: i0.ElementRef }, { token: FocusMonitor }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkMonitorFocus, isStandalone: true, selector: \"[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]\", outputs: { cdkFocusChange: \"cdkFocusChange\" }, exportAs: [\"cdkMonitorFocus\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkMonitorFocus, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]',\n                    exportAs: 'cdkMonitorFocus',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: FocusMonitor }], propDecorators: { cdkFocusChange: [{\n                type: Output\n            }] } });\n\n/** Set of possible high-contrast mode backgrounds. */\nvar HighContrastMode;\n(function (HighContrastMode) {\n    HighContrastMode[HighContrastMode[\"NONE\"] = 0] = \"NONE\";\n    HighContrastMode[HighContrastMode[\"BLACK_ON_WHITE\"] = 1] = \"BLACK_ON_WHITE\";\n    HighContrastMode[HighContrastMode[\"WHITE_ON_BLACK\"] = 2] = \"WHITE_ON_BLACK\";\n})(HighContrastMode || (HighContrastMode = {}));\n/** CSS class applied to the document body when in black-on-white high-contrast mode. */\nconst BLACK_ON_WHITE_CSS_CLASS = 'cdk-high-contrast-black-on-white';\n/** CSS class applied to the document body when in white-on-black high-contrast mode. */\nconst WHITE_ON_BLACK_CSS_CLASS = 'cdk-high-contrast-white-on-black';\n/** CSS class applied to the document body when in high-contrast mode. */\nconst HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS = 'cdk-high-contrast-active';\n/**\n * Service to determine whether the browser is currently in a high-contrast-mode environment.\n *\n * Microsoft Windows supports an accessibility feature called \"High Contrast Mode\". This mode\n * changes the appearance of all applications, including web applications, to dramatically increase\n * contrast.\n *\n * IE, Edge, and Firefox currently support this mode. Chrome does not support Windows High Contrast\n * Mode. This service does not detect high-contrast mode as added by the Chrome \"High Contrast\"\n * browser extension.\n */\nclass HighContrastModeDetector {\n    constructor(_platform, document) {\n        this._platform = _platform;\n        this._document = document;\n        this._breakpointSubscription = inject(BreakpointObserver)\n            .observe('(forced-colors: active)')\n            .subscribe(() => {\n            if (this._hasCheckedHighContrastMode) {\n                this._hasCheckedHighContrastMode = false;\n                this._applyBodyHighContrastModeCssClasses();\n            }\n        });\n    }\n    /** Gets the current high-contrast-mode for the page. */\n    getHighContrastMode() {\n        if (!this._platform.isBrowser) {\n            return HighContrastMode.NONE;\n        }\n        // Create a test element with an arbitrary background-color that is neither black nor\n        // white; high-contrast mode will coerce the color to either black or white. Also ensure that\n        // appending the test element to the DOM does not affect layout by absolutely positioning it\n        const testElement = this._document.createElement('div');\n        testElement.style.backgroundColor = 'rgb(1,2,3)';\n        testElement.style.position = 'absolute';\n        this._document.body.appendChild(testElement);\n        // Get the computed style for the background color, collapsing spaces to normalize between\n        // browsers. Once we get this color, we no longer need the test element. Access the `window`\n        // via the document so we can fake it in tests. Note that we have extra null checks, because\n        // this logic will likely run during app bootstrap and throwing can break the entire app.\n        const documentWindow = this._document.defaultView || window;\n        const computedStyle = documentWindow && documentWindow.getComputedStyle\n            ? documentWindow.getComputedStyle(testElement)\n            : null;\n        const computedColor = ((computedStyle && computedStyle.backgroundColor) || '').replace(/ /g, '');\n        testElement.remove();\n        switch (computedColor) {\n            // Pre Windows 11 dark theme.\n            case 'rgb(0,0,0)':\n            // Windows 11 dark themes.\n            case 'rgb(45,50,54)':\n            case 'rgb(32,32,32)':\n                return HighContrastMode.WHITE_ON_BLACK;\n            // Pre Windows 11 light theme.\n            case 'rgb(255,255,255)':\n            // Windows 11 light theme.\n            case 'rgb(255,250,239)':\n                return HighContrastMode.BLACK_ON_WHITE;\n        }\n        return HighContrastMode.NONE;\n    }\n    ngOnDestroy() {\n        this._breakpointSubscription.unsubscribe();\n    }\n    /** Applies CSS classes indicating high-contrast mode to document body (browser-only). */\n    _applyBodyHighContrastModeCssClasses() {\n        if (!this._hasCheckedHighContrastMode && this._platform.isBrowser && this._document.body) {\n            const bodyClasses = this._document.body.classList;\n            bodyClasses.remove(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n            this._hasCheckedHighContrastMode = true;\n            const mode = this.getHighContrastMode();\n            if (mode === HighContrastMode.BLACK_ON_WHITE) {\n                bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS);\n            }\n            else if (mode === HighContrastMode.WHITE_ON_BLACK) {\n                bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: HighContrastModeDetector, deps: [{ token: i1.Platform }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: HighContrastModeDetector, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: HighContrastModeDetector, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: i1.Platform }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\nclass A11yModule {\n    constructor(highContrastModeDetector) {\n        highContrastModeDetector._applyBodyHighContrastModeCssClasses();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: A11yModule, deps: [{ token: HighContrastModeDetector }], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: A11yModule, imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus], exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: A11yModule, imports: [ObserversModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: A11yModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n                    exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n                }]\n        }], ctorParameters: () => [{ type: HighContrastModeDetector }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { A11yModule, ActiveDescendantKeyManager, AriaDescriber, CDK_DESCRIBEDBY_HOST_ATTRIBUTE, CDK_DESCRIBEDBY_ID_PREFIX, CdkAriaLive, CdkMonitorFocus, CdkTrapFocus, ConfigurableFocusTrap, ConfigurableFocusTrapFactory, EventListenerFocusTrapInertStrategy, FOCUS_MONITOR_DEFAULT_OPTIONS, FOCUS_TRAP_INERT_STRATEGY, FocusKeyManager, FocusMonitor, FocusMonitorDetectionMode, FocusTrap, FocusTrapFactory, HighContrastMode, HighContrastModeDetector, INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS, INPUT_MODALITY_DETECTOR_OPTIONS, InputModalityDetector, InteractivityChecker, IsFocusableConfig, LIVE_ANNOUNCER_DEFAULT_OPTIONS, LIVE_ANNOUNCER_ELEMENT_TOKEN, LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY, ListKeyManager, LiveAnnouncer, MESSAGES_CONTAINER_ID, addAriaReferencedId, getAriaReferenceIds, isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader, removeAriaReferencedId };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAC7L,OAAO,KAAKC,EAAE,MAAM,uBAAuB;AAC3C,SAASC,QAAQ,EAAEC,iCAAiC,EAAEC,+BAA+B,EAAEC,eAAe,EAAEC,cAAc,QAAQ,uBAAuB;AACrJ,SAASC,OAAO,EAAEC,YAAY,EAAEC,eAAe,EAAEC,EAAE,QAAQ,MAAM;AACjE,SAASC,cAAc,EAAEC,CAAC,EAAEC,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAEC,SAAS,EAAEC,OAAO,EAAEC,GAAG,EAAEC,IAAI,EAAEC,UAAU,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,GAAG,EAAEC,GAAG,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,KAAK,QAAQ,uBAAuB;AAChM,SAASC,GAAG,EAAEC,YAAY,EAAEC,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,oBAAoB,EAAEC,SAAS,QAAQ,gBAAgB;AAC5G,OAAO,KAAKC,IAAI,MAAM,wBAAwB;AAC9C,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,kBAAkB,QAAQ,qBAAqB;;AAExD;AACA,MAAMC,YAAY,GAAG,GAAG;AACxB;AACA;AACA;AACA;AACA,SAASC,mBAAmBA,CAACC,EAAE,EAAEC,IAAI,EAAEC,EAAE,EAAE;EACvC,MAAMC,GAAG,GAAGC,mBAAmB,CAACJ,EAAE,EAAEC,IAAI,CAAC;EACzCC,EAAE,GAAGA,EAAE,CAACG,IAAI,CAAC,CAAC;EACd,IAAIF,GAAG,CAACG,IAAI,CAACC,UAAU,IAAIA,UAAU,CAACF,IAAI,CAAC,CAAC,KAAKH,EAAE,CAAC,EAAE;IAClD;EACJ;EACAC,GAAG,CAACK,IAAI,CAACN,EAAE,CAAC;EACZF,EAAE,CAACS,YAAY,CAACR,IAAI,EAAEE,GAAG,CAACO,IAAI,CAACZ,YAAY,CAAC,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA,SAASa,sBAAsBA,CAACX,EAAE,EAAEC,IAAI,EAAEC,EAAE,EAAE;EAC1C,MAAMC,GAAG,GAAGC,mBAAmB,CAACJ,EAAE,EAAEC,IAAI,CAAC;EACzCC,EAAE,GAAGA,EAAE,CAACG,IAAI,CAAC,CAAC;EACd,MAAMO,WAAW,GAAGT,GAAG,CAACf,MAAM,CAACyB,GAAG,IAAIA,GAAG,KAAKX,EAAE,CAAC;EACjD,IAAIU,WAAW,CAACE,MAAM,EAAE;IACpBd,EAAE,CAACS,YAAY,CAACR,IAAI,EAAEW,WAAW,CAACF,IAAI,CAACZ,YAAY,CAAC,CAAC;EACzD,CAAC,MACI;IACDE,EAAE,CAACe,eAAe,CAACd,IAAI,CAAC;EAC5B;AACJ;AACA;AACA;AACA;AACA;AACA,SAASG,mBAAmBA,CAACJ,EAAE,EAAEC,IAAI,EAAE;EACnC;EACA,MAAMe,SAAS,GAAGhB,EAAE,CAACiB,YAAY,CAAChB,IAAI,CAAC;EACvC,OAAOe,SAAS,EAAEE,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAG,mCAAmC;AACjE;AACA;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,GAAG,yBAAyB;AAC3D;AACA;AACA;AACA;AACA;AACA,MAAMC,8BAA8B,GAAG,sBAAsB;AAC7D;AACA,IAAIC,MAAM,GAAG,CAAC;AACd;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChBC,WAAWA,CAACC,SAAS;EACrB;AACJ;AACA;AACA;EACIC,SAAS,EAAE;IACP,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAACC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACjC;IACA,IAAI,CAACC,kBAAkB,GAAG,IAAI;IAC9B;IACA,IAAI,CAACC,GAAG,GAAG,GAAGR,MAAM,EAAE,EAAE;IACxB,IAAI,CAACG,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACK,GAAG,GAAGxF,MAAM,CAACC,MAAM,CAAC,GAAG,GAAG,GAAG+E,MAAM,EAAE;EAC9C;EACAS,QAAQA,CAACC,WAAW,EAAEC,OAAO,EAAEC,IAAI,EAAE;IACjC,IAAI,CAAC,IAAI,CAACC,eAAe,CAACH,WAAW,EAAEC,OAAO,CAAC,EAAE;MAC7C;IACJ;IACA,MAAMG,GAAG,GAAGC,MAAM,CAACJ,OAAO,EAAEC,IAAI,CAAC;IACjC,IAAI,OAAOD,OAAO,KAAK,QAAQ,EAAE;MAC7B;MACAK,YAAY,CAACL,OAAO,EAAE,IAAI,CAACH,GAAG,CAAC;MAC/B,IAAI,CAACH,gBAAgB,CAACY,GAAG,CAACH,GAAG,EAAE;QAAEI,cAAc,EAAEP,OAAO;QAAEQ,cAAc,EAAE;MAAE,CAAC,CAAC;IAClF,CAAC,MACI,IAAI,CAAC,IAAI,CAACd,gBAAgB,CAACe,GAAG,CAACN,GAAG,CAAC,EAAE;MACtC,IAAI,CAACO,qBAAqB,CAACV,OAAO,EAAEC,IAAI,CAAC;IAC7C;IACA,IAAI,CAAC,IAAI,CAACU,4BAA4B,CAACZ,WAAW,EAAEI,GAAG,CAAC,EAAE;MACtD,IAAI,CAACS,oBAAoB,CAACb,WAAW,EAAEI,GAAG,CAAC;IAC/C;EACJ;EACAU,iBAAiBA,CAACd,WAAW,EAAEC,OAAO,EAAEC,IAAI,EAAE;IAC1C,IAAI,CAACD,OAAO,IAAI,CAAC,IAAI,CAACc,cAAc,CAACf,WAAW,CAAC,EAAE;MAC/C;IACJ;IACA,MAAMI,GAAG,GAAGC,MAAM,CAACJ,OAAO,EAAEC,IAAI,CAAC;IACjC,IAAI,IAAI,CAACU,4BAA4B,CAACZ,WAAW,EAAEI,GAAG,CAAC,EAAE;MACrD,IAAI,CAACY,uBAAuB,CAAChB,WAAW,EAAEI,GAAG,CAAC;IAClD;IACA;IACA;IACA,IAAI,OAAOH,OAAO,KAAK,QAAQ,EAAE;MAC7B,MAAMgB,iBAAiB,GAAG,IAAI,CAACtB,gBAAgB,CAACuB,GAAG,CAACd,GAAG,CAAC;MACxD,IAAIa,iBAAiB,IAAIA,iBAAiB,CAACR,cAAc,KAAK,CAAC,EAAE;QAC7D,IAAI,CAACU,qBAAqB,CAACf,GAAG,CAAC;MACnC;IACJ;IACA,IAAI,IAAI,CAACP,kBAAkB,EAAEuB,UAAU,CAACtC,MAAM,KAAK,CAAC,EAAE;MAClD,IAAI,CAACe,kBAAkB,CAACwB,MAAM,CAAC,CAAC;MAChC,IAAI,CAACxB,kBAAkB,GAAG,IAAI;IAClC;EACJ;EACA;EACAyB,WAAWA,CAAA,EAAG;IACV,MAAMC,iBAAiB,GAAG,IAAI,CAAC9B,SAAS,CAAC+B,gBAAgB,CAAC,IAAInC,8BAA8B,KAAK,IAAI,CAACS,GAAG,IAAI,CAAC;IAC9G,KAAK,IAAI2B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,iBAAiB,CAACzC,MAAM,EAAE2C,CAAC,EAAE,EAAE;MAC/C,IAAI,CAACC,iCAAiC,CAACH,iBAAiB,CAACE,CAAC,CAAC,CAAC;MAC5DF,iBAAiB,CAACE,CAAC,CAAC,CAAC1C,eAAe,CAACM,8BAA8B,CAAC;IACxE;IACA,IAAI,CAACQ,kBAAkB,EAAEwB,MAAM,CAAC,CAAC;IACjC,IAAI,CAACxB,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACF,gBAAgB,CAACgC,KAAK,CAAC,CAAC;EACjC;EACA;AACJ;AACA;AACA;EACIhB,qBAAqBA,CAACV,OAAO,EAAEC,IAAI,EAAE;IACjC,MAAMM,cAAc,GAAG,IAAI,CAACf,SAAS,CAACmC,aAAa,CAAC,KAAK,CAAC;IAC1DtB,YAAY,CAACE,cAAc,EAAE,IAAI,CAACV,GAAG,CAAC;IACtCU,cAAc,CAACqB,WAAW,GAAG5B,OAAO;IACpC,IAAIC,IAAI,EAAE;MACNM,cAAc,CAAC/B,YAAY,CAAC,MAAM,EAAEyB,IAAI,CAAC;IAC7C;IACA,IAAI,CAAC4B,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAACjC,kBAAkB,CAACkC,WAAW,CAACvB,cAAc,CAAC;IACnD,IAAI,CAACb,gBAAgB,CAACY,GAAG,CAACF,MAAM,CAACJ,OAAO,EAAEC,IAAI,CAAC,EAAE;MAAEM,cAAc;MAAEC,cAAc,EAAE;IAAE,CAAC,CAAC;EAC3F;EACA;EACAU,qBAAqBA,CAACf,GAAG,EAAE;IACvB,IAAI,CAACT,gBAAgB,CAACuB,GAAG,CAACd,GAAG,CAAC,EAAEI,cAAc,EAAEa,MAAM,CAAC,CAAC;IACxD,IAAI,CAAC1B,gBAAgB,CAACqC,MAAM,CAAC5B,GAAG,CAAC;EACrC;EACA;EACA0B,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACjC,kBAAkB,EAAE;MACzB;IACJ;IACA,MAAMoC,kBAAkB,GAAG,mCAAmC;IAC9D,MAAMC,gBAAgB,GAAG,IAAI,CAACzC,SAAS,CAAC+B,gBAAgB,CAAC,IAAIS,kBAAkB,qBAAqB,CAAC;IACrG,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,gBAAgB,CAACpD,MAAM,EAAE2C,CAAC,EAAE,EAAE;MAC9C;MACA;MACA;MACA;MACAS,gBAAgB,CAACT,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC;IAChC;IACA,MAAMc,iBAAiB,GAAG,IAAI,CAAC1C,SAAS,CAACmC,aAAa,CAAC,KAAK,CAAC;IAC7D;IACA;IACA;IACA;IACAO,iBAAiB,CAACC,KAAK,CAACC,UAAU,GAAG,QAAQ;IAC7C;IACA;IACAF,iBAAiB,CAACG,SAAS,CAACC,GAAG,CAACN,kBAAkB,CAAC;IACnDE,iBAAiB,CAACG,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;IACtD;IACA,IAAI,IAAI,CAAC7C,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAAC8C,SAAS,EAAE;MAC7CL,iBAAiB,CAAC1D,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC;IACxD;IACA,IAAI,CAACgB,SAAS,CAACgD,IAAI,CAACV,WAAW,CAACI,iBAAiB,CAAC;IAClD,IAAI,CAACtC,kBAAkB,GAAGsC,iBAAiB;EAC/C;EACA;EACAT,iCAAiCA,CAACgB,OAAO,EAAE;IACvC;IACA,MAAMC,oBAAoB,GAAGvE,mBAAmB,CAACsE,OAAO,EAAE,kBAAkB,CAAC,CAACtF,MAAM,CAACc,EAAE,IAAIA,EAAE,CAAC0E,OAAO,CAACxD,yBAAyB,CAAC,IAAI,CAAC,CAAC;IACtIsD,OAAO,CAACjE,YAAY,CAAC,kBAAkB,EAAEkE,oBAAoB,CAACjE,IAAI,CAAC,GAAG,CAAC,CAAC;EAC5E;EACA;AACJ;AACA;AACA;EACImC,oBAAoBA,CAAC6B,OAAO,EAAEtC,GAAG,EAAE;IAC/B,MAAMa,iBAAiB,GAAG,IAAI,CAACtB,gBAAgB,CAACuB,GAAG,CAACd,GAAG,CAAC;IACxD;IACA;IACArC,mBAAmB,CAAC2E,OAAO,EAAE,kBAAkB,EAAEzB,iBAAiB,CAACT,cAAc,CAACtC,EAAE,CAAC;IACrFwE,OAAO,CAACjE,YAAY,CAACY,8BAA8B,EAAE,IAAI,CAACS,GAAG,CAAC;IAC9DmB,iBAAiB,CAACR,cAAc,EAAE;EACtC;EACA;AACJ;AACA;AACA;EACIO,uBAAuBA,CAAC0B,OAAO,EAAEtC,GAAG,EAAE;IAClC,MAAMa,iBAAiB,GAAG,IAAI,CAACtB,gBAAgB,CAACuB,GAAG,CAACd,GAAG,CAAC;IACxDa,iBAAiB,CAACR,cAAc,EAAE;IAClC9B,sBAAsB,CAAC+D,OAAO,EAAE,kBAAkB,EAAEzB,iBAAiB,CAACT,cAAc,CAACtC,EAAE,CAAC;IACxFwE,OAAO,CAAC3D,eAAe,CAACM,8BAA8B,CAAC;EAC3D;EACA;EACAuB,4BAA4BA,CAAC8B,OAAO,EAAEtC,GAAG,EAAE;IACvC,MAAMyC,YAAY,GAAGzE,mBAAmB,CAACsE,OAAO,EAAE,kBAAkB,CAAC;IACrE,MAAMzB,iBAAiB,GAAG,IAAI,CAACtB,gBAAgB,CAACuB,GAAG,CAACd,GAAG,CAAC;IACxD,MAAM0C,SAAS,GAAG7B,iBAAiB,IAAIA,iBAAiB,CAACT,cAAc,CAACtC,EAAE;IAC1E,OAAO,CAAC,CAAC4E,SAAS,IAAID,YAAY,CAACD,OAAO,CAACE,SAAS,CAAC,IAAI,CAAC,CAAC;EAC/D;EACA;EACA3C,eAAeA,CAACuC,OAAO,EAAEzC,OAAO,EAAE;IAC9B,IAAI,CAAC,IAAI,CAACc,cAAc,CAAC2B,OAAO,CAAC,EAAE;MAC/B,OAAO,KAAK;IAChB;IACA,IAAIzC,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MACxC;MACA;MACA;MACA,OAAO,IAAI;IACf;IACA,MAAM8C,cAAc,GAAG9C,OAAO,IAAI,IAAI,GAAG,EAAE,GAAG,GAAGA,OAAO,EAAE,CAAC5B,IAAI,CAAC,CAAC;IACjE,MAAM2E,SAAS,GAAGN,OAAO,CAACzD,YAAY,CAAC,YAAY,CAAC;IACpD;IACA;IACA,OAAO8D,cAAc,GAAG,CAACC,SAAS,IAAIA,SAAS,CAAC3E,IAAI,CAAC,CAAC,KAAK0E,cAAc,GAAG,KAAK;EACrF;EACA;EACAhC,cAAcA,CAAC2B,OAAO,EAAE;IACpB,OAAOA,OAAO,CAACO,QAAQ,KAAK,IAAI,CAACxD,SAAS,CAACyD,YAAY;EAC3D;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,sBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwF9D,aAAa,EAAvBlF,EAAE,CAAAiJ,QAAA,CAAuClJ,QAAQ,GAAjDC,EAAE,CAAAiJ,QAAA,CAA4DjI,EAAE,CAACC,QAAQ;IAAA,CAA6C;EAAE;EACxN;IAAS,IAAI,CAACiI,KAAK,kBAD6ElJ,EAAE,CAAAmJ,kBAAA;MAAAC,KAAA,EACYlE,aAAa;MAAAmE,OAAA,EAAbnE,aAAa,CAAA4D,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AACxJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGvJ,EAAE,CAAAwJ,iBAAA,CAGXtE,aAAa,EAAc,CAAC;IAC3GuE,IAAI,EAAEtJ,UAAU;IAChBuJ,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CH,IAAI,EAAErJ,MAAM;MACZsJ,IAAI,EAAE,CAAC3J,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE0J,IAAI,EAAEzI,EAAE,CAACC;EAAS,CAAC,CAAC;AAAA;AAC5C;AACA,SAAS+E,MAAMA,CAACJ,OAAO,EAAEC,IAAI,EAAE;EAC3B,OAAO,OAAOD,OAAO,KAAK,QAAQ,GAAG,GAAGC,IAAI,IAAI,EAAE,IAAID,OAAO,EAAE,GAAGA,OAAO;AAC7E;AACA;AACA,SAASK,YAAYA,CAACoC,OAAO,EAAEwB,SAAS,EAAE;EACtC,IAAI,CAACxB,OAAO,CAACxE,EAAE,EAAE;IACbwE,OAAO,CAACxE,EAAE,GAAG,GAAGkB,yBAAyB,IAAI8E,SAAS,IAAI5E,MAAM,EAAE,EAAE;EACxE;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAM6E,cAAc,CAAC;EACjB3E,WAAWA,CAAC4E,MAAM,EAAEC,QAAQ,EAAE;IAC1B,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACC,gBAAgB,GAAG,IAAI9I,OAAO,CAAC,CAAC;IACrC,IAAI,CAAC+I,sBAAsB,GAAG9I,YAAY,CAAC+I,KAAK;IAChD,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,cAAc,GAAG;MAAEC,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAG,CAAC;IACnD;AACR;AACA;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAIC,IAAI,IAAKA,IAAI,CAACC,QAAQ;IAC/C;IACA,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB;AACR;AACA;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,IAAI3J,OAAO,CAAC,CAAC;IAC3B;IACA,IAAI,CAAC4J,MAAM,GAAG,IAAI5J,OAAO,CAAC,CAAC;IAC3B;IACA;IACA;IACA,IAAIyI,MAAM,YAAY1J,SAAS,EAAE;MAC7B,IAAI,CAAC8K,wBAAwB,GAAGpB,MAAM,CAACqB,OAAO,CAACC,SAAS,CAAEC,QAAQ,IAAK,IAAI,CAACC,aAAa,CAACD,QAAQ,CAACE,OAAO,CAAC,CAAC,CAAC,CAAC;IAClH,CAAC,MACI,IAAIlL,QAAQ,CAACyJ,MAAM,CAAC,EAAE;MACvB,IAAI,CAACC,QAAQ,KAAK,OAAOT,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC9D,MAAM,IAAIkC,KAAK,CAAC,mEAAmE,CAAC;MACxF;MACA,IAAI,CAACC,UAAU,GAAGnL,MAAM,CAAC,MAAM,IAAI,CAACgL,aAAa,CAACxB,MAAM,CAAC,CAAC,CAAC,EAAE;QAAEC;MAAS,CAAC,CAAC;IAC9E;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI2B,aAAaA,CAACC,SAAS,EAAE;IACrB,IAAI,CAACf,gBAAgB,GAAGe,SAAS;IACjC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIC,QAAQA,CAACC,UAAU,GAAG,IAAI,EAAE;IACxB,IAAI,CAAC3B,KAAK,GAAG2B,UAAU;IACvB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIC,uBAAuBA,CAACpB,OAAO,GAAG,IAAI,EAAE;IACpC,IAAI,CAACJ,SAAS,GAAGI,OAAO;IACxB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIqB,yBAAyBA,CAACC,SAAS,EAAE;IACjC,IAAI,CAACC,WAAW,GAAGD,SAAS;IAC5B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIE,uBAAuBA,CAACC,IAAI,EAAE;IAC1B,IAAI,CAAC5B,oBAAoB,GAAG4B,IAAI;IAChC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIC,aAAaA,CAACC,gBAAgB,GAAG,GAAG,EAAE;IAClC,IAAI,OAAO/C,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,MAAMgD,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACnC,IAAID,KAAK,CAAC9H,MAAM,GAAG,CAAC,IAAI8H,KAAK,CAACtI,IAAI,CAAC6G,IAAI,IAAI,OAAOA,IAAI,CAAC2B,QAAQ,KAAK,UAAU,CAAC,EAAE;QAC7E,MAAMhB,KAAK,CAAC,8EAA8E,CAAC;MAC/F;IACJ;IACA,IAAI,CAACpB,sBAAsB,CAACqC,WAAW,CAAC,CAAC;IACzC;IACA;IACA;IACA,IAAI,CAACrC,sBAAsB,GAAG,IAAI,CAACD,gBAAgB,CAC9CuC,IAAI,CAAC9J,GAAG,CAAC+J,MAAM,IAAI,IAAI,CAAC5B,eAAe,CAAC7G,IAAI,CAACyI,MAAM,CAAC,CAAC,EAAE9J,YAAY,CAACwJ,gBAAgB,CAAC,EAAEvJ,MAAM,CAAC,MAAM,IAAI,CAACiI,eAAe,CAACvG,MAAM,GAAG,CAAC,CAAC,EAAEzB,GAAG,CAAC,MAAM,IAAI,CAACgI,eAAe,CAAC3G,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAC/KgH,SAAS,CAACwB,WAAW,IAAI;MAC1B,MAAMN,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACnC;MACA;MACA,KAAK,IAAIpF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmF,KAAK,CAAC9H,MAAM,GAAG,CAAC,EAAE2C,CAAC,EAAE,EAAE;QACvC,MAAM0F,KAAK,GAAG,CAAC,IAAI,CAAC7C,gBAAgB,GAAG7C,CAAC,IAAImF,KAAK,CAAC9H,MAAM;QACxD,MAAMqG,IAAI,GAAGyB,KAAK,CAACO,KAAK,CAAC;QACzB,IAAI,CAAC,IAAI,CAACjC,gBAAgB,CAACC,IAAI,CAAC,IAC5BA,IAAI,CAAC2B,QAAQ,CAAC,CAAC,CAACM,WAAW,CAAC,CAAC,CAAC/I,IAAI,CAAC,CAAC,CAACuE,OAAO,CAACsE,WAAW,CAAC,KAAK,CAAC,EAAE;UACjE,IAAI,CAACG,aAAa,CAACF,KAAK,CAAC;UACzB;QACJ;MACJ;MACA,IAAI,CAAC9B,eAAe,GAAG,EAAE;IAC7B,CAAC,CAAC;IACF,OAAO,IAAI;EACf;EACA;EACAiC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACjC,eAAe,GAAG,EAAE;IACzB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIkC,cAAcA,CAACvC,OAAO,GAAG,IAAI,EAAE;IAC3B,IAAI,CAACF,WAAW,GAAGE,OAAO;IAC1B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIwC,cAAcA,CAACxC,OAAO,GAAG,IAAI,EAAEC,KAAK,GAAG,EAAE,EAAE;IACvC,IAAI,CAACF,cAAc,GAAG;MAAEC,OAAO;MAAEC;IAAM,CAAC;IACxC,OAAO,IAAI;EACf;EACAoC,aAAaA,CAAClC,IAAI,EAAE;IAChB,MAAMsC,kBAAkB,GAAG,IAAI,CAAClD,WAAW;IAC3C,IAAI,CAACmD,gBAAgB,CAACvC,IAAI,CAAC;IAC3B,IAAI,IAAI,CAACZ,WAAW,KAAKkD,kBAAkB,EAAE;MACzC,IAAI,CAAClC,MAAM,CAACoC,IAAI,CAAC,IAAI,CAACrD,gBAAgB,CAAC;IAC3C;EACJ;EACA;AACJ;AACA;AACA;EACIsD,SAASA,CAACC,KAAK,EAAE;IACb,MAAMC,OAAO,GAAGD,KAAK,CAACC,OAAO;IAC7B,MAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC;IAC9D,MAAMC,iBAAiB,GAAGD,SAAS,CAACE,KAAK,CAACC,QAAQ,IAAI;MAClD,OAAO,CAACL,KAAK,CAACK,QAAQ,CAAC,IAAI,IAAI,CAACrD,oBAAoB,CAACjC,OAAO,CAACsF,QAAQ,CAAC,GAAG,CAAC,CAAC;IAC/E,CAAC,CAAC;IACF,QAAQJ,OAAO;MACX,KAAKlL,GAAG;QACJ,IAAI,CAAC0I,MAAM,CAACqC,IAAI,CAAC,CAAC;QAClB;MACJ,KAAKhL,UAAU;QACX,IAAI,IAAI,CAACiI,SAAS,IAAIoD,iBAAiB,EAAE;UACrC,IAAI,CAACG,iBAAiB,CAAC,CAAC;UACxB;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAKzL,QAAQ;QACT,IAAI,IAAI,CAACkI,SAAS,IAAIoD,iBAAiB,EAAE;UACrC,IAAI,CAACI,qBAAqB,CAAC,CAAC;UAC5B;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAK3L,WAAW;QACZ,IAAI,IAAI,CAAC8J,WAAW,IAAIyB,iBAAiB,EAAE;UACvC,IAAI,CAACzB,WAAW,KAAK,KAAK,GAAG,IAAI,CAAC6B,qBAAqB,CAAC,CAAC,GAAG,IAAI,CAACD,iBAAiB,CAAC,CAAC;UACpF;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAK3L,UAAU;QACX,IAAI,IAAI,CAAC+J,WAAW,IAAIyB,iBAAiB,EAAE;UACvC,IAAI,CAACzB,WAAW,KAAK,KAAK,GAAG,IAAI,CAAC4B,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;UACpF;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAK7L,IAAI;QACL,IAAI,IAAI,CAACuI,WAAW,IAAIkD,iBAAiB,EAAE;UACvC,IAAI,CAACK,kBAAkB,CAAC,CAAC;UACzB;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAK/L,GAAG;QACJ,IAAI,IAAI,CAACwI,WAAW,IAAIkD,iBAAiB,EAAE;UACvC,IAAI,CAACM,iBAAiB,CAAC,CAAC;UACxB;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAKjM,OAAO;QACR,IAAI,IAAI,CAAC0I,cAAc,CAACC,OAAO,IAAIgD,iBAAiB,EAAE;UAClD,MAAMO,WAAW,GAAG,IAAI,CAACjE,gBAAgB,GAAG,IAAI,CAACS,cAAc,CAACE,KAAK;UACrE,IAAI,CAACuD,qBAAqB,CAACD,WAAW,GAAG,CAAC,GAAGA,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC;UAChE;QACJ,CAAC,MACI;UACD;QACJ;MACJ,KAAKnM,SAAS;QACV,IAAI,IAAI,CAAC2I,cAAc,CAACC,OAAO,IAAIgD,iBAAiB,EAAE;UAClD,MAAMO,WAAW,GAAG,IAAI,CAACjE,gBAAgB,GAAG,IAAI,CAACS,cAAc,CAACE,KAAK;UACrE,MAAMwD,WAAW,GAAG,IAAI,CAAC5B,cAAc,CAAC,CAAC,CAAC/H,MAAM;UAChD,IAAI,CAAC0J,qBAAqB,CAACD,WAAW,GAAGE,WAAW,GAAGF,WAAW,GAAGE,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;UACzF;QACJ,CAAC,MACI;UACD;QACJ;MACJ;QACI,IAAIT,iBAAiB,IAAIjM,cAAc,CAAC8L,KAAK,EAAE,UAAU,CAAC,EAAE;UACxD;UACA;UACA,IAAIA,KAAK,CAACzH,GAAG,IAAIyH,KAAK,CAACzH,GAAG,CAACtB,MAAM,KAAK,CAAC,EAAE;YACrC,IAAI,CAAC2F,gBAAgB,CAACkD,IAAI,CAACE,KAAK,CAACzH,GAAG,CAACsI,iBAAiB,CAAC,CAAC,CAAC;UAC7D,CAAC,MACI,IAAKZ,OAAO,IAAI9L,CAAC,IAAI8L,OAAO,IAAI7L,CAAC,IAAM6L,OAAO,IAAI5L,IAAI,IAAI4L,OAAO,IAAI3L,IAAK,EAAE;YAC7E,IAAI,CAACsI,gBAAgB,CAACkD,IAAI,CAACgB,MAAM,CAACC,YAAY,CAACd,OAAO,CAAC,CAAC;UAC5D;QACJ;QACA;QACA;QACA;IACR;IACA,IAAI,CAACzC,eAAe,GAAG,EAAE;IACzBwC,KAAK,CAACgB,cAAc,CAAC,CAAC;EAC1B;EACA;EACA,IAAIC,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACxE,gBAAgB;EAChC;EACA;EACA,IAAIyE,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACxE,WAAW;EAC3B;EACA;EACAyE,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC3D,eAAe,CAACvG,MAAM,GAAG,CAAC;EAC1C;EACA;EACAuJ,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACG,qBAAqB,CAAC,CAAC,EAAE,CAAC,CAAC;EACpC;EACA;EACAF,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACE,qBAAqB,CAAC,IAAI,CAAC3B,cAAc,CAAC,CAAC,CAAC/H,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;EACpE;EACA;EACAqJ,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC7D,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAAC+D,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAACY,qBAAqB,CAAC,CAAC,CAAC;EACzF;EACA;EACAb,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC9D,gBAAgB,GAAG,CAAC,IAAI,IAAI,CAACE,KAAK,GACjC,IAAI,CAAC8D,iBAAiB,CAAC,CAAC,GACxB,IAAI,CAACW,qBAAqB,CAAC,CAAC,CAAC,CAAC;EACxC;EACAvB,gBAAgBA,CAACvC,IAAI,EAAE;IACnB,MAAM+D,SAAS,GAAG,IAAI,CAACrC,cAAc,CAAC,CAAC;IACvC,MAAMM,KAAK,GAAG,OAAOhC,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAG+D,SAAS,CAACtG,OAAO,CAACuC,IAAI,CAAC;IACvE,MAAM4D,UAAU,GAAGG,SAAS,CAAC/B,KAAK,CAAC;IACnC;IACA,IAAI,CAAC5C,WAAW,GAAGwE,UAAU,IAAI,IAAI,GAAG,IAAI,GAAGA,UAAU;IACzD,IAAI,CAACzE,gBAAgB,GAAG6C,KAAK;EACjC;EACA;EACAgC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACzE,sBAAsB,CAACqC,WAAW,CAAC,CAAC;IACzC,IAAI,CAACvB,wBAAwB,EAAEuB,WAAW,CAAC,CAAC;IAC5C,IAAI,CAAChB,UAAU,EAAEoD,OAAO,CAAC,CAAC;IAC1B,IAAI,CAAC1E,gBAAgB,CAAC2E,QAAQ,CAAC,CAAC;IAChC,IAAI,CAAC9D,MAAM,CAAC8D,QAAQ,CAAC,CAAC;IACtB,IAAI,CAAC7D,MAAM,CAAC6D,QAAQ,CAAC,CAAC;IACtB,IAAI,CAAC/D,eAAe,GAAG,EAAE;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACI4D,qBAAqBA,CAAChE,KAAK,EAAE;IACzB,IAAI,CAACT,KAAK,GAAG,IAAI,CAAC6E,oBAAoB,CAACpE,KAAK,CAAC,GAAG,IAAI,CAACqE,uBAAuB,CAACrE,KAAK,CAAC;EACvF;EACA;AACJ;AACA;AACA;AACA;EACIoE,oBAAoBA,CAACpE,KAAK,EAAE;IACxB,MAAM2B,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACnC,KAAK,IAAIpF,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAImF,KAAK,CAAC9H,MAAM,EAAE2C,CAAC,EAAE,EAAE;MACpC,MAAM0F,KAAK,GAAG,CAAC,IAAI,CAAC7C,gBAAgB,GAAGW,KAAK,GAAGxD,CAAC,GAAGmF,KAAK,CAAC9H,MAAM,IAAI8H,KAAK,CAAC9H,MAAM;MAC/E,MAAMqG,IAAI,GAAGyB,KAAK,CAACO,KAAK,CAAC;MACzB,IAAI,CAAC,IAAI,CAACjC,gBAAgB,CAACC,IAAI,CAAC,EAAE;QAC9B,IAAI,CAACkC,aAAa,CAACF,KAAK,CAAC;QACzB;MACJ;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;EACImC,uBAAuBA,CAACrE,KAAK,EAAE;IAC3B,IAAI,CAACuD,qBAAqB,CAAC,IAAI,CAAClE,gBAAgB,GAAGW,KAAK,EAAEA,KAAK,CAAC;EACpE;EACA;AACJ;AACA;AACA;AACA;EACIuD,qBAAqBA,CAACrB,KAAK,EAAEoC,aAAa,EAAE;IACxC,MAAM3C,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACnC,IAAI,CAACD,KAAK,CAACO,KAAK,CAAC,EAAE;MACf;IACJ;IACA,OAAO,IAAI,CAACjC,gBAAgB,CAAC0B,KAAK,CAACO,KAAK,CAAC,CAAC,EAAE;MACxCA,KAAK,IAAIoC,aAAa;MACtB,IAAI,CAAC3C,KAAK,CAACO,KAAK,CAAC,EAAE;QACf;MACJ;IACJ;IACA,IAAI,CAACE,aAAa,CAACF,KAAK,CAAC;EAC7B;EACA;EACAN,cAAcA,CAAA,EAAG;IACb,IAAIlM,QAAQ,CAAC,IAAI,CAACyJ,MAAM,CAAC,EAAE;MACvB,OAAO,IAAI,CAACA,MAAM,CAAC,CAAC;IACxB;IACA,OAAO,IAAI,CAACA,MAAM,YAAY1J,SAAS,GAAG,IAAI,CAAC0J,MAAM,CAACyB,OAAO,CAAC,CAAC,GAAG,IAAI,CAACzB,MAAM;EACjF;EACA;EACAwB,aAAaA,CAACD,QAAQ,EAAE;IACpB,IAAI,IAAI,CAACpB,WAAW,EAAE;MAClB,MAAMiF,QAAQ,GAAG7D,QAAQ,CAAC/C,OAAO,CAAC,IAAI,CAAC2B,WAAW,CAAC;MACnD,IAAIiF,QAAQ,GAAG,CAAC,CAAC,IAAIA,QAAQ,KAAK,IAAI,CAAClF,gBAAgB,EAAE;QACrD,IAAI,CAACA,gBAAgB,GAAGkF,QAAQ;MACpC;IACJ;EACJ;AACJ;AAEA,MAAMC,0BAA0B,SAAStF,cAAc,CAAC;EACpDkD,aAAaA,CAACF,KAAK,EAAE;IACjB,IAAI,IAAI,CAAC4B,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACW,iBAAiB,CAAC,CAAC;IACvC;IACA,KAAK,CAACrC,aAAa,CAACF,KAAK,CAAC;IAC1B,IAAI,IAAI,CAAC4B,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACY,eAAe,CAAC,CAAC;IACrC;EACJ;AACJ;AAEA,MAAMC,eAAe,SAASzF,cAAc,CAAC;EACzC3E,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGqK,SAAS,CAAC;IACnB,IAAI,CAACC,OAAO,GAAG,SAAS;EAC5B;EACA;AACJ;AACA;AACA;EACIC,cAAcA,CAACC,MAAM,EAAE;IACnB,IAAI,CAACF,OAAO,GAAGE,MAAM;IACrB,OAAO,IAAI;EACf;EACA3C,aAAaA,CAAClC,IAAI,EAAE;IAChB,KAAK,CAACkC,aAAa,CAAClC,IAAI,CAAC;IACzB,IAAI,IAAI,CAAC4D,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACkB,KAAK,CAAC,IAAI,CAACH,OAAO,CAAC;IACvC;EACJ;AACJ;;AAEA;AACA;AACA;AACA,MAAMI,iBAAiB,CAAC;EACpB1K,WAAWA,CAAA,EAAG;IACV;AACR;AACA;IACQ,IAAI,CAAC2K,gBAAgB,GAAG,KAAK;EACjC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,CAAC;EACvB5K,WAAWA,CAACE,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;EACI2K,UAAUA,CAAC3H,OAAO,EAAE;IAChB;IACA;IACA,OAAOA,OAAO,CAAC4H,YAAY,CAAC,UAAU,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,SAASA,CAAC7H,OAAO,EAAE;IACf,OAAO8H,WAAW,CAAC9H,OAAO,CAAC,IAAI+H,gBAAgB,CAAC/H,OAAO,CAAC,CAACL,UAAU,KAAK,SAAS;EACrF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIqI,UAAUA,CAAChI,OAAO,EAAE;IAChB;IACA,IAAI,CAAC,IAAI,CAAChD,SAAS,CAAC8C,SAAS,EAAE;MAC3B,OAAO,KAAK;IAChB;IACA,MAAMmI,YAAY,GAAGC,eAAe,CAACC,SAAS,CAACnI,OAAO,CAAC,CAAC;IACxD,IAAIiI,YAAY,EAAE;MACd;MACA,IAAIG,gBAAgB,CAACH,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;QACvC,OAAO,KAAK;MAChB;MACA;MACA,IAAI,CAAC,IAAI,CAACJ,SAAS,CAACI,YAAY,CAAC,EAAE;QAC/B,OAAO,KAAK;MAChB;IACJ;IACA,IAAII,QAAQ,GAAGrI,OAAO,CAACqI,QAAQ,CAACC,WAAW,CAAC,CAAC;IAC7C,IAAIC,aAAa,GAAGH,gBAAgB,CAACpI,OAAO,CAAC;IAC7C,IAAIA,OAAO,CAAC4H,YAAY,CAAC,iBAAiB,CAAC,EAAE;MACzC,OAAOW,aAAa,KAAK,CAAC,CAAC;IAC/B;IACA,IAAIF,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,QAAQ,EAAE;MAChD;MACA;MACA;MACA,OAAO,KAAK;IAChB;IACA;IACA,IAAI,IAAI,CAACrL,SAAS,CAACwL,MAAM,IAAI,IAAI,CAACxL,SAAS,CAACyL,GAAG,IAAI,CAACC,wBAAwB,CAAC1I,OAAO,CAAC,EAAE;MACnF,OAAO,KAAK;IAChB;IACA,IAAIqI,QAAQ,KAAK,OAAO,EAAE;MACtB;MACA;MACA,IAAI,CAACrI,OAAO,CAAC4H,YAAY,CAAC,UAAU,CAAC,EAAE;QACnC,OAAO,KAAK;MAChB;MACA;MACA;MACA,OAAOW,aAAa,KAAK,CAAC,CAAC;IAC/B;IACA,IAAIF,QAAQ,KAAK,OAAO,EAAE;MACtB;MACA;MACA;MACA;MACA,IAAIE,aAAa,KAAK,CAAC,CAAC,EAAE;QACtB,OAAO,KAAK;MAChB;MACA;MACA;MACA,IAAIA,aAAa,KAAK,IAAI,EAAE;QACxB,OAAO,IAAI;MACf;MACA;MACA;MACA;MACA,OAAO,IAAI,CAACvL,SAAS,CAAC2L,OAAO,IAAI3I,OAAO,CAAC4H,YAAY,CAAC,UAAU,CAAC;IACrE;IACA,OAAO5H,OAAO,CAAC4I,QAAQ,IAAI,CAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAAC7I,OAAO,EAAE8I,MAAM,EAAE;IACzB;IACA;IACA,OAAQC,sBAAsB,CAAC/I,OAAO,CAAC,IACnC,CAAC,IAAI,CAAC2H,UAAU,CAAC3H,OAAO,CAAC,KACxB8I,MAAM,EAAErB,gBAAgB,IAAI,IAAI,CAACI,SAAS,CAAC7H,OAAO,CAAC,CAAC;EAC7D;EACA;IAAS,IAAI,CAACS,IAAI,YAAAuI,6BAAArI,CAAA;MAAA,YAAAA,CAAA,IAAwF+G,oBAAoB,EAliB9B/P,EAAE,CAAAiJ,QAAA,CAkiB8CjI,EAAE,CAACC,QAAQ;IAAA,CAA6C;EAAE;EAC1M;IAAS,IAAI,CAACiI,KAAK,kBAniB6ElJ,EAAE,CAAAmJ,kBAAA;MAAAC,KAAA,EAmiBY2G,oBAAoB;MAAA1G,OAAA,EAApB0G,oBAAoB,CAAAjH,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AAC/J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAriBoGvJ,EAAE,CAAAwJ,iBAAA,CAqiBXuG,oBAAoB,EAAc,CAAC;IAClHtG,IAAI,EAAEtJ,UAAU;IAChBuJ,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAEzI,EAAE,CAACC;EAAS,CAAC,CAAC;AAAA;AACzD;AACA;AACA;AACA;AACA;AACA,SAASsP,eAAeA,CAACe,MAAM,EAAE;EAC7B,IAAI;IACA,OAAOA,MAAM,CAAChB,YAAY;EAC9B,CAAC,CACD,MAAM;IACF,OAAO,IAAI;EACf;AACJ;AACA;AACA,SAASH,WAAWA,CAAC9H,OAAO,EAAE;EAC1B;EACA;EACA,OAAO,CAAC,EAAEA,OAAO,CAACkJ,WAAW,IACzBlJ,OAAO,CAACmJ,YAAY,IACnB,OAAOnJ,OAAO,CAACoJ,cAAc,KAAK,UAAU,IAAIpJ,OAAO,CAACoJ,cAAc,CAAC,CAAC,CAAChN,MAAO,CAAC;AAC1F;AACA;AACA,SAASiN,mBAAmBA,CAACrJ,OAAO,EAAE;EAClC,IAAIqI,QAAQ,GAAGrI,OAAO,CAACqI,QAAQ,CAACC,WAAW,CAAC,CAAC;EAC7C,OAAQD,QAAQ,KAAK,OAAO,IACxBA,QAAQ,KAAK,QAAQ,IACrBA,QAAQ,KAAK,QAAQ,IACrBA,QAAQ,KAAK,UAAU;AAC/B;AACA;AACA,SAASiB,aAAaA,CAACtJ,OAAO,EAAE;EAC5B,OAAOuJ,cAAc,CAACvJ,OAAO,CAAC,IAAIA,OAAO,CAACoB,IAAI,IAAI,QAAQ;AAC9D;AACA;AACA,SAASoI,gBAAgBA,CAACxJ,OAAO,EAAE;EAC/B,OAAOyJ,eAAe,CAACzJ,OAAO,CAAC,IAAIA,OAAO,CAAC4H,YAAY,CAAC,MAAM,CAAC;AACnE;AACA;AACA,SAAS2B,cAAcA,CAACvJ,OAAO,EAAE;EAC7B,OAAOA,OAAO,CAACqI,QAAQ,CAACC,WAAW,CAAC,CAAC,IAAI,OAAO;AACpD;AACA;AACA,SAASmB,eAAeA,CAACzJ,OAAO,EAAE;EAC9B,OAAOA,OAAO,CAACqI,QAAQ,CAACC,WAAW,CAAC,CAAC,IAAI,GAAG;AAChD;AACA;AACA,SAASoB,gBAAgBA,CAAC1J,OAAO,EAAE;EAC/B,IAAI,CAACA,OAAO,CAAC4H,YAAY,CAAC,UAAU,CAAC,IAAI5H,OAAO,CAAC4I,QAAQ,KAAKtH,SAAS,EAAE;IACrE,OAAO,KAAK;EAChB;EACA,IAAIsH,QAAQ,GAAG5I,OAAO,CAACzD,YAAY,CAAC,UAAU,CAAC;EAC/C,OAAO,CAAC,EAAEqM,QAAQ,IAAI,CAACe,KAAK,CAACC,QAAQ,CAAChB,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;AACzD;AACA;AACA;AACA;AACA;AACA,SAASR,gBAAgBA,CAACpI,OAAO,EAAE;EAC/B,IAAI,CAAC0J,gBAAgB,CAAC1J,OAAO,CAAC,EAAE;IAC5B,OAAO,IAAI;EACf;EACA;EACA,MAAM4I,QAAQ,GAAGgB,QAAQ,CAAC5J,OAAO,CAACzD,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;EACrE,OAAOoN,KAAK,CAACf,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAGA,QAAQ;AAC1C;AACA;AACA,SAASF,wBAAwBA,CAAC1I,OAAO,EAAE;EACvC,IAAIqI,QAAQ,GAAGrI,OAAO,CAACqI,QAAQ,CAACC,WAAW,CAAC,CAAC;EAC7C,IAAIuB,SAAS,GAAGxB,QAAQ,KAAK,OAAO,IAAIrI,OAAO,CAACoB,IAAI;EACpD,OAAQyI,SAAS,KAAK,MAAM,IACxBA,SAAS,KAAK,UAAU,IACxBxB,QAAQ,KAAK,QAAQ,IACrBA,QAAQ,KAAK,UAAU;AAC/B;AACA;AACA;AACA;AACA;AACA,SAASU,sBAAsBA,CAAC/I,OAAO,EAAE;EACrC;EACA,IAAIsJ,aAAa,CAACtJ,OAAO,CAAC,EAAE;IACxB,OAAO,KAAK;EAChB;EACA,OAAQqJ,mBAAmB,CAACrJ,OAAO,CAAC,IAChCwJ,gBAAgB,CAACxJ,OAAO,CAAC,IACzBA,OAAO,CAAC4H,YAAY,CAAC,iBAAiB,CAAC,IACvC8B,gBAAgB,CAAC1J,OAAO,CAAC;AACjC;AACA;AACA,SAASmI,SAASA,CAAC2B,IAAI,EAAE;EACrB;EACA,OAAQA,IAAI,CAACC,aAAa,IAAID,IAAI,CAACC,aAAa,CAACC,WAAW,IAAKf,MAAM;AAC3E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgB,SAAS,CAAC;EACZ;EACA,IAAI3H,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC4H,QAAQ;EACxB;EACA,IAAI5H,OAAOA,CAAC6H,KAAK,EAAE;IACf,IAAI,CAACD,QAAQ,GAAGC,KAAK;IACrB,IAAI,IAAI,CAACC,YAAY,IAAI,IAAI,CAACC,UAAU,EAAE;MACtC,IAAI,CAACC,qBAAqB,CAACH,KAAK,EAAE,IAAI,CAACC,YAAY,CAAC;MACpD,IAAI,CAACE,qBAAqB,CAACH,KAAK,EAAE,IAAI,CAACE,UAAU,CAAC;IACtD;EACJ;EACAvN,WAAWA,CAACyN,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAE1N,SAAS,EAAE2N,YAAY,GAAG,KAAK,EAAE;IACtE,IAAI,CAACH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC1N,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC4N,YAAY,GAAG,KAAK;IACzB;IACA,IAAI,CAACC,mBAAmB,GAAG,MAAM,IAAI,CAACC,wBAAwB,CAAC,CAAC;IAChE,IAAI,CAACC,iBAAiB,GAAG,MAAM,IAAI,CAACC,yBAAyB,CAAC,CAAC;IAC/D,IAAI,CAACb,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACQ,YAAY,EAAE;MACf,IAAI,CAACM,aAAa,CAAC,CAAC;IACxB;EACJ;EACA;EACAvE,OAAOA,CAAA,EAAG;IACN,MAAMwE,WAAW,GAAG,IAAI,CAACb,YAAY;IACrC,MAAMc,SAAS,GAAG,IAAI,CAACb,UAAU;IACjC,IAAIY,WAAW,EAAE;MACbA,WAAW,CAACE,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACP,mBAAmB,CAAC;MAClEK,WAAW,CAACtM,MAAM,CAAC,CAAC;IACxB;IACA,IAAIuM,SAAS,EAAE;MACXA,SAAS,CAACC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACL,iBAAiB,CAAC;MAC9DI,SAAS,CAACvM,MAAM,CAAC,CAAC;IACtB;IACA,IAAI,CAACyL,YAAY,GAAG,IAAI,CAACC,UAAU,GAAG,IAAI;IAC1C,IAAI,CAACM,YAAY,GAAG,KAAK;EAC7B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIK,aAAaA,CAAA,EAAG;IACZ;IACA,IAAI,IAAI,CAACL,YAAY,EAAE;MACnB,OAAO,IAAI;IACf;IACA,IAAI,CAACF,OAAO,CAACW,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAAC,IAAI,CAAChB,YAAY,EAAE;QACpB,IAAI,CAACA,YAAY,GAAG,IAAI,CAACiB,aAAa,CAAC,CAAC;QACxC,IAAI,CAACjB,YAAY,CAACkB,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACV,mBAAmB,CAAC;MACzE;MACA,IAAI,CAAC,IAAI,CAACP,UAAU,EAAE;QAClB,IAAI,CAACA,UAAU,GAAG,IAAI,CAACgB,aAAa,CAAC,CAAC;QACtC,IAAI,CAAChB,UAAU,CAACiB,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACR,iBAAiB,CAAC;MACrE;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAACP,QAAQ,CAACgB,UAAU,EAAE;MAC1B,IAAI,CAAChB,QAAQ,CAACgB,UAAU,CAACC,YAAY,CAAC,IAAI,CAACpB,YAAY,EAAE,IAAI,CAACG,QAAQ,CAAC;MACvE,IAAI,CAACA,QAAQ,CAACgB,UAAU,CAACC,YAAY,CAAC,IAAI,CAACnB,UAAU,EAAE,IAAI,CAACE,QAAQ,CAACkB,WAAW,CAAC;MACjF,IAAI,CAACd,YAAY,GAAG,IAAI;IAC5B;IACA,OAAO,IAAI,CAACA,YAAY;EAC5B;EACA;AACJ;AACA;AACA;AACA;EACIe,4BAA4BA,CAACC,OAAO,EAAE;IAClC,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;MAC1B,IAAI,CAACC,gBAAgB,CAAC,MAAMD,OAAO,CAAC,IAAI,CAACE,mBAAmB,CAACJ,OAAO,CAAC,CAAC,CAAC;IAC3E,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIK,kCAAkCA,CAACL,OAAO,EAAE;IACxC,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;MAC1B,IAAI,CAACC,gBAAgB,CAAC,MAAMD,OAAO,CAAC,IAAI,CAACd,yBAAyB,CAACY,OAAO,CAAC,CAAC,CAAC;IACjF,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIM,iCAAiCA,CAACN,OAAO,EAAE;IACvC,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;MAC1B,IAAI,CAACC,gBAAgB,CAAC,MAAMD,OAAO,CAAC,IAAI,CAAChB,wBAAwB,CAACc,OAAO,CAAC,CAAC,CAAC;IAChF,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIO,kBAAkBA,CAACC,KAAK,EAAE;IACtB;IACA,MAAMC,OAAO,GAAG,IAAI,CAAC7B,QAAQ,CAACzL,gBAAgB,CAAC,qBAAqBqN,KAAK,KAAK,GAAG,kBAAkBA,KAAK,KAAK,GAAG,cAAcA,KAAK,GAAG,CAAC;IACvI,IAAI,OAAOjL,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqN,OAAO,CAAChQ,MAAM,EAAE2C,CAAC,EAAE,EAAE;QACrC;QACA,IAAIqN,OAAO,CAACrN,CAAC,CAAC,CAAC6I,YAAY,CAAC,aAAauE,KAAK,EAAE,CAAC,EAAE;UAC/CE,OAAO,CAACC,IAAI,CAAC,gDAAgDH,KAAK,KAAK,GACnE,sBAAsBA,KAAK,4BAA4B,GACvD,qCAAqC,EAAEC,OAAO,CAACrN,CAAC,CAAC,CAAC;QAC1D,CAAC,MACI,IAAIqN,OAAO,CAACrN,CAAC,CAAC,CAAC6I,YAAY,CAAC,oBAAoBuE,KAAK,EAAE,CAAC,EAAE;UAC3DE,OAAO,CAACC,IAAI,CAAC,uDAAuDH,KAAK,KAAK,GAC1E,sBAAsBA,KAAK,sCAAsC,GACjE,2BAA2B,EAAEC,OAAO,CAACrN,CAAC,CAAC,CAAC;QAChD;MACJ;IACJ;IACA,IAAIoN,KAAK,IAAI,OAAO,EAAE;MAClB,OAAOC,OAAO,CAAChQ,MAAM,GAAGgQ,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAACG,wBAAwB,CAAC,IAAI,CAAChC,QAAQ,CAAC;IACrF;IACA,OAAO6B,OAAO,CAAChQ,MAAM,GACfgQ,OAAO,CAACA,OAAO,CAAChQ,MAAM,GAAG,CAAC,CAAC,GAC3B,IAAI,CAACoQ,uBAAuB,CAAC,IAAI,CAACjC,QAAQ,CAAC;EACrD;EACA;AACJ;AACA;AACA;EACIwB,mBAAmBA,CAACJ,OAAO,EAAE;IACzB;IACA,MAAMc,iBAAiB,GAAG,IAAI,CAAClC,QAAQ,CAACmC,aAAa,CAAC,uBAAuB,GAAG,mBAAmB,CAAC;IACpG,IAAID,iBAAiB,EAAE;MACnB;MACA,IAAI,CAAC,OAAOvL,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC9CuL,iBAAiB,CAAC7E,YAAY,CAAC,mBAAmB,CAAC,EAAE;QACrDyE,OAAO,CAACC,IAAI,CAAC,yDAAyD,GAClE,0DAA0D,GAC1D,0BAA0B,EAAEG,iBAAiB,CAAC;MACtD;MACA;MACA;MACA,IAAI,CAAC,OAAOvL,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC9C,CAAC,IAAI,CAACsJ,QAAQ,CAAC3B,WAAW,CAAC4D,iBAAiB,CAAC,EAAE;QAC/CJ,OAAO,CAACC,IAAI,CAAC,wDAAwD,EAAEG,iBAAiB,CAAC;MAC7F;MACA,IAAI,CAAC,IAAI,CAACjC,QAAQ,CAAC3B,WAAW,CAAC4D,iBAAiB,CAAC,EAAE;QAC/C,MAAME,cAAc,GAAG,IAAI,CAACJ,wBAAwB,CAACE,iBAAiB,CAAC;QACvEE,cAAc,EAAEpF,KAAK,CAACoE,OAAO,CAAC;QAC9B,OAAO,CAAC,CAACgB,cAAc;MAC3B;MACAF,iBAAiB,CAAClF,KAAK,CAACoE,OAAO,CAAC;MAChC,OAAO,IAAI;IACf;IACA,OAAO,IAAI,CAACZ,yBAAyB,CAACY,OAAO,CAAC;EAClD;EACA;AACJ;AACA;AACA;EACIZ,yBAAyBA,CAACY,OAAO,EAAE;IAC/B,MAAMc,iBAAiB,GAAG,IAAI,CAACP,kBAAkB,CAAC,OAAO,CAAC;IAC1D,IAAIO,iBAAiB,EAAE;MACnBA,iBAAiB,CAAClF,KAAK,CAACoE,OAAO,CAAC;IACpC;IACA,OAAO,CAAC,CAACc,iBAAiB;EAC9B;EACA;AACJ;AACA;AACA;EACI5B,wBAAwBA,CAACc,OAAO,EAAE;IAC9B,MAAMc,iBAAiB,GAAG,IAAI,CAACP,kBAAkB,CAAC,KAAK,CAAC;IACxD,IAAIO,iBAAiB,EAAE;MACnBA,iBAAiB,CAAClF,KAAK,CAACoE,OAAO,CAAC;IACpC;IACA,OAAO,CAAC,CAACc,iBAAiB;EAC9B;EACA;AACJ;AACA;EACIG,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACjC,YAAY;EAC5B;EACA;EACA4B,wBAAwBA,CAACM,IAAI,EAAE;IAC3B,IAAI,IAAI,CAACrC,QAAQ,CAAC3B,WAAW,CAACgE,IAAI,CAAC,IAAI,IAAI,CAACrC,QAAQ,CAACxC,UAAU,CAAC6E,IAAI,CAAC,EAAE;MACnE,OAAOA,IAAI;IACf;IACA,MAAMC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IAC9B,KAAK,IAAI/N,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+N,QAAQ,CAAC1Q,MAAM,EAAE2C,CAAC,EAAE,EAAE;MACtC,MAAMgO,aAAa,GAAGD,QAAQ,CAAC/N,CAAC,CAAC,CAACwB,QAAQ,KAAK,IAAI,CAACxD,SAAS,CAACyD,YAAY,GACpE,IAAI,CAAC+L,wBAAwB,CAACO,QAAQ,CAAC/N,CAAC,CAAC,CAAC,GAC1C,IAAI;MACV,IAAIgO,aAAa,EAAE;QACf,OAAOA,aAAa;MACxB;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACAP,uBAAuBA,CAACK,IAAI,EAAE;IAC1B,IAAI,IAAI,CAACrC,QAAQ,CAAC3B,WAAW,CAACgE,IAAI,CAAC,IAAI,IAAI,CAACrC,QAAQ,CAACxC,UAAU,CAAC6E,IAAI,CAAC,EAAE;MACnE,OAAOA,IAAI;IACf;IACA;IACA,MAAMC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IAC9B,KAAK,IAAI/N,CAAC,GAAG+N,QAAQ,CAAC1Q,MAAM,GAAG,CAAC,EAAE2C,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3C,MAAMgO,aAAa,GAAGD,QAAQ,CAAC/N,CAAC,CAAC,CAACwB,QAAQ,KAAK,IAAI,CAACxD,SAAS,CAACyD,YAAY,GACpE,IAAI,CAACgM,uBAAuB,CAACM,QAAQ,CAAC/N,CAAC,CAAC,CAAC,GACzC,IAAI;MACV,IAAIgO,aAAa,EAAE;QACf,OAAOA,aAAa;MACxB;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACA1B,aAAaA,CAAA,EAAG;IACZ,MAAM2B,MAAM,GAAG,IAAI,CAACjQ,SAAS,CAACmC,aAAa,CAAC,KAAK,CAAC;IAClD,IAAI,CAACoL,qBAAqB,CAAC,IAAI,CAACJ,QAAQ,EAAE8C,MAAM,CAAC;IACjDA,MAAM,CAACpN,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAC3CmN,MAAM,CAACpN,SAAS,CAACC,GAAG,CAAC,uBAAuB,CAAC;IAC7CmN,MAAM,CAACjR,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAC1C,OAAOiR,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;EACI1C,qBAAqBA,CAAC2C,SAAS,EAAED,MAAM,EAAE;IACrC;IACA;IACAC,SAAS,GAAGD,MAAM,CAACjR,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,GAAGiR,MAAM,CAAC3Q,eAAe,CAAC,UAAU,CAAC;EACzF;EACA;AACJ;AACA;AACA;EACI6Q,aAAaA,CAAC5K,OAAO,EAAE;IACnB,IAAI,IAAI,CAAC8H,YAAY,IAAI,IAAI,CAACC,UAAU,EAAE;MACtC,IAAI,CAACC,qBAAqB,CAAChI,OAAO,EAAE,IAAI,CAAC8H,YAAY,CAAC;MACtD,IAAI,CAACE,qBAAqB,CAAChI,OAAO,EAAE,IAAI,CAAC+H,UAAU,CAAC;IACxD;EACJ;EACA;EACAyB,gBAAgBA,CAACqB,EAAE,EAAE;IACjB,IAAI,IAAI,CAAC1C,OAAO,CAAC2C,QAAQ,EAAE;MACvBD,EAAE,CAAC,CAAC;IACR,CAAC,MACI;MACD,IAAI,CAAC1C,OAAO,CAAC4C,QAAQ,CAAC/I,IAAI,CAAC1J,IAAI,CAAC,CAAC,CAAC,CAAC,CAACoI,SAAS,CAACmK,EAAE,CAAC;IACrD;EACJ;AACJ;AACA;AACA;AACA;AACA,MAAMG,gBAAgB,CAAC;EACnBxQ,WAAWA,CAAC0N,QAAQ,EAAEC,OAAO,EAAE1N,SAAS,EAAE;IACtC,IAAI,CAACyN,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC1N,SAAS,GAAGA,SAAS;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIwQ,MAAMA,CAACvN,OAAO,EAAEwN,oBAAoB,GAAG,KAAK,EAAE;IAC1C,OAAO,IAAIvD,SAAS,CAACjK,OAAO,EAAE,IAAI,CAACwK,QAAQ,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAAC1N,SAAS,EAAEyQ,oBAAoB,CAAC;EACpG;EACA;IAAS,IAAI,CAAC/M,IAAI,YAAAgN,yBAAA9M,CAAA;MAAA,YAAAA,CAAA,IAAwF2M,gBAAgB,EAv6B1B3V,EAAE,CAAAiJ,QAAA,CAu6B0C8G,oBAAoB,GAv6BhE/P,EAAE,CAAAiJ,QAAA,CAu6B2EjJ,EAAE,CAAC+V,MAAM,GAv6BtF/V,EAAE,CAAAiJ,QAAA,CAu6BiGlJ,QAAQ;IAAA,CAA6C;EAAE;EAC1P;IAAS,IAAI,CAACmJ,KAAK,kBAx6B6ElJ,EAAE,CAAAmJ,kBAAA;MAAAC,KAAA,EAw6BYuM,gBAAgB;MAAAtM,OAAA,EAAhBsM,gBAAgB,CAAA7M,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AAC3J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA16BoGvJ,EAAE,CAAAwJ,iBAAA,CA06BXmM,gBAAgB,EAAc,CAAC;IAC9GlM,IAAI,EAAEtJ,UAAU;IAChBuJ,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAEsG;EAAqB,CAAC,EAAE;IAAEtG,IAAI,EAAEzJ,EAAE,CAAC+V;EAAO,CAAC,EAAE;IAAEtM,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MACpGH,IAAI,EAAErJ,MAAM;MACZsJ,IAAI,EAAE,CAAC3J,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA,MAAMiW,YAAY,CAAC;EACf;EACA,IAAIrL,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACsL,SAAS,EAAEtL,OAAO,IAAI,KAAK;EAC3C;EACA,IAAIA,OAAOA,CAAC6H,KAAK,EAAE;IACf,IAAI,IAAI,CAACyD,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACtL,OAAO,GAAG6H,KAAK;IAClC;EACJ;EACArN,WAAWA,CAAC+Q,WAAW,EAAEC,iBAAiB;EAC1C;AACJ;AACA;AACA;EACI/Q,SAAS,EAAE;IACP,IAAI,CAAC8Q,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C;IACA,IAAI,CAACC,yBAAyB,GAAG,IAAI;IACrC,MAAMC,QAAQ,GAAGpW,MAAM,CAACgB,QAAQ,CAAC;IACjC,IAAIoV,QAAQ,CAAClO,SAAS,EAAE;MACpB,IAAI,CAAC8N,SAAS,GAAG,IAAI,CAACE,iBAAiB,CAACP,MAAM,CAAC,IAAI,CAACM,WAAW,CAACI,aAAa,EAAE,IAAI,CAAC;IACxF;EACJ;EACArP,WAAWA,CAAA,EAAG;IACV,IAAI,CAACgP,SAAS,EAAEnH,OAAO,CAAC,CAAC;IACzB;IACA;IACA,IAAI,IAAI,CAACsH,yBAAyB,EAAE;MAChC,IAAI,CAACA,yBAAyB,CAACxG,KAAK,CAAC,CAAC;MACtC,IAAI,CAACwG,yBAAyB,GAAG,IAAI;IACzC;EACJ;EACAG,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACN,SAAS,EAAE5C,aAAa,CAAC,CAAC;IAC/B,IAAI,IAAI,CAACmD,WAAW,EAAE;MAClB,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB;EACJ;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACT,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAAChB,WAAW,CAAC,CAAC,EAAE;MACjD,IAAI,CAACgB,SAAS,CAAC5C,aAAa,CAAC,CAAC;IAClC;EACJ;EACAsD,WAAWA,CAACvL,OAAO,EAAE;IACjB,MAAMwL,iBAAiB,GAAGxL,OAAO,CAAC,aAAa,CAAC;IAChD,IAAIwL,iBAAiB,IACjB,CAACA,iBAAiB,CAACC,WAAW,IAC9B,IAAI,CAACL,WAAW,IAChB,IAAI,CAACP,SAAS,EAAEhB,WAAW,CAAC,CAAC,EAAE;MAC/B,IAAI,CAACwB,aAAa,CAAC,CAAC;IACxB;EACJ;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACL,yBAAyB,GAAGlV,iCAAiC,CAAC,CAAC;IACpE,IAAI,CAAC+U,SAAS,EAAElC,4BAA4B,CAAC,CAAC;EAClD;EACA;IAAS,IAAI,CAACjL,IAAI,YAAAgO,qBAAA9N,CAAA;MAAA,YAAAA,CAAA,IAAwFgN,YAAY,EA5+BtBhW,EAAE,CAAA+W,iBAAA,CA4+BsC/W,EAAE,CAACgX,UAAU,GA5+BrDhX,EAAE,CAAA+W,iBAAA,CA4+BgEpB,gBAAgB,GA5+BlF3V,EAAE,CAAA+W,iBAAA,CA4+B6FhX,QAAQ;IAAA,CAA4C;EAAE;EACrP;IAAS,IAAI,CAACkX,IAAI,kBA7+B8EjX,EAAE,CAAAkX,iBAAA;MAAAzN,IAAA,EA6+BJuM,YAAY;MAAAmB,SAAA;MAAAC,MAAA;QAAAzM,OAAA,GA7+BV3K,EAAE,CAAAqX,YAAA,CAAAC,0BAAA,6BA6+ByG9W,gBAAgB;QAAAgW,WAAA,GA7+B3HxW,EAAE,CAAAqX,YAAA,CAAAC,0BAAA,4CA6+BoL9W,gBAAgB;MAAA;MAAA+W,QAAA;MAAAC,UAAA;MAAAC,QAAA,GA7+BtMzX,EAAE,CAAA0X,wBAAA,EAAF1X,EAAE,CAAA2X,oBAAA;IAAA,EA6+ByQ;EAAE;AACjX;AACA;EAAA,QAAApO,SAAA,oBAAAA,SAAA,KA/+BoGvJ,EAAE,CAAAwJ,iBAAA,CA++BXwM,YAAY,EAAc,CAAC;IAC1GvM,IAAI,EAAEhJ,SAAS;IACfiJ,IAAI,EAAE,CAAC;MACCkO,QAAQ,EAAE,gBAAgB;MAC1BL,QAAQ,EAAE,cAAc;MACxBC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE/N,IAAI,EAAEzJ,EAAE,CAACgX;EAAW,CAAC,EAAE;IAAEvN,IAAI,EAAEkM;EAAiB,CAAC,EAAE;IAAElM,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MACpGH,IAAI,EAAErJ,MAAM;MACZsJ,IAAI,EAAE,CAAC3J,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE4K,OAAO,EAAE,CAAC;MACnClB,IAAI,EAAE/I,KAAK;MACXgJ,IAAI,EAAE,CAAC;QAAEmO,KAAK,EAAE,cAAc;QAAEC,SAAS,EAAEtX;MAAiB,CAAC;IACjE,CAAC,CAAC;IAAEgW,WAAW,EAAE,CAAC;MACd/M,IAAI,EAAE/I,KAAK;MACXgJ,IAAI,EAAE,CAAC;QAAEmO,KAAK,EAAE,yBAAyB;QAAEC,SAAS,EAAEtX;MAAiB,CAAC;IAC5E,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuX,qBAAqB,SAASzF,SAAS,CAAC;EAC1C;EACA,IAAI3H,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC4H,QAAQ;EACxB;EACA,IAAI5H,OAAOA,CAAC6H,KAAK,EAAE;IACf,IAAI,CAACD,QAAQ,GAAGC,KAAK;IACrB,IAAI,IAAI,CAACD,QAAQ,EAAE;MACf,IAAI,CAACyF,iBAAiB,CAACC,QAAQ,CAAC,IAAI,CAAC;IACzC,CAAC,MACI;MACD,IAAI,CAACD,iBAAiB,CAACE,UAAU,CAAC,IAAI,CAAC;IAC3C;EACJ;EACA/S,WAAWA,CAACyN,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAE1N,SAAS,EAAE4S,iBAAiB,EAAEG,cAAc,EAAEhH,MAAM,EAAE;IAC3F,KAAK,CAACyB,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAE1N,SAAS,EAAE+L,MAAM,CAACiH,KAAK,CAAC;IAC3D,IAAI,CAACJ,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACG,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACH,iBAAiB,CAACC,QAAQ,CAAC,IAAI,CAAC;EACzC;EACA;EACAnJ,OAAOA,CAAA,EAAG;IACN,IAAI,CAACkJ,iBAAiB,CAACE,UAAU,CAAC,IAAI,CAAC;IACvC,KAAK,CAACpJ,OAAO,CAAC,CAAC;EACnB;EACA;EACAuJ,OAAOA,CAAA,EAAG;IACN,IAAI,CAACF,cAAc,CAACG,YAAY,CAAC,IAAI,CAAC;IACtC,IAAI,CAAC/C,aAAa,CAAC,IAAI,CAAC;EAC5B;EACA;EACAgD,QAAQA,CAAA,EAAG;IACP,IAAI,CAACJ,cAAc,CAACK,UAAU,CAAC,IAAI,CAAC;IACpC,IAAI,CAACjD,aAAa,CAAC,KAAK,CAAC;EAC7B;AACJ;;AAEA;AACA,MAAMkD,yBAAyB,GAAG,IAAI9X,cAAc,CAAC,2BAA2B,CAAC;;AAEjF;AACA;AACA;AACA;AACA,MAAM+X,mCAAmC,CAAC;EACtCvT,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACwT,SAAS,GAAG,IAAI;EACzB;EACA;EACAL,YAAYA,CAACrC,SAAS,EAAE;IACpB;IACA,IAAI,IAAI,CAAC0C,SAAS,EAAE;MAChB1C,SAAS,CAAC7Q,SAAS,CAACoO,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACmF,SAAS,EAAE,IAAI,CAAC;IAC1E;IACA,IAAI,CAACA,SAAS,GAAIC,CAAC,IAAK,IAAI,CAACC,UAAU,CAAC5C,SAAS,EAAE2C,CAAC,CAAC;IACrD3C,SAAS,CAACnD,OAAO,CAACW,iBAAiB,CAAC,MAAM;MACtCwC,SAAS,CAAC7Q,SAAS,CAACuO,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACgF,SAAS,EAAE,IAAI,CAAC;IACvE,CAAC,CAAC;EACN;EACA;EACAH,UAAUA,CAACvC,SAAS,EAAE;IAClB,IAAI,CAAC,IAAI,CAAC0C,SAAS,EAAE;MACjB;IACJ;IACA1C,SAAS,CAAC7Q,SAAS,CAACoO,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACmF,SAAS,EAAE,IAAI,CAAC;IACtE,IAAI,CAACA,SAAS,GAAG,IAAI;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIE,UAAUA,CAAC5C,SAAS,EAAEzI,KAAK,EAAE;IACzB,MAAMsL,MAAM,GAAGtL,KAAK,CAACsL,MAAM;IAC3B,MAAMC,aAAa,GAAG9C,SAAS,CAACrD,QAAQ;IACxC;IACA;IACA,IAAIkG,MAAM,IAAI,CAACC,aAAa,CAACC,QAAQ,CAACF,MAAM,CAAC,IAAI,CAACA,MAAM,CAACG,OAAO,GAAG,sBAAsB,CAAC,EAAE;MACxF;MACA;MACA;MACAC,UAAU,CAAC,MAAM;QACb;QACA,IAAIjD,SAAS,CAACtL,OAAO,IAAI,CAACoO,aAAa,CAACC,QAAQ,CAAC/C,SAAS,CAAC7Q,SAAS,CAAC+T,aAAa,CAAC,EAAE;UACjFlD,SAAS,CAAC7C,yBAAyB,CAAC,CAAC;QACzC;MACJ,CAAC,CAAC;IACN;EACJ;AACJ;;AAEA;AACA,MAAMgG,gBAAgB,CAAC;EACnBjU,WAAWA,CAAA,EAAG;IACV;IACA;IACA,IAAI,CAACkU,eAAe,GAAG,EAAE;EAC7B;EACA;AACJ;AACA;AACA;EACIpB,QAAQA,CAAChC,SAAS,EAAE;IAChB;IACA,IAAI,CAACoD,eAAe,GAAG,IAAI,CAACA,eAAe,CAACtW,MAAM,CAACuW,EAAE,IAAIA,EAAE,KAAKrD,SAAS,CAAC;IAC1E,IAAIsD,KAAK,GAAG,IAAI,CAACF,eAAe;IAChC,IAAIE,KAAK,CAAC9U,MAAM,EAAE;MACd8U,KAAK,CAACA,KAAK,CAAC9U,MAAM,GAAG,CAAC,CAAC,CAAC8T,QAAQ,CAAC,CAAC;IACtC;IACAgB,KAAK,CAACpV,IAAI,CAAC8R,SAAS,CAAC;IACrBA,SAAS,CAACoC,OAAO,CAAC,CAAC;EACvB;EACA;AACJ;AACA;AACA;EACIH,UAAUA,CAACjC,SAAS,EAAE;IAClBA,SAAS,CAACsC,QAAQ,CAAC,CAAC;IACpB,MAAMgB,KAAK,GAAG,IAAI,CAACF,eAAe;IAClC,MAAMjS,CAAC,GAAGmS,KAAK,CAAChR,OAAO,CAAC0N,SAAS,CAAC;IAClC,IAAI7O,CAAC,KAAK,CAAC,CAAC,EAAE;MACVmS,KAAK,CAACC,MAAM,CAACpS,CAAC,EAAE,CAAC,CAAC;MAClB,IAAImS,KAAK,CAAC9U,MAAM,EAAE;QACd8U,KAAK,CAACA,KAAK,CAAC9U,MAAM,GAAG,CAAC,CAAC,CAAC4T,OAAO,CAAC,CAAC;MACrC;IACJ;EACJ;EACA;IAAS,IAAI,CAACvP,IAAI,YAAA2Q,yBAAAzQ,CAAA;MAAA,YAAAA,CAAA,IAAwFoQ,gBAAgB;IAAA,CAAoD;EAAE;EAChL;IAAS,IAAI,CAAClQ,KAAK,kBA1oC6ElJ,EAAE,CAAAmJ,kBAAA;MAAAC,KAAA,EA0oCYgQ,gBAAgB;MAAA/P,OAAA,EAAhB+P,gBAAgB,CAAAtQ,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AAC3J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA5oCoGvJ,EAAE,CAAAwJ,iBAAA,CA4oCX4P,gBAAgB,EAAc,CAAC;IAC9G3P,IAAI,EAAEtJ,UAAU;IAChBuJ,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;;AAEV;AACA,MAAMoQ,4BAA4B,CAAC;EAC/BvU,WAAWA,CAAC0N,QAAQ,EAAEC,OAAO,EAAEkF,iBAAiB,EAAE5S,SAAS,EAAE+S,cAAc,EAAE;IACzE,IAAI,CAACtF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACkF,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAAC5S,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAAC+S,cAAc,GAAGA,cAAc,IAAI,IAAIO,mCAAmC,CAAC,CAAC;EACrF;EACA9C,MAAMA,CAACvN,OAAO,EAAE8I,MAAM,GAAG;IAAEiH,KAAK,EAAE;EAAM,CAAC,EAAE;IACvC,IAAIuB,YAAY;IAChB,IAAI,OAAOxI,MAAM,KAAK,SAAS,EAAE;MAC7BwI,YAAY,GAAG;QAAEvB,KAAK,EAAEjH;MAAO,CAAC;IACpC,CAAC,MACI;MACDwI,YAAY,GAAGxI,MAAM;IACzB;IACA,OAAO,IAAI4G,qBAAqB,CAAC1P,OAAO,EAAE,IAAI,CAACwK,QAAQ,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAAC1N,SAAS,EAAE,IAAI,CAAC4S,iBAAiB,EAAE,IAAI,CAACG,cAAc,EAAEwB,YAAY,CAAC;EACrJ;EACA;IAAS,IAAI,CAAC7Q,IAAI,YAAA8Q,qCAAA5Q,CAAA;MAAA,YAAAA,CAAA,IAAwF0Q,4BAA4B,EArqCtC1Z,EAAE,CAAAiJ,QAAA,CAqqCsD8G,oBAAoB,GArqC5E/P,EAAE,CAAAiJ,QAAA,CAqqCuFjJ,EAAE,CAAC+V,MAAM,GArqClG/V,EAAE,CAAAiJ,QAAA,CAqqC6GmQ,gBAAgB,GArqC/HpZ,EAAE,CAAAiJ,QAAA,CAqqC0IlJ,QAAQ,GArqCpJC,EAAE,CAAAiJ,QAAA,CAqqC+JwP,yBAAyB;IAAA,CAA6D;EAAE;EACzV;IAAS,IAAI,CAACvP,KAAK,kBAtqC6ElJ,EAAE,CAAAmJ,kBAAA;MAAAC,KAAA,EAsqCYsQ,4BAA4B;MAAArQ,OAAA,EAA5BqQ,4BAA4B,CAAA5Q,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AACvK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAxqCoGvJ,EAAE,CAAAwJ,iBAAA,CAwqCXkQ,4BAA4B,EAAc,CAAC;IAC1HjQ,IAAI,EAAEtJ,UAAU;IAChBuJ,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAEsG;EAAqB,CAAC,EAAE;IAAEtG,IAAI,EAAEzJ,EAAE,CAAC+V;EAAO,CAAC,EAAE;IAAEtM,IAAI,EAAE2P;EAAiB,CAAC,EAAE;IAAE3P,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAChIH,IAAI,EAAErJ,MAAM;MACZsJ,IAAI,EAAE,CAAC3J,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE0J,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCH,IAAI,EAAE7I;IACV,CAAC,EAAE;MACC6I,IAAI,EAAErJ,MAAM;MACZsJ,IAAI,EAAE,CAAC+O,yBAAyB;IACpC,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA,SAASoB,+BAA+BA,CAACrM,KAAK,EAAE;EAC5C;EACA;EACA;EACA;EACA;EACA,OAAOA,KAAK,CAACsM,OAAO,KAAK,CAAC,IAAItM,KAAK,CAACuM,MAAM,KAAK,CAAC;AACpD;AACA;AACA,SAASC,gCAAgCA,CAACxM,KAAK,EAAE;EAC7C,MAAMyM,KAAK,GAAIzM,KAAK,CAAC0M,OAAO,IAAI1M,KAAK,CAAC0M,OAAO,CAAC,CAAC,CAAC,IAAM1M,KAAK,CAAC2M,cAAc,IAAI3M,KAAK,CAAC2M,cAAc,CAAC,CAAC,CAAE;EACtG;EACA;EACA;EACA;EACA,OAAQ,CAAC,CAACF,KAAK,IACXA,KAAK,CAACG,UAAU,KAAK,CAAC,CAAC,KACtBH,KAAK,CAACI,OAAO,IAAI,IAAI,IAAIJ,KAAK,CAACI,OAAO,KAAK,CAAC,CAAC,KAC7CJ,KAAK,CAACK,OAAO,IAAI,IAAI,IAAIL,KAAK,CAACK,OAAO,KAAK,CAAC,CAAC;AACtD;;AAEA;AACA;AACA;AACA;AACA,MAAMC,+BAA+B,GAAG,IAAI5Z,cAAc,CAAC,qCAAqC,CAAC;AACjG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6Z,uCAAuC,GAAG;EAC5CC,UAAU,EAAE,CAACjY,GAAG,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,KAAK;AACpD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8X,eAAe,GAAG,GAAG;AAC3B;AACA;AACA;AACA;AACA,MAAMC,4BAA4B,GAAGxZ,+BAA+B,CAAC;EACjEyZ,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,CAAC;EACxB;EACA,IAAIC,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACC,SAAS,CAACxI,KAAK;EAC/B;EACArN,WAAWA,CAACE,SAAS,EAAE4V,MAAM,EAAEC,QAAQ,EAAElH,OAAO,EAAE;IAC9C,IAAI,CAAC3O,SAAS,GAAGA,SAAS;IAC1B;AACR;AACA;AACA;IACQ,IAAI,CAAC8V,iBAAiB,GAAG,IAAI;IAC7B;IACA,IAAI,CAACH,SAAS,GAAG,IAAIxZ,eAAe,CAAC,IAAI,CAAC;IAC1C;AACR;AACA;AACA;IACQ,IAAI,CAAC4Z,YAAY,GAAG,CAAC;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAI7N,KAAK,IAAK;MACzB;MACA;MACA,IAAI,IAAI,CAAC8N,QAAQ,EAAEb,UAAU,EAAExW,IAAI,CAACwJ,OAAO,IAAIA,OAAO,KAAKD,KAAK,CAACC,OAAO,CAAC,EAAE;QACvE;MACJ;MACA,IAAI,CAACuN,SAAS,CAAC1N,IAAI,CAAC,UAAU,CAAC;MAC/B,IAAI,CAAC6N,iBAAiB,GAAG/Z,eAAe,CAACoM,KAAK,CAAC;IACnD,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAI,CAAC+N,YAAY,GAAI/N,KAAK,IAAK;MAC3B;MACA;MACA;MACA,IAAIgO,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACL,YAAY,GAAGV,eAAe,EAAE;QAClD;MACJ;MACA;MACA;MACA,IAAI,CAACM,SAAS,CAAC1N,IAAI,CAACuM,+BAA+B,CAACrM,KAAK,CAAC,GAAG,UAAU,GAAG,OAAO,CAAC;MAClF,IAAI,CAAC2N,iBAAiB,GAAG/Z,eAAe,CAACoM,KAAK,CAAC;IACnD,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAI,CAACkO,aAAa,GAAIlO,KAAK,IAAK;MAC5B;MACA;MACA,IAAIwM,gCAAgC,CAACxM,KAAK,CAAC,EAAE;QACzC,IAAI,CAACwN,SAAS,CAAC1N,IAAI,CAAC,UAAU,CAAC;QAC/B;MACJ;MACA;MACA;MACA,IAAI,CAAC8N,YAAY,GAAGI,IAAI,CAACC,GAAG,CAAC,CAAC;MAC9B,IAAI,CAACT,SAAS,CAAC1N,IAAI,CAAC,OAAO,CAAC;MAC5B,IAAI,CAAC6N,iBAAiB,GAAG/Z,eAAe,CAACoM,KAAK,CAAC;IACnD,CAAC;IACD,IAAI,CAAC8N,QAAQ,GAAG;MACZ,GAAGd,uCAAuC;MAC1C,GAAGxG;IACP,CAAC;IACD;IACA,IAAI,CAAC2H,gBAAgB,GAAG,IAAI,CAACX,SAAS,CAACrO,IAAI,CAACzJ,IAAI,CAAC,CAAC,CAAC,CAAC;IACpD,IAAI,CAAC0Y,eAAe,GAAG,IAAI,CAACD,gBAAgB,CAAChP,IAAI,CAACxJ,oBAAoB,CAAC,CAAC,CAAC;IACzE;IACA;IACA,IAAIkC,SAAS,CAAC8C,SAAS,EAAE;MACrB8S,MAAM,CAACxH,iBAAiB,CAAC,MAAM;QAC3ByH,QAAQ,CAACvH,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC0H,UAAU,EAAEV,4BAA4B,CAAC;QACnFO,QAAQ,CAACvH,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC4H,YAAY,EAAEZ,4BAA4B,CAAC;QACvFO,QAAQ,CAACvH,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC+H,aAAa,EAAEf,4BAA4B,CAAC;MAC7F,CAAC,CAAC;IACN;EACJ;EACA1T,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC+T,SAAS,CAACjM,QAAQ,CAAC,CAAC;IACzB,IAAI,IAAI,CAAC1J,SAAS,CAAC8C,SAAS,EAAE;MAC1B+S,QAAQ,CAAC1H,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC6H,UAAU,EAAEV,4BAA4B,CAAC;MACtFO,QAAQ,CAAC1H,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC+H,YAAY,EAAEZ,4BAA4B,CAAC;MAC1FO,QAAQ,CAAC1H,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAACkI,aAAa,EAAEf,4BAA4B,CAAC;IAChG;EACJ;EACA;IAAS,IAAI,CAAC7R,IAAI,YAAA+S,8BAAA7S,CAAA;MAAA,YAAAA,CAAA,IAAwF8R,qBAAqB,EA31C/B9a,EAAE,CAAAiJ,QAAA,CA21C+CjI,EAAE,CAACC,QAAQ,GA31C5DjB,EAAE,CAAAiJ,QAAA,CA21CuEjJ,EAAE,CAAC+V,MAAM,GA31ClF/V,EAAE,CAAAiJ,QAAA,CA21C6FlJ,QAAQ,GA31CvGC,EAAE,CAAAiJ,QAAA,CA21CkHsR,+BAA+B;IAAA,CAA6D;EAAE;EAClT;IAAS,IAAI,CAACrR,KAAK,kBA51C6ElJ,EAAE,CAAAmJ,kBAAA;MAAAC,KAAA,EA41CY0R,qBAAqB;MAAAzR,OAAA,EAArByR,qBAAqB,CAAAhS,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AAChK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA91CoGvJ,EAAE,CAAAwJ,iBAAA,CA81CXsR,qBAAqB,EAAc,CAAC;IACnHrR,IAAI,EAAEtJ,UAAU;IAChBuJ,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAEzI,EAAE,CAACC;EAAS,CAAC,EAAE;IAAEwI,IAAI,EAAEzJ,EAAE,CAAC+V;EAAO,CAAC,EAAE;IAAEtM,IAAI,EAAEqS,QAAQ;IAAElS,UAAU,EAAE,CAAC;MAC1FH,IAAI,EAAErJ,MAAM;MACZsJ,IAAI,EAAE,CAAC3J,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE0J,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCH,IAAI,EAAE7I;IACV,CAAC,EAAE;MACC6I,IAAI,EAAErJ,MAAM;MACZsJ,IAAI,EAAE,CAAC6Q,+BAA+B;IAC1C,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAMwB,4BAA4B,GAAG,IAAIpb,cAAc,CAAC,sBAAsB,EAAE;EAC5E2I,UAAU,EAAE,MAAM;EAClBD,OAAO,EAAE2S;AACb,CAAC,CAAC;AACF;AACA,SAASA,oCAAoCA,CAAA,EAAG;EAC5C,OAAO,IAAI;AACf;AACA;AACA,MAAMC,8BAA8B,GAAG,IAAItb,cAAc,CAAC,gCAAgC,CAAC;AAE3F,IAAIub,SAAS,GAAG,CAAC;AACjB,MAAMC,aAAa,CAAC;EAChBhX,WAAWA,CAACiX,YAAY,EAAEtJ,OAAO,EAAE1N,SAAS,EAAEiX,eAAe,EAAE;IAC3D,IAAI,CAACvJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACuJ,eAAe,GAAGA,eAAe;IACtC;IACA;IACA;IACA,IAAI,CAACjX,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACkX,YAAY,GAAGF,YAAY,IAAI,IAAI,CAACG,kBAAkB,CAAC,CAAC;EACjE;EACAC,QAAQA,CAAC5W,OAAO,EAAE,GAAG8D,IAAI,EAAE;IACvB,MAAM+S,cAAc,GAAG,IAAI,CAACJ,eAAe;IAC3C,IAAIK,UAAU;IACd,IAAIC,QAAQ;IACZ,IAAIjT,IAAI,CAACjF,MAAM,KAAK,CAAC,IAAI,OAAOiF,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MAClDiT,QAAQ,GAAGjT,IAAI,CAAC,CAAC,CAAC;IACtB,CAAC,MACI;MACD,CAACgT,UAAU,EAAEC,QAAQ,CAAC,GAAGjT,IAAI;IACjC;IACA,IAAI,CAACpC,KAAK,CAAC,CAAC;IACZsV,YAAY,CAAC,IAAI,CAACC,gBAAgB,CAAC;IACnC,IAAI,CAACH,UAAU,EAAE;MACbA,UAAU,GACND,cAAc,IAAIA,cAAc,CAACC,UAAU,GAAGD,cAAc,CAACC,UAAU,GAAG,QAAQ;IAC1F;IACA,IAAIC,QAAQ,IAAI,IAAI,IAAIF,cAAc,EAAE;MACpCE,QAAQ,GAAGF,cAAc,CAACE,QAAQ;IACtC;IACA;IACA,IAAI,CAACL,YAAY,CAAClY,YAAY,CAAC,WAAW,EAAEsY,UAAU,CAAC;IACvD,IAAI,IAAI,CAACJ,YAAY,CAACzY,EAAE,EAAE;MACtB,IAAI,CAACiZ,wBAAwB,CAAC,IAAI,CAACR,YAAY,CAACzY,EAAE,CAAC;IACvD;IACA;IACA;IACA;IACA;IACA;IACA,OAAO,IAAI,CAACiP,OAAO,CAACW,iBAAiB,CAAC,MAAM;MACxC,IAAI,CAAC,IAAI,CAACsJ,eAAe,EAAE;QACvB,IAAI,CAACA,eAAe,GAAG,IAAI9I,OAAO,CAACC,OAAO,IAAK,IAAI,CAAC8I,eAAe,GAAG9I,OAAQ,CAAC;MACnF;MACA0I,YAAY,CAAC,IAAI,CAACC,gBAAgB,CAAC;MACnC,IAAI,CAACA,gBAAgB,GAAG3D,UAAU,CAAC,MAAM;QACrC,IAAI,CAACoD,YAAY,CAAC9U,WAAW,GAAG5B,OAAO;QACvC,IAAI,OAAO+W,QAAQ,KAAK,QAAQ,EAAE;UAC9B,IAAI,CAACE,gBAAgB,GAAG3D,UAAU,CAAC,MAAM,IAAI,CAAC5R,KAAK,CAAC,CAAC,EAAEqV,QAAQ,CAAC;QACpE;QACA;QACA;QACA,IAAI,CAACK,eAAe,GAAG,CAAC;QACxB,IAAI,CAACD,eAAe,GAAG,IAAI,CAACC,eAAe,GAAGrT,SAAS;MAC3D,CAAC,EAAE,GAAG,CAAC;MACP,OAAO,IAAI,CAACoT,eAAe;IAC/B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIzV,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACgV,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAAC9U,WAAW,GAAG,EAAE;IACtC;EACJ;EACAP,WAAWA,CAAA,EAAG;IACV2V,YAAY,CAAC,IAAI,CAACC,gBAAgB,CAAC;IACnC,IAAI,CAACP,YAAY,EAAEtV,MAAM,CAAC,CAAC;IAC3B,IAAI,CAACsV,YAAY,GAAG,IAAI;IACxB,IAAI,CAACU,eAAe,GAAG,CAAC;IACxB,IAAI,CAACD,eAAe,GAAG,IAAI,CAACC,eAAe,GAAGrT,SAAS;EAC3D;EACA4S,kBAAkBA,CAAA,EAAG;IACjB,MAAMU,YAAY,GAAG,4BAA4B;IACjD,MAAMC,gBAAgB,GAAG,IAAI,CAAC9X,SAAS,CAAC+X,sBAAsB,CAACF,YAAY,CAAC;IAC5E,MAAMG,MAAM,GAAG,IAAI,CAAChY,SAAS,CAACmC,aAAa,CAAC,KAAK,CAAC;IAClD;IACA,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8V,gBAAgB,CAACzY,MAAM,EAAE2C,CAAC,EAAE,EAAE;MAC9C8V,gBAAgB,CAAC9V,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC;IAChC;IACAoW,MAAM,CAACnV,SAAS,CAACC,GAAG,CAAC+U,YAAY,CAAC;IAClCG,MAAM,CAACnV,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAC3CkV,MAAM,CAAChZ,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAC1CgZ,MAAM,CAAChZ,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC;IAC1CgZ,MAAM,CAACvZ,EAAE,GAAG,sBAAsBqY,SAAS,EAAE,EAAE;IAC/C,IAAI,CAAC9W,SAAS,CAACgD,IAAI,CAACV,WAAW,CAAC0V,MAAM,CAAC;IACvC,OAAOA,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;EACIN,wBAAwBA,CAACjZ,EAAE,EAAE;IACzB;IACA;IACA;IACA;IACA;IACA;IACA,MAAMwZ,MAAM,GAAG,IAAI,CAACjY,SAAS,CAAC+B,gBAAgB,CAAC,mDAAmD,CAAC;IACnG,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiW,MAAM,CAAC5Y,MAAM,EAAE2C,CAAC,EAAE,EAAE;MACpC,MAAMkW,KAAK,GAAGD,MAAM,CAACjW,CAAC,CAAC;MACvB,MAAMmW,QAAQ,GAAGD,KAAK,CAAC1Y,YAAY,CAAC,WAAW,CAAC;MAChD,IAAI,CAAC2Y,QAAQ,EAAE;QACXD,KAAK,CAAClZ,YAAY,CAAC,WAAW,EAAEP,EAAE,CAAC;MACvC,CAAC,MACI,IAAI0Z,QAAQ,CAAChV,OAAO,CAAC1E,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;QAClCyZ,KAAK,CAAClZ,YAAY,CAAC,WAAW,EAAEmZ,QAAQ,GAAG,GAAG,GAAG1Z,EAAE,CAAC;MACxD;IACJ;EACJ;EACA;IAAS,IAAI,CAACiF,IAAI,YAAA0U,sBAAAxU,CAAA;MAAA,YAAAA,CAAA,IAAwFmT,aAAa,EAz+CvBnc,EAAE,CAAAiJ,QAAA,CAy+CuC8S,4BAA4B,MAz+CrE/b,EAAE,CAAAiJ,QAAA,CAy+CgGjJ,EAAE,CAAC+V,MAAM,GAz+C3G/V,EAAE,CAAAiJ,QAAA,CAy+CsHlJ,QAAQ,GAz+ChIC,EAAE,CAAAiJ,QAAA,CAy+C2IgT,8BAA8B;IAAA,CAA6D;EAAE;EAC1U;IAAS,IAAI,CAAC/S,KAAK,kBA1+C6ElJ,EAAE,CAAAmJ,kBAAA;MAAAC,KAAA,EA0+CY+S,aAAa;MAAA9S,OAAA,EAAb8S,aAAa,CAAArT,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AACxJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA5+CoGvJ,EAAE,CAAAwJ,iBAAA,CA4+CX2S,aAAa,EAAc,CAAC;IAC3G1S,IAAI,EAAEtJ,UAAU;IAChBuJ,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CH,IAAI,EAAE7I;IACV,CAAC,EAAE;MACC6I,IAAI,EAAErJ,MAAM;MACZsJ,IAAI,EAAE,CAACqS,4BAA4B;IACvC,CAAC;EAAE,CAAC,EAAE;IAAEtS,IAAI,EAAEzJ,EAAE,CAAC+V;EAAO,CAAC,EAAE;IAAEtM,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MACvDH,IAAI,EAAErJ,MAAM;MACZsJ,IAAI,EAAE,CAAC3J,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE0J,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCH,IAAI,EAAE7I;IACV,CAAC,EAAE;MACC6I,IAAI,EAAErJ,MAAM;MACZsJ,IAAI,EAAE,CAACuS,8BAA8B;IACzC,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA;AACA;AACA;AACA,MAAMwB,WAAW,CAAC;EACd;EACA,IAAIf,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACgB,WAAW;EAC3B;EACA,IAAIhB,UAAUA,CAAClK,KAAK,EAAE;IAClB,IAAI,CAACkL,WAAW,GAAGlL,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,WAAW,GAAGA,KAAK,GAAG,QAAQ;IAC9E,IAAI,IAAI,CAACkL,WAAW,KAAK,KAAK,EAAE;MAC5B,IAAI,IAAI,CAACC,aAAa,EAAE;QACpB,IAAI,CAACA,aAAa,CAACjR,WAAW,CAAC,CAAC;QAChC,IAAI,CAACiR,aAAa,GAAG,IAAI;MAC7B;IACJ,CAAC,MACI,IAAI,CAAC,IAAI,CAACA,aAAa,EAAE;MAC1B,IAAI,CAACA,aAAa,GAAG,IAAI,CAAC7K,OAAO,CAACW,iBAAiB,CAAC,MAAM;QACtD,OAAO,IAAI,CAACmK,gBAAgB,CAACC,OAAO,CAAC,IAAI,CAAC3H,WAAW,CAAC,CAAC7K,SAAS,CAAC,MAAM;UACnE;UACA,MAAMyS,WAAW,GAAG,IAAI,CAAC5H,WAAW,CAACI,aAAa,CAAC9O,WAAW;UAC9D;UACA;UACA,IAAIsW,WAAW,KAAK,IAAI,CAACC,sBAAsB,EAAE;YAC7C,IAAI,CAACC,cAAc,CAACxB,QAAQ,CAACsB,WAAW,EAAE,IAAI,CAACJ,WAAW,EAAE,IAAI,CAACf,QAAQ,CAAC;YAC1E,IAAI,CAACoB,sBAAsB,GAAGD,WAAW;UAC7C;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ;EACA3Y,WAAWA,CAAC+Q,WAAW,EAAE8H,cAAc,EAAEJ,gBAAgB,EAAE9K,OAAO,EAAE;IAChE,IAAI,CAACoD,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC8H,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACJ,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAC9K,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC4K,WAAW,GAAG,QAAQ;EAC/B;EACAzW,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC0W,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACjR,WAAW,CAAC,CAAC;IACpC;EACJ;EACA;IAAS,IAAI,CAAC5D,IAAI,YAAAmV,oBAAAjV,CAAA;MAAA,YAAAA,CAAA,IAAwFyU,WAAW,EAziDrBzd,EAAE,CAAA+W,iBAAA,CAyiDqC/W,EAAE,CAACgX,UAAU,GAziDpDhX,EAAE,CAAA+W,iBAAA,CAyiD+DoF,aAAa,GAziD9Enc,EAAE,CAAA+W,iBAAA,CAyiDyF1T,IAAI,CAAC6a,eAAe,GAziD/Gle,EAAE,CAAA+W,iBAAA,CAyiD0H/W,EAAE,CAAC+V,MAAM;IAAA,CAA4C;EAAE;EACnR;IAAS,IAAI,CAACkB,IAAI,kBA1iD8EjX,EAAE,CAAAkX,iBAAA;MAAAzN,IAAA,EA0iDJgU,WAAW;MAAAtG,SAAA;MAAAC,MAAA;QAAAsF,UAAA,GA1iDT1c,EAAE,CAAAqX,YAAA,CAAA8G,IAAA;QAAAxB,QAAA,GAAF3c,EAAE,CAAAqX,YAAA,CAAA8G,IAAA;MAAA;MAAA5G,QAAA;MAAAC,UAAA;IAAA,EA0iDwM;EAAE;AAChT;AACA;EAAA,QAAAjO,SAAA,oBAAAA,SAAA,KA5iDoGvJ,EAAE,CAAAwJ,iBAAA,CA4iDXiU,WAAW,EAAc,CAAC;IACzGhU,IAAI,EAAEhJ,SAAS;IACfiJ,IAAI,EAAE,CAAC;MACCkO,QAAQ,EAAE,eAAe;MACzBL,QAAQ,EAAE,aAAa;MACvBC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE/N,IAAI,EAAEzJ,EAAE,CAACgX;EAAW,CAAC,EAAE;IAAEvN,IAAI,EAAE0S;EAAc,CAAC,EAAE;IAAE1S,IAAI,EAAEpG,IAAI,CAAC6a;EAAgB,CAAC,EAAE;IAAEzU,IAAI,EAAEzJ,EAAE,CAAC+V;EAAO,CAAC,CAAC,EAAkB;IAAE2G,UAAU,EAAE,CAAC;MAC1JjT,IAAI,EAAE/I,KAAK;MACXgJ,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC,CAAC;IAAEiT,QAAQ,EAAE,CAAC;MACXlT,IAAI,EAAE/I,KAAK;MACXgJ,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,IAAI0U,yBAAyB;AAC7B,CAAC,UAAUA,yBAAyB,EAAE;EAClC;AACJ;AACA;AACA;AACA;EACIA,yBAAyB,CAACA,yBAAyB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACnF;AACJ;AACA;AACA;EACIA,yBAAyB,CAACA,yBAAyB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;AACrF,CAAC,EAAEA,yBAAyB,KAAKA,yBAAyB,GAAG,CAAC,CAAC,CAAC,CAAC;AACjE;AACA,MAAMC,6BAA6B,GAAG,IAAI1d,cAAc,CAAC,mCAAmC,CAAC;AAC7F;AACA;AACA;AACA;AACA,MAAM2d,2BAA2B,GAAGnd,+BAA+B,CAAC;EAChEyZ,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA,MAAM0D,YAAY,CAAC;EACfpZ,WAAWA,CAAC2N,OAAO,EAAEzN,SAAS,EAAEmZ,sBAAsB,EACtD;EACAtD,QAAQ,EAAElH,OAAO,EAAE;IACf,IAAI,CAAClB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACzN,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACmZ,sBAAsB,GAAGA,sBAAsB;IACpD;IACA,IAAI,CAAC/O,OAAO,GAAG,IAAI;IACnB;IACA,IAAI,CAACgP,cAAc,GAAG,KAAK;IAC3B;AACR;AACA;AACA;IACQ,IAAI,CAACC,2BAA2B,GAAG,KAAK;IACxC;IACA,IAAI,CAACC,YAAY,GAAG,IAAIpZ,GAAG,CAAC,CAAC;IAC7B;IACA,IAAI,CAACqZ,sBAAsB,GAAG,CAAC;IAC/B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,2BAA2B,GAAG,IAAItZ,GAAG,CAAC,CAAC;IAC5C;AACR;AACA;AACA;IACQ,IAAI,CAACuZ,oBAAoB,GAAG,MAAM;MAC9B;MACA;MACA,IAAI,CAACL,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACM,qBAAqB,GAAGzN,MAAM,CAAC4H,UAAU,CAAC,MAAO,IAAI,CAACuF,cAAc,GAAG,KAAM,CAAC;IACvF,CAAC;IACD;IACA,IAAI,CAACO,0BAA0B,GAAG,IAAI1d,OAAO,CAAC,CAAC;IAC/C;AACR;AACA;AACA;IACQ,IAAI,CAAC2d,6BAA6B,GAAIzR,KAAK,IAAK;MAC5C,MAAMsL,MAAM,GAAG1X,eAAe,CAACoM,KAAK,CAAC;MACrC;MACA,KAAK,IAAInF,OAAO,GAAGyQ,MAAM,EAAEzQ,OAAO,EAAEA,OAAO,GAAGA,OAAO,CAAC6W,aAAa,EAAE;QACjE,IAAI1R,KAAK,CAAC/D,IAAI,KAAK,OAAO,EAAE;UACxB,IAAI,CAAC0V,QAAQ,CAAC3R,KAAK,EAAEnF,OAAO,CAAC;QACjC,CAAC,MACI;UACD,IAAI,CAAC+W,OAAO,CAAC5R,KAAK,EAAEnF,OAAO,CAAC;QAChC;MACJ;IACJ,CAAC;IACD,IAAI,CAACjD,SAAS,GAAG8V,QAAQ;IACzB,IAAI,CAACmE,cAAc,GAAGrL,OAAO,EAAEsL,aAAa,IAAIlB,yBAAyB,CAACmB,SAAS;EACvF;EACAC,OAAOA,CAACnX,OAAO,EAAEoX,aAAa,GAAG,KAAK,EAAE;IACpC,MAAMnJ,aAAa,GAAG/S,aAAa,CAAC8E,OAAO,CAAC;IAC5C;IACA,IAAI,CAAC,IAAI,CAAChD,SAAS,CAAC8C,SAAS,IAAImO,aAAa,CAAC1N,QAAQ,KAAK,CAAC,EAAE;MAC3D;MACA,OAAOnH,EAAE,CAAC,CAAC;IACf;IACA;IACA;IACA;IACA,MAAMie,QAAQ,GAAGre,cAAc,CAACiV,aAAa,CAAC,IAAI,IAAI,CAACqJ,YAAY,CAAC,CAAC;IACrE,MAAMC,UAAU,GAAG,IAAI,CAACjB,YAAY,CAAC9X,GAAG,CAACyP,aAAa,CAAC;IACvD;IACA,IAAIsJ,UAAU,EAAE;MACZ,IAAIH,aAAa,EAAE;QACf;QACA;QACA;QACAG,UAAU,CAACH,aAAa,GAAG,IAAI;MACnC;MACA,OAAOG,UAAU,CAACC,OAAO;IAC7B;IACA;IACA,MAAMC,IAAI,GAAG;MACTL,aAAa,EAAEA,aAAa;MAC5BI,OAAO,EAAE,IAAIve,OAAO,CAAC,CAAC;MACtBoe;IACJ,CAAC;IACD,IAAI,CAACf,YAAY,CAACzY,GAAG,CAACoQ,aAAa,EAAEwJ,IAAI,CAAC;IAC1C,IAAI,CAACC,wBAAwB,CAACD,IAAI,CAAC;IACnC,OAAOA,IAAI,CAACD,OAAO;EACvB;EACAG,cAAcA,CAAC3X,OAAO,EAAE;IACpB,MAAMiO,aAAa,GAAG/S,aAAa,CAAC8E,OAAO,CAAC;IAC5C,MAAM4X,WAAW,GAAG,IAAI,CAACtB,YAAY,CAAC9X,GAAG,CAACyP,aAAa,CAAC;IACxD,IAAI2J,WAAW,EAAE;MACbA,WAAW,CAACJ,OAAO,CAAC9Q,QAAQ,CAAC,CAAC;MAC9B,IAAI,CAACmR,WAAW,CAAC5J,aAAa,CAAC;MAC/B,IAAI,CAACqI,YAAY,CAAChX,MAAM,CAAC2O,aAAa,CAAC;MACvC,IAAI,CAAC6J,sBAAsB,CAACF,WAAW,CAAC;IAC5C;EACJ;EACAG,QAAQA,CAAC/X,OAAO,EAAEsH,MAAM,EAAEqE,OAAO,EAAE;IAC/B,MAAMsC,aAAa,GAAG/S,aAAa,CAAC8E,OAAO,CAAC;IAC5C,MAAMgY,cAAc,GAAG,IAAI,CAACV,YAAY,CAAC,CAAC,CAACxG,aAAa;IACxD;IACA;IACA;IACA,IAAI7C,aAAa,KAAK+J,cAAc,EAAE;MAClC,IAAI,CAACC,uBAAuB,CAAChK,aAAa,CAAC,CAACiK,OAAO,CAAC,CAAC,CAACC,cAAc,EAAEV,IAAI,CAAC,KAAK,IAAI,CAACW,cAAc,CAACD,cAAc,EAAE7Q,MAAM,EAAEmQ,IAAI,CAAC,CAAC;IACtI,CAAC,MACI;MACD,IAAI,CAACY,UAAU,CAAC/Q,MAAM,CAAC;MACvB;MACA,IAAI,OAAO2G,aAAa,CAAC1G,KAAK,KAAK,UAAU,EAAE;QAC3C0G,aAAa,CAAC1G,KAAK,CAACoE,OAAO,CAAC;MAChC;IACJ;EACJ;EACA/M,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC0X,YAAY,CAAC4B,OAAO,CAAC,CAACI,KAAK,EAAEtY,OAAO,KAAK,IAAI,CAAC2X,cAAc,CAAC3X,OAAO,CAAC,CAAC;EAC/E;EACA;EACAsX,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACva,SAAS,IAAI8V,QAAQ;EACrC;EACA;EACA0F,UAAUA,CAAA,EAAG;IACT,MAAMC,GAAG,GAAG,IAAI,CAAClB,YAAY,CAAC,CAAC;IAC/B,OAAOkB,GAAG,CAACxO,WAAW,IAAIf,MAAM;EACpC;EACAwP,eAAeA,CAACC,gBAAgB,EAAE;IAC9B,IAAI,IAAI,CAACtR,OAAO,EAAE;MACd;MACA;MACA,IAAI,IAAI,CAACiP,2BAA2B,EAAE;QAClC,OAAO,IAAI,CAACsC,0BAA0B,CAACD,gBAAgB,CAAC,GAAG,OAAO,GAAG,SAAS;MAClF,CAAC,MACI;QACD,OAAO,IAAI,CAACtR,OAAO;MACvB;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACgP,cAAc,IAAI,IAAI,CAACwC,gBAAgB,EAAE;MAC9C,OAAO,IAAI,CAACA,gBAAgB;IAChC;IACA;IACA;IACA;IACA;IACA,IAAIF,gBAAgB,IAAI,IAAI,CAACG,gCAAgC,CAACH,gBAAgB,CAAC,EAAE;MAC7E,OAAO,OAAO;IAClB;IACA,OAAO,SAAS;EACpB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,0BAA0BA,CAACD,gBAAgB,EAAE;IACzC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,OAAQ,IAAI,CAAC1B,cAAc,KAAKjB,yBAAyB,CAAC+C,QAAQ,IAC9D,CAAC,CAACJ,gBAAgB,EAAE/H,QAAQ,CAAC,IAAI,CAACwF,sBAAsB,CAACrD,iBAAiB,CAAC;EACnF;EACA;AACJ;AACA;AACA;AACA;EACI+E,WAAWA,CAAC7X,OAAO,EAAEsH,MAAM,EAAE;IACzBtH,OAAO,CAACJ,SAAS,CAACmZ,MAAM,CAAC,aAAa,EAAE,CAAC,CAACzR,MAAM,CAAC;IACjDtH,OAAO,CAACJ,SAAS,CAACmZ,MAAM,CAAC,mBAAmB,EAAEzR,MAAM,KAAK,OAAO,CAAC;IACjEtH,OAAO,CAACJ,SAAS,CAACmZ,MAAM,CAAC,sBAAsB,EAAEzR,MAAM,KAAK,UAAU,CAAC;IACvEtH,OAAO,CAACJ,SAAS,CAACmZ,MAAM,CAAC,mBAAmB,EAAEzR,MAAM,KAAK,OAAO,CAAC;IACjEtH,OAAO,CAACJ,SAAS,CAACmZ,MAAM,CAAC,qBAAqB,EAAEzR,MAAM,KAAK,SAAS,CAAC;EACzE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI+Q,UAAUA,CAAC/Q,MAAM,EAAE0R,iBAAiB,GAAG,KAAK,EAAE;IAC1C,IAAI,CAACvO,OAAO,CAACW,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAAChE,OAAO,GAAGE,MAAM;MACrB,IAAI,CAAC+O,2BAA2B,GAAG/O,MAAM,KAAK,OAAO,IAAI0R,iBAAiB;MAC1E;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAAChC,cAAc,KAAKjB,yBAAyB,CAACmB,SAAS,EAAE;QAC7D3C,YAAY,CAAC,IAAI,CAAC0E,gBAAgB,CAAC;QACnC,MAAMC,EAAE,GAAG,IAAI,CAAC7C,2BAA2B,GAAGhE,eAAe,GAAG,CAAC;QACjE,IAAI,CAAC4G,gBAAgB,GAAGpI,UAAU,CAAC,MAAO,IAAI,CAACzJ,OAAO,GAAG,IAAK,EAAE8R,EAAE,CAAC;MACvE;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIpC,QAAQA,CAAC3R,KAAK,EAAEnF,OAAO,EAAE;IACrB;IACA;IACA;IACA;IACA;IACA;IACA,MAAM4X,WAAW,GAAG,IAAI,CAACtB,YAAY,CAAC9X,GAAG,CAACwB,OAAO,CAAC;IAClD,MAAM0Y,gBAAgB,GAAG3f,eAAe,CAACoM,KAAK,CAAC;IAC/C,IAAI,CAACyS,WAAW,IAAK,CAACA,WAAW,CAACR,aAAa,IAAIpX,OAAO,KAAK0Y,gBAAiB,EAAE;MAC9E;IACJ;IACA,IAAI,CAACN,cAAc,CAACpY,OAAO,EAAE,IAAI,CAACyY,eAAe,CAACC,gBAAgB,CAAC,EAAEd,WAAW,CAAC;EACrF;EACA;AACJ;AACA;AACA;AACA;EACIb,OAAOA,CAAC5R,KAAK,EAAEnF,OAAO,EAAE;IACpB;IACA;IACA,MAAM4X,WAAW,GAAG,IAAI,CAACtB,YAAY,CAAC9X,GAAG,CAACwB,OAAO,CAAC;IAClD,IAAI,CAAC4X,WAAW,IACXA,WAAW,CAACR,aAAa,IACtBjS,KAAK,CAACgU,aAAa,YAAYC,IAAI,IACnCpZ,OAAO,CAAC2Q,QAAQ,CAACxL,KAAK,CAACgU,aAAa,CAAE,EAAE;MAC5C;IACJ;IACA,IAAI,CAACtB,WAAW,CAAC7X,OAAO,CAAC;IACzB,IAAI,CAACqZ,WAAW,CAACzB,WAAW,EAAE,IAAI,CAAC;EACvC;EACAyB,WAAWA,CAAC5B,IAAI,EAAEnQ,MAAM,EAAE;IACtB,IAAImQ,IAAI,CAACD,OAAO,CAAC8B,SAAS,CAACld,MAAM,EAAE;MAC/B,IAAI,CAACqO,OAAO,CAAC8O,GAAG,CAAC,MAAM9B,IAAI,CAACD,OAAO,CAACvS,IAAI,CAACqC,MAAM,CAAC,CAAC;IACrD;EACJ;EACAoQ,wBAAwBA,CAACE,WAAW,EAAE;IAClC,IAAI,CAAC,IAAI,CAAC5a,SAAS,CAAC8C,SAAS,EAAE;MAC3B;IACJ;IACA,MAAMuX,QAAQ,GAAGO,WAAW,CAACP,QAAQ;IACrC,MAAMmC,sBAAsB,GAAG,IAAI,CAAChD,2BAA2B,CAAChY,GAAG,CAAC6Y,QAAQ,CAAC,IAAI,CAAC;IAClF,IAAI,CAACmC,sBAAsB,EAAE;MACzB,IAAI,CAAC/O,OAAO,CAACW,iBAAiB,CAAC,MAAM;QACjCiM,QAAQ,CAAC/L,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACsL,6BAA6B,EAAEX,2BAA2B,CAAC;QACnGoB,QAAQ,CAAC/L,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAACsL,6BAA6B,EAAEX,2BAA2B,CAAC;MACtG,CAAC,CAAC;IACN;IACA,IAAI,CAACO,2BAA2B,CAAC3Y,GAAG,CAACwZ,QAAQ,EAAEmC,sBAAsB,GAAG,CAAC,CAAC;IAC1E;IACA,IAAI,EAAE,IAAI,CAACjD,sBAAsB,KAAK,CAAC,EAAE;MACrC;MACA;MACA,IAAI,CAAC9L,OAAO,CAACW,iBAAiB,CAAC,MAAM;QACjC,MAAMnC,MAAM,GAAG,IAAI,CAACsP,UAAU,CAAC,CAAC;QAChCtP,MAAM,CAACqC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACmL,oBAAoB,CAAC;MAC/D,CAAC,CAAC;MACF;MACA,IAAI,CAACN,sBAAsB,CAAC7C,gBAAgB,CACvChP,IAAI,CAACvJ,SAAS,CAAC,IAAI,CAAC4b,0BAA0B,CAAC,CAAC,CAChD3T,SAAS,CAACyW,QAAQ,IAAI;QACvB,IAAI,CAACpB,UAAU,CAACoB,QAAQ,EAAE,IAAI,CAAC,uBAAuB,CAAC;MAC3D,CAAC,CAAC;IACN;EACJ;EACA3B,sBAAsBA,CAACF,WAAW,EAAE;IAChC,MAAMP,QAAQ,GAAGO,WAAW,CAACP,QAAQ;IACrC,IAAI,IAAI,CAACb,2BAA2B,CAACxY,GAAG,CAACqZ,QAAQ,CAAC,EAAE;MAChD,MAAMmC,sBAAsB,GAAG,IAAI,CAAChD,2BAA2B,CAAChY,GAAG,CAAC6Y,QAAQ,CAAC;MAC7E,IAAImC,sBAAsB,GAAG,CAAC,EAAE;QAC5B,IAAI,CAAChD,2BAA2B,CAAC3Y,GAAG,CAACwZ,QAAQ,EAAEmC,sBAAsB,GAAG,CAAC,CAAC;MAC9E,CAAC,MACI;QACDnC,QAAQ,CAAClM,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACyL,6BAA6B,EAAEX,2BAA2B,CAAC;QACtGoB,QAAQ,CAAClM,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAACyL,6BAA6B,EAAEX,2BAA2B,CAAC;QACrG,IAAI,CAACO,2BAA2B,CAAClX,MAAM,CAAC+X,QAAQ,CAAC;MACrD;IACJ;IACA;IACA,IAAI,CAAC,GAAE,IAAI,CAACd,sBAAsB,EAAE;MAChC,MAAMtN,MAAM,GAAG,IAAI,CAACsP,UAAU,CAAC,CAAC;MAChCtP,MAAM,CAACkC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACsL,oBAAoB,CAAC;MAC9D;MACA,IAAI,CAACE,0BAA0B,CAAC1R,IAAI,CAAC,CAAC;MACtC;MACAsP,YAAY,CAAC,IAAI,CAACmC,qBAAqB,CAAC;MACxCnC,YAAY,CAAC,IAAI,CAAC0E,gBAAgB,CAAC;IACvC;EACJ;EACA;EACAb,cAAcA,CAACpY,OAAO,EAAEsH,MAAM,EAAEsQ,WAAW,EAAE;IACzC,IAAI,CAACC,WAAW,CAAC7X,OAAO,EAAEsH,MAAM,CAAC;IACjC,IAAI,CAAC+R,WAAW,CAACzB,WAAW,EAAEtQ,MAAM,CAAC;IACrC,IAAI,CAACsR,gBAAgB,GAAGtR,MAAM;EAClC;EACA;AACJ;AACA;AACA;AACA;EACI2Q,uBAAuBA,CAACjY,OAAO,EAAE;IAC7B,MAAM0Z,OAAO,GAAG,EAAE;IAClB,IAAI,CAACpD,YAAY,CAAC4B,OAAO,CAAC,CAACT,IAAI,EAAEU,cAAc,KAAK;MAChD,IAAIA,cAAc,KAAKnY,OAAO,IAAKyX,IAAI,CAACL,aAAa,IAAIe,cAAc,CAACxH,QAAQ,CAAC3Q,OAAO,CAAE,EAAE;QACxF0Z,OAAO,CAAC5d,IAAI,CAAC,CAACqc,cAAc,EAAEV,IAAI,CAAC,CAAC;MACxC;IACJ,CAAC,CAAC;IACF,OAAOiC,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;EACIb,gCAAgCA,CAACH,gBAAgB,EAAE;IAC/C,MAAM;MAAE5F,iBAAiB,EAAE6G,gBAAgB;MAAEjH;IAAmB,CAAC,GAAG,IAAI,CAACyD,sBAAsB;IAC/F;IACA;IACA;IACA,IAAIzD,kBAAkB,KAAK,OAAO,IAC9B,CAACiH,gBAAgB,IACjBA,gBAAgB,KAAKjB,gBAAgB,IACpCA,gBAAgB,CAACrQ,QAAQ,KAAK,OAAO,IAAIqQ,gBAAgB,CAACrQ,QAAQ,KAAK,UAAW,IACnFqQ,gBAAgB,CAAChW,QAAQ,EAAE;MAC3B,OAAO,KAAK;IAChB;IACA,MAAMkX,MAAM,GAAGlB,gBAAgB,CAACkB,MAAM;IACtC,IAAIA,MAAM,EAAE;MACR,KAAK,IAAI7a,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6a,MAAM,CAACxd,MAAM,EAAE2C,CAAC,EAAE,EAAE;QACpC,IAAI6a,MAAM,CAAC7a,CAAC,CAAC,CAAC4R,QAAQ,CAACgJ,gBAAgB,CAAC,EAAE;UACtC,OAAO,IAAI;QACf;MACJ;IACJ;IACA,OAAO,KAAK;EAChB;EACA;IAAS,IAAI,CAAClZ,IAAI,YAAAoZ,qBAAAlZ,CAAA;MAAA,YAAAA,CAAA,IAAwFuV,YAAY,EA77DtBve,EAAE,CAAAiJ,QAAA,CA67DsCjJ,EAAE,CAAC+V,MAAM,GA77DjD/V,EAAE,CAAAiJ,QAAA,CA67D4DjI,EAAE,CAACC,QAAQ,GA77DzEjB,EAAE,CAAAiJ,QAAA,CA67DoF6R,qBAAqB,GA77D3G9a,EAAE,CAAAiJ,QAAA,CA67DsHlJ,QAAQ,MA77DhIC,EAAE,CAAAiJ,QAAA,CA67D2JoV,6BAA6B;IAAA,CAA6D;EAAE;EACzV;IAAS,IAAI,CAACnV,KAAK,kBA97D6ElJ,EAAE,CAAAmJ,kBAAA;MAAAC,KAAA,EA87DYmV,YAAY;MAAAlV,OAAA,EAAZkV,YAAY,CAAAzV,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AACvJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAh8DoGvJ,EAAE,CAAAwJ,iBAAA,CAg8DX+U,YAAY,EAAc,CAAC;IAC1G9U,IAAI,EAAEtJ,UAAU;IAChBuJ,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAEzJ,EAAE,CAAC+V;EAAO,CAAC,EAAE;IAAEtM,IAAI,EAAEzI,EAAE,CAACC;EAAS,CAAC,EAAE;IAAEwI,IAAI,EAAEqR;EAAsB,CAAC,EAAE;IAAErR,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC5HH,IAAI,EAAE7I;IACV,CAAC,EAAE;MACC6I,IAAI,EAAErJ,MAAM;MACZsJ,IAAI,EAAE,CAAC3J,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE0J,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCH,IAAI,EAAE7I;IACV,CAAC,EAAE;MACC6I,IAAI,EAAErJ,MAAM;MACZsJ,IAAI,EAAE,CAAC2U,6BAA6B;IACxC,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8D,eAAe,CAAC;EAClBhd,WAAWA,CAAC+Q,WAAW,EAAEkM,aAAa,EAAE;IACpC,IAAI,CAAClM,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACkM,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,cAAc,GAAG,IAAIzhB,YAAY,CAAC,CAAC;EAC5C;EACA,IAAI0hB,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACF,YAAY;EAC5B;EACAG,eAAeA,CAAA,EAAG;IACd,MAAMna,OAAO,GAAG,IAAI,CAAC6N,WAAW,CAACI,aAAa;IAC9C,IAAI,CAACmM,oBAAoB,GAAG,IAAI,CAACL,aAAa,CACzC5C,OAAO,CAACnX,OAAO,EAAEA,OAAO,CAACO,QAAQ,KAAK,CAAC,IAAIP,OAAO,CAAC4H,YAAY,CAAC,wBAAwB,CAAC,CAAC,CAC1F5E,SAAS,CAACsE,MAAM,IAAI;MACrB,IAAI,CAAC0S,YAAY,GAAG1S,MAAM;MAC1B,IAAI,CAAC2S,cAAc,CAACI,IAAI,CAAC/S,MAAM,CAAC;IACpC,CAAC,CAAC;EACN;EACA1I,WAAWA,CAAA,EAAG;IACV,IAAI,CAACmb,aAAa,CAACpC,cAAc,CAAC,IAAI,CAAC9J,WAAW,CAAC;IACnD,IAAI,IAAI,CAACuM,oBAAoB,EAAE;MAC3B,IAAI,CAACA,oBAAoB,CAAC/V,WAAW,CAAC,CAAC;IAC3C;EACJ;EACA;IAAS,IAAI,CAAC5D,IAAI,YAAA6Z,wBAAA3Z,CAAA;MAAA,YAAAA,CAAA,IAAwFmZ,eAAe,EAh/DzBniB,EAAE,CAAA+W,iBAAA,CAg/DyC/W,EAAE,CAACgX,UAAU,GAh/DxDhX,EAAE,CAAA+W,iBAAA,CAg/DmEwH,YAAY;IAAA,CAA4C;EAAE;EAC/N;IAAS,IAAI,CAACtH,IAAI,kBAj/D8EjX,EAAE,CAAAkX,iBAAA;MAAAzN,IAAA,EAi/DJ0Y,eAAe;MAAAhL,SAAA;MAAAyL,OAAA;QAAAN,cAAA;MAAA;MAAA/K,QAAA;MAAAC,UAAA;IAAA,EAAmL;EAAE;AACtS;AACA;EAAA,QAAAjO,SAAA,oBAAAA,SAAA,KAn/DoGvJ,EAAE,CAAAwJ,iBAAA,CAm/DX2Y,eAAe,EAAc,CAAC;IAC7G1Y,IAAI,EAAEhJ,SAAS;IACfiJ,IAAI,EAAE,CAAC;MACCkO,QAAQ,EAAE,oDAAoD;MAC9DL,QAAQ,EAAE,iBAAiB;MAC3BC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE/N,IAAI,EAAEzJ,EAAE,CAACgX;EAAW,CAAC,EAAE;IAAEvN,IAAI,EAAE8U;EAAa,CAAC,CAAC,EAAkB;IAAE+D,cAAc,EAAE,CAAC;MACxG7Y,IAAI,EAAE3I;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,IAAI+hB,gBAAgB;AACpB,CAAC,UAAUA,gBAAgB,EAAE;EACzBA,gBAAgB,CAACA,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACvDA,gBAAgB,CAACA,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;EAC3EA,gBAAgB,CAACA,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;AAC/E,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C;AACA,MAAMC,wBAAwB,GAAG,kCAAkC;AACnE;AACA,MAAMC,wBAAwB,GAAG,kCAAkC;AACnE;AACA,MAAMC,mCAAmC,GAAG,0BAA0B;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,CAAC;EAC3B9d,WAAWA,CAACE,SAAS,EAAE6V,QAAQ,EAAE;IAC7B,IAAI,CAAC7V,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACD,SAAS,GAAG8V,QAAQ;IACzB,IAAI,CAACgI,uBAAuB,GAAGjjB,MAAM,CAACuD,kBAAkB,CAAC,CACpDqa,OAAO,CAAC,yBAAyB,CAAC,CAClCxS,SAAS,CAAC,MAAM;MACjB,IAAI,IAAI,CAAC8X,2BAA2B,EAAE;QAClC,IAAI,CAACA,2BAA2B,GAAG,KAAK;QACxC,IAAI,CAACC,oCAAoC,CAAC,CAAC;MAC/C;IACJ,CAAC,CAAC;EACN;EACA;EACAC,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAAChe,SAAS,CAAC8C,SAAS,EAAE;MAC3B,OAAO0a,gBAAgB,CAACS,IAAI;IAChC;IACA;IACA;IACA;IACA,MAAMC,WAAW,GAAG,IAAI,CAACne,SAAS,CAACmC,aAAa,CAAC,KAAK,CAAC;IACvDgc,WAAW,CAACxb,KAAK,CAACyb,eAAe,GAAG,YAAY;IAChDD,WAAW,CAACxb,KAAK,CAAC0b,QAAQ,GAAG,UAAU;IACvC,IAAI,CAACre,SAAS,CAACgD,IAAI,CAACV,WAAW,CAAC6b,WAAW,CAAC;IAC5C;IACA;IACA;IACA;IACA,MAAMG,cAAc,GAAG,IAAI,CAACte,SAAS,CAACiN,WAAW,IAAIf,MAAM;IAC3D,MAAMqS,aAAa,GAAGD,cAAc,IAAIA,cAAc,CAACtT,gBAAgB,GACjEsT,cAAc,CAACtT,gBAAgB,CAACmT,WAAW,CAAC,GAC5C,IAAI;IACV,MAAMK,aAAa,GAAG,CAAED,aAAa,IAAIA,aAAa,CAACH,eAAe,IAAK,EAAE,EAAEK,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;IAChGN,WAAW,CAACvc,MAAM,CAAC,CAAC;IACpB,QAAQ4c,aAAa;MACjB;MACA,KAAK,YAAY;MACjB;MACA,KAAK,eAAe;MACpB,KAAK,eAAe;QAChB,OAAOf,gBAAgB,CAACiB,cAAc;MAC1C;MACA,KAAK,kBAAkB;MACvB;MACA,KAAK,kBAAkB;QACnB,OAAOjB,gBAAgB,CAACkB,cAAc;IAC9C;IACA,OAAOlB,gBAAgB,CAACS,IAAI;EAChC;EACArc,WAAWA,CAAA,EAAG;IACV,IAAI,CAACic,uBAAuB,CAACxW,WAAW,CAAC,CAAC;EAC9C;EACA;EACA0W,oCAAoCA,CAAA,EAAG;IACnC,IAAI,CAAC,IAAI,CAACD,2BAA2B,IAAI,IAAI,CAAC9d,SAAS,CAAC8C,SAAS,IAAI,IAAI,CAAC/C,SAAS,CAACgD,IAAI,EAAE;MACtF,MAAM4b,WAAW,GAAG,IAAI,CAAC5e,SAAS,CAACgD,IAAI,CAACH,SAAS;MACjD+b,WAAW,CAAChd,MAAM,CAACgc,mCAAmC,EAAEF,wBAAwB,EAAEC,wBAAwB,CAAC;MAC3G,IAAI,CAACI,2BAA2B,GAAG,IAAI;MACvC,MAAMc,IAAI,GAAG,IAAI,CAACZ,mBAAmB,CAAC,CAAC;MACvC,IAAIY,IAAI,KAAKpB,gBAAgB,CAACkB,cAAc,EAAE;QAC1CC,WAAW,CAAC9b,GAAG,CAAC8a,mCAAmC,EAAEF,wBAAwB,CAAC;MAClF,CAAC,MACI,IAAImB,IAAI,KAAKpB,gBAAgB,CAACiB,cAAc,EAAE;QAC/CE,WAAW,CAAC9b,GAAG,CAAC8a,mCAAmC,EAAED,wBAAwB,CAAC;MAClF;IACJ;EACJ;EACA;IAAS,IAAI,CAACja,IAAI,YAAAob,iCAAAlb,CAAA;MAAA,YAAAA,CAAA,IAAwFia,wBAAwB,EA1lElCjjB,EAAE,CAAAiJ,QAAA,CA0lEkDjI,EAAE,CAACC,QAAQ,GA1lE/DjB,EAAE,CAAAiJ,QAAA,CA0lE0ElJ,QAAQ;IAAA,CAA6C;EAAE;EACnO;IAAS,IAAI,CAACmJ,KAAK,kBA3lE6ElJ,EAAE,CAAAmJ,kBAAA;MAAAC,KAAA,EA2lEY6Z,wBAAwB;MAAA5Z,OAAA,EAAxB4Z,wBAAwB,CAAAna,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AACnK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA7lEoGvJ,EAAE,CAAAwJ,iBAAA,CA6lEXyZ,wBAAwB,EAAc,CAAC;IACtHxZ,IAAI,EAAEtJ,UAAU;IAChBuJ,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAEzI,EAAE,CAACC;EAAS,CAAC,EAAE;IAAEwI,IAAI,EAAEE,SAAS;IAAEC,UAAU,EAAE,CAAC;MACtEH,IAAI,EAAErJ,MAAM;MACZsJ,IAAI,EAAE,CAAC3J,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAMokB,UAAU,CAAC;EACbhf,WAAWA,CAACif,wBAAwB,EAAE;IAClCA,wBAAwB,CAAChB,oCAAoC,CAAC,CAAC;EACnE;EACA;IAAS,IAAI,CAACta,IAAI,YAAAub,mBAAArb,CAAA;MAAA,YAAAA,CAAA,IAAwFmb,UAAU,EAzmEpBnkB,EAAE,CAAAiJ,QAAA,CAymEoCga,wBAAwB;IAAA,CAA2C;EAAE;EAC3M;IAAS,IAAI,CAACqB,IAAI,kBA1mE8EtkB,EAAE,CAAAukB,gBAAA;MAAA9a,IAAA,EA0mES0a;IAAU,EAAkI;EAAE;EACzP;IAAS,IAAI,CAACK,IAAI,kBA3mE8ExkB,EAAE,CAAAykB,gBAAA;MAAAC,OAAA,GA2mE+BphB,eAAe;IAAA,EAAI;EAAE;AAC1J;AACA;EAAA,QAAAiG,SAAA,oBAAAA,SAAA,KA7mEoGvJ,EAAE,CAAAwJ,iBAAA,CA6mEX2a,UAAU,EAAc,CAAC;IACxG1a,IAAI,EAAE1I,QAAQ;IACd2I,IAAI,EAAE,CAAC;MACCgb,OAAO,EAAE,CAACphB,eAAe,EAAEma,WAAW,EAAEzH,YAAY,EAAEmM,eAAe,CAAC;MACtEwC,OAAO,EAAE,CAAClH,WAAW,EAAEzH,YAAY,EAAEmM,eAAe;IACxD,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE1Y,IAAI,EAAEwZ;EAAyB,CAAC,CAAC;AAAA;;AAEtE;AACA;AACA;;AAEA,SAASkB,UAAU,EAAE/U,0BAA0B,EAAElK,aAAa,EAAEF,8BAA8B,EAAED,yBAAyB,EAAE0Y,WAAW,EAAE0E,eAAe,EAAEnM,YAAY,EAAE+B,qBAAqB,EAAE2B,4BAA4B,EAAEhB,mCAAmC,EAAE2F,6BAA6B,EAAE5F,yBAAyB,EAAElJ,eAAe,EAAEgP,YAAY,EAAEH,yBAAyB,EAAE9L,SAAS,EAAEqD,gBAAgB,EAAEkN,gBAAgB,EAAEI,wBAAwB,EAAEzI,uCAAuC,EAAED,+BAA+B,EAAEO,qBAAqB,EAAE/K,oBAAoB,EAAEF,iBAAiB,EAAEoM,8BAA8B,EAAEF,4BAA4B,EAAEC,oCAAoC,EAAElS,cAAc,EAAEqS,aAAa,EAAErX,qBAAqB,EAAEpB,mBAAmB,EAAEK,mBAAmB,EAAE8V,+BAA+B,EAAEG,gCAAgC,EAAE1V,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}