{"ast": null, "code": "import { map, take } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport class AuthGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate() {\n    return this.authService.isAuthenticated$.pipe(take(1), map(isAuthenticated => {\n      if (isAuthenticated) {\n        return true;\n      } else {\n        return this.router.createUrlTree(['/auth/login']);\n      }\n    }));\n  }\n  static {\n    this.ɵfac = function AuthGuard_Factory(t) {\n      return new (t || AuthGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "take", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "isAuthenticated$", "pipe", "isAuthenticated", "createUrlTree", "i0", "ɵɵinject", "i1", "AuthService", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\core\\guards\\auth.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { CanActivate, Router, UrlTree } from '@angular/router';\nimport { Observable, map, take } from 'rxjs';\n\nimport { AuthService } from '../services/auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthGuard implements CanActivate {\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  canActivate(): Observable<boolean | UrlTree> {\n    return this.authService.isAuthenticated$.pipe(\n      take(1),\n      map(isAuthenticated => {\n        if (isAuthenticated) {\n          return true;\n        } else {\n          return this.router.createUrlTree(['/auth/login']);\n        }\n      })\n    );\n  }\n}\n"], "mappings": "AAEA,SAAqBA,GAAG,EAAEC,IAAI,QAAQ,MAAM;;;;AAO5C,OAAM,MAAOC,SAAS;EACpBC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,WAAWA,CAAA;IACT,OAAO,IAAI,CAACF,WAAW,CAACG,gBAAgB,CAACC,IAAI,CAC3CP,IAAI,CAAC,CAAC,CAAC,EACPD,GAAG,CAACS,eAAe,IAAG;MACpB,IAAIA,eAAe,EAAE;QACnB,OAAO,IAAI;OACZ,MAAM;QACL,OAAO,IAAI,CAACJ,MAAM,CAACK,aAAa,CAAC,CAAC,aAAa,CAAC,CAAC;;IAErD,CAAC,CAAC,CACH;EACH;;;uBAjBWR,SAAS,EAAAS,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAATd,SAAS;MAAAe,OAAA,EAATf,SAAS,CAAAgB,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}