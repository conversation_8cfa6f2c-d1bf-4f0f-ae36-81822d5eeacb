{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport { TrendingProductsComponent } from '../trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../new-arrivals/new-arrivals.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/product.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3];\nconst _c1 = a0 => [\"/category\", a0];\nfunction SidebarComponent_div_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12);\n    i0.ɵɵelement(2, \"div\", 13)(3, \"div\", 14)(4, \"div\", 15);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SidebarComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtemplate(1, SidebarComponent_div_7_div_1_Template, 5, 0, \"div\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction SidebarComponent_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵelement(1, \"img\", 18);\n    i0.ɵɵelementStart(2, \"div\", 19)(3, \"h5\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_8_div_1_Template_button_click_7_listener() {\n      const user_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.followUser(user_r2.id));\n    });\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", user_r2.avatar, i0.ɵɵsanitizeUrl)(\"alt\", user_r2.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r2.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r2.followedBy);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", user_r2.isFollowing ? \"Following\" : \"Follow\", \" \");\n  }\n}\nfunction SidebarComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, SidebarComponent_div_8_div_1_Template, 9, 5, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.suggestedUsers);\n  }\n}\nfunction SidebarComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"ion-icon\", 22);\n    i0.ɵɵelementStart(2, \"h3\", 23);\n    i0.ɵɵtext(3, \"No Suggestions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 24);\n    i0.ɵɵtext(5, \"Check back later for user suggestions\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SidebarComponent_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12);\n    i0.ɵɵelement(2, \"div\", 13)(3, \"div\", 14)(4, \"div\", 15);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SidebarComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtemplate(1, SidebarComponent_div_13_div_1_Template, 5, 0, \"div\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction SidebarComponent_div_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"img\", 18);\n    i0.ɵɵelementStart(2, \"div\", 27)(3, \"h5\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 28)(8, \"span\", 29);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 30);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_14_div_1_Template_button_click_12_listener() {\n      const influencer_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.followInfluencer(influencer_r5.id));\n    });\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const influencer_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", influencer_r5.avatar, i0.ɵɵsanitizeUrl)(\"alt\", influencer_r5.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(influencer_r5.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.formatFollowerCount(influencer_r5.followersCount), \" followers\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", influencer_r5.postsCount, \" posts\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", influencer_r5.engagement, \"% engagement\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", influencer_r5.isFollowing ? \"Following\" : \"Follow\", \" \");\n  }\n}\nfunction SidebarComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, SidebarComponent_div_14_div_1_Template, 14, 7, \"div\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.topInfluencers);\n  }\n}\nfunction SidebarComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"ion-icon\", 22);\n    i0.ɵɵelementStart(2, \"h3\", 23);\n    i0.ɵɵtext(3, \"No Influencers Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 24);\n    i0.ɵɵtext(5, \"Check back later for top fashion influencers\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SidebarComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelement(1, \"img\", 18);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c1, category_r6.slug));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", category_r6.image, i0.ɵɵsanitizeUrl)(\"alt\", category_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r6.name);\n  }\n}\nexport class SidebarComponent {\n  constructor(productService, router) {\n    this.productService = productService;\n    this.router = router;\n    this.suggestedUsers = [];\n    this.trendingProducts = [];\n    this.topInfluencers = [];\n    this.categories = [];\n    this.isLoadingInfluencers = false;\n    this.isLoadingSuggestions = false;\n  }\n  ngOnInit() {\n    this.loadSuggestedUsers();\n    this.loadTrendingProducts();\n    this.loadTopInfluencers();\n    this.loadCategories();\n  }\n  loadSuggestedUsers() {\n    this.isLoadingSuggestions = true;\n    // Simulate API call\n    setTimeout(() => {\n      this.suggestedUsers = [{\n        id: '1',\n        username: '@arjun_style',\n        fullName: 'Arjun Kumar',\n        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',\n        followedBy: 'Followed by maya_fashion + 12 others',\n        isFollowing: false\n      }, {\n        id: '2',\n        username: '@sneha_trends',\n        fullName: 'Sneha Reddy',\n        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100',\n        followedBy: 'Followed by raj_style + 8 others',\n        isFollowing: false\n      }, {\n        id: '3',\n        username: '@fashion_vikram',\n        fullName: 'Vikram Singh',\n        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100',\n        followedBy: 'Followed by priya_chic + 15 others',\n        isFollowing: true\n      }];\n      this.isLoadingSuggestions = false;\n    }, 1200);\n  }\n  loadTrendingProducts() {\n    // Load from API - empty for now\n    this.trendingProducts = [];\n  }\n  loadTopInfluencers() {\n    this.isLoadingInfluencers = true;\n    // Simulate API call\n    setTimeout(() => {\n      this.topInfluencers = [{\n        id: '1',\n        username: '@fashionista_maya',\n        fullName: 'Maya Sharma',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=100',\n        followersCount: 125000,\n        postsCount: 342,\n        engagement: 8.5,\n        isFollowing: false\n      }, {\n        id: '2',\n        username: '@style_guru_raj',\n        fullName: 'Raj Patel',\n        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100',\n        followersCount: 89000,\n        postsCount: 198,\n        engagement: 7.2,\n        isFollowing: true\n      }, {\n        id: '3',\n        username: '@trendy_priya',\n        fullName: 'Priya Singh',\n        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100',\n        followersCount: 67000,\n        postsCount: 156,\n        engagement: 9.1,\n        isFollowing: false\n      }];\n      this.isLoadingInfluencers = false;\n    }, 1500);\n  }\n  loadCategories() {\n    this.categories = [{\n      name: 'Women',\n      slug: 'women',\n      image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=100'\n    }, {\n      name: 'Men',\n      slug: 'men',\n      image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=100'\n    }, {\n      name: 'Kids',\n      slug: 'children',\n      image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=100'\n    }, {\n      name: 'Ethnic',\n      slug: 'ethnic',\n      image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=100'\n    }];\n  }\n  formatFollowerCount(count) {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'k';\n    }\n    return count.toString();\n  }\n  followUser(userId) {\n    const user = this.suggestedUsers.find(u => u.id === userId);\n    if (user) {\n      user.isFollowing = !user.isFollowing;\n    }\n  }\n  followInfluencer(influencerId) {\n    const influencer = this.topInfluencers.find(i => i.id === influencerId);\n    if (influencer) {\n      influencer.isFollowing = !influencer.isFollowing;\n    }\n  }\n  quickBuy(productId) {\n    console.log('Quick buy product:', productId);\n    // TODO: Implement quick buy functionality\n  }\n  browseCategory(categorySlug) {\n    console.log('Browse category:', categorySlug);\n    this.router.navigate(['/category', categorySlug]);\n  }\n  static {\n    this.ɵfac = function SidebarComponent_Factory(t) {\n      return new (t || SidebarComponent)(i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SidebarComponent,\n      selectors: [[\"app-sidebar\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 21,\n      vars: 7,\n      consts: [[1, \"sidebar\"], [1, \"suggestions\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"influencers\"], [1, \"categories\"], [1, \"category-grid\"], [\"class\", \"category-item\", \"routerLinkActive\", \"active\", \"tabindex\", \"0\", 3, \"routerLink\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-container\"], [\"class\", \"loading-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-card\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [\"class\", \"suggestion-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"suggestion-item\"], [3, \"src\", \"alt\"], [1, \"suggestion-info\"], [1, \"follow-btn\", 3, \"click\"], [1, \"empty-container\"], [\"name\", \"people-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"], [\"class\", \"influencer-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"influencer-item\"], [1, \"influencer-info\"], [1, \"influencer-stats\"], [1, \"posts-count\"], [1, \"engagement\"], [\"routerLinkActive\", \"active\", \"tabindex\", \"0\", 1, \"category-item\", 3, \"routerLink\"]],\n      template: function SidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"aside\", 0);\n          i0.ɵɵelement(1, \"app-trending-products\")(2, \"app-featured-brands\")(3, \"app-new-arrivals\");\n          i0.ɵɵelementStart(4, \"div\", 1)(5, \"h3\");\n          i0.ɵɵtext(6, \"Suggested for you\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, SidebarComponent_div_7_Template, 2, 2, \"div\", 2)(8, SidebarComponent_div_8_Template, 2, 1, \"div\", 3)(9, SidebarComponent_div_9_Template, 6, 0, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 5)(11, \"h3\");\n          i0.ɵɵtext(12, \"Top Fashion Influencers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(13, SidebarComponent_div_13_Template, 2, 2, \"div\", 2)(14, SidebarComponent_div_14_Template, 2, 1, \"div\", 3)(15, SidebarComponent_div_15_Template, 6, 0, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 6)(17, \"h3\");\n          i0.ɵɵtext(18, \"Shop by Category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 7);\n          i0.ɵɵtemplate(20, SidebarComponent_div_20_Template, 4, 6, \"div\", 8);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingSuggestions);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingSuggestions && ctx.suggestedUsers.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingSuggestions && ctx.suggestedUsers.length === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingInfluencers);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingInfluencers && ctx.topInfluencers.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingInfluencers && ctx.topInfluencers.length === 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, RouterModule, i2.RouterLink, i2.RouterLinkActive, IonicModule, i4.IonIcon, i4.RouterLinkDelegate, CarouselModule, TrendingProductsComponent, FeaturedBrandsComponent, NewArrivalsComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.sidebar[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 80px;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n  max-height: calc(100vh - 100px);\\n  overflow-y: auto;\\n  padding-right: 8px;\\n}\\n\\napp-view-add-stories[_ngcontent-%COMP%], app-trending-products[_ngcontent-%COMP%], app-featured-brands[_ngcontent-%COMP%], app-new-arrivals[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: block;\\n  margin-bottom: 24px;\\n}\\n\\n.suggestions[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n  box-shadow: 0 8px 32px rgba(255, 107, 107, 0.2);\\n}\\n\\n.categories[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n  box-shadow: 0 8px 32px rgba(0, 210, 211, 0.2);\\n}\\n\\n.trending[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  padding: 16px;\\n}\\n\\n.influencers[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDCA1\\\";\\n  font-size: 28px;\\n}\\n\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDECD\\uFE0F\\\";\\n  font-size: 28px;\\n}\\n\\n.trending[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin-bottom: 16px;\\n  color: #8e8e8e;\\n}\\n\\n.influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDC51\\\";\\n  font-size: 28px;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 16px;\\n  margin-bottom: 16px;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 16px;\\n  margin-bottom: 16px;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);\\n}\\n\\n.influencer-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid rgba(255, 255, 255, 0.3);\\n  transition: transform 0.3s ease;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.suggestion-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.suggestion-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 4px;\\n}\\n\\n.suggestion-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 8px 16px;\\n  border-radius: 8px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n  transform: translateY(-2px);\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  padding: 16px;\\n  margin-bottom: 16px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.suggestions[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  background: rgba(255, 255, 255, 0.2);\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  border-radius: 6px;\\n  margin-bottom: 8px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.suggestions[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.suggestions[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: rgba(255, 255, 255, 0.4);\\n  margin-bottom: 16px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 8px;\\n}\\n.suggestions[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.influencer-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 16px;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid rgba(255, 255, 255, 0.3);\\n  transition: transform 0.3s ease;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.influencer-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.influencer-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 4px;\\n}\\n\\n.influencer-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.7);\\n  margin-bottom: 8px;\\n}\\n\\n.influencer-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n  margin-bottom: 12px;\\n}\\n\\n.influencer-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: rgba(255, 255, 255, 0.6);\\n  background: rgba(255, 255, 255, 0.1);\\n  padding: 4px 8px;\\n  border-radius: 8px;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  -webkit-backdrop-filter: blur(5px);\\n          backdrop-filter: blur(5px);\\n}\\n\\n.follow-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 8px 16px;\\n  border-radius: 8px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  align-self: flex-start;\\n}\\n\\n.follow-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n  transform: translateY(-2px);\\n}\\n\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  padding: 16px;\\n  margin-bottom: 16px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  background: rgba(255, 255, 255, 0.2);\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  border-radius: 6px;\\n  margin-bottom: 8px;\\n}\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0%, 100% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n.influencers[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.influencers[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: rgba(255, 255, 255, 0.4);\\n  margin-bottom: 16px;\\n}\\n.influencers[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 8px;\\n}\\n.influencers[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.trending-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n  padding-bottom: 16px;\\n  border-bottom: 1px solid #f1f5f9;\\n}\\n\\n.trending-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n  margin-bottom: 0;\\n  padding-bottom: 0;\\n}\\n\\n.trending-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 8px;\\n  object-fit: cover;\\n}\\n\\n.trending-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.trending-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin-bottom: 4px;\\n}\\n\\n.trending-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: var(--primary-color);\\n  margin-bottom: 8px;\\n}\\n\\n.original-price[_ngcontent-%COMP%] {\\n  text-decoration: line-through;\\n  color: #8e8e8e;\\n  font-weight: 400;\\n  margin-left: 4px;\\n}\\n\\n.trending-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n\\n.trending-badge[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 600;\\n  background: #fef3c7;\\n  color: #92400e;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n\\n.views[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #8e8e8e;\\n}\\n\\n.quick-buy-btn[_ngcontent-%COMP%] {\\n  background: #f1f5f9;\\n  color: #64748b;\\n  border: 1px solid #e2e8f0;\\n  padding: 6px 12px;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n  align-self: flex-start;\\n}\\n\\n.quick-buy-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-color);\\n  color: #fff;\\n  border-color: var(--primary-color);\\n}\\n\\n.categories[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1f2937;\\n  margin-bottom: 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDECD\\uFE0F\\\";\\n  font-size: 18px;\\n}\\n\\n.category-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 16px;\\n}\\n\\n.category-item[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 20px 16px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-align: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  text-decoration: none;\\n  color: white;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.category-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);\\n}\\n\\n.category-item.active[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  border-color: rgba(255, 255, 255, 0.4);\\n  transform: translateY(-4px);\\n}\\n\\n.category-item.active[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n.category-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  margin-bottom: 12px;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  transition: transform 0.3s ease;\\n}\\n\\n.category-item[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.category-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: white;\\n  text-align: center;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.category-item[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.category-item[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n\\n@media (max-width: 1024px) {\\n  .sidebar[_ngcontent-%COMP%] {\\n    order: -1;\\n    position: static;\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n    gap: 20px;\\n    max-height: none;\\n    overflow-y: visible;\\n  }\\n  app-view-add-stories[_ngcontent-%COMP%], app-trending-products[_ngcontent-%COMP%], app-featured-brands[_ngcontent-%COMP%], app-new-arrivals[_ngcontent-%COMP%] {\\n    margin-bottom: 16px;\\n  }\\n  .suggestions[_ngcontent-%COMP%], .influencers[_ngcontent-%COMP%], .categories[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .suggestion-item[_ngcontent-%COMP%], .influencer-item[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .suggestion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n  }\\n  .category-grid[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n  .category-item[_ngcontent-%COMP%] {\\n    padding: 16px 12px;\\n  }\\n  .category-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .sidebar[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  app-view-add-stories[_ngcontent-%COMP%], app-trending-products[_ngcontent-%COMP%], app-featured-brands[_ngcontent-%COMP%], app-new-arrivals[_ngcontent-%COMP%] {\\n    margin-bottom: 12px;\\n  }\\n  .suggestions[_ngcontent-%COMP%], .influencers[_ngcontent-%COMP%], .categories[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .suggestion-item[_ngcontent-%COMP%], .influencer-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n    padding: 16px;\\n  }\\n  .suggestion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n    margin-bottom: 12px;\\n  }\\n  .influencer-stats[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    justify-content: center;\\n    gap: 8px;\\n  }\\n  .category-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 12px;\\n  }\\n  .category-item[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    text-align: left;\\n    padding: 12px;\\n    gap: 12px;\\n  }\\n  .category-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n    margin-bottom: 0;\\n  }\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "IonicModule", "CarouselModule", "TrendingProductsComponent", "FeaturedBrandsComponent", "NewArrivalsComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "SidebarComponent_div_7_div_1_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵtext", "ɵɵlistener", "SidebarComponent_div_8_div_1_Template_button_click_7_listener", "user_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "followUser", "id", "avatar", "ɵɵsanitizeUrl", "fullName", "ɵɵtextInterpolate", "username", "<PERSON><PERSON><PERSON>", "ɵɵtextInterpolate1", "isFollowing", "SidebarComponent_div_8_div_1_Template", "suggestedUsers", "SidebarComponent_div_13_div_1_Template", "SidebarComponent_div_14_div_1_Template_button_click_12_listener", "influencer_r5", "_r4", "followInfluencer", "formatFollowerCount", "followersCount", "postsCount", "engagement", "SidebarComponent_div_14_div_1_Template", "topInfluencers", "ɵɵpureFunction1", "_c1", "category_r6", "slug", "image", "name", "SidebarComponent", "constructor", "productService", "router", "trendingProducts", "categories", "isLoadingInfluencers", "isLoadingSuggestions", "ngOnInit", "loadSuggestedUsers", "loadTrendingProducts", "loadTopInfluencers", "loadCategories", "setTimeout", "count", "toFixed", "toString", "userId", "user", "find", "u", "influencerId", "influencer", "i", "quickBuy", "productId", "console", "log", "browseCategory", "categorySlug", "navigate", "ɵɵdirectiveInject", "i1", "ProductService", "i2", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SidebarComponent_Template", "rf", "ctx", "SidebarComponent_div_7_Template", "SidebarComponent_div_8_Template", "SidebarComponent_div_9_Template", "SidebarComponent_div_13_Template", "SidebarComponent_div_14_Template", "SidebarComponent_div_15_Template", "SidebarComponent_div_20_Template", "length", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "RouterLink", "RouterLinkActive", "i4", "IonIcon", "RouterLinkDelegate", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\sidebar\\sidebar.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\sidebar\\sidebar.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport { OwlOptions } from 'ngx-owl-carousel-o';\n\nimport { ProductService } from '../../../../core/services/product.service';\nimport { Product } from '../../../../core/models/product.model';\nimport { TrendingProductsComponent } from '../trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../new-arrivals/new-arrivals.component';\n\n@Component({\n  selector: 'app-sidebar',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    IonicModule,\n    CarouselModule,\n    TrendingProductsComponent,\n    FeaturedBrandsComponent,\n    NewArrivalsComponent\n  ],\n  templateUrl: './sidebar.component.html',\n  styleUrls: ['./sidebar.component.scss']\n})\nexport class SidebarComponent implements OnInit {\n  suggestedUsers: any[] = [];\n  trendingProducts: Product[] = [];\n  topInfluencers: any[] = [];\n  categories: any[] = [];\n  isLoadingInfluencers: boolean = false;\n  isLoadingSuggestions: boolean = false;\n\n  constructor(\n    private productService: ProductService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadSuggestedUsers();\n    this.loadTrendingProducts();\n    this.loadTopInfluencers();\n    this.loadCategories();\n  }\n\n  loadSuggestedUsers() {\n    this.isLoadingSuggestions = true;\n\n    // Simulate API call\n    setTimeout(() => {\n      this.suggestedUsers = [\n        {\n          id: '1',\n          username: '@arjun_style',\n          fullName: 'Arjun Kumar',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',\n          followedBy: 'Followed by maya_fashion + 12 others',\n          isFollowing: false\n        },\n        {\n          id: '2',\n          username: '@sneha_trends',\n          fullName: 'Sneha Reddy',\n          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100',\n          followedBy: 'Followed by raj_style + 8 others',\n          isFollowing: false\n        },\n        {\n          id: '3',\n          username: '@fashion_vikram',\n          fullName: 'Vikram Singh',\n          avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100',\n          followedBy: 'Followed by priya_chic + 15 others',\n          isFollowing: true\n        }\n      ];\n      this.isLoadingSuggestions = false;\n    }, 1200);\n  }\n\n  loadTrendingProducts() {\n    // Load from API - empty for now\n    this.trendingProducts = [];\n  }\n\n  loadTopInfluencers() {\n    this.isLoadingInfluencers = true;\n\n    // Simulate API call\n    setTimeout(() => {\n      this.topInfluencers = [\n        {\n          id: '1',\n          username: '@fashionista_maya',\n          fullName: 'Maya Sharma',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=100',\n          followersCount: 125000,\n          postsCount: 342,\n          engagement: 8.5,\n          isFollowing: false\n        },\n        {\n          id: '2',\n          username: '@style_guru_raj',\n          fullName: 'Raj Patel',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100',\n          followersCount: 89000,\n          postsCount: 198,\n          engagement: 7.2,\n          isFollowing: true\n        },\n        {\n          id: '3',\n          username: '@trendy_priya',\n          fullName: 'Priya Singh',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100',\n          followersCount: 67000,\n          postsCount: 156,\n          engagement: 9.1,\n          isFollowing: false\n        }\n      ];\n      this.isLoadingInfluencers = false;\n    }, 1500);\n  }\n\n  loadCategories() {\n    this.categories = [\n      {\n        name: 'Women',\n        slug: 'women',\n        image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=100'\n      },\n      {\n        name: 'Men',\n        slug: 'men',\n        image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=100'\n      },\n      {\n        name: 'Kids',\n        slug: 'children',\n        image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=100'\n      },\n      {\n        name: 'Ethnic',\n        slug: 'ethnic',\n        image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=100'\n      }\n    ];\n  }\n\n  formatFollowerCount(count: number): string {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'k';\n    }\n    return count.toString();\n  }\n\n  followUser(userId: string) {\n    const user = this.suggestedUsers.find(u => u.id === userId);\n    if (user) {\n      user.isFollowing = !user.isFollowing;\n    }\n  }\n\n  followInfluencer(influencerId: string) {\n    const influencer = this.topInfluencers.find(i => i.id === influencerId);\n    if (influencer) {\n      influencer.isFollowing = !influencer.isFollowing;\n    }\n  }\n\n  quickBuy(productId: string) {\n    console.log('Quick buy product:', productId);\n    // TODO: Implement quick buy functionality\n  }\n\n  browseCategory(categorySlug: string) {\n    console.log('Browse category:', categorySlug);\n    this.router.navigate(['/category', categorySlug]);\n  }\n}\n", "<aside class=\"sidebar\">\n  <!-- Instagram-style Stories -->\n \n\n  <!-- Trending Products Section -->\n  <app-trending-products></app-trending-products>\n\n  <!-- Featured Brands Section -->\n  <app-featured-brands></app-featured-brands>\n\n  <!-- New Arrivals Section -->\n  <app-new-arrivals></app-new-arrivals>\n\n  <!-- Suggested for you -->\n  <div class=\"suggestions\">\n    <h3>Suggested for you</h3>\n\n    <!-- Loading State -->\n    <div *ngIf=\"isLoadingSuggestions\" class=\"loading-container\">\n      <div class=\"loading-card\" *ngFor=\"let item of [1,2,3]\">\n        <div class=\"loading-content\">\n          <div class=\"loading-line short\"></div>\n          <div class=\"loading-line medium\"></div>\n          <div class=\"loading-line long\"></div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Suggestions List -->\n    <div *ngIf=\"!isLoadingSuggestions && suggestedUsers.length > 0\">\n      <div *ngFor=\"let user of suggestedUsers\" class=\"suggestion-item\">\n        <img [src]=\"user.avatar\" [alt]=\"user.fullName\">\n        <div class=\"suggestion-info\">\n          <h5>{{ user.username }}</h5>\n          <p>{{ user.followedBy }}</p>\n        </div>\n        <button class=\"follow-btn\" (click)=\"followUser(user.id)\">\n          {{ user.isFollowing ? 'Following' : 'Follow' }}\n        </button>\n      </div>\n    </div>\n\n    <!-- Empty State -->\n    <div *ngIf=\"!isLoadingSuggestions && suggestedUsers.length === 0\" class=\"empty-container\">\n      <ion-icon name=\"people-outline\" class=\"empty-icon\"></ion-icon>\n      <h3 class=\"empty-title\">No Suggestions</h3>\n      <p class=\"empty-message\">Check back later for user suggestions</p>\n    </div>\n  </div>\n\n  <!-- Top Fashion Influencers -->\n  <div class=\"influencers\">\n    <h3>Top Fashion Influencers</h3>\n\n    <!-- Loading State -->\n    <div *ngIf=\"isLoadingInfluencers\" class=\"loading-container\">\n      <div class=\"loading-card\" *ngFor=\"let item of [1,2,3]\">\n        <div class=\"loading-content\">\n          <div class=\"loading-line short\"></div>\n          <div class=\"loading-line medium\"></div>\n          <div class=\"loading-line long\"></div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Influencers List -->\n    <div *ngIf=\"!isLoadingInfluencers && topInfluencers.length > 0\">\n      <div *ngFor=\"let influencer of topInfluencers\" class=\"influencer-item\">\n        <img [src]=\"influencer.avatar\" [alt]=\"influencer.fullName\">\n        <div class=\"influencer-info\">\n          <h5>{{ influencer.username }}</h5>\n          <p>{{ formatFollowerCount(influencer.followersCount) }} followers</p>\n          <div class=\"influencer-stats\">\n            <span class=\"posts-count\">{{ influencer.postsCount }} posts</span>\n            <span class=\"engagement\">{{ influencer.engagement }}% engagement</span>\n          </div>\n          <button class=\"follow-btn\" (click)=\"followInfluencer(influencer.id)\">\n            {{ influencer.isFollowing ? 'Following' : 'Follow' }}\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Empty State -->\n    <div *ngIf=\"!isLoadingInfluencers && topInfluencers.length === 0\" class=\"empty-container\">\n      <ion-icon name=\"people-outline\" class=\"empty-icon\"></ion-icon>\n      <h3 class=\"empty-title\">No Influencers Found</h3>\n      <p class=\"empty-message\">Check back later for top fashion influencers</p>\n    </div>\n  </div>\n\n  <!-- Shop by Category -->\n  <div class=\"categories\">\n    <h3>Shop by Category</h3>\n    <div class=\"category-grid\">\n      <div\n        *ngFor=\"let category of categories\"\n        class=\"category-item\"\n        [routerLink]=\"['/category', category.slug]\"\n        routerLinkActive=\"active\"\n        tabindex=\"0\"\n      >\n        <img [src]=\"category.image\" [alt]=\"category.name\">\n        <span>{{ category.name }}</span>\n      </div>\n    </div>\n  </div>\n\n\n</aside>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,oBAAoB;AAKnD,SAASC,yBAAyB,QAAQ,kDAAkD;AAC5F,SAASC,uBAAuB,QAAQ,8CAA8C;AACtF,SAASC,oBAAoB,QAAQ,wCAAwC;;;;;;;;;;ICSrEC,EADF,CAAAC,cAAA,cAAuD,cACxB;IAG3BD,EAFA,CAAAE,SAAA,cAAsC,cACC,cACF;IAEzCF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAPRH,EAAA,CAAAC,cAAA,aAA4D;IAC1DD,EAAA,CAAAI,UAAA,IAAAC,qCAAA,kBAAuD;IAOzDL,EAAA,CAAAG,YAAA,EAAM;;;IAPuCH,EAAA,CAAAM,SAAA,EAAU;IAAVN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAU;;;;;;IAWrDT,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAE,SAAA,cAA+C;IAE7CF,EADF,CAAAC,cAAA,cAA6B,SACvB;IAAAD,EAAA,CAAAU,MAAA,GAAmB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,GAAqB;IAC1BV,EAD0B,CAAAG,YAAA,EAAI,EACxB;IACNH,EAAA,CAAAC,cAAA,iBAAyD;IAA9BD,EAAA,CAAAW,UAAA,mBAAAC,8DAAA;MAAA,MAAAC,OAAA,GAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAG,UAAA,CAAAP,OAAA,CAAAQ,EAAA,CAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAG,YAAA,EAAS,EACL;;;;IARCH,EAAA,CAAAM,SAAA,EAAmB;IAACN,EAApB,CAAAO,UAAA,QAAAM,OAAA,CAAAS,MAAA,EAAAtB,EAAA,CAAAuB,aAAA,CAAmB,QAAAV,OAAA,CAAAW,QAAA,CAAsB;IAExCxB,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAyB,iBAAA,CAAAZ,OAAA,CAAAa,QAAA,CAAmB;IACpB1B,EAAA,CAAAM,SAAA,GAAqB;IAArBN,EAAA,CAAAyB,iBAAA,CAAAZ,OAAA,CAAAc,UAAA,CAAqB;IAGxB3B,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAA4B,kBAAA,MAAAf,OAAA,CAAAgB,WAAA,+BACF;;;;;IATJ7B,EAAA,CAAAC,cAAA,UAAgE;IAC9DD,EAAA,CAAAI,UAAA,IAAA0B,qCAAA,kBAAiE;IAUnE9B,EAAA,CAAAG,YAAA,EAAM;;;;IAVkBH,EAAA,CAAAM,SAAA,EAAiB;IAAjBN,EAAA,CAAAO,UAAA,YAAAU,MAAA,CAAAc,cAAA,CAAiB;;;;;IAazC/B,EAAA,CAAAC,cAAA,cAA0F;IACxFD,EAAA,CAAAE,SAAA,mBAA8D;IAC9DF,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAU,MAAA,qBAAc;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAU,MAAA,4CAAqC;IAChEV,EADgE,CAAAG,YAAA,EAAI,EAC9D;;;;;IAUFH,EADF,CAAAC,cAAA,cAAuD,cACxB;IAG3BD,EAFA,CAAAE,SAAA,cAAsC,cACC,cACF;IAEzCF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAPRH,EAAA,CAAAC,cAAA,aAA4D;IAC1DD,EAAA,CAAAI,UAAA,IAAA4B,sCAAA,kBAAuD;IAOzDhC,EAAA,CAAAG,YAAA,EAAM;;;IAPuCH,EAAA,CAAAM,SAAA,EAAU;IAAVN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAU;;;;;;IAWrDT,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAE,SAAA,cAA2D;IAEzDF,EADF,CAAAC,cAAA,cAA6B,SACvB;IAAAD,EAAA,CAAAU,MAAA,GAAyB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,GAA8D;IAAAV,EAAA,CAAAG,YAAA,EAAI;IAEnEH,EADF,CAAAC,cAAA,cAA8B,eACF;IAAAD,EAAA,CAAAU,MAAA,GAAiC;IAAAV,EAAA,CAAAG,YAAA,EAAO;IAClEH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAU,MAAA,IAAuC;IAClEV,EADkE,CAAAG,YAAA,EAAO,EACnE;IACNH,EAAA,CAAAC,cAAA,kBAAqE;IAA1CD,EAAA,CAAAW,UAAA,mBAAAsB,gEAAA;MAAA,MAAAC,aAAA,GAAAlC,EAAA,CAAAc,aAAA,CAAAqB,GAAA,EAAAnB,SAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAmB,gBAAA,CAAAF,aAAA,CAAAb,EAAA,CAA+B;IAAA,EAAC;IAClErB,EAAA,CAAAU,MAAA,IACF;IAEJV,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;;IAZCH,EAAA,CAAAM,SAAA,EAAyB;IAACN,EAA1B,CAAAO,UAAA,QAAA2B,aAAA,CAAAZ,MAAA,EAAAtB,EAAA,CAAAuB,aAAA,CAAyB,QAAAW,aAAA,CAAAV,QAAA,CAA4B;IAEpDxB,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAyB,iBAAA,CAAAS,aAAA,CAAAR,QAAA,CAAyB;IAC1B1B,EAAA,CAAAM,SAAA,GAA8D;IAA9DN,EAAA,CAAA4B,kBAAA,KAAAX,MAAA,CAAAoB,mBAAA,CAAAH,aAAA,CAAAI,cAAA,gBAA8D;IAErCtC,EAAA,CAAAM,SAAA,GAAiC;IAAjCN,EAAA,CAAA4B,kBAAA,KAAAM,aAAA,CAAAK,UAAA,WAAiC;IAClCvC,EAAA,CAAAM,SAAA,GAAuC;IAAvCN,EAAA,CAAA4B,kBAAA,KAAAM,aAAA,CAAAM,UAAA,iBAAuC;IAGhExC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAA4B,kBAAA,MAAAM,aAAA,CAAAL,WAAA,+BACF;;;;;IAZN7B,EAAA,CAAAC,cAAA,UAAgE;IAC9DD,EAAA,CAAAI,UAAA,IAAAqC,sCAAA,mBAAuE;IAczEzC,EAAA,CAAAG,YAAA,EAAM;;;;IAdwBH,EAAA,CAAAM,SAAA,EAAiB;IAAjBN,EAAA,CAAAO,UAAA,YAAAU,MAAA,CAAAyB,cAAA,CAAiB;;;;;IAiB/C1C,EAAA,CAAAC,cAAA,cAA0F;IACxFD,EAAA,CAAAE,SAAA,mBAA8D;IAC9DF,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAU,MAAA,2BAAoB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAU,MAAA,mDAA4C;IACvEV,EADuE,CAAAG,YAAA,EAAI,EACrE;;;;;IAOJH,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAE,SAAA,cAAkD;IAClDF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,GAAmB;IAC3BV,EAD2B,CAAAG,YAAA,EAAO,EAC5B;;;;IANJH,EAAA,CAAAO,UAAA,eAAAP,EAAA,CAAA2C,eAAA,IAAAC,GAAA,EAAAC,WAAA,CAAAC,IAAA,EAA2C;IAItC9C,EAAA,CAAAM,SAAA,EAAsB;IAACN,EAAvB,CAAAO,UAAA,QAAAsC,WAAA,CAAAE,KAAA,EAAA/C,EAAA,CAAAuB,aAAA,CAAsB,QAAAsB,WAAA,CAAAG,IAAA,CAAsB;IAC3ChD,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAyB,iBAAA,CAAAoB,WAAA,CAAAG,IAAA,CAAmB;;;AD3EjC,OAAM,MAAOC,gBAAgB;EAQ3BC,YACUC,cAA8B,EAC9BC,MAAc;IADd,KAAAD,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IAThB,KAAArB,cAAc,GAAU,EAAE;IAC1B,KAAAsB,gBAAgB,GAAc,EAAE;IAChC,KAAAX,cAAc,GAAU,EAAE;IAC1B,KAAAY,UAAU,GAAU,EAAE;IACtB,KAAAC,oBAAoB,GAAY,KAAK;IACrC,KAAAC,oBAAoB,GAAY,KAAK;EAKlC;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAH,kBAAkBA,CAAA;IAChB,IAAI,CAACF,oBAAoB,GAAG,IAAI;IAEhC;IACAM,UAAU,CAAC,MAAK;MACd,IAAI,CAAC/B,cAAc,GAAG,CACpB;QACEV,EAAE,EAAE,GAAG;QACPK,QAAQ,EAAE,cAAc;QACxBF,QAAQ,EAAE,aAAa;QACvBF,MAAM,EAAE,oEAAoE;QAC5EK,UAAU,EAAE,sCAAsC;QAClDE,WAAW,EAAE;OACd,EACD;QACER,EAAE,EAAE,GAAG;QACPK,QAAQ,EAAE,eAAe;QACzBF,QAAQ,EAAE,aAAa;QACvBF,MAAM,EAAE,iEAAiE;QACzEK,UAAU,EAAE,kCAAkC;QAC9CE,WAAW,EAAE;OACd,EACD;QACER,EAAE,EAAE,GAAG;QACPK,QAAQ,EAAE,iBAAiB;QAC3BF,QAAQ,EAAE,cAAc;QACxBF,MAAM,EAAE,oEAAoE;QAC5EK,UAAU,EAAE,oCAAoC;QAChDE,WAAW,EAAE;OACd,CACF;MACD,IAAI,CAAC2B,oBAAoB,GAAG,KAAK;IACnC,CAAC,EAAE,IAAI,CAAC;EACV;EAEAG,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAACN,gBAAgB,GAAG,EAAE;EAC5B;EAEAO,kBAAkBA,CAAA;IAChB,IAAI,CAACL,oBAAoB,GAAG,IAAI;IAEhC;IACAO,UAAU,CAAC,MAAK;MACd,IAAI,CAACpB,cAAc,GAAG,CACpB;QACErB,EAAE,EAAE,GAAG;QACPK,QAAQ,EAAE,mBAAmB;QAC7BF,QAAQ,EAAE,aAAa;QACvBF,MAAM,EAAE,oEAAoE;QAC5EgB,cAAc,EAAE,MAAM;QACtBC,UAAU,EAAE,GAAG;QACfC,UAAU,EAAE,GAAG;QACfX,WAAW,EAAE;OACd,EACD;QACER,EAAE,EAAE,GAAG;QACPK,QAAQ,EAAE,iBAAiB;QAC3BF,QAAQ,EAAE,WAAW;QACrBF,MAAM,EAAE,oEAAoE;QAC5EgB,cAAc,EAAE,KAAK;QACrBC,UAAU,EAAE,GAAG;QACfC,UAAU,EAAE,GAAG;QACfX,WAAW,EAAE;OACd,EACD;QACER,EAAE,EAAE,GAAG;QACPK,QAAQ,EAAE,eAAe;QACzBF,QAAQ,EAAE,aAAa;QACvBF,MAAM,EAAE,oEAAoE;QAC5EgB,cAAc,EAAE,KAAK;QACrBC,UAAU,EAAE,GAAG;QACfC,UAAU,EAAE,GAAG;QACfX,WAAW,EAAE;OACd,CACF;MACD,IAAI,CAAC0B,oBAAoB,GAAG,KAAK;IACnC,CAAC,EAAE,IAAI,CAAC;EACV;EAEAM,cAAcA,CAAA;IACZ,IAAI,CAACP,UAAU,GAAG,CAChB;MACEN,IAAI,EAAE,OAAO;MACbF,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;KACR,EACD;MACEC,IAAI,EAAE,KAAK;MACXF,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE;KACR,EACD;MACEC,IAAI,EAAE,MAAM;MACZF,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE;KACR,EACD;MACEC,IAAI,EAAE,QAAQ;MACdF,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;KACR,CACF;EACH;EAEAV,mBAAmBA,CAAC0B,KAAa;IAC/B,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEA7C,UAAUA,CAAC8C,MAAc;IACvB,MAAMC,IAAI,GAAG,IAAI,CAACpC,cAAc,CAACqC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChD,EAAE,KAAK6C,MAAM,CAAC;IAC3D,IAAIC,IAAI,EAAE;MACRA,IAAI,CAACtC,WAAW,GAAG,CAACsC,IAAI,CAACtC,WAAW;;EAExC;EAEAO,gBAAgBA,CAACkC,YAAoB;IACnC,MAAMC,UAAU,GAAG,IAAI,CAAC7B,cAAc,CAAC0B,IAAI,CAACI,CAAC,IAAIA,CAAC,CAACnD,EAAE,KAAKiD,YAAY,CAAC;IACvE,IAAIC,UAAU,EAAE;MACdA,UAAU,CAAC1C,WAAW,GAAG,CAAC0C,UAAU,CAAC1C,WAAW;;EAEpD;EAEA4C,QAAQA,CAACC,SAAiB;IACxBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,SAAS,CAAC;IAC5C;EACF;EAEAG,cAAcA,CAACC,YAAoB;IACjCH,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEE,YAAY,CAAC;IAC7C,IAAI,CAAC1B,MAAM,CAAC2B,QAAQ,CAAC,CAAC,WAAW,EAAED,YAAY,CAAC,CAAC;EACnD;;;uBA3JW7B,gBAAgB,EAAAjD,EAAA,CAAAgF,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAlF,EAAA,CAAAgF,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAhBnC,gBAAgB;MAAAoC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAvF,EAAA,CAAAwF,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC5B7B9F,EAAA,CAAAC,cAAA,eAAuB;UAWrBD,EANA,CAAAE,SAAA,4BAA+C,0BAGJ,uBAGN;UAInCF,EADF,CAAAC,cAAA,aAAyB,SACnB;UAAAD,EAAA,CAAAU,MAAA,wBAAiB;UAAAV,EAAA,CAAAG,YAAA,EAAK;UA4B1BH,EAzBA,CAAAI,UAAA,IAAA4F,+BAAA,iBAA4D,IAAAC,+BAAA,iBAWI,IAAAC,+BAAA,iBAc0B;UAK5FlG,EAAA,CAAAG,YAAA,EAAM;UAIJH,EADF,CAAAC,cAAA,cAAyB,UACnB;UAAAD,EAAA,CAAAU,MAAA,+BAAuB;UAAAV,EAAA,CAAAG,YAAA,EAAK;UAgChCH,EA7BA,CAAAI,UAAA,KAAA+F,gCAAA,iBAA4D,KAAAC,gCAAA,iBAWI,KAAAC,gCAAA,iBAkB0B;UAK5FrG,EAAA,CAAAG,YAAA,EAAM;UAIJH,EADF,CAAAC,cAAA,cAAwB,UAClB;UAAAD,EAAA,CAAAU,MAAA,wBAAgB;UAAAV,EAAA,CAAAG,YAAA,EAAK;UACzBH,EAAA,CAAAC,cAAA,cAA2B;UACzBD,EAAA,CAAAI,UAAA,KAAAkG,gCAAA,iBAMC;UAQPtG,EAJI,CAAAG,YAAA,EAAM,EACF,EAGA;;;UA3FEH,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAO,UAAA,SAAAwF,GAAA,CAAAvC,oBAAA,CAA0B;UAW1BxD,EAAA,CAAAM,SAAA,EAAwD;UAAxDN,EAAA,CAAAO,UAAA,UAAAwF,GAAA,CAAAvC,oBAAA,IAAAuC,GAAA,CAAAhE,cAAA,CAAAwE,MAAA,KAAwD;UAcxDvG,EAAA,CAAAM,SAAA,EAA0D;UAA1DN,EAAA,CAAAO,UAAA,UAAAwF,GAAA,CAAAvC,oBAAA,IAAAuC,GAAA,CAAAhE,cAAA,CAAAwE,MAAA,OAA0D;UAY1DvG,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAO,UAAA,SAAAwF,GAAA,CAAAxC,oBAAA,CAA0B;UAW1BvD,EAAA,CAAAM,SAAA,EAAwD;UAAxDN,EAAA,CAAAO,UAAA,UAAAwF,GAAA,CAAAxC,oBAAA,IAAAwC,GAAA,CAAArD,cAAA,CAAA6D,MAAA,KAAwD;UAkBxDvG,EAAA,CAAAM,SAAA,EAA0D;UAA1DN,EAAA,CAAAO,UAAA,UAAAwF,GAAA,CAAAxC,oBAAA,IAAAwC,GAAA,CAAArD,cAAA,CAAA6D,MAAA,OAA0D;UAYvCvG,EAAA,CAAAM,SAAA,GAAa;UAAbN,EAAA,CAAAO,UAAA,YAAAwF,GAAA,CAAAzC,UAAA,CAAa;;;qBD/EtC7D,YAAY,EAAA+G,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZhH,YAAY,EAAAyF,EAAA,CAAAwB,UAAA,EAAAxB,EAAA,CAAAyB,gBAAA,EACZjH,WAAW,EAAAkH,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,kBAAA,EACXnH,cAAc,EACdC,yBAAyB,EACzBC,uBAAuB,EACvBC,oBAAoB;MAAAiH,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}