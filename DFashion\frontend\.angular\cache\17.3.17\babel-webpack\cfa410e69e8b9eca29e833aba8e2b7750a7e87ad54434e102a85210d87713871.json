{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/cart.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction CartComponent_p_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.getTotalItems(), \" items (\", ctx_r0.cartItems.length, \" unique)\");\n  }\n}\nfunction CartComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function CartComponent_div_6_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.toggleSelectAll());\n    });\n    i0.ɵɵelement(2, \"i\", 10);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function CartComponent_div_6_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.bulkRemoveItems());\n    });\n    i0.ɵɵelement(5, \"i\", 12);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function CartComponent_div_6_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.refreshCart());\n    });\n    i0.ɵɵelement(8, \"i\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"selected\", ctx_r0.allItemsSelected());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.allItemsSelected() ? \"Deselect All\" : \"Select All\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.selectedItems.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Remove Selected (\", ctx_r0.selectedItems.length, \") \");\n  }\n}\nfunction CartComponent_div_7_div_2_div_11_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Size: \", item_r5.size, \"\");\n  }\n}\nfunction CartComponent_div_7_div_2_div_11_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Color: \", item_r5.color, \"\");\n  }\n}\nfunction CartComponent_div_7_div_2_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtemplate(1, CartComponent_div_7_div_2_div_11_span_1_Template, 2, 1, \"span\", 62)(2, CartComponent_div_7_div_2_div_11_span_2_Template, 2, 1, \"span\", 62);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.size);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.color);\n  }\n}\nfunction CartComponent_div_7_div_2_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind1(2, 1, item_r5.product.originalPrice), \" \");\n  }\n}\nfunction CartComponent_div_7_div_2_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getDiscountPercentage(item_r5.product.originalPrice, item_r5.product.price), \"% OFF \");\n  }\n}\nfunction CartComponent_div_7_div_2_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" You save: \\u20B9\", i0.ɵɵpipeBind1(2, 1, (item_r5.product.originalPrice - item_r5.product.price) * item_r5.quantity), \" \");\n  }\n}\nfunction CartComponent_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31)(2, \"input\", 32);\n    i0.ɵɵlistener(\"change\", function CartComponent_div_7_div_2_Template_input_change_2_listener() {\n      const item_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.toggleItemSelection(item_r5._id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"label\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 34);\n    i0.ɵɵelement(5, \"img\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 36)(7, \"h3\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 37);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, CartComponent_div_7_div_2_div_11_Template, 3, 2, \"div\", 38);\n    i0.ɵɵelementStart(12, \"div\", 39)(13, \"div\", 40)(14, \"span\", 41);\n    i0.ɵɵtext(15, \"Unit Price:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 42);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, CartComponent_div_7_div_2_span_19_Template, 3, 3, \"span\", 43)(20, CartComponent_div_7_div_2_span_20_Template, 2, 1, \"span\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 45)(22, \"span\", 46);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\", 47);\n    i0.ɵɵtext(25, \"Item Total: \");\n    i0.ɵɵelementStart(26, \"strong\");\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"number\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(29, \"div\", 48)(30, \"div\", 49);\n    i0.ɵɵtext(31, \"Quantity:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 50)(33, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function CartComponent_div_7_div_2_Template_button_click_33_listener() {\n      const item_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.decreaseQuantity(item_r5));\n    });\n    i0.ɵɵelement(34, \"i\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"span\", 53);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function CartComponent_div_7_div_2_Template_button_click_37_listener() {\n      const item_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.increaseQuantity(item_r5));\n    });\n    i0.ɵɵelement(38, \"i\", 55);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 56)(40, \"div\", 57);\n    i0.ɵɵtext(41, \"Total:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"div\", 58);\n    i0.ɵɵtext(43);\n    i0.ɵɵpipe(44, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(45, CartComponent_div_7_div_2_div_45_Template, 3, 3, \"div\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function CartComponent_div_7_div_2_Template_button_click_46_listener() {\n      const item_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.removeItem(item_r5));\n    });\n    i0.ɵɵelement(47, \"i\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", ctx_r0.selectedItems.includes(item_r5._id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r0.selectedItems.includes(item_r5._id))(\"id\", \"item-\" + item_r5._id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", \"item-\" + item_r5._id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", item_r5.product.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", item_r5.product.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r5.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r5.product.brand);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.size || item_r5.color);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(18, 19, item_r5.product.price), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r5.product.originalPrice && item_r5.product.originalPrice > item_r5.product.price);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r5.product.originalPrice && item_r5.product.originalPrice > item_r5.product.price);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Quantity: \", item_r5.quantity, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(28, 21, item_r5.product.price * item_r5.quantity), \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", item_r5.quantity <= 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r5.quantity);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(44, 23, item_r5.product.price * item_r5.quantity), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r5.product.originalPrice && item_r5.product.originalPrice > item_r5.product.price);\n  }\n}\nfunction CartComponent_div_7_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"div\", 68)(2, \"span\", 69);\n    i0.ɵɵtext(3, \"Selected Items:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 70);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 68)(7, \"span\", 69);\n    i0.ɵɵtext(8, \"Selected Quantity:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 70);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.selectedItems.length, \" of \", ctx_r0.cartItems.length, \" items\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.getSelectedItemsCount(), \" items\");\n  }\n}\nfunction CartComponent_div_7_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 72);\n    i0.ɵɵelement(2, \"i\", 73);\n    i0.ɵɵelementStart(3, \"div\", 74)(4, \"span\", 75);\n    i0.ɵɵtext(5, \"Selected Items Total\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 76);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"number\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(8, 1, ctx_r0.getSelectedItemsTotal()), \"\");\n  }\n}\nfunction CartComponent_div_7_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77);\n    i0.ɵɵelement(1, \"i\", 78);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Select items to see total amount\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CartComponent_div_7_div_10_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"span\");\n    i0.ɵɵtext(2, \"You Save\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 66);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(5, 1, ctx_r0.getSelectedItemsSavings()), \"\");\n  }\n}\nfunction CartComponent_div_7_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79)(1, \"div\", 80)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, CartComponent_div_7_div_10_div_7_Template, 6, 3, \"div\", 81);\n    i0.ɵɵelementStart(8, \"div\", 80)(9, \"span\");\n    i0.ɵɵtext(10, \"Shipping\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 82);\n    i0.ɵɵtext(12, \"FREE\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 80)(14, \"span\");\n    i0.ɵɵtext(15, \"Tax (18% GST)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \"Included\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(18, \"hr\");\n    i0.ɵɵelementStart(19, \"div\", 83)(20, \"span\")(21, \"strong\");\n    i0.ɵɵtext(22, \"Final Total\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"span\")(24, \"strong\");\n    i0.ɵɵtext(25);\n    i0.ɵɵpipe(26, \"number\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Subtotal (\", ctx_r0.getSelectedItemsCount(), \" items)\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(6, 4, ctx_r0.getSelectedItemsTotal()), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getSelectedItemsSavings() > 0);\n    i0.ɵɵadvance(18);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(26, 6, ctx_r0.getSelectedItemsTotal()), \"\");\n  }\n}\nfunction CartComponent_div_7_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84);\n    i0.ɵɵelement(1, \"hr\");\n    i0.ɵɵelementStart(2, \"h4\");\n    i0.ɵɵtext(3, \"All Cart Items\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 80)(5, \"span\");\n    i0.ɵɵtext(6, \"Total Items in Cart:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 80)(10, \"span\");\n    i0.ɵɵtext(11, \"Total Cart Value:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.getCartBreakdown().totalItems, \" unique (\", ctx_r0.getCartBreakdown().totalQuantity, \" items)\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind1(14, 3, ctx_r0.getCartBreakdown().finalTotal), \"\");\n  }\n}\nfunction CartComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16);\n    i0.ɵɵtemplate(2, CartComponent_div_7_div_2_Template, 48, 25, \"div\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 18)(4, \"div\", 19)(5, \"h3\");\n    i0.ɵɵtext(6, \"Order Summary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, CartComponent_div_7_div_7_Template, 11, 3, \"div\", 20)(8, CartComponent_div_7_div_8_Template, 9, 3, \"div\", 21)(9, CartComponent_div_7_div_9_Template, 4, 0, \"div\", 22)(10, CartComponent_div_7_div_10_Template, 27, 8, \"div\", 23)(11, CartComponent_div_7_div_11_Template, 15, 5, \"div\", 24);\n    i0.ɵɵelementStart(12, \"div\", 25)(13, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function CartComponent_div_7_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.proceedToCheckout());\n    });\n    i0.ɵɵelement(14, \"i\", 27);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function CartComponent_div_7_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.continueShopping());\n    });\n    i0.ɵɵelement(17, \"i\", 29);\n    i0.ɵɵtext(18, \" Continue Shopping \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.cartItems);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.cartItems.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedItems.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedItems.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedItems.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedItems.length !== ctx_r0.cartItems.length && ctx_r0.cartItems.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.selectedItems.length === 0)(\"title\", ctx_r0.selectedItems.length === 0 ? \"Select items to checkout\" : \"Proceed to checkout with selected items\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Checkout Selected (\", ctx_r0.selectedItems.length, \") \");\n  }\n}\nfunction CartComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85);\n    i0.ɵɵelement(1, \"i\", 73);\n    i0.ɵɵelementStart(2, \"h3\");\n    i0.ɵɵtext(3, \"Your cart is empty\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Add some products to get started\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function CartComponent_div_8_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.continueShopping());\n    });\n    i0.ɵɵtext(7, \" Shop Now \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CartComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87);\n    i0.ɵɵelement(1, \"div\", 88);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading cart...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let CartComponent = /*#__PURE__*/(() => {\n  class CartComponent {\n    constructor(cartService, router) {\n      this.cartService = cartService;\n      this.router = router;\n      this.cartItems = [];\n      this.cartSummary = null;\n      this.isLoading = true;\n      this.selectedItems = [];\n      this.cartCount = 0;\n    }\n    ngOnInit() {\n      this.loadCart();\n      this.subscribeToCartUpdates();\n      this.subscribeToCartCount();\n    }\n    loadCart() {\n      this.isLoading = true;\n      this.cartService.getCart().subscribe({\n        next: response => {\n          this.cartItems = response.cart?.items || [];\n          this.cartSummary = response.summary;\n          this.isLoading = false;\n          // Select all items by default\n          this.selectedItems = this.cartItems.map(item => item._id);\n          console.log('🛒 Cart component loaded:', this.cartItems.length, 'items');\n          console.log('🛒 Cart summary:', this.cartSummary);\n          console.log('🛒 Detailed cart items:', this.cartItems.map(item => ({\n            id: item._id,\n            name: item.product?.name,\n            quantity: item.quantity,\n            unitPrice: item.product?.price,\n            itemTotal: item.product?.price * item.quantity,\n            originalPrice: item.product?.originalPrice\n          })));\n          // Log cart breakdown for debugging\n          if (this.cartItems.length > 0) {\n            const breakdown = this.getCartBreakdown();\n            console.log('🛒 Cart breakdown:', breakdown);\n            console.log('🛒 Selected items breakdown:', this.getSelectedItemsBreakdown());\n          }\n        },\n        error: error => {\n          console.error('❌ Failed to load cart:', error);\n          this.isLoading = false;\n        }\n      });\n    }\n    subscribeToCartUpdates() {\n      this.cartService.cartItems$.subscribe(items => {\n        this.cartItems = items;\n        this.isLoading = false;\n        console.log('🔄 Cart items updated via subscription:', items.length, 'items');\n        // Clear selections when cart updates\n        this.selectedItems = this.selectedItems.filter(id => items.some(item => item._id === id));\n      });\n      this.cartService.cartSummary$.subscribe(summary => {\n        this.cartSummary = summary;\n        console.log('🔄 Cart summary updated:', summary);\n      });\n    }\n    subscribeToCartCount() {\n      this.cartService.cartItemCount$.subscribe(count => {\n        this.cartCount = count;\n      });\n    }\n    // Selection methods\n    toggleItemSelection(itemId) {\n      const index = this.selectedItems.indexOf(itemId);\n      if (index > -1) {\n        this.selectedItems.splice(index, 1);\n      } else {\n        this.selectedItems.push(itemId);\n      }\n      console.log('🛒 Item selection toggled:', itemId, 'Selected items:', this.selectedItems.length);\n      console.log('🛒 Updated selected items breakdown:', this.getSelectedItemsBreakdown());\n    }\n    isItemSelected(itemId) {\n      return this.selectedItems.includes(itemId);\n    }\n    toggleSelectAll() {\n      if (this.allItemsSelected()) {\n        this.selectedItems = [];\n      } else {\n        this.selectedItems = this.cartItems.map(item => item._id);\n      }\n    }\n    allItemsSelected() {\n      return this.cartItems.length > 0 && this.selectedItems.length === this.cartItems.length;\n    }\n    // Bulk operations\n    bulkRemoveItems() {\n      if (this.selectedItems.length === 0) return;\n      if (confirm(`Are you sure you want to remove ${this.selectedItems.length} item(s) from your cart?`)) {\n        this.cartService.bulkRemoveFromCart(this.selectedItems).subscribe({\n          next: response => {\n            console.log(`✅ ${response.removedCount} items removed from cart`);\n            this.selectedItems = [];\n            this.loadCart();\n          },\n          error: error => {\n            console.error('Failed to remove items:', error);\n          }\n        });\n      }\n    }\n    refreshCart() {\n      this.isLoading = true;\n      this.cartService.refreshCartCount();\n      this.loadCart();\n    }\n    increaseQuantity(item) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.cartService.updateCartItem(item._id, item.quantity + 1).subscribe({\n          next: () => {\n            _this.loadCart(); // Refresh cart\n          },\n          error: error => {\n            console.error('Failed to update quantity:', error);\n          }\n        });\n      })();\n    }\n    decreaseQuantity(item) {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        if (item.quantity > 1) {\n          _this2.cartService.updateCartItem(item._id, item.quantity - 1).subscribe({\n            next: () => {\n              _this2.loadCart(); // Refresh cart\n            },\n            error: error => {\n              console.error('Failed to update quantity:', error);\n            }\n          });\n        }\n      })();\n    }\n    removeItem(item) {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        _this3.cartService.removeFromCart(item._id).subscribe({\n          next: () => {\n            _this3.loadCart(); // Refresh cart\n          },\n          error: error => {\n            console.error('Failed to remove item:', error);\n          }\n        });\n      })();\n    }\n    getTotalItems() {\n      return this.cartSummary?.totalQuantity || 0;\n    }\n    getSubtotal() {\n      return this.cartSummary?.subtotal || 0;\n    }\n    getDiscount() {\n      return this.cartSummary?.discount || 0;\n    }\n    getTotal() {\n      return this.cartSummary?.total || 0;\n    }\n    // Calculate discount percentage\n    getDiscountPercentage(originalPrice, currentPrice) {\n      if (!originalPrice || originalPrice <= currentPrice) return 0;\n      return Math.round((originalPrice - currentPrice) / originalPrice * 100);\n    }\n    // Get individual item total\n    getItemTotal(item) {\n      return item.product.price * item.quantity;\n    }\n    // Get individual item savings\n    getItemSavings(item) {\n      if (!item.product.originalPrice || item.product.originalPrice <= item.product.price) return 0;\n      return (item.product.originalPrice - item.product.price) * item.quantity;\n    }\n    // Get cart breakdown for detailed display\n    getCartBreakdown() {\n      return {\n        totalItems: this.cartItems.length,\n        totalQuantity: this.cartItems.reduce((sum, item) => sum + item.quantity, 0),\n        subtotal: this.cartItems.reduce((sum, item) => sum + (item.product.originalPrice || item.product.price) * item.quantity, 0),\n        totalSavings: this.cartItems.reduce((sum, item) => sum + this.getItemSavings(item), 0),\n        finalTotal: this.cartItems.reduce((sum, item) => sum + this.getItemTotal(item), 0)\n      };\n    }\n    // Get selected items breakdown for detailed display\n    getSelectedItemsBreakdown() {\n      const selectedCartItems = this.cartItems.filter(item => this.selectedItems.includes(item._id));\n      return {\n        selectedItems: selectedCartItems.length,\n        selectedQuantity: selectedCartItems.reduce((sum, item) => sum + item.quantity, 0),\n        selectedSubtotal: selectedCartItems.reduce((sum, item) => sum + (item.product.originalPrice || item.product.price) * item.quantity, 0),\n        selectedSavings: selectedCartItems.reduce((sum, item) => sum + this.getItemSavings(item), 0),\n        selectedTotal: selectedCartItems.reduce((sum, item) => sum + this.getItemTotal(item), 0)\n      };\n    }\n    // Get selected items totals for display\n    getSelectedItemsTotal() {\n      const selectedCartItems = this.cartItems.filter(item => this.selectedItems.includes(item._id));\n      return selectedCartItems.reduce((sum, item) => sum + this.getItemTotal(item), 0);\n    }\n    getSelectedItemsCount() {\n      const selectedCartItems = this.cartItems.filter(item => this.selectedItems.includes(item._id));\n      return selectedCartItems.reduce((sum, item) => sum + item.quantity, 0);\n    }\n    getSelectedItemsSavings() {\n      const selectedCartItems = this.cartItems.filter(item => this.selectedItems.includes(item._id));\n      return selectedCartItems.reduce((sum, item) => sum + this.getItemSavings(item), 0);\n    }\n    proceedToCheckout() {\n      this.router.navigate(['/shop/checkout']);\n    }\n    continueShopping() {\n      this.router.navigate(['/']);\n    }\n    static {\n      this.ɵfac = function CartComponent_Factory(t) {\n        return new (t || CartComponent)(i0.ɵɵdirectiveInject(i1.CartService), i0.ɵɵdirectiveInject(i2.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CartComponent,\n        selectors: [[\"app-cart\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 10,\n        vars: 5,\n        consts: [[1, \"cart-page\"], [1, \"cart-header\"], [1, \"header-main\"], [4, \"ngIf\"], [\"class\", \"header-actions\", 4, \"ngIf\"], [\"class\", \"cart-content\", 4, \"ngIf\"], [\"class\", \"empty-cart\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [1, \"header-actions\"], [1, \"select-all-btn\", 3, \"click\"], [1, \"fas\", \"fa-check\"], [1, \"bulk-remove-btn\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-trash\"], [\"title\", \"Refresh cart\", 1, \"refresh-btn\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [1, \"cart-content\"], [1, \"cart-items\"], [\"class\", \"cart-item\", 3, \"selected\", 4, \"ngFor\", \"ngForOf\"], [1, \"cart-summary\"], [1, \"summary-card\"], [\"class\", \"selection-status\", 4, \"ngIf\"], [\"class\", \"cart-total-highlight\", 4, \"ngIf\"], [\"class\", \"no-selection-message\", 4, \"ngIf\"], [\"class\", \"summary-details\", 4, \"ngIf\"], [\"class\", \"all-items-summary\", 4, \"ngIf\"], [1, \"action-buttons\"], [1, \"checkout-btn\", 3, \"click\", \"disabled\", \"title\"], [1, \"fas\", \"fa-credit-card\"], [1, \"continue-shopping-btn\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\"], [1, \"cart-item\"], [1, \"item-checkbox\"], [\"type\", \"checkbox\", 3, \"change\", \"checked\", \"id\"], [3, \"for\"], [1, \"item-image\"], [3, \"src\", \"alt\"], [1, \"item-details\"], [1, \"brand\"], [\"class\", \"item-options\", 4, \"ngIf\"], [1, \"item-price-section\"], [1, \"unit-price\"], [1, \"price-label\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"quantity-price-info\"], [1, \"quantity-info\"], [1, \"item-total-label\"], [1, \"item-quantity-controls\"], [1, \"quantity-label\"], [1, \"quantity-controls\"], [1, \"qty-btn\", \"decrease\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-minus\"], [1, \"quantity-display\"], [1, \"qty-btn\", \"increase\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [1, \"item-total-section\"], [1, \"total-label\"], [1, \"total-amount\"], [\"class\", \"savings\", 4, \"ngIf\"], [1, \"remove-btn\", 3, \"click\"], [1, \"item-options\"], [\"class\", \"option-tag\", 4, \"ngIf\"], [1, \"option-tag\"], [1, \"original-price\"], [1, \"discount-badge\"], [1, \"savings\"], [1, \"selection-status\"], [1, \"status-item\"], [1, \"status-label\"], [1, \"status-value\"], [1, \"cart-total-highlight\"], [1, \"total-amount-display\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"amount-details\"], [1, \"amount-label\"], [1, \"amount-value\"], [1, \"no-selection-message\"], [1, \"fas\", \"fa-info-circle\"], [1, \"summary-details\"], [1, \"summary-row\"], [\"class\", \"summary-row\", 4, \"ngIf\"], [1, \"free-shipping\"], [1, \"summary-row\", \"total\"], [1, \"all-items-summary\"], [1, \"empty-cart\"], [1, \"shop-now-btn\", 3, \"click\"], [1, \"loading-container\"], [1, \"spinner\"]],\n        template: function CartComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n            i0.ɵɵtext(4, \"Shopping Cart\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(5, CartComponent_p_5_Template, 2, 2, \"p\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(6, CartComponent_div_6_Template, 9, 5, \"div\", 4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(7, CartComponent_div_7_Template, 19, 9, \"div\", 5)(8, CartComponent_div_8_Template, 8, 0, \"div\", 6)(9, CartComponent_div_9_Template, 4, 0, \"div\", 7);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.cartItems.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.cartItems.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.cartItems.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.cartItems.length === 0 && !ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          }\n        },\n        dependencies: [CommonModule, i3.NgForOf, i3.NgIf, i3.DecimalPipe],\n        styles: [\".cart-page[_ngcontent-%COMP%]{padding:2rem;max-width:1200px;margin:0 auto}.cart-header[_ngcontent-%COMP%]{margin-bottom:2rem;display:flex;justify-content:space-between;align-items:flex-start;flex-wrap:wrap;gap:1rem}.header-main[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;margin-bottom:.5rem;color:#333}.header-actions[_ngcontent-%COMP%]{display:flex;gap:.5rem;align-items:center;flex-wrap:wrap}.select-all-btn[_ngcontent-%COMP%], .bulk-remove-btn[_ngcontent-%COMP%], .refresh-btn[_ngcontent-%COMP%]{padding:.5rem 1rem;border:1px solid #ddd;border-radius:6px;background:#fff;cursor:pointer;font-size:.9rem;transition:all .2s;display:flex;align-items:center;gap:.5rem}.select-all-btn[_ngcontent-%COMP%]:hover, .refresh-btn[_ngcontent-%COMP%]:hover{background:#f8f9fa;border-color:#007bff}.select-all-btn.selected[_ngcontent-%COMP%]{background:#007bff;color:#fff;border-color:#007bff}.bulk-remove-btn[_ngcontent-%COMP%]{background:#dc3545;color:#fff;border-color:#dc3545}.bulk-remove-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:#c82333}.bulk-remove-btn[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.refresh-btn[_ngcontent-%COMP%]{padding:.5rem;min-width:40px;justify-content:center}.cart-content[_ngcontent-%COMP%]{display:grid;grid-template-columns:2fr 1fr;gap:2rem}.cart-items[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:1rem}.cart-item[_ngcontent-%COMP%]{display:grid;grid-template-columns:auto 100px 1fr auto auto auto;gap:1rem;padding:1rem;border:1px solid #eee;border-radius:8px;align-items:center;transition:all .2s}.cart-item.selected[_ngcontent-%COMP%]{border-color:#007bff;background:#007bf60d}.item-checkbox[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}.item-checkbox[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]{width:18px;height:18px;cursor:pointer;accent-color:#007bff}.item-checkbox[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{cursor:pointer;margin:0}.item-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100px;height:100px;object-fit:cover;border-radius:8px}.item-details[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:600;margin-bottom:.5rem;color:#333}.brand[_ngcontent-%COMP%]{color:#666;font-size:.9rem;margin-bottom:.5rem}.item-options[_ngcontent-%COMP%]{display:flex;gap:1rem;font-size:.9rem;color:#666;margin-bottom:.5rem}.option-tag[_ngcontent-%COMP%]{background:#f0f0f0;padding:2px 8px;border-radius:12px;font-size:.8rem;color:#555}.item-price-section[_ngcontent-%COMP%]{margin-top:.5rem}.unit-price[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;margin-bottom:.5rem}.price-label[_ngcontent-%COMP%]{font-size:.85rem;color:#666;font-weight:500}.quantity-price-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.25rem}.quantity-info[_ngcontent-%COMP%]{font-size:.9rem;color:#666}.item-total-label[_ngcontent-%COMP%]{font-size:.95rem;color:#333}.discount-badge[_ngcontent-%COMP%]{background:#28a745;color:#fff;padding:2px 6px;border-radius:8px;font-size:.75rem;font-weight:600}.item-price[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.current-price[_ngcontent-%COMP%]{font-weight:600;color:#e91e63}.original-price[_ngcontent-%COMP%]{color:#999;text-decoration:line-through;font-size:.9rem}.item-quantity-controls[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:.5rem}.quantity-label[_ngcontent-%COMP%]{font-size:.85rem;color:#666;font-weight:500}.quantity-controls[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;border:1px solid #ddd;border-radius:8px;padding:.25rem;background:#fff}.quantity-display[_ngcontent-%COMP%]{min-width:2.5rem;text-align:center;font-weight:600;font-size:1rem;color:#333}.item-quantity[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;border:1px solid #ddd;border-radius:4px;padding:.25rem}.qty-btn[_ngcontent-%COMP%]{width:30px;height:30px;border:none;background:#f8f9fa;border-radius:4px;cursor:pointer;display:flex;align-items:center;justify-content:center;font-weight:600}.qty-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:#e9ecef}.qty-btn[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.quantity[_ngcontent-%COMP%]{min-width:2rem;text-align:center;font-weight:600}.item-total-section[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:.25rem;text-align:center}.total-label[_ngcontent-%COMP%]{font-size:.85rem;color:#666;font-weight:500}.total-amount[_ngcontent-%COMP%]{font-weight:700;font-size:1.2rem;color:#e91e63}.savings[_ngcontent-%COMP%]{font-size:.8rem;color:#28a745;font-weight:500}.item-total[_ngcontent-%COMP%]{font-weight:600;font-size:1.1rem;color:#333}.remove-btn[_ngcontent-%COMP%]{width:40px;height:40px;border:none;background:#f8f9fa;border-radius:50%;cursor:pointer;display:flex;align-items:center;justify-content:center;color:#dc3545;transition:all .2s}.remove-btn[_ngcontent-%COMP%]:hover{background:#dc3545;color:#fff}.summary-card[_ngcontent-%COMP%]{background:#f8f9fa;padding:1.5rem;border-radius:8px;position:sticky;top:2rem}.summary-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin-bottom:1rem;color:#333}.cart-breakdown[_ngcontent-%COMP%]{background:#fff;border:1px solid #e0e0e0;border-radius:8px;padding:1rem;margin-bottom:1rem}.breakdown-item[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:.5rem}.breakdown-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.breakdown-label[_ngcontent-%COMP%]{font-size:.9rem;color:#666;font-weight:500}.breakdown-value[_ngcontent-%COMP%]{font-size:.9rem;color:#333;font-weight:600}.summary-details[_ngcontent-%COMP%]{margin-top:1rem}.free-shipping[_ngcontent-%COMP%], .savings[_ngcontent-%COMP%]{color:#28a745;font-weight:600}.action-buttons[_ngcontent-%COMP%]{margin-top:1.5rem}.cart-total-highlight[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4834d4,#686de0);border-radius:12px;padding:16px;margin-bottom:20px;color:#fff}.total-amount-display[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.total-amount-display[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:24px;color:#fff}.amount-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;flex:1}.amount-label[_ngcontent-%COMP%]{font-size:14px;font-weight:500;opacity:.9;margin-bottom:4px}.amount-value[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#fff}.summary-row[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-bottom:.75rem}.summary-row.total[_ngcontent-%COMP%]{font-weight:700;font-size:1.2rem;color:#333}.discount[_ngcontent-%COMP%]{color:#28a745}.selection-status[_ngcontent-%COMP%]{background:#f8f9fa;border-radius:8px;padding:12px;margin-bottom:16px;border-left:4px solid #007bff}.status-item[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-bottom:8px}.status-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.status-label[_ngcontent-%COMP%]{font-weight:500;color:#666}.status-value[_ngcontent-%COMP%]{font-weight:600;color:#333}.no-selection-message[_ngcontent-%COMP%]{text-align:center;padding:24px;background:#fff3cd;border:1px solid #ffeaa7;border-radius:8px;margin-bottom:16px}.no-selection-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:24px;color:#856404;margin-bottom:8px}.no-selection-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#856404;font-weight:500}.all-items-summary[_ngcontent-%COMP%]{background:#f8f9fa;border-radius:8px;padding:12px;margin-top:16px}.all-items-summary[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 12px;font-size:14px;color:#666;text-transform:uppercase;letter-spacing:.5px}.checkout-btn[_ngcontent-%COMP%]{width:100%;background:#007bff;color:#fff;border:none;padding:1rem;border-radius:8px;font-size:1.1rem;font-weight:600;cursor:pointer;margin-bottom:1rem;transition:background .2s}.checkout-btn[_ngcontent-%COMP%]:disabled{background:#6c757d;cursor:not-allowed;opacity:.6}.checkout-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:#0056b3}.continue-shopping-btn[_ngcontent-%COMP%]{width:100%;background:transparent;color:#007bff;border:2px solid #007bff;padding:1rem;border-radius:8px;font-size:1rem;font-weight:600;cursor:pointer;transition:all .2s}.continue-shopping-btn[_ngcontent-%COMP%]:hover{background:#007bff;color:#fff}.empty-cart[_ngcontent-%COMP%]{text-align:center;padding:4rem 2rem;color:#666}.empty-cart[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:4rem;margin-bottom:1rem;color:#ddd}.shop-now-btn[_ngcontent-%COMP%]{background:#007bff;color:#fff;border:none;padding:1rem 2rem;border-radius:8px;font-size:1.1rem;font-weight:600;cursor:pointer;margin-top:1rem}.loading-container[_ngcontent-%COMP%]{text-align:center;padding:4rem 2rem}.spinner[_ngcontent-%COMP%]{width:40px;height:40px;border:3px solid #f3f3f3;border-top:3px solid #007bff;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite;margin:0 auto 1rem}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@media (max-width: 768px){.cart-content[_ngcontent-%COMP%]{grid-template-columns:1fr}.cart-item[_ngcontent-%COMP%]{grid-template-columns:80px 1fr;grid-template-rows:auto auto auto;gap:.5rem}.item-quantity[_ngcontent-%COMP%], .item-total[_ngcontent-%COMP%], .remove-btn[_ngcontent-%COMP%]{grid-column:1/-1;justify-self:start}}\"]\n      });\n    }\n  }\n  return CartComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}