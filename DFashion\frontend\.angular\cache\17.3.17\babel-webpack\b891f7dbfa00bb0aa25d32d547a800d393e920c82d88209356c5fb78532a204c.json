{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { c as componentOnReady } from './helpers.js';\n\n// TODO(FW-2832): types\nconst attachComponent = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (delegate, container, component, cssClasses, componentProps, inline) {\n    var _a;\n    if (delegate) {\n      return delegate.attachViewToDom(container, component, componentProps, cssClasses);\n    }\n    if (!inline && typeof component !== 'string' && !(component instanceof HTMLElement)) {\n      throw new Error('framework delegate is missing');\n    }\n    const el = typeof component === 'string' ? (_a = container.ownerDocument) === null || _a === void 0 ? void 0 : _a.createElement(component) : component;\n    if (cssClasses) {\n      cssClasses.forEach(c => el.classList.add(c));\n    }\n    if (componentProps) {\n      Object.assign(el, componentProps);\n    }\n    container.appendChild(el);\n    yield new Promise(resolve => componentOnReady(el, resolve));\n    return el;\n  });\n  return function attachComponent(_x, _x2, _x3, _x4, _x5, _x6) {\n    return _ref.apply(this, arguments);\n  };\n}();\nconst detachComponent = (delegate, element) => {\n  if (element) {\n    if (delegate) {\n      const container = element.parentElement;\n      return delegate.removeViewFromDom(container, element);\n    }\n    element.remove();\n  }\n  return Promise.resolve();\n};\nconst CoreDelegate = () => {\n  let BaseComponent;\n  let Reference;\n  const attachViewToDom = /*#__PURE__*/function () {\n    var _ref2 = _asyncToGenerator(function* (parentElement, userComponent, userComponentProps = {}, cssClasses = []) {\n      var _a, _b;\n      BaseComponent = parentElement;\n      let ChildComponent;\n      /**\n       * If passing in a component via the `component` props\n       * we need to append it inside of our overlay component.\n       */\n      if (userComponent) {\n        /**\n         * If passing in the tag name, create\n         * the element otherwise just get a reference\n         * to the component.\n         */\n        const el = typeof userComponent === 'string' ? (_a = BaseComponent.ownerDocument) === null || _a === void 0 ? void 0 : _a.createElement(userComponent) : userComponent;\n        /**\n         * Add any css classes passed in\n         * via the cssClasses prop on the overlay.\n         */\n        cssClasses.forEach(c => el.classList.add(c));\n        /**\n         * Add any props passed in\n         * via the componentProps prop on the overlay.\n         */\n        Object.assign(el, userComponentProps);\n        /**\n         * Finally, append the component\n         * inside of the overlay component.\n         */\n        BaseComponent.appendChild(el);\n        ChildComponent = el;\n        yield new Promise(resolve => componentOnReady(el, resolve));\n      } else if (BaseComponent.children.length > 0 && (BaseComponent.tagName === 'ION-MODAL' || BaseComponent.tagName === 'ION-POPOVER')) {\n        /**\n         * The delegate host wrapper el is only needed for modals and popovers\n         * because they allow the dev to provide custom content to the overlay.\n         */\n        const root = ChildComponent = BaseComponent.children[0];\n        if (!root.classList.contains('ion-delegate-host')) {\n          /**\n           * If the root element is not a delegate host, it means\n           * that the overlay has not been presented yet and we need\n           * to create the containing element with the specified classes.\n           */\n          const el = (_b = BaseComponent.ownerDocument) === null || _b === void 0 ? void 0 : _b.createElement('div');\n          // Add a class to track if the root element was created by the delegate.\n          el.classList.add('ion-delegate-host');\n          cssClasses.forEach(c => el.classList.add(c));\n          // Move each child from the original template to the new parent element.\n          el.append(...BaseComponent.children);\n          // Append the new parent element to the original parent element.\n          BaseComponent.appendChild(el);\n          /**\n           * Update the ChildComponent to be the\n           * newly created div in the event that one\n           * does not already exist.\n           */\n          ChildComponent = el;\n        }\n      }\n      /**\n       * Get the root of the app and\n       * add the overlay there.\n       */\n      const app = document.querySelector('ion-app') || document.body;\n      /**\n       * Create a placeholder comment so that\n       * we can return this component to where\n       * it was previously.\n       */\n      Reference = document.createComment('ionic teleport');\n      BaseComponent.parentNode.insertBefore(Reference, BaseComponent);\n      app.appendChild(BaseComponent);\n      /**\n       * We return the child component rather than the overlay\n       * reference itself since modal and popover will\n       * use this to wait for any Ionic components in the child view\n       * to be ready (i.e. componentOnReady) when using the\n       * lazy loaded component bundle.\n       *\n       * However, we fall back to returning BaseComponent\n       * in the event that a modal or popover is presented\n       * with no child content.\n       */\n      return ChildComponent !== null && ChildComponent !== void 0 ? ChildComponent : BaseComponent;\n    });\n    return function attachViewToDom(_x7, _x8) {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n  const removeViewFromDom = () => {\n    /**\n     * Return component to where it was previously in the DOM.\n     */\n    if (BaseComponent && Reference) {\n      Reference.parentNode.insertBefore(BaseComponent, Reference);\n      Reference.remove();\n    }\n    return Promise.resolve();\n  };\n  return {\n    attachViewToDom,\n    removeViewFromDom\n  };\n};\nexport { CoreDelegate as C, attachComponent as a, detachComponent as d };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}