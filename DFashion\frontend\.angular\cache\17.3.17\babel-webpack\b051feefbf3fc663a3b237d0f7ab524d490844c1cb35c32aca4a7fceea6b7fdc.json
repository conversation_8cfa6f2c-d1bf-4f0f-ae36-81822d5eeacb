{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule, Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../core/services/cart.service\";\nimport * as i3 from \"../../core/services/payment.service\";\nimport * as i4 from \"../../core/services/auth.service\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@angular/common/http\";\nimport * as i8 from \"@angular/common\";\nfunction CheckoutComponent_div_26_div_3_div_1_div_7_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Size: \", item_r1.size, \"\");\n  }\n}\nfunction CheckoutComponent_div_26_div_3_div_1_div_7_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Color: \", item_r1.color, \"\");\n  }\n}\nfunction CheckoutComponent_div_26_div_3_div_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, CheckoutComponent_div_26_div_3_div_1_div_7_span_1_Template, 2, 1, \"span\", 32)(2, CheckoutComponent_div_26_div_3_div_1_div_7_span_2_Template, 2, 1, \"span\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r1.size);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r1.color);\n  }\n}\nfunction CheckoutComponent_div_26_div_3_div_1_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u20B9\", i0.ɵɵpipeBind2(2, 1, item_r1.product.originalPrice, \"1.0-0\"), \" \");\n  }\n}\nfunction CheckoutComponent_div_26_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"img\", 22);\n    i0.ɵɵelementStart(2, \"div\", 23)(3, \"h3\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 24);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, CheckoutComponent_div_26_div_3_div_1_div_7_Template, 3, 2, \"div\", 25);\n    i0.ɵɵelementStart(8, \"div\", 26)(9, \"span\", 27);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, CheckoutComponent_div_26_div_3_div_1_span_12_Template, 3, 4, \"span\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 29)(14, \"span\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 30);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r1.getImageUrl(item_r1.product.images[0]), i0.ɵɵsanitizeUrl)(\"alt\", item_r1.product.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r1.product.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.product.brand);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r1.size || item_r1.color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(11, 9, item_r1.product.price, \"1.0-0\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r1.product.originalPrice);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Qty: \", item_r1.quantity, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(18, 12, item_r1.product.price * item_r1.quantity, \"1.0-0\"), \"\");\n  }\n}\nfunction CheckoutComponent_div_26_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtemplate(1, CheckoutComponent_div_26_div_3_div_1_Template, 19, 15, \"div\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.cartItems);\n  }\n}\nfunction CheckoutComponent_div_26_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"i\", 35);\n    i0.ɵɵelementStart(2, \"h3\");\n    i0.ɵɵtext(3, \"Your cart is empty\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Add some items to your cart to continue\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function CheckoutComponent_div_26_div_4_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToShopping());\n    });\n    i0.ɵɵtext(7, \"Continue Shopping\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CheckoutComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"h2\");\n    i0.ɵɵtext(2, \"Review Your Order\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CheckoutComponent_div_26_div_3_Template, 2, 1, \"div\", 17)(4, CheckoutComponent_div_26_div_4_Template, 8, 0, \"div\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cartItems.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.cartItems.length === 0);\n  }\n}\nfunction CheckoutComponent_div_27_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtext(1, \" Full name is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CheckoutComponent_div_27_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtext(1, \" Valid phone number is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CheckoutComponent_div_27_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtext(1, \" Address is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CheckoutComponent_div_27_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtext(1, \" City is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CheckoutComponent_div_27_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtext(1, \" State is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CheckoutComponent_div_27_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtext(1, \" Valid 6-digit pincode is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CheckoutComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"h2\");\n    i0.ɵɵtext(2, \"Shipping Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"form\", 37)(4, \"div\", 38)(5, \"div\", 39)(6, \"label\", 40);\n    i0.ɵɵtext(7, \"Full Name *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"input\", 41);\n    i0.ɵɵtemplate(9, CheckoutComponent_div_27_div_9_Template, 2, 0, \"div\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 39)(11, \"label\", 43);\n    i0.ɵɵtext(12, \"Phone Number *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 44);\n    i0.ɵɵtemplate(14, CheckoutComponent_div_27_div_14_Template, 2, 0, \"div\", 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 39)(16, \"label\", 45);\n    i0.ɵɵtext(17, \"Address Line 1 *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"input\", 46);\n    i0.ɵɵtemplate(19, CheckoutComponent_div_27_div_19_Template, 2, 0, \"div\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 39)(21, \"label\", 47);\n    i0.ɵɵtext(22, \"Address Line 2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(23, \"input\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 38)(25, \"div\", 39)(26, \"label\", 49);\n    i0.ɵɵtext(27, \"City *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(28, \"input\", 50);\n    i0.ɵɵtemplate(29, CheckoutComponent_div_27_div_29_Template, 2, 0, \"div\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 39)(31, \"label\", 51);\n    i0.ɵɵtext(32, \"State *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"select\", 52)(34, \"option\", 53);\n    i0.ɵɵtext(35, \"Select State\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"option\", 54);\n    i0.ɵɵtext(37, \"Maharashtra\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"option\", 55);\n    i0.ɵɵtext(39, \"Delhi\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"option\", 56);\n    i0.ɵɵtext(41, \"Karnataka\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"option\", 57);\n    i0.ɵɵtext(43, \"Tamil Nadu\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"option\", 58);\n    i0.ɵɵtext(45, \"Gujarat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"option\", 59);\n    i0.ɵɵtext(47, \"Rajasthan\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"option\", 60);\n    i0.ɵɵtext(49, \"West Bengal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"option\", 61);\n    i0.ɵɵtext(51, \"Uttar Pradesh\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(52, CheckoutComponent_div_27_div_52_Template, 2, 0, \"div\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"div\", 39)(54, \"label\", 62);\n    i0.ɵɵtext(55, \"Pincode *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(56, \"input\", 63);\n    i0.ɵɵtemplate(57, CheckoutComponent_div_27_div_57_Template, 2, 0, \"div\", 42);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.shippingForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r1.shippingForm.get(\"fullName\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r1.shippingForm.get(\"fullName\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r1.shippingForm.get(\"phone\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r1.shippingForm.get(\"phone\")) == null ? null : tmp_3_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r1.shippingForm.get(\"addressLine1\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r1.shippingForm.get(\"addressLine1\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx_r1.shippingForm.get(\"city\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx_r1.shippingForm.get(\"city\")) == null ? null : tmp_5_0.touched));\n    i0.ɵɵadvance(23);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx_r1.shippingForm.get(\"state\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx_r1.shippingForm.get(\"state\")) == null ? null : tmp_6_0.touched));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx_r1.shippingForm.get(\"pincode\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx_r1.shippingForm.get(\"pincode\")) == null ? null : tmp_7_0.touched));\n  }\n}\nfunction CheckoutComponent_div_28_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵlistener(\"click\", function CheckoutComponent_div_28_div_4_Template_div_click_0_listener() {\n      const method_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.selectPaymentMethod(method_r5.id));\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementStart(2, \"div\", 68)(3, \"h3\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 69)(8, \"input\", 70);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CheckoutComponent_div_28_div_4_Template_input_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedPaymentMethod, $event) || (ctx_r1.selectedPaymentMethod = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const method_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", ctx_r1.selectedPaymentMethod === method_r5.id);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(method_r5.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(method_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(method_r5.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", method_r5.id);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedPaymentMethod);\n  }\n}\nfunction CheckoutComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"h2\");\n    i0.ɵɵtext(2, \"Payment Method\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 65);\n    i0.ɵɵtemplate(4, CheckoutComponent_div_28_div_4_Template, 9, 8, \"div\", 66);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.paymentMethods);\n  }\n}\nfunction CheckoutComponent_div_29_div_1_p_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate((tmp_3_0 = ctx_r1.shippingForm.get(\"addressLine2\")) == null ? null : tmp_3_0.value);\n  }\n}\nfunction CheckoutComponent_div_29_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"h2\");\n    i0.ɵɵtext(2, \"Order Confirmation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 74)(4, \"div\", 75)(5, \"h3\");\n    i0.ɵɵtext(6, \"Shipping Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 76)(8, \"p\")(9, \"strong\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"p\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, CheckoutComponent_div_29_div_1_p_15_Template, 2, 1, \"p\", 32);\n    i0.ɵɵelementStart(16, \"p\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 77)(19, \"h3\");\n    i0.ɵɵtext(20, \"Payment Method\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"p\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate((tmp_2_0 = ctx_r1.shippingForm.get(\"fullName\")) == null ? null : tmp_2_0.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((tmp_3_0 = ctx_r1.shippingForm.get(\"phone\")) == null ? null : tmp_3_0.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((tmp_4_0 = ctx_r1.shippingForm.get(\"addressLine1\")) == null ? null : tmp_4_0.value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx_r1.shippingForm.get(\"addressLine2\")) == null ? null : tmp_5_0.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\"\", (tmp_6_0 = ctx_r1.shippingForm.get(\"city\")) == null ? null : tmp_6_0.value, \", \", (tmp_6_0 = ctx_r1.shippingForm.get(\"state\")) == null ? null : tmp_6_0.value, \" \", (tmp_6_0 = ctx_r1.shippingForm.get(\"pincode\")) == null ? null : tmp_6_0.value, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.getPaymentMethodName(ctx_r1.selectedPaymentMethod));\n  }\n}\nfunction CheckoutComponent_div_29_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79);\n    i0.ɵɵelement(2, \"i\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h2\");\n    i0.ɵɵtext(4, \"Order Placed Successfully!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 81)(8, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function CheckoutComponent_div_29_div_2_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.viewOrder());\n    });\n    i0.ɵɵtext(9, \"View Order\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function CheckoutComponent_div_29_div_2_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.continueShopping());\n    });\n    i0.ɵɵtext(11, \"Continue Shopping\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"Your order #\", ctx_r1.orderNumber, \" has been placed successfully.\");\n  }\n}\nfunction CheckoutComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtemplate(1, CheckoutComponent_div_29_div_1_Template, 23, 8, \"div\", 71)(2, CheckoutComponent_div_29_div_2_Template, 12, 1, \"div\", 72);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.orderPlaced);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.orderPlaced);\n  }\n}\nfunction CheckoutComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"div\", 84);\n    i0.ɵɵelement(2, \"i\", 35);\n    i0.ɵɵelementStart(3, \"div\", 85)(4, \"span\", 86);\n    i0.ɵɵtext(5, \"Cart Total Amount\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 87);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"number\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(8, 1, ctx_r1.cartSummary.total, \"1.0-0\"), \"\");\n  }\n}\nfunction CheckoutComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"span\");\n    i0.ɵɵtext(2, \"Discount\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 88);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"-\\u20B9\", i0.ɵɵpipeBind2(5, 1, ctx_r1.cartSummary.discount, \"1.0-0\"), \"\");\n  }\n}\nfunction CheckoutComponent_button_61_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function CheckoutComponent_button_61_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.previousStep());\n    });\n    i0.ɵɵtext(1, \" Previous \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.processing);\n  }\n}\nfunction CheckoutComponent_button_62_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.currentStep === 3 ? \"Place Order\" : \"Continue\");\n  }\n}\nfunction CheckoutComponent_button_62_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 91);\n    i0.ɵɵtext(2, \" Processing... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CheckoutComponent_button_62_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function CheckoutComponent_button_62_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextStep());\n    });\n    i0.ɵɵtemplate(1, CheckoutComponent_button_62_span_1_Template, 2, 1, \"span\", 32)(2, CheckoutComponent_button_62_span_2_Template, 3, 0, \"span\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canProceed() || ctx_r1.processing);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.processing);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.processing);\n  }\n}\nexport let CheckoutComponent = /*#__PURE__*/(() => {\n  class CheckoutComponent {\n    constructor(fb, cartService,\n    // private checkoutService: CheckoutService,\n    paymentService, authService, snackBar, router, http) {\n      this.fb = fb;\n      this.cartService = cartService;\n      this.paymentService = paymentService;\n      this.authService = authService;\n      this.snackBar = snackBar;\n      this.router = router;\n      this.http = http;\n      this.currentStep = 1;\n      this.cartItems = [];\n      this.cartSummary = null;\n      this.processing = false;\n      this.orderPlaced = false;\n      this.orderNumber = '';\n      this.selectedPaymentMethod = '';\n      this.paymentMethods = [];\n      this.shippingForm = this.fb.group({\n        fullName: ['', Validators.required],\n        phone: ['', [Validators.required, Validators.pattern(/^[6-9]\\d{9}$/)]],\n        addressLine1: ['', Validators.required],\n        addressLine2: [''],\n        city: ['', Validators.required],\n        state: ['', Validators.required],\n        pincode: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\n      });\n    }\n    ngOnInit() {\n      if (!this.authService.isAuthenticated) {\n        this.router.navigate(['/auth/login'], {\n          queryParams: {\n            returnUrl: '/checkout'\n          }\n        });\n        return;\n      }\n      this.loadCartData();\n      this.loadPaymentMethods();\n    }\n    loadPaymentMethods() {\n      this.paymentMethods = this.paymentService.getPaymentMethods();\n    }\n    loadCartData() {\n      // Load cart data from API\n      this.cartItems = [];\n      this.cartSummary = {\n        itemCount: 0,\n        totalQuantity: 0,\n        subtotal: 0,\n        discount: 0,\n        total: 0\n      };\n    }\n    canProceed() {\n      switch (this.currentStep) {\n        case 1:\n          return this.cartItems.length > 0;\n        case 2:\n          return this.shippingForm.valid;\n        case 3:\n          return !!this.selectedPaymentMethod;\n        default:\n          return false;\n      }\n    }\n    nextStep() {\n      if (!this.canProceed()) return;\n      if (this.currentStep === 3) {\n        this.placeOrder();\n      } else {\n        this.currentStep++;\n      }\n    }\n    previousStep() {\n      if (this.currentStep > 1) {\n        this.currentStep--;\n      }\n    }\n    selectPaymentMethod(methodId) {\n      this.selectedPaymentMethod = methodId;\n    }\n    getPaymentMethodName(methodId) {\n      const method = this.paymentMethods.find(m => m.id === methodId);\n      return method ? method.name : '';\n    }\n    placeOrder() {\n      this.processing = true;\n      const orderData = {\n        shippingAddress: this.shippingForm.value,\n        billingAddress: this.shippingForm.value,\n        paymentMethod: this.selectedPaymentMethod\n      };\n      // Create order first\n      this.http.post('/api/orders', orderData).subscribe({\n        next: response => {\n          if (response.success) {\n            const orderId = response.data.order._id;\n            this.processPayment(orderId);\n          } else {\n            this.snackBar.open('Failed to create order', 'Close', {\n              duration: 3000\n            });\n            this.processing = false;\n          }\n        },\n        error: error => {\n          console.error('Order creation error:', error);\n          this.snackBar.open('Failed to create order. Please try again.', 'Close', {\n            duration: 3000\n          });\n          this.processing = false;\n        }\n      });\n    }\n    processPayment(orderId) {\n      const paymentData = {\n        orderId: orderId,\n        paymentMethod: this.selectedPaymentMethod,\n        returnUrl: window.location.origin + '/order-confirmation'\n      };\n      this.paymentService.initiatePayment(paymentData).subscribe({\n        next: response => {\n          if (response.success && response.data) {\n            if (this.selectedPaymentMethod === 'cod') {\n              // Cash on Delivery - order is confirmed immediately\n              this.orderNumber = response.data.orderNumber;\n              this.currentStep = 4;\n              this.orderPlaced = true;\n              this.processing = false;\n              this.snackBar.open('Order placed successfully!', 'Close', {\n                duration: 3000\n              });\n            } else {\n              // Online payment - open Razorpay checkout\n              this.openRazorpayCheckout(response.data);\n            }\n          } else {\n            this.snackBar.open('Payment initiation failed', 'Close', {\n              duration: 3000\n            });\n            this.processing = false;\n          }\n        },\n        error: error => {\n          console.error('Payment initiation error:', error);\n          this.snackBar.open('Payment failed. Please try again.', 'Close', {\n            duration: 3000\n          });\n          this.processing = false;\n        }\n      });\n    }\n    openRazorpayCheckout(paymentData) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const user = _this.authService.currentUserValue;\n          const options = {\n            key: paymentData.razorpayKeyId,\n            amount: paymentData.amount,\n            currency: paymentData.currency,\n            order_id: paymentData.razorpayOrderId,\n            name: 'DFashion',\n            description: `Order #${paymentData.orderNumber}`,\n            image: '/assets/logo.png',\n            prefill: {\n              name: user?.fullName || '',\n              email: user?.email || '',\n              contact: user?.phone || ''\n            },\n            theme: {\n              color: '#3f51b5'\n            },\n            handler: response => {\n              _this.verifyPayment(response, paymentData.paymentId);\n            },\n            modal: {\n              ondismiss: () => {\n                _this.processing = false;\n                _this.snackBar.open('Payment cancelled', 'Close', {\n                  duration: 3000\n                });\n              }\n            }\n          };\n          yield _this.paymentService.openRazorpayCheckout(options);\n        } catch (error) {\n          console.error('Razorpay checkout error:', error);\n          _this.snackBar.open('Failed to open payment gateway', 'Close', {\n            duration: 3000\n          });\n          _this.processing = false;\n        }\n      })();\n    }\n    verifyPayment(razorpayResponse, paymentId) {\n      const verificationData = {\n        razorpay_order_id: razorpayResponse.razorpay_order_id,\n        razorpay_payment_id: razorpayResponse.razorpay_payment_id,\n        razorpay_signature: razorpayResponse.razorpay_signature,\n        paymentId: paymentId\n      };\n      this.paymentService.verifyPayment(verificationData).subscribe({\n        next: response => {\n          if (response.success && response.data) {\n            this.orderNumber = response.data.order.orderNumber;\n            this.currentStep = 4;\n            this.orderPlaced = true;\n            this.snackBar.open('Payment successful! Order confirmed.', 'Close', {\n              duration: 3000\n            });\n          } else {\n            this.snackBar.open('Payment verification failed', 'Close', {\n              duration: 3000\n            });\n          }\n          this.processing = false;\n        },\n        error: error => {\n          console.error('Payment verification error:', error);\n          this.snackBar.open('Payment verification failed', 'Close', {\n            duration: 3000\n          });\n          this.processing = false;\n        }\n      });\n    }\n    goToShopping() {\n      this.router.navigate(['/products']);\n    }\n    viewOrder() {\n      this.router.navigate(['/account/orders']);\n    }\n    continueShopping() {\n      this.router.navigate(['/']);\n    }\n    getImageUrl(image) {\n      if (typeof image === 'string') {\n        return image;\n      }\n      return image?.url || '';\n    }\n    getTaxAmount() {\n      return this.cartSummary ? this.cartSummary.subtotal * 0.18 : 0;\n    }\n    static {\n      this.ɵfac = function CheckoutComponent_Factory(t) {\n        return new (t || CheckoutComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.CartService), i0.ɵɵdirectiveInject(i3.PaymentService), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i5.MatSnackBar), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.HttpClient));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CheckoutComponent,\n        selectors: [[\"app-checkout\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 63,\n        vars: 35,\n        consts: [[1, \"checkout-container\"], [1, \"checkout-header\"], [1, \"step-indicator\"], [1, \"step\"], [1, \"step-number\"], [1, \"step-label\"], [1, \"checkout-content\"], [\"class\", \"checkout-step\", 4, \"ngIf\"], [1, \"order-summary\"], [\"class\", \"cart-total-highlight\", 4, \"ngIf\"], [1, \"summary-line\"], [\"class\", \"summary-line\", 4, \"ngIf\"], [1, \"summary-line\", \"total\"], [1, \"checkout-actions\"], [\"class\", \"btn btn-secondary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-primary\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"checkout-step\"], [\"class\", \"cart-items\", 4, \"ngIf\"], [\"class\", \"empty-cart\", 4, \"ngIf\"], [1, \"cart-items\"], [\"class\", \"cart-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"cart-item\"], [1, \"item-image\", 3, \"src\", \"alt\"], [1, \"item-details\"], [1, \"item-brand\"], [\"class\", \"item-variants\", 4, \"ngIf\"], [1, \"item-price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"item-quantity\"], [1, \"item-total\"], [1, \"item-variants\"], [4, \"ngIf\"], [1, \"original-price\"], [1, \"empty-cart\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"shipping-form\", 3, \"formGroup\"], [1, \"form-row\"], [1, \"form-group\"], [\"for\", \"fullName\"], [\"type\", \"text\", \"id\", \"fullName\", \"formControlName\", \"fullName\", 1, \"form-control\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"for\", \"phone\"], [\"type\", \"tel\", \"id\", \"phone\", \"formControlName\", \"phone\", 1, \"form-control\"], [\"for\", \"addressLine1\"], [\"type\", \"text\", \"id\", \"addressLine1\", \"formControlName\", \"addressLine1\", 1, \"form-control\"], [\"for\", \"addressLine2\"], [\"type\", \"text\", \"id\", \"addressLine2\", \"formControlName\", \"addressLine2\", 1, \"form-control\"], [\"for\", \"city\"], [\"type\", \"text\", \"id\", \"city\", \"formControlName\", \"city\", 1, \"form-control\"], [\"for\", \"state\"], [\"id\", \"state\", \"formControlName\", \"state\", 1, \"form-control\"], [\"value\", \"\"], [\"value\", \"Maharashtra\"], [\"value\", \"Delhi\"], [\"value\", \"Karnataka\"], [\"value\", \"Tamil Nadu\"], [\"value\", \"Gujarat\"], [\"value\", \"Rajasthan\"], [\"value\", \"West Bengal\"], [\"value\", \"Uttar Pradesh\"], [\"for\", \"pincode\"], [\"type\", \"text\", \"id\", \"pincode\", \"formControlName\", \"pincode\", \"maxlength\", \"6\", 1, \"form-control\"], [1, \"error-message\"], [1, \"payment-methods\"], [\"class\", \"payment-option\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"payment-option\", 3, \"click\"], [1, \"method-details\"], [1, \"method-radio\"], [\"type\", \"radio\", 3, \"ngModelChange\", \"value\", \"ngModel\"], [\"class\", \"order-confirmation\", 4, \"ngIf\"], [\"class\", \"order-success\", 4, \"ngIf\"], [1, \"order-confirmation\"], [1, \"confirmation-details\"], [1, \"shipping-summary\"], [1, \"address-display\"], [1, \"payment-summary\"], [1, \"order-success\"], [1, \"success-icon\"], [1, \"fas\", \"fa-check-circle\"], [1, \"order-actions\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"cart-total-highlight\"], [1, \"total-amount-display\"], [1, \"amount-details\"], [1, \"amount-label\"], [1, \"amount-value\"], [1, \"discount\"], [1, \"btn\", \"btn-secondary\", 3, \"click\", \"disabled\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"]],\n        template: function CheckoutComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n            i0.ɵɵtext(3, \"Checkout\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"div\", 2)(5, \"div\", 3)(6, \"span\", 4);\n            i0.ɵɵtext(7, \"1\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"span\", 5);\n            i0.ɵɵtext(9, \"Cart Review\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 3)(11, \"span\", 4);\n            i0.ɵɵtext(12, \"2\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"span\", 5);\n            i0.ɵɵtext(14, \"Shipping\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"div\", 3)(16, \"span\", 4);\n            i0.ɵɵtext(17, \"3\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"span\", 5);\n            i0.ɵɵtext(19, \"Payment\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"div\", 3)(21, \"span\", 4);\n            i0.ɵɵtext(22, \"4\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"span\", 5);\n            i0.ɵɵtext(24, \"Confirmation\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(25, \"div\", 6);\n            i0.ɵɵtemplate(26, CheckoutComponent_div_26_Template, 5, 2, \"div\", 7)(27, CheckoutComponent_div_27_Template, 58, 7, \"div\", 7)(28, CheckoutComponent_div_28_Template, 5, 1, \"div\", 7)(29, CheckoutComponent_div_29_Template, 3, 2, \"div\", 7);\n            i0.ɵɵelementStart(30, \"div\", 8)(31, \"h3\");\n            i0.ɵɵtext(32, \"Order Summary\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(33, CheckoutComponent_div_33_Template, 9, 4, \"div\", 9);\n            i0.ɵɵelementStart(34, \"div\", 10)(35, \"span\");\n            i0.ɵɵtext(36);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"span\");\n            i0.ɵɵtext(38);\n            i0.ɵɵpipe(39, \"number\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(40, CheckoutComponent_div_40_Template, 6, 4, \"div\", 11);\n            i0.ɵɵelementStart(41, \"div\", 10)(42, \"span\");\n            i0.ɵɵtext(43, \"Tax (18% GST)\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"span\");\n            i0.ɵɵtext(45);\n            i0.ɵɵpipe(46, \"number\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(47, \"div\", 10)(48, \"span\");\n            i0.ɵɵtext(49, \"Shipping\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(50, \"span\");\n            i0.ɵɵtext(51, \"FREE\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(52, \"div\", 12)(53, \"span\")(54, \"strong\");\n            i0.ɵɵtext(55, \"Final Total\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(56, \"span\")(57, \"strong\");\n            i0.ɵɵtext(58);\n            i0.ɵɵpipe(59, \"number\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(60, \"div\", 13);\n            i0.ɵɵtemplate(61, CheckoutComponent_button_61_Template, 2, 1, \"button\", 14)(62, CheckoutComponent_button_62_Template, 3, 3, \"button\", 15);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵclassProp(\"active\", ctx.currentStep >= 1)(\"completed\", ctx.currentStep > 1);\n            i0.ɵɵadvance(5);\n            i0.ɵɵclassProp(\"active\", ctx.currentStep >= 2)(\"completed\", ctx.currentStep > 2);\n            i0.ɵɵadvance(5);\n            i0.ɵɵclassProp(\"active\", ctx.currentStep >= 3)(\"completed\", ctx.currentStep > 3);\n            i0.ɵɵadvance(5);\n            i0.ɵɵclassProp(\"active\", ctx.currentStep >= 4);\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 3);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 4);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.cartSummary);\n            i0.ɵɵadvance(3);\n            i0.ɵɵtextInterpolate1(\"Subtotal (\", ctx.cartSummary == null ? null : ctx.cartSummary.itemCount, \" items)\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(39, 26, ctx.cartSummary == null ? null : ctx.cartSummary.subtotal, \"1.0-0\"), \"\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ctx.cartSummary && ctx.cartSummary.discount > 0);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(46, 29, ctx.getTaxAmount(), \"1.0-0\"), \"\");\n            i0.ɵɵadvance(13);\n            i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(59, 32, ctx.cartSummary == null ? null : ctx.cartSummary.total, \"1.0-0\"), \"\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1 && ctx.currentStep < 4);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.currentStep < 4);\n          }\n        },\n        dependencies: [CommonModule, i8.NgForOf, i8.NgIf, i8.DecimalPipe, ReactiveFormsModule, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.RadioControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName, FormsModule, i1.NgModel],\n        styles: [\".checkout-container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:20px}.checkout-header[_ngcontent-%COMP%]{margin-bottom:30px}.checkout-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;color:#2d3436;margin-bottom:20px}.step-indicator[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-bottom:30px}.step[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;flex:1;position:relative}.step[_ngcontent-%COMP%]:not(:last-child):after{content:\\\"\\\";position:absolute;top:20px;right:-50%;width:100%;height:2px;background:#ddd;z-index:1}.step.completed[_ngcontent-%COMP%]:not(:last-child):after{background:#00b894}.step-number[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background:#ddd;color:#636e72;display:flex;align-items:center;justify-content:center;font-weight:600;margin-bottom:8px;position:relative;z-index:2}.step.active[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%]{background:#0984e3;color:#fff}.step.completed[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%]{background:#00b894;color:#fff}.step-label[_ngcontent-%COMP%]{font-size:14px;color:#636e72;text-align:center}.checkout-content[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 350px;gap:30px}.checkout-step[_ngcontent-%COMP%]{background:#fff;padding:30px;border-radius:12px;box-shadow:0 4px 20px #0000001a}.checkout-step[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:600;color:#2d3436;margin-bottom:20px}.cart-items[_ngcontent-%COMP%]{space-y:16px}.cart-item[_ngcontent-%COMP%]{display:flex;gap:16px;padding:16px;border:1px solid #e9ecef;border-radius:8px;margin-bottom:16px}.item-image[_ngcontent-%COMP%]{width:80px;height:80px;object-fit:cover;border-radius:8px}.item-details[_ngcontent-%COMP%]{flex:1}.item-details[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:#2d3436;margin-bottom:4px}.item-brand[_ngcontent-%COMP%]{color:#636e72;font-size:14px;margin-bottom:8px}.item-variants[_ngcontent-%COMP%]{display:flex;gap:12px;font-size:12px;color:#636e72;margin-bottom:8px}.item-price[_ngcontent-%COMP%]{display:flex;gap:8px;align-items:center}.current-price[_ngcontent-%COMP%]{font-weight:600;color:#2d3436}.original-price[_ngcontent-%COMP%]{text-decoration:line-through;color:#636e72;font-size:14px}.item-quantity[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-end;gap:8px}.item-total[_ngcontent-%COMP%]{font-weight:600;color:#2d3436}.shipping-form[_ngcontent-%COMP%]{space-y:20px}.form-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:20px}.form-group[_ngcontent-%COMP%]{margin-bottom:20px}.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;font-weight:600;color:#2d3436;margin-bottom:8px}.form-control[_ngcontent-%COMP%]{width:100%;padding:12px;border:1px solid #ddd;border-radius:8px;font-size:14px;transition:border-color .3s ease}.form-control[_ngcontent-%COMP%]:focus{outline:none;border-color:#0984e3;box-shadow:0 0 0 3px #0984e31a}.error-message[_ngcontent-%COMP%]{color:#e74c3c;font-size:12px;margin-top:4px}.payment-methods[_ngcontent-%COMP%]{space-y:12px}.payment-option[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;padding:16px;border:2px solid #e9ecef;border-radius:8px;cursor:pointer;transition:all .3s ease;margin-bottom:12px}.payment-option[_ngcontent-%COMP%]:hover{border-color:#0984e3}.payment-option.selected[_ngcontent-%COMP%]{border-color:#0984e3;background:#0984e30d}.payment-option[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:24px;color:#0984e3}.method-details[_ngcontent-%COMP%]{flex:1}.method-details[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:#2d3436;margin-bottom:4px}.method-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#636e72;font-size:14px}.order-summary[_ngcontent-%COMP%]{background:#fff;padding:24px;border-radius:12px;box-shadow:0 4px 20px #0000001a;height:-moz-fit-content;height:fit-content;position:sticky;top:20px}.order-summary[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:#2d3436;margin-bottom:20px}.cart-total-highlight[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4834d4,#686de0);border-radius:12px;padding:16px;margin-bottom:20px;color:#fff}.total-amount-display[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.total-amount-display[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:24px;color:#fff}.amount-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;flex:1}.amount-label[_ngcontent-%COMP%]{font-size:14px;font-weight:500;opacity:.9;margin-bottom:4px}.amount-value[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#fff}.summary-line[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-bottom:12px;font-size:14px}.summary-line.total[_ngcontent-%COMP%]{border-top:1px solid #e9ecef;padding-top:12px;margin-top:16px;font-size:16px}.discount[_ngcontent-%COMP%]{color:#00b894}.checkout-actions[_ngcontent-%COMP%]{margin-top:24px;display:flex;gap:12px}.btn[_ngcontent-%COMP%]{padding:12px 24px;border:none;border-radius:8px;font-weight:600;cursor:pointer;transition:all .3s ease;flex:1}.btn-primary[_ngcontent-%COMP%]{background:#0984e3;color:#fff}.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled){background:#0770c2}.btn-secondary[_ngcontent-%COMP%]{background:#636e72;color:#fff}.btn-secondary[_ngcontent-%COMP%]:hover:not(:disabled){background:#4a5459}.btn[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed}.order-success[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.success-icon[_ngcontent-%COMP%]{font-size:4rem;color:#00b894;margin-bottom:20px}.order-success[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#00b894;margin-bottom:16px}.order-actions[_ngcontent-%COMP%]{display:flex;gap:16px;justify-content:center;margin-top:24px}.empty-cart[_ngcontent-%COMP%]{text-align:center;padding:60px 20px}.empty-cart[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:4rem;color:#ddd;margin-bottom:20px}.empty-cart[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#636e72;margin-bottom:12px}@media (max-width: 768px){.checkout-content[_ngcontent-%COMP%], .form-row[_ngcontent-%COMP%]{grid-template-columns:1fr}.step-indicator[_ngcontent-%COMP%]{flex-direction:column;gap:16px}.step[_ngcontent-%COMP%]:not(:last-child):after{display:none}}\"]\n      });\n    }\n  }\n  return CheckoutComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}