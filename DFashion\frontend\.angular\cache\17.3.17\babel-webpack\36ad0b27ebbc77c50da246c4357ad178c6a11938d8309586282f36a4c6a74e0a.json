{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashion/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/trending.service\";\nimport * as i2 from \"../../../../core/services/social-interactions.service\";\nimport * as i3 from \"../../../../core/services/cart.service\";\nimport * as i4 from \"../../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@ionic/angular\";\nimport * as i8 from \"ngx-owl-carousel-o\";\nconst _c0 = () => [1, 2, 3, 4, 5, 6, 7, 8];\nconst _c1 = () => [1, 2, 3, 4, 5];\nfunction TrendingProductsComponent_div_11_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵelement(1, \"div\", 16);\n    i0.ɵɵelementStart(2, \"div\", 17);\n    i0.ɵɵelement(3, \"div\", 18)(4, \"div\", 19)(5, \"div\", 20);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TrendingProductsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13);\n    i0.ɵɵtemplate(2, TrendingProductsComponent_div_11_div_2_Template, 6, 0, \"div\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction TrendingProductsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"ion-icon\", 22);\n    i0.ɵɵelementStart(2, \"p\", 23);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_12_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 25);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction TrendingProductsComponent_div_13_ng_container_2_ng_template_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r4 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDiscountPercentage(product_r4), \"% OFF \");\n  }\n}\nfunction TrendingProductsComponent_div_13_ng_container_2_ng_template_1_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r4 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r4.originalPrice));\n  }\n}\nfunction TrendingProductsComponent_div_13_ng_container_2_ng_template_1_ion_icon_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ion-icon\", 38);\n  }\n  if (rf & 2) {\n    const star_r5 = ctx.$implicit;\n    const product_r4 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r5 <= product_r4.rating.average);\n    i0.ɵɵproperty(\"name\", star_r5 <= product_r4.rating.average ? \"star\" : \"star-outline\");\n  }\n}\nfunction TrendingProductsComponent_div_13_ng_container_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_13_ng_container_2_ng_template_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const product_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onProductClick(product_r4));\n    });\n    i0.ɵɵelementStart(1, \"div\", 31);\n    i0.ɵɵelement(2, \"img\", 32);\n    i0.ɵɵelementStart(3, \"div\", 33);\n    i0.ɵɵelement(4, \"ion-icon\", 34);\n    i0.ɵɵtext(5, \" Trending \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, TrendingProductsComponent_div_13_ng_container_2_ng_template_1_div_6_Template, 2, 1, \"div\", 35);\n    i0.ɵɵelementStart(7, \"div\", 36)(8, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_13_ng_container_2_ng_template_1_Template_button_click_8_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const product_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onLikeProduct(product_r4, $event));\n    });\n    i0.ɵɵelement(9, \"ion-icon\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_13_ng_container_2_ng_template_1_Template_button_click_10_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const product_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onShareProduct(product_r4, $event));\n    });\n    i0.ɵɵelement(11, \"ion-icon\", 40);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 41)(13, \"div\", 42);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"h3\", 43);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 44)(18, \"span\", 45);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, TrendingProductsComponent_div_13_ng_container_2_ng_template_1_span_20_Template, 2, 1, \"span\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 47)(22, \"div\", 48);\n    i0.ɵɵtemplate(23, TrendingProductsComponent_div_13_ng_container_2_ng_template_1_ion_icon_23_Template, 1, 3, \"ion-icon\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\", 50);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 51)(27, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_13_ng_container_2_ng_template_1_Template_button_click_27_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const product_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAddToCart(product_r4, $event));\n    });\n    i0.ɵɵelement(28, \"ion-icon\", 53);\n    i0.ɵɵtext(29, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_13_ng_container_2_ng_template_1_Template_button_click_30_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const product_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAddToWishlist(product_r4, $event));\n    });\n    i0.ɵɵelement(31, \"ion-icon\", 55);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r4.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r4.images[0].alt || product_r4.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDiscountPercentage(product_r4) > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r1.isProductLiked(product_r4._id));\n    i0.ɵɵattribute(\"aria-label\", \"Like \" + product_r4.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isProductLiked(product_r4._id) ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", \"Share \" + product_r4.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(product_r4.brand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r4.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r4.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r4.originalPrice && product_r4.originalPrice > product_r4.price);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(14, _c1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r4.rating.count, \")\");\n  }\n}\nfunction TrendingProductsComponent_div_13_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TrendingProductsComponent_div_13_ng_container_2_ng_template_1_Template, 32, 15, \"ng-template\", 29);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction TrendingProductsComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"owl-carousel-o\", 27);\n    i0.ɵɵtemplate(2, TrendingProductsComponent_div_13_ng_container_2_Template, 2, 0, \"ng-container\", 28);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r1.carouselOptions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.trendingProducts)(\"ngForTrackBy\", ctx_r1.trackByProductId);\n  }\n}\nfunction TrendingProductsComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵelement(1, \"ion-icon\", 59);\n    i0.ɵɵelementStart(2, \"h3\", 60);\n    i0.ɵɵtext(3, \"No Trending Products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 61);\n    i0.ɵɵtext(5, \"Check back later for trending items\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class TrendingProductsComponent {\n  constructor(trendingService, socialService, cartService, wishlistService, router) {\n    this.trendingService = trendingService;\n    this.socialService = socialService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.router = router;\n    this.trendingProducts = [];\n    this.isLoading = true;\n    this.error = null;\n    this.likedProducts = new Set();\n    this.subscription = new Subscription();\n    // Owl Carousel Options with Auto-sliding\n    this.carouselOptions = {\n      loop: true,\n      mouseDrag: true,\n      touchDrag: true,\n      pullDrag: false,\n      dots: true,\n      navSpeed: 600,\n      navText: ['<i class=\"ion-chevron-back\"></i>', '<i class=\"ion-chevron-forward\"></i>'],\n      autoplay: true,\n      autoplayTimeout: 3000,\n      autoplayHoverPause: true,\n      autoplaySpeed: 800,\n      responsive: {\n        0: {\n          items: 1,\n          margin: 10\n        },\n        576: {\n          items: 2,\n          margin: 15\n        },\n        768: {\n          items: 3,\n          margin: 20\n        },\n        992: {\n          items: 4,\n          margin: 20\n        }\n      },\n      nav: true,\n      margin: 20\n    };\n    // Listen for window resize\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n      this.generateDots();\n      this.currentSlide = 0; // Reset to first slide on resize\n      this.updateTranslateX();\n    });\n  }\n  ngOnInit() {\n    this.loadTrendingProducts();\n    this.subscribeTrendingProducts();\n    this.subscribeLikedProducts();\n    this.updateResponsiveSettings();\n    this.generateDots();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n  subscribeTrendingProducts() {\n    this.subscription.add(this.trendingService.trendingProducts$.subscribe(products => {\n      this.trendingProducts = products;\n      this.isLoading = false;\n      this.generateDots(); // Regenerate dots when products change\n      this.currentSlide = 0; // Reset to first slide\n      this.updateTranslateX();\n    }));\n  }\n  subscribeLikedProducts() {\n    this.subscription.add(this.socialService.likedProducts$.subscribe(likedProducts => {\n      this.likedProducts = likedProducts;\n    }));\n  }\n  loadTrendingProducts() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        yield _this.trendingService.loadTrendingProducts(1, 8);\n      } catch (error) {\n        console.error('Error loading trending products:', error);\n        _this.error = 'Failed to load trending products';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onProductClick(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  onLikeProduct(product, event) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const result = yield _this2.socialService.likeProduct(product._id);\n        if (result.success) {\n          console.log(result.message);\n        } else {\n          console.error('Failed to like product:', result.message);\n        }\n      } catch (error) {\n        console.error('Error liking product:', error);\n      }\n    })();\n  }\n  onShareProduct(product, event) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        // For now, copy link to clipboard\n        const productUrl = `${window.location.origin}/product/${product._id}`;\n        yield navigator.clipboard.writeText(productUrl);\n        // Track the share\n        yield _this3.socialService.shareProduct(product._id, {\n          platform: 'copy_link',\n          message: `Check out this amazing ${product.name} from ${product.brand}!`\n        });\n        console.log('Product link copied to clipboard!');\n      } catch (error) {\n        console.error('Error sharing product:', error);\n      }\n    })();\n  }\n  onAddToCart(product, event) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        yield _this4.cartService.addToCart(product._id, 1);\n        console.log('Product added to cart!');\n      } catch (error) {\n        console.error('Error adding to cart:', error);\n      }\n    })();\n  }\n  onAddToWishlist(product, event) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        yield _this5.wishlistService.addToWishlist(product._id);\n        console.log('Product added to wishlist!');\n      } catch (error) {\n        console.error('Error adding to wishlist:', error);\n      }\n    })();\n  }\n  getDiscountPercentage(product) {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n    }\n    return 0;\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  onRetry() {\n    this.loadTrendingProducts();\n  }\n  onViewAll() {\n    this.router.navigate(['/products'], {\n      queryParams: {\n        filter: 'trending'\n      }\n    });\n  }\n  isProductLiked(productId) {\n    return this.likedProducts.has(productId);\n  }\n  trackByProductId(index, product) {\n    return product._id;\n  }\n  // Slider Methods\n  updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width < 576) {\n      this.visibleCards = 1;\n      this.cardWidth = width - 40; // Account for padding\n    } else if (width < 768) {\n      this.visibleCards = 2;\n      this.cardWidth = 280;\n    } else if (width < 992) {\n      this.visibleCards = 3;\n      this.cardWidth = 280;\n    } else {\n      this.visibleCards = 4;\n      this.cardWidth = 280;\n    }\n  }\n  generateDots() {\n    const totalDots = Math.ceil(this.trendingProducts.length / this.visibleCards);\n    this.dots = Array(totalDots).fill(0).map((_, i) => i);\n  }\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateTranslateX();\n    }\n  }\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateTranslateX();\n    }\n  }\n  goToSlide(slideIndex) {\n    this.currentSlide = slideIndex;\n    this.updateTranslateX();\n  }\n  updateTranslateX() {\n    this.translateX = -(this.currentSlide * this.visibleCards * this.cardWidth);\n  }\n  static {\n    this.ɵfac = function TrendingProductsComponent_Factory(t) {\n      return new (t || TrendingProductsComponent)(i0.ɵɵdirectiveInject(i1.TrendingService), i0.ɵɵdirectiveInject(i2.SocialInteractionsService), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService), i0.ɵɵdirectiveInject(i5.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TrendingProductsComponent,\n      selectors: [[\"app-trending-products\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 15,\n      vars: 4,\n      consts: [[1, \"trending-products-container\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"trending-up\", 1, \"title-icon\"], [1, \"section-subtitle\"], [1, \"view-all-btn\", 3, \"click\"], [\"name\", \"chevron-forward\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"products-slider-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-card\"], [1, \"loading-image\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"products-slider-container\"], [3, \"options\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"carouselSlide\", \"\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"src\", \"alt\"], [1, \"trending-badge\"], [\"name\", \"trending-up\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"action-buttons\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"name\", \"share-outline\"], [1, \"product-info\"], [1, \"product-brand\"], [1, \"product-name\"], [1, \"price-section\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"rating-section\"], [1, \"stars\"], [3, \"name\", \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [1, \"product-actions\"], [1, \"cart-btn\", 3, \"click\"], [\"name\", \"bag-add-outline\"], [1, \"wishlist-btn\", 3, \"click\"], [\"name\", \"heart-outline\"], [1, \"discount-badge\"], [1, \"original-price\"], [1, \"empty-container\"], [\"name\", \"trending-up-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"]],\n      template: function TrendingProductsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\", 3);\n          i0.ɵɵelement(4, \"ion-icon\", 4);\n          i0.ɵɵtext(5, \" Trending Now \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \"Most popular products this week\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function TrendingProductsComponent_Template_button_click_8_listener() {\n            return ctx.onViewAll();\n          });\n          i0.ɵɵtext(9, \" View All \");\n          i0.ɵɵelement(10, \"ion-icon\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(11, TrendingProductsComponent_div_11_Template, 3, 2, \"div\", 8)(12, TrendingProductsComponent_div_12_Template, 7, 1, \"div\", 9)(13, TrendingProductsComponent_div_13_Template, 3, 3, \"div\", 10)(14, TrendingProductsComponent_div_14_Template, 6, 0, \"div\", 11);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.trendingProducts.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.trendingProducts.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i6.NgForOf, i6.NgIf, IonicModule, i7.IonIcon, CarouselModule, i8.CarouselComponent, i8.CarouselSlideDirective],\n      styles: [\".trending-products-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #ff6b35;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  margin: 0;\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);\\n  color: white;\\n  border: none;\\n  padding: 12px 20px;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 20px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 200px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  border-radius: 6px;\\n  margin-bottom: 8px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0% {\\n    background-position: 200% 0;\\n  }\\n  100% {\\n    background-position: -200% 0;\\n  }\\n}\\n.error-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #dc3545;\\n  margin-bottom: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #666;\\n  margin-bottom: 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  color: white;\\n  border: none;\\n  padding: 12px 24px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 auto;\\n  transition: background 0.3s ease;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  background: #0056b3;\\n}\\n\\n.products-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  z-index: 10;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  border: none;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: rgba(0, 0, 0, 0.9);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .prev-btn[_ngcontent-%COMP%] {\\n  left: -20px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .next-btn[_ngcontent-%COMP%] {\\n  right: -20px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-wrapper[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  margin: 0 20px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-wrapper[_ngcontent-%COMP%]   .slider-track[_ngcontent-%COMP%] {\\n  display: flex;\\n  transition: transform 0.3s ease-in-out;\\n  gap: 20px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-dots[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 8px;\\n  margin-top: 20px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-dots[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  border: none;\\n  background: #ddd;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-dots[_ngcontent-%COMP%]   .dot.active[_ngcontent-%COMP%] {\\n  background: #ff6b35;\\n  transform: scale(1.2);\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-dots[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:hover {\\n  background: #ff6b35;\\n}\\n\\n.product-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  flex: 0 0 280px;\\n  width: 280px;\\n}\\n.product-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n}\\n.product-card[_ngcontent-%COMP%]:hover   .action-buttons[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n\\n.product-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 200px;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  left: 12px;\\n  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);\\n  color: white;\\n  padding: 6px 12px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  background: #dc3545;\\n  color: white;\\n  padding: 6px 10px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 700;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  right: 12px;\\n  transform: translateY(-50%);\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  opacity: 0;\\n  transition: all 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: #333;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  transform: scale(1.1);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%] {\\n  background: rgba(220, 53, 69, 0.1);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.product-info[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  text-transform: uppercase;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 4px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1a1a1a;\\n  margin: 0 0 12px 0;\\n  line-height: 1.4;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 12px;\\n}\\n.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: #ff6b35;\\n}\\n.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #999;\\n  text-decoration: line-through;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #ddd;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon.filled[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);\\n  color: white;\\n  border: none;\\n  padding: 12px 16px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%] {\\n  width: 44px;\\n  height: 44px;\\n  border: 2px solid #e9ecef;\\n  background: white;\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: #666;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover {\\n  border-color: #ff6b35;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n}\\n\\n.empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: #ccc;\\n  margin-bottom: 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 8px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n}\\n\\n@media (max-width: 768px) {\\n  .trending-products-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .section-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 16px;\\n  }\\n  .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n    align-self: flex-end;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .slider-wrapper[_ngcontent-%COMP%] {\\n    margin: 0 10px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%] {\\n    left: -15px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%] {\\n    right: -15px;\\n  }\\n  .product-card[_ngcontent-%COMP%] {\\n    flex: 0 0 250px;\\n    width: 250px;\\n  }\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n}\\n@media (max-width: 575.98px) {\\n  .products-slider-container[_ngcontent-%COMP%]   .slider-wrapper[_ngcontent-%COMP%] {\\n    margin: 0 5px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%] {\\n    width: 30px;\\n    height: 30px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%] {\\n    left: -10px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%] {\\n    right: -10px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .slider-track[_ngcontent-%COMP%] {\\n    gap: 10px;\\n  }\\n  .product-card[_ngcontent-%COMP%] {\\n    flex: 0 0 calc(100vw - 60px);\\n    width: calc(100vw - 60px);\\n    max-width: 300px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subscription", "IonicModule", "CarouselModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "TrendingProductsComponent_div_11_div_2_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵtext", "ɵɵlistener", "TrendingProductsComponent_div_12_Template_button_click_4_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onRetry", "ɵɵtextInterpolate", "error", "ɵɵtextInterpolate1", "getDiscountPercentage", "product_r4", "formatPrice", "originalPrice", "ɵɵclassProp", "star_r5", "rating", "average", "TrendingProductsComponent_div_13_ng_container_2_ng_template_1_Template_div_click_0_listener", "_r3", "$implicit", "onProductClick", "TrendingProductsComponent_div_13_ng_container_2_ng_template_1_div_6_Template", "TrendingProductsComponent_div_13_ng_container_2_ng_template_1_Template_button_click_8_listener", "$event", "onLikeProduct", "TrendingProductsComponent_div_13_ng_container_2_ng_template_1_Template_button_click_10_listener", "onShareProduct", "TrendingProductsComponent_div_13_ng_container_2_ng_template_1_span_20_Template", "TrendingProductsComponent_div_13_ng_container_2_ng_template_1_ion_icon_23_Template", "TrendingProductsComponent_div_13_ng_container_2_ng_template_1_Template_button_click_27_listener", "onAddToCart", "TrendingProductsComponent_div_13_ng_container_2_ng_template_1_Template_button_click_30_listener", "onAddToWishlist", "images", "url", "ɵɵsanitizeUrl", "alt", "name", "isProductLiked", "_id", "brand", "price", "_c1", "count", "ɵɵelementContainerStart", "TrendingProductsComponent_div_13_ng_container_2_ng_template_1_Template", "TrendingProductsComponent_div_13_ng_container_2_Template", "carouselOptions", "trendingProducts", "trackByProductId", "TrendingProductsComponent", "constructor", "trendingService", "socialService", "cartService", "wishlistService", "router", "isLoading", "likedProducts", "Set", "subscription", "loop", "mouseDrag", "touchDrag", "pullDrag", "dots", "navSpeed", "navText", "autoplay", "autoplayTimeout", "autoplayHoverPause", "autoplaySpeed", "responsive", "items", "margin", "nav", "window", "addEventListener", "updateResponsiveSettings", "generateDots", "currentSlide", "updateTranslateX", "ngOnInit", "loadTrendingProducts", "subscribeTrendingProducts", "subscribeLikedProducts", "ngOnDestroy", "unsubscribe", "add", "trendingProducts$", "subscribe", "products", "likedProducts$", "_this", "_asyncToGenerator", "console", "product", "navigate", "event", "_this2", "stopPropagation", "result", "likeProduct", "success", "log", "message", "_this3", "productUrl", "location", "origin", "navigator", "clipboard", "writeText", "shareProduct", "platform", "_this4", "addToCart", "_this5", "addToWishlist", "Math", "round", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "onViewAll", "queryParams", "filter", "productId", "has", "index", "width", "innerWidth", "visibleCards", "<PERSON><PERSON><PERSON><PERSON>", "totalDots", "ceil", "length", "Array", "fill", "map", "_", "i", "slidePrev", "slideNext", "maxSlide", "goToSlide", "slideIndex", "translateX", "ɵɵdirectiveInject", "i1", "TrendingService", "i2", "SocialInteractionsService", "i3", "CartService", "i4", "WishlistService", "i5", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TrendingProductsComponent_Template", "rf", "ctx", "TrendingProductsComponent_Template_button_click_8_listener", "TrendingProductsComponent_div_11_Template", "TrendingProductsComponent_div_12_Template", "TrendingProductsComponent_div_13_Template", "TrendingProductsComponent_div_14_Template", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i7", "IonIcon", "i8", "CarouselComponent", "CarouselSlideDirective", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\trending-products\\trending-products.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\trending-products\\trending-products.component.html"], "sourcesContent": ["import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { TrendingService } from '../../../../core/services/trending.service';\nimport { Product } from '../../../../core/models/product.model';\nimport { SocialInteractionsService } from '../../../../core/services/social-interactions.service';\nimport { CartService } from '../../../../core/services/cart.service';\nimport { WishlistService } from '../../../../core/services/wishlist.service';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule, OwlOptions } from 'ngx-owl-carousel-o';\n\n@Component({\n  selector: 'app-trending-products',\n  standalone: true,\n  imports: [CommonModule, IonicModule, CarouselModule],\n  templateUrl: './trending-products.component.html',\n  styleUrls: ['./trending-products.component.scss']\n})\nexport class TrendingProductsComponent implements OnInit, OnDestroy {\n  trendingProducts: Product[] = [];\n  isLoading = true;\n  error: string | null = null;\n  likedProducts = new Set<string>();\n  private subscription: Subscription = new Subscription();\n\n  // Owl Carousel Options with Auto-sliding\n  carouselOptions: OwlOptions = {\n    loop: true,\n    mouseDrag: true,\n    touchDrag: true,\n    pullDrag: false,\n    dots: true,\n    navSpeed: 600,\n    navText: [\n      '<i class=\"ion-chevron-back\"></i>',\n      '<i class=\"ion-chevron-forward\"></i>'\n    ],\n    autoplay: true,\n    autoplayTimeout: 3000,\n    autoplayHoverPause: true,\n    autoplaySpeed: 800,\n    responsive: {\n      0: {\n        items: 1,\n        margin: 10\n      },\n      576: {\n        items: 2,\n        margin: 15\n      },\n      768: {\n        items: 3,\n        margin: 20\n      },\n      992: {\n        items: 4,\n        margin: 20\n      }\n    },\n    nav: true,\n    margin: 20\n  };\n\n  constructor(\n    private trendingService: TrendingService,\n    private socialService: SocialInteractionsService,\n    private cartService: CartService,\n    private wishlistService: WishlistService,\n    private router: Router\n  ) {\n    // Listen for window resize\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n      this.generateDots();\n      this.currentSlide = 0; // Reset to first slide on resize\n      this.updateTranslateX();\n    });\n  }\n\n  ngOnInit() {\n    this.loadTrendingProducts();\n    this.subscribeTrendingProducts();\n    this.subscribeLikedProducts();\n    this.updateResponsiveSettings();\n    this.generateDots();\n  }\n\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n  }\n\n  private subscribeTrendingProducts() {\n    this.subscription.add(\n      this.trendingService.trendingProducts$.subscribe(products => {\n        this.trendingProducts = products;\n        this.isLoading = false;\n        this.generateDots(); // Regenerate dots when products change\n        this.currentSlide = 0; // Reset to first slide\n        this.updateTranslateX();\n      })\n    );\n  }\n\n  private subscribeLikedProducts() {\n    this.subscription.add(\n      this.socialService.likedProducts$.subscribe(likedProducts => {\n        this.likedProducts = likedProducts;\n      })\n    );\n  }\n\n  private async loadTrendingProducts() {\n    try {\n      this.isLoading = true;\n      this.error = null;\n      await this.trendingService.loadTrendingProducts(1, 8);\n    } catch (error) {\n      console.error('Error loading trending products:', error);\n      this.error = 'Failed to load trending products';\n      this.isLoading = false;\n    }\n  }\n\n  onProductClick(product: Product) {\n    this.router.navigate(['/product', product._id]);\n  }\n\n  async onLikeProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      const result = await this.socialService.likeProduct(product._id);\n      if (result.success) {\n        console.log(result.message);\n      } else {\n        console.error('Failed to like product:', result.message);\n      }\n    } catch (error) {\n      console.error('Error liking product:', error);\n    }\n  }\n\n  async onShareProduct(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      // For now, copy link to clipboard\n      const productUrl = `${window.location.origin}/product/${product._id}`;\n      await navigator.clipboard.writeText(productUrl);\n\n      // Track the share\n      await this.socialService.shareProduct(product._id, {\n        platform: 'copy_link',\n        message: `Check out this amazing ${product.name} from ${product.brand}!`\n      });\n\n      console.log('Product link copied to clipboard!');\n    } catch (error) {\n      console.error('Error sharing product:', error);\n    }\n  }\n\n  async onAddToCart(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      await this.cartService.addToCart(product._id, 1);\n      console.log('Product added to cart!');\n    } catch (error) {\n      console.error('Error adding to cart:', error);\n    }\n  }\n\n  async onAddToWishlist(product: Product, event: Event) {\n    event.stopPropagation();\n    try {\n      await this.wishlistService.addToWishlist(product._id);\n      console.log('Product added to wishlist!');\n    } catch (error) {\n      console.error('Error adding to wishlist:', error);\n    }\n  }\n\n  getDiscountPercentage(product: Product): number {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);\n    }\n    return 0;\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n\n  onRetry() {\n    this.loadTrendingProducts();\n  }\n\n  onViewAll() {\n    this.router.navigate(['/products'], {\n      queryParams: { filter: 'trending' }\n    });\n  }\n\n  isProductLiked(productId: string): boolean {\n    return this.likedProducts.has(productId);\n  }\n\n  trackByProductId(index: number, product: Product): string {\n    return product._id;\n  }\n\n  // Slider Methods\n  updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width < 576) {\n      this.visibleCards = 1;\n      this.cardWidth = width - 40; // Account for padding\n    } else if (width < 768) {\n      this.visibleCards = 2;\n      this.cardWidth = 280;\n    } else if (width < 992) {\n      this.visibleCards = 3;\n      this.cardWidth = 280;\n    } else {\n      this.visibleCards = 4;\n      this.cardWidth = 280;\n    }\n  }\n\n  generateDots() {\n    const totalDots = Math.ceil(this.trendingProducts.length / this.visibleCards);\n    this.dots = Array(totalDots).fill(0).map((_, i) => i);\n  }\n\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateTranslateX();\n    }\n  }\n\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateTranslateX();\n    }\n  }\n\n  goToSlide(slideIndex: number) {\n    this.currentSlide = slideIndex;\n    this.updateTranslateX();\n  }\n\n  private updateTranslateX() {\n    this.translateX = -(this.currentSlide * this.visibleCards * this.cardWidth);\n  }\n}\n", "<div class=\"trending-products-container\">\n  <!-- Header -->\n  <div class=\"section-header\">\n    <div class=\"header-content\">\n      <h2 class=\"section-title\">\n        <ion-icon name=\"trending-up\" class=\"title-icon\"></ion-icon>\n        Trending Now\n      </h2>\n      <p class=\"section-subtitle\">Most popular products this week</p>\n    </div>\n    <button class=\"view-all-btn\" (click)=\"onViewAll()\">\n      View All\n      <ion-icon name=\"chevron-forward\"></ion-icon>\n    </button>\n  </div>\n\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <div class=\"loading-grid\">\n      <div *ngFor=\"let item of [1,2,3,4,5,6,7,8]\" class=\"loading-card\">\n        <div class=\"loading-image\"></div>\n        <div class=\"loading-content\">\n          <div class=\"loading-line short\"></div>\n          <div class=\"loading-line medium\"></div>\n          <div class=\"loading-line long\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error && !isLoading\" class=\"error-container\">\n    <ion-icon name=\"alert-circle\" class=\"error-icon\"></ion-icon>\n    <p class=\"error-message\">{{ error }}</p>\n    <button class=\"retry-btn\" (click)=\"onRetry()\">\n      <ion-icon name=\"refresh\"></ion-icon>\n      Try Again\n    </button>\n  </div>\n\n  <!-- Products Slider -->\n  <div *ngIf=\"!isLoading && !error && trendingProducts.length > 0\" class=\"products-slider-container\">\n    <owl-carousel-o [options]=\"carouselOptions\">\n      <ng-container *ngFor=\"let product of trendingProducts; trackBy: trackByProductId\">\n        <ng-template carouselSlide>\n          <div class=\"product-card\" (click)=\"onProductClick(product)\">\n\n      <div class=\"product-image-container\">\n        <img \n          [src]=\"product.images[0].url\"\n          [alt]=\"product.images[0].alt || product.name\"\n          class=\"product-image\"\n          loading=\"lazy\"\n        />\n        \n      \n        <div class=\"trending-badge\">\n          <ion-icon name=\"trending-up\"></ion-icon>\n          Trending\n        </div>\n\n      \n        <div *ngIf=\"getDiscountPercentage(product) > 0\" class=\"discount-badge\">\n          {{ getDiscountPercentage(product) }}% OFF\n        </div>\n\n       \n        <div class=\"action-buttons\">\n          <button\n            class=\"action-btn like-btn\"\n            [class.liked]=\"isProductLiked(product._id)\"\n            (click)=\"onLikeProduct(product, $event)\"\n            [attr.aria-label]=\"'Like ' + product.name\"\n          >\n            <ion-icon [name]=\"isProductLiked(product._id) ? 'heart' : 'heart-outline'\"></ion-icon>\n          </button>\n          <button \n            class=\"action-btn share-btn\" \n            (click)=\"onShareProduct(product, $event)\"\n            [attr.aria-label]=\"'Share ' + product.name\"\n          >\n            <ion-icon name=\"share-outline\"></ion-icon>\n          </button>\n        </div>\n      </div>\n\n    \n      <div class=\"product-info\">\n        <div class=\"product-brand\">{{ product.brand }}</div>\n        <h3 class=\"product-name\">{{ product.name }}</h3>\n        \n      \n        <div class=\"price-section\">\n          <span class=\"current-price\">{{ formatPrice(product.price) }}</span>\n          <span *ngIf=\"product.originalPrice && product.originalPrice > product.price\" \n                class=\"original-price\">{{ formatPrice(product.originalPrice) }}</span>\n        </div>\n\n       \n        <div class=\"rating-section\">\n          <div class=\"stars\">\n            <ion-icon \n              *ngFor=\"let star of [1,2,3,4,5]\" \n              [name]=\"star <= product.rating.average ? 'star' : 'star-outline'\"\n              [class.filled]=\"star <= product.rating.average\"\n            ></ion-icon>\n          </div>\n          <span class=\"rating-text\">({{ product.rating.count }})</span>\n        </div>\n\n        <!-- Action Buttons -->\n        <div class=\"product-actions\">\n          <button \n            class=\"cart-btn\" \n            (click)=\"onAddToCart(product, $event)\"\n          >\n            <ion-icon name=\"bag-add-outline\"></ion-icon>\n            Add to Cart\n          </button>\n          <button \n            class=\"wishlist-btn\" \n            (click)=\"onAddToWishlist(product, $event)\"\n          >\n            <ion-icon name=\"heart-outline\"></ion-icon>\n          </button>\n        </div>\n      </div>\n          </div>\n        </ng-template>\n      </ng-container>\n    </owl-carousel-o>\n  </div>\n\n  <!-- Empty State -->\n  <div *ngIf=\"!isLoading && !error && trendingProducts.length === 0\" class=\"empty-container\">\n    <ion-icon name=\"trending-up-outline\" class=\"empty-icon\"></ion-icon>\n    <h3 class=\"empty-title\">No Trending Products</h3>\n    <p class=\"empty-message\">Check back later for trending items</p>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AAMnC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAoB,oBAAoB;;;;;;;;;;;;;;ICSzDC,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAE,SAAA,cAAiC;IACjCF,EAAA,CAAAC,cAAA,cAA6B;IAG3BD,EAFA,CAAAE,SAAA,cAAsC,cACC,cACF;IAEzCF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IARRH,EADF,CAAAC,cAAA,cAAiD,cACrB;IACxBD,EAAA,CAAAI,UAAA,IAAAC,+CAAA,kBAAiE;IASrEL,EADE,CAAAG,YAAA,EAAM,EACF;;;IAToBH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAoB;;;;;;IAY9CT,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,SAAA,mBAA4D;IAC5DF,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAU,MAAA,GAAW;IAAAV,EAAA,CAAAG,YAAA,EAAI;IACxCH,EAAA,CAAAC,cAAA,iBAA8C;IAApBD,EAAA,CAAAW,UAAA,mBAAAC,kEAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAG,OAAA,EAAS;IAAA,EAAC;IAC3ClB,EAAA,CAAAE,SAAA,mBAAoC;IACpCF,EAAA,CAAAU,MAAA,kBACF;IACFV,EADE,CAAAG,YAAA,EAAS,EACL;;;;IALqBH,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAmB,iBAAA,CAAAJ,MAAA,CAAAK,KAAA,CAAW;;;;;IA6BhCpB,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAG,YAAA,EAAM;;;;;IADJH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAqB,kBAAA,MAAAN,MAAA,CAAAO,qBAAA,CAAAC,UAAA,YACF;;;;;IA8BEvB,EAAA,CAAAC,cAAA,eAC6B;IAAAD,EAAA,CAAAU,MAAA,GAAwC;IAAAV,EAAA,CAAAG,YAAA,EAAO;;;;;IAA/CH,EAAA,CAAAM,SAAA,EAAwC;IAAxCN,EAAA,CAAAmB,iBAAA,CAAAJ,MAAA,CAAAS,WAAA,CAAAD,UAAA,CAAAE,aAAA,EAAwC;;;;;IAMnEzB,EAAA,CAAAE,SAAA,mBAIY;;;;;IADVF,EAAA,CAAA0B,WAAA,WAAAC,OAAA,IAAAJ,UAAA,CAAAK,MAAA,CAAAC,OAAA,CAA+C;IAD/C7B,EAAA,CAAAO,UAAA,SAAAoB,OAAA,IAAAJ,UAAA,CAAAK,MAAA,CAAAC,OAAA,2BAAiE;;;;;;IA1DrE7B,EAAA,CAAAC,cAAA,cAA4D;IAAlCD,EAAA,CAAAW,UAAA,mBAAAmB,4FAAA;MAAA9B,EAAA,CAAAa,aAAA,CAAAkB,GAAA;MAAA,MAAAR,UAAA,GAAAvB,EAAA,CAAAgB,aAAA,GAAAgB,SAAA;MAAA,MAAAjB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAkB,cAAA,CAAAV,UAAA,CAAuB;IAAA,EAAC;IAE/DvB,EAAA,CAAAC,cAAA,cAAqC;IACnCD,EAAA,CAAAE,SAAA,cAKE;IAGFF,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAE,SAAA,mBAAwC;IACxCF,EAAA,CAAAU,MAAA,iBACF;IAAAV,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAI,UAAA,IAAA8B,4EAAA,kBAAuE;IAMrElC,EADF,CAAAC,cAAA,cAA4B,iBAMzB;IAFCD,EAAA,CAAAW,UAAA,mBAAAwB,+FAAAC,MAAA;MAAApC,EAAA,CAAAa,aAAA,CAAAkB,GAAA;MAAA,MAAAR,UAAA,GAAAvB,EAAA,CAAAgB,aAAA,GAAAgB,SAAA;MAAA,MAAAjB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAsB,aAAA,CAAAd,UAAA,EAAAa,MAAA,CAA8B;IAAA,EAAC;IAGxCpC,EAAA,CAAAE,SAAA,mBAAsF;IACxFF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAW,UAAA,mBAAA2B,gGAAAF,MAAA;MAAApC,EAAA,CAAAa,aAAA,CAAAkB,GAAA;MAAA,MAAAR,UAAA,GAAAvB,EAAA,CAAAgB,aAAA,GAAAgB,SAAA;MAAA,MAAAjB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAwB,cAAA,CAAAhB,UAAA,EAAAa,MAAA,CAA+B;IAAA,EAAC;IAGzCpC,EAAA,CAAAE,SAAA,oBAA0C;IAGhDF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAIJH,EADF,CAAAC,cAAA,eAA0B,eACG;IAAAD,EAAA,CAAAU,MAAA,IAAmB;IAAAV,EAAA,CAAAG,YAAA,EAAM;IACpDH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAU,MAAA,IAAkB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAI9CH,EADF,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAU,MAAA,IAAgC;IAAAV,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAI,UAAA,KAAAoC,8EAAA,mBAC6B;IAC/BxC,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAA4B,eACP;IACjBD,EAAA,CAAAI,UAAA,KAAAqC,kFAAA,uBAIC;IACHzC,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAU,MAAA,IAA4B;IACxDV,EADwD,CAAAG,YAAA,EAAO,EACzD;IAIJH,EADF,CAAAC,cAAA,eAA6B,kBAI1B;IADCD,EAAA,CAAAW,UAAA,mBAAA+B,gGAAAN,MAAA;MAAApC,EAAA,CAAAa,aAAA,CAAAkB,GAAA;MAAA,MAAAR,UAAA,GAAAvB,EAAA,CAAAgB,aAAA,GAAAgB,SAAA;MAAA,MAAAjB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAA4B,WAAA,CAAApB,UAAA,EAAAa,MAAA,CAA4B;IAAA,EAAC;IAEtCpC,EAAA,CAAAE,SAAA,oBAA4C;IAC5CF,EAAA,CAAAU,MAAA,qBACF;IAAAV,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGC;IADCD,EAAA,CAAAW,UAAA,mBAAAiC,gGAAAR,MAAA;MAAApC,EAAA,CAAAa,aAAA,CAAAkB,GAAA;MAAA,MAAAR,UAAA,GAAAvB,EAAA,CAAAgB,aAAA,GAAAgB,SAAA;MAAA,MAAAjB,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAA8B,eAAA,CAAAtB,UAAA,EAAAa,MAAA,CAAgC;IAAA,EAAC;IAE1CpC,EAAA,CAAAE,SAAA,oBAA0C;IAI5CF,EAHA,CAAAG,YAAA,EAAS,EACL,EACF,EACI;;;;;IA9ENH,EAAA,CAAAM,SAAA,GAA6B;IAC7BN,EADA,CAAAO,UAAA,QAAAgB,UAAA,CAAAuB,MAAA,IAAAC,GAAA,EAAA/C,EAAA,CAAAgD,aAAA,CAA6B,QAAAzB,UAAA,CAAAuB,MAAA,IAAAG,GAAA,IAAA1B,UAAA,CAAA2B,IAAA,CACgB;IAYzClD,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAO,UAAA,SAAAQ,MAAA,CAAAO,qBAAA,CAAAC,UAAA,MAAwC;IAQ1CvB,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAA0B,WAAA,UAAAX,MAAA,CAAAoC,cAAA,CAAA5B,UAAA,CAAA6B,GAAA,EAA2C;;IAIjCpD,EAAA,CAAAM,SAAA,EAAgE;IAAhEN,EAAA,CAAAO,UAAA,SAAAQ,MAAA,CAAAoC,cAAA,CAAA5B,UAAA,CAAA6B,GAAA,8BAAgE;IAK1EpD,EAAA,CAAAM,SAAA,EAA2C;;IASpBN,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAmB,iBAAA,CAAAI,UAAA,CAAA8B,KAAA,CAAmB;IACrBrD,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAmB,iBAAA,CAAAI,UAAA,CAAA2B,IAAA,CAAkB;IAIblD,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAmB,iBAAA,CAAAJ,MAAA,CAAAS,WAAA,CAAAD,UAAA,CAAA+B,KAAA,EAAgC;IACrDtD,EAAA,CAAAM,SAAA,EAAoE;IAApEN,EAAA,CAAAO,UAAA,SAAAgB,UAAA,CAAAE,aAAA,IAAAF,UAAA,CAAAE,aAAA,GAAAF,UAAA,CAAA+B,KAAA,CAAoE;IAQtDtD,EAAA,CAAAM,SAAA,GAAc;IAAdN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,KAAA+C,GAAA,EAAc;IAKTvD,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAqB,kBAAA,MAAAE,UAAA,CAAAK,MAAA,CAAA4B,KAAA,MAA4B;;;;;IAhE1DxD,EAAA,CAAAyD,uBAAA,GAAkF;IAChFzD,EAAA,CAAAI,UAAA,IAAAsD,sEAAA,4BAA2B;;;;;;IAF/B1D,EADF,CAAAC,cAAA,cAAmG,yBACrD;IAC1CD,EAAA,CAAAI,UAAA,IAAAuD,wDAAA,2BAAkF;IAwFtF3D,EADE,CAAAG,YAAA,EAAiB,EACb;;;;IAzFYH,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAO,UAAA,YAAAQ,MAAA,CAAA6C,eAAA,CAA2B;IACP5D,EAAA,CAAAM,SAAA,EAAqB;IAAAN,EAArB,CAAAO,UAAA,YAAAQ,MAAA,CAAA8C,gBAAA,CAAqB,iBAAA9C,MAAA,CAAA+C,gBAAA,CAAyB;;;;;IA2FpF9D,EAAA,CAAAC,cAAA,cAA2F;IACzFD,EAAA,CAAAE,SAAA,mBAAmE;IACnEF,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAU,MAAA,2BAAoB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAU,MAAA,0CAAmC;IAC9DV,EAD8D,CAAAG,YAAA,EAAI,EAC5D;;;ADvHR,OAAM,MAAO4D,yBAAyB;EA6CpCC,YACUC,eAAgC,EAChCC,aAAwC,EACxCC,WAAwB,EACxBC,eAAgC,EAChCC,MAAc;IAJd,KAAAJ,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IAjDhB,KAAAR,gBAAgB,GAAc,EAAE;IAChC,KAAAS,SAAS,GAAG,IAAI;IAChB,KAAAlD,KAAK,GAAkB,IAAI;IAC3B,KAAAmD,aAAa,GAAG,IAAIC,GAAG,EAAU;IACzB,KAAAC,YAAY,GAAiB,IAAI5E,YAAY,EAAE;IAEvD;IACA,KAAA+D,eAAe,GAAe;MAC5Bc,IAAI,EAAE,IAAI;MACVC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,KAAK;MACfC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE,CACP,kCAAkC,EAClC,qCAAqC,CACtC;MACDC,QAAQ,EAAE,IAAI;MACdC,eAAe,EAAE,IAAI;MACrBC,kBAAkB,EAAE,IAAI;MACxBC,aAAa,EAAE,GAAG;MAClBC,UAAU,EAAE;QACV,CAAC,EAAE;UACDC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE;SACT;QACD,GAAG,EAAE;UACHD,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE;SACT;QACD,GAAG,EAAE;UACHD,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE;SACT;QACD,GAAG,EAAE;UACHD,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE;;OAEX;MACDC,GAAG,EAAE,IAAI;MACTD,MAAM,EAAE;KACT;IASC;IACAE,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MACrC,IAAI,CAACC,wBAAwB,EAAE;MAC/B,IAAI,CAACC,YAAY,EAAE;MACnB,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC,CAAC;MACvB,IAAI,CAACC,gBAAgB,EAAE;IACzB,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,yBAAyB,EAAE;IAChC,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACP,wBAAwB,EAAE;IAC/B,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAO,WAAWA,CAAA;IACT,IAAI,CAAC1B,YAAY,CAAC2B,WAAW,EAAE;EACjC;EAEQH,yBAAyBA,CAAA;IAC/B,IAAI,CAACxB,YAAY,CAAC4B,GAAG,CACnB,IAAI,CAACpC,eAAe,CAACqC,iBAAiB,CAACC,SAAS,CAACC,QAAQ,IAAG;MAC1D,IAAI,CAAC3C,gBAAgB,GAAG2C,QAAQ;MAChC,IAAI,CAAClC,SAAS,GAAG,KAAK;MACtB,IAAI,CAACsB,YAAY,EAAE,CAAC,CAAC;MACrB,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC,CAAC;MACvB,IAAI,CAACC,gBAAgB,EAAE;IACzB,CAAC,CAAC,CACH;EACH;EAEQI,sBAAsBA,CAAA;IAC5B,IAAI,CAACzB,YAAY,CAAC4B,GAAG,CACnB,IAAI,CAACnC,aAAa,CAACuC,cAAc,CAACF,SAAS,CAAChC,aAAa,IAAG;MAC1D,IAAI,CAACA,aAAa,GAAGA,aAAa;IACpC,CAAC,CAAC,CACH;EACH;EAEcyB,oBAAoBA,CAAA;IAAA,IAAAU,KAAA;IAAA,OAAAC,iBAAA;MAChC,IAAI;QACFD,KAAI,CAACpC,SAAS,GAAG,IAAI;QACrBoC,KAAI,CAACtF,KAAK,GAAG,IAAI;QACjB,MAAMsF,KAAI,CAACzC,eAAe,CAAC+B,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC;OACtD,CAAC,OAAO5E,KAAK,EAAE;QACdwF,OAAO,CAACxF,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxDsF,KAAI,CAACtF,KAAK,GAAG,kCAAkC;QAC/CsF,KAAI,CAACpC,SAAS,GAAG,KAAK;;IACvB;EACH;EAEArC,cAAcA,CAAC4E,OAAgB;IAC7B,IAAI,CAACxC,MAAM,CAACyC,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAACzD,GAAG,CAAC,CAAC;EACjD;EAEMf,aAAaA,CAACwE,OAAgB,EAAEE,KAAY;IAAA,IAAAC,MAAA;IAAA,OAAAL,iBAAA;MAChDI,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMC,MAAM,SAASF,MAAI,CAAC9C,aAAa,CAACiD,WAAW,CAACN,OAAO,CAACzD,GAAG,CAAC;QAChE,IAAI8D,MAAM,CAACE,OAAO,EAAE;UAClBR,OAAO,CAACS,GAAG,CAACH,MAAM,CAACI,OAAO,CAAC;SAC5B,MAAM;UACLV,OAAO,CAACxF,KAAK,CAAC,yBAAyB,EAAE8F,MAAM,CAACI,OAAO,CAAC;;OAE3D,CAAC,OAAOlG,KAAK,EAAE;QACdwF,OAAO,CAACxF,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;IAC9C;EACH;EAEMmB,cAAcA,CAACsE,OAAgB,EAAEE,KAAY;IAAA,IAAAQ,MAAA;IAAA,OAAAZ,iBAAA;MACjDI,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF;QACA,MAAMO,UAAU,GAAG,GAAG/B,MAAM,CAACgC,QAAQ,CAACC,MAAM,YAAYb,OAAO,CAACzD,GAAG,EAAE;QACrE,MAAMuE,SAAS,CAACC,SAAS,CAACC,SAAS,CAACL,UAAU,CAAC;QAE/C;QACA,MAAMD,MAAI,CAACrD,aAAa,CAAC4D,YAAY,CAACjB,OAAO,CAACzD,GAAG,EAAE;UACjD2E,QAAQ,EAAE,WAAW;UACrBT,OAAO,EAAE,0BAA0BT,OAAO,CAAC3D,IAAI,SAAS2D,OAAO,CAACxD,KAAK;SACtE,CAAC;QAEFuD,OAAO,CAACS,GAAG,CAAC,mCAAmC,CAAC;OACjD,CAAC,OAAOjG,KAAK,EAAE;QACdwF,OAAO,CAACxF,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;IAC/C;EACH;EAEMuB,WAAWA,CAACkE,OAAgB,EAAEE,KAAY;IAAA,IAAAiB,MAAA;IAAA,OAAArB,iBAAA;MAC9CI,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMe,MAAI,CAAC7D,WAAW,CAAC8D,SAAS,CAACpB,OAAO,CAACzD,GAAG,EAAE,CAAC,CAAC;QAChDwD,OAAO,CAACS,GAAG,CAAC,wBAAwB,CAAC;OACtC,CAAC,OAAOjG,KAAK,EAAE;QACdwF,OAAO,CAACxF,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;IAC9C;EACH;EAEMyB,eAAeA,CAACgE,OAAgB,EAAEE,KAAY;IAAA,IAAAmB,MAAA;IAAA,OAAAvB,iBAAA;MAClDI,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMiB,MAAI,CAAC9D,eAAe,CAAC+D,aAAa,CAACtB,OAAO,CAACzD,GAAG,CAAC;QACrDwD,OAAO,CAACS,GAAG,CAAC,4BAA4B,CAAC;OAC1C,CAAC,OAAOjG,KAAK,EAAE;QACdwF,OAAO,CAACxF,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;;IAClD;EACH;EAEAE,qBAAqBA,CAACuF,OAAgB;IACpC,IAAIA,OAAO,CAACpF,aAAa,IAAIoF,OAAO,CAACpF,aAAa,GAAGoF,OAAO,CAACvD,KAAK,EAAE;MAClE,OAAO8E,IAAI,CAACC,KAAK,CAAE,CAACxB,OAAO,CAACpF,aAAa,GAAGoF,OAAO,CAACvD,KAAK,IAAIuD,OAAO,CAACpF,aAAa,GAAI,GAAG,CAAC;;IAE5F,OAAO,CAAC;EACV;EAEAD,WAAWA,CAAC8B,KAAa;IACvB,OAAO,IAAIgF,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACrF,KAAK,CAAC;EAClB;EAEApC,OAAOA,CAAA;IACL,IAAI,CAAC8E,oBAAoB,EAAE;EAC7B;EAEA4C,SAASA,CAAA;IACP,IAAI,CAACvE,MAAM,CAACyC,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;MAClC+B,WAAW,EAAE;QAAEC,MAAM,EAAE;MAAU;KAClC,CAAC;EACJ;EAEA3F,cAAcA,CAAC4F,SAAiB;IAC9B,OAAO,IAAI,CAACxE,aAAa,CAACyE,GAAG,CAACD,SAAS,CAAC;EAC1C;EAEAjF,gBAAgBA,CAACmF,KAAa,EAAEpC,OAAgB;IAC9C,OAAOA,OAAO,CAACzD,GAAG;EACpB;EAEA;EACAuC,wBAAwBA,CAAA;IACtB,MAAMuD,KAAK,GAAGzD,MAAM,CAAC0D,UAAU;IAC/B,IAAID,KAAK,GAAG,GAAG,EAAE;MACf,IAAI,CAACE,YAAY,GAAG,CAAC;MACrB,IAAI,CAACC,SAAS,GAAGH,KAAK,GAAG,EAAE,CAAC,CAAC;KAC9B,MAAM,IAAIA,KAAK,GAAG,GAAG,EAAE;MACtB,IAAI,CAACE,YAAY,GAAG,CAAC;MACrB,IAAI,CAACC,SAAS,GAAG,GAAG;KACrB,MAAM,IAAIH,KAAK,GAAG,GAAG,EAAE;MACtB,IAAI,CAACE,YAAY,GAAG,CAAC;MACrB,IAAI,CAACC,SAAS,GAAG,GAAG;KACrB,MAAM;MACL,IAAI,CAACD,YAAY,GAAG,CAAC;MACrB,IAAI,CAACC,SAAS,GAAG,GAAG;;EAExB;EAEAzD,YAAYA,CAAA;IACV,MAAM0D,SAAS,GAAGlB,IAAI,CAACmB,IAAI,CAAC,IAAI,CAAC1F,gBAAgB,CAAC2F,MAAM,GAAG,IAAI,CAACJ,YAAY,CAAC;IAC7E,IAAI,CAACtE,IAAI,GAAG2E,KAAK,CAACH,SAAS,CAAC,CAACI,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC;EACvD;EAEAC,SAASA,CAAA;IACP,IAAI,IAAI,CAACjE,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,IAAI,CAACC,gBAAgB,EAAE;;EAE3B;EAEAiE,SAASA,CAAA;IACP,IAAI,IAAI,CAAClE,YAAY,GAAG,IAAI,CAACmE,QAAQ,EAAE;MACrC,IAAI,CAACnE,YAAY,EAAE;MACnB,IAAI,CAACC,gBAAgB,EAAE;;EAE3B;EAEAmE,SAASA,CAACC,UAAkB;IAC1B,IAAI,CAACrE,YAAY,GAAGqE,UAAU;IAC9B,IAAI,CAACpE,gBAAgB,EAAE;EACzB;EAEQA,gBAAgBA,CAAA;IACtB,IAAI,CAACqE,UAAU,GAAG,EAAE,IAAI,CAACtE,YAAY,GAAG,IAAI,CAACuD,YAAY,GAAG,IAAI,CAACC,SAAS,CAAC;EAC7E;;;uBA/OWtF,yBAAyB,EAAA/D,EAAA,CAAAoK,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAtK,EAAA,CAAAoK,iBAAA,CAAAG,EAAA,CAAAC,yBAAA,GAAAxK,EAAA,CAAAoK,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA1K,EAAA,CAAAoK,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAA5K,EAAA,CAAAoK,iBAAA,CAAAS,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAzB/G,yBAAyB;MAAAgH,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAjL,EAAA,CAAAkL,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCfhCxL,EAJN,CAAAC,cAAA,aAAyC,aAEX,aACE,YACA;UACxBD,EAAA,CAAAE,SAAA,kBAA2D;UAC3DF,EAAA,CAAAU,MAAA,qBACF;UAAAV,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAU,MAAA,sCAA+B;UAC7DV,EAD6D,CAAAG,YAAA,EAAI,EAC3D;UACNH,EAAA,CAAAC,cAAA,gBAAmD;UAAtBD,EAAA,CAAAW,UAAA,mBAAA+K,2DAAA;YAAA,OAASD,GAAA,CAAA7C,SAAA,EAAW;UAAA,EAAC;UAChD5I,EAAA,CAAAU,MAAA,iBACA;UAAAV,EAAA,CAAAE,SAAA,mBAA4C;UAEhDF,EADE,CAAAG,YAAA,EAAS,EACL;UAwHNH,EArHA,CAAAI,UAAA,KAAAuL,yCAAA,iBAAiD,KAAAC,yCAAA,iBAcQ,KAAAC,yCAAA,kBAU0C,KAAAC,yCAAA,kBA6FR;UAK7F9L,EAAA,CAAAG,YAAA,EAAM;;;UA1HEH,EAAA,CAAAM,SAAA,IAAe;UAAfN,EAAA,CAAAO,UAAA,SAAAkL,GAAA,CAAAnH,SAAA,CAAe;UAcftE,EAAA,CAAAM,SAAA,EAAyB;UAAzBN,EAAA,CAAAO,UAAA,SAAAkL,GAAA,CAAArK,KAAA,KAAAqK,GAAA,CAAAnH,SAAA,CAAyB;UAUzBtE,EAAA,CAAAM,SAAA,EAAyD;UAAzDN,EAAA,CAAAO,UAAA,UAAAkL,GAAA,CAAAnH,SAAA,KAAAmH,GAAA,CAAArK,KAAA,IAAAqK,GAAA,CAAA5H,gBAAA,CAAA2F,MAAA,KAAyD;UA6FzDxJ,EAAA,CAAAM,SAAA,EAA2D;UAA3DN,EAAA,CAAAO,UAAA,UAAAkL,GAAA,CAAAnH,SAAA,KAAAmH,GAAA,CAAArK,KAAA,IAAAqK,GAAA,CAAA5H,gBAAA,CAAA2F,MAAA,OAA2D;;;qBDvHvD5J,YAAY,EAAAmM,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEnM,WAAW,EAAAoM,EAAA,CAAAC,OAAA,EAAEpM,cAAc,EAAAqM,EAAA,CAAAC,iBAAA,EAAAD,EAAA,CAAAE,sBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}