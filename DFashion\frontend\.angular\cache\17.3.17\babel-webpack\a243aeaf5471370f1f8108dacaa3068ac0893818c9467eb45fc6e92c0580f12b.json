{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let AnalyticsService = /*#__PURE__*/(() => {\n  class AnalyticsService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = `${environment.apiUrl}/admin`;\n    }\n    // Dashboard Statistics (Admin API)\n    getDashboardStats() {\n      return this.http.get(`${this.apiUrl}/dashboard`);\n    }\n    // Sales Analytics\n    getSalesData(period = '30d') {\n      return this.http.get(`${this.apiUrl}/sales?period=${period}`);\n    }\n    getSalesStats(period = '30d') {\n      return this.http.get(`${this.apiUrl}/sales/stats?period=${period}`);\n    }\n    // User Analytics\n    getUserAnalytics(period = '30d') {\n      return this.http.get(`${this.apiUrl}/users?period=${period}`);\n    }\n    getUserGrowth(period = '12m') {\n      return this.http.get(`${this.apiUrl}/users/growth?period=${period}`);\n    }\n    getUserActivity(period = '7d') {\n      return this.http.get(`${this.apiUrl}/users/activity?period=${period}`);\n    }\n    // Product Analytics\n    getProductAnalytics(period = '30d') {\n      return this.http.get(`${this.apiUrl}/products?period=${period}`);\n    }\n    getTopSellingProducts(limit = 10, period = '30d') {\n      return this.http.get(`${this.apiUrl}/products/top-selling?limit=${limit}&period=${period}`);\n    }\n    getProductPerformance(productId, period = '30d') {\n      return this.http.get(`${this.apiUrl}/products/${productId}/performance?period=${period}`);\n    }\n    // Order Analytics\n    getOrderAnalytics(period = '30d') {\n      return this.http.get(`${this.apiUrl}/orders?period=${period}`);\n    }\n    getOrderTrends(period = '12m') {\n      return this.http.get(`${this.apiUrl}/orders/trends?period=${period}`);\n    }\n    // Revenue Analytics\n    getRevenueAnalytics(period = '30d') {\n      return this.http.get(`${this.apiUrl}/revenue?period=${period}`);\n    }\n    getRevenueByCategory(period = '30d') {\n      return this.http.get(`${this.apiUrl}/revenue/by-category?period=${period}`);\n    }\n    getRevenueByVendor(period = '30d') {\n      return this.http.get(`${this.apiUrl}/revenue/by-vendor?period=${period}`);\n    }\n    // Traffic Analytics\n    getTrafficAnalytics(period = '30d') {\n      return this.http.get(`${this.apiUrl}/traffic?period=${period}`);\n    }\n    getPageViews(period = '7d') {\n      return this.http.get(`${this.apiUrl}/traffic/page-views?period=${period}`);\n    }\n    // Conversion Analytics\n    getConversionRates(period = '30d') {\n      return this.http.get(`${this.apiUrl}/conversion?period=${period}`);\n    }\n    getFunnelAnalytics(period = '30d') {\n      return this.http.get(`${this.apiUrl}/conversion/funnel?period=${period}`);\n    }\n    // Customer Analytics\n    getCustomerAnalytics(period = '30d') {\n      return this.http.get(`${this.apiUrl}/customers?period=${period}`);\n    }\n    getCustomerLifetimeValue(period = '12m') {\n      return this.http.get(`${this.apiUrl}/customers/lifetime-value?period=${period}`);\n    }\n    getCustomerRetention(period = '12m') {\n      return this.http.get(`${this.apiUrl}/customers/retention?period=${period}`);\n    }\n    // Inventory Analytics\n    getInventoryAnalytics() {\n      return this.http.get(`${this.apiUrl}/inventory`);\n    }\n    getLowStockProducts(threshold = 10) {\n      return this.http.get(`${this.apiUrl}/inventory/low-stock?threshold=${threshold}`);\n    }\n    getStockMovement(period = '30d') {\n      return this.http.get(`${this.apiUrl}/inventory/movement?period=${period}`);\n    }\n    // Marketing Analytics\n    getMarketingAnalytics(period = '30d') {\n      return this.http.get(`${this.apiUrl}/marketing?period=${period}`);\n    }\n    getCampaignPerformance(period = '30d') {\n      return this.http.get(`${this.apiUrl}/marketing/campaigns?period=${period}`);\n    }\n    // Financial Analytics\n    getFinancialAnalytics(period = '30d') {\n      return this.http.get(`${this.apiUrl}/financial?period=${period}`);\n    }\n    getProfitAnalysis(period = '30d') {\n      return this.http.get(`${this.apiUrl}/financial/profit?period=${period}`);\n    }\n    // Export Analytics\n    exportAnalyticsReport(type, period = '30d', format = 'csv') {\n      let params = new HttpParams().set('type', type).set('period', period).set('format', format);\n      return this.http.get(`${this.apiUrl}/export`, {\n        params,\n        responseType: 'blob'\n      });\n    }\n    // Real-time Analytics\n    getRealTimeStats() {\n      return this.http.get(`${this.apiUrl}/real-time`);\n    }\n    // Custom Analytics\n    getCustomAnalytics(query) {\n      return this.http.post(`${this.apiUrl}/custom`, query);\n    }\n    // Comparative Analytics\n    getComparativeAnalytics(periods) {\n      let params = new HttpParams();\n      periods.forEach(period => {\n        params = params.append('periods', period);\n      });\n      return this.http.get(`${this.apiUrl}/comparative`, {\n        params\n      });\n    }\n    static {\n      this.ɵfac = function AnalyticsService_Factory(t) {\n        return new (t || AnalyticsService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AnalyticsService,\n        factory: AnalyticsService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AnalyticsService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}