{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\nimport { TrendingProductsComponent } from '../trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../new-arrivals/new-arrivals.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/product.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3];\nconst _c1 = a0 => [\"/category\", a0];\nfunction SidebarComponent_div_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12);\n    i0.ɵɵelement(2, \"div\", 13)(3, \"div\", 14)(4, \"div\", 15);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SidebarComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtemplate(1, SidebarComponent_div_7_div_1_Template, 5, 0, \"div\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction SidebarComponent_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵelement(1, \"img\", 18);\n    i0.ɵɵelementStart(2, \"div\", 19)(3, \"h5\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_8_div_1_Template_button_click_7_listener() {\n      const user_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.followUser(user_r2.id));\n    });\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", user_r2.avatar, i0.ɵɵsanitizeUrl)(\"alt\", user_r2.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r2.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r2.followedBy);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", user_r2.isFollowing ? \"Following\" : \"Follow\", \" \");\n  }\n}\nfunction SidebarComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, SidebarComponent_div_8_div_1_Template, 9, 5, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.suggestedUsers);\n  }\n}\nfunction SidebarComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"ion-icon\", 22);\n    i0.ɵɵelementStart(2, \"h3\", 23);\n    i0.ɵɵtext(3, \"No Suggestions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 24);\n    i0.ɵɵtext(5, \"Check back later for user suggestions\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SidebarComponent_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12);\n    i0.ɵɵelement(2, \"div\", 13)(3, \"div\", 14)(4, \"div\", 15);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SidebarComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtemplate(1, SidebarComponent_div_13_div_1_Template, 5, 0, \"div\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction SidebarComponent_div_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"img\", 18);\n    i0.ɵɵelementStart(2, \"div\", 27)(3, \"h5\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 28)(8, \"span\", 29);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 30);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_14_div_1_Template_button_click_12_listener() {\n      const influencer_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.followInfluencer(influencer_r5.id));\n    });\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const influencer_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", influencer_r5.avatar, i0.ɵɵsanitizeUrl)(\"alt\", influencer_r5.fullName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(influencer_r5.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.formatFollowerCount(influencer_r5.followersCount), \" followers\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", influencer_r5.postsCount, \" posts\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", influencer_r5.engagement, \"% engagement\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", influencer_r5.isFollowing ? \"Following\" : \"Follow\", \" \");\n  }\n}\nfunction SidebarComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, SidebarComponent_div_14_div_1_Template, 14, 7, \"div\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.topInfluencers);\n  }\n}\nfunction SidebarComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"ion-icon\", 22);\n    i0.ɵɵelementStart(2, \"h3\", 23);\n    i0.ɵɵtext(3, \"No Influencers Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 24);\n    i0.ɵɵtext(5, \"Check back later for top fashion influencers\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SidebarComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelement(1, \"img\", 18);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c1, category_r6.slug));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", category_r6.image, i0.ɵɵsanitizeUrl)(\"alt\", category_r6.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r6.name);\n  }\n}\nexport class SidebarComponent {\n  constructor(productService, router) {\n    this.productService = productService;\n    this.router = router;\n    this.suggestedUsers = [];\n    this.trendingProducts = [];\n    this.topInfluencers = [];\n    this.categories = [];\n    this.isLoadingInfluencers = false;\n    this.isLoadingSuggestions = false;\n  }\n  ngOnInit() {\n    this.loadSuggestedUsers();\n    this.loadTrendingProducts();\n    this.loadTopInfluencers();\n    this.loadCategories();\n  }\n  loadSuggestedUsers() {\n    this.isLoadingSuggestions = true;\n    // Simulate API call\n    setTimeout(() => {\n      this.suggestedUsers = [{\n        id: '1',\n        username: '@arjun_style',\n        fullName: 'Arjun Kumar',\n        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',\n        followedBy: 'Followed by maya_fashion + 12 others',\n        isFollowing: false\n      }, {\n        id: '2',\n        username: '@sneha_trends',\n        fullName: 'Sneha Reddy',\n        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100',\n        followedBy: 'Followed by raj_style + 8 others',\n        isFollowing: false\n      }, {\n        id: '3',\n        username: '@fashion_vikram',\n        fullName: 'Vikram Singh',\n        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100',\n        followedBy: 'Followed by priya_chic + 15 others',\n        isFollowing: true\n      }];\n      this.isLoadingSuggestions = false;\n    }, 1200);\n  }\n  loadTrendingProducts() {\n    // Load from API - empty for now\n    this.trendingProducts = [];\n  }\n  loadTopInfluencers() {\n    this.isLoadingInfluencers = true;\n    // Simulate API call\n    setTimeout(() => {\n      this.topInfluencers = [{\n        id: '1',\n        username: '@fashionista_maya',\n        fullName: 'Maya Sharma',\n        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=100',\n        followersCount: 125000,\n        postsCount: 342,\n        engagement: 8.5,\n        isFollowing: false\n      }, {\n        id: '2',\n        username: '@style_guru_raj',\n        fullName: 'Raj Patel',\n        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100',\n        followersCount: 89000,\n        postsCount: 198,\n        engagement: 7.2,\n        isFollowing: true\n      }, {\n        id: '3',\n        username: '@trendy_priya',\n        fullName: 'Priya Singh',\n        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100',\n        followersCount: 67000,\n        postsCount: 156,\n        engagement: 9.1,\n        isFollowing: false\n      }];\n      this.isLoadingInfluencers = false;\n    }, 1500);\n  }\n  loadCategories() {\n    this.categories = [{\n      name: 'Women',\n      slug: 'women',\n      image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=100'\n    }, {\n      name: 'Men',\n      slug: 'men',\n      image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=100'\n    }, {\n      name: 'Kids',\n      slug: 'children',\n      image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=100'\n    }, {\n      name: 'Ethnic',\n      slug: 'ethnic',\n      image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=100'\n    }];\n  }\n  formatFollowerCount(count) {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'k';\n    }\n    return count.toString();\n  }\n  followUser(userId) {\n    const user = this.suggestedUsers.find(u => u.id === userId);\n    if (user) {\n      user.isFollowing = !user.isFollowing;\n    }\n  }\n  followInfluencer(influencerId) {\n    const influencer = this.topInfluencers.find(i => i.id === influencerId);\n    if (influencer) {\n      influencer.isFollowing = !influencer.isFollowing;\n    }\n  }\n  quickBuy(productId) {\n    console.log('Quick buy product:', productId);\n    // TODO: Implement quick buy functionality\n  }\n  browseCategory(categorySlug) {\n    console.log('Browse category:', categorySlug);\n    this.router.navigate(['/category', categorySlug]);\n  }\n  static {\n    this.ɵfac = function SidebarComponent_Factory(t) {\n      return new (t || SidebarComponent)(i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SidebarComponent,\n      selectors: [[\"app-sidebar\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 21,\n      vars: 7,\n      consts: [[1, \"sidebar\"], [1, \"suggestions\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"influencers\"], [1, \"categories\"], [1, \"category-grid\"], [\"class\", \"category-item\", \"routerLinkActive\", \"active\", \"tabindex\", \"0\", 3, \"routerLink\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-container\"], [\"class\", \"loading-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-card\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [\"class\", \"suggestion-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"suggestion-item\"], [3, \"src\", \"alt\"], [1, \"suggestion-info\"], [1, \"follow-btn\", 3, \"click\"], [1, \"empty-container\"], [\"name\", \"people-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"], [\"class\", \"influencer-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"influencer-item\"], [1, \"influencer-info\"], [1, \"influencer-stats\"], [1, \"posts-count\"], [1, \"engagement\"], [\"routerLinkActive\", \"active\", \"tabindex\", \"0\", 1, \"category-item\", 3, \"routerLink\"]],\n      template: function SidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"aside\", 0);\n          i0.ɵɵelement(1, \"app-trending-products\")(2, \"app-featured-brands\")(3, \"app-new-arrivals\");\n          i0.ɵɵelementStart(4, \"div\", 1)(5, \"h3\");\n          i0.ɵɵtext(6, \"Suggested for you\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, SidebarComponent_div_7_Template, 2, 2, \"div\", 2)(8, SidebarComponent_div_8_Template, 2, 1, \"div\", 3)(9, SidebarComponent_div_9_Template, 6, 0, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 5)(11, \"h3\");\n          i0.ɵɵtext(12, \"Top Fashion Influencers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(13, SidebarComponent_div_13_Template, 2, 2, \"div\", 2)(14, SidebarComponent_div_14_Template, 2, 1, \"div\", 3)(15, SidebarComponent_div_15_Template, 6, 0, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 6)(17, \"h3\");\n          i0.ɵɵtext(18, \"Shop by Category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 7);\n          i0.ɵɵtemplate(20, SidebarComponent_div_20_Template, 4, 6, \"div\", 8);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingSuggestions);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingSuggestions && ctx.suggestedUsers.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingSuggestions && ctx.suggestedUsers.length === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingInfluencers);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingInfluencers && ctx.topInfluencers.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingInfluencers && ctx.topInfluencers.length === 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n        }\n      },\n      dependencies: [CommonModule, i3.NgForOf, i3.NgIf, RouterModule, i2.RouterLink, i2.RouterLinkActive, IonicModule, i4.IonIcon, i4.RouterLinkDelegate, TrendingProductsComponent, FeaturedBrandsComponent, NewArrivalsComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.sidebar[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 80px;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n  max-height: calc(100vh - 100px);\\n  overflow-y: auto;\\n  padding-right: 8px;\\n}\\n\\napp-view-add-stories[_ngcontent-%COMP%], app-trending-products[_ngcontent-%COMP%], app-featured-brands[_ngcontent-%COMP%], app-new-arrivals[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: block;\\n  margin-bottom: 24px;\\n}\\n\\n.suggestions[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n  box-shadow: 0 8px 32px rgba(255, 107, 107, 0.2);\\n}\\n\\n.categories[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n  box-shadow: 0 8px 32px rgba(0, 210, 211, 0.2);\\n}\\n\\n.trending[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  padding: 16px;\\n}\\n\\n.influencers[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDCA1\\\";\\n  font-size: 28px;\\n}\\n\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDECD\\uFE0F\\\";\\n  font-size: 28px;\\n}\\n\\n.trending[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin-bottom: 16px;\\n  color: #8e8e8e;\\n}\\n\\n.influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 24px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDC51\\\";\\n  font-size: 28px;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 16px;\\n  margin-bottom: 16px;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 16px;\\n  margin-bottom: 16px;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);\\n}\\n\\n.influencer-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid rgba(255, 255, 255, 0.3);\\n  transition: transform 0.3s ease;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.suggestion-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.suggestion-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 4px;\\n}\\n\\n.suggestion-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 8px 16px;\\n  border-radius: 8px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.suggestions[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n  transform: translateY(-2px);\\n}\\n\\n.influencer-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 16px;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid rgba(255, 255, 255, 0.3);\\n  transition: transform 0.3s ease;\\n}\\n\\n.influencer-item[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.influencer-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.influencer-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 4px;\\n}\\n\\n.influencer-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.7);\\n  margin-bottom: 8px;\\n}\\n\\n.influencer-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n  margin-bottom: 12px;\\n}\\n\\n.influencer-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: rgba(255, 255, 255, 0.6);\\n  background: rgba(255, 255, 255, 0.1);\\n  padding: 4px 8px;\\n  border-radius: 8px;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  -webkit-backdrop-filter: blur(5px);\\n          backdrop-filter: blur(5px);\\n}\\n\\n.follow-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 8px 16px;\\n  border-radius: 8px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  align-self: flex-start;\\n}\\n\\n.follow-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n  transform: translateY(-2px);\\n}\\n\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  padding: 16px;\\n  margin-bottom: 16px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  background: rgba(255, 255, 255, 0.2);\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  border-radius: 6px;\\n  margin-bottom: 8px;\\n}\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.influencers[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0%, 100% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n.influencers[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.influencers[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: rgba(255, 255, 255, 0.4);\\n  margin-bottom: 16px;\\n}\\n.influencers[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 8px;\\n}\\n.influencers[_ngcontent-%COMP%]   .empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n.trending-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n  padding-bottom: 16px;\\n  border-bottom: 1px solid #f1f5f9;\\n}\\n\\n.trending-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n  margin-bottom: 0;\\n  padding-bottom: 0;\\n}\\n\\n.trending-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 8px;\\n  object-fit: cover;\\n}\\n\\n.trending-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.trending-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin-bottom: 4px;\\n}\\n\\n.trending-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: var(--primary-color);\\n  margin-bottom: 8px;\\n}\\n\\n.original-price[_ngcontent-%COMP%] {\\n  text-decoration: line-through;\\n  color: #8e8e8e;\\n  font-weight: 400;\\n  margin-left: 4px;\\n}\\n\\n.trending-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n\\n.trending-badge[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-weight: 600;\\n  background: #fef3c7;\\n  color: #92400e;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n\\n.views[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #8e8e8e;\\n}\\n\\n.quick-buy-btn[_ngcontent-%COMP%] {\\n  background: #f1f5f9;\\n  color: #64748b;\\n  border: 1px solid #e2e8f0;\\n  padding: 6px 12px;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s;\\n  align-self: flex-start;\\n}\\n\\n.quick-buy-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-color);\\n  color: #fff;\\n  border-color: var(--primary-color);\\n}\\n\\n.categories[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1f2937;\\n  margin-bottom: 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDECD\\uFE0F\\\";\\n  font-size: 18px;\\n}\\n\\n.category-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 16px;\\n}\\n\\n.category-item[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  padding: 20px 16px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-align: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  text-decoration: none;\\n  color: white;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.category-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);\\n}\\n\\n.category-item.active[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  border-color: rgba(255, 255, 255, 0.4);\\n  transform: translateY(-4px);\\n}\\n\\n.category-item.active[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n\\n.category-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  margin-bottom: 12px;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  transition: transform 0.3s ease;\\n}\\n\\n.category-item[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.category-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: white;\\n  text-align: center;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.category-item[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.category-item[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n\\n@media (max-width: 1024px) {\\n  .sidebar[_ngcontent-%COMP%] {\\n    order: -1;\\n    position: static;\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n    gap: 20px;\\n    max-height: none;\\n    overflow-y: visible;\\n  }\\n  app-view-add-stories[_ngcontent-%COMP%], app-trending-products[_ngcontent-%COMP%], app-featured-brands[_ngcontent-%COMP%], app-new-arrivals[_ngcontent-%COMP%] {\\n    margin-bottom: 16px;\\n  }\\n  .influencers[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .influencer-item[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .sidebar[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  app-view-add-stories[_ngcontent-%COMP%], app-trending-products[_ngcontent-%COMP%], app-featured-brands[_ngcontent-%COMP%], app-new-arrivals[_ngcontent-%COMP%] {\\n    margin-bottom: 12px;\\n  }\\n  .influencers[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .influencer-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n    padding: 16px;\\n  }\\n  .influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n    margin-bottom: 12px;\\n  }\\n  .influencer-stats[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    justify-content: center;\\n    gap: 8px;\\n  }\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "IonicModule", "TrendingProductsComponent", "FeaturedBrandsComponent", "NewArrivalsComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "SidebarComponent_div_7_div_1_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵtext", "ɵɵlistener", "SidebarComponent_div_8_div_1_Template_button_click_7_listener", "user_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "followUser", "id", "avatar", "ɵɵsanitizeUrl", "fullName", "ɵɵtextInterpolate", "username", "<PERSON><PERSON><PERSON>", "ɵɵtextInterpolate1", "isFollowing", "SidebarComponent_div_8_div_1_Template", "suggestedUsers", "SidebarComponent_div_13_div_1_Template", "SidebarComponent_div_14_div_1_Template_button_click_12_listener", "influencer_r5", "_r4", "followInfluencer", "formatFollowerCount", "followersCount", "postsCount", "engagement", "SidebarComponent_div_14_div_1_Template", "topInfluencers", "ɵɵpureFunction1", "_c1", "category_r6", "slug", "image", "name", "SidebarComponent", "constructor", "productService", "router", "trendingProducts", "categories", "isLoadingInfluencers", "isLoadingSuggestions", "ngOnInit", "loadSuggestedUsers", "loadTrendingProducts", "loadTopInfluencers", "loadCategories", "setTimeout", "count", "toFixed", "toString", "userId", "user", "find", "u", "influencerId", "influencer", "i", "quickBuy", "productId", "console", "log", "browseCategory", "categorySlug", "navigate", "ɵɵdirectiveInject", "i1", "ProductService", "i2", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SidebarComponent_Template", "rf", "ctx", "SidebarComponent_div_7_Template", "SidebarComponent_div_8_Template", "SidebarComponent_div_9_Template", "SidebarComponent_div_13_Template", "SidebarComponent_div_14_Template", "SidebarComponent_div_15_Template", "SidebarComponent_div_20_Template", "length", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "RouterLink", "RouterLinkActive", "i4", "IonIcon", "RouterLinkDelegate", "styles"], "sources": ["E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\sidebar\\sidebar.component.ts", "E:\\Fashion\\DFashion\\frontend\\src\\app\\features\\home\\components\\sidebar\\sidebar.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, RouterModule } from '@angular/router';\nimport { IonicModule } from '@ionic/angular';\n\nimport { ProductService } from '../../../../core/services/product.service';\nimport { Product } from '../../../../core/models/product.model';\nimport { TrendingProductsComponent } from '../trending-products/trending-products.component';\nimport { FeaturedBrandsComponent } from '../featured-brands/featured-brands.component';\nimport { NewArrivalsComponent } from '../new-arrivals/new-arrivals.component';\n\n@Component({\n  selector: 'app-sidebar',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    IonicModule,\n    TrendingProductsComponent,\n    FeaturedBrandsComponent,\n    NewArrivalsComponent\n  ],\n  templateUrl: './sidebar.component.html',\n  styleUrls: ['./sidebar.component.scss']\n})\nexport class SidebarComponent implements OnInit {\n  suggestedUsers: any[] = [];\n  trendingProducts: Product[] = [];\n  topInfluencers: any[] = [];\n  categories: any[] = [];\n  isLoadingInfluencers: boolean = false;\n  isLoadingSuggestions: boolean = false;\n\n  constructor(\n    private productService: ProductService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    this.loadSuggestedUsers();\n    this.loadTrendingProducts();\n    this.loadTopInfluencers();\n    this.loadCategories();\n  }\n\n  loadSuggestedUsers() {\n    this.isLoadingSuggestions = true;\n\n    // Simulate API call\n    setTimeout(() => {\n      this.suggestedUsers = [\n        {\n          id: '1',\n          username: '@arjun_style',\n          fullName: 'Arjun Kumar',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',\n          followedBy: 'Followed by maya_fashion + 12 others',\n          isFollowing: false\n        },\n        {\n          id: '2',\n          username: '@sneha_trends',\n          fullName: 'Sneha Reddy',\n          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100',\n          followedBy: 'Followed by raj_style + 8 others',\n          isFollowing: false\n        },\n        {\n          id: '3',\n          username: '@fashion_vikram',\n          fullName: 'Vikram Singh',\n          avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100',\n          followedBy: 'Followed by priya_chic + 15 others',\n          isFollowing: true\n        }\n      ];\n      this.isLoadingSuggestions = false;\n    }, 1200);\n  }\n\n  loadTrendingProducts() {\n    // Load from API - empty for now\n    this.trendingProducts = [];\n  }\n\n  loadTopInfluencers() {\n    this.isLoadingInfluencers = true;\n\n    // Simulate API call\n    setTimeout(() => {\n      this.topInfluencers = [\n        {\n          id: '1',\n          username: '@fashionista_maya',\n          fullName: 'Maya Sharma',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=100',\n          followersCount: 125000,\n          postsCount: 342,\n          engagement: 8.5,\n          isFollowing: false\n        },\n        {\n          id: '2',\n          username: '@style_guru_raj',\n          fullName: 'Raj Patel',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100',\n          followersCount: 89000,\n          postsCount: 198,\n          engagement: 7.2,\n          isFollowing: true\n        },\n        {\n          id: '3',\n          username: '@trendy_priya',\n          fullName: 'Priya Singh',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100',\n          followersCount: 67000,\n          postsCount: 156,\n          engagement: 9.1,\n          isFollowing: false\n        }\n      ];\n      this.isLoadingInfluencers = false;\n    }, 1500);\n  }\n\n  loadCategories() {\n    this.categories = [\n      {\n        name: 'Women',\n        slug: 'women',\n        image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=100'\n      },\n      {\n        name: 'Men',\n        slug: 'men',\n        image: 'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=100'\n      },\n      {\n        name: 'Kids',\n        slug: 'children',\n        image: 'https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=100'\n      },\n      {\n        name: 'Ethnic',\n        slug: 'ethnic',\n        image: 'https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=100'\n      }\n    ];\n  }\n\n  formatFollowerCount(count: number): string {\n    if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'k';\n    }\n    return count.toString();\n  }\n\n  followUser(userId: string) {\n    const user = this.suggestedUsers.find(u => u.id === userId);\n    if (user) {\n      user.isFollowing = !user.isFollowing;\n    }\n  }\n\n  followInfluencer(influencerId: string) {\n    const influencer = this.topInfluencers.find(i => i.id === influencerId);\n    if (influencer) {\n      influencer.isFollowing = !influencer.isFollowing;\n    }\n  }\n\n  quickBuy(productId: string) {\n    console.log('Quick buy product:', productId);\n    // TODO: Implement quick buy functionality\n  }\n\n  browseCategory(categorySlug: string) {\n    console.log('Browse category:', categorySlug);\n    this.router.navigate(['/category', categorySlug]);\n  }\n}\n", "<aside class=\"sidebar\">\n  <!-- Instagram-style Stories -->\n \n\n  <!-- Trending Products Section -->\n  <app-trending-products></app-trending-products>\n\n  <!-- Featured Brands Section -->\n  <app-featured-brands></app-featured-brands>\n\n  <!-- New Arrivals Section -->\n  <app-new-arrivals></app-new-arrivals>\n\n  <!-- Suggested for you -->\n  <div class=\"suggestions\">\n    <h3>Suggested for you</h3>\n\n    <!-- Loading State -->\n    <div *ngIf=\"isLoadingSuggestions\" class=\"loading-container\">\n      <div class=\"loading-card\" *ngFor=\"let item of [1,2,3]\">\n        <div class=\"loading-content\">\n          <div class=\"loading-line short\"></div>\n          <div class=\"loading-line medium\"></div>\n          <div class=\"loading-line long\"></div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Suggestions List -->\n    <div *ngIf=\"!isLoadingSuggestions && suggestedUsers.length > 0\">\n      <div *ngFor=\"let user of suggestedUsers\" class=\"suggestion-item\">\n        <img [src]=\"user.avatar\" [alt]=\"user.fullName\">\n        <div class=\"suggestion-info\">\n          <h5>{{ user.username }}</h5>\n          <p>{{ user.followedBy }}</p>\n        </div>\n        <button class=\"follow-btn\" (click)=\"followUser(user.id)\">\n          {{ user.isFollowing ? 'Following' : 'Follow' }}\n        </button>\n      </div>\n    </div>\n\n    <!-- Empty State -->\n    <div *ngIf=\"!isLoadingSuggestions && suggestedUsers.length === 0\" class=\"empty-container\">\n      <ion-icon name=\"people-outline\" class=\"empty-icon\"></ion-icon>\n      <h3 class=\"empty-title\">No Suggestions</h3>\n      <p class=\"empty-message\">Check back later for user suggestions</p>\n    </div>\n  </div>\n\n  <!-- Top Fashion Influencers -->\n  <div class=\"influencers\">\n    <h3>Top Fashion Influencers</h3>\n\n    <!-- Loading State -->\n    <div *ngIf=\"isLoadingInfluencers\" class=\"loading-container\">\n      <div class=\"loading-card\" *ngFor=\"let item of [1,2,3]\">\n        <div class=\"loading-content\">\n          <div class=\"loading-line short\"></div>\n          <div class=\"loading-line medium\"></div>\n          <div class=\"loading-line long\"></div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Influencers List -->\n    <div *ngIf=\"!isLoadingInfluencers && topInfluencers.length > 0\">\n      <div *ngFor=\"let influencer of topInfluencers\" class=\"influencer-item\">\n        <img [src]=\"influencer.avatar\" [alt]=\"influencer.fullName\">\n        <div class=\"influencer-info\">\n          <h5>{{ influencer.username }}</h5>\n          <p>{{ formatFollowerCount(influencer.followersCount) }} followers</p>\n          <div class=\"influencer-stats\">\n            <span class=\"posts-count\">{{ influencer.postsCount }} posts</span>\n            <span class=\"engagement\">{{ influencer.engagement }}% engagement</span>\n          </div>\n          <button class=\"follow-btn\" (click)=\"followInfluencer(influencer.id)\">\n            {{ influencer.isFollowing ? 'Following' : 'Follow' }}\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Empty State -->\n    <div *ngIf=\"!isLoadingInfluencers && topInfluencers.length === 0\" class=\"empty-container\">\n      <ion-icon name=\"people-outline\" class=\"empty-icon\"></ion-icon>\n      <h3 class=\"empty-title\">No Influencers Found</h3>\n      <p class=\"empty-message\">Check back later for top fashion influencers</p>\n    </div>\n  </div>\n\n  <!-- Shop by Category -->\n  <div class=\"categories\">\n    <h3>Shop by Category</h3>\n    <div class=\"category-grid\">\n      <div\n        *ngFor=\"let category of categories\"\n        class=\"category-item\"\n        [routerLink]=\"['/category', category.slug]\"\n        routerLinkActive=\"active\"\n        tabindex=\"0\"\n      >\n        <img [src]=\"category.image\" [alt]=\"category.name\">\n        <span>{{ category.name }}</span>\n      </div>\n    </div>\n  </div>\n\n\n</aside>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAiBC,YAAY,QAAQ,iBAAiB;AACtD,SAASC,WAAW,QAAQ,gBAAgB;AAI5C,SAASC,yBAAyB,QAAQ,kDAAkD;AAC5F,SAASC,uBAAuB,QAAQ,8CAA8C;AACtF,SAASC,oBAAoB,QAAQ,wCAAwC;;;;;;;;;;ICWrEC,EADF,CAAAC,cAAA,cAAuD,cACxB;IAG3BD,EAFA,CAAAE,SAAA,cAAsC,cACC,cACF;IAEzCF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAPRH,EAAA,CAAAC,cAAA,aAA4D;IAC1DD,EAAA,CAAAI,UAAA,IAAAC,qCAAA,kBAAuD;IAOzDL,EAAA,CAAAG,YAAA,EAAM;;;IAPuCH,EAAA,CAAAM,SAAA,EAAU;IAAVN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAU;;;;;;IAWrDT,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAE,SAAA,cAA+C;IAE7CF,EADF,CAAAC,cAAA,cAA6B,SACvB;IAAAD,EAAA,CAAAU,MAAA,GAAmB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,GAAqB;IAC1BV,EAD0B,CAAAG,YAAA,EAAI,EACxB;IACNH,EAAA,CAAAC,cAAA,iBAAyD;IAA9BD,EAAA,CAAAW,UAAA,mBAAAC,8DAAA;MAAA,MAAAC,OAAA,GAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAG,UAAA,CAAAP,OAAA,CAAAQ,EAAA,CAAmB;IAAA,EAAC;IACtDrB,EAAA,CAAAU,MAAA,GACF;IACFV,EADE,CAAAG,YAAA,EAAS,EACL;;;;IARCH,EAAA,CAAAM,SAAA,EAAmB;IAACN,EAApB,CAAAO,UAAA,QAAAM,OAAA,CAAAS,MAAA,EAAAtB,EAAA,CAAAuB,aAAA,CAAmB,QAAAV,OAAA,CAAAW,QAAA,CAAsB;IAExCxB,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAyB,iBAAA,CAAAZ,OAAA,CAAAa,QAAA,CAAmB;IACpB1B,EAAA,CAAAM,SAAA,GAAqB;IAArBN,EAAA,CAAAyB,iBAAA,CAAAZ,OAAA,CAAAc,UAAA,CAAqB;IAGxB3B,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAA4B,kBAAA,MAAAf,OAAA,CAAAgB,WAAA,+BACF;;;;;IATJ7B,EAAA,CAAAC,cAAA,UAAgE;IAC9DD,EAAA,CAAAI,UAAA,IAAA0B,qCAAA,kBAAiE;IAUnE9B,EAAA,CAAAG,YAAA,EAAM;;;;IAVkBH,EAAA,CAAAM,SAAA,EAAiB;IAAjBN,EAAA,CAAAO,UAAA,YAAAU,MAAA,CAAAc,cAAA,CAAiB;;;;;IAazC/B,EAAA,CAAAC,cAAA,cAA0F;IACxFD,EAAA,CAAAE,SAAA,mBAA8D;IAC9DF,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAU,MAAA,qBAAc;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAU,MAAA,4CAAqC;IAChEV,EADgE,CAAAG,YAAA,EAAI,EAC9D;;;;;IAUFH,EADF,CAAAC,cAAA,cAAuD,cACxB;IAG3BD,EAFA,CAAAE,SAAA,cAAsC,cACC,cACF;IAEzCF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAPRH,EAAA,CAAAC,cAAA,aAA4D;IAC1DD,EAAA,CAAAI,UAAA,IAAA4B,sCAAA,kBAAuD;IAOzDhC,EAAA,CAAAG,YAAA,EAAM;;;IAPuCH,EAAA,CAAAM,SAAA,EAAU;IAAVN,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAAQ,eAAA,IAAAC,GAAA,EAAU;;;;;;IAWrDT,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAE,SAAA,cAA2D;IAEzDF,EADF,CAAAC,cAAA,cAA6B,SACvB;IAAAD,EAAA,CAAAU,MAAA,GAAyB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,GAA8D;IAAAV,EAAA,CAAAG,YAAA,EAAI;IAEnEH,EADF,CAAAC,cAAA,cAA8B,eACF;IAAAD,EAAA,CAAAU,MAAA,GAAiC;IAAAV,EAAA,CAAAG,YAAA,EAAO;IAClEH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAU,MAAA,IAAuC;IAClEV,EADkE,CAAAG,YAAA,EAAO,EACnE;IACNH,EAAA,CAAAC,cAAA,kBAAqE;IAA1CD,EAAA,CAAAW,UAAA,mBAAAsB,gEAAA;MAAA,MAAAC,aAAA,GAAAlC,EAAA,CAAAc,aAAA,CAAAqB,GAAA,EAAAnB,SAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASF,MAAA,CAAAmB,gBAAA,CAAAF,aAAA,CAAAb,EAAA,CAA+B;IAAA,EAAC;IAClErB,EAAA,CAAAU,MAAA,IACF;IAEJV,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;;IAZCH,EAAA,CAAAM,SAAA,EAAyB;IAACN,EAA1B,CAAAO,UAAA,QAAA2B,aAAA,CAAAZ,MAAA,EAAAtB,EAAA,CAAAuB,aAAA,CAAyB,QAAAW,aAAA,CAAAV,QAAA,CAA4B;IAEpDxB,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAyB,iBAAA,CAAAS,aAAA,CAAAR,QAAA,CAAyB;IAC1B1B,EAAA,CAAAM,SAAA,GAA8D;IAA9DN,EAAA,CAAA4B,kBAAA,KAAAX,MAAA,CAAAoB,mBAAA,CAAAH,aAAA,CAAAI,cAAA,gBAA8D;IAErCtC,EAAA,CAAAM,SAAA,GAAiC;IAAjCN,EAAA,CAAA4B,kBAAA,KAAAM,aAAA,CAAAK,UAAA,WAAiC;IAClCvC,EAAA,CAAAM,SAAA,GAAuC;IAAvCN,EAAA,CAAA4B,kBAAA,KAAAM,aAAA,CAAAM,UAAA,iBAAuC;IAGhExC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAA4B,kBAAA,MAAAM,aAAA,CAAAL,WAAA,+BACF;;;;;IAZN7B,EAAA,CAAAC,cAAA,UAAgE;IAC9DD,EAAA,CAAAI,UAAA,IAAAqC,sCAAA,mBAAuE;IAczEzC,EAAA,CAAAG,YAAA,EAAM;;;;IAdwBH,EAAA,CAAAM,SAAA,EAAiB;IAAjBN,EAAA,CAAAO,UAAA,YAAAU,MAAA,CAAAyB,cAAA,CAAiB;;;;;IAiB/C1C,EAAA,CAAAC,cAAA,cAA0F;IACxFD,EAAA,CAAAE,SAAA,mBAA8D;IAC9DF,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAU,MAAA,2BAAoB;IAAAV,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAU,MAAA,mDAA4C;IACvEV,EADuE,CAAAG,YAAA,EAAI,EACrE;;;;;IAOJH,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAE,SAAA,cAAkD;IAClDF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,GAAmB;IAC3BV,EAD2B,CAAAG,YAAA,EAAO,EAC5B;;;;IANJH,EAAA,CAAAO,UAAA,eAAAP,EAAA,CAAA2C,eAAA,IAAAC,GAAA,EAAAC,WAAA,CAAAC,IAAA,EAA2C;IAItC9C,EAAA,CAAAM,SAAA,EAAsB;IAACN,EAAvB,CAAAO,UAAA,QAAAsC,WAAA,CAAAE,KAAA,EAAA/C,EAAA,CAAAuB,aAAA,CAAsB,QAAAsB,WAAA,CAAAG,IAAA,CAAsB;IAC3ChD,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAyB,iBAAA,CAAAoB,WAAA,CAAAG,IAAA,CAAmB;;;AD9EjC,OAAM,MAAOC,gBAAgB;EAQ3BC,YACUC,cAA8B,EAC9BC,MAAc;IADd,KAAAD,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IAThB,KAAArB,cAAc,GAAU,EAAE;IAC1B,KAAAsB,gBAAgB,GAAc,EAAE;IAChC,KAAAX,cAAc,GAAU,EAAE;IAC1B,KAAAY,UAAU,GAAU,EAAE;IACtB,KAAAC,oBAAoB,GAAY,KAAK;IACrC,KAAAC,oBAAoB,GAAY,KAAK;EAKlC;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAH,kBAAkBA,CAAA;IAChB,IAAI,CAACF,oBAAoB,GAAG,IAAI;IAEhC;IACAM,UAAU,CAAC,MAAK;MACd,IAAI,CAAC/B,cAAc,GAAG,CACpB;QACEV,EAAE,EAAE,GAAG;QACPK,QAAQ,EAAE,cAAc;QACxBF,QAAQ,EAAE,aAAa;QACvBF,MAAM,EAAE,oEAAoE;QAC5EK,UAAU,EAAE,sCAAsC;QAClDE,WAAW,EAAE;OACd,EACD;QACER,EAAE,EAAE,GAAG;QACPK,QAAQ,EAAE,eAAe;QACzBF,QAAQ,EAAE,aAAa;QACvBF,MAAM,EAAE,iEAAiE;QACzEK,UAAU,EAAE,kCAAkC;QAC9CE,WAAW,EAAE;OACd,EACD;QACER,EAAE,EAAE,GAAG;QACPK,QAAQ,EAAE,iBAAiB;QAC3BF,QAAQ,EAAE,cAAc;QACxBF,MAAM,EAAE,oEAAoE;QAC5EK,UAAU,EAAE,oCAAoC;QAChDE,WAAW,EAAE;OACd,CACF;MACD,IAAI,CAAC2B,oBAAoB,GAAG,KAAK;IACnC,CAAC,EAAE,IAAI,CAAC;EACV;EAEAG,oBAAoBA,CAAA;IAClB;IACA,IAAI,CAACN,gBAAgB,GAAG,EAAE;EAC5B;EAEAO,kBAAkBA,CAAA;IAChB,IAAI,CAACL,oBAAoB,GAAG,IAAI;IAEhC;IACAO,UAAU,CAAC,MAAK;MACd,IAAI,CAACpB,cAAc,GAAG,CACpB;QACErB,EAAE,EAAE,GAAG;QACPK,QAAQ,EAAE,mBAAmB;QAC7BF,QAAQ,EAAE,aAAa;QACvBF,MAAM,EAAE,oEAAoE;QAC5EgB,cAAc,EAAE,MAAM;QACtBC,UAAU,EAAE,GAAG;QACfC,UAAU,EAAE,GAAG;QACfX,WAAW,EAAE;OACd,EACD;QACER,EAAE,EAAE,GAAG;QACPK,QAAQ,EAAE,iBAAiB;QAC3BF,QAAQ,EAAE,WAAW;QACrBF,MAAM,EAAE,oEAAoE;QAC5EgB,cAAc,EAAE,KAAK;QACrBC,UAAU,EAAE,GAAG;QACfC,UAAU,EAAE,GAAG;QACfX,WAAW,EAAE;OACd,EACD;QACER,EAAE,EAAE,GAAG;QACPK,QAAQ,EAAE,eAAe;QACzBF,QAAQ,EAAE,aAAa;QACvBF,MAAM,EAAE,oEAAoE;QAC5EgB,cAAc,EAAE,KAAK;QACrBC,UAAU,EAAE,GAAG;QACfC,UAAU,EAAE,GAAG;QACfX,WAAW,EAAE;OACd,CACF;MACD,IAAI,CAAC0B,oBAAoB,GAAG,KAAK;IACnC,CAAC,EAAE,IAAI,CAAC;EACV;EAEAM,cAAcA,CAAA;IACZ,IAAI,CAACP,UAAU,GAAG,CAChB;MACEN,IAAI,EAAE,OAAO;MACbF,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;KACR,EACD;MACEC,IAAI,EAAE,KAAK;MACXF,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE;KACR,EACD;MACEC,IAAI,EAAE,MAAM;MACZF,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE;KACR,EACD;MACEC,IAAI,EAAE,QAAQ;MACdF,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE;KACR,CACF;EACH;EAEAV,mBAAmBA,CAAC0B,KAAa;IAC/B,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEA7C,UAAUA,CAAC8C,MAAc;IACvB,MAAMC,IAAI,GAAG,IAAI,CAACpC,cAAc,CAACqC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChD,EAAE,KAAK6C,MAAM,CAAC;IAC3D,IAAIC,IAAI,EAAE;MACRA,IAAI,CAACtC,WAAW,GAAG,CAACsC,IAAI,CAACtC,WAAW;;EAExC;EAEAO,gBAAgBA,CAACkC,YAAoB;IACnC,MAAMC,UAAU,GAAG,IAAI,CAAC7B,cAAc,CAAC0B,IAAI,CAACI,CAAC,IAAIA,CAAC,CAACnD,EAAE,KAAKiD,YAAY,CAAC;IACvE,IAAIC,UAAU,EAAE;MACdA,UAAU,CAAC1C,WAAW,GAAG,CAAC0C,UAAU,CAAC1C,WAAW;;EAEpD;EAEA4C,QAAQA,CAACC,SAAiB;IACxBC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,SAAS,CAAC;IAC5C;EACF;EAEAG,cAAcA,CAACC,YAAoB;IACjCH,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEE,YAAY,CAAC;IAC7C,IAAI,CAAC1B,MAAM,CAAC2B,QAAQ,CAAC,CAAC,WAAW,EAAED,YAAY,CAAC,CAAC;EACnD;;;uBA3JW7B,gBAAgB,EAAAjD,EAAA,CAAAgF,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAlF,EAAA,CAAAgF,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAhBnC,gBAAgB;MAAAoC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAvF,EAAA,CAAAwF,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCzB7B9F,EAAA,CAAAC,cAAA,eAAuB;UAWrBD,EANA,CAAAE,SAAA,4BAA+C,0BAGJ,uBAGN;UAInCF,EADF,CAAAC,cAAA,aAAyB,SACnB;UAAAD,EAAA,CAAAU,MAAA,wBAAiB;UAAAV,EAAA,CAAAG,YAAA,EAAK;UA4B1BH,EAzBA,CAAAI,UAAA,IAAA4F,+BAAA,iBAA4D,IAAAC,+BAAA,iBAWI,IAAAC,+BAAA,iBAc0B;UAK5FlG,EAAA,CAAAG,YAAA,EAAM;UAIJH,EADF,CAAAC,cAAA,cAAyB,UACnB;UAAAD,EAAA,CAAAU,MAAA,+BAAuB;UAAAV,EAAA,CAAAG,YAAA,EAAK;UAgChCH,EA7BA,CAAAI,UAAA,KAAA+F,gCAAA,iBAA4D,KAAAC,gCAAA,iBAWI,KAAAC,gCAAA,iBAkB0B;UAK5FrG,EAAA,CAAAG,YAAA,EAAM;UAIJH,EADF,CAAAC,cAAA,cAAwB,UAClB;UAAAD,EAAA,CAAAU,MAAA,wBAAgB;UAAAV,EAAA,CAAAG,YAAA,EAAK;UACzBH,EAAA,CAAAC,cAAA,cAA2B;UACzBD,EAAA,CAAAI,UAAA,KAAAkG,gCAAA,iBAMC;UAQPtG,EAJI,CAAAG,YAAA,EAAM,EACF,EAGA;;;UA3FEH,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAO,UAAA,SAAAwF,GAAA,CAAAvC,oBAAA,CAA0B;UAW1BxD,EAAA,CAAAM,SAAA,EAAwD;UAAxDN,EAAA,CAAAO,UAAA,UAAAwF,GAAA,CAAAvC,oBAAA,IAAAuC,GAAA,CAAAhE,cAAA,CAAAwE,MAAA,KAAwD;UAcxDvG,EAAA,CAAAM,SAAA,EAA0D;UAA1DN,EAAA,CAAAO,UAAA,UAAAwF,GAAA,CAAAvC,oBAAA,IAAAuC,GAAA,CAAAhE,cAAA,CAAAwE,MAAA,OAA0D;UAY1DvG,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAO,UAAA,SAAAwF,GAAA,CAAAxC,oBAAA,CAA0B;UAW1BvD,EAAA,CAAAM,SAAA,EAAwD;UAAxDN,EAAA,CAAAO,UAAA,UAAAwF,GAAA,CAAAxC,oBAAA,IAAAwC,GAAA,CAAArD,cAAA,CAAA6D,MAAA,KAAwD;UAkBxDvG,EAAA,CAAAM,SAAA,EAA0D;UAA1DN,EAAA,CAAAO,UAAA,UAAAwF,GAAA,CAAAxC,oBAAA,IAAAwC,GAAA,CAAArD,cAAA,CAAA6D,MAAA,OAA0D;UAYvCvG,EAAA,CAAAM,SAAA,GAAa;UAAbN,EAAA,CAAAO,UAAA,YAAAwF,GAAA,CAAAzC,UAAA,CAAa;;;qBDjFtC5D,YAAY,EAAA8G,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ/G,YAAY,EAAAwF,EAAA,CAAAwB,UAAA,EAAAxB,EAAA,CAAAyB,gBAAA,EACZhH,WAAW,EAAAiH,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,kBAAA,EACXlH,yBAAyB,EACzBC,uBAAuB,EACvBC,oBAAoB;MAAAiH,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}