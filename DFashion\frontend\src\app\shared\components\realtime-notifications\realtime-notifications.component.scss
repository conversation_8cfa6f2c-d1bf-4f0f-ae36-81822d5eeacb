.notification-bell {
  position: relative;
}

.notification-button {
  transition: all 0.3s ease;
}

.notification-button.has-notifications {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.notification-menu {
  width: 400px;
  max-height: 600px;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f5f5f5;
}

.notification-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.connection-status {
  display: flex;
  align-items: center;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.connection-status.connected {
  color: #4caf50;
}

.connection-status.disconnected {
  color: #f44336;
}

.notification-list {
  max-height: 400px;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px;
  gap: 16px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px;
  color: #666;
}

.empty-state mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.notification-item:hover {
  background: #f9f9f9;
}

.notification-item.unread {
  background: #e3f2fd;
  border-left: 4px solid #2196f3;
}

.notification-item.urgent {
  border-left-color: #f44336;
}

.notification-item.high {
  border-left-color: #ff9800;
}

.notification-icon {
  margin-right: 12px;
  margin-top: 4px;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
  color: #333;
}

.notification-message {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 8px;
  word-wrap: break-word;
}

.notification-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #999;
}

.notification-category {
  background: #e0e0e0;
  padding: 2px 6px;
  border-radius: 10px;
  text-transform: uppercase;
  font-weight: 500;
}

.notification-actions {
  margin-left: 8px;
}

.mark-read-btn {
  width: 32px;
  height: 32px;
  line-height: 32px;
}

.notification-footer {
  padding: 16px;
  text-align: center;
  border-top: 1px solid #eee;
  background: #fafafa;
}

.view-all-btn {
  width: 100%;
  color: #2196f3;
  font-weight: 500;
}

.mark-all-read-btn {
  color: #2196f3;
}

/* Scrollbar styling */
.notification-list::-webkit-scrollbar {
  width: 6px;
}

.notification-list::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.notification-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.notification-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
